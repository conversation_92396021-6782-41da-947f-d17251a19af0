#if ACGGAME_CLIENT

using System;
using System.Collections.Generic;
using UnityEngine;

namespace TriggerSystem
{
    [System.Serializable]
    public abstract class TriggerViewAction
    {
        [HideInInspector]
        public int actionId;
    
        public abstract TriggerEnum.E_TRIGGER_ACTION GetActionType();
    
        public abstract void Execute(TriggerViewSystem system);

        public abstract bool isCorrect(LinkedHashMap<string, BaseTriggerParam> paramDict);
    
        #region 初始化专用
    
        private static T CreateAction<T>() where T : TriggerViewAction, new()
        {
            return new T();
        }

        public static void Init<T>(Dictionary<TriggerEnum.E_TRIGGER_ACTION, Func<TriggerViewAction>> dic, TriggerEnum.E_TRIGGER_ACTION dataType) where T : TriggerViewAction, new()
        {
            dic.Add(dataType, CreateAction<T>);
        }
    
        #endregion
    }
}

#endif
