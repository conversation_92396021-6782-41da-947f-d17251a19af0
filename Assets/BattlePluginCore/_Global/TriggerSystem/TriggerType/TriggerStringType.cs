#if ACGGAME_CLIENT

using System.Collections.Generic;
using System.IO;

#if UNITY_EDITOR
using UnityEditor;
using UnityEngine;
#endif

namespace TriggerSystem
{
    public static class TriggerStringType
    {
        public static void Init(Dictionary<TriggerEnum.E_TRIGGER_PARAM,ParamCondtion> dic)
        {
            Specializer<string>.ReadFunc = Read;
            Specializer<string>.WriteFunc = Write;
            Specializer<string>.CompareFunc = Compare;
            Specializer<string>.DataTypeFunc = GetDataType;
            Specializer<string>.DefaultValueFunc = DefaultValue;
        
            Specializer<string>.Init(dic);
        
#if UNITY_EDITOR
            InitEditor();
#endif
        }
        
        private static string DefaultValue()
        {
            return string.Empty;
        }
    
        private static TriggerEnum.E_TRIGGER_PARAM GetDataType()
        {
            return TriggerEnum.E_TRIGGER_PARAM.STRING;
        }

        private static string Read(BinaryReader br)
        {
            return br.ReadString();
        }
    
        private static void Write(BinaryWriter bw, string value)
        {
            bw.Write(value);
        }
    
        private static bool Compare(string a, string b, TriggerEnum.E_TRIGGER_CONDITION compareFunc)
        {
            switch (compareFunc)
            {
                case TriggerEnum.E_TRIGGER_CONDITION.Equal:
                    return a == b;
                case TriggerEnum.E_TRIGGER_CONDITION.NotEqual:
                    return a != b;
                case TriggerEnum.E_TRIGGER_CONDITION.AnyValue:
                    return true;
                case TriggerEnum.E_TRIGGER_CONDITION.Contain:
                    return a.Contains(b);
                default:
                    return false;
            }
        }

#if UNITY_EDITOR
    
        private static void InitEditor()
        {
            Specializer<string>.DrawValueFunc = DrawValue;
        }
    
        private static string DrawValue(Rect rect, string label,  string oldValue)
        {
            return EditorGUI.TextField(rect, label, oldValue);
        }
    
#endif
    }
}

#endif
