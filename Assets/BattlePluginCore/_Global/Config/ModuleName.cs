using UnityEngine;
using System.Collections;
using System.Collections.Generic;


public class ModuleName
{
    #region 定义 SceneName / ModuleName / WinName .  需跟类名一样，不容易出错。

    //Game  Scene -------------------
    public const string SCENE_INTRO = "Intro";
    public const string SCENE_PLATFORM_LOGIN = "Login";
    public const string SCENE_CHARACTER_SELECT = "CreateRole";

    public const string SCENE_WORLD = "H_areamap_01_03";
	public const string SCENE_CITY = "H_citymap_01_01";
    public const string SCENE_PVP = "Pvp01_00";


    //Stage -------------------------

    public const string STG_PlatformLogin = "STG_PlatformLogin";//平台登录界面
    public const string STG_ServerSelect = "STG_ServerSelect";//服务器选择
    public const string STG_CharacterSelect = "STG_CharacterSelect";//角色选择
    public const string STG_Main = "STG_Main";//主场景



	//UIPart -------------------------
	public const string PRT_NavigatorBar = "gui/common/component/PRT_NavigatorBar";
	public const string PRT_TopBar = "gui/common/PRT_TopBar";
	public const string PRT_UIBackground = "gui/common/component/PRT_UIBackground";
    public const string PRT_EquipDistill = "gui/equip/PRT_EquipDistill";
    public const string PRT_TaskTip = "gui/task/PRT_TaskTip";
	public const string PRT_Chat = "gui/chat/PRT_Chat";

	//荣耀主城主界面部分;
	public const string PRT_BottomChatPanel = "gui/gloryMain/PRT_BottomChatPanel";
	public const string PRT_ShortcutsPanel = "gui/gloryMain/PRT_ShortcutsPanel";
	public const string PRT_SystemActivityPanel = "gui/gloryMain/PRT_SystemActivityPanel";
	public const string PRT_SystemItemPanel = "gui/gloryMain/PRT_SystemItemPanel";
	public const string PRT_TaskTeamShortcutsPanel = "gui/gloryMain/PRT_TaskTeamShortcutsPanel";
	public const string PRT_WorldUI = "gui/world/PRT_WorldUI";

	//虚拟竞技场;
	public const string PRT_VFightMatchLobby = "gui/vfight/PRT_VFightMatchLobby";
	public const string PRT_VFightAccountCardSetting = "gui/vfight/PRT_VFightAccountCardSetting";
	public const string PRT_VFightChangeSkill = "gui/vfight/PRT_VFightChangeSkill";
	public const string PRT_VFightLoading = "gui/vfight/PRT_VFightLoading";
	public const string PRT_VFightSuperStarInfo = "gui/vfight/PRT_VFightSuperStarInfo";
	public const string PRT_VFightChangeTechnic = "gui/vfight/PRT_VFightChangeTechnic";
	public const string PRT_VFightVSInfo = "gui/vfight/PRT_VFightVSInfo";
	public const string PRT_VFightTactics = "gui/vfight/PRT_VFightTactics";
	public const string PRT_VFightUI = "gui/vfight/PRT_VFightUI";
    public const string PRT_VFightReport = "gui/vfight/PRT_VFightReport";

	// 商城
	public const string PRT_MarketFrame = "gui/market/PRT_MarketFrame";
	public const string PRT_MarketSilverBuy = "gui/market/PRT_MarketSilverBuy";
	public const string PRT_MarketSilverSell = "gui/market/PRT_MarketSilverSell";
	public const string PRT_MarketPrice = "gui/market/PRT_MarketPrice";
	public const string PRT_MarketTicketBuy = "gui/market/PRT_MarketTicketBuy";

    //手机
    public const string PRT_Phone = "gui/phone/PRT_Phone";

    //摆摊
    public const string PRT_StallStore = "gui/stallStore/PRT_StallStore";

    //副本
    public const string PRT_DungeonMap = "gui/dungeon/PRT_DungeonMap";
    public const string PRT_DungeonLeftShowList = "gui/dungeon/PRT_DungeonLeftShowList";
    public const string PRT_DungeonDetailInfo = "gui/dungeon/PRT_DungeonDetailInfo";
    public const string PRT_DungeonListPanel = "gui/dungeon/PRT_DungeonListPanel";
    public const string PRT_DungeonResult = "gui/result/PRT_DungeonResult";
    //地图2D;
    public const string PRT_Map2D = "gui/map/PRT_Map2d";

	// 拍卖
	public const string PRT_Auction = "gui/auction/PRT_Auction";

    #endregion
}