// Unity built-in shader source. Copyright (c) 2016 Unity Technologies. MIT license (see license.txt)

Shader "Skybox/HDRI_bake" {
    Properties{

        [HDR] _Tint("Tint Color 染色", Color) = (1,1,1)
        _Exposure("Exposure 曝光", Range(-20, 20)) = 0
        _Rotation("Rotation 旋转", Range(0, 360)) = 0
        //[Toggle(_NO_SUN)] _SunEnabled("Disable Sun 裁剪太阳", float) = 0.0
        //_MaxSun("Max value 最高亮度", Float) =2
        [NoScaleOffset] _Tex("Cubemap (HDR) ", Cube) = "white" {}
        [Toggle(_PANOROMIC)] _PanoromicEnabled("PanoromicEnabled", float) = 0.0
        [NoScaleOffset] _PanoromicTex("Panoromic (HDR) ", 2D) = "white" {}


        //_LightTex("_LightTex (HDR) ", 2D) = "black" {}


    }

        SubShader{
            Tags { "Queue" = "Background" "RenderType" = "Background" "PreviewType" = "Skybox" }
            Cull back
             ZWrite Off

            Pass {

                CGPROGRAM
                #pragma vertex vert
                #pragma fragment frag
                #pragma target 2.0
                #pragma shader_feature _REF_LIGHT_MAX_VALUE
                #pragma shader_feature _NO_SUN_VISUAL
                #pragma shader_feature _PANOROMIC
                #pragma shader_feature _DEBUG_EXPOSURE_ENABLED 
                #pragma shader_feature _DEBUG_LUX_ENABLED 

                #include "UnityCG.cginc"

                samplerCUBE _Tex;
                half4 _Tex_HDR;
                half4 _Tint;
                half _Exposure;
                float _Rotation;
                float _RefLightMaxValue;
                sampler2D _PanoromicTex;
                half4 _PanoromicTex_HDR;
            /*    sampler2D _LightTex;
                float4 _LightTex_ST;
                half4 _LightTex_HDR;*/

                float3 RotateAroundYInDegrees(float3 vertex, float degrees)
                {
                    float alpha = degrees * UNITY_PI / 180.0;
                    float sina, cosa;
                    sincos(alpha, sina, cosa);
                    float2x2 m = float2x2(cosa, -sina, sina, cosa);
                    return float3(mul(m, vertex.xz), vertex.y).xzy;
                }

                struct appdata_t {
                    float4 vertex : POSITION;
                    float4 uv : TEXCOORD0;
                    UNITY_VERTEX_INPUT_INSTANCE_ID
                };

                struct v2f {
                    float4 vertex : SV_POSITION;
                    float3 texcoord : TEXCOORD0;
                    float2 texcoord1 : TEXCOORD1;
                    UNITY_VERTEX_OUTPUT_STEREO
                };

                v2f vert(appdata_t v)
                {
                    v2f o;
                    UNITY_SETUP_INSTANCE_ID(v);
                    UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
                    float3 rotated = RotateAroundYInDegrees(v.vertex, _Rotation);
                    o.vertex = UnityObjectToClipPos(rotated);
                    o.texcoord = v.vertex.xyz;
                    o.texcoord1 = v.uv;

                    //o.texcoord1 = TRANSFORM_TEX(v.uv, _LightTex);

                    // o.image180ScaleAndCutoff = float2(1.0, 1.0);
                    // o.layout3DScaleAndOffset = float4(0, 0, 1, 1);


                     return o;
                 }
                 inline float2 ToRadialCoords(float3 coords)
                 {
                     float3 normalizedCoords = normalize(coords);
                     float latitude = acos(normalizedCoords.y);
                     float longitude = atan2(normalizedCoords.z, normalizedCoords.x);
                     float2 sphereCoords = float2(longitude, latitude) * float2(0.5 / UNITY_PI, 1.0 / UNITY_PI);
                     return float2(0.5, 1.0) - sphereCoords;
                 }
                 fixed4 frag(v2f i) : SV_Target
                 {
                 #if _PANOROMIC
                            float2 tc = ToRadialCoords(i.texcoord);
                            half4 tex = tex2D(_PanoromicTex, tc);
                            half3 c = DecodeHDR(tex, _PanoromicTex_HDR);
                #else
                            half4 tex = texCUBE(_Tex, i.texcoord);
                            half3 c = DecodeHDR(tex, _Tex_HDR);
                            //c.rgb = 1;
                 #endif



                    c = c * _Tint.rgb;// *unity_ColorSpaceDouble.rgb;
                    c.rgb *= pow(2, _Exposure);
                    //还没处理Gamma 和 Linear下问题
                    #if _REF_LIGHT_MAX_VALUE

                            float value = dot(c.rgb, float3(0.3, 0.59, 0.11)).x;
                            if (value > _RefLightMaxValue)
                            {
                        #if _NO_SUN_VISUAL
                                 c.rgb = half3(1,0,0);

                        #else
                                value = 1 - (value - _RefLightMaxValue) / value;
                                float3 cn = normalize(c.rgb);
                                c.rgb = c.rgb * value;
                        #endif

                           }

                    #endif




                    #if _DEBUG_EXPOSURE_ENABLED
                                float3 col = c.rgb;

                                fixed3 debugColors[16];
                                debugColors[0] = fixed3(0.0, 0.0, 0.0);  // black
                                debugColors[1] = fixed3(0.0, 0.0, 0.1647);  // darkest blue
                                debugColors[2] = fixed3(0.0, 0.0, 0.3647);  // darker blue
                                debugColors[3] = fixed3(0.0, 0.0, 0.6647);  // dark blue
                                debugColors[4] = fixed3(0.0, 0.0, 0.9647);  // blue
                                debugColors[5] = fixed3(0.5, 0.5, 0.5);  // cyan
                                //debugColors[5] = fixed3(0.0, 0.9255, 0.9255);  // cyan
                                debugColors[6] = fixed3(0.0, 0.5647, 0.0);  // dark green
                                debugColors[7] = fixed3(0.0, 0.5647, 0.0);  // green
                                debugColors[8] = fixed3(0.0, 0.7843, 0.0);  // yellow
                                debugColors[9] = fixed3(1, 1, 0);  //  yellow-orange
                                debugColors[10] = fixed3(0.90588, 0.75294, 0.0);  // orange
                                debugColors[11] = fixed3(1.0, 0.5647, 0.0);  // bright red 
                                debugColors[12] = fixed3(1.0, 0.0, 0.0);  // red
                                debugColors[13] = fixed3(1.0, 0.0, 1.0);  // magenta
                                debugColors[14] = fixed3(0.6, 0.3333, 0.7882);  // purple
                                debugColors[15] = fixed3(1.0, 1.0, 1.0);  // white


                                float v = log2(dot(col, float3(0.3, 0.59, 0.11)).x / 0.18);
                                //float v = log10(dot(col, float3(0.3, 0.59, 0.11)).x );
                                v = clamp(v + 5.0, 0.0, 15.0);
                                v = clamp(v , 0.0, 15.0);
                                int index = int(floor(v));
                                col = lerp(debugColors[index], debugColors[min(15, index + 1)], v - floor(v));
                                //col = debugColors[index];
                                //col = debugColors[5];
                                c.rgb = col.rgb;

                    #endif

                                //#if _DEBUG_LUX_ENABLED
                                //            c.rgb = half3(0, 0, 0);
                                //#endif

                    return half4(c, 1);
                }

                ENDCG
            }
    }


        Fallback Off

}
