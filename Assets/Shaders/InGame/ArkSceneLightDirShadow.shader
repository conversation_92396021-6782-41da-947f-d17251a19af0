Shader "InGame/Scene/Ark/LightDirShadow"
{
    Properties
    {
        //_Color ("颜色", Color) = (1, 1, 1, 1)
        _MainTex ("主贴图", 2D) = "white" {}
        
        _ShasowTex ("四方向阴影贴图", 2D) = "white" {}
        //_AmbientPower("环境光 强度", Range(0, 1)) = 1
        //_DarkColor ("环境光 染色", Color) = (0.2, 0.1, 0.3, 1)

        //[HDR]_EmissiveColor ("自发光 颜色", Color) = (0, 0, 0, 1)
        _EmissiveTex ("自发光 贴图", 2D) = "black" {}

        // [Header(Snow)]
        // _SnowTex ("Snow Texture", 2D) = "white" {}
        // _SnowPower("Snow Power", Range(-1, 1)) = 0
        // _SnowTilling("Snow Tilling", Range(0.1, 5)) = 1
        // _MaskTex ("Mask Texture", 2D) = "white" {}

        [Header(Cloud Shadow)]
        //[Toggle]_Cloud("Cloud Enable ?",Float) = 0
        //_CloudColor ("云投影 颜色", Color) = (0, 0, 0, 1)
        _PatternTex ("云投影 图案", 2D) = "white" {}
        //_CloudPower("云投影 强度", Range(0, 1)) = 1

        [Header(Wet Density)]
        //[Toggle]_Ripple("Wet Enable ?",Float) = 0
        //_WetPower("雨水 程度", Range(-1, 1)) = 0
        _RainMaskTex ("雨水扩散遮罩贴图", 2D) = "white" {}
        _RippleTex("涟漪贴图", 2D) = "white" {}
		//_RippleScale("涟漪 大小",Range(0.2,1.5)) =1
        //_RippleSpeed("涟漪 速度",Range(1,20)) = 5
        //_RipplePower("涟漪 强度",Range(0.5,10)) = 1
    }

    SubShader
    {
        Tags { "RenderType"="Opaque" }

        Pass
        {
            	// 遮挡描边的场景, 参考值5
			Stencil
			{
				Ref 5
				Comp Always
				Pass Replace
			}

            Tags { "LightMode"="ForwardBase" }

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #pragma multi_compile _ _RIPPLE_ON
            #pragma multi_compile _ _CLOUD_ON
            
            #include "Common/InGame.cginc"
            #include "Common/InGameArk.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                half2 texcoord2: TEXCOORD1;
                half3 normal: NORMAL;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                half2 uv2: TEXCOORD1;
                float3 worldPos: TEXCOORD2;
                float3 worldNormal: TEXCOORD3;
            };

            uniform half3 _ForwardDir;

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);

                o.uv2 = v.texcoord2;

                o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;  
                o.worldNormal = UnityObjectToWorldNormal(v.normal);  

                return o;
            }


            fixed4 frag (v2f i) : SV_Target
            {
                //Diffuse Lighting
                //float3 worldPos = ark_SceneCenterPos-i.worldPos;
                float3 lightDir = normalize(UnityWorldSpaceLightDir(i.worldPos));
                float3 worldNormal = normalize(i.worldNormal);
                  //世界空间通用UV
                //float2 worldUV = (worldPos.xy + worldPos.yz + worldPos.xz)/3;
                // half nl = max(0, dot(worldNormal, lightDir));
                // nl = (nl + 1)/2;
                half3 sh = ShadeSH9(float4(worldNormal,1)) * ark_AmbientPower;

                #ifdef _RIPPLE_ON
                    float2 rippleUV = worldUV(i.worldPos) * _RippleTex_ST.xy + _RippleTex_ST.zw;
                    float3 ripple = ComputeRipple(rippleUV / ark_rippleScale, _Time.x * ark_rippleSpeed, ark_ripplePower, _RippleTex) ;
                    i.uv.x += lerp(0, lerp(0, ripple.r, saturate(ark_WetPower)), worldNormal.y);
                #endif

                // sample the texture
                half4 albedo = tex2D(_MainTex, i.uv) * ark_GlobalColor;
                
                //不同角度lightmap的选择判断
                half finalShadow = ComputeShadowInfo(_ShasowTex, i.uv2, _ForwardDir);

                //Emission Color
                half4 emission = tex2D(_EmissiveTex, i.uv) * ark_EmissionColor;

                //Final Color
                half4 col = 1;
                col.rgb = albedo.rgb * finalShadow + lerp(ark_DarkColor * albedo.rgb, 0, finalShadow);
                col.rgb += emission.rgb;

              
                //云投影动画
                #ifdef _CLOUD_ON
                    half2 rollSpeed = half2(ark_CloudSpeedX, ark_CloudSpeedY);
                    half cloudTex = tex2D(_PatternTex, worldUV(i.worldPos) * ark_cloudTilling.xy + ark_cloudTilling.zw + frac(_Time.y * rollSpeed)).r;
                    col.rgb *= lerp(1, lerp(ark_CloudColor.rgb, 1, cloudTex), ark_CloudPower);
                #endif

                col += albedo * half4(sh, 1);


                //地面湿润
                #ifdef _RIPPLE_ON
                    half rainMask = tex2D(_RainMaskTex, worldUV(i.worldPos) * _RainMaskTex_ST.xy).r;
                    col = lerp(col, col * 0.5 , saturate(rainMask + ark_WetPower));
                #endif
                 APPLY_HEIGHT_FOG(col,i.worldPos.y);
                return col;
            }
            ENDCG
        }
    }
}
