Shader "InGame/Scene/SpotLight"
{
    Properties
    {
        _Color("Color",Color) = (1.0,1.0,1.0,1.0)
        _MainTex("Texture", 2D) = "white" {}
        
        //【【LL需求】小小英雄攻击特效编辑器内添加屏幕（黑屏）表现功能及效果实现】 http://tapd.oa.com/cchess/prong/stories/view/1020417564865672619  (fengxzeng)
        [HideInInspector]_dark("Dark", Range(0.0, 1.0)) = 0
        
    }
    SubShader
    {
        Tags { "RenderType"="Opaque"/* "DisableBatching"="True" */}
        LOD 100
        Pass
        {
            // 遮挡描边的场景, 参考值5
            Stencil
            {
                Ref 5
                Comp Always
                Pass Replace
            }
            
            ColorMask RBG

            CGPROGRAM
            #pragma multi_compile_instancing
            #pragma vertex vert
            #pragma fragment frag
            
            #define _IN_GAME_SCENE 1
            
            #include "Common/InGame.cginc"
            #include "../TKCGFast.cginc"
            struct appdata
            {
                UNITY_VERTEX_INPUT_INSTANCE_ID
                float4 vertex       : POSITION;
                half2 uv            : TEXCOORD0;
                
            };

            struct v2f
            {
                half4 vertex        : SV_POSITION; 
                half2 uv            : TEXCOORD0; 
                half3 worldPos     : TEXCOORD1;      
            };

            fixed4 _Color;
            sampler2D _MainTex;
            float4 _MainTex_ST;
            
            v2f vert (appdata v)
            {
                v2f o;
                UNITY_SETUP_INSTANCE_ID(v);
                o.worldPos = ObjectToWorldFast(v.vertex);
                o.vertex = WorldToClipFast(o.worldPos); 
                o.uv = TRANSFORM_TEX(v.uv, _MainTex); 
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            { 
                float3 center = float3(unity_ObjectToWorld[0].w, unity_ObjectToWorld[1].w, unity_ObjectToWorld[2].w);
                fixed4 col = tex2D(_MainTex, i.uv.xy);
                col *= _Color; 
                APPLY_HEIGHT_FOG(col, i.worldPos.y); 
                //WS_SceneVignette On:SceneVignette = 2  Off:SceneVignette = 0
                APPLY_VIGNETTE_WORLDSPACE(col,i.worldPos.xz,center);  
                APPLY_EFFECT_DARK_SCENE(col);
                return col; 
            }
            ENDCG
        }
    }
    FallBack "Unlit/Texture" 

}
