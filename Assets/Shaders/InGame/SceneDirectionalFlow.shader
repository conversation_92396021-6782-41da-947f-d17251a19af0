Shader "InGame/Scene/Water/Directional Flow"
{
	Properties
	{
		_Color("颜色", Color) = (1, 1, 1, .5) 
		_MainTex ("基础 贴图", 2D) = "white" {}
		[HDR]_Color02("颜色", Color) = (1, 1, 1, .5) 
		[NoScaleOffset]_SecTex ("流动 贴图", 2D) = "black" {}
		_FlowSpeed("速度(xy)和缩放(zw)", Vector) = (0,0,1,1)
		
		[Space(5)]
        [Header(Distort)]
        [Space(5)]
		[NoScaleOffset]_NoiseTex("扰动 贴图", 2D) = "white" {}
		[KeywordEnum(XZ, YZ, XY)] _UV("UV 轴向", Float) = 0
		_Scale("UV 缩放", Range(0,1)) = 0.5
		_TextureDistort01("扰动 强度(主贴图)", range(0,1)) = 0.1
		_TextureDistort02("扰动 强度(流动贴图)", range(0,1)) = 0.1
		_DistortSpeed("扰动 速度", range(-5,5)) = 1
		_RimSmoothMin("Smooth Min",Range(0,1)) = 0
		_RimSmoothMax("Smooth Max",Range(0,1)) = 1

		[Space(5)]
        [Header(Foam)]
        [Space(5)]
		[Toggle]_UseVex("使用 顶点色 ?",Float) = 0
		[HDR]_FoamC("Foam Color", Color) = (1, 1, 1, .5) 
		_FoamTex ("Foam Texture", 2D) = "white" {}
		_Foam("泡沫 宽度", Range(0,10)) = 8

		[Space(5)]
        [Header(WaterFall Foam)]
        [Space(5)]
		[HDR]_WaterFallFoamColor("瀑布泡沫 颜色(顶点色A)", Color) = (1, 1, 1, .5) 
		_WaterFallFoam("瀑布泡沫 亮度", Range(0,10)) = 2
		// _TopSpread("Foam Position", Range(0,6)) = 0.05
		// _Softness ("Foam Softness", Range(0,0.5)) = 0.1
		// _EdgeWidth("Foam Width", Range(0,2)) = 0.4


		[Space(5)]
        [Header(Rim)]
        [Space(5)]
		_RimColor("边缘光 颜色", Color) = (1, 1, 1, .5) 
		_RimRange("边缘光 范围",Range(0.1,1)) = 1

		[Header(Stencil)]
		[IntRange] _Stencil ("Stencil ID", Range(0,255)) = 0
		[Enum(UnityEngine.Rendering.CompareFunction)] _StencilComp ("Stencil Comparison", Float) = 8
		[Enum(UnityEngine.Rendering.StencilOp)] _StencilPass ("Stencil Pass", Float) = 0
		[Enum(UnityEngine.Rendering.StencilOp)] _StencilFail ("Stencil Fail", Float) = 0

		//【【LL需求】小小英雄攻击特效编辑器内添加屏幕（黑屏）表现功能及效果实现】 http://tapd.oa.com/cchess/prong/stories/view/1020417564865672619  (fengxzeng)
		[HideInInspector]_dark("Dark", Range(0.0, 1.0)) = 0
	}

	SubShader
	{
		Tags { "RenderType"= "Opaque"  "Queue" = "Geometry" }

		Stencil
        {
			Ref [_Stencil]
			Comp [_StencilComp]
			Pass [_StencilPass]
			Fail [_StencilFail]
        }

		Pass
		{
			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag

			#pragma shader_feature  _UV_XZ _UV_YZ _UV_XY

			#define _IN_GAME_SCENE 1
 
			#include "UnityCG.cginc"
			#include "Common/InGame.cginc"
 
			struct appdata
			{
				float4 vertex : POSITION;
				float2 uv : TEXCOORD0;
				float2 uv2 : TEXCOORD1;
				float3 normal : NORMAL;
				float4 color : COLOR;
			};
 
			struct v2f
			{
				float4 vertex : SV_POSITION;
				float2 uv : TEXCOORD1;
				float3 worldNormal : TEXCOORD2;
				float3 worldViewDir : TEXCOORD3;
				float4 worldPos : TEXCOORD4;//
				float2 uv02 : TEXCOORD5;

				float4 vertexColors : COLOR0;
			};

			float _TextureDistort01, _TextureDistort02,  _DistortSpeed;
			float4 _Color, _Color02, _RimColor;
			sampler2D _MainTex, _NoiseTex, _SecTex;//
			float4 _MainTex_ST;
			float _Speed, _Amount, _Height, _Foam, _Scale, _RimRange, _TopSpread, _Softness, _EdgeWidth;// 
			float4 _FoamC, _FlowSpeed;
			half _RimSmoothMin, _RimSmoothMax ;
			sampler2D _FoamTex;
			half4 _WaterFallFoamColor;
			fixed _WaterFallFoam;
 
			v2f vert (appdata v)
			{
				v2f o;
				UNITY_INITIALIZE_OUTPUT(v2f, o);
				o.vertex = UnityObjectToClipPos(v.vertex);
				o.worldPos = mul(unity_ObjectToWorld, v.vertex);

				o.worldNormal = UnityObjectToWorldNormal(v.normal);
				o.worldViewDir = _WorldSpaceCameraPos - o.worldPos ;

				o.vertexColors = v.color;
 
				o.uv = v.uv;
				o.uv02 = v.uv2;


				return o;
			}
 
			fixed4 frag (v2f i) : SV_Target
			{
				half3 worldNormal = normalize(i.worldNormal);
                half3 worldViewDir = normalize(i.worldViewDir);
				
				half2 distortxUV;
				#ifdef _UV_XZ
					distortxUV = i.worldPos.xz;
				#elif _UV_YZ
					distortxUV = i.worldPos.yz;
				#elif _UV_XY
					distortxUV = i.worldPos.xy;
				#endif

				// sample the texture
				fixed distortx = tex2D(_NoiseTex, (distortxUV * _Scale)  - frac(_Time.x * _DistortSpeed)).r ;// distortion alpha

				fixed distortxPow01 = distortx * _TextureDistort01;
				fixed distortxPow02 = distortx * _TextureDistort02;

				half2 flowUV01 = i.uv * _MainTex_ST.xy;
				half2 flowUV02 = i.uv02 * _FlowSpeed.zw + frac(_Time.y * _FlowSpeed.xy);

				half4 col = tex2D(_MainTex, flowUV01 - distortxPow01 );// texture times tint;	
				half4 col02 = tex2D(_SecTex, flowUV02 - distortxPow02 );

				col = col  * _Color + col02  * _Color02;

				// 	Foam
				half worldNormalDotNoise = dot(float3(0,0,1) , worldNormal.y );
				half sm01 = smoothstep(_TopSpread, _TopSpread + _Softness, worldNormalDotNoise);
                half sm02 = smoothstep(worldNormalDotNoise,worldNormalDotNoise + _Softness, _TopSpread + _EdgeWidth);
                //half foam = (sm01 *  sm02 ) ;
				half foam =  1 - i.vertexColors.a ;

				half foamNoise;
				#ifdef _USEVEX_ON
					foamNoise = i.vertexColors.r ;
				#else
					foamNoise = tex2D(_FoamTex, flowUV01).r;
					//oamNoise = col.a ;
				#endif

				half foamLine = 1 - saturate(_Foam * float4(distortx.rrr,1) * foamNoise );

				half waterfallFoam = saturate(_WaterFallFoam * float4(distortx.rrr,1) * foam );

				//col += foamLine * _FoamC + waterfallFoam * _WaterFallFoamColor; // add the foam line and tint to the texture
				col += foamLine * _FoamC; // add the foam line and tint to the texture
				col.rgb += waterfallFoam * _WaterFallFoamColor.rgb;

				half nv = saturate(dot(worldNormal , worldViewDir)) ;
                half4 rim = saturate(_RimRange - nv) * _RimColor;
				col.rgb += rim.rgb;

				APPLY_EFFECT_DARK_SCENE(col);
				return   col;
			}
			ENDCG
		}
	}
}
