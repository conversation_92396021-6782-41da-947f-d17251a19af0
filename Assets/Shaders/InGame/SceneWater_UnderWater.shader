Shader "InGame/Scene/Water_Under"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}

        _BumpMap ("Distort Texture", 2D) = "white" {}
        _DistortPower("Distort Power", Range(0,2)) = 1

        [Space(5)]
        [Header(Water)]
        [Space(5)]
        _TargetWaterHeight("Target Water Height", Float) = 0
        _WaterDeep("Water Deep", Range(0,2)) = 1
        _UnderColor ("Water Under Color", COLOR)  = ( 0, 0, 0, 1)

         //【【LL需求】小小英雄攻击特效编辑器内添加屏幕（黑屏）表现功能及效果实现】 http://tapd.oa.com/cchess/prong/stories/view/1020417564865672619  (fengxzeng)
        [HideInInspector]_dark("Dark", Range(0.0, 1.0)) = 0
    }
    
    SubShader
    {
        Tags { "RenderType"="Transparent" "Queue" = "Transparent" }

        Pass
        {
            Stencil
            {
                Ref 2
                Comp Equal
            }
            
			ColorMask RGB
            Blend SrcAlpha OneMinusSrcAlpha
            ZWrite off
            ZTest Greater

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"

            #define _IN_GAME_SCENE 1
            #include "Common/InGame.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float4 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;

                float4 worldPos : TEXCOORD1;
            };

            sampler2D _MainTex, _BumpMap;
            float4 _BumpMap_ST;
            half4 _UnderColor;
            fixed _WaterDeep;
            half _TargetWaterHeight, _DistortPower;

            uniform float4 _WaveScale4;
			uniform float4 _WaveOffset;

            v2f vert (appdata v)
            {
                v2f o;

                o.vertex = UnityObjectToClipPos(v.vertex);

                o.uv.xy = v.uv;
                o.uv.zw = TRANSFORM_TEX(v.uv, _BumpMap);

                o.worldPos = mul(unity_ObjectToWorld, v.vertex);


                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                half2 uv01 = i.uv.zw - frac(_Time.x * half2(-0.6,0.3) );
                half2 uv02 = i.uv.zw - frac(_Time.x * half2(0.5,-0.2) );
                half3 bump1 = UnpackNormal(tex2D( _BumpMap, uv01 )).rgb;
				half3 bump2 = UnpackNormal(tex2D( _BumpMap, uv02 )).rgb;
				half3 bump = (bump1 + bump2) * 0.5 * _DistortPower;

                fixed4 col = tex2D(_MainTex, half2(i.uv.x, 1 - i.uv.y) + bump);

                fixed targetPosY = _TargetWaterHeight;

                fixed waterDeep = (targetPosY - i.worldPos.y) * _WaterDeep;

                col.rgb = lerp(col.rgb, col.rgb * _UnderColor.rgb, waterDeep);

                col.a = saturate(col.a - waterDeep) * _UnderColor.a;

                APPLY_EFFECT_DARK_SCENE(col);

                return col ;
            }
            ENDCG
        }
        
    }
}
