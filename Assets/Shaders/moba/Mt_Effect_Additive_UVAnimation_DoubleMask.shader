Shader "Mt/Effect/Additive UVAnimation Mask(Double)"
{
	Properties
	{
		_MainTex ("Main Texture", 2D) = "white" {}
		_UVRotate ("UV Rotate(1:90 2:180 3:270)",Range(0,4)) = 0
		[HDR]_Color("Main Color", Color) = (1.0,1.0,1.0,1.0)
		_DissolveTex("Mask", 2D) = "white" {}
		_Mask("Mask Value",Range(-1,1)) = 0
		_MaskToggle("Mask Toggle(0 : Value; 1 : Custom)",float) = 0.0
		_MaskSecUVOffset("(RG)MsUV Offset,(BA)",Vector) = (0.2,0,1.0,1.0)
		_SpeedX("Horizontal Speed", Range(-5.0, 5.0)) = 0
		_SpeedY("Vertical Speed",Range(-5.0, 5.0)) = 0
		_SpeedToggle("Speed Toggle",float) = 0.0
		_NormalFactor("Z Depth Offset",Range(-1.0,1.0)) = 0
		//_GradientValue("不要修改！Effect Fade Value",Float) = 1

		// 特效裁切用
		[HideInInspector]_MainTexUVRotation("特效裁切用: MainTex UV旋转值(x:sinθ, y:cosθ)(不要修改!)", Vector) = (0, 1, 0, 0)
		[HideInInspector]_MainTexUVScaleOnCenter("特效裁切用: MainTex UV是否基于中心点缩放(不要修改!)", Float) = 0.0

		[HideInInspector]_DissolveTexUVRotation("特效裁切用: DissolveTex UV旋转值(x:sinθ, y:cosθ)(不要修改!)", Vector) = (0, 1, 0, 0)
		[HideInInspector]_DissolveTexUVScaleOnCenter("特效裁切用: DissolveTex UV是否基于中心点缩放(不要修改!)", Float) = 0.0
	}
	SubShader
	{
		Tags
		{
			"RenderType"="Transparent"
			"Queue" = "Transparent"
		}
		LOD 100
		Blend SrcAlpha One,Zero One
		Cull Off
		Lighting Off
		ZWrite Off
		Pass
		{
			CGPROGRAM
			#pragma shader_feature _OFFSCREEN_RENDER
			#pragma vertex effectVertexDissUV
			#pragma fragment frag
			#include "UnityCG.cginc"
			#include "ACGameCG.cginc"
			fixed4 frag (effect_v2f_diss i) : SV_Target
			{
				fixed4 color = fixed4(0,0,0,1);
				half2 uv = i.uv.xy;
				fixed4 main_var = tex2D(_MainTex, uv) * _MaskSecUVOffset.z;
				//combine mask sector.
				fixed4 mask_var = tex2D(_DissolveTex,i.uv1.xy);
				i.uv1.x += _MaskSecUVOffset.x;
				i.uv1.y += _MaskSecUVOffset.y;
				fixed4 mask_var2 = tex2D(_DissolveTex,i.uv1.xy);
				mask_var = (mask_var + mask_var2) * _MaskSecUVOffset.w * 0.5;
				color = main_var;
				color.rgb = main_var.rgb * _Color.rgb * i.color.rgb * 2;
				color.a = saturate(color.a * i.color.a * saturate(main_var.r - lerp(_Mask,i.uv.w,_MaskToggle)) * _Color.a);

				OffScreenLateZ(color.a, i.vertex);
				return color;
			}
			ENDCG
		}
	}
	FallBack "Mt/Effect/Additive UVAnimation Base"
}
