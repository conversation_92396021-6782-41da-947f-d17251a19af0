Shader "Mt/Effect/LEDLoop"
{
	Properties
	{
 
		_MainTex ("Main Texture", 2D) = "white" {}
 
		_UVRotate ("UV Rotate(1:90 2:180 3:270)",Range(0,4)) = 0
		[HDR]_Color("Main Color", Color) = (1.0,1.0,1.0,1.0)
		_ScreenColor("Screen Color", Color) = (0,0,0,0)
		_DissolveTex("Disturbance Texture", 2D) = "white" {}
		_Dissolve("Disturbance Strength", Range(-1,1)) = 0
		_DissolveToggle("Disturbance Toggle(0 : Value; 1 : Custom)",float) = 0.0

		[HDR]_MaskColor("Mask Color", Color) = (1.0,1.0,1.0,1.0)
		_Dissolve02("Dissolve Value", Range(-1,1)) = 0
		_DissolveIntensity("Dissolve Intensity",Range(0,10)) = 0
		_DissolveSmooth("Dissolve Smooth",Range(0,100)) = 1

		_SpeedX("Horizontal Speed", Range(-5.0, 5.0)) = 0
		_SpeedY("Vertical Speed",Range(-5.0, 5.0)) = 0
		_SpeedX2("Noise Horizontal Speed", Range(-5.0, 5.0)) = 0
		_SpeedY2("Noise Vertical Speed",Range(-5.0, 5.0)) = 0 
	 
		[Toggle(_ISCLAMP)]_IsClamp("Clamp(1) Repeat(0)",Int) = 0.0
 
		[Space(10)]
        [Header(Rim Lighting)]
        [Space(5)]
		[Toggle]_RE("边缘光 开启",Float) = 0
		[HDR]_EdgeColor("边缘光颜色", Color) = (1, 1, 1, 1)
		_RimSmoothMin("最小平滑度",Range(0,1)) = 0
		_RimSmoothMax("最大平滑度",Range(0,1)) = 1
 
	}
	SubShader
	{
		Tags
		{
			"RenderType"="Transparent"
			"Queue" = "Transparent"
		}
			LOD 100
		Blend SrcAlpha OneMinusSrcAlpha 
	    	ZWrite Off
		Pass
		{
			CGPROGRAM
	 
			#pragma vertex vert 
			#pragma fragment frag  
			#pragma shader_feature _RE_ON
			#pragma multi_compile _ _ISCLAMP
			
			#include "UnityCG.cginc"
			#include "ACGameCG.cginc"

			fixed _RimSmoothMin, _RimSmoothMax;
            float4 _EdgeColor;
			float4 _ScreenColor;
            float _DissolveDir,_Dissolve02;
			half _DissolveIntensity,_DissolveSmooth; 
			float4 _MaskColor ; 
		 
			struct appdate_eff
			{
				float4 vertex : POSITION;
				 float3 normal : NORMAL;
				float4 uvmirror : TEXCOORD0;
				float4 uv: TEXCOORD1;
				 
			};
			struct effect_v2f_eff
			{
				float4 vertex : SV_POSITION;
				float3 normal : NORMAL;

				half4 color : COLOR;
				float4 uv : TEXCOORD0; 
                half4 uv2 : TEXCOORD1;
				float3 viewDir : TEXCOORD3;
				float3 worldPos : TEXCOORD4;
	 
		 
			};

        
			effect_v2f_eff vert(appdate_eff v)
			{
				effect_v2f_eff o = (effect_v2f_eff)0;
				o.vertex = UnityObjectToClipPos(v.vertex);

 				float4 normal4 = float4(v.normal, 0.0);
                o.normal = normalize(mul(normal4, unity_WorldToObject).xyz);
                o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
                o.viewDir = normalize(_WorldSpaceCameraPos - o.worldPos );

                o.uv.xy = TRANSFORM_TEX(v.uvmirror.xy,_MainTex);
                o.uv2.xy = TRANSFORM_TEX(v.uvmirror.xy,_DissolveTex);
			 
                o.uv.xy = GetAnimationUV(o.uv,o.uv,half2(_SpeedX,_SpeedY));
                o.uv2.xy = GetAnimationUV(o.uv2.xy,o.uv2.xy,half2(_SpeedX2,_SpeedY2));
		 
				return o;
			}
			
			fixed4 frag (effect_v2f_eff i) : SV_Target
			{
				half edgeFactor = abs(dot(i.viewDir, i.normal));
                edgeFactor = smoothstep(_RimSmoothMin, _RimSmoothMax,edgeFactor);
                half oneMinusEdge = 1.0 - edgeFactor;
 
                half2 uv2 = i.uv2;
				half2 uv = i.uv;
           
			    half2 noise_uv = tex2D(_DissolveTex,float2(uv2.x,uv2.y)).xy * _Dissolve;  
				fixed4 maintex = tex2D(_MainTex,i.uv+float2(noise_uv.x,0));
 
				fixed dissolve = maintex.r; 
					  dissolve = saturate(dissolve)*_DissolveSmooth - lerp(_DissolveSmooth,-1,_Dissolve02);
					  dissolve = saturate(dissolve);
	 
                maintex.rgb = maintex.r*_Color+_ScreenColor;
				maintex.a *= dissolve*_Color.a;

				 #ifdef _RE_ON
					half3 rimColor = _EdgeColor * oneMinusEdge;
					 maintex.rgb += rimColor;
				#endif
				return maintex;
			}
			ENDCG
		}
	}
 
}