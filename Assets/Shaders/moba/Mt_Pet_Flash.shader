Shader "Mt/Pet/Flash(流光)"
{
    Properties
    {
        _MainTex ("基础贴图", 2D) = "white" {}
        [Space(10)]
        _PatternTex ("流光 图案(RGB分三层)", 2D) = "white" {}
        [HDR]_Color01("R通道 颜色",Color)=(0,0,0,0)
        [HDR]_Color02("G通道 颜色",Color)=(0,0,0,0)
        [HDR]_Color03("B通道 颜色",Color)=(0,0,0,0)

        _NoiseTex ("流光 遮罩(RGB分别对应流光图案RGB)", 2D) = "white" {}
        _Speed("流光 速度", Range(-1, 1)) = 0.2
    }
    SubShader
    {
        Tags { "RenderType"="Opaque" }

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float3 normal : NORMAL;
                float4 color : COLOR ;
            };

            struct v2f
            {
                float4 vertex : SV_POSITION;
                float2 uv : TEXCOORD0;
    
                float4 vertexColor : COLOR0;
            };

            sampler2D _MainTex, _PatternTex, _NoiseTex;
            fixed _Speed;
            half4 _Color01, _Color02, _Color03;

            v2f vert (appdata v)
            {
                v2f o;
                UNITY_INITIALIZE_OUTPUT(v2f, o);
                o.vertex = UnityObjectToClipPos(v.vertex);

                o.uv = v.uv;

                //o.vertexColor = v.color;

                return o;
            }

            
            fixed4 frag (v2f i) : SV_Target
            {
                // sample the texture
                fixed4 col = tex2D(_MainTex, i.uv);

                fixed4 pattern = tex2D(_PatternTex, i.uv);

                half time = frac(_Time.y * _Speed ) ;

                fixed4 noise = tex2D(_NoiseTex, float2(i.uv.x , i.uv.y + time));
                
                half4 pattern01 = pattern.r * noise.x  * _Color01;
                half4 pattern02 = pattern.g * noise.y  * _Color02;
                half4 pattern03 = pattern.b * noise.z  * _Color03;

                pattern01 = pattern01 + pattern02 + pattern03 ;

                col += pattern01;

                return col;
            }
            ENDCG
        }

        Pass 
		{
			Tags{ "LightMode" = "ShadowCaster" }

			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			#pragma multi_compile_shadowcaster
			#pragma fragmentoption ARB_precision_hint_fastest
			#include "UnityCG.cginc"
			
			struct v2f
			{
				V2F_SHADOW_CASTER;

			};

			v2f vert(appdata_base v) 
			{
				v2f o;
				TRANSFER_SHADOW_CASTER_NORMALOFFSET(o)
				return o;
			}

			float4 frag(v2f i) :SV_Target
			{
				SHADOW_CASTER_FRAGMENT(i)
			}

			ENDCG
		}
    }
}
