Shader "Mt/Effect/Screen_Distortion"
{
	Properties
	{
		_DistortOffsetX("OffsetX",Range(-0.1,0.1)) = 0
		_DistortOffsetY("OffsetY",Range(-0.1,0.1)) = 0
		_DistortScale("Scale",Range(1,0.5)) = 1
		
		
	}

	Category
	{
		Tags
		{
			"IgnoreProjector" = "True"
			"Queue"="Transparent+500"
			"RenderType"="Transparent"
		}
		SubShader
		{
			
			LOD 1000
			Cull Off Lighting Off ZWrite Off
			ZTest [_ZTestMode]
			GrabPass
			{
				"_EffectGrabTexture"
			}
			Pass
			{
				CGPROGRAM
				#pragma vertex vert
				#pragma fragment frag
				#pragma target 2.0
				#pragma multi_compile_particles
				#include "UnityCG.cginc"

				struct appdata
				{
					float4 vertex : POSITION;
					float2 uv : TEXCOORD0;
				};

				struct v2f
				{
					float2 uv : TEXCOORD0;
					float4 vertex : SV_POSITION;
					float4 grabPos:TEXCOORD1;
				};
				
				
				fixed _DistortOffsetX;
				fixed _DistortOffsetY;
				fixed _DistortScale;
				uniform sampler2D _EffectGrabTexture;
				
				v2f vert(appdata v)
				{
					v2f o;
					o.vertex = UnityObjectToClipPos(v.vertex); 
					o.uv =v.uv;
					o.grabPos = ComputeGrabScreenPos(o.vertex);
					return o;
				}

				fixed4 frag(v2f i) : SV_Target
				
				{   
					float2 uv =i.grabPos.xy / i.grabPos.w; 
					uv -= float2(0.5,0.5);
					uv *=  _DistortScale ;
					uv += float2(0.5,0.5); 

					uv.x +=   _DistortOffsetX;
					uv.y +=   _DistortOffsetY;

					half4 bgcolor = tex2D (_EffectGrabTexture, uv ); 
					return  bgcolor ;
				}
				ENDCG
			}
		}
		Fallback Off
	}

}