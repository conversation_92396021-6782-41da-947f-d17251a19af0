Shader "Mt/Effect/CyberGlitch"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
		[HDR]_Color("Tint Color", Color) = (1,1,1,1)
		_MaskTex1 ("Mask Texture1", 2D) = "white" {}
		_Mask1 ("Mask 1 Speed", Range(0,1)) = 0.1
		_Mask1Intens ("Mask 1 Intensity", Range(0,5)) = 1
       	_MaskTex2 ("Mask Texture2", 2D) = "white" {}
		_Mask2 ("Mask 2 Speed", Range(0,1)) = 0.1
		_Mask2Intens ("Mask 2 Intensity", Range(0,5)) = 1
		_Translucency ("Translucency", Range(0,1)) = 0.5
		_NormalFactor("Z Depth Offset",Range(-1.0,1.0)) = 0

		// 特效裁切用
		[HideInInspector]_MainTexUVRotation("特效裁切用: MainTex UV旋转值(x:sinθ, y:cosθ)(不要修改!)", Vector) = (0, 1, 0, 0)
		[HideInInspector]_MainTexUVScaleOnCenter("特效裁切用: MainTex UV是否基于中心点缩放(不要修改!)", Float) = 0.0

		[HideInInspector]_MaskTex1UVRotation("特效裁切用: MaskTex1 UV旋转值(x:sinθ, y:cosθ)(不要修改!)", Vector) = (0, 1, 0, 0)
		[HideInInspector]_MaskTex1UVScaleOnCenter("特效裁切用: MaskTex1 UV是否基于中心点缩放(不要修改!)", Float) = 0.0

		[HideInInspector]_MaskTex2UVRotation("特效裁切用: MaskTex2 UV旋转值(x:sinθ, y:cosθ)(不要修改!)", Vector) = (0, 1, 0, 0)
		[HideInInspector]_MaskTex2UVScaleOnCenter("特效裁切用: MaskTex2 UV是否基于中心点缩放(不要修改!)", Float) = 0.0
    }
    SubShader
    {
        Tags
        {
            "RenderType" = "Transparent"
            "Queue" = "Transparent"
        }
        LOD 100
        Blend SrcAlpha One,Zero One
        Cull Off
        Lighting Off
        ZWrite Off

		Pass
		{
			CGPROGRAM
			//#pragma shader_feature _OFFSCREEN_RENDER
			#pragma vertex vert
			#pragma fragment frag
			#include "UnityCG.cginc"
			//#include "ACGameCG.cginc"

			
			uniform sampler2D _MainTex;
			uniform float4 _MainTex_ST;
			uniform sampler2D _MaskTex1;
			uniform float4 _MaskTex1_ST;
			uniform sampler2D _MaskTex2;
			uniform float4 _MaskTex2_ST;
			uniform half _Mask1;
			uniform half _Mask2;
			uniform float _Mask1Intens;
			uniform float _Mask2Intens;
			uniform half _Translucency;
			uniform half4 _Color;
			uniform float _NormalFactor;


			struct effectAppdata
			{
				float4 vertex : POSITION;
				float4 texcoord : TEXCOORD0;
				half4 color : COLOR;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct v2f
			{
				float4 vertex : SV_POSITION;
				half4 color : COLOR;
				half2 uv : TEXCOORD0;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			inline half remap(half x, half t1, half t2, half s1, half s2)
			{
				return (x - t1) / (t2 - t1) * (s2 - s1) + s1;
			}
			
			v2f vert(effectAppdata v)
			{
				v2f o = (v2f)0;

				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_TRANSFER_INSTANCE_ID(v, o);

				o.vertex = UnityObjectToClipPos(v.vertex);
				o.uv.xy = v.texcoord.xy;
				o.color.rgba = v.color.rgba;
#if UNITY_UV_STARTS_AT_TOP
				o.vertex.z += _NormalFactor;
#else
				o.vertex.z -= _NormalFactor;
#endif
				return o;
			}

			fixed4 frag(v2f i) : SV_Target
			{
				fixed4 color = fixed4(0,0,0,1);
				
				half2 maskUV1 = TRANSFORM_TEX(i.uv.xy, _MaskTex1);
				maskUV1 = maskUV1.y + frac(_Mask1 * _Time.y);
				half2 maskUV2 = TRANSFORM_TEX(i.uv.xy, _MaskTex2);
				maskUV2 = maskUV2.y  + frac(_Mask2 * floor(_Time.y*10));
				half2 uv1 = i.uv.xy;
				fixed4 mask1_var = tex2D(_MaskTex1, maskUV1);
				fixed4 mask2_var = tex2D(_MaskTex2, maskUV2);

				float adjusted = remap(mask2_var.x, 0, 1, -_Mask2Intens, _Mask2Intens) * 20.0 * remap(mask1_var.x, 0, 1, -_Mask1Intens, _Mask1Intens) / 50.0;
				
				half2 mainUV =  TRANSFORM_TEX(uv1, _MainTex);

				fixed4 main_var  = tex2D(_MainTex, half2(mainUV.x + adjusted, mainUV.y + 2*adjusted));
				
				color.rgb = main_var.rgb * i.color.rgb * _Color.rgb;

				color.a = main_var.a * i.color.a * (saturate(mask2_var.r + _Translucency)) * _Color.a;
				return color;
			}
			ENDCG
		}
    }
}
