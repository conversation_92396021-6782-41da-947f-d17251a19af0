Shader "Mt/Hero/Hero_StarFlash"
{
    Properties
    {
        _Color ("Main Color", Color) = (1,1,1,1)
        _MainTex ("Main Texture", 2D) = "white" {}

        [HDR]_starcol("颜色", Color) = (1,1,1,1)
		[NoScaleOffset]_starmap("星辰 贴图 ",2D) = "black"{}
        _StarOffsetSpeed ("星辰缩放(xy)和速度(zw)", Vector) = (1,1,-0.05,0.05)
        _starMask("遮罩 贴图",2D) = "white"{}

        [HDR]_RimColor ("边缘光 颜色", Color) = (1,1,1,1)
        _RimRange("边缘光 范围",Range(0.1,1)) = 1
		_RimSmoothMin("Smooth Min",Range(0,1)) = 0
		_RimSmoothMax("Smooth Max",Range(0,1)) = 1
    }
    SubShader
    {
        Tags { "RenderType"="Opaque" "Queue" = "Geometry+200"}

        Pass
        {
            Stencil
			{
				Ref 1
				Comp Always
				Pass Replace
	        }

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"

            struct appdata {
				float4 vertex : POSITION;
				float3 normal : NORMAL;
				float4 texcoord : TEXCOORD0;
				float4 color : COLOR;
			}; 

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                float2 screenUV : TEXCOORD1;
                float3 worldNormal : TEXCOORD2;
                float3 worldViewDir : TEXCOORD3;

            };

            half4 _Color, _RimColor;
            sampler2D _MainTex;
            float4 _MainTex_ST;

            half4 _starcol;
            sampler2D _starmap, _starMask;

            half4 _StarOffsetSpeed;

            fixed _RimRange, _RimSmoothMin, _RimSmoothMax;


            v2f vert (appdata v)
            {
                v2f o;

                UNITY_INITIALIZE_OUTPUT(v2f, o);

                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.texcoord, _MainTex);

                o.screenUV.xy = o.vertex.xy;

                o.worldNormal = UnityObjectToWorldNormal(v.normal);
                half3 worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
				o.worldViewDir = normalize(_WorldSpaceCameraPos - worldPos );

                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                half3 worldNormal = normalize(i.worldNormal);
                half3 worldViewDir = normalize(i.worldViewDir);
                half nv = saturate(dot(worldNormal , worldViewDir)) ;
                half rim = saturate(_RimRange - nv);
                rim = smoothstep(_RimSmoothMin, _RimSmoothMax,rim);

                fixed4 col = tex2D(_MainTex, i.uv) * _Color;
                half4 rimColor = rim * _RimColor;
                col += rimColor;

                float2 star_uv_base = i.screenUV.xy  ;
                float2 StarUV = star_uv_base * _StarOffsetSpeed.xy + frac(_Time.y * _StarOffsetSpeed.zw) ;

                fixed4 startex = tex2D(_starmap, StarUV) * _starcol * (1 - rim);
                fixed4 mask = tex2D(_starMask, i.uv);

                col += startex * mask.r;

                return col;
            }
            ENDCG
        }
    }
}
