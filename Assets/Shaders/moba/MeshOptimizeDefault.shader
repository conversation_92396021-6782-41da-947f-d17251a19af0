// 此Shader不用于渲染，用于在Build AB优化Mesh通道时保留对Normal、Color、UV0、UV1等通道的引用
Shader "MeshOptimize/Default"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
    }
	
    SubShader
    {
        Tags { "RenderType"="Opaque" }

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
				float2 uv1 : TEXCOORD1;
				float3 normal : NORMAL;
				fixed4 color : COLOR;
            };

            struct v2f
            {
                float4 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
				fixed3 normalDir : TEXCOORD1;
				fixed4 color : TEXCOORD2;
            };

            sampler2D _MainTex;
            float4 _MainTex_ST;

            v2f vert (appdata v)
            {
                v2f o;

                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv.xy = TRANSFORM_TEX(v.uv, _MainTex);
				o.uv.zw = TRANSFORM_TEX(v.uv1, _MainTex);
				o.normalDir = UnityObjectToWorldNormal(v.normal);
				o.color = v.color.rgba;

                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                // sample the texture
                fixed4 col = tex2D(_MainTex, i.uv.xy);
				col *= tex2D(_MainTex, i.uv.zw);
				col += i.color;
				col.rgb += i.normalDir;

                return col;
            }
            ENDCG
        }
    }
}
