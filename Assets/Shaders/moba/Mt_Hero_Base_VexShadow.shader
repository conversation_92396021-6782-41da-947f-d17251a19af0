Shader "Mt/Hero/Base(VexShadow)" 
{
    Properties 
	{
		[Toggle]_Void("Is Void?",float) = 0
		_VoidTexture("Void Flowing Texture",2D) = "black"{}
		[HDR]_VoidColor("Void Color",Color) = (1.0,1.0,1.0,1.0)
		_VoidScreenSpeedX("Void Screen Speed X",Range(-5,5)) = 0
		_VoidScreenSpeedY("Void Screen Speed Y",Range(-5,5)) = 0
		[HDR]_VoidFlowingColor("Void Flowing Color",Color) = (0.0,0.0,0.0,0.0)

		[Toggle]_CutoutAlpha("Cutout Alpha",float) = 0

		_MainTex("Texture", 2D) = "white" {}
		_MainCol("Main Color", Color) = (1.0,1.0,1.0,1.0)

		_SpecularTex("Specular&Flowing Mask", 2D) = "black" {}
		_ScreenSpeedX("Screen Speed X",Range(-5,5)) = 0
		_ScreenSpeedY("Screen Speed Y",Range(-5,5)) = 0
		[HDR]_FlowingColor("Flowing Color", Color) = (1.0,1.0,1.0,1.0)

		_Alpha("Alpha", Range(0,1)) = 1

		_SpecularExponent("Specular Exponent", Range(0.001,16.0)) = 3.0
		_SpecularColor("Specular Color", Color) = (1.0,1.0,1.0,1.0)
		_SpecularScale("Specular Scale", Range(0.0,10.0)) = 8.2

		[Toggle]_CorrectMatcap("Use Matcap",float) = 0
		//_SideLightDirection("SideLight Direction",vector) = (1,0.3,-0.5,0.0)
		[HDR]_SideLightColor("Character SideLight Color",Color) = (.75,.75,.75,1.0)

		_SpeedFresnel("Fresnel Speed",float) = 1
		[ToggleOff]_FXFresnel("Is Effect Used",float) = 0
		_FresnelFromValue("Fresnel From Value",Range(0,1)) = 0
		_FresnelToValue("Fresnel To Value",Range(0,1)) = 1
		_FresnelExponent("Fresnel Exponent", Range(0.01, 8)) = 3.6
		[HDR]_FresnelColor("Fresnel Color", Color) = (0.08499137,0.3454357,0.9632353,1)
		_FresnelScale("Fresnel Scale", Range(0.1, 10)) = 5.0
		_SinAmplitude("Fresnel Amplitude", Range(0.0,1.0)) = 1.0

		_ShadowPlane("Shadow Plane", vector) = (0,1,0,0)
    }
	SubShader
	{
		LOD 150
		Tags
		{
			"RenderType" = "Transparent"
			"Queue" = "Transparent"
			"Target" = "Hero"
		}
		Pass
		{
			Name "FORWARD"
			Tags
			{
				"LightMode" = "ForwardBase"
			}
			
		Blend SrcAlpha OneMinusSrcAlpha, Zero OneMinusSrcAlpha

			// Vex阴影不需要描边, 但是需要防止重叠绘制
			Stencil
			{
				Ref 2
				Comp Less
				Pass Replace
			}

			CGPROGRAM
			#include "FKHEROCGINCLUDE.cginc"
			#pragma vertex vert
			#pragma fragment fragTransparent
			#pragma shader_feature _VOID_ON
			#pragma shader_feature _CORRECTMATCAP_ON
			#pragma shader_feature _CUTOUTALPHA_ON

			ENDCG
		}
	}
}
