#ifndef OUTGAME_V2_SHADINGFUNCTION_INCLUDE
#define OUTGAME_V2_SHADINGFUNCTION_INCLUDE

#include "OutGameV2_CommonProperty.cginc"
#include "OutGameV2_VertexFactory.cginc"
#include "OutGameV2_FragInput.cginc"
#include "UnityStandardInput.cginc"
half4 SampleMainTex(in FragInput frag)
{
    // return 0;//
    return tex2D(_MainTex, GetVertexUV0(frag));
}

half1 GetNDotL(in FragInput frag)
{
    half1 ndotl = dot(GetVertexNormal(frag), _WorldSpaceLightPos0.xyz);
    return saturate(ndotl);
}
float2 CenterZoomUVBySize(float2 texcoord, float size)
{
    return texcoord * size - (size * 0.5 - 0.5);
}




#ifdef TANGENT_SPACE

half3 NormalInTangentSpace(half2 uv)
{
// return 0;
    float2 bumpuv = uv;
    half3 normalTangent = UnpackScaleNormal(tex2D(_Bump, bumpuv), _BumpScale);
    
    return normalize(normalTangent);
}
half3 TransformTangentToWorld(in FragInput frag, half3 tangentVector)
{
    return GetVertexTangent(frag) * tangentVector.x + GetVertexBiNormal(frag) * tangentVector.y + GetVertexNormal(frag) * tangentVector.z;
}

half3 GetDetailNormal(in FragInput frag)
{
    half3 normalInTangent = normalize(tex2D(_Bump, GetVertexUV0(frag)).rgb);        //todo: decode
    return TransformTangentToWorld(frag, normalInTangent);
}

half3 Tangent2World(VertexOutput i, half3 normal)
{
    return normalize(i.BiNormal * normal.y + i.Tangent * normal.x + i.Normal * normal.z);
}

#endif

half3 SampleLightmap(in FragInput frag)
{
#ifdef LIGHTMAP_ON
    half4 bakedColorTex = UNITY_SAMPLE_TEX2D(unity_Lightmap, GetLightmapUV(frag));
    half3 bakedColor = DecodeLightmap(bakedColorTex);
    return bakedColor;
#else
    return half3(1, 1, 1);
#endif
}


// SH
float AreaElement(float x, float y)
{
    return atan2(x * y, sqrt(x * x + y * y + 1));
}

float DifferentialSolidAngle(float textureSize, float2 uv)
{
    float inv = 1.0 / textureSize;
    float u = 2.0 * (uv.x + 0.5 * inv) - 1;
    float v = 2.0 * (uv.y + 0.5 * inv) - 1;
    float x0 = u - inv;
    float y0 = v - inv;
    float x1 = u + inv;
    float y1 = v + inv;
    return AreaElement(x0, y0) - AreaElement(x0, y1) - AreaElement(x1, y0) + AreaElement(x1, y1);
}

float rand(float2 n)
{
    return frac(sin(dot(n, float2(12.9898, 4.1414))) * 43758.5453);
}

float Y0(float3 v)
{
    return 0.2820947917f;
}

float Y1(float3 v)
{
    return 0.4886025119f * v.y;
}

float Y2(float3 v)
{
    return 0.4886025119f * v.z;
}

float Y3(float3 v)
{
    return 0.4886025119f * v.x;
}

float Y4(float3 v)
{
    return 1.0925484306f * v.x * v.y;
}

float Y5(float3 v)
{
    return 1.0925484306f * v.y * v.z;
}

float Y6(float3 v)
{
    return 0.3153915652f * (3.0f * v.z * v.z - 1.0f);
}

float Y7(float3 v)
{
    return 1.0925484306f * v.x * v.z;
}

float Y8(float3 v)
{
    return 0.5462742153f * (v.x * v.x - v.y * v.y);
}

//GET TEXEL DIRECTION VECTOR FROM UV
float3 RfromUV(uint face, float u, float v)
{
    float3 dir;

    switch (face)
    {
    case 0: //+X
        dir.x = 1;
        dir.y = v * -2.0f + 1.0f;
        dir.z = u * -2.0f + 1.0f;
        break;

    case 1: //-X
        dir.x = -1;
        dir.y = v * -2.0f + 1.0f;
        dir.z = u * 2.0f - 1.0f;
        break;

    case 2: //+Y
        dir.x = u * 2.0f - 1.0f;
        dir.y = 1.0f;
        dir.z = v * 2.0f - 1.0f;
        break;

    case 3: //-Y
        dir.x = u * 2.0f - 1.0f;
        dir.y = -1.0f;
        dir.z = v * -2.0f + 1.0f;
        break;

    case 4: //+Z
        dir.x = u * 2.0f - 1.0f;
        dir.y = v * -2.0f + 1.0f;
        dir.z = 1;
        break;

    case 5: //-Z
        dir.x = u * -2.0f + 1.0f;
        dir.y = v * -2.0f + 1.0f;
        dir.z = -1;
        break;
    }

    return dir;
}
#ifdef _RIM_PRO
half3 RimPro( half3 rimDir,  half3 normal,half _RimPow,half _RimScal ,fixed3 _RimCol)
{
    half hitRim = saturate(dot(normal, rimDir));
    //hitRim = pow(hitRim, _RimPow); 
    return _RimCol * saturate(hitRim) * _RimScal;
}

half RimMask(half3 normal ,half3 forwordDir) {
    half mask = saturate(dot(normal, forwordDir));
    mask = 1 - mask;
    return mask;// *mask* mask* mask;
}
half3 DepthRim(half4 screenUv,half3 wNor,float3 rimProDir1,float3 rimProDir2,half rimMask1,half rimMask2,half3 vierDir)
{
    half RimWidth = _RimShape.x;
    half MinRange = _RimShape.y;
    half MaxRange = _RimShape.z;
    half RimPow = 0.5;
    float3 normalVS = mul(UNITY_MATRIX_V, float4(wNor, 0.0)).xyz;
    float3 NdotL =smoothstep(0,RimPow, (dot(wNor,rimProDir2)+0.5)*0.5);
    float3 NdotL2 =smoothstep( 0,RimPow,(dot(wNor,rimProDir1)+0.5)*0.5);
    //原屏幕空间uv
    float2 scrUV = screenUv.xy / screenUv.w;
    half depth1 = SAMPLE_DEPTH_TEXTURE(_CameraDepthTexture, scrUV).r;
    half linearEyeDepth = LinearEyeDepth(depth1);
    //偏移屏幕空间uv
    half2 offsetuv1 = scrUV + normalVS.xy * RimWidth * 0.01 ;
    half depthTex = SAMPLE_DEPTH_TEXTURE(_CameraDepthTexture, offsetuv1); 
    half depthScene = LinearEyeDepth(depthTex);
    //深度差
    half depthDiff = depthScene - linearEyeDepth; 					
    //过度
    half intensity = smoothstep(0.2*(1-MinRange) , 0.2*(1-MaxRange), depthDiff) ;
    //菲涅尔过渡
    float NdotV = saturate(dot(wNor, -vierDir));
    float fresnel = pow((1 - NdotV), max(0.01, 1));
    fresnel = smoothstep(min(0.99, 1-MaxRange),min(0.99, 1-MaxRange)+RimPow, fresnel);
    //顶点色遮蔽光
    half Diff = saturate(saturate(intensity+fresnel )* NdotL*rimMask2);
    half Diff2 =saturate( saturate(intensity+fresnel) * NdotL2*rimMask1);

    half3 rimColor1 = lerp(0,_RimCol2.xyz,Diff);
    half3 rimColor2 = lerp(0,_RimCol1.xyz,Diff2);
	
    half3 col = rimColor1+rimColor2;
    return col;

}
#endif

#endif