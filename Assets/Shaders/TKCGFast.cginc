#ifndef TK_CG_FAST_INCLUDE
#define TK_CG_FAST_INCLUDE

//fast object to world
inline float3 ObjectToWorldFast(const in float3 objectPosition)
{
    float4x4 M_T = transpose(unity_ObjectToWorld);
    float3 result = M_T[0].xyz * objectPosition.xxx + M_T[3].xyz;
           result = M_T[1].xyz * objectPosition.yyy + result.xyz;
           result = M_T[2].xyz * objectPosition.zzz + result.xyz;
    return result;
}

//fast world to clip
inline half4 WorldToClipFast(const in float3 worldPosition)
{
    float4x4 VP_T = transpose(unity_MatrixVP);
    float4 result = VP_T[0].xyzw * worldPosition.xxxx + VP_T[3].xyzw;
           result = VP_T[1].xyzw * worldPosition.yyyy + result.xyzw;
           result = VP_T[2].xyzw * worldPosition.zzzz + result.xyzw;
    return result;
}

//fast object to clip
inline half4 ObjectToClipFast(const in float3 objectPosition)
{
    return WorldToClipFast(ObjectToWorldFast(objectPosition));
}
#endif