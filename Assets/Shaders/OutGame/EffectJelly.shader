Shader "OutGame/Effect/Jelly"
{
	Properties
	{
		[Header(Main Color)]
        [Space(5)]
		_Brightness("亮度", Range(0,2)) = 1
		_Color("主纹理颜色", Color) = (1, 1, 1, 1)
		_MainTex("纹理贴图 ",2D) = "white"{}
		_BumpMap ("法线贴图", 2D) = "bump" {}
		_BumpValue ("法线强度", Range(0,2)) = 1
		[Space(5)]
		[NoScaleOffset]_MatcapTex("质感贴图(Matcap Texture)", 2D) = "black" {}

		[Space(10)]
        [Header(Rim Lighting)]
        [Space(5)]
		//_InnerColor("内部颜色", Color) = (1, 1, 1, 1)
		_EdgeColor("边缘光颜色", Color) = (1, 1, 1, 1)
		_RimSmoothMin("最小平滑度",Range(0,1)) = 0
		_RimSmoothMax("最大平滑度",Range(0,1)) = 1
		
		[Space(10)]
        [Header(Parallax Effect)]
        [Space(5)]
        _starcol("颜色", Color) = (1,1,1,1)
		_starmap("视差纹理(Tilling表示缩放, Offset表示流动速度) ",2D) = "black"{}
		_Parallax("视差深度", Range(0,3)) = 0.0

	}
	
	SubShader
	{
		Tags { "RenderType"="Opaque" "Queue"="Geometry" }

        Pass
		{
			CGPROGRAM

			#pragma vertex vert
			#pragma fragment frag

            #include "UnityCG.cginc"
			#include "AutoLight.cginc"

			// Properties
			sampler2D		_MainTex, _BumpMap, _MatcapTex;
			float4 _BumpMap_ST;
			uniform float4	_Color;
			uniform float4	_EdgeColor, _InnerColor;
			fixed _Brightness, _RimSmoothMin, _RimSmoothMax;
			fixed _BumpValue;

			half _Parallax;
			half4 _starcol;
            sampler2D _starmap;
            float4 _starmap_ST;

			struct a2v
			{
				float4 vertex : POSITION;
				float3 normal : NORMAL;
				float4 tangent : TANGENT;
				float4 texcoord : TEXCOORD0;
			};

			struct v2f
			{
				float4 pos : SV_POSITION;
				float3 normal : NORMAL;

				float3 viewDir : TEXCOORD1;
                float3 worldPos : TEXCOORD2;
				float3 tangentViewDir : TEXCOORD3;
				float2 uv : TEXCOORD4;

				float3	TtoV0 : TEXCOORD5;
				float3	TtoV1 : TEXCOORD6;
				float3	TtoV2 : TEXCOORD7;
			};

			v2f vert(a2v v)
			{
				v2f o;

				UNITY_INITIALIZE_OUTPUT(v2f, o);

				o.pos = UnityObjectToClipPos(v.vertex);
				float4 normal4 = float4(v.normal, 0.0);
				o.normal = normalize(mul(normal4, unity_WorldToObject).xyz);
                o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
				o.viewDir = normalize(_WorldSpaceCameraPos - o.worldPos );

				o.uv = v.texcoord;

				TANGENT_SPACE_ROTATION;
				o.TtoV0 = normalize(mul(rotation, UNITY_MATRIX_IT_MV[0].xyz));
				o.TtoV1 = normalize(mul(rotation, UNITY_MATRIX_IT_MV[1].xyz));
				o.TtoV2 = normalize(mul(rotation, UNITY_MATRIX_IT_MV[2].xyz));
                o.tangentViewDir = mul(rotation,ObjSpaceViewDir(v.vertex)).xyz;

				return o;
			}

			float4 frag(v2f i) : COLOR 
			{
				float3 normal = UnpackNormal(tex2D(_BumpMap, i.uv.xy * _BumpMap_ST.xy));
				normal.xy *= _BumpValue;
				normal.z = sqrt(1.0- saturate(dot(normal.xy ,normal.xy)));
				normal = normalize(normal);

				half3 viewNormal;

                //viewNormal = mul(UNITY_MATRIX_V,i.normal);
				viewNormal.x = dot(i.TtoV0, normal);
				viewNormal.y = dot(i.TtoV1, normal);
				viewNormal.z = dot(i.TtoV2, normal);

				half3 viewPos = UnityWorldToViewPos(i.worldPos);
				half3 r = normalize(reflect(viewPos, viewNormal));
				half m = 2 * sqrt(r.x * r.x + r.y * r.y + (r.z + 1) * (r.z + 1));
				half2 matcapUV = r.xy/m + 0.5 ;
				//half2 matcapUV = viewNormal.xy * 0.5 + 0.5 ;
				
				half4 texColor = tex2D(_MatcapTex, matcapUV );
				half4 albedo = tex2D(_MainTex, i.uv) * _Color;
				texColor = texColor * albedo;

				half edgeFactor = abs(dot(i.viewDir, i.normal));
				edgeFactor = smoothstep(_RimSmoothMin, _RimSmoothMax,edgeFactor);

				half oneMinusEdge = 1.0 - edgeFactor;
				half parallaxDepth = oneMinusEdge * 0.5 ;

				half3 rgb = (albedo.rgb * edgeFactor * _Brightness) + (_EdgeColor * oneMinusEdge);
				rgb = (rgb + texColor.rgb);

				float2 baseUV = i.uv;
				float2 parallax = ParallaxOffset(parallaxDepth, _Parallax, i.tangentViewDir);
				fixed4 startex01 = tex2D(_starmap, (baseUV * _starmap_ST.xy + frac(_Time.y  * _starmap_ST.zw)) + parallax) * _starcol;

				rgb += startex01.rgb;

				half4 output = float4(rgb, 1);

				return output;
			}

			ENDCG
		}

		Pass 
		{
			Tags{ "LightMode" = "ShadowCaster" }

			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			#pragma multi_compile_shadowcaster
			#pragma fragmentoption ARB_precision_hint_fastest
			#include "UnityCG.cginc"
			
			struct v2f
			{
				V2F_SHADOW_CASTER;

			};

			v2f vert(appdata_base v) 
			{
				v2f o;
				TRANSFER_SHADOW_CASTER_NORMALOFFSET(o)
				return o;
			}

			float4 frag(v2f i) :SV_Target
			{
				SHADOW_CASTER_FRAGMENT(i)
			}

			ENDCG
		}


	}

}