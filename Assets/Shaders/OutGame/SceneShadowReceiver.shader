Shader "OutGame/Scene/ShadowReceiver"
{
    Properties
    {
        _Color("Color",Color) = (1.0,1.0,1.0,1.0)
        _MainTex("Texture", 2D) = "white" {}
        [NoScaleOffset]	_LightMap("lightmap", 2D) = "black" {}
    
        [Enum(UnityEngine.Rendering.CullMode)] _Cull("Cull Mode", Float) = 2
        //blending state 
        [HideInInspector] _Mode("Blend Mode", float) = 0
        [HideInInspector] _SrcBlend("Source Blend", float) = 1.0
        [HideInInspector] _DstBlend("Destination Blend", float) = 0.0
        [HideInInspector] _ZWrite("Z Write", float) = 1.0
        _Cutoff("Alpha Cutoff", Range(0.0, 0.95)) = 0.8
         // 【ID863885247】局外升星，场景变黑			--ho<PERSON><PERSON><PERSON>
        [HideInInspector]_dark("Dark", Range(0.0, 1.0)) = 0
        //Plane Reflection --fengxzeng
        [Toggle(_PLANE_REFLECTION)] _PlaneReflectionEnabled("Enable Plane Reflection", float) = 0.0
        _PlaneReflectionIntensity(" Plane Reflection Intensity", range(0, 10)) = 0.5
        [HideInInspector] _RenderQueueOffset("_Render Queue Offset", int) = 0
        // UniqueShadow开关
        [Toggle]_UniqueShadow("软阴影 UniqueShadow", float) = 0
        // 软阴影默认ShadowMap定义为纯白贴图, 防止影响编辑器效果
        [HideInInspector] u_UniqueShadowTexture("UniqueShadowTexture", 2D) = "white" {}
        _ShadowColor("Shadow Color(RGB:Color A:ShadowAlpha)",Color)=(0,0,0,1)
        _ShadowAddColor("Shadow Add Color)",Color)=(0,0,0,1) 
       // _ShadowFalloff("衰减 程度",Range(0 ,1)) = 0.1
    }
    SubShader
    {
        Tags 
		{
			"RenderType"="Opaque"
			"Queue"="Geometry"
		}
        LOD 100

        Pass
        {
            Name "FORWARD"
            Tags
            {
                "LightMode" = "ForwardBase"
            }
			// 遮挡描边的场景, 参考值5
			Stencil
			{
				Ref 5
				Comp Always
				Pass Replace
			}

             Blend SrcAlpha OneMinusSrcAlpha
            //Zwrite[_ZWrite]
           // Cull[_Cull]
            

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
			#pragma target 3.0
			#pragma multi_compile_fwdbase

            #pragma shader_feature LIGHTMAP_ON
            #pragma shader_feature LIGHTMAP_SHADOW_MIXING
            #pragma shader_feature SHADOWS_SHADOWMASK
            #pragma shader_feature QUALITY_ULTRA QUALITY_HIGH QUALITY_MEDIUM QUALITY_LOW 
            #define USE_UNIQUE_SHADOW 1

            #pragma skip_variants POINT SPOT FOG_EXP FOG_EXP2 VERTEXLIGHT_ON SHADOWS_CUBE

            #include "UnityStandardCore.cginc"
            #include "./Common/OutGame.cginc"
            #include "./UniqueShadow/UniqueShadow_ShadowSample.cginc"
             
            float4 _ShadowColor,_ShadowAddColor; 
            //float _ShadowFalloff;

            struct appdata
            {
                float4 vertex : POSITION;
                float2 texcoord : TEXCOORD0;
				float3 normal : NORMAL;
            };

            struct v2f
            {
				float4 pos : SV_POSITION;
                half2 uv : TEXCOORD0;
				float2 uv1 : TEXCOORD1;
				float3 worldPos : TEXCOORD2; 
                UNITY_SHADOW_COORDS(3)
            };

			v2f vert (appdata v)
            {
				v2f o = (v2f)0;
				o.uv.xy = v.texcoord.xy;
                o.uv1 = TRANSFORM_TEX(v.texcoord, _MainTex);
				o.pos = UnityObjectToClipPos(v.vertex);
				o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
				float3 worldPos = o.worldPos; 
                UNITY_TRANSFER_SHADOW(o, o.uv.xy); 
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {  
                fixed4 col = tex2D(_MainTex, i.uv1); 
                float3 worldPos = i.worldPos;
           
                UNITY_LIGHT_ATTENUATION(atten, i, worldPos);
                float shadowPart = (1-atten)*col.r *_ShadowColor.a;
				col.rgb = _ShadowColor.rgb+_ShadowAddColor.rgb;
                col.a  *= shadowPart ;


                return col;
            }
            ENDCG
        }
        
    } 
}
