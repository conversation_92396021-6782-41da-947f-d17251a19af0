
Shader "OutGame/UI/GlassBall"
{
    Properties
    {
        [HideInInspector] [HDR] _ColorRe("反射颜色",Color) = (1,1,1,1)
        _ColorRa("折射颜色",Color) = (1,1,1,1)
        [HideInInspector]_ColorDif("diffuse颜色",Color) = (1,1,1,1)

        [Toggle(_CUSTOM_CUBE)] _CustomCube("自定义环境", float) = 0
        _Cubemap("自定义环境球", Cube) = "" {}
        _IconTex("icon", 2D) = "black" {}

        _Transmission("透射度 Transmission",Range(0,1)) = 1
        _RefractRatio("折射率 Refract Ratio", Range(0,1)) = 0.35
        _RoughnessR2("折射射粗糙度 Roughness",  Range(0,1)) = 0.18

        _Reflection("反射强度 Reflection",Range(0,1)) = 0.4
        _RoughnessR1("反射粗糙度 Roughness",  Range(0,1)) = 0.18



        [HideInInspector] _RimVector("边缘光方向RimVector", Vector) = (1,1,1,1)
        [HDR]_RimColor("边缘光颜色RimColor", Color) = (0,0,0,1)
        _RimPower("边缘光对比RimPower", Range(0, 10)) = 3
        _RimScale("边缘光强度RimScale", Range(0, 5)) = 1
            // _RefractionAmount("Reflection Amount", Range(0, 1)) = 0.5
    }

        SubShader
        {
            Pass
            {
                Tags
                {
                    "LightMode" = "ForwardBase"
                }
                Cull Back
                LOD 100
                CGPROGRAM
                #pragma vertex vert
                #pragma fragment frag
                #pragma multi_compile_fwdbase
                #pragma shader_feature _CUSTOM_CUBE

                #include "UnityCG.cginc"
                #include "Lighting.cginc"
                #include "AutoLight.cginc"

                samplerCUBE _Cubemap;
                sampler2D _IconTex;
                float4 _IconTex_ST;

                float _RoughnessR1;
                float _RoughnessR2;
                float _Reflection;
                float _RefractRatio;
                float _RefractionAmount;
                fixed4 _ColorRe;
                fixed4 _ColorRa;
                fixed4 _ColorDif;
                float _Transmission;

                half4 _RimVector;
                fixed4 _RimColor;
                half _RimPower;
                half _RimScale;


                float4 _SceneDistanceFogColor;
                float _SceneDistanceFogStart;
                float _SceneDistanceFogEnd;
                float _SceneDistanceFog;


                     struct appdata
                     {
            /*             float4 vertex : POSITION;
                         float4 color : COLOR;
                         float3 normal : NORMAl;*/

                         float4 vertex    : POSITION;  // The vertex position in model space.
                         float3 normal    : NORMAL;    // The vertex normal in model space.
                         float4 texcoord  : TEXCOORD0; // The first UV coordinate.
                         float4 texcoord1 : TEXCOORD1; // The second UV coordinate.
                         float4 tangent   : TANGENT;   // The tangent vector in Model Space (used for normal mapping).
                         float4 color     : COLOR;     // Per-vertex color
                     };

                     struct v2f
                     {
                         float4 pos : SV_POSITION;
                         fixed4 color : COLOR;
                         float3 worldNormal : TEXCOORD0;
                         float3 worldPos : TEXCOORD1;
                         float3 worldView : TEXCOORD2;
                         float3 uv : TEXCOORD3;
                         float4 iconUv : TEXCOORD4;

                         //SHADOW_COORDS(5)
                     };

                     v2f vert(appdata_full v)
                     {
                         v2f o;
                         o.pos = UnityObjectToClipPos(v.vertex);
                         o.color = v.color;
                         o.worldNormal = normalize(UnityObjectToWorldNormal(v.normal));
                         o.worldPos = mul(unity_ObjectToWorld, v.vertex);
                         //TRANSFER_SHADOW(o);

                         o.worldView = normalize(o.worldPos - _WorldSpaceCameraPos);
                         o.uv = v.texcoord;

                         float4 objPos = mul(unity_ObjectToWorld, float4(0, 0, 0, 1));

                         o.iconUv = mul(UNITY_MATRIX_MV, v.vertex) + mul(UNITY_MATRIX_MV, objPos);

                         o.iconUv.xy = TRANSFORM_TEX(o.iconUv, _IconTex);
                         o.iconUv.xy = mul(UNITY_MATRIX_MV, objPos);
                        // o.iconUv.xy = frac(o.iconUv.xy);


                         return o;
                     }


                     fixed4 frag(v2f i) : SV_TARGET
                     {
                        //  return fixed4(i.iconUv.xyz, 1);
                         float3 worldLight = UnityWorldSpaceLightDir(i.worldPos);

                         float3 normal = i.worldNormal;
                         float3 lightDir = UnityWorldSpaceLightDir(i.worldPos);
                         float3 viewDir = -normalize(i.worldPos - _WorldSpaceCameraPos);//  UnityWorldSpaceViewDir(i.worldPos);

                         float3 halfDir = Unity_SafeNormalize(float3(worldLight)+viewDir);


                         //float nl = saturate(dot(normal, lightDir));
                         //float nh = saturate(dot(normal, halfDir));
                         //half lv = saturate(dot(lightDir, viewDir));
                         //half lh = saturate(dot(lightDir, halfDir));
                         //float nv = saturate(dot(normal, viewDir));

                         float smoothnessR1 = 1 - _RoughnessR1;
                         float smoothnessR2 = 1 - _RoughnessR2;
                         float perceptualRoughnessR1 = SmoothnessToPerceptualRoughness(smoothnessR1);
                         float perceptualRoughnessR2 = SmoothnessToPerceptualRoughness(smoothnessR2);
                         half surfaceReduction;

                     #   ifdef UNITY_COLORSPACE_GAMMA
                                         surfaceReduction = 1.0 - 0.28 * _RoughnessR1 * perceptualRoughnessR1;
                                         // 1-0.28*x^3 as approximation for (1/(x^4+1))^(1/2.2) on the domain [0;1]
                     #   else
                                         surfaceReduction = 1.0 / (_RoughnessR1 * _RoughnessR1 + 1.0);
     #endif
                         float oneMinusReflectivity = OneMinusReflectivityFromMetallic(0);
                          half grazingTerm = saturate(1 - _RoughnessR1 + (1 - 0.04));


                         float3 worldView = UnityWorldSpaceViewDir(i.worldPos);
                         float3 refDir = refract(-normalize(worldView), normalize(normal), _RefractRatio);


                         float mip_roughnessR1 = perceptualRoughnessR1 * (1.7 - 0.7 * perceptualRoughnessR1);
                         float mip_roughnessR2 = perceptualRoughnessR2 * (1.7 - 0.7 * perceptualRoughnessR2);

                         half mipR1 = mip_roughnessR1 * UNITY_SPECCUBE_LOD_STEPS;
                         half mipR2 = mip_roughnessR2 * UNITY_SPECCUBE_LOD_STEPS;


     #if _CUSTOM_CUBE
                         fixed3 refCol = texCUBElod(_Cubemap, float4(refDir, mipR2)) * _ColorRa;
                         fixed3 speCol = texCUBElod(_Cubemap, float4(i.worldNormal, mipR1)) * _ColorRe;
     #else
                         fixed3 refCol = UNITY_SAMPLE_TEXCUBE_LOD(unity_SpecCube0, refDir, mipR2) * _ColorRa;
                         fixed3 speCol = UNITY_SAMPLE_TEXCUBE_LOD(unity_SpecCube0, i.worldNormal, mipR1) * _ColorRe;
     #endif

                         fixed4 icon = tex2D(_IconTex, i.uv.xy);
                         // UNITY_LIGHT_ATTENUATION(atten, i, i.worldPos);

                         //fixed3 col = ambient + lerp(diffuse, refCol, _RefractionAmount) * atten;
                         //fixed3 col =  lerp(diffuse, refCol, _RefractionAmount);// *atten;

                         float nv = abs(dot(normal, viewDir));
                         fixed3 col = FresnelLerp(_Reflection, grazingTerm, nv) * speCol.rgb + lerp(_ColorDif * UNITY_LIGHTMODEL_AMBIENT.rgb, refCol,_Transmission);
                         // return fixed4(FresnelLerp(_Reflection, grazingTerm, nv), 1);

                          //return FresnelLerp(_Reflection, grazingTerm, nv).r;


                          float dis = distance(i.worldPos.xyz , _WorldSpaceCameraPos.xyz);
                          float height = abs(_SceneDistanceFogEnd - _SceneDistanceFogStart);
                          float fog = (1 - saturate(min(_SceneDistanceFogEnd - max(_SceneDistanceFogStart, dis), height) / max(0.00001, height))) * _SceneDistanceFog;


                          // col = lerp(col, icon.rgb, icon.a);
                          // col = viewDir;(half3 nv, half3 rimLightDir, half nl, half3 normal)

                          col += pow(1 - saturate(dot(normal, viewDir)), _RimPower) * _RimScale * _RimColor.rgb;
                          col += icon.a;
                          col.rgb = lerp(col.rgb, _SceneDistanceFogColor, fog);
                          return fixed4(col, 1);

                          }

                          ENDCG
                      }
        }

            Fallback "VertexLit"
}