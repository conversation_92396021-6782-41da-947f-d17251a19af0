// Upgrade NOTE: replaced 'defined _OUTGAME_SCENE' with 'defined (_OUTGAME_SCENE)'

#ifndef CUSTOM_PBR_CORE_FORWARD_INCLUDED
#define CUSTOM_PBR_CORE_FORWARD_INCLUDED

#include "SH_Utils.cginc"
#include "CustomPBRCore.cginc"
#include "CustomFunction.cginc"

inline float UnLinear01Depth(float z)
{
	return ((1 / z) - _ZBufferParams.y) / _ZBufferParams.x;
}

v2f vert_main(appdata v)
{
	v2f o;
	o = (v2f)0;
	UNITY_SETUP_INSTANCE_ID(v);
	UNITY_TRANSFER_INSTANCE_ID(v, o);

#if defined(_OUTGAME_CHAR) && defined(_OUTGAME_SCENE) 
	o.DebugVec4 = 0;
#endif 


#ifdef _OUTGAME_CHAR
//#if _VERTEX_WIND 先去掉 【优化keyword】(fengxzeng)
	APPLY_VERTEX_WIND(v.vertex,v.normal,v.color.r);
#endif 
#if  defined(_STAR) || defined(_SPARKLE)  || defined(_MODE_MODEL)
	o.vertexNormal = v.normal;
	o.objectPosition = v.vertex
		// workaround: get rid of HiSilicon Kirin970(Mali-G72MP12) fog calculation wrong result in driver version before r18.
		// we cannot use 1.0 because unity shader compiler will automatically optimize and remove this operation.
		//     --hearstzhang 2022.5.7
		#if SHADER_API_MOBILE && !SHADER_API_METAL
				* 0.99999
		#endif
		;
#endif

	o.pos = UnityObjectToClipPos(v.vertex);
	o.uv = _TexCoords(v);

#if  defined(_SPACEMODE_SCREEN)
	o.objectPosition = o.pos.xyz;
#endif

#if _FLOWADD
	o.uv02.xy = v.uv1;

#endif
//for debug
#if defined(_DEBUG_CHANNEL_CHECK) && defined(_DEBUG) 

	//通过 DEBUG_FUNCTION 来 遍历所有通道设置
	#define DEBUG_FUNCTION(name) DEBUG_COLOR_VALUE_ID(name)  

	// 设置一下公共ID 给vertexshader 使用所以需要声明全局的
	DEBUG_VALUES
	#define DEBUG_FUNCTION(name) DEBUG_SET_COLOR_VER(name)  

	//给遍历设置 index 用
	int index = 0;
	DEBUG_VALUES

	if (_DebugCheckIndex == DEBUG_INDEX(vertexNormal))
	{
		//显示归一化的数据
		o.DebugVec4.xyz = normalize(v.normal.xyz);
	}
	if (_DebugCheckIndex == DEBUG_INDEX(objectPosition))
	{
		o.DebugVec4 = v.vertex;
	}
	if (_DebugCheckIndex == DEBUG_INDEX(uv1))
	{
		o.DebugVec4 = v.uv;
	}
		if (_DebugCheckIndex == DEBUG_INDEX(uv2))
	{
		o.DebugVec4 = v.uv1;
	}
	if (_DebugCheckIndex == DEBUG_INDEX(vertexTangent))
	{
		//显示归一化的数据
		o.DebugVec4.xyz = normalize(v.tangent.xyz);
	}
#endif
	o.worldNormal = UnityObjectToWorldNormal(v.normal);

	float4 tangentWorld = float4(UnityObjectToWorldDir(v.tangent.xyz), v.tangent.w);
	float sign = tangentWorld.w * unity_WorldTransformParams.w;
    float3 binormal = cross(o.worldNormal, tangentWorld.xyz) * sign;
	o.worldTangent = tangentWorld.xyz;
	o.worldBinormal = binormal;
	
#ifdef LIGHTMAP_ON
	o.ambientOrLightmapUV.xy = v.uv1.xy * unity_LightmapST.xy + unity_LightmapST.zw;
	o.ambientOrLightmapUV.zw = 0;
#else
	//o.ambientOrLightmapUV.rgb = ShadeSHPerVertex(o.worldNormal, o.ambientOrLightmapUV.rgb);
	OUTPUT_VERTEX_GI(o, o.worldNormal, o.ambientOrLightmapUV.rgb)
#endif	
#ifdef DYNAMICLIGHTMAP_ON
	o.ambientOrLightmapUV.zw = v.uv2.xy * unity_DynamicLightmapST.xy + unity_DynamicLightmapST.zw;
#endif

#if _RIM
	// fengxzeng 美术需要类似matcap的边缘光 转换为屏幕空间方向
	o.rimLightDir = mul(normalize(_RimVector.xyz),UNITY_MATRIX_V) ;
#endif

#if _RIM_PRO 
	o.screenPos = ComputeScreenPos(o.pos);
	//轮廓光两个屏幕空间方向
	o.rimProDir1 = mul(normalize(_RimDir1.xyz), UNITY_MATRIX_V);
	o.rimProDir2 = mul(normalize(_RimDir2.xyz), UNITY_MATRIX_V);
	
	//转到tangentspace 方向
	float3x3 world2tangent = float3x3(o.worldTangent, o.worldBinormal, o.worldNormal);
	float3 rimProDir1 = normalize(mul(world2tangent, o.rimProDir1));
	float3 rimProDir2 = normalize(mul(world2tangent, o.rimProDir2));


	//计算屏幕空间遮罩需要mask
	if (_RimNoSH)
	{
		//有烘焙顶点色
		float4 sh01 = float4(Y0(rimProDir1), Y1(rimProDir1), Y2(rimProDir1), Y3(rimProDir1));
		o.rimProDir1.w = dot(sh01, v.color);

		float4 sh02 = float4(Y0(rimProDir2), Y1(rimProDir2), Y2(rimProDir2), Y3(rimProDir2));
		o.rimProDir2.w = dot(sh02, v.color);
	}
	else {
		//特殊没有烘焙顶点色的情况
		o.rimProDir1.w = 1;
		o.rimProDir2.w = 1;
	
	}
	


#endif


	float3 worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
	o.worldPos = worldPos;
	o.worldView = o.worldPos - _WorldSpaceCameraPos;

	UNITY_TRANSFER_SHADOW(o, v.uv1.xy);//no use for v.uv1
#if _POINT_LIGHT
	o.pointlightDir.xyz = normalize(_PointLightPos.xyz - o.worldPos.xyz);
	o.pointlightDir2.xyz = normalize(_PointLightPos2.xyz - o.worldPos.xyz);
#endif

//Toon Water  --horacezhao
#if _WATER
	o.distortUV = TRANSFORM_TEX(v.uv, _MainTex);
	o.noiseUV = TRANSFORM_TEX(v.uv, _SurfaceNoise);
	o.viewNormal = COMPUTE_VIEW_NORMAL;
#endif

//屏幕空间UV
#if defined(_PLANE_REFLECTION) || defined(_WATER)
	o.screenPos = ComputeScreenPos(o.pos);
#endif

#if defined(_STAR)  || defined(_MODE_MODEL)
	o.screenPos = ComputeScreenPos(o.pos);
	half dis = distance(v.vertex.xyz * _ModelPosScale.xyz, _ModelPosOffset.xyz);
	dis /= _ModelPosScale.w;
	o.screenPos.z = dis;
#endif

	o.color = v.color;
	// Opal,Eye --horacezhao

#if defined(_OPAL) || defined(_EYE) || defined(_STAR)  || defined(_MODE_MODEL)
	half3x3 objectToTangent = half3x3(
		v.tangent.xyz,
		cross(v.normal, v.tangent.xyz) * v.tangent.w,
		v.normal
		);
	o.tangentViewDir.xyz = mul(objectToTangent, ObjSpaceViewDir(v.vertex)).xyz;
 #endif

//【角色】Eye 眼睛  (horacezhao)
// 【ID864232499】保留原眼睛shader Mt_Pet_Eyes的贴图高光，可与cubemap方式切换使用
#if _EYE
	float3 viewNormal = COMPUTE_VIEW_NORMAL;
	o.eyeUV = TRANSFORM_TEX(v.uv.xy, _EyesTex);
	half2 offset = half2(-viewNormal.x, viewNormal.y);				
	offset.x = clamp(offset.x, _EyesArea.x, _EyesArea.y);
	offset.y = clamp(offset.y, _EyesArea.z, _EyesArea.w);
	o.eyeUV += offset * _Strength;
#endif



	return o;
}

v2fadd vert_add(appdata v)
{
	v2fadd o;
	o = (v2fadd)0;

	//UNITY_SETUP_INSTANCE_ID(v);
	//UNITY_TRANSFER_INSTANCE_ID(v, o);


#ifdef _OUTGAME_CHAR
//#if _VERTEX_WIND 先去掉 【优化keyword】(fengxzeng)
	APPLY_VERTEX_WIND(v.vertex, v.normal, v.color.r);
#endif 
	o.pos = UnityObjectToClipPos(v.vertex);

	o.uv = _TexCoords(v);
	o.worldNormal = UnityObjectToWorldNormal(v.normal);

	float4 tangentWorld = float4(UnityObjectToWorldDir(v.tangent.xyz), v.tangent.w);
	float sign = tangentWorld.w * unity_WorldTransformParams.w;
    float3 binormal = cross(o.worldNormal, tangentWorld.xyz) * sign;
	o.worldTangent = tangentWorld.xyz;
	o.worldBinormal = binormal;
	
	float3 worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
	o.worldPos = worldPos;
	o.worldlightDir = normalize(_WorldSpaceLightPos0.xyz - worldPos.xyz * _WorldSpaceLightPos0.w);
	o.uv1 = v.uv1;

	UNITY_TRANSFER_SHADOW(o, o.uv1.xy);
	return o;
}


half4 frag_main(v2f i) : SV_Target
{
	UNITY_SETUP_INSTANCE_ID(i);

	half4 final_col;
#if defined(_DEBUG_CHANNEL_CHECK) && defined(_DEBUG) 
	DebugData da;


	da = DebugDataSetup();

	FragmentData s = FragmentSetup(i,da);
	da.uv1.xy = i.uv.xy;
	
	//for debug
	if (_DebugCheckIndex == DEBUG_INDEX(uv2))
	{
		if (_DebugUv)
		{
			float nv = abs(dot(s.worldNormal, -s.worldView))*0.7+0.3;
			da.uv2.xyz = UVcheckMap(i.DebugVec4.xy)*nv;
		}
		else {
			da.uv2 = i.DebugVec4;
		}

		//da.uv2=i.DebugVec4;
	}

#else
	FragmentData s = FragmentSetup(i);

#endif


	UnityLight mainLight = MainLight();


	float3 worldPos = i.worldPos;
	UNITY_LIGHT_ATTENUATION(atten, i, worldPos);

	UnityGI gi = Fragment_GI(i, s.occlusion, i.ambientOrLightmapUV, s.smoothness, s.worldNormal, s.worldView, s.specColor, atten, mainLight);

#if defined(_DEBUG_CHANNEL_CHECK) && defined(_DEBUG) 
	//half3 normalTangent = tex2D(_Bump, i.uv);
	half3 normalTangent = UnpackScaleNormal(tex2Dbias(_Bump, float4(i.uv.x, i.uv.y, 0, _NormalBias)), _BumpScale);
	#if defined (_DETAIL_MULX2)
		half3 detailNormalTangent = UnpackScaleNormal(tex2D(_DetailBump, i.uv.zw), _DetailBumpScale);// _DetailBumpScale;
		normalTangent = BlendNormals(normalTangent, detailNormalTangent);
	#endif
	normalTangent = (normalTangent + 1) / 2;


	//for debug

	if (_DebugCheckIndex == DEBUG_INDEX(vertexNormal))
	{
		da.vertexNormal = i.DebugVec4;
		//return float4(da.vertexNormal.rgb, 0);
	}
	if (_DebugCheckIndex == DEBUG_INDEX(objectPosition))
	{
		da.objectPosition = i.DebugVec4;
		//return float4(da.objectPosition.rgb, 0);
	}
	if (_DebugCheckIndex == DEBUG_INDEX(uv1))
	{
		if (_DebugUv)
		{
			float nv = abs(dot(s.worldNormal, -s.worldView))*0.7+0.3;
			da.uv1.xyz = UVcheckMap(i.DebugVec4.xy)*nv;
		}
		else {
			da.uv1 = i.DebugVec4;
		}
		

		//return float4(da.uv1.rgb, 0);
	}
	if (_DebugCheckIndex == DEBUG_INDEX(vertexTangent))
	{
		da.vertexTangent = i.DebugVec4;
		//return float4(da.vertexTangent.rgb, 0);
	}
	//if (_DebugCheckIndex == 19)
	//{
	//	da.vertexTangent = i.DebugVec4;
	//	return DEBUG_INDEX(vertexTangent);
	//}

	da.worldNormal.rgb = s.worldNormal;
	da.vertexColor = i.color;
	da.smoothness = s.smoothness;
	da.Roughness = 1-s.smoothness;
	da.alpha = s.alpha;
# ifdef UNITY_COLORSPACE_GAMMA
#ifdef _TO_LINEAR
	da.emission.rgb = LinearToGammaSpace(s.emission);
#else
	da.emission.rgb = s.emission;
#endif
#else
	da.emission.rgb = s.emission;
#endif
	da.occlusion.rgb = s.occlusion;
#if _SKIN_ENABLED
	da.translucency.rg = s.translucency;
	da.curvature.rgb = s.curvature;
#endif
	da.metallic.rgb = s.metallic;


# ifdef UNITY_COLORSPACE_GAMMA
#ifdef _TO_LINEAR
	da.diffColor.rgb = LinearToGammaSpace(s.diffColor);
	da.specColor.rgb = LinearToGammaSpace(s.specColor);
#else
	da.diffColor.rgb = s.diffColor;
	da.specColor.rgb = s.specColor;
#endif
#else

	da.diffColor.rgb = s.diffColor;
	da.specColor.rgb = s.specColor;
#endif
	//da.normal = s.worldNormal;
	da.normalMap.rgb = normalTangent;
	
	da.atten = atten;
	//da.IndirectSpecular.rgb = gi.indirect.specular;
	//da.IndirectDiffuse.rgb = gi.indirect.diffuse;
#endif


	half nl, nv;
	nl = saturate(dot(s.worldNormal, gi.light.dir));
	nv = abs(dot(s.worldNormal, -s.worldView));

#if defined(_CHAR_LIGHT_MAP) && defined(_DEBUG) 
//【Debug】【角色】 lightmnap 测试部分  (fengxzeng)
	float4 lighmap = tex2D(_LightMap, i.uv.xy);
	//lighmap.rgb= DecodeHDR(lighmap, unity_SpecCube0_HDR);
	//LinearToGammaSpace
	lighmap.rgb = pow(lighmap.rgb, 2.2);
	gi.indirect.diffuse += lighmap * _LightmapColor;
#endif




	//【场景】镜面反射支持   (fengxzeng)
#ifdef _PLANE_REFLECTION
	//float4 planarRefColor = tex2Dlod(_ReflectionTex, float4(s.screenUv.xy / s.screenUv.w, 0, 0));
	float4 planarRefColor = tex2D(_ReflectionTex, s.screenUv.xy / s.screenUv.w);// , 0, 0));
	//高度衰减
	float  fac = 1 - saturate(abs(worldPos.y - _PlaneReflectionPar.x) / max(_PlaneReflectionPar.y, 0.0001));
	//粗糙度衰减 [0.1-1.0]
	fac *= saturate(1-1.1f* (1 - s.smoothness));
	//方向衰减
	fac *= saturate(s.worldNormal.y * s.worldNormal.y);
	//强度控制衰减 <1
	fac *= saturate(_PlaneReflectionIntensity);

	gi.indirect.specular = lerp(gi.indirect.specular, planarRefColor * _PlaneReflectionIntensity, planarRefColor.a*fac);

#endif

	//【公共】Specluar AO 高光遮蔽 (fengxzeng)
	half perceptualRoughness = SmoothnessToPerceptualRoughness(s.smoothness);
	half roughness = PerceptualRoughnessToRoughness(perceptualRoughness);
	gi.indirect.specular *= computeSpecularAO(nv, s.occlusion, roughness);


#if !SHADER_API_MOBILE // 暂时只限pc 测试
#if defined(_DEBUG_CHANNEL_CHECK) && defined(_DEBUG) 
	s.smoothness = GeometricSpecluarAA(s.worldNormal, s.smoothness,da);


#else
	s.smoothness = GeometricSpecluarAA(s.worldNormal, s.smoothness);
#endif
#endif
	
//【角色】Opal 油膜效果  (horacezhao)
#ifdef _OPAL	
	{
		half3 R = normalize(reflect(-s.worldView, s.worldNormal));
		half NoL = dot(s.worldNormal, gi.light.dir);
		half VoR = abs(dot(s.worldView, R));
		half SignFace = sign(dot(s.worldNormal, s.worldView));		
		half Temp = NoL * VoR;
		half3 TV = normalize(i.tangentViewDir.xyz);
		half3 Opal = Opal_Shading(TV,i.uv.xy, Temp, SignFace, s.diffColor, s.diffColor);
		s.emission.rgb += Opal;
    }
#endif

//【角色】Flow  流光效果(lvanchen)
#ifdef _FLOWADD
	half mask = tex2D(_MetallicMap,i.uv).a;//mask
	half4 pattern = tex2D(_FlowPattern, i.uv02 * _FlowPattern_ST.xy + _FlowPattern_ST.zw);
	half time = frac(_Time.y * _FlowMaskSpeed);
	half4 noise = tex2D(_FlowMaskTex, float2(i.uv02.x + time, i.uv02.y));
	pattern.r *= (noise.x * pattern.z);
    pattern.g *= (noise.y * pattern.w);
	half4 color01 = lerp(_Pattern01Color, 0, pattern.g) * noise.y * mask ;
	half4 color02 = lerp(0, _Pattern02Color, pattern.r) * mask ;

	s.emission.rgb += color01.rgb + color02.rgb ;
    
#endif

//【场景】Toon Water 卡通水  (horacezhao)
#if _WATER		
	half waterDepthDifference01 = 1.0;		//局内local postion gradient to do 
	half surfaceNoiseCutoff = _SurfaceNoiseCutoff;
	//alpha mask
	half water_alpha = tex2D(_SurfaceNoise, i.uv.xy).g;

// 【ID863827793】局内适配；深度开关	--horacezhao
#ifndef _LOW
	// 【ID863575703】为了兼容以后可能会使用SSAO
	half existingDepthLinear = LinearEyeDepth(tex2Dproj(_CameraDepthTexture, UNITY_PROJ_COORD(s.screenUv)).x);
	#if _TK_INCLUDE_NORMAL
		existingDepthLinear = LinearEyeDepth(UnLinear01Depth(DecodeFloatRG(tex2Dproj(_CameraDepthNormalsTexture, UNITY_PROJ_COORD(s.screenUv)).zw)));	
	#endif
	// float existingDepth01 = tex2Dproj(_CameraDepthTexture, UNITY_PROJ_COORD(s.screenUv)).r;
	// float existingDepthLinear = LinearEyeDepth(existingDepth01);
	half depthDifference = existingDepthLinear - s.screenUv.w;
	waterDepthDifference01 = saturate(depthDifference / _DepthMaxDistance);
	
	// half3 existingNormal = tex2Dproj(_CameraNormalsTexture, UNITY_PROJ_COORD(s.screenUv));	// 和直接使用模型normal对比差别不大
	half3 normalDot = saturate(dot(i.worldNormal, i.viewNormal));		//用顶点法线算边缘颜色不会被bump影响
	half foamDistance = lerp(_FoamMaxDistance, _FoamMinDistance, normalDot);
	half foamDepthDifference01 = saturate(depthDifference / foamDistance);
	surfaceNoiseCutoff *= foamDepthDifference01;
#else
	//没有深度时用alpha mask来算边缘
	half alpha_edge = pow(saturate(water_alpha - _FoamMinDistance / water_alpha),_FoamMaxDistance);
	half alpha_depth = saturate(water_alpha - _DepthMaxDistance / water_alpha);
	surfaceNoiseCutoff *= alpha_edge;
	waterDepthDifference01 = alpha_depth;
#endif
	half4 waterColor = lerp(_DepthGradientShallow, _DepthGradientDeep, waterDepthDifference01);
	float2 distortSample = (tex2D(_MainTex, i.distortUV).xy * 2 - 1) * _SurfaceDistortionAmount;	
	float2 noise_t = frac(_Time.y * _SurfaceNoiseScroll.xy);
	float2 noiseUV = float2((i.noiseUV.x + noise_t.x) + distortSample.x, 
	(i.noiseUV.y + noise_t.y) + distortSample.y);
	half surfaceNoiseSample = tex2D(_SurfaceNoise, noiseUV).r;
	
	

	half surfaceNoise = smoothstep(surfaceNoiseCutoff - 0.01, surfaceNoiseCutoff + 0.01, surfaceNoiseSample);
	
	half4 surfaceNoiseColor = _FoamColor;
	surfaceNoiseColor.a *= surfaceNoise;
	
	s.emission = (surfaceNoiseColor.rgb * surfaceNoiseColor.a) + (waterColor.rgb * (1 - surfaceNoiseColor.a));
	s.alpha = surfaceNoiseColor.a + waterColor.a * (1 - surfaceNoiseColor.a);
	s.alpha *= water_alpha * i.color.a;			//为特效使用
	s.smoothness = _Glossiness;
	s.diffColor = half3(0.0,0.0,0.0);
#endif

//【角色】Eye 眼睛  (horacezhao)
#if _EYE
	// float3 tangentLightDir = WorldSpaceToTangentDir(i.worldTangent,i.worldBinormal,i.worldNormal, gi.light.dir);
	
	half4 scleratex = tex2D(_MainTex, CenterZoomUVBySize(i.uv.xy,_ScleraSize));
	half3 scleara_tint = _ScleraColor.rgb;
# ifdef UNITY_COLORSPACE_GAMMA
#ifdef _TO_LINEAR
	scleratex.rgb = GammaToLinearSpace(scleratex.rgb);
	scleara_tint = GammaToLinearSpace(_ScleraColor.rgb);
#else
	scleratex.rgb = scleratex.rgb;
	scleara_tint = _ScleraColor.rgb;
#endif
#endif
	scleratex.rgb = lerp(scleratex.rgb, scleratex.rgb * scleara_tint, _ScleraColor.a);
	
	half4 packNormal = tex2D(_Bump,CenterZoomUVBySize(i.uv.xy,_IrisSize));
	half irismasktex = packNormal.w;		//角膜mask，使凹凸只影响角膜部分
	half3 cBump = UnpackNormal(packNormal);
	half3 tangentNormalDir = half3(0,0,1);
	cBump.xyz = lerp(tangentNormalDir,cBump, _BumpScale);
	half3 tangentNormal = lerp(tangentNormalDir,cBump,irismasktex);
	s.worldNormal = normalize(i.worldBinormal * tangentNormal.y + i.worldTangent * tangentNormal.x + i.worldNormal * tangentNormal.z);
	
	// float4 normalInTangent_EYE  = NormalInTangentSpace_EYE(i.uv);
	// 获取Mask贴图
	half2 MaskTex = tex2D(_MaskTex,i.uv.xy).xz;
	half AO = lerp(1.0, MaskTex.x, _AOIntensity);
	half uvMask = 1.0 - MaskTex.y;	

	// 计算虹膜纹理贴图
	half iSize = _IrisSize * 0.6;
	float2 irUVp = CenterZoomUVBySize(i.uv.xy,iSize);
	// float pupilSize = lerp(0.5 - 0.06 * iSize , 1.2 - 0.09 * iSize , _PupilSize);
	float pupilSize = lerp(lerp(0.5,0.2,iSize * 0.2),lerp(1.2,0.75,iSize * 0.2),_PupilSize);
	float2 irUVc = irUVp*(-1.0+(uvMask*pupilSize)) - 0.5 * uvMask * pupilSize;

	// 计算视差纹理坐标
	half plxtex = tex2D(_MaskTex,irUVp).g;		//虹膜mask，区分视差折射区域
	
	float realParallax = lerp(0.0, _Parallax * 2.0 ,plxtex);

	// 计算 虹膜和瞳孔 Mask贴图
	float2 irUV = lerp( i.uv.xy*0.75+0.125 , i.uv.xy*pupilSize-(pupilSize-1.0)*0.5 , i.uv.xy);
	
	half3 vDir = -i.tangentViewDir.xyz;
	vDir.xz = clamp(vDir.xz,-0.75,0.75);
	float height = tex2D(_MaskTex,irUV).b; 		//瞳孔mask,颜色越深视差越凹，且决定瞳孔size最大值
	float2 offset = ParallaxOffset(height, realParallax,vDir) * plxtex;
	offset = clamp(offset,-0.1,0.1);
	
	// 获取虹膜和瞳孔贴图
	half4 irisColTex = tex2D(_IrisColorTex,irUVc-offset);
# ifdef UNITY_COLORSPACE_GAMMA
#ifdef _TO_LINEAR

	irisColTex.rgb = GammaToLinearSpace(irisColTex.rgb);
	_IrisColor.rgb = GammaToLinearSpace(_IrisColor.rgb);
	_PupilColor.rgb = GammaToLinearSpace(_PupilColor.rgb);
#endif	
#endif	
	// 混合虹膜和巩膜
	half3 IrisCol = lerp(irisColTex.rgb, irisColTex.rgb*_IrisColor.rgb, _IrisColor.a);		//虹膜颜色
	half3 PuilCol = lerp(irisColTex.rgb, irisColTex.rgb*_PupilColor.rgb, _PupilColor.a);	//瞳孔颜色
	irisColTex.rgb = lerp(IrisCol, PuilCol, irisColTex.a);		//a通道保持与rgb纹理中的瞳孔大小一致即可
	s.emission = lerp(scleratex.rgb, irisColTex.rgb, irismasktex);
	
	// 焦散效果(让步给瞳孔虹膜mask自定义颜色，为了方便美术复用眼球贴图)
	// s.emission.rgb += s.diffColor * (2.0*_Emissioncolor.a)*_Emissioncolor.rgb * irismasktex * irisColTex.a;
	
	// 仅用来计算高光间接反射	--horacezhao
	s.smoothness = lerp(_ScleraSmoothness, _CorneaSmoothness, saturate(lerp(-2,5,irismasktex)) );
	s.emission = lerp(s.emission, s.emission * 1.5, irismasktex * 2);	
	
	//  美术不需要眼睛有亮暗面区分，但需整体亮度与场景光挂钩	--horacezhao
	// 不乘diffuseTerm因为会右NoL的亮暗面；后续通过打光避免投影在眼睛上的明显分割
	// half3 diffuseTerm;
	// diffuseTerm = DisneyDiffuse(abs(dot(s.worldNormal, -s.worldView)), saturate(dot(s.worldNormal, gi.light.dir)), saturate(dot(gi.light.dir, normalize(gi.light.dir -s.worldView))), 1)* saturate(dot(s.worldNormal, gi.light.dir));	
	half3 lightColor = gi.light.color;	
#ifdef UNITY_COLORSPACE_GAMMA
#ifdef _TO_LINEAR

	lightColor = GammaToLinearSpace(lightColor);
#endif
#endif
	s.emission.rgb *= AO;
	s.emission.rgb *= gi.indirect.diffuse * AO + lightColor;	//间接漫反射AO		--horacezhao
//眼睛独立间接高光反射		--horacezhao
// 【ID864232499】保留原眼睛shader Mt_Pet_Eyes的贴图高光，可与cubemap方式切换使用		--horacezhao
	half3 indir_spec;
#ifdef _CUBE
	float3 refDir = reflect(s.worldView, s.worldNormal);
	refDir = RotateAroundYInDegrees(refDir, _Rotation);
	float3 ref = texCUBElod(_Cubemap, float4(refDir, _ReflLOD)).rgb;
	indir_spec = pow(ref * _ReflInten,_ReflPow);
	half roughness_eye = PerceptualRoughnessToRoughness(SmoothnessToPerceptualRoughness(s.smoothness));		//仅在cubemap时同步pbr的高光间接反射AO		--horacezhao
	indir_spec *= computeSpecularAO(nv, AO, roughness_eye); 
#else
	half4 eyesForm = tex2D(_EyesTex, i.eyeUV.xy);
	indir_spec = eyesForm.rgb * _EyesColor.rgb;
#endif
	s.emission.rgb += indir_spec;
#endif

//【角色】Stars Skin 星辰效果  (horacezhao)
#if defined(_STAR) 
	half3 startex = 0;
	half3 startex02 = 0;
	float2 star_offset = i.objectPosition.xy;

		#if defined(_SPACEMODE_SCREEN)
			float2 star_offset01 = star_offset * _StarSpeed01.xy + frac(_Time.y  * _StarSpeed01.zw);
			startex = tex2D(_starmap, star_offset01).rgb;
		#else
			float2 star_offset01 = star_offset * _StarSpeed01.xy + frac(_Time.y  * _StarSpeed01.zw);
			float2 star_offset02 = star_offset * _StarSpeed02.xy + frac(_Time.y  * _StarSpeed02.zw);
			float2 parallax = ParallaxOffset(s.screenUv.z, _ParallaxDepth, i.tangentViewDir);
			float2 parallax02 = ParallaxOffset(s.screenUv.z * s.screenUv.z, _ParallaxDepth02, i.tangentViewDir);
			startex = tex2D(_starmap, star_offset01 + parallax).rgb;
			startex02 = tex2D(_starmap02, star_offset02 + parallax02).rgb;
		#endif


		#ifdef UNITY_COLORSPACE_GAMMA
			#ifdef _TO_LINEAR

				startex.rgb = GammaToLinearSpace(startex.rgb);
				_starcol.rgb = GammaToLinearSpace(_starcol.rgb);
				startex02.rgb = GammaToLinearSpace(startex02.rgb);
				_starcol02.rgb = GammaToLinearSpace(_starcol02.rgb);
			#endif	

			// if(_RimAddEnable==1 && _RimLerp ==1)
			// {
			// 	half3 ShapeRim =  DepthRim(s.screenUv,s.worldNormal, i.rimProDir1, i.rimProDir2, rimMask1, rimMask2,s.worldView);
			// 	rimCol2 =ShapeRim;
			// }	
			#if defined(_DEBUG_CHANNEL_CHECK) && defined(_DEBUG) 
					da.parallaxDepth.rgb = s.screenUv.z;
			#endif

		#endif	
		startex = lerp(startex.rgb, startex.rgb * _starcol.rgb, _starcol.a);
		startex02 = lerp(startex02.rgb, startex02.rgb * _starcol02.rgb, _starcol02.a);
		startex = (startex + startex02)/2;
	
	s.emission.rgb += lerp(0.0, startex, s.rimNoise);	//rimNoise确定没用，于是拿来做特殊材质mask
#endif	

//【角色】Sparkle  闪光效果  (lvanchen)
	float2 uvSpark;
	half4 star01;
	half4 star02;
#ifdef _SPARKLE
	uvSpark = TRANSFORM_TEX(i.uv.xy, _SparkleTex);
	star01 = tex2D(_SparkleTex, uvSpark);
	star02 = tex2D(_SparkleTex, uvSpark + s.worldView);
	//half4 star02 = tex2D(_SparkleTex, uv + i.viewDir - (_Time.x/2));
	half4 spark = star01 * star02 * _SparkleColor;

	#ifdef UNITY_COLORSPACE_GAMMA
		#ifdef _TO_LINEAR
			spark.rgb = GammaToLinearSpace(spark.rgb);
		#endif
	#endif
    float sparkleMask = lerp(s.rimNoise,clamp(1-s.rimNoise,0,1),_SparkleReverse);
	s.emission.rgb += lerp(0, spark.rgb,sparkleMask);
#endif

//【角色】Sparkle  闪光效果基于视角
#ifdef _SPARKLEVIEW
	uvSpark = TRANSFORM_TEX(i.uv.xy, _SparkleViewTex01);
	float2 uvSpark02 = TRANSFORM_TEX(i.uv.xy, _SparkleViewTex02);
	
    half3 gemDir = dot(s.worldNormal,normalize(_GemVector.xyz));	
	half3 R = normalize(reflect(-gemDir.xyz,  s.worldNormal));
	half NoL = dot(s.worldNormal,normalize(UnityWorldSpaceLightDir(s.worldPos)));
 	half VoR = abs(dot(gemDir.xyz, R));

	VoR = smoothstep(_GemMin,_GemMax,VoR*NoL);
	star01 = tex2D(_SparkleViewTex01, uvSpark - s.worldView *_Gem_Enabled+ (_Time.y * _FlowSpeed.xy/5*_IfFlow));
	star02 = tex2D(_SparkleViewTex01, uvSpark + s.worldView+ (_Time.y * _FlowSpeed.xy/5*_IfFlow));

	half4 star03 = tex2D(_SparkleViewTex02, uvSpark02 - s.worldView * _SparkleSpeed *_Gem_Enabled + (_Time.y * _FlowSpeed.zw/5*_IfFlow));
	half4 star04 = tex2D(_SparkleViewTex02, uvSpark02 + s.worldView * _SparkleSpeed + (_Time.y * _FlowSpeed.zw/5*_IfFlow));

	half4 spark1 = star01 * star02 * _SparkleColor01;
	half3 spark2 = star03 * star04 * _SparkleColor02*lerp(1,VoR,_Gem_Enabled); 

	#ifdef UNITY_COLORSPACE_GAMMA
		#ifdef _TO_LINEAR
			spark1.rgb = GammaToLinearSpace(spark1.rgb);
			spark2.rgb = GammaToLinearSpace(spark2.rgb);
		#endif
	#endif

	half3 sparkview = lerp(0, (spark1.rgb*lerp(1,s.rimNoise,_UseGemMask)+spark2.rgb*s.rimNoise),pow(nv,max(0.001,_SparklePow))); 
	s.emission.rgb += sparkview;
#endif
//【角色】StarLine  星座连接效果  (lvanchen)
#ifdef _STARLINE

	half starLineColor = DrawRandomLine(i.uv.xy, _StarLine);
	half4 starLineMask = tex2D(_StarLineMask, i.uv.xy);
	s.emission.rgb += starLineColor * starLineMask.r;

#endif


//【角色】Dissolve	溶解特性
#ifdef _DISSOLVEEFFECT
				float2 uv = i.objectPosition.xy; 
				float3 noisetexture = tex2D(_DissolveNoise, i.uv.xy* _DissolveNoiseScale); 
				float4 direction = tex2D(_DirectionTex, i.uv.xy);
				direction = direction + (_DisAmount* (-1.2)) + 0.1;  
				float MovingPosOnModel =direction ;
				MovingPosOnModel *= noisetexture;

				// glowing bit that's a bit longer
				float maintexturePart = smoothstep(0, _Smoothness, MovingPosOnModel ); 
				float glowingPart = smoothstep(0, _Smoothness, MovingPosOnModel);
 	
				glowingPart = step(_DissolveCutoff, maintexturePart); 
				clip( glowingPart  -0.01);
				glowingPart *= (1 - maintexturePart);   
	 			float4 glowingColored = glowingPart * _DisColor;  
				s.emission.rgb +=(glowingColored * noisetexture)*glowingPart; 
#endif
 
	
#ifndef _LOW // 【ID863827793】局内适配；局内不走光照 -horacezhao
/*
#if _SKIN_ENABLED


	//【角色】皮肤 BSDF  (jacieli)
#if defined(_DEBUG_CHANNEL_CHECK) && defined(_DEBUG) 
	final_col = BSSRDF_PBS_ForSkin(s.diffColor, s.specColor, s.oneMinusReflectivity, s.smoothness, s.worldNormal, s.emission, -s.worldView, gi.light, gi.indirect, atten
		, s.translucency, s.blurredWorldNormal, i.worldPos, s.curvature, nl, nv, s.occlusion,da);
#else

    final_col = BSSRDF_PBS_ForSkin(s.diffColor, s.specColor, s.oneMinusReflectivity, s.smoothness, s.worldNormal, s.emission, -s.worldView, gi.light, gi.indirect, atten
		, s.translucency, s.blurredWorldNormal, i.worldPos, s.curvature, nl, nv,s.occlusion);
#endif


#else


	//【公共】BRDF  (jacieli)
#if defined(_DEBUG_CHANNEL_CHECK) && defined(_DEBUG) 
	final_col = BSSRDF_PBS(s.diffColor, s.specColor, s.oneMinusReflectivity, s.smoothness, s.worldNormal,
		s.emission, -s.worldView, gi.light, gi.indirect, atten, nl, nv,da);
#else
	final_col = BSSRDF_PBS(s.diffColor, s.specColor, s.oneMinusReflectivity, s.smoothness, s.worldNormal, 
		s.emission, -s.worldView, gi.light, gi.indirect, atten, nl, nv);
#endif



#endif
*/

// 【ID864553267】使用合并后的BRDF函数		--horacezhao
#if defined(_DEBUG_CHANNEL_CHECK) && defined(_DEBUG) 
	final_col = CUSTOM_BSSRDF_PBS(s.diffColor, s.specColor, s.oneMinusReflectivity, s.smoothness, s.worldNormal, s.emission, -s.worldView, gi.light, gi.indirect, atten
		, s.translucency, i.worldPos, s.curvature, nl, nv, da);// , s.visualFade);
#else

    final_col = CUSTOM_BSSRDF_PBS(s.diffColor, s.specColor, s.oneMinusReflectivity, s.smoothness, s.worldNormal, s.emission, -s.worldView, gi.light, gi.indirect, atten
		, s.translucency, i.worldPos, s.curvature, nl, nv);// , s.visualFade);
#endif

//【特效】特效点光 (jacieli)
#if _POINT_LIGHT
	final_col.rgb += Lighting_Point(s.diffColor, s.worldNormal, i.pointlightDir, i.worldPos, _PointLightPos, _PointLightRange,_PointLightColor.xyz, _PointLightIntensity, _PointLightSwith);
	final_col.rgb += Lighting_Point(s.diffColor, s.worldNormal, i.pointlightDir, i.worldPos, _PointLightPos2, _PointLightRange2,_PointLightColor2.xyz, _PointLightIntensity2, _PointLightSwith2);
#endif

#else
	final_col.rgb = s.emission +_GlobalBrightness;
#endif

	// 【角色】头发 (neozfzheng)
	// Add Hair

	float3 hairColor = tex2D(_HairSpecularTex, float2(i.uv.r,i.uv.g + s.worldView.y * _HairOffsetIntensity + _HairOffset )).rgb  ;
	float3 ramp = _HairSpecularColor1 * hairColor.r;
	float3 ramp2 = lerp( _HairSpecularColor2 * hairColor.g,0,hairColor.r);
	float3 hairSpecTerm = ( ramp + ramp2 );

	
	final_col.rgb += lerp(0,pow(nv * nl * hairSpecTerm,3),_HairEnabled);

//【角色】屏幕空间边缘光  (fengxzeng)
#if _RIM
	half4 rimLightDir = half4(i.rimLightDir, s.rimNoise);
	
	half3 rimCol=  Lighting_Rim(nv, rimLightDir, nl, s.worldNormal)* s.occlusion;
#if  defined(_DEBUG) 
	if (_DebugRimProShow == 0) {
		//rimCol = 0;
	}
#endif
#if defined(_DEBUG_CHANNEL_CHECK) && defined(_DEBUG) 

	da.Rim.rgb = rimCol;
#endif

	final_col.rgb += rimCol;
#endif

//【角色】RIM_PRO 屏幕空间边缘光  (fengxzeng) --story=867026919 【TA需求】局外展示小小英雄 新轮廓光——无需测试
#if _RIM_PRO
	//half3 rimCol1 = RimPro(i.rimProDir1, s.worldNormal, _RimPower1, _RimScale1, _RimCol1.rgb);
	half3 rimCol1 = RimPro(i.rimProDir1, s.worldNormal, _RimPower1, 1, _RimCol1.rgb) ;
	half3 rimCol2 = RimPro(i.rimProDir2, s.worldNormal, _RimPower2, 1, _RimCol2.rgb);
	//rimCol2 = rimCol1 + rimCol2;
	half offsetMaskMin = _RimMaskClamp.x;
	half offsetMaskMax = _RimMaskClamp.y;
	half rimMask1 = saturate(min(max(offsetMaskMin, i.rimProDir1.w), offsetMaskMax) - offsetMaskMin);
	half rimMask2 = saturate(min(max(offsetMaskMin, i.rimProDir2.w), offsetMaskMax) - offsetMaskMin);

	rimMask1 = rimMask1 / max((offsetMaskMax - offsetMaskMin), 0.02);
	rimMask2 = rimMask2 / max((offsetMaskMax - offsetMaskMin), 0.02);
	
	half oc = lerp(1,s.occlusion, _RimMaskClamp.z);// *s.occlusion;
	rimMask1 *= oc;
	rimMask2 *= oc;



//debug 模式开关
#if  defined(_DEBUG) 
	if (_DebugRimPro==0
		)
	{
		rimCol1 *= rimMask1;
		rimCol2 *= rimMask2;
	}
	if (_DebugRimProShow==0)
	{
		//rimCol1 = 0;
		//rimCol2 = 0;
	}
#else
	rimCol1 *= rimMask1;
	rimCol2 *= rimMask2;
#endif

	rimCol2 = rimCol1 + rimCol2;

	if(_RimAddEnable==1 && _RimLerp ==1)
	{
		half3 ShapeRim =  DepthRim(s.screenUv,s.worldNormal, i.rimProDir1, i.rimProDir2, rimMask1, rimMask2,s.worldView);
		rimCol2 =ShapeRim;
	}	
#if defined(_DEBUG_CHANNEL_CHECK) && defined(_DEBUG) 
	da.RimMask.x = rimMask1;
	da.RimMask.y = rimMask2;
	da.Rim.rgb = rimCol2;
	da.RimNormal.x = rimMask1*oc;
	da.RimNormal.y = rimMask2*oc;

	da.RimMask= rimMask1;
	da.RimNormal = rimMask2;


#endif
	//特效用 rim 边缘光升星用
	APPLY_EFFECT_RIM(rimCol2, nv)
	final_col.rgb += rimCol2;
#endif


	final_col.a = s.alpha;

//【角色】Rim Transparency 边缘透明  (horacezhao)
#ifdef _OUTGAME_CHAR
	final_col.a *=  lerp(1, saturate(pow(1-nv,_RimAlphaRange)*_RimAlpha), _RimTransparency_Enabled);
#endif		

//【公共】【ID864173447】全局距离雾 添加全局距离雾 (horacezhao)
#if defined (_DISTANCE_FOG)
	APPLY_DISTANCE_FOG(_SceneDistanceFogColor, final_col, i.worldPos);
#endif	

// 【场景】【特效】【ID863885247】局外升星，场景变黑	(horacezhao)
#if defined (_OUTGAME_SCENE)
	APPLY_EFFECT_DARK_SCENE(final_col);
#endif	

//【公共】【ID864230841】局外爆炸黑白闪	(horacezhao)
	APPLY_EFFECT_BOOM(final_col, s.worldNormal);



#ifndef _WATER
	//final_col.a *= _Color.a;
#endif





	//【Debug】Tonemaping  ACES 测试部分  (fengxzeng)
#if defined (_DEBUG)
	//final_col.rgb = s.occlusion;
#if defined (_DEBUG_CHANNEL_CHECK)
	APPLY_TONEMAPING_ACES(final_col);

	if (_DebugCheckIndex!=-2)
	{
		final_col.rgb = CheckByIndex(da, _DebugCheckIndex);
	}
	
#endif	
	if (_FalseColor == 1)
	{
		half3 debugColors[16];

		//debugColors[0] = half3(0.0, 0.0, 0.0);  // black
		//debugColors[1] = half3(0.0, 0.0, 0.5);  // darkest blue
		//debugColors[2] = half3(0.0, 0.0, 1);  // darker blue
		//debugColors[3] = half3(0.0, 0.5, 0);  // dark blue
		//debugColors[4] = half3(0.0, 1, 0);  // blue
		//debugColors[5] = half3(0.5, 0, 0);  // cyan
		//debugColors[6] = half3(1, 0, 0);  // cyan
		//debugColors[7] = half3(1, 1, 1);  // cyan


		debugColors[0] = half3(0.0, 0.0, 0.0);  // black
		debugColors[1] = half3(0.0, 0.0, 0.1647);  // darkest blue
		debugColors[2] = half3(0.0, 0.0, 0.3647);  // darker blue
		debugColors[3] = half3(0.0, 0.0, 0.6647);  // dark blue
		debugColors[4] = half3(0.0, 0.0, 0.9647);  // blue
		debugColors[5] = half3(0.5, 0.5, 0.5);  // cyan
		//debugColors[5] = half3(0.0, 0.9255, 0.9255);  // cyan
		debugColors[6] = half3(0.0, 0.5647, 0.0);  // dark green
		debugColors[7] = half3(0.0, 0.5647, 0.0);  // green
		debugColors[8] = half3(0.0, 0.7843, 0.0);  // yellow
		debugColors[9] = half3(1, 1, 0);  //  yellow-orange
		debugColors[10] = half3(0.90588, 0.75294, 0.0);  // orange
		debugColors[11] = half3(1.0, 0.5647, 0.0);  // bright red 
		debugColors[12] = half3(1.0, 0.0, 0.0);  // red
		debugColors[13] = half3(1.0, 0.0, 1.0);  // magenta
		debugColors[14] = half3(0.6, 0.3333, 0.7882);  // purple
		debugColors[15] = half3(1.0, 1.0, 1.0);  // white

		final_col.rgb = pow(2, _falseColorExposure) * final_col.rgb;

		float v = log2(dot(final_col.rgb, float3(0.3, 0.59, 0.11)).x / 0.18);
		//float v = log2(dot(final_col.rgb, float3(0.3, 0.59, 0.11)).x);
		//float v = log10(dot(col, float3(0.3, 0.59, 0.11)).x );
		//v = clamp(v + 5.0, 0.0, 15.0);
		v = clamp(v, 0.0, 15.0);
		int index = int(floor(v));
		//int index = int(ceil(v));
		//half3 colv = debugColors[index];
		half3 colv = lerp(debugColors[index], debugColors[min(15, index + 1)], v - floor(v));

		final_col.rgb = colv.rgb;
	}
#endif	

#ifdef _PREPASS
	final_col.a *= _PrepassAlphaScale;
#endif
//
//#if defined(_DEBUG) 
//	half3 debugColors[16];
//
//
//
//	debugColors[0] = half3(0.0, 0.0, 0.0);  // black
//	debugColors[1] = half3(0.0, 0.0, 0.5);  // darkest blue
//	debugColors[2] = half3(0.0, 0.0, 1);  // darker blue
//	debugColors[3] = half3(0.0, 0.5, 0);  // dark blue
//	debugColors[4] = half3(0.0, 1, 0);  // blue
//	debugColors[5] = half3(0.5, 0, 0);  // cyan
//	debugColors[6] = half3(1, 0, 0);  // cyan
//	debugColors[7] = half3(1, 1, 1);  // cyan
//
//
//	//debugColors[0] = half3(0.0, 0.0, 0.0);  // black
//	//debugColors[1] = half3(0.0, 0.0, 0.1647);  // darkest blue
//	//debugColors[2] = half3(0.0, 0.0, 0.3647);  // darker blue
//	//debugColors[3] = half3(0.0, 0.0, 0.6647);  // dark blue
//	//debugColors[4] = half3(0.0, 0.0, 0.9647);  // blue
//	//debugColors[5] = half3(0.5, 0.5, 0.5);  // cyan
//	////debugColors[5] = half3(0.0, 0.9255, 0.9255);  // cyan
//	//debugColors[6] = half3(0.0, 0.5647, 0.0);  // dark green
//	//debugColors[7] = half3(0.0, 0.5647, 0.0);  // green
//	debugColors[8] = half3(0.0, 0.7843, 0.0);  // yellow
//	debugColors[9] = half3(1, 1, 0);  //  yellow-orange
//	debugColors[10] = half3(0.90588, 0.75294, 0.0);  // orange
//	debugColors[11] = half3(1.0, 0.5647, 0.0);  // bright red 
//	debugColors[12] = half3(1.0, 0.0, 0.0);  // red
//	debugColors[13] = half3(1.0, 0.0, 1.0);  // magenta
//	debugColors[14] = half3(0.6, 0.3333, 0.7882);  // purple
//	debugColors[15] = half3(1.0, 1.0, 1.0);  // white
//
//	//float v = log2(dot(final_col.rgb, float3(0.3, 0.59, 0.11)).x / 0.18);
//	float v = log2(dot(final_col.rgb, float3(0.3, 0.59, 0.11)).x );
//	//float v = log10(dot(col, float3(0.3, 0.59, 0.11)).x );
//	//v = clamp(v + 5.0, 0.0, 15.0);
//	v = clamp(v, 0.0, 15.0);
//	int index = int(floor(v));
//	//int index = int(ceil(v));
//	//half3 colv = debugColors[index];
//	half3 colv = lerp(debugColors[index], debugColors[min(15, index + 1)], v - floor(v));
//
//	final_col.rgb = colv.rgb;
//#endif

	//return s.worldNormal.xyzx * 0.5 + 0.5;
// 防止被unity 把顶点色优化掉  --fengxzeng 
	return final_col + i.color.r * 0.000000001;
	return float4(s.diffColor,1);
}





half4 frag_add(v2fadd i) : SV_Target
{
	UNITY_LIGHT_ATTENUATION(atten, i, i.worldPos);
	UnityLight light = AdditiveLight (i.worldlightDir, atten);
	half4 final_col = 0;
	half4 albedo_alpha = tex2D(_MainTex, i.uv.xy);
# ifdef UNITY_COLORSPACE_GAMMA
	albedo_alpha.rgb = (albedo_alpha.rgb) * (_Color.rgb);
#ifdef _TO_LINEAR
	albedo_alpha.rgb = GammaToLinearSpace(albedo_alpha.rgb) * GammaToLinearSpace(_Color.rgb);
#else
	albedo_alpha.rgb = albedo_alpha.rgb * _Color.rgb;

#endif

	half3 albedo = albedo_alpha.rgb;
#else
	albedo_alpha *= _Color;
	half3 albedo = albedo_alpha.rgb;
#endif
	half alpha = albedo_alpha.a;

	#if defined(_ALPHATEST_ON)
		clip(alpha - _Cutoff);
	#else
		//clip(alpha < 0.01 ? -1 : 1);//blend pass to clip the 0 alpha part
	#endif
	float3 normalInTangent = NormalInTangentSpace(i.uv);
	float3 worldNormal = normalize(i.worldBinormal * normalInTangent.y + i.worldTangent * normalInTangent.x + i.worldNormal * normalInTangent.z);
	//float3 vertexToLightSource =  _WorldSpaceLightPos0.xyz - i.worldPos.xyz;
    //half distance = length(vertexToLightSource);
 
	half3 lightColor = light.color;
#ifdef UNITY_COLORSPACE_GAMMA
#ifdef _TO_LINEAR

	lightColor = GammaToLinearSpace(lightColor);
#endif
#endif
	final_col.rgb = lightColor *  albedo * saturate(dot(i.worldlightDir, worldNormal)) * atten* _Color.a;



#   ifdef UNITY_COLORSPACE_GAMMA
#ifdef _TO_LINEAR

	final_col.rgb = LinearToGammaSpace(final_col.rgb);
#   endif
#   endif

//【公共】【ID864230841】局外爆炸黑白闪	(horacezhao)
	APPLY_EFFECT_BOOM(final_col, worldNormal);

	final_col.a = alpha * _Color.a;

//【公共】【ID864173447】全局距离雾 添加全局距离雾 (horacezhao)
#if defined (_DISTANCE_FOG)
	APPLY_DISTANCE_FOG(half3(0,0,0), final_col, i.worldPos);
#endif	

	return final_col;	
}
#endif


