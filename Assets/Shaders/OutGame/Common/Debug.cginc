// Upgrade NOTE: replaced 'defined _DEBUG_CHANNEL_CHECK' with 'defined (_DEBUG_CHANNEL_CHECK)'
// Upgrade NOTE: excluded shader from DX11, OpenGL ES 2.0 because it uses unsized arrays
#pragma exclude_renderers d3d11 gles

////////////////////////////////////
/////////局外功能汇总////////////////
////////////////////////////////////
//【Debug】测试使用
#ifndef DEBUG_INCLUDED
#define DEBUG_INCLUDED


#define _DEBUG 1
//#define _DEBUG_CHANNEL_CHECK 1


//所有debug 功能只能在PC上 禁止打包到手机
#if SHADER_API_MOBILE
#undef _DEBUG_CHANNEL_CHECK
#undef _DEBUG
#endif 


//================================
////【Debug】测试区域 /////////////
//================================
#if _DEBUG

//【Debug】【角色】 lightmnap 测试部分  (fengxzeng)
#if _CHAR_LIGHT_MAP
sampler2D _LightMap;
float4 _LightmapColor;
#endif 

//【Debug】PBR Check  (fengxzeng)

#if defined (_DEBUG_CHANNEL_CHECK)

#define  DIELECTRIC_DIFFUSE_MIN  0.196  //50.0 / 255
#define  DIELECTRIC_DIFFUSE_MAX  0.9529 //243 / 255.0
#define  ELECTRIC_DIFFUSE_MIN  0.7294 //186.0 / 255.0
#define  ELECTRIC_DIFFUSE_MAX 1 //255.0 / 255.0

#define  ELECTRIC_VIUSAL_COLOR_LOW  fixed3(0,1,0)
#define  ELECTRIC_VIUSAL_COLOR_HIGH  fixed3(1,1,0)
#define  DIELECTRIC_VIUSAL_COLOR_LOW  fixed3(0,0,1)
#define  DIELECTRIC_VIUSAL_COLOR_HIGH  fixed3(1,0,0)
#define  PBR_VIUSAL_COLOR_NORMAL  fixed3(0,0,0)


#define  METAL_VIUSAL_COLOR_ERROR  fixed3(1,0,0)
#define  METAL_VIUSAL_COLOR_NORMAL  fixed3(0,0,0)


uniform int _DebugCheckIndex;
uniform float _DebugCheckChannels[5];



#define DEBUG_DATA_COUNT 38

//Debug 通道ID名字
#define DEBUG_INDEX(name)  name##_index

//Debug 通道及其index 声明
#define DEBUG_COLOR_VALUE(name) float4 name;

//Debug index 声明  在vertex shader 中获取id
#define DEBUG_COLOR_VALUE_ID(name)  int name##_index;

//Debug 在vertex shader 中获取id
#define DEBUG_SET_COLOR_VER(name)   \
	name##_index = index;	\
	index++;	\

// 遍历所有通道设置
#define DEBUG_VALUES \
	DEBUG_FUNCTION (baseColor)\
	DEBUG_FUNCTION (alpha)\
	\
	DEBUG_FUNCTION (diffColor)\
	DEBUG_FUNCTION (specColor)\
	\
	DEBUG_FUNCTION (metallic)\
	DEBUG_FUNCTION (smoothness)\
	DEBUG_FUNCTION (Roughness)\
	DEBUG_FUNCTION (occlusion)\
	DEBUG_FUNCTION (emission)\
	\
	\
	DEBUG_FUNCTION (sssMask)\
	DEBUG_FUNCTION (translucency)\
	DEBUG_FUNCTION (curvature)\
	DEBUG_FUNCTION (parallaxDepth)\
	\
	DEBUG_FUNCTION (objectPosition)\
	DEBUG_FUNCTION (normalMap)\
	DEBUG_FUNCTION (uv1)\
	DEBUG_FUNCTION (uv2)\
	DEBUG_FUNCTION (worldNormal)\
	DEBUG_FUNCTION (vertexNormal)\
	DEBUG_FUNCTION (vertexTangent)\
	DEBUG_FUNCTION (vertexColor)\
	\
	\
	DEBUG_FUNCTION (atten)\
	DEBUG_FUNCTION (IndirectSpecular)\
	DEBUG_FUNCTION (IndirectDiffuse)\
	DEBUG_FUNCTION (DirectSpecular)\
	DEBUG_FUNCTION (DirectDiffuse)\
	DEBUG_FUNCTION (Specular)\
	DEBUG_FUNCTION (Diffuse)\
	DEBUG_FUNCTION (Transmission)\
	DEBUG_FUNCTION (DirectLight)\
	DEBUG_FUNCTION (IndirectLight)\
	DEBUG_FUNCTION (EmissionLight)\
	\
	\
	DEBUG_FUNCTION (Rim)\
	DEBUG_FUNCTION (RimNormal)\
	DEBUG_FUNCTION (RimMask)\
	\
	DEBUG_FUNCTION (GSAA)\
	\
	\
	DEBUG_FUNCTION (MatelValidate)\
	DEBUG_FUNCTION (BasecolorValidate)\




struct DebugData
{
	float4  listData[DEBUG_DATA_COUNT];
	//声明通道变量
#define DEBUG_FUNCTION(name) DEBUG_COLOR_VALUE(name)  
	DEBUG_VALUES
};



#define DEBUG_FUNCTION(name) DEBUG_COLOR_VALUE_ID(name)  
// 设置一下公共ID 给vertexshader 使用所以需要声明全局的
DEBUG_VALUES



inline DebugData DebugDataSetup()
{
	DebugData da = (DebugData)0;
#define DEBUG_FUNCTION(name) DEBUG_SET_COLOR_VER(name)  
	//给遍历设置 index 用
	int index = 0;
	DEBUG_VALUES
		return da;
}

//设置通道 float4 array 
#define DEBUG_SET_COLOR(name)   \
	da.listData[i]= da.name;	\
	name##_index = i;	\
	i++;	\


//设置 linear to Gamma
#define DEBUG_TO_GAMMA(name)  name##.rgb = LinearToGammaSpace(name##.rgb);


//设置通道 float4 array 
float3 CheckByIndex(DebugData da, int index)
{
	float3 col = float3(0, 0, 0);
	int i = 0;

	da.Specular = da.DirectSpecular + da.IndirectSpecular;
	da.Diffuse = da.DirectDiffuse + da.IndirectDiffuse;
	da.DirectLight = da.DirectSpecular + da.DirectDiffuse;
	da.IndirectLight = da.IndirectDiffuse + da.IndirectSpecular;



	//MatelValidate
	float checkMatel = abs(da.metallic.r - 0.5) / 0.5;
	float matel = round(da.metallic.r);
	da.MatelValidate.rgb = lerp(METAL_VIUSAL_COLOR_ERROR, METAL_VIUSAL_COLOR_NORMAL, checkMatel);

	//BasecolorValidate
	float  grey = Luminance(da.baseColor.rgb);

	fixed3 DielectMinVisuaMask = ceil(saturate(-grey + DIELECTRIC_DIFFUSE_MIN));
	fixed3 DielectMaxVisualMask = 1 - ceil(saturate(DIELECTRIC_DIFFUSE_MAX - grey));
	fixed3 DielectVisualMask = lerp(ELECTRIC_VIUSAL_COLOR_HIGH, PBR_VIUSAL_COLOR_NORMAL, DielectMinVisuaMask) * lerp(ELECTRIC_VIUSAL_COLOR_LOW, PBR_VIUSAL_COLOR_NORMAL, DielectMaxVisualMask);

	fixed3 DielectMinVisual = lerp(DIELECTRIC_VIUSAL_COLOR_LOW, PBR_VIUSAL_COLOR_NORMAL, clamp(0, DIELECTRIC_DIFFUSE_MIN, grey) / DIELECTRIC_DIFFUSE_MIN);
	fixed3 DielectMaxVisual = lerp(DIELECTRIC_VIUSAL_COLOR_HIGH, PBR_VIUSAL_COLOR_NORMAL, 1 - saturate(grey - DIELECTRIC_DIFFUSE_MAX) / (1 - DIELECTRIC_DIFFUSE_MAX));
	fixed3 DielectVisual = DielectMinVisual * DielectMinVisuaMask + DielectMaxVisual * DielectMaxVisualMask;

	fixed3 ElectMinVisuaMask = ceil(saturate(-grey + ELECTRIC_DIFFUSE_MIN));
	fixed3 ElectMinVisual = lerp(ELECTRIC_VIUSAL_COLOR_LOW, PBR_VIUSAL_COLOR_NORMAL, 1 - saturate(-grey + ELECTRIC_DIFFUSE_MIN) / ELECTRIC_DIFFUSE_MIN);

	fixed3 ElectVisual = ElectMinVisuaMask * ElectMinVisual;

	da.BasecolorValidate.rgb = lerp(DielectVisual, ElectVisual, matel);

	//设置通道 float4 array 
#define DEBUG_FUNCTION(name) DEBUG_SET_COLOR(name)  
	DEBUG_VALUES


		//通道选择
		float4 rcol = da.listData[index];
		float rcolChannel = rcol.r * _DebugCheckChannels[0] + rcol.g * _DebugCheckChannels[1] + rcol.b * _DebugCheckChannels[2] + rcol.a * _DebugCheckChannels[3];
		col = lerp(float3(rcol.r * _DebugCheckChannels[0], rcol.g * _DebugCheckChannels[1], rcol.b * _DebugCheckChannels[2]), float3(rcolChannel, rcolChannel, rcolChannel), _DebugCheckChannels[4]);

		return col;
}

//#define APPLY_DEBUG_CHECK(col,da,index) (col).rgb = CheckByIndex(da,index);




float _DebugUvTitle;// = 30;
float _DebugUv;// = 30;
float _DebugUvBG;// = 30;
//棋盘格生成
float3 UVcheckMap(float2 uv)
{

	//float t = 30;
	float cx = step(frac(uv.x * _DebugUvTitle), 0.5);
	float cy = step(frac(uv.y* _DebugUvTitle),0.5);

	float c = step(cx, 0.5)+ step(cy, 0.5);

	if (c>1)
	{
		c = 0;
	}


	c = c * 0.5 +0.5;
	// c = c * _DebugUvBG +0.5;
	float3 col = float3(c, c, c);

	if (uv.x * uv.x > 1)
	{

		if (uv.y * uv.y > 1)
		{
			col *= float3(0.8, 0.2, 0.2);
		}
	}
	return col;
}

#endif 






//曝光可视化
fixed3 debugColors[16];

half _FalseColor = 0;
half _falseColorExposure = 0;


half3 ExposureVisual(half3 col)
{

	debugColors[0] = fixed3(0.0, 0.0, 0.0);  // black
	debugColors[1] = fixed3(0.0, 0.0, 0.1647);  // darkest blue
	debugColors[2] = fixed3(0.0, 0.0, 0.3647);  // darker blue
	debugColors[3] = fixed3(0.0, 0.0, 0.6647);  // dark blue
	debugColors[4] = fixed3(0.0, 0.0, 0.9647);  // blue
	debugColors[5] = fixed3(0.5, 0.5, 0.5);  // cyan
	//debugColors[5] = fixed3(0.0, 0.9255, 0.9255);  // cyan
	debugColors[6] = fixed3(0.0, 0.5647, 0.0);  // dark green
	debugColors[7] = fixed3(0.0, 0.5647, 0.0);  // green
	debugColors[8] = fixed3(0.0, 0.7843, 0.0);  // yellow
	debugColors[9] = fixed3(1, 1, 0);  //  yellow-orange
	debugColors[10] = fixed3(0.90588, 0.75294, 0.0);  // orange
	debugColors[11] = fixed3(1.0, 0.5647, 0.0);  // bright red 
	debugColors[12] = fixed3(1.0, 0.0, 0.0);  // red
	debugColors[13] = fixed3(1.0, 0.0, 1.0);  // magenta
	debugColors[14] = fixed3(0.6, 0.3333, 0.7882);  // purple
	debugColors[15] = fixed3(1.0, 1.0, 1.0);  // white

	float v = log2(dot(col, float3(0.3, 0.59, 0.11)).x / 0.18);
	//float v = log10(dot(col, float3(0.3, 0.59, 0.11)).x );
	v = clamp(v + 5.0, 0.0, 15.0);
	v = clamp(v, 0.0, 15.0);
	int index = int(floor(v));
	half3 colv = lerp(debugColors[index], debugColors[min(15, index + 1)], v - floor(v));
	return colv;
}




//照度可视化
//#if _DEBUG_LUX_ENABLED
//col = gi.diffuse + light.color * diffuseTerm;
//col = dot(col, float3(0.3, 0.59, 0.11));
//fixed3 c0 = fixed3(0, 0, 1);
//fixed3 c1 = fixed3(1, 1, 0);
//fixed3 c2 = fixed3(1, 0, 0);
//fixed3 c3 = fixed3(1, 1, 1);
//if (col.r < 1)
//	col = lerp(c0, c1, col.r);
//if (col.r < 2 && 1 < col.r)
//	col = lerp(c1, c2, col.r - 1);
//if (col.r < 5 && 2 < col.r)
//	col = lerp(c2, c3, (col.r - 2) / 3);
//#endif   
//
//#   ifdef UNITY_COLORSPACE_GAMMA
//col = LinearToGammaSpace(col);
//#   endif
//



//【Debug】Tonemaping  ACES 测试部分  (fengxzeng)
uniform float _ACESLum;
uniform float _ACESOn;
float3 ACESToneMapping(float3 color, float adapted_lum)
{
	const float A = 2.51f;
	const float B = 0.03f;
	const float C = 2.43f;
	const float D = 0.59f;
	const float E = 0.14f;

	color *= adapted_lum;
	return (color * (A * color + B)) / (color * (C * color + D) + E);
}
#define APPLY_TONEMAPING_ACES(col) (col).rgb = lerp((col).rgb, ACESToneMapping((col).rgb, 1), _ACESOn);






#endif


#if _XXXXXXX

//================================
////【废弃】废弃部分（待删除） /////////////
//================================

//【废弃】(fengxzeng)
float3 RotateAroundAxis(float3 center, float3 original, float3 u, float angle)
{
	original -= center;
	float C = cos(angle);
	float S = sin(angle);
	float t = 1 - C;
	float m00 = t * u.x * u.x + C;
	float m01 = t * u.x * u.y - S * u.z;
	float m02 = t * u.x * u.z + S * u.y;
	float m10 = t * u.x * u.y + S * u.z;
	float m11 = t * u.y * u.y + C;
	float m12 = t * u.y * u.z - S * u.x;
	float m20 = t * u.x * u.z - S * u.y;
	float m21 = t * u.y * u.z + S * u.x;
	float m22 = t * u.z * u.z + C;
	float3x3 finalMatrix = float3x3(m00, m01, m02, m10, m11, m12, m20, m21, m22);
	return mul(finalMatrix, original) + center;
}

#endif		



#endif		
