
// Upgrade NOTE: replaced '_Object2World' with 'unity_ObjectToWorld'
// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

Shader "OutGame/Scene/DistortPR" 
{
	Properties 
	{
		_WaveScale ("Wave Scale", Range (0.02,0.15)) = 0.063
		_ReflDistort ("Reflection Distort", Range (0,1.5)) = 0.44
		[NoScaleOffset] _BumpMap ("Distort Normalmap ", 2D) = "bump" {}
		_NormalmapPower ("Normalmap Power", Range (0.01,1)) = 1
		WaveSpeed ("Wave speed (map1 x,y; map2 x,y)", Vector) = (19,9,-16,-7)
		_ReflColor ("Reflection Color", COLOR)  = ( .34, .85, .92, 1)
		[NoScaleOffset] _ReflectiveColor ("Reflective Color (RGB) fresnel (A) ", 2D) = "" {}
		_FresnelPower ("fresnel Power", Range (0.01,1)) = 1
		[HideInInspector] _ReflectionTex ("Internal Reflection", 2D) = "" {}
		// _SmoothMin("Smooth Min", Range(0,1)) = 0
		// _SmoothMax("Smooth Max", Range(0,1)) = 1
	}


	Subshader 
	{
		Tags { "WaterMode"="Reflective" "RenderType"="Opaque" }

		Pass 
		{
			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			#pragma multi_compile_fog
			#pragma multi_compile WATER_REFLECTIVE WATER_SIMPLE

			#include "UnityCG.cginc"

			uniform float4 _WaveScale4;
			uniform float4 _WaveOffset;
			uniform float _ReflDistort;
			sampler2D _ReflectionTex;
			sampler2D _ReflectiveColor;
			uniform float4 _ReflColor;
			sampler2D _BumpMap;
			fixed _NormalmapPower,_FresnelPower;

			fixed _SmoothMin, _SmoothMax;

			struct appdata {
				float4 vertex : POSITION;
				float3 normal : NORMAL;
			};

			struct v2f {
				float4 pos : SV_POSITION;

				float4 ref : TEXCOORD0;
				float2 bumpuv0 : TEXCOORD1;
				float2 bumpuv1 : TEXCOORD2;
				float3 viewDir : TEXCOORD3;

				UNITY_FOG_COORDS(4)
			};

			v2f vert(appdata v)
			{
				v2f o;
				UNITY_INITIALIZE_OUTPUT(v2f, o);
				
				o.pos = UnityObjectToClipPos (v.vertex);

				// scroll bump waves
				float4 temp;
				float4 wpos = mul (unity_ObjectToWorld, v.vertex);
				temp.xyzw = wpos.xzxz * _WaveScale4 + _WaveOffset;
				o.bumpuv0 = temp.xy;
				o.bumpuv1 = temp.wz;
				
				// object space view direction (will normalize per pixel)
				o.viewDir.xzy = WorldSpaceViewDir(v.vertex);

				#if defined(WATER_REFLECTIVE)
				o.ref = ComputeScreenPos(o.pos);
				#endif

				UNITY_TRANSFER_FOG(o,o.pos);
				return o;
			}

			

			half4 frag( v2f i ) : SV_Target
			{
				i.viewDir = normalize(i.viewDir);
				
				// combine two scrolling bumpmaps into one
				half3 bump1 = UnpackNormal(tex2D( _BumpMap, i.bumpuv0 )).rgb;
				half3 bump2 = UnpackNormal(tex2D( _BumpMap, i.bumpuv1 )).rgb;
				half3 bump = (bump1 + bump2) * 0.5 * _NormalmapPower;
				//half3 bump = bump1;
				
				// fresnel factor
				half fresnelFac = dot( i.viewDir, bump );

				#if defined(WATER_REFLECTIVE)
				float4 uv1 = i.ref; 
				uv1.xy += bump * _ReflDistort;
				half4 refl = tex2Dproj( _ReflectionTex, UNITY_PROJ_COORD(uv1) );
				#endif
				
				half4 color;
				
				#if defined(WATER_REFLECTIVE)
				half4 water = tex2D( _ReflectiveColor, float2(fresnelFac,fresnelFac) );
				water.a = lerp(1, water.a, _FresnelPower);
				color.rgb = lerp( water.rgb, refl.rgb, water.a );
				color.a = refl.a * water.a;
				#endif

				#if defined(WATER_SIMPLE)
				half4 water = tex2D( _ReflectiveColor, float2(fresnelFac,fresnelFac) );
				color.rgb = lerp( water.rgb, _ReflColor.rgb, water.a );
				color.a = _ReflColor.a;
				#endif
				

				UNITY_APPLY_FOG(i.fogCoord, color);
				return color;
			}
			ENDCG

		}
	}

}
