//Eye	--ho<PERSON><PERSON><PERSON>
Shader "OutGame/Character/Eye"
{
	Properties
	{
		//【ID864230841】局外爆炸黑白闪	--<PERSON><PERSON><PERSON><PERSON>
		[Toggle]_boom ("Boom", Range (0, 1)) = 0
		[LightDir]_BoomDir("BoomDir", Vector) = (0.5,0.5,0,0)
		// 【ID863885247】局外升星，场景变黑			--ho<PERSON><PERSON><PERSON>
		[HideInInspector]_dark("Dark", Range(0.0, 1.0)) = 0
		
		[HideInInspector][Toggle(_CHAR_LIGHT_MAP)] _LightmapEnabled("Enable Lightmap", float) = 0.0
		[HideInInspector][HDR] _LightmapColor("_LightmapColor", Color) = (1,1,1,1)
		[HideInInspector][NoScaleOffset]	_LightMap("lightmap", 2D) = "black" {}
		[HideInInspector]_Color("Color", Color) = (1,1,1,1)		//实时点光需要

		//blending state 
		[HideInInspector] _Mode("Blend Mode", float) = 0
		[HideInInspector] _SrcBlend("Source Blend", float) = 1.0
		[HideInInspector] _DstBlend("Destination Blend", float) = 0.0
		[HideInInspector] _ZWrite("Z Write", float) = 1.0
		
		[Header(Common)]
		[NoScaleOffset] _Bump("Bump Map", 2D) = "bump"{}
		_BumpScale("BumpScale", Float) = 1.0
		_ScleraSmoothness("ScleraSmoothness", Range(0.0, 1.0)) = 0.1
		_CorneaSmoothness("CorneaSmoothness", Range(0.0, 1.0)) = 0.9		//角膜
		_AOIntensity("AO Intensity", Range(0,1)) = 1
		[Enum(UnityEngine.Rendering.CullMode)] _Cull ("Cull Mode", Float) = 2
		[NoScaleOffset]	_MaskTex("Mask Map", 2D) = "white" {}

		[Space(10)][Header(Sclera)]
		[NoScaleOffset]	_MainTex("ScleraTex", 2D) = "white" {}
		[HDR]_ScleraColor("ScleraColor", Color) = (1,1,1,1)
		_ScleraSize("ScleraSize", Range(0.85,2.2)) = 1.0	//巩膜（眼白）
		
		[Space(10)][Header(Iris)]
		[NoScaleOffset]	_IrisColorTex("IrisColorTex", 2D) = "white" {}
		[HDR]_IrisColor("IrisColor", Color) = (1,1,1,1)
		_IrisSize("IrisSize", Range(1.0, 5.0)) = 1.88			//虹膜
		[HDR]_PupilColor("PupilColor", Color) = (1,1,1,1)	
		_PupilSize("PupilSize", Range(0.0, 1.0)) = 0.27			//瞳孔
		_Parallax("Parallax", Range(0.0, 0.1)) = 0.05
		
		[Space(10)][Header(Indirect Specular Reflection)]
		[Toggle(_CUBE)] _CUBE("Enable Cubemap", float) = 0.0		//美术要求默认用2D高光
		[NoScaleOffset]_Cubemap("CubeMap", CUBE) = "Skybox"{}
		_Rotation("Rotation", Range(0, 360)) = 0
		_ReflInten("Reflection Intensity", Range(0, 10)) = 0.0
		_ReflPow("Reflection Contrast", Range(0, 10)) = 1.0
		_ReflLOD("Reflection LOD", Range(0, 8)) = 1

		// 【ID864232499】保留原眼睛shader Mt_Pet_Eyes的贴图高光，可与cubemap方式切换使用		--horacezhao
		_EyesTex("Eyes Form", 2D) = "black" {}
		_EyesArea("XY:U;ZW:V", Vector) = (-1,1,-1,1)
		_Strength("Eyes Strength", Range(0,1)) = 1
		[HDR]_EyesColor ("Eyes Color", Color) = (1.0,1.0,1.0,1.0)
	}

	SubShader
	{
		Tags{ "RenderType" = "Opaque" "PerformanceChecks" = "False" }
		LOD 100
		Stencil{
			Ref[_StencilRef]
			ReadMask[_StencilRead]
			WriteMask[_StencilWrite]
			Pass[_StencilPass]
		}

		Pass
		{
			Name "FORWARD"
			Tags{ "LightMode" = "ForwardBase" }
			Cull Back

			CGPROGRAM

			#pragma vertex vert_main
			#pragma fragment frag_main
			#pragma target 3.0

			#pragma multi_compile_fwdbase //nolightmap nodynlightmap 
			#pragma shader_feature _DEBUG_LUX_ENABLED
			#pragma shader_feature _ALPHATEST_ON
			#pragma shader_feature LIGHTMAP_ON
			#pragma shader_feature LIGHTMAP_SHADOW_MIXING
			#pragma shader_feature SHADOWS_SHADOWMASK
			#pragma shader_feature _METALLICGLOSSMAP
			#pragma shader_feature _CUBE

			#define _POINT_LIGHT 1
			// #define _RIM 1
			#define _EYE 1
			#define _LOW 1		//不走PBR光照

			#pragma shader_feature INSTANCING_ON
			#pragma skip_variants POINT SPOT FOG_EXP FOG_EXP2 VERTEXLIGHT_ON SHADOWS_CUBE
			#include "./Common/CustomPBRCoreForward.cginc"
			#include "UnityCG.cginc"
			
			ENDCG
		}
		Pass
		{
			Name "FORWARD_DELTA"
			Tags { "LightMode" = "ForwardAdd" }
			Blend [_SrcBlend] One
			Fog { Color (0,0,0,0) } // in additive pass fog should be black
			ZWrite Off
			ZTest LEqual
			ColorMask rgba
			//Offset -1, -1

			CGPROGRAM
			#pragma target 3.0
			#pragma multi_compile POINT
			#pragma shader_feature _ _ALPHATEST_ON _ALPHABLEND_ON _ALPHAPREMULTIPLY_ON
			//#define POINT

			#pragma vertex vert_add
			#pragma fragment frag_add
			#include "UnityCG.cginc"
			#include "Lighting.cginc"
			#include "AutoLight.cginc"
			#include "./Common/CustomPBRCoreForward.cginc"

			ENDCG
		}
		Pass
		{
			Name "META" 
			Tags { "LightMode"="Meta" }

			Cull Off

			CGPROGRAM
			#pragma vertex vert_meta
			#pragma fragment frag_meta
			#pragma shader_feature _METALLICGLOSSMAP
			#pragma shader_feature _SMOOTHNESS_TEXTURE_ALBEDO_CHANNEL_A
			#pragma shader_feature _DETAIL_MULX2
			#pragma shader_feature EDITOR_VISUALIZATION

			#include "UnityStandardMeta.cginc"
			ENDCG
		}
		Pass 
		{
			Name "ShadowCaster"
			Tags { "LightMode" = "ShadowCaster" }
			
			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			#pragma target 2.0
			#pragma multi_compile_shadowcaster
			#pragma shader_feature INSTANCING_ON
			#include "UnityCG.cginc"

			struct v2f 
			{ 
				V2F_SHADOW_CASTER;
				UNITY_VERTEX_OUTPUT_STEREO
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			v2f vert( appdata_base v )
			{
				v2f o;
				TRANSFER_SHADOW_CASTER_NORMALOFFSET(o)
				return o;
			}

			float4 frag( v2f i ) : SV_Target
			{
				UNITY_SETUP_INSTANCE_ID(i);
				SHADOW_CASTER_FRAGMENT(i)
			}
			ENDCG

		}
	}
	//use unique shadow
	CustomEditor "EyeShaderGUI"
}