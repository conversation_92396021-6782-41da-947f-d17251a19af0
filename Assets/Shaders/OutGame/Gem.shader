// Upgrade NOTE: replaced '_Object2World' with 'unity_ObjectToWorld'

// Upgrade NOTE: replaced '_Object2World' with 'unity_ObjectToWorld'

Shader "OutGame/Character/Gem"
{
    Properties
    {
        _MainTex ("Texture,默认贴图,alpha:控制透明度", 2D) = "white" {}
        //_Matcap("Matcap",2D) = "white" {}
        [Toggle(_NORMAL_ON)]NormalOn("NormalMapOn，是否用法线贴图",float) = 0
        _NormalMap("Normal Map,法线贴图",2D) = "white" {}
        
        _RimIntensity("RimIntensity，边缘光强度",Range(0,10))=0.1
        _RimColor("RimColor，边缘光颜色",Color) = (1,1,1,1)
        _DefaultLightColor("DefaultLightColor，高光颜色",Color) = (1,1,1,1)
        _HighLightIntensity("HighLightIntensity，高光强度",Range(1,10)) = 5
        _SpecularScale("SpecularScale,高光缩放",Range(0,5)) = 1
        _OffsetIntensity("OffsetIntensity，高光偏移程度",Range(0,0.3)) = 0.1
        _DefaultColor("DefaultColor，暗面颜色",Color) = (1,1,1,1)
        _ReflectionStrength("ReflectionStrength，反射强度",Range(0,1))=0.3
        _Alpha("Alpha，透明度",Range(0,1)) = 1
        
        
        [Header(Diomand)]
        [Toggle(_TEX_ON)]_Toggle("DiomandUsed，是否为切面宝石",float) = 0
        _ShineColor("ShineColor，碎片颜色",Color) = (1,1,1,1)
        _OpalMap("OpalMap",2D) = "white"{}//晶状体的贴图
        [HideInInspector]_OpalDepth("_OpalDepth，深度",float) = 0.5
        _OpalFrequency("_OpalFrequency，频率",float) = 0.5
        _IOR("_IOR,折射",Range(0,1)) = 0.5
        _Level("_Level",Range(0,1)) = 0.5
        _OpalStrength("_OpalStrength，油膜强度",Range(0,16)) = 0.5
        _DiomandOffset("DiomandTex Offset，xy:tiling,zw:offset",Vector)=(1,1,1,1)

    }
    SubShader
    {
        Tags { "RenderType"="Transparent" "Queue"="Transparent" }
        
        
		LOD 100
        
		Pass {
            ZWrite on
            Cull Front
            Blend SrcAlpha OneMinusSrcAlpha   
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            // make fog work
            #pragma multi_compile_fog
            #pragma shader_feature _TEX_ON
            #pragma shader_feature _NORMAL_ON

            #include "UnityCG.cginc"
            #include "Lighting.cginc"
            #include "AutoLight.cginc"
            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float3 normal : NORMAL;
                float4 tangent :TANGENT;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                UNITY_FOG_COORDS(1)
                float3 viewPos : TEXCOORD2;
                float3 viewDir : TEXCOORD3;
                float3 worldNormal : TEXCOORD4;
                float3 worldView : TEXCOORD5;
                float3 worldPos : TEXCOORD6;
                float3 tangentViewDir : TEXCOORD7;
                
                float4 vertex : SV_POSITION;
            };
            #if _TEX_ON
                sampler2D _OpalMap;
                float4 _OpalMap_ST;    
                float4 _ShineColor;    
                float4 _DiomandOffset;
                half _IOR;
                half _Level;
                half _OpalStrength;
                half _OpalDepth;
                half _OpalFrequency;
           #endif
            sampler2D _MainTex;
			sampler2D _NormalMap;
            float4 _DefaultColor;
            float _Alpha;
            #if _TEX_ON
            half3 FilmIridescence_MonsterHunterWorld(half cos0)
            {
	            half tr = cos0 * _Level - _IOR;
	            half3 n_color = (cos((tr * 35.0) * half3(1, 1, 1.0)) * -0.5) + 0.5;
	            n_color = lerp(n_color*_ShineColor, half3(0.5, 0.5, 0.5), tr);
	            //n_color = lerp(n_color*_ShineColor, _ShineColor, tr);
	            n_color *= n_color * _OpalStrength;
	            return n_color;
            }
            half3 Opal_Shading(half3 TV, half2 uv, half Temp, half SignFace, half3 Diffuse)
            {
	            float2 Opal_UV = TRANSFORM_TEX(uv, _OpalMap);
	            float2 Opal_Depth_UV = Opal_UV + ParallaxOffset(Temp, _OpalDepth, TV * SignFace);
	            
	            half Height = tex2D(_OpalMap, Opal_UV).b;
	            //half3 Height = tex2D(_OpalMap, Opal_UV);
	            half2 Data = tex2D(_OpalMap, Opal_Depth_UV).rg; //ID/Mask
	            half3 Opal = FilmIridescence_MonsterHunterWorld(Data.r + Temp *_OpalFrequency) * Data.g;
	            Opal = lerp(0, Opal, Height);
	            return Opal;
	            //return Height*_DefaultColor;
	            //return half3(Data.g,Data.g,Data.g);
            }
            #endif
            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.worldNormal = UnityObjectToWorldNormal(v.normal);
                o.uv = v.uv;
                o.viewPos = mul(UNITY_MATRIX_MV, v.vertex);
                float3 worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
                o.worldPos = worldPos;
	            o.worldView = normalize(o.worldPos - _WorldSpaceCameraPos); 
                o.viewDir = normalize(ObjSpaceViewDir(v.vertex));
                half3x3 objectToTangent = half3x3(
		            v.tangent.xyz,
		            cross(v.normal, v.tangent.xyz) * v.tangent.w,
		            v.normal
		            );
                o.tangentViewDir.xyz = mul(objectToTangent, ObjSpaceViewDir(v.vertex)).xyz;
                UNITY_TRANSFER_FOG(o,o.vertex);
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                // sample the texture
                float4 col = tex2D(_MainTex,i.uv);
                fixed4 dark_col = fixed4(0,0,0,1);
                float3 worldNormal = i.worldNormal;
#if _NORMAL_ON
                worldNormal = normalize(UnpackNormal(tex2D(_NormalMap,i.uv))).xyz;
#endif

                #if _TEX_ON
                    float4 diffuseColor = float4(1,1,1,1);
                    col = diffuseColor;
                    half3 R = normalize(reflect(-i.worldView, worldNormal));
		            half NoL = dot(worldNormal,normalize(UnityWorldSpaceLightDir(i.worldPos)));//灯光位置
		            half VoR = abs(dot(i.worldView, R));
		            half SignFace = sign(dot(worldNormal, i.worldView));		
		            half Temp = NoL * VoR;
		            half3 TV = normalize(i.tangentViewDir.xyz);
                    half3 Opal = Opal_Shading(TV,i.uv.xy*_DiomandOffset.xy+_DiomandOffset.zw, Temp, SignFace,diffuseColor.xyz);
                    col.xyz*=Opal;
                    
                    return fixed4(col.rgb,_Alpha*col.a);
                #endif
                
		        ////return fixed4(col.xyz,_InsideAlpha);
		        return float4(0,0,0,_Alpha);
            }
            ENDCG
		}
        Pass{
            ZWrite on
            Blend SrcAlpha one
            Cull back
            CGPROGRAM
            #pragma vertex vert
	        #pragma fragment frag

            #pragma shader_feature _TEX_ON
            #pragma shader_feature _NORMAL_ON

	        #include "UnityCG.cginc"
            #include "Lighting.cginc"
            #include "AutoLight.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float3 normal : NORMAL;
            };
            struct v2f
            {
                float2 uv : TEXCOORD0;
                float3 worldPos : TEXCOORD1;
                float3 worldNormal : TEXCOORD4;
                UNITY_FOG_COORDS(2)
                SHADOW_COORDS(3)
                float4 vertex : SV_POSITION;
            };
            sampler2D _MainTex;
            sampler2D _Matcap;
            sampler2D _NormalMap;
            float4 _MainTex_ST;
           
            float _Refraction;
            float _RimIntensity;
            float4 _RimColor;
            float4 _DefaultColor;
            float4 _DefaultLightColor;
            float4 _ShineColor;
            float _OffsetIntensity;
            float _HighLightIntensity;
            float _ReflectionStrength;
            float _Alpha;
            float _SpecularScale;
           

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.worldPos = mul(unity_ObjectToWorld,v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                o.worldNormal = UnityObjectToWorldNormal(v.normal);

                
                UNITY_TRANSFER_FOG(o,o.vertex);
                TRANSFER_SHADOW(o);
                return o;
            }
            fixed4 frag (v2f i) : SV_Target
            {
                fixed4 finalColor = fixed4(0,0,0,1);
                fixed4 source1 = tex2D(_MainTex, i.uv.xy);
                fixed3 albedo = source1.rgb;
                fixed3 normalDir = i.worldNormal;
#if _NORMAL_ON
                normalDir = normalize(UnpackNormal(tex2D(_NormalMap,i.uv))).xyz;
#endif

               
                fixed3 viewDir = normalize(_WorldSpaceCameraPos.xyz - i.worldPos.xyz);

                fixed3 offset =normalize(_WorldSpaceCameraPos.xyz) - normalize(_WorldSpaceLightPos0.xyz);
                fixed3 worldNormal_offset = (normalDir-_OffsetIntensity*offset);//视角变换影响法线偏移
                fixed3 reflectDir = normalize(reflect(-viewDir, worldNormal_offset));

                half4 reflection = UNITY_SAMPLE_TEXCUBE(unity_SpecCube0, reflectDir);
                reflection.rgb = DecodeHDR (reflection, unity_SpecCube0_HDR);//reflection

                fixed3 refractDir = refract(normalDir,viewDir,_Refraction);
                half4 refraction = UNITY_SAMPLE_TEXCUBE(unity_SpecCube0, refractDir);

                fixed RdotV = max(0.0, dot(reflectDir, viewDir));
                float specularIntensity = pow(RdotV, _HighLightIntensity) * _SpecularScale;

                fixed rim = max(0,dot(normalDir,viewDir));
                float fresnelStrength = pow((1.0 - rim), _RimIntensity) ;
                finalColor+=(1-specularIntensity)*_DefaultColor;
                finalColor+=fresnelStrength*_RimColor;
                finalColor+=specularIntensity*_DefaultLightColor;
                finalColor+=reflection*_ReflectionStrength;
                //finalColor+=refraction;
                //return specularIntensity*_DefaultLightColor;
                //return (fresnelStrength*_RimColor+specularIntensity*_DefaultLightColor);
                return fixed4(finalColor.rgb,source1.a*_Alpha);

            }
            ENDCG
        
        }
        
    //    Pass{//Render Outside
    //        ZWrite on
    //        Blend SrcAlpha one
    //        Cull back
    //        CGPROGRAM
    //        #pragma vertex vert
    //        #pragma fragment frag
    //        #pragma multi_compile_fog

    //        #include "UnityCG.cginc"
    //        #include "Lighting.cginc"
    //        #include "AutoLight.cginc"
    //        //#pragma multi_compile_fwdbase
    //        #define POINT 0
    //        struct appdata
    //        {
    //            float4 vertex : POSITION;
    //            float2 uv : TEXCOORD0;
    //        };

    //        struct v2f
    //        {
    //            float2 uv : TEXCOORD0;
    //            float3 worldPos : TEXCOORD1;
    //            UNITY_FOG_COORDS(2)
    //            SHADOW_COORDS(3)
    //            float4 vertex : SV_POSITION;
    //        };

    //        sampler2D _MainTex;
    //        sampler2D _Matcap;
    //        sampler2D _NormalMap;
    //        float4 _MainTex_ST;
           
    //        float _Refraction;
    //        float _RimIntensity;
    //        float4 _RimColor;
    //        float4 _DefaultColor;
    //        float4 _DefaultLightColor;
    //        float4 _ShineColor;
    //        float _OffsetIntensity;
    //        float _HighLightIntensity;
    //        float _ReflectionStrength;
    //        float _Alpha;

    //        v2f vert (appdata v)
    //        {
    //            v2f o;
    //            o.vertex = UnityObjectToClipPos(v.vertex);
    //            o.worldPos = mul(unity_ObjectToWorld,v.vertex);
    //            o.uv = TRANSFORM_TEX(v.uv, _MainTex);
    //            UNITY_TRANSFER_FOG(o,o.vertex);
    //            TRANSFER_SHADOW(o);
    //            return o;
    //        }

    //        fixed4 frag (v2f i) : SV_Target
    //        {
    //            fixed4 finalColor = fixed4(0,0,0,1);
    //            fixed4 col = tex2D(_MainTex,i.uv);//alpha通道为透明度通道
    //            fixed3 offset =normalize(_WorldSpaceCameraPos.xyz) - normalize(_WorldSpaceLightPos0.xyz);

    //            fixed3 worldNormal = normalize(UnpackNormal(tex2D(_NormalMap,i.uv)));//normal map


    //            fixed3 worldNormal_offset = (worldNormal-_OffsetIntensity*offset);//视角变换影响法线偏移

    //            fixed2 viewNormal = fixed2(1,1);
    //            viewNormal.x = mul(UNITY_MATRIX_V[0].xyz,worldNormal_offset)*0.5+0.5;
    //            viewNormal.y = mul(UNITY_MATRIX_V[1].xyz,worldNormal_offset)*0.5+0.5;//matcap uv

    //            fixed3 worldLightDir = normalize(_WorldSpaceLightPos0.xyz);
				
    //            fixed3 reflectLightDir = normalize(reflect(-worldLightDir, worldNormal_offset));//reflectdir
                
    //            fixed3 viewDir = normalize(_WorldSpaceCameraPos.xyz-i.worldPos.xyz);

    //            fixed mask = saturate((dot(reflectLightDir, viewDir)));//highlight_mask
    //            fixed specMask = step(0.01,mask);
    //            fixed3 rim_col = pow(specMask,_RimIntensity)*_RimColor;

    //            fixed3 reflectuv = reflect(-viewDir,worldNormal);

    //            half4 reflection = UNITY_SAMPLE_TEXCUBE(unity_SpecCube0, reflectuv);
				//reflection.rgb = DecodeHDR (reflection, unity_SpecCube0_HDR);//reflection

    //            fixed3 refractDir = refract(worldNormal,viewDir,_Refraction);
    //            half4 refraction = UNITY_SAMPLE_TEXCUBE(unity_SpecCube0, refractDir);
				//refraction.rgb = DecodeHDR (refraction, unity_SpecCube0_HDR);//refraction_test
    //            //fixed3 refraction = texCUBE(_RefractionTex, refractDir).rgb;//refraction

    //            fixed4 MatCol = tex2D(_Matcap,viewNormal);

    //            UNITY_LIGHT_ATTENUATION(atten, i, i.worldPos);
                
    //            fixed highLight = pow(specMask, _HighLightIntensity);
                
    //            half3 reflection2 = reflection.rgb*_ReflectionStrength*specMask;
                
    //            fixed4 RenderCol = fixed4(reflection2,1);
    //            fixed4 AddCol =  lerp(_DefaultColor,_DefaultLightColor,smoothstep(0,1,specMask))*col;
    //            AddCol.rgb += rim_col.rgb;
                
    //            UNITY_APPLY_FOG(i.fogCoord, col);
                
    //            return fixed4((RenderCol.rgb+AddCol.rgb*_F0)+lerp(_DefaultColor,MatCol,highLight),lerp(col.a,1,_Alpha))*atten;
                
    //        }
    //        ENDCG
    //    }
        UsePass "VertexLit/SHADOWCASTER"
    }
}
