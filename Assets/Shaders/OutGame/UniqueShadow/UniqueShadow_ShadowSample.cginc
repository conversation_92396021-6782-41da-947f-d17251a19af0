#ifndef FILE_UNIQUESHADOW_SHADOWSAMPLE
#define FILE_UNIQUESHADOW_SHADOWSAMPLE
	
	#if defined(UNIQUE_SHADOW_VeryHigh) || defined(UNIQUE_SHADOW_High) || defined(UNIQUE_SHADOW_Middle)
		#if defined(DIRECTIONAL) || defined(<PERSON><PERSON>EC<PERSON><PERSON><PERSON>_COOKIE)
			#define USE_UNIQUE_SHADOW
		#endif
	#endif
	
	#ifndef USE_UNIQUE_SHADOW
		#define UNIQUE_SHADOW_SAMPLE(i)				1.f
		#define UNIQUE_SHADOW_INTERP(i)
		#define UNIQUE_SHADOW_TRANSFER(o)
		#define UNIQUE_SHADOW_ATTENUATION(i)		1.f
		#define UNIQUE_SHADOW_SHADOW_ATTENUATION(i)	(UNITY_SHADOW_ATTENUATION(i) * UNIQUE_SHADOW_ATTENUATION(i))
		#define UNIQUE_SHADOW_LIGHT_ATTENUATION(i)	(UNITY_LIGHT_ATTENUATION(i) * UNIQUE_SHADOW_ATTENUATION(i))
	#endif

	#ifdef USE_UNIQUE_SHADOW //<-ends at the very bottom

		float BoundsMask(float3 coords )
		{
			half3 bound = step(0.0,coords) * step(coords,1.0);
			half strength = bound.x * bound.y * bound.z ;
			return strength ;
		}

		float DisFade(float worldPos )
		{
			float ZDis = dot(_WorldSpaceCameraPos - worldPos , UNITY_MATRIX_V[2].xyz);
			float fade = saturate(ZDis * _LightShadowData.z * 100 + _LightShadowData.w);
			return fade ;
		}

		#if defined(UNIQUE_SHADOW_VeryHigh)
			
			cbuffer POISSON_DISKS {
				static half2 poisson[80] = {
					half2(0.02971195f, 0.8905211f),
					half2(0.2495298f, 0.732075f),
					half2(-0.3469206f, 0.6437836f),
					half2(-0.01878909f, 0.4827394f),
					half2(-0.2725213f, 0.896188f),
					half2(-0.6814336f, 0.6480481f),
					half2(0.4152045f, 0.2794172f),
					half2(0.1310554f, 0.2675925f),
					half2(0.5344744f, 0.5624411f),
					half2(0.8385689f, 0.5137348f),
					half2(0.6045052f, 0.08393857f),
					half2(0.4643163f, 0.8684642f),
					half2(0.335507f, -0.110113f),
					half2(0.03007669f, -0.0007075319f),
					half2(0.8077537f, 0.2551664f),
					half2(-0.1521498f, 0.2429521f),
					half2(-0.2997617f, 0.0234927f),
					half2(0.2587779f, -0.4226915f),
					half2(-0.01448214f, -0.2720358f),
					half2(-0.3937779f, -0.228529f),
					half2(-0.7833176f, 0.1737299f),
					half2(-0.4447537f, 0.2582748f),
					half2(-0.9030743f, 0.406874f),
					half2(-0.729588f, -0.2115215f),
					half2(-0.5383645f, -0.6681151f),
					half2(-0.07709587f, -0.5395499f),
					half2(-0.3402214f, -0.4782109f),
					half2(-0.5580465f, 0.01399586f),
					half2(-0.105644f, -0.9191031f),
					half2(-0.8343651f, -0.4750755f),
					half2(-0.9959937f, -0.0540134f),
					half2(0.1747736f, -0.936202f),
					half2(-0.3642297f, -0.926432f),
					half2(0.1719682f, -0.6798802f),
					half2(0.4424475f, -0.7744268f),
					half2(0.6849481f, -0.3031401f),
					half2(0.5453879f, -0.5152272f),
					half2(0.9634013f, -0.2050581f),
					half2(0.9907925f, 0.08320642f),
					half2(0.8386722f, -0.5428791f),
					half2(0.02971195f, 0.8905211f),
					half2(0.2495298f, 0.732075f),
					half2(-0.3469206f, 0.6437836f),
					half2(-0.01878909f, 0.4827394f),
					half2(-0.2725213f, 0.896188f),
					half2(-0.6814336f, 0.6480481f),
					half2(0.4152045f, 0.2794172f),
					half2(0.1310554f, 0.2675925f),
					half2(0.5344744f, 0.5624411f),
					half2(0.8385689f, 0.5137348f),
					half2(0.6045052f, 0.08393857f),
					half2(0.4643163f, 0.8684642f),
					half2(0.335507f, -0.110113f),
					half2(0.03007669f, -0.0007075319f),
					half2(0.8077537f, 0.2551664f),
					half2(-0.1521498f, 0.2429521f),
					half2(-0.2997617f, 0.0234927f),
					half2(0.2587779f, -0.4226915f),
					half2(-0.01448214f, -0.2720358f),
					half2(-0.3937779f, -0.228529f),
					half2(-0.7833176f, 0.1737299f),
					half2(-0.4447537f, 0.2582748f),
					half2(-0.9030743f, 0.406874f),
					half2(-0.729588f, -0.2115215f),
					half2(-0.5383645f, -0.6681151f),
					half2(-0.07709587f, -0.5395499f),
					half2(-0.3402214f, -0.4782109f),
					half2(-0.5580465f, 0.01399586f),
					half2(-0.105644f, -0.9191031f),
					half2(-0.8343651f, -0.4750755f),
					half2(-0.9959937f, -0.0540134f),
					half2(0.1747736f, -0.936202f),
					half2(-0.3642297f, -0.926432f),
					half2(0.1719682f, -0.6798802f),
					half2(0.4424475f, -0.7744268f),
					half2(0.6849481f, -0.3031401f),
					half2(0.5453879f, -0.5152272f),
					half2(0.9634013f, -0.2050581f),
					half2(0.9907925f, 0.08320642f),
					half2(0.8386722f, -0.5428791f)
				};
			};

            UNITY_DECLARE_SHADOWMAP(u_UniqueShadowTexture);

			uniform half2 u_UniqueShadowBlockerWidth;
			uniform half u_UniqueShadowBlockerDistanceScale;
			uniform half2 u_UniqueShadowLightWidth;

			half SampleUniquePCSS(half4 uv, half3 worldPos) {

				half shadow = 0.f;
                const float c_LightWidth = lerp(u_UniqueShadowLightWidth.x, u_UniqueShadowLightWidth.y, 0.1f);
				for(int i = 0; i < 16; ++i) {
                    half3 ustCoord = uv.xyz;
                    ustCoord.xy += poisson[i] * c_LightWidth;
                    shadow += UNITY_SAMPLE_SHADOW(u_UniqueShadowTexture, ustCoord);
				} 
				shadow = shadow / 16.f;
				shadow = _LightShadowData.r + shadow * (1-_LightShadowData.r);
				return lerp(1.0, shadow, BoundsMask(uv));
			}

			#define UNIQUE_SHADOW_SAMPLE(i) SampleUniquePCSS(i.uniqueShadowPos, i.worldPos)

		#elif defined(UNIQUE_SHADOW_High)
			static half2 poisson[8] = {
				half2(0.02971195f, -0.8905211f),
				half2(0.2495298f, 0.732075f),
				half2(-0.3469206f, -0.6437836f),
				half2(-0.01878909f, 0.4827394f),
				half2(-0.2725213f, -0.896188f),
				half2(-0.6814336f, 0.6480481f),
				half2(0.4152045f, -0.2794172f),
				half2(0.1310554f, 0.2675925f),
			};
			UNITY_DECLARE_SHADOWMAP(u_UniqueShadowTexture);
            //UNITY_DECLARE_DEPTH_TEXTURE(u_UniqueShadowTexture);
			uniform half2 u_UniqueShadowFilterWidth;
			half SampleUniquePCF(half4 coords, half3 worldPos)
			{
			    half4 uv = coords;
                half shadow = 0.f;
                for(int i = 0; i < 8; ++i) {
                    uv.xy = coords.xy + poisson[i] * u_UniqueShadowFilterWidth;
                    shadow += UNITY_SAMPLE_SHADOW(u_UniqueShadowTexture, uv.xyz);
                } 
				shadow = shadow / 8.f;
				return lerp(1.0, shadow, BoundsMask(coords));
			}
			
			#define UNIQUE_SHADOW_SAMPLE(i) SampleUniquePCF(i.uniqueShadowPos,i.worldPos)

		#elif defined(UNIQUE_SHADOW_Middle)
			UNITY_DECLARE_SHADOWMAP(u_UniqueShadowTexture);
			half SampleUniqueHardShadow(half4 coords, half3 worldPos)
			{
			    half4 uv = coords;
                half shadow = 0.f;
				shadow = UNITY_SAMPLE_SHADOW(u_UniqueShadowTexture, uv.xyz);
				shadow = _LightShadowData.r + shadow * (1-_LightShadowData.r);
				return lerp(1.0, shadow, BoundsMask(coords));
			}
			
			#define UNIQUE_SHADOW_SAMPLE(i) SampleUniqueHardShadow(i.uniqueShadowPos,i.worldPos)
		
		#else
			#define UNIQUE_SHADOW_SAMPLE(i) 1.0f
		#endif

		uniform float4x4 u_UniqueShadowMatrix;

		#define UNIQUE_SHADOW_INTERP(i)				                    float4 uniqueShadowPos : TEXCOORD##i ;
		#define UNIQUE_SHADOW_TRANSFER(o)			                    o.uniqueShadowPos = mul(u_UniqueShadowMatrix, float4(posWorld.xyz, 1.f));
		#define UNIQUE_SHADOW_ATTENUATION(i)		                    UNIQUE_SHADOW_SAMPLE(i)
		#define UNIQUE_SHADOW_SHADOW_ATTENUATION(i, worldPos)	        (UNITY_SHADOW_ATTENUATION(i, worldPos) * UNIQUE_SHADOW_ATTENUATION(i))
		#define UNIQUE_SHADOW_LIGHT_ATTENUATION(destName, i, woldPos)   (UNITY_LIGHT_ATTENUATION(destName, i, woldPos) * UNIQUE_SHADOW_ATTENUATION(i))

		#if defined(UNITY_PASS_FORWARDBASE) || defined(UNITY_PASS_FORWARDADD) || defined(UNIQUE_SHADOW_FORCE_REPLACE_BUILTIN)
			#undef UNITY_SHADOW_COORDS
			#undef UNITY_TRANSFER_SHADOW
			#undef UNITY_SHADOW_ATTENUATION
			#define UNITY_SHADOW_COORDS(i)					UNIQUE_SHADOW_INTERP(i)
			#define UNITY_TRANSFER_SHADOW(i, coord)			o.uniqueShadowPos = mul(u_UniqueShadowMatrix, float4(i.worldPos.xyz, 1.f));
			#define UNITY_SHADOW_ATTENUATION(i, worldPos)	UNIQUE_SHADOW_SAMPLE(i);
		#endif


	#endif //USE_UNIQUE_SHADOWS
#endif //FILE_UNIQUESHADOW_SHADOWSAMPLE