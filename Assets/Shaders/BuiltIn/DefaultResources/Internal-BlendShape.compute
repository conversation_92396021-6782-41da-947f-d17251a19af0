// Unity built-in shader source. Copyright (c) 2016 Unity Technologies. MIT license (see license.txt)

#pragma kernel main
#pragma kernel main SKIN_NORM
#pragma kernel main SKIN_NORM SKIN_TANG

#include "HLSLSupport.cginc"
#include "Internal-Skinning-Util.cginc"

struct BlendShapeVertex
{
    int index;
    float3 pos;
    float3 norm;
    float3 tang;
};

uint g_FirstVert; // First vertex from blend shape buffer to use
uint g_VertCount; // Sparse vertex count, not the full amount of vertices in mesh
float g_Weight;

[numthreads(64, 1, 1)]
void main(uint3 threadID : SV_DispatchThreadID, SAMPLER_UNIFORM RWStructuredBuffer<MeshVertex> inOutMeshVertices, SAMPLER_UNIFORM StructuredBuffer<BlendShapeVertex> inBlendShapeVertices)
{
    const uint t = threadID.x;

    if (t >= g_VertCount)
    {
        return;
    }

    BlendShapeVertex blendShapeVert = inBlendShapeVertices[t + g_FirstVert];

    const uint vertIndex = blendShapeVert.index;

    inOutMeshVertices[vertIndex].pos += blendShapeVert.pos * g_Weight;
#if SKIN_NORM
    inOutMeshVertices[vertIndex].norm += blendShapeVert.norm * g_Weight;
#endif
#if SKIN_TANG
    inOutMeshVertices[vertIndex].tang.xyz += blendShapeVert.tang * g_Weight;
#endif
}
