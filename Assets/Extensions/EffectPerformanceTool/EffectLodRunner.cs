#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using GfxFramework;
using TKFrame;

public class EffectLodRunner : EffectRunner {
    
    private NcCurveAnimation[] m_ncAnimations = null;

    private GfxRoot m_gfxRoot = null;
    
    public override bool isUIEffect()
    {
        return false;
    }

    public override void OnEffectInstanced()
    {
        m_gfxRoot = m_inst.GetComponent<GfxRoot>();

        m_ps = m_inst.GetComponentsInChildren<ParticleSystem>(true);
        m_ncAnimations = m_inst.GetComponentsInChildren<NcCurveAnimation>(true);

        NsEffectManager.SetReplayEffect(m_inst);
    }

    protected override EDevicePower GetMaxLodLevel()
    {
        if (IsLodExist())
            return EDevicePower.EDP_Ultra;
        return EDevicePower.EDP_None;
    }

    /// <summary>
    /// LOD 存在判定规则
    /// 
    /// 1 低配几乎没有LOD0节点，高配可能存在LOD2节点
    /// 2 必定存在LOD1节点
    /// 
    /// </summary>
    /// <returns></returns>
    public override bool IsLodExist()
    {
        return m_gfxRoot != null;
    }

    public override bool HasLod(EDevicePower lod)
    {
        return true;
    }

    public override void ShowLod(EDevicePower lod)
    {

        GfxManager.Instance.Lod = (GfxLod)lod;
        if (lod == EDevicePower.EDP_Ultra)
        {
            GfxManager.Instance.SwitchTag("NoBloom", "Bloom");
        }
        else
        {
            GfxManager.Instance.SwitchTag("Bloom", "NoBloom");
        }

        Debug.Log("ShowLod name:" + effect);
        m_inst.SetActive(false);
        m_inst.SetActive(true);

        Reset();
    }

    private void Reset()
    {
        m_inst.SetActive(false);
        if (m_ncAnimations != null)
        {
            for (int i = m_ncAnimations.Length; --i >= 0;)
            {
                if (m_ncAnimations[i] == null)
                {
                    continue;
                }
                m_ncAnimations[i].ResetAnimation();

            }
        }

        NsEffectManager.RunReplayEffect(this.m_inst, false);

        if (m_ps != null)
        {
            for (int i = m_ps.Length; --i >= 0;)
            {
                ParticleSystem particle = m_ps[i];
                if (particle == null)
                {
                    continue;
                }

                particle.Clear(false);
                particle.Stop(false);
            }
        }

        if (m_ps != null)
        {
            for (int i = 0; i < m_ps.Length; ++i)
            {
                if (m_ps[i] != null)
                {
                    m_ps[i].Play(false);
                }
            }
        }
        m_inst.SetActive(true);
    }
}
#endif