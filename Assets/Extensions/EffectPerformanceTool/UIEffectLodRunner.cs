
using GfxFramework;
#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using TKFrame;
using UnityEngine;

public class UIEffectLodRunner : EffectLodRunner {
    
    private NcCurveAnimation[] m_ncAnimations = null;
    private GfxRoot m_gfxRoot = null;

    private Transform m_parent;
    
    public override bool isUIEffect()
    {
        return true;
    }

    public void SetParent(Transform parent)
    {
        m_parent = parent;
    }

    public override void OnEffectInstanced()
    {
        base.OnEffectInstanced();
        
        m_gfxRoot = m_inst.GetComponent<GfxRoot>();

        m_ps = m_inst.GetComponentsInChildren<ParticleSystem>(true);
        m_ncAnimations = m_inst.GetComponentsInChildren<NcCurveAnimation>(true);

        m_inst.transform.SetParent(m_parent, false);

        m_inst.SetActive(true);
    }

    protected override EDevicePower GetMaxLodLevel()
    {
        if (IsLodExist())
            return EDevicePower.EDP_Ultra;
        return EDevicePower.EDP_None;
    }

    /// <summary>
    /// LOD 存在判定规则
    /// 
    /// 1 低配几乎没有LOD0节点，高配可能存在LOD2节点
    /// 2 必定存在LOD1节点
    /// 
    /// </summary>
    /// <returns></returns>
    public override bool IsLodExist()
    {
        return m_gfxRoot != null;
    }

    public override bool HasLod(EDevicePower lod)
    {
        return true;
    }

    public override void ShowLod(EDevicePower lod)
    {
        GfxManager.Instance.Lod = (GfxLod)lod;
        if (lod == EDevicePower.EDP_Ultra)
        {
            GfxManager.Instance.SwitchTag("NoBloom", "Bloom");
        }
        else
        {
            GfxManager.Instance.SwitchTag("Bloom", "NoBloom");
        }

        m_inst.SetActive(false);
        m_inst.SetActive(true);

        Reset();
    }

    private void Reset()
    {
        if (m_ncAnimations != null)
        {
            for (int i = m_ncAnimations.Length; --i >= 0;)
            {
                if (m_ncAnimations[i] == null)
                {
                    continue;
                }
                m_ncAnimations[i].ResetAnimation();

            }
        }

        if (m_ps != null)
        {
            for (int i = m_ps.Length; --i >= 0;)
            {
                ParticleSystem particle = m_ps[i];
                if (particle == null)
                {
                    continue;
                }

                particle.Clear(false);
                particle.Stop(false);
            }
        }

        if (m_ps != null)
        {
            for (int i = 0; i < m_ps.Length; ++i)
            {
                if (m_ps[i] != null)
                {
                    m_ps[i].Play(false);
                }
            }
        }
    }
}
#endif