#if UNITY_EDITOR
using System;
using UnityEditor;
using UnityEditor.IMGUI.Controls;
using UnityEngine;

public class EffectTableView : BaseTableView<EffectViewInfo>
{
    public Action<EffectViewInfo> whiteListChanged;
    protected EffectTestScene effectScene;

    public EffectTableView(EffectTestScene effectScene, TreeViewState state, MultiColumnHeader multiColumnHeader) : base(state, multiColumnHeader)
    {
        multiColumnHeader.height = 20f;
        this.effectScene = effectScene;
    }

    protected override float GetCustomRowHeight(int row, TreeViewItem item)
    {
        return 20f;
    }

    protected override void DrawRow(RowGUIArgs args)
    {
        base.DrawRow(args);
        BaseTableItem<EffectViewInfo> dataItem = args.item as BaseTableItem<EffectViewInfo>;
        EffectViewInfo data = dataItem.Data;

        for(int i = 0;i<args.GetNumVisibleColumns();i++)
        {
            Rect colRect = args.GetCellRect(i);
            colRect.xMin += GetContentIndent(dataItem);

            int k = args.GetColumn(i);
            switch(k)
            {
                case 0:
                    EditorGUI.LabelField(colRect, data.name);
                    break;
                case 1:
                    EditorGUI.BeginChangeCheck();
                    data.isWhiteList = EditorGUI.Toggle(colRect, data.isWhiteList);
                    if(EditorGUI.EndChangeCheck())
                    {
                        if(whiteListChanged != null)
                        {
                            whiteListChanged(data);
                        }
                    }
                    break;
                case 2:
                    if (EditorApplication.isPlaying)
                    {
                        EditorGUI.Toggle(colRect, data.isUIEffect);
                    }
                    else
                    {
                        GUI.enabled = false;
                        EditorGUI.Toggle(colRect, data.isUIEffect);
                        GUI.enabled = true;
                    }
                    break;
                case 3:
                    if (!EditorApplication.isPlaying)
                    {
                        GUI.enabled = false;
                    }
                    if (GUI.Button(colRect, "测试"))
                    {
                        if (effectScene != null)
                        {
                            if (effectScene.InRunOneEffect())
                            {
                                EditorUtility.DisplayDialog("提示", "当前正在测试中，请稍后", "确定");
                            }
                            else
                            {
                                effectScene.RunOneEffectWithAllLod(data);
                            }
                        }
                    }
                    GUI.enabled = true;
                    break;
            }
        }
    }

    public static MultiColumnHeaderState CreateDefaultMultiColumnHeaderState()
    {
        MultiColumnHeaderState.Column[] columns = new[]
        {
                new MultiColumnHeaderState.Column
                {
                    headerContent = new GUIContent("特效名"),
                    headerTextAlignment = TextAlignment.Left,
                    sortedAscending = true,
                    sortingArrowAlignment = TextAlignment.Right,
                    width = 200,
                    minWidth = 60,
                    autoResize = false,
                    canSort = false,
                    allowToggleVisibility = false
                },
                new MultiColumnHeaderState.Column
                {
                    headerContent = new GUIContent("白名单"),
                    headerTextAlignment = TextAlignment.Left,
                    sortedAscending = true,
                    sortingArrowAlignment = TextAlignment.Right,
                    width = 60,
                    minWidth = 60,
                    autoResize = false,
                    canSort = false,
                    allowToggleVisibility = false
                },
                new MultiColumnHeaderState.Column
                {
                    headerContent = new GUIContent("UI特效"),
                    headerTextAlignment = TextAlignment.Left,
                    sortedAscending = true,
                    sortingArrowAlignment = TextAlignment.Right,
                    width = 60,
                    minWidth = 60,
                    autoResize = false,
                    canSort = false,
                    allowToggleVisibility = false
                },
                new MultiColumnHeaderState.Column
                {
                    headerContent = new GUIContent("操作"),
                    headerTextAlignment = TextAlignment.Left,
                    sortedAscending = true,
                    sortingArrowAlignment = TextAlignment.Right,
                    width = 60,
                    minWidth = 60,
                    autoResize = false,
                    canSort = false,
                    allowToggleVisibility = false
                },
            };

        MultiColumnHeaderState state = new MultiColumnHeaderState(columns);
        return state;
    }
}
#endif

