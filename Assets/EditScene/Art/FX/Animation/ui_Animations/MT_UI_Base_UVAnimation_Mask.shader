Shader "Mt/UI/Base UVAnimation Mask"
{
    Properties
    {
        _MainTex (" Texture", 2D) = "white" {}
        _UVRotate ("UV Rotate(1:90 2:180 3:270)",Range(0,4)) = 0
        [HDR]_Color1 ("Color1", Color) = (0.5,0.5,0.5,0.5)
        [HDR]_Color2 ("Color2", Color) = (0.5,0.5,0.5,0.5)
		[HDR]_Color3("Color3", Color) = (0.5,0.5,0.5,0.5)
		[HDR]_SpeedColor("Speed Color", Color) = (0.5,0.5,0.5,0.5)
        [HDR]_RimColor ("Rim Color", Color) = (0.5,0.5,0.5,0.5)
		[HDR]_RimColor2 ("Rim Color2", Color) = (0.5,0.5,0.5,0.5)
        _DissolveTex ("Mask", 2D) = "white" {}
        [HideInInspector]_Color("Tint",Color)=(1,1,1,1)
        [HideInInspector]_Mask("Mask Value",Range(-1,1)) = 0
        //_MaskToggle("Mask Toggle(0 : Value; 1 : Custom)",float) = 0.0
        //_SpeedToggle("Speed Toggle",float) = 0.0
        _SpeedX("Horizontal Speed", Range(-5.0, 5.0)) = 0
        _SpeedY("Vertical Speed",Range(-5.0, 5.0)) = 0

        _Alpha("Alpha",Range(0,1))=1
        //_SpeedX2("Mask Horizontal Speed", Range(-5.0, 5.0)) = 0
        //_SpeedY2("Mask Vertical Speed",Range(-5.0, 5.0)) = 0
        [HideInInspector]_NormalFactor("Z Depth Offset",Range(-1.0,1.0)) = 0
        
        //_GradientValue("不要修改！Effect Fade Value",Float) = 1

		[HideInInspector][Toggle]_UIEffect("UI Effect",float) = 0.0
		[HideInInspector]_FadeRange("Fade Range",Range(0,1)) = 1

        [HideInInspector]_StencilComp ("Stencil Comp", Float) = 8
        [HideInInspector]_Stencil ("Stencil Ref", Float) = 0
        [HideInInspector]_StencilOp ("Stencil Pass", Float) = 0
        [HideInInspector]_StencilWriteMask ("Stencil Write Mask", Float) = 255
        [HideInInspector]_StencilReadMask ("Stencil Read Mask", Float) = 255
        _ColorMask ("Color Mask", Float) = 15
        [HideInInspector][Toggle(UNITY_UI_ALPHACLIP)] _UseUIAlphaClip ("Use Alpha Clip", Float) = 0

		// 特效裁切用
		[HideInInspector]_MainTexUVRotation("特效裁切用: MainTex UV旋转值(x:sinθ, y:cosθ)(不要修改!)", Vector) = (0, 1, 0, 0)
		[HideInInspector]_MainTexUVScaleOnCenter("特效裁切用: MainTex UV是否基于中心点缩放(不要修改!)", Float) = 0.0

		[HideInInspector]_DissolveTexUVRotation("特效裁切用: DissolveTex UV旋转值(x:sinθ, y:cosθ)(不要修改!)", Vector) = (0, 1, 0, 0)
		[HideInInspector]_DissolveTexUVScaleOnCenter("特效裁切用: DissolveTex UV是否基于中心点缩放(不要修改!)", Float) = 0.0
		[HideInInspector]_XMin("X Min",float) = -1
		[HideInInspector]_XMax("X Max",float) = 1
		[HideInInspector]_YMin("Y Min",float) = -1
		[HideInInspector]_YMax("Y Max",float) = 1
    }
    Category
    {
        Tags
        {
            "Queue"="Transparent"
            "IgnoreProjector"="True"
            "RenderType"="Transparent"
            "PreviewType"="Plane"
            "CanUseSpriteAtlas"="True"
        }
        Blend SrcAlpha OneMinusSrcAlpha
        Cull Off
        Lighting Off
        ZWrite Off
        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            Pass [_StencilOp]
            ReadMask [_StencilReadMask]
            WriteMask [_StencilWriteMask]
        }

        ZTest [unity_GUIZTestMode]
        ColorMask [_ColorMask]
        SubShader
        {
            CGINCLUDE
            #include "UnityCG.cginc"
            #include "ACGameCG.cginc"
			#include "UnityUI.cginc"
            ENDCG
            
            Pass
			{
                //Blend SrcAlpha OneMinusSrcAlpha
                CGPROGRAM
				float4 _SpeedColor;
                float4 _Color1;
				float4 _Color3;
				//float4 _Color4;
                float4 _RimColor;
				float4 _RimColor2;
                float _Alpha;
                #pragma vertex effectVertexDiss
                #pragma fragment frag
                #pragma fragmentoption ARB_precision_hint_fastest
		        #pragma shader_feature _UIEFFECT_ON
                fixed4 frag (effect_v2f_diss i) : SV_Target
                {
					half2 uv = i.uv.xy;//main
                    half2 uv2 = i.uv1.xy;//dissolve
					fixed4 color = tex2D(_MainTex, uv);
                    //fixed2 mask = tex2D (_DissolveTex, uv2).rg;
					fixed4 mask_var = tex2D(_DissolveTex, uv2).rgba;
					fixed mask = mask_var.r;
					fixed frame1 = mask_var.g;
					fixed frame2 = mask_var.b;
					fixed lerpMask = mask_var.a;
                    fixed4 final_color = lerp(_Color1,_Color2,i.uv1.y)*mask;
					//fixed4 final_color1 = lerp(_Color3, _Color4, i.uv1.x)*mask;
					fixed4 final_color1 = _Color3 * lerpMask;
					final_color.rgb = final_color.rgb + final_color1.rgb;
                    //fixed4 color = fixed4(1,1,1,1);
                    color.rgb = (frame1.rrr*_RimColor.rgb +final_color.rgb + frame2.rrr * _RimColor2.rgb)*i.color.rgb+color.rgb*_SpeedColor.rgb;
                    color.a = saturate(mask - lerp(_Mask, i.uv.w, _MaskToggle) + frame1.r + frame2.r)*_Alpha*i.color.a;
                    return color;
                }
                ENDCG
            }
    //        Pass
    //        {
    //            
    //            CGPROGRAM
    //            float _Alpha;
    //            float4 _Color1;
    //            #pragma vertex effectVertexDiss
    //            #pragma fragment frag
    //            #pragma fragmentoption ARB_precision_hint_fastest
				//#pragma shader_feature _UIEFFECT_ON
    //            fixed4 frag (effect_v2f_diss i) : SV_Target
    //            {
    //                half2 uv = i.uv.xy;
				//    half2 uv2 = i.uv1.xy;
    //                fixed4 color = tex2D (_MainTex, uv);
				//	fixed4 mask_var = tex2D(_DissolveTex, uv2).rgba;
				//	fixed mask = mask_var.r;

    //                
    //                fixed4 final_color = lerp(_Color2,_Color1,i.uv1.y)*mask;
    //                color.rgb *= final_color*i.color.rgb;
    //               

    //                color.a = _Alpha*color.a * i.color.a * saturate(mask - lerp(_Mask,i.uv.w,_MaskToggle)) * _Color.a;
    //                //clip(1-mask-_MaskStep2);
    //                //color.a = saturate(color.a);

    //                #ifdef UNITY_UI_CLIP_RECT
    //                color.a *= UnityGet2DClipping(IN.worldPosition.xy, _ClipRect);
    //                #endif

    //                #ifdef UNITY_UI_ALPHACLIP
    //                clip (color.a - 0.001);
    //                #endif

				//	#ifdef _UIEFFECT_ON
				//	float clipX = i.clipPosition.x;
				//	float clipY = i.clipPosition.y;
				//	float xRange = clamp((clipX - _XMin) / _FadeRange, 0, 1) * clamp((_XMax - clipX) / _FadeRange, 0, 1);
				//	float yRange = clamp((clipY - _YMin) / _FadeRange.z, 0, 1) * clamp((_YMax - clipY) / _FadeRange.w, 0, 1);
				//	float alphaFactor = xRange * yRange;
				//	color.a = color.a * alphaFactor;
				//	#endif

    //               
    //                
    //                //step(shine_col,0.2f);
    //                //color.a *= shine_col.a;
    //                return color;
    //            }
    //            ENDCG
    //        }
            
        }
        
         //SubShader
         //{
         //	Pass
         //	{
         //		SetTexture [_MainTex]
         //		{
         //			constantColor [_TintColor]
         //			combine constant * primary
         //		}
         //		SetTexture [_MainTex]
         //		{
         //			combine texture * previous DOUBLE
         //		}
         //	}
         //}
         //SubShader
         //{
         //	Pass
         //	{
         //		SetTexture [_MainTex]
         //		{
         //			combine texture * primary
         //		}
         //	}
         //}
    }
}
