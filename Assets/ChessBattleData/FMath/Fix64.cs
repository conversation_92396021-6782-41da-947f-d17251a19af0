///////////////////////////////////////////////////////////////////////////////
/// author : taodeng
/// time :   2017-6-22 9:57
/// version: 1.0
///////////////////////////////////////////////////////////////////////////////

using System;

namespace GameFramework.FMath
{
    /// <summary>
    /// 定点数类型
    /// 1.定点数(与数值类型的相互转换int<->fixed,float<->fixed),加减乘除运算(+、-、*、\)
    /// 2.位布局：第63位为符号位(1位)，第47-16位为整数位(32位)，第15-0位为小数位（16位）
    /// 3.小数精度：1/65536 = 0.0000152587890625
    /// 4.可以考虑的优化方式，提供一组针对rawvalue的计算函数，用户可以保存中间值，用于后续的计算。
    /// </summary>
    public partial struct Fix64
    {
        public const int fractionBits = 16;

        public const long fractionFactor = ((long)1 << fractionBits);
        public const long maxRawValue = 0x00007fffffffffff;
		public static Fix64 zero { get { return _zero; } }
        public static Fix64 one { get { return _one; } }
        public static Fix64 maxValue { get { return _maxValue; } }
        public static Fix64 minValue { get { return _minValue; } }
        public static Fix64 e { get { return _e; } }
        public static Fix64 pi { get { return _pi; } }    
        public static Fix64 pi_div_2 { get { return _pi_div_2; } }    
        public static Fix64 epsilon { get { return _epsilon; } }
        public static Fix64 positiveInfinity { get { return _positiveInfinity; } }
        public static Fix64 negativeInfinity { get { return _negativeInfinity; } }
        public static Fix64 radToDeg { get { return _radToDeg; } }
        public static Fix64 degToRad { get { return _degToRad; } }

        public static Fix64 _01 = 0.1.ToFix64();
        public static Fix64 _02 = 0.2.ToFix64();
        public static Fix64 _03 = 0.3.ToFix64();
        public static Fix64 _04 = 0.4.ToFix64();
        public static Fix64 _05 = 0.5.ToFix64();
        public static Fix64 _06 = 0.6.ToFix64();
        public static Fix64 _07 = 0.7.ToFix64();
        public static Fix64 _08 = 0.8.ToFix64();
        public static Fix64 _09 = 0.9.ToFix64();
        public static Fix64 _00001 = 0.00001.ToFix64();
        public static Fix64 _0001 = 0.0001.ToFix64();
        public static Fix64 _001 = 0.001.ToFix64();
        public static Fix64 _10 = 10.ToFix64();
        public static Fix64 _90 = 90.ToFix64();
        public static Fix64 _180 = 180.ToFix64();
        public static Fix64 _210 = 210.ToFix64();
        public static Fix64 _360 = 360.ToFix64();
        public static Fix64 _100 = 100.ToFix64();
        public static Fix64 _1000 = 1000.ToFix64();
        public static Fix64 _10000 = 10000.ToFix64();
        /// <summary>
        /// 定点数精度
        /// </summary>
        public static Fix64 mini = FromRawValue(0x0000000000000001);
        //public const long zero = 0;
        //public const long one = fractionFactor;
        //public const long maxValue = 0x00007fffffffffff;
        //public const long minValue = -maxValue;
        //public const long e = 0x000000000002b7e1;
        //public const long pi = 0x000000000003243f;
        //public const long half_pi = pi >> 1;
        //public const long twoPi = pi << 1;
        //public const long epsilon = 1;
        //const long _degToRad = 0x0000000000000477;
        //const long _radToDeg = 0x0000000000394bbf;

        public static Fix64 DegToRad(Fix64 angle)
        {
            return angle / _radToDeg;
        }

        public static Fix64 RadToDeg(Fix64 angle)
        {
            return angle / _degToRad;
        }

        //public static long RadToDeg(long angle)
        //{
        //    return Div(angle, degToRad);
        //}

        static Fix64 _zero = FromInt(0);
        static Fix64 _one = FromInt(1);
        static Fix64 _maxValue = FromRawValue(maxRawValue);
		
		static Fix64 _minValue = FromRawValue(-maxRawValue);
        static Fix64 _e = FromRawValue(0x000000000002b7e1);//FromDouble(2.7182818284590451);
        static Fix64 _pi = FromRawValue(0x000000000003243f);//FromDouble(3.1415926535897931);   
        static Fix64 _pi_div_2 = FromRawValue(0x000000000001921f);
        static Fix64 _positiveInfinity = FromRawValue(long.MaxValue);
        static Fix64 _negativeInfinity = FromRawValue(long.MinValue);
        static Fix64 _epsilon = FromRawValue(1);
        static Fix64 _degToRad = FromRawValue(0x0000000000000477);
        static Fix64 _radToDeg = FromRawValue(0x0000000000394bbf);

        public long rawValue { get { return mRawValue; } }
        private long mRawValue;

        private static long PI_RAW = pi.rawValue;
        private static long PI_RAW_MULTIPLY_2 = PI_RAW * 2;
        private static long PI_RAW_DIV_2 = PI_RAW / 2;

        public bool isInfinity
        {
            get
            {
                return mRawValue == _positiveInfinity.mRawValue || 
                       mRawValue == _negativeInfinity.mRawValue;
            }
        }

        public bool isPositiveInfinity
        {
            get
            {
                return mRawValue == _positiveInfinity.mRawValue;
            }
        }

        public bool isNegativeInfinity
        {
            get
            {
                return mRawValue == _negativeInfinity.mRawValue;
            }
        }

        public static Fix64 FromInt(int val)
        {
            return new Fix64(val);
        }

        //public static long FromInt(int val)
        //{
        //    return val * fractionFactor;
        //}

        public static Fix64 FromSingle(float val)
        {
            return new Fix64(val);
        }

        //public static long FromSingle(float val)
        //{
        //    return (long)(val * fractionFactor);
        //}

        public static Fix64 FromDouble(double val)
        {
            return new Fix64(val);
        }

        //public static long FromDouble(double val)
        //{
        //    return (long)(val * fractionFactor);
        //}

        public double ToDouble()
        {
            return (integer + fraction / (double)(fractionFactor));
        }

        public float ToSingle()
        {
            return (integer + fraction / (float)(fractionFactor));
        }

        //public static double ToDouble(long val)
        //{
        //    return (GetInteger(val) + GetFraction(val) / (double)(fractionFactor));
        //}

        public Fix64(int val)
        {
            mRawValue = val * fractionFactor;
		}

		public static Fix64 FromRawValue(long rawValue)
        {
            Fix64 val = new Fix64();
            val.mRawValue = rawValue;

            return val;
        }

        public static void SetRawValue(ref Fix64 fix64, long rawValue)
        {
            fix64.mRawValue = rawValue;
        }

        public static void SetIntValue(ref Fix64 fix64, int value)
        {
            fix64.mRawValue = value * fractionFactor;
        }

        public static void SetFloatValue(ref Fix64 fix64, float value)
        {
            SetDoubleValue(ref fix64, value);
        }
        
        public static void SetDoubleValue(ref Fix64 fix64, double value)
        {
            fix64.mRawValue = (long)(value * fractionFactor);
// #if UNITY_EDITOR
//             if (System.Math.Abs(value) > Int32.MaxValue)
//             {
//                 TKFrame.Diagnostic.Error("---Exceed range, val: {0}", value);
//             }
// #endif
        }

        public static Fix64 FromComponents(long i, long f)
        {
            return FromRawValue(i * fractionFactor + f);
        }

        public long integer
        {
            get { return rawValue / fractionFactor; }
        }

        //static long GetInteger(long rawValue)
        //{
        //    return rawValue / fractionFactor;
        //}

        //public static long GetInteger(long rawValue, long fraction)
        //{
        //    return (rawValue - fraction) >> fractionBits;
        //}

        public long fraction
        {
            get
            {
                return rawValue % fractionFactor;
            }
        }

        public void GetIntegerAndFraction(out long integer, out long fraction)
        {
            fraction = rawValue % fractionFactor;
            integer = (rawValue - fraction) >> fractionBits;
        }

        //public static long GetFraction(long rawValue)
        //{
        //    return (rawValue % fractionFactor);
        //}
		
        public Fix64(double val)
        {
            mRawValue = (long)(val * fractionFactor);
// #if UNITY_EDITOR
// 			if (System.Math.Abs(val) > Int32.MaxValue)
// 			{
// 				TKFrame.Diagnostic.Error("---Exceed range, val: {0}", val);
// 			}
// #endif
		}

		//public static implicit operator Fix64(int val)
		//{
		//    return new Fix64(val);
		//}

		//public static implicit operator Fix64(float val)
		//{
		//    return new Fix64(val);
		//}

		//public static implicit operator Fix64(double val)
		//{
		//    return new Fix64(val);
		//}

		//public static implicit operator Fix64(bool val)
		//{
		//    return val ? Fix64.one : Fix64.zero;
		//}

		public static Fix64 operator -(Fix64 a)
        {
            return FromRawValue(-a.mRawValue);
        }

        public static Fix64 operator +(Fix64 a, Fix64 b)
        {
            return FromRawValue(a.mRawValue + b.mRawValue);
        }

        public static Fix64 operator -(Fix64 a, Fix64 b)
        {
            return FromRawValue(a.mRawValue - b.mRawValue);
        }

        public static bool operator ==(Fix64 a, Fix64 b)
        {
            return a.mRawValue == b.mRawValue;
        }

        public static bool operator !=(Fix64 a, Fix64 b)
        {
            return a.mRawValue != b.mRawValue;
        }

        public static bool operator >(Fix64 a, Fix64 b)
        {
            return a.mRawValue > b.mRawValue;
        }

        //public static bool operator >(Fix64 a, long b)
        //{
        //    return a.mRawValue > b;
        //}

        public static bool operator >=(Fix64 a, Fix64 b)
        {
            return a.mRawValue >= b.mRawValue;
        }

        //public static bool operator >=(Fix64 a, long b)
        //{
        //    return a.mRawValue >= b;
        //}

        public static bool operator <(Fix64 a, Fix64 b)
        {
            return a.mRawValue < b.mRawValue;
        }

        public static bool operator <=(Fix64 a, Fix64 b)
        {
            return a.mRawValue <= b.mRawValue;
        }

        //public static bool operator <(Fix64 a, long b)
        //{
        //    return a.mRawValue < b;
        //}

        //public static bool operator <=(Fix64 a, long b)
        //{
        //    return a.mRawValue <= b;
        //}

        public override bool Equals(object obj)
        {            
            return mRawValue == ((Fix64)obj).mRawValue;
        }

        public static Fix64 operator *(Fix64 a, Fix64 b)
        {
            long f1 = a.mRawValue % fractionFactor; 
            long f2 = b.mRawValue % fractionFactor;

            long i1 = (a.mRawValue - f1) >> fractionBits; 
            long i2 = (b.mRawValue - f2) >> fractionBits;

            long r1 = ((a.mRawValue * i2)) + i1 * f2 + ((f1 * f2) / fractionFactor);

            return FromRawValue(r1);
        }

        public static Fix64 operator *(Fix64 a, int b)
        {
            return FromRawValue(a.mRawValue * b);
        }

        public static Fix64 operator *(int a, Fix64 b)
        {
            return FromRawValue(a * b.mRawValue);
        }
        
        

        /// <summary>
        /// 用于特殊优化计算.
        /// 用户可以自行保存小数部分和整数部分，以此不用每次都计算小数和整数。
        /// </summary>
        /// <param name="i1">第1个操作数的整数部分</param>
        /// <param name="f1">第1个操作数的小数部分</param>
        /// <param name="i2">第2个操作数的整数部分</param>
        /// <param name="f2">第2个操作数的小数部分</param>
        /// <returns></returns>
        //public static long Mul(long i1, long f1, long i2, long f2)
        //{
        //    return ((i1 * i2) << fractionBits) + i1 * f2 + i2 * f1 + ((f1 * f2) / fractionFactor);
        //}

        /// <summary>
        /// 用于特殊优化计算.
        /// 用户可以自行保存小数部分和整数部分，以此不用每次都计算小数和整数。
        /// </summary>
        /// <param name="i1">第1个操作数的整数部分</param>
        /// <param name="f1">第1个操作数的小数部分</param>
        /// <param name="i2">第2个操作数的整数部分</param>
        /// <param name="f2">第2个操作数的小数部分</param>
        /// <returns></returns>
        //public static long Mul(long a, long b)
        //{
        //    long f1 = a % fractionFactor; 
        //    long f2 = b % fractionFactor;
        //    long i1 = (a - f1) >> fractionBits; 
        //    long i2 = (b - f2) >> fractionBits;

        //    return ((a * i2)) + i1 * f2 + ((f1 * f2) / fractionFactor);
        //}

        /// <summary>
        /// 用于特殊优化计算.
        /// 用户可以自行保存小数部分和整数部分，以此不用每次都计算小数和整数。
        /// </summary>
        /// <param name="i1">第1个操作数的整数部分</param>
        /// <param name="f1">第1个操作数的小数部分</param>
        /// <param name="i2">第2个操作数的整数部分</param>
        /// <param name="f2">第2个操作数的小数部分</param>
        /// <returns></returns>
        //public static long MulInteger(long a, int b)
        //{
        //    return a * b;
        //}

        public static Fix64 operator /(Fix64 a, Fix64 b)
        {
            if (b.mRawValue == 0)
            {
                b.mRawValue = 1;
            }

            return FromRawValue((a.mRawValue << fractionBits) / b.mRawValue);
        }

        public static Fix64 operator /(Fix64 a, int b)
        {
            return FromRawValue(a.mRawValue / b);
        }

        ///// <summary>
        ///// a/b
        ///// </summary>
        ///// <param name="rawValueA"></param>
        ///// <param name="rawValueB"></param>
        ///// <returns></returns>
        //public static long Div(long rawValueA, long rawValueB)
        //{
        //    return (rawValueA << fractionBits) / rawValueB;
        //}

        //public static long DivInteger(long rawValueA, int i)
        //{
        //    return rawValueA / i;
        //}

        //public override string ToString()
        //{
        //    return (integer + fraction / (double)(fractionFactor)).ToString();
        //}

        public override string ToString()
        {
            return (integer + fraction / (double)(fractionFactor)).ToString();
        }

        public override int GetHashCode()
        {
            return (int)mRawValue;
        }

        public static Fix64 Max(Fix64 a, Fix64 b)
        {
            return a >= b ? a : b;
        }

        //public static long Max(long a, long b)
        //{
        //    return a >= b ? a : b;
        //}

        public static Fix64 Min(Fix64 a, Fix64 b)
        {
            return a <= b ? a : b;
        }

        //public static long Min(long a, long b)
        //{
        //    return a <= b ? a : b;
        //}

        public static Fix64 Lerp(Fix64 a, Fix64 b, Fix64 t)
        {
            return a + (b - a) * t;
        }

        //public static long Lerp(long a, long b, long t)
        //{
        //    return a + Mul((one - t), b);
        //}

        public int Sign()
        {
            return Sign(mRawValue);
        }

        static int Sign(long rawValue)
        {
            uint k = (uint)(((ulong)(rawValue)) >> 63);
            k ^= 1;

            return (int)(k << 1) - 1;
        }

        public Fix64 Abs()
        {
            return FromRawValue(Abs(mRawValue));
        }

        static long Abs(long rawValue)
        {
            //long temp = rawValue >> 63;
            //return (rawValue + temp) ^ temp;

            return System.Math.Abs(rawValue);

        }

        public Fix64 Sqrt()
        {
            return Fix64.FromRawValue(Sqrt(this.rawValue));
        }

        /// <summary>
        /// 64位定点数开平方——用牛顿迭代法实现.
        /// </summary>
        /// <param name="rawValue"></param>
        /// <returns></returns>
        /// <remarks>
        /// sample_count = 1000000
        /// TestNewtonSqrt_Pref elapsedTime = 106471
        /// TestSqrt_Pref elapsedTime = 8140
        /// TestNewtonSqrtLong_Pref elapsedTime = 231810
        /// TestFastSqrt_Pref elapsedTime = 50450
        /// 
        /// 结论：
        ///  1.系统库sqrt性能是NewtonSqrtLong的25倍左右
        ///  2.系统库sqrt性能是fastsqrt的5倍  
        ///  3.浮点数由于其存储结构，可以迅速找到数量级上的平方数.
        ///  
        /// double / double
        /// 00FB4414  movsd       xmm0,mmword ptr [eax+0FBE168h]  
        /// 00FB441C  divsd       xmm0, mmword ptr[ecx + 0FBE168h]
        /// long / long
        /// 00FB44B1  mov         ecx, dword ptr samples(0FBE158h)[ecx]
        /// 00FB44B7  mov         eax, dword ptr samples(0FBE158h)[eax]
        /// 00FB44BD  cdq
        /// 00FB44BE  idiv        eax, ecx
        /// 00FB44C0  cdq

        /// int64/int64
        /// 000544CD  call        __alldiv (051352h)  
        /// </remarks>
        ///
        static long Sqrt(long rawValue)
        {
            if (rawValue > maxRawValue)
            { 
                long last_res = 0;
                long y = rawValue;//<< fractionBits ;
                long res = rawValue >> 1;      // 初始值取x/2,可以提升20-25%左右的性能（rawValue为正数）
                long diff = 0;

                if (res != 0)
                {
                    do
                    {
                        last_res = res;
                        res = (res + y / res) >> 1;
                        diff = last_res - res;
                    }
                    while (diff > 1 || diff < -1);
                }

                return res << 8;
            }
            else if (rawValue > 0)
            {
                long last_res = 0;
                long y = rawValue << fractionBits ;
                long res = rawValue >> 1;      // 初始值取x/2,可以提升20-25%左右的性能（rawValue为正数）
                long diff = 0;

                if (res != 0)
                {
                    do
                    {
                        last_res = res;
                        res = (res + y / res) >> 1;
                        diff = last_res - res;
                    }
                    while (diff > 1 || diff < -1);
                }

                return res;
            }
            else
            {
                return 0;
            }
        }

        /// <summary>
        /// 指定初始进行运算
        /// </summary>
        /// <param name="rawValue"></param>
        /// <param name="x0"></param>
        /// <returns></returns>
        static long Sqrt(long rawValue, long x0)
        {
            if (rawValue > 0)
            {
                long last_res = 0;
                long y = rawValue;// << fractionBits;
                long res = x0;      // 初始值为外部指定的数值   
                long diff = 0;

                do
                {
                    last_res = res;
                    res = (res + y / res) >> 1;
                    diff = last_res - res;
                }
                while (diff > 1 || diff < -1);

                return res << 8;
            }
            else
            {
                return 0;
            }
        }

        /// <summary>
        /// 增加一个counter，用于记录迭代的次数，该函数一般用于性能测试。
        /// sqrt性能优化的方向:
        /// 用最小的成本确定初始值，使得初始值距离真实集尽量接近,
        /// Quake3的做法是利用了浮点数表示法中，指数除2，使得初始解与真实解在数量级上一致，
        /// 后续再做一次牛顿迭代法，即可得到相对精确的解，如果需要增大精度，只需要多迭代几次。
        /// </summary>
        /// <param name="rawValue"></param>
        /// <param name="counter"></param>
        /// <returns></returns>
        public static long Sqrt(long rawValue, out int counter)
        {
            counter = 0;
            
            if (rawValue > 0)
            {
                long last_res = 0;
                long y = rawValue; //<< fractionBits;
                long res = y >> 1;      // 初始值取x/2,可以提升20-25%左右的性能（rawValue为正数）
                long diff = 0;

                do
                {
                    ++counter;
                    last_res = res;
                    res = (res + y / res) >> 1;
                    diff = last_res - res;
                }
                while (diff > 1 || diff < -1);

                return res << 8;
            }
            else
            {
                return 0;
            }
        }

        public static long Sqrt(long rawValue, long x0, out int counter)
        {
            counter = 0;

            if (rawValue > 0)
            {
                long last_res = 0;
                long y = rawValue;//<< fractionBits;
                long res = x0;      // 初始值为外部指定的数值   
                long diff = 0;

                do
                {
                    ++counter;
                    last_res = res;
                    res = (res + y / res) >> 1;
                    diff = last_res - res;
                }
                while (diff > 1 || diff < -1);

                return res << 8;
            }
            else
            {
                return 0;
            }
        }

        public static Fix64 Repeat(Fix64 t, Fix64 length)
        {
            return t - length * (t / length).Floor();
        }

        public int Floor()
        {
            // positive
            if (mRawValue >= 0)
            {
                return (int)integer;
            }
            else // negative
            {
                long i, f;
                GetIntegerAndFraction(out i, out f);

                if (f != 0) // f < 0
                {
                    return (int)(i - 1);
                }
                else
                {
                    return (int)i;
                }
            }
        }

		public int Ceiling()
		{
			// positive
			if (mRawValue >= 0)
			{
				long i, f;
				GetIntegerAndFraction(out i, out f);

				if (f == 0) 
				{
					return (int)i;
				}
				else // f > 0
				{

					return (int)i+1;
				}
			}
			else // negative
			{
				return (int)integer;
			}
		}
        
        public int Round()
        {
            long i, f;
            //小数的绝对值
            f = rawValue & (fractionFactor - 1);
            
            // positive
            if (mRawValue >= 0)
            {
                i = (rawValue - f) >> fractionBits;

                if (f < _05.rawValue || (f == _05.rawValue && ( mRawValue & fractionFactor) == 0))
                {
                    return (int) i;
                }
                else
                {
                    return (int)i+1;
                }
            }
            else // negative
            {
                if (f > _05.rawValue || f == 0 || (f == _05.rawValue && ( (-mRawValue) & fractionFactor) == 0))
                {
                    return (int) integer;
                }
                else
                {
                    return (int) integer - 1;
                }
            }
        }

        public static Fix64 Clamp(Fix64 value, Fix64 minValue, Fix64 maxValue)
        {
            if (value > maxValue)
                value = maxValue;
            if (value < minValue)
                value = minValue;

            return value;
        }

		// power函数暂不实现
		////public Fix64 Pow(Fix64 power)
		////{

		////}

		////public long Pow(long rawValue)
		////{

		////}

		////public Fix64 Exp();

		//public static long Square(long rawValue)
		//{
		//    long f = GetFraction(rawValue);
		//    long i = GetInteger(rawValue, f);

		//    return Mul(i, f, i, f);
		//}

		//public Fix64 Square()
		//{
		//    return FromRawValue(Square(mRawValue));
		//}
        #region 计算优化
        /// <summary>
        /// 加等 a+= b
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        public static void Plus_Equal(ref Fix64 a, ref Fix64 b)
        {
            a.mRawValue += b.mRawValue;
        }

        public static bool Equals_Ref(ref Fix64 a, ref Fix64 b)
        {
            return a.mRawValue == b.mRawValue;
        }

        /// <summary>
        /// 减等 a-=b
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        public static void Dec_Equal(ref Fix64 a, ref Fix64 b)
        {
            a.mRawValue -= b.mRawValue;
        }
        
        /// <summary>
        /// 乘等 a*= b
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        public static void Multiply_Equal(ref Fix64 a, ref Fix64 b)
        {
            long f1 = a.mRawValue % fractionFactor; 
            long f2 = b.mRawValue % fractionFactor;

            long i1 = (a.mRawValue - f1) >> fractionBits; 
            long i2 = (b.mRawValue - f2) >> fractionBits;

            a.mRawValue = ((a.mRawValue * i2)) + i1 * f2 + ((f1 * f2) / fractionFactor);
        }
        
        /// <summary>
        /// 乘等 a*= b
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        public static void Multiply_Equal(ref Fix64 a, int b)
        {
            a.mRawValue *= b;
        }
        
        /// <summary>
        /// 除等于 a/=b
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        public static void Divide_Equal(ref Fix64 a, ref Fix64 b)
        {
            if (b.mRawValue == 0)
            {
                b.mRawValue = 1;
            }

            a.mRawValue = (a.mRawValue << fractionBits) / b.mRawValue;
        }
        
        /// <summary>
        /// 除等于 a/=b
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        public static void Divide_Equal(ref Fix64 a, int b)
        {
            a.mRawValue /= b;
        }
        
        /// <summary>
        /// 取负数
        /// </summary>
        /// <param name="a"></param>
        public static void Negative(ref Fix64 a)
        {
            a.mRawValue = -a.mRawValue;
        }
        
        #endregion
	}




}

