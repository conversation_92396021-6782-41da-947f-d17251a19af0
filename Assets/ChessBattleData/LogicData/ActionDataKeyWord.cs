//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Lucifer.ActCore
{
    
    //4 getter
    public enum GenericType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("空")]
        Enum_nothing,
        [EnumNameAttribute("巴德233层彩蛋, s3.5-getter")]
        Enum_Get_BuddleS3_Effect,

        Enum_2,

        Enum_3,

        Enum_4,

        Enum_5,

        Enum_6,
        
        Enum_7,
        [EnumNameAttribute("字符串拼接-getter")]
        Enum_StringAppend,
        [EnumNameAttribute("飓风掉落-s9-执行")]
        Enum_Do_Hurricane_Drop,
        Enum_1,
        [EnumNameAttribute("船长转弯-s9-getter")]
        Enum_s9_Ganplank_ship_angle,
    }
    //4 execute
    public enum ExecuteGenericType
    {
        Enum_NULL,
        
        [EnumNameAttribute("空")]
        Enum_nothing,
       
        Enum_1,

        Enum_2,

        Enum_3,

        Enum_4,

        Enum_5,

        Enum_6,
        
        Enum_7,
        [EnumNameAttribute("飓风掉落-s9-执行")]
        Enum_Do_Hurricane_Drop,
        [EnumNameAttribute("金币掉落-执行")]
        Enum_Do_Coin_Drop,
        [EnumNameAttribute("法球掉落-执行")]
        Enum_Do_Drop,
    }
    //4 condition
    public enum ConditionGenericType
    {
        Enum_NULL,
        
        [EnumNameAttribute("空")]
        Enum_nothing,
        
        [EnumNameAttribute("随机一次，获取是否普攻暴击")]
        Enum_CheckHeroAttackCrit,

        Enum_1 = 100,

        Enum_2,

        Enum_3,

        Enum_4,

        Enum_5,

        Enum_6,
        
        Enum_7,
     
    }
    
   
    
    public enum GenericViewType
    {
        
        Enum_NULL,
        [EnumNameAttribute("空")]
        Enum_nothing,
        Enum_1,

        Enum_2,

        Enum_3,

        Enum_4,

        Enum_5,

        Enum_6,
        
        Enum_7,
        [EnumNameAttribute("角色表现-只留模型显示")]
        Enum_CharacterUnitCommandDeadButNotHide = 9999,
        [EnumNameAttribute("角色表现-更新位置旋转")]
        Enum_CharacterUnitCommandUpdateTransform ,
        
    }
    
    public enum GetGridUnitGridType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("外圈通过攻击距离排序找格子")]
        Enum_EmptyUnitByOutRange,
        [EnumNameAttribute("自己的格子")]
        Enum_SelfGrid,
        [EnumNameAttribute("自己位置的格子")]
        Enum_SelfPosGrid,
    }
    
    public enum GetSummonType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("第一个")]
        Enum_One,
        
        [EnumNameAttribute("最后一个")]
        Enum_Last,
        
        [EnumNameAttribute("随机一个")]
        Enum_RandomOne,
        
        [EnumNameAttribute("所有")]
        Enum_All,
        
        [EnumNameAttribute("个数")]
        Enum_Count,
    }
    
    public enum CheckSummonStateCompareCondition
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("大于")]
        Enum_Greater,
        
        [EnumNameAttribute("大于等于")]
        Enum_GreaterOrEqual,
        
        [EnumNameAttribute("等于")]
        Enum_Equal,
        
        [EnumNameAttribute("小于等于")]
        Enum_LessOrEqual,
        
        [EnumNameAttribute("小于")]
        Enum_Less,
    }
    
    public enum CheckSummonStateType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("是否死亡")]
        Enum_Death,
        
        [EnumNameAttribute("是否存活")]
        Enum_Alive,
        
        [EnumNameAttribute("存活个数")]
        Enum_AliveCount,
    }
    
    public enum GetProjectileType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("最后一个")]
        Enum_ONE,
        
        [EnumNameAttribute("第一个")]
        Enum_FIRST_ONE,
        
        [EnumNameAttribute("全部")]
        Enum_ALL,
    }
    
    public enum GetActiveFetterDataType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("羁绊等级")]
        Enum_Level,
        
        [EnumNameAttribute("羁绊数量")]
        Enum_Number,
    }
    
    public enum GetRoleSpeedSpeedType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("普攻速度")]
        Enum_normal_attack,
    }
    
    public enum FindTargetSortType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("距离排序")]
        Enum_nothing,
        
        [EnumNameAttribute("受击时间排序")]
        Enum_hurt_time,
        
        [EnumNameAttribute("随机排序")]
        Enum_random,
    }
    
    public enum GetCellPosDirectionType2
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("前方")]
        Enum_front,
        
        [EnumNameAttribute("后方")]
        Enum_back,
    }
    
    public enum GetCellPosDirectionType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("绝对位置")]
        Enum_default,
        
        [EnumNameAttribute("自己的方向")]
        Enum_self_direction,
        
        [EnumNameAttribute("目标的方向")]
        Enum_target_direction,
        
        [EnumNameAttribute("连线的方向")]
        Enum_self_to_target,
    }
    
    public enum GetCellPosTargetPos
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("目标处")]
        Enum_None,
        
        [EnumNameAttribute("左")]
        Enum_Left,
        
        [EnumNameAttribute("左上")]
        Enum_Left_Top,
        
        [EnumNameAttribute("右上")]
        Enum_Right_Top,
        
        [EnumNameAttribute("右")]
        Enum_Right,
        
        [EnumNameAttribute("右下")]
        Enum_Right_Bottom,
        
        [EnumNameAttribute("左下")]
        Enum_Left_Bottom,
    }
    
    public enum GetSkillTargetFindType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("随机")]
        Enum_random,
        
        [EnumNameAttribute("优先背后(找完一个方向，才找另外一个方向)")]
        Enum_backwardPriority,
        
        [EnumNameAttribute("优先前方(找完一个方向，才找另外一个方向)")]
        Enum_forwardPriority,
        
        [EnumNameAttribute("仅背后")]
        Enum_backwardOnly,
        
        [EnumNameAttribute("仅前方")]
        Enum_forwardOnly,
        
        [EnumNameAttribute("按圈优先前方")]
        Enum_circleForwardPriority,
        
        [EnumNameAttribute("按圈优先后方")]
        Enum_circleBackwardPriority,
    }
    
    public enum CalculateVectorValueType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("vec和vec")]
        Enum_vector3,
        
        [EnumNameAttribute("vec和浮点")]
        Enum_fixed,
        
        [EnumNameAttribute("vec和vec 范围")]
        Enum_random,
        
        [EnumNameAttribute("vec 旋转 角度")]
        Enum_vector3_rotation,
        
        [EnumNameAttribute("vec 计算朝向 ")]
        Enum_vector3_lookDirection,


    }
    
    public enum GetSkillExcelTimeType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("施法时间")]
        Enum_CastTime,
        
        [EnumNameAttribute("蓄力时间")]
        Enum_HoldTime,
    }
    
    public enum GetVariableValueEffectTarget
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("自己")]
        Enum_self,
        
        [EnumNameAttribute("拥有者")]
        Enum_owner,
        
        [EnumNameAttribute("自定义")]
        Enum_custom,
    }
    
    public enum GetCountReturnType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("总个数")]
        Enum_totalCount,
        
        [EnumNameAttribute("迭代个数")]
        Enum_ienumeratorCount,
    }
    
    public enum CheckSkillGrabStateType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("未命中")]
        Enum_miss,
        
        [EnumNameAttribute("失败")]
        Enum_fail,
        
        [EnumNameAttribute("成功")]
        Enum_success,
    }
    
    public enum NormalAttackNotifyType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("攻击时")]
        Enum_attacking,
        
        [EnumNameAttribute("攻击完成后")]
        Enum_complete,
        
        [EnumNameAttribute("普攻结束")]
        Enum_attack_end,
    }
    
    public enum DoublecastTriggerNotifySelectType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("重选")]
        Enum_reSelect,
        
        [EnumNameAttribute("原有目标")]
        Enum_oldTarget,
        
        [EnumNameAttribute("排除原有目标，重选")]
        Enum_exclude_oldTarget,
    }
    
    public enum SkillCostPointCostType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("正常释放")]
        Enum_normal,
        
        [EnumNameAttribute("被打断")]
        Enum_break,
    }
    
    public enum ChangeColliderSelfColliderType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("无阻挡")]
        Enum_none,
        
        [EnumNameAttribute("怪物阻挡")]
        Enum_monster,
        
        [EnumNameAttribute("建筑阻挡")]
        Enum_building,
    }
    
    public enum CheckTimeSource
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("播放时间")]
        Enum_actionTime,
    }
    
    public enum EffectTweenControlEffect
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("黑屏")]
        Enum_BlackScreen,
        
        [EnumNameAttribute("白屏")]
        Enum_WhiteScreen,
        
        [EnumNameAttribute("时速")]
        Enum_TimeScale,
    }
    
    public enum BulletTimeTargetType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("敌方")]
        Enum_Enemy,
    }
    
    public enum ActionSendEventName
    {
        
        Enum_NULL,
        
        Enum_Nothing,
        
        [EnumNameAttribute("巴德添加木灵")]
        Enum_Bard_Add_Muling,
        
        [EnumNameAttribute("巴德显示木灵")]
        Enum_Bard_Show_Muling,
        
        [EnumNameAttribute("巴德死亡时解锁木灵位置，防止木灵是0的情况")]
        Enum_Bard_Unlock_Muling_Pos,
         
        [EnumNameAttribute("修改技能描述(参数1:Skill表Id)")]
        Enum_Change_Skill_Desc,
         
        [EnumNameAttribute("自定义事件")]
        Enum_Custom = 999,
    }
    
    public enum EventActionDataType
    {
        
        Enum_NULL,
        
        Enum_Nothing,
    }
    
    public enum HitGroupControlControlType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("打开")]
        Enum_Enable,
        
        [EnumNameAttribute("关闭")]
        Enum_Disable,
    }
    
    public enum HitGroupControlGroupType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("攻击框")]
        Enum_Attack,
        
        [EnumNameAttribute("受击框")]
        Enum_Bear,
    }
    
    public enum ExecuteHitHitTarget
    {
        
        Enum_NULL,
        
        Enum_self,
        
        Enum_target,
    }
    
    public enum ThrowTargetTypeName
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("无")]
        Enum_Nothing,
        
        [EnumNameAttribute("抛物线追踪【旧】")]
        Enum_TrackWithCurve,
        
        [EnumNameAttribute("抛物线throw【新】")]
        Enum_ThrowCurve,
        
        [EnumNameAttribute("直线")]
        Enum_Linear,
    }
    
    public enum ThrowTargetName
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("抓取目标")]
        Enum_grabtargets,
        
        [EnumNameAttribute("普攻目标")]
        Enum_targets,
        
        [EnumNameAttribute("技能目标")]
        Enum_skillTargets,
        
        [EnumNameAttribute("拥有者")]
        Enum_owner,
        
        [EnumNameAttribute("召唤物")]
        Enum_Summon,

        [EnumNameAttribute("召唤者")]
        Enum_Summoner,

        [EnumNameAttribute("自定义")]
        Enum_custom,
    }
    
    public enum TurnRoundTurnType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("固定角度")]
        Enum_custom,
    }
    
    public enum CameraCustomSettingWithAnimationRelativeNode
    {
        
        Enum_NULL,
        
        Enum_Default,
        
        Enum_DataSelect,
    }
    
    public enum SetAnimatorVParameterValueSource
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("输入")]
        Enum_userinput,
        
        [EnumNameAttribute("animator的值")]
        Enum_fromanimator,
    }
    
    public enum SetVariableValueAdvanceEffectTarget
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("自己")]
        Enum_self,
        
        [EnumNameAttribute("拥有者")]
        Enum_owner, 
        
        [EnumNameAttribute("自定义")]
        Enum_custom,
    }
    
    public enum AddViewMotionModityType
    {
        
        Enum_NULL,
        
        Enum_Modify,
        
        Enum_Destroy,
        
        Enum_Resume,
    }
    
    public enum AddViewMotionMotionType
    {
        
        Enum_NULL,
        [EnumNameAttribute("努努吃")]
        Enum_Swallow,
        [EnumNameAttribute("灵风")]
        Enum_Zephyr,
        [EnumNameAttribute("恢复正常")]
        Enum_BackNormal,
        [EnumNameAttribute("地底效果")]
        Enum_UnderGround,
        [EnumNameAttribute("地底效果恢复")]
        Enum_UnderGroundToGround,
        [EnumNameAttribute("set11瑟提效果sb")]
        Enum_Set11Sett3StarEffect,
        [EnumNameAttribute("留空0")]
        Enum_Blank0 = 10000,
        [EnumNameAttribute("留空1")]
        Enum_Blank1,
        [EnumNameAttribute("留空2")]
        Enum_Blank2,
        [EnumNameAttribute("留空3")]
        Enum_Blank3,
        [EnumNameAttribute("留空4")]
        Enum_Blank4,
        [EnumNameAttribute("留空5")]
        Enum_Blank5,
        
    }
    
    public enum AddMoveMotionMoveDirection
    {
        
        Enum_NULL,
        
        Enum_LEFT,
        
        Enum_RIGHT,
        
        Enum_FORWARD,
        
        Enum_BACK,
        
        Enum_BYMOVESTATE,
        
        Enum_NOTHING,
    }
    
    public enum FlyToTargetAdvanceChangeType
    {
        
        Enum_NULL,
        
        Enum_NONE,
        
        Enum_PERCENT,
    }
    
    public enum AutoChangeDirectionAttackNode
    {
        
        Enum_NULL,
        
        Enum_Default,
        
        Enum_Head,
        
        Enum_Neck,
        
        Enum_HandRight,
        
        Enum_HandLeft,
        
        Enum_FootRight,
        
        Enum_FootLeft,
    }
    
    public enum MoveWithAMTRelativeNode
    {
        
        Enum_NULL,
        
        Enum_Default,
        
        Enum_DataSelect,
    }
    
    public enum MoveToTargetSpeedType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("输入")]
        Enum_CustomInput,
        
        [EnumNameAttribute("计算")]
        Enum_Calculate,
        
        [EnumNameAttribute("计算-带action速度")]
        Enum_CalculateWithSpeed,
    }
    
    public enum MoveToTargetTargetPos
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("目标处")]
        Enum_nothing,
        
        [EnumNameAttribute("前方")]
        Enum_front,
        
        [EnumNameAttribute("后方")]
        Enum_back,
        
        [EnumNameAttribute("连线的前方")]
        Enum_self_to_target_front,
        
        [EnumNameAttribute("连线的后方")]
        Enum_self_to_target_back,
        
        [EnumNameAttribute("周边")]
        Enum_round,
        
        [EnumNameAttribute("背后或当前位置")]
        Enum_special_back,
    }
    
    public enum MoveToTargetSelectTarget
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("(技能)目标")]
        Enum_targets,
        
        [EnumNameAttribute("目标格子")]
        Enum_targetGrid,
        
        [EnumNameAttribute("(普攻)目标")]
        Enum_targetsAttack,
        
        [EnumNameAttribute("目标模型（只在目标选定为目标处有效）")]
        Enum_targetsTransform,
        
        [EnumNameAttribute("自定义link")]
        Enum_custom,
        
        [EnumNameAttribute("目标格子顶点")]
        Enum_targetGridVertex,
    }
    
    public enum ResetActionStateType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("正常状态")]
        Enum_STAND,
        
        [EnumNameAttribute("死亡状态 - 自杀")]
        Enum_DEAD,
        
        [EnumNameAttribute("落地")]
        Enum_Ground,
        
        [EnumNameAttribute("死亡状态可见")]
        Enum_IsShowInDeadState,
        
        [EnumNameAttribute("地底")]
        Enum_Underground,
        
        [EnumNameAttribute("从地底状态恢复")]
        Enum_ResumeGround,
        
        [EnumNameAttribute("不显示消融效果,瞬间消失")]
        Enum_ImmediateDeadEffect,
        [EnumNameAttribute("释放自己的格子占用")]
        Enum_ReleaseSelfGridUnit,
    }
    
    public enum ProjectileFlyBackLineType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("默认")]
        Enum_Default,
        
        [EnumNameAttribute("抛物线")]
        Enum_curve,
        
        [EnumNameAttribute("追踪曲线向前")]
        Enum_TraceRotateTowards,
    }
    
    public enum ControlProjectileViewType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("消除")]
        Enum_Destroy,
        
        [EnumNameAttribute("绑定当前目标")]
        Enum_BindTarget,
        
        [EnumNameAttribute("隐藏效果")]
        Enum_Hide,
        
        [EnumNameAttribute("显示效果")]
        Enum_Show,
    }
    
    public enum LaunchProjectileLineType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("静止")]
        Enum_Static,
        
        [EnumNameAttribute("直线")]
        Enum_Linear,
        
        [EnumNameAttribute("追踪")]
        Enum_Track,
        
        [EnumNameAttribute("绑定")]
        Enum_Bind,
        
        [EnumNameAttribute("抛物线追踪")]
        Enum_TrackWithCurve,
        
        [EnumNameAttribute("环绕")]
        Enum_Around,
        
        [EnumNameAttribute("追踪曲线向前")]
        Enum_TraceRotateTowards,
        
        [EnumNameAttribute("追踪贝泽尔曲线")]
        Enum_TraceWithBezier,
        
        [EnumNameAttribute("飞回")] 
        Enum_FlyBack,
        
        [EnumNameAttribute("跟随目标轨迹")]
        Enum_TraceToTargetMotion,

    }
    
    public enum LaunchProjectileSourceType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("新建")]
        Enum_New,
        
        [EnumNameAttribute("自己")]
        Enum_Self,
        
        [EnumNameAttribute("弹药库")]
        Enum_BulletGroup,
    }
    
    public enum BuffControlType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("移除")]
        Enum_Remove,
    }
    
    public enum ProjectileControlType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("引爆")]
        Enum_Detonate,
        
        [EnumNameAttribute("召回")]
        Enum_FlyBack,
        
        [EnumNameAttribute("销毁")]
        Enum_Dispose,
        
        [EnumNameAttribute("自定义动作")]
        Enum_Custom,
    }
    
    public enum ProjectileControlCnt
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("最后一个")]
        Enum_ONE,
        
        [EnumNameAttribute("第一个")]
        Enum_FIRST_ONE,
        
        [EnumNameAttribute("全部")]
        Enum_ALL,
    }
    
    public enum ChangeDirectionType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("目标优先")]
        Enum_targetFirst,
        
        [EnumNameAttribute("技能目标优先")]
        Enum_skillTargetFirst,
        
        [EnumNameAttribute("绝对角度")]
        Enum_absoluteAngle,
        
        [EnumNameAttribute("自定义link")]
        Enum_custom,
        
        [EnumNameAttribute("技能目标所在格子")]
        Enum_skillTargetCell,
    }
    
    public enum ActionLayerName
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("当前layer")]
        Enum_Current,
        
        [EnumNameAttribute("技能Layer")]
        Enum_SkillLayer,
        
        [EnumNameAttribute("共用Layer_带时间")]
        Enum_ExtendLayer_Skill,
        
        [EnumNameAttribute("状态Layer")]
        Enum_StateLayer,
        
        [EnumNameAttribute("受击Layer")]
        Enum_HurtLayer,
        
        [EnumNameAttribute("无限Layer，慎用")]
        Enum_Infinity_Layer,

    }
    
    public enum CompairisonOperatorEnum
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("大于")]
        Enum_greaterThan,
        
        [EnumNameAttribute("大于等于")]
        Enum_greaterThanOrEqual,
        
        [EnumNameAttribute("小于")]
        Enum_lessThan,
        
        [EnumNameAttribute("小于等于")]
        Enum_lessThanOrEqual,
        
        [EnumNameAttribute("等于")]
        Enum_equal,
        
        [EnumNameAttribute("不等于")]
        Enum_unEqual,
    }
    
    public enum ValueTypeEnum
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("整型")]
        Enum_int,
        
        [EnumNameAttribute("无符号整型")]
        Enum_uint,
        
        [EnumNameAttribute("浮点")]
        Enum_float,
        
        [EnumNameAttribute("字符串")]
        Enum_string,
        
        [EnumNameAttribute("布尔")]
        Enum_bool,
        
        [EnumNameAttribute("GameObject")]
        Enum_GameObject,
        
        [EnumNameAttribute("ActionRunner")]
        Enum_ActionRunner,
        
        [EnumNameAttribute("数组")]
        Enum_Array,

        [EnumNameAttribute("fix64")]
        Enum_Fix64,
        
        [EnumNameAttribute("格子")]
        Enum_Cell, 
        
        [EnumNameAttribute("Vector")]
        Enum_Vector,
    }
    
    public enum OperatorTypeEnum
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("等")]
        Enum_equal,
        
        [EnumNameAttribute("加")]
        Enum_add,
        
        [EnumNameAttribute("减")]
        Enum_subtract,
        
        [EnumNameAttribute("乘")]
        Enum_multiply,
        
        [EnumNameAttribute("除")]
        Enum_divide,
        
        [EnumNameAttribute("余")]
        Enum_residual,
        
        [EnumNameAttribute("非")]
        Enum_not,
        
        [EnumNameAttribute("取大")]
        Enum_max,
        
        [EnumNameAttribute("取小")]
        Enum_min,
    }
    
    public enum VectorOperatorTypeEnum
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("等")]
        Enum_equal,
        
        [EnumNameAttribute("加")]
        Enum_add,
        
        [EnumNameAttribute("减")]
        Enum_subtract,
        
        [EnumNameAttribute("除")]
        Enum_divide,
        
        [EnumNameAttribute("乘")]
        Enum_multiply,
        
        [EnumNameAttribute("余")]
        Enum_normalized,
        
        [EnumNameAttribute("非")]
        Enum_not,
    }
    
    public enum ActionLayerEnum
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("基础layer")]
        Enum_BaseLayer,
        
        [EnumNameAttribute("技能Layer")]
        Enum_SkillLayer,
        
        [EnumNameAttribute("共用Layer_只能一帧")]
        Enum_ExtendLayer,
        
        [EnumNameAttribute("共用Layer_带时间")]
        Enum_ExtendLayer_Skill,
        
        [EnumNameAttribute("状态Layer")]
        Enum_StateLayer,
        
        [EnumNameAttribute("受击Layer")]
        Enum_HurtLayer,
        
        [EnumNameAttribute("无限Layer，慎用")]
        Enum_Infinity_Layer,
        
       
    }
    
    public enum LinkModeEnum
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("aciton的名字")]
        Enum_ACTION_NAME,
        
        [EnumNameAttribute("连击ID")]
        Enum_COMBO_ATTACK_ID,
    }
    
    public enum BlendTypeEnum
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("叠加")]
        Enum_Additive,
        
        [EnumNameAttribute("覆盖")]
        Enum_Override,
    }
    
    public enum ScaleModeEnum
    {
        
        Enum_NULL,
        
        Enum_MODEL,
        
        Enum_COLLIDER,
    }
    
    public enum CampEnum
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("已方阵营")]
        Enum_CURRENT_CAMP,
        
        [EnumNameAttribute("敌方阵营")]
        Enum_INVERSE_CAMP,
        
        [EnumNameAttribute("中立阵营")]
        Enum_NEUTRAL_CAMP,
    }
    
    public enum ProjectileLaunchTypeEnum
    {
        [EnumNameAttribute("默认")]
        Enum_NULL,
        
        [EnumNameAttribute("普攻")]
        Enum_ProjectileLaunchType_NormalAttack,
        
        [EnumNameAttribute("技能")]
        Enum_ProjectileLaunchType_SkillAttack

    }
    
    public enum ActionUseModeEnum
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("地面")]
        Enum_LAND,
        
        [EnumNameAttribute("空中")]
        Enum_AIR,
        
        [EnumNameAttribute("以上都是")]
        Enum_BOTH,
    }
    
    public enum ComboNameEnum
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("摇杆")]
        Enum_move,
        
        [EnumNameAttribute("普攻")]
        Enum_a,
        
        [EnumNameAttribute("技能1")]
        Enum_b,
        
        [EnumNameAttribute("技能2")]
        Enum_c,
        
        [EnumNameAttribute("技能3")]
        Enum_d,
        
        [EnumNameAttribute("技能4")]
        Enum_e,
        
        [EnumNameAttribute("技能5")]
        Enum_h,
        
        [EnumNameAttribute("觉醒技")]
        Enum_f,
        
        [EnumNameAttribute("闪避技")]
        Enum_g,
    }
    
    public enum KeyStateEnum
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("按下")]
        Enum_KD,
        
        [EnumNameAttribute("按压")]
        Enum_KH,
        
        [EnumNameAttribute("抬起")]
        Enum_KU,
    }
    
    public enum RelativeNode
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("自己")]
        Enum_self,
        
        [EnumNameAttribute("目标")]
        Enum_target,
        
        [EnumNameAttribute("选中位置")]
        Enum_targetPos,
        
        [EnumNameAttribute("自己到目标")]
        Enum_self2target,
    }
    
    public enum EffectTarget
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("自己")]
        Enum_self,
        
        [EnumNameAttribute("目标")]
        Enum_targets,
        
        [EnumNameAttribute("技能目标")]
        Enum_skillTargets,
        
        [EnumNameAttribute("拥有者")]
        Enum_owner,
        
        [EnumNameAttribute("召唤物")]
        Enum_Summon,
        
        [EnumNameAttribute("召唤者")]
        Enum_Summoner,
        
        [EnumNameAttribute("自定义")]
        Enum_custom,

        [EnumNameAttribute("抓取")]
        Enum_grabtargets,
    }
    
    public enum BeatenMode
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("正常")]
        Enum_normal,
        
        [EnumNameAttribute("防御")]
        Enum_defend,
        
        [EnumNameAttribute("不可选中")]
        Enum_unselect,
        
        [EnumNameAttribute("不可攻击")]
        Enum_unattack,
    }
    
    public enum MoveSpeedTypeEnum
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("行走速度")]
        Enum_WalkSpeed,
        
        [EnumNameAttribute("跑动速度")]
        Enum_RunSpeed,
        
        [EnumNameAttribute("快跑速度")]
        Enum_FastRunSpeed,
        
        [EnumNameAttribute("自定义")]
        Enum_Custom,
    }
    
    public enum WarningEnum
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("圆形")]
        Enum_warning_circle,
        
        [EnumNameAttribute("扇形")]
        Enum_warning_sector,
        
        [EnumNameAttribute("矩形")]
        Enum_warning_rectangle,
        
        [EnumNameAttribute("直线")]
        Enum_warning_line,
    }
    
    public enum DataSourceEnum
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("输入")]
        Enum_custom,
        
        [EnumNameAttribute("link导入")]
        Enum_link,
    }
    
    public enum SelectTargetEnum
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("当前目标")]
        Enum_currentTarget,
        
        [EnumNameAttribute("飞行道具")]
        Enum_projectileTarget,
        
        [EnumNameAttribute("飞行道具_第一个")]
        Enum_projectileTarget_first,
        
        [EnumNameAttribute("buff标记")]
        Enum_buffTagTarget,
        
        [EnumNameAttribute("自己")]
        Enum_self,
        
        [EnumNameAttribute("自己所在的格子")]
        Enum_selfGrid,
        
        [EnumNameAttribute("格子位置")]
        Enum_targetPos,
        
        [EnumNameAttribute("目标[人]所在的格子位置")]
        Enum_targetGrid,
        
        [EnumNameAttribute("自己obj位置")]
        Enum_objPos,
        
        [EnumNameAttribute("拥有者位置")]
        Enum_ownerPos,
        [EnumNameAttribute("特殊选择-link")]
        Enum_target_link,
    }
    
    public enum SkillSliderEnum
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("蓄力条")]
        Enum_0,
        
        [EnumNameAttribute("连续条")]
        Enum_1,
    }
    
    public enum ActionStateEnum
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("存在")]
        Enum_exist,
        
        [EnumNameAttribute("正常")]
        Enum_normal,
        
        [EnumNameAttribute("正常[只论状态 无视 浮空]")]
        Enum_normal_only_state,
        
        [EnumNameAttribute("浮空")]
        Enum_air,
        
        [EnumNameAttribute("倒地")]
        Enum_lie,
        
        [EnumNameAttribute("被抓取")]
        Enum_beGrab,
        
        [EnumNameAttribute("僵直")]
        Enum_numb,
        
        [EnumNameAttribute("死亡")]
        Enum_dead,
        
        [EnumNameAttribute("彻底死亡")]
        Enum_dead_complete,
        
        [EnumNameAttribute("ai能否技能")]
        Enum_spellable,
        
        [EnumNameAttribute("ai能否攻击")]
        Enum_attackable,
        
        [EnumNameAttribute("ai能否移动")]
        Enum_movable,
        
        [EnumNameAttribute("技能是否满能量")]
        Enum_energyFull,
        
        [EnumNameAttribute("能否移动【状态】")]
        Enum_MoveEnable,
        
        [EnumNameAttribute("能否被选择【状态】")]
        Enum_SelectEnable,
        
        [EnumNameAttribute("能否被敌军选择【状态】")]
        Enum_EnemySelectEnable,
        
        [EnumNameAttribute("能否被友军选择【状态】")]
        Enum_AlliesSelectEnable,
        
        [EnumNameAttribute("通用buff状态")]
        Enum_GenericStatus,
        
        [EnumNameAttribute("不可打断技能状态")]
        Enum_UnbreakableSkillCasting,
        
        [EnumNameAttribute("备用1")]
        Enum_Value_1,
        [EnumNameAttribute("备用2")]
        Enum_Value_2,
    }
    
    public enum BuffStatusEnum
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("锁蓝")]
        Enum_Lock_Mp,
        Enum_Set3_Machine_Breakup,
    }
    
    public enum SummonTargetSelect
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("召唤者")]
        Enum_summer,
        
        [EnumNameAttribute("自己")]
        Enum_self,
    }
    
    public enum ActionSummonSourceDataType
    {
        [EnumName("None")]
        None,
        [EnumName("随机一个死亡的英雄")]
        OneOfDeadHero = 1,
        [EnumName("带有某buff的死亡的英雄")]
        DeadHeroWithBuff = 2,
        [EnumName("从英雄库里随机")]
        HeroKu = 3,
        [EnumName("死亡的最强大的英雄(不重复)")]
        DeadHeroStrongest = 4,
    }
    
    public enum ControlTargetMoveType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("无")]
        Enum_none,
        
        [EnumNameAttribute("向自己移动")]
        Enum_tome,
    }
    
    public enum ControlTargetDirectionType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("无")]
        Enum_none,
        
        [EnumNameAttribute("向自己")]
        Enum_tome,
        
        [EnumNameAttribute("向自己反向")]
        Enum_opp_tome,
    }
    
    public enum CharacterAttributeEnum
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("魔能专精")]
        Enum_1,
        
        [EnumNameAttribute("攻击")]
        Enum_2,
        
        [EnumNameAttribute("血量上限")]
        Enum_3,
        
        [EnumNameAttribute("物理防御")]
        Enum_4,
        
        [EnumNameAttribute("法术防御")]
        Enum_5,
        
        [EnumNameAttribute("格挡等级")]
        Enum_6,
        
        [EnumNameAttribute("格挡率")]
        Enum_7,
        
        [EnumNameAttribute("格挡穿透等级")]
        Enum_8,
        
        [EnumNameAttribute("格挡穿透率")]
        Enum_9,
        
        [EnumNameAttribute("最终伤害绝对值")]
        Enum_10,
        
        [EnumNameAttribute("最终伤害减免绝对值")]
        Enum_11,
        
        [EnumNameAttribute("致命等级")]
        Enum_12,
        
        [EnumNameAttribute("致命率")]
        Enum_13,
        
        [EnumNameAttribute("韧性")]
        Enum_14,
        
        [EnumNameAttribute("物理减免")]
        Enum_15,
        
        [EnumNameAttribute("法术减免")]
        Enum_16,
        
        [EnumNameAttribute("冲刺速度")]
        Enum_17,
        
        [EnumNameAttribute("跑步速度")]
        Enum_18,
        
        [EnumNameAttribute("行走速度")]
        Enum_19,
        
        [EnumNameAttribute("伤害加成等级")]
        Enum_20,
        
        [EnumNameAttribute("伤害加成")]
        Enum_21,
        
        [EnumNameAttribute("伤害减免")]
        Enum_22,
        
        [EnumNameAttribute("物理伤害")]
        Enum_23,
        
        [EnumNameAttribute("法术伤害")]
        Enum_24,
        
        [EnumNameAttribute("致命伤害等级")]
        Enum_25,
        
        [EnumNameAttribute("致命伤害")]
        Enum_26,
        
        [EnumNameAttribute("韧性等级")]
        Enum_27,
        
        [EnumNameAttribute("致命减免等级")]
        Enum_28,
        
        [EnumNameAttribute("致命减免")]
        Enum_29,
        
        [EnumNameAttribute("震荡等级")]
        Enum_30,
        
        [EnumNameAttribute("震荡率")]
        Enum_31,
        
        [EnumNameAttribute("震荡伤害等级")]
        Enum_32,
        
        [EnumNameAttribute("震荡伤害")]
        Enum_33,
        
        [EnumNameAttribute("抵力等级")]
        Enum_34,
        
        [EnumNameAttribute("抵力")]
        Enum_35,
        
        [EnumNameAttribute("震荡减免等级")]
        Enum_36,
        
        [EnumNameAttribute("震荡减免")]
        Enum_37,
        
        [EnumNameAttribute("连击等级")]
        Enum_38,
        
        [EnumNameAttribute("连击率")]
        Enum_39,
        
        [EnumNameAttribute("连击伤害等级")]
        Enum_40,
        
        [EnumNameAttribute("连击伤害")]
        Enum_41,
        
        [EnumNameAttribute("抗连击等级")]
        Enum_42,
        
        [EnumNameAttribute("抗连击")]
        Enum_43,
        
        [EnumNameAttribute("连击减免等级")]
        Enum_44,
        
        [EnumNameAttribute("连击减免")]
        Enum_45,
        
        [EnumNameAttribute("魔能：空")]
        Enum_46,
        
        [EnumNameAttribute("魔能：地")]
        Enum_47,
        
        [EnumNameAttribute("魔能：奥")]
        Enum_48,
        
        [EnumNameAttribute("魔能：灭")]
        Enum_49,
        
        [EnumNameAttribute("魔能伤害")]
        Enum_50,
        
        [EnumNameAttribute("魔能：空伤害")]
        Enum_51,
        
        [EnumNameAttribute("魔能：地伤害")]
        Enum_52,
        
        [EnumNameAttribute("魔能：奥伤害")]
        Enum_53,
        
        [EnumNameAttribute("魔能：灭伤害")]
        Enum_54,
        
        [EnumNameAttribute("魔能防御")]
        Enum_55,
        
        [EnumNameAttribute("魔能：空防御")]
        Enum_56,
        
        [EnumNameAttribute("魔能：地防御")]
        Enum_57,
        
        [EnumNameAttribute("魔能：奥防御")]
        Enum_58,
        
        [EnumNameAttribute("魔能：灭防御")]
        Enum_59,
        
        [EnumNameAttribute("魔能减免")]
        Enum_60,
        
        [EnumNameAttribute("空属性魔能减免")]
        Enum_61,
        
        [EnumNameAttribute("地属性魔能减免")]
        Enum_62,
        
        [EnumNameAttribute("奥属性魔能减免")]
        Enum_63,
        
        [EnumNameAttribute("灭属性魔能减免")]
        Enum_64,
        
        [EnumNameAttribute("魔能穿透等级")]
        Enum_65,
        
        [EnumNameAttribute("魔能穿透")]
        Enum_66,
        
        [EnumNameAttribute("物理穿透等级")]
        Enum_67,
        
        [EnumNameAttribute("物理穿透")]
        Enum_68,
        
        [EnumNameAttribute("法术穿透等级")]
        Enum_69,
        
        [EnumNameAttribute("法术穿透")]
        Enum_70,
        
        [EnumNameAttribute("穿透率")]
        Enum_71,
        
        [EnumNameAttribute("脱离战斗回血速度")]
        Enum_72,
        
        [EnumNameAttribute("战斗回血速度")]
        Enum_73,
        
        [EnumNameAttribute("僵直")]
        Enum_74,
        
        [EnumNameAttribute("僵直抗性")]
        Enum_75,
        
        [EnumNameAttribute("体力上限")]
        Enum_1001,
        
        [EnumNameAttribute("体力非战斗回复速度")]
        Enum_1002,
        
        [EnumNameAttribute("体力战斗回复速度")]
        Enum_1003,
        
        [EnumNameAttribute("体力击中回复")]
        Enum_1004,
        
        [EnumNameAttribute("体力被击中回复")]
        Enum_1005,
        
        [EnumNameAttribute("体力初始值")]
        Enum_1006,
        
        [EnumNameAttribute("体力值")]
        Enum_1007,
        
        [EnumNameAttribute("SP上限")]
        Enum_1011,
        
        [EnumNameAttribute("SP非战斗回复速度")]
        Enum_1012,
        
        [EnumNameAttribute("SP战斗回复速度")]
        Enum_1013,
        
        [EnumNameAttribute("SP击中回复")]
        Enum_1014,
        
        [EnumNameAttribute("SP被击中回复")]
        Enum_1015,
        
        [EnumNameAttribute("SP初始值")]
        Enum_1016,
        
        [EnumNameAttribute("SP值")]
        Enum_1017,
        
        [EnumNameAttribute("怒气上限")]
        Enum_1021,
        
        [EnumNameAttribute("怒气非战斗回复速度")]
        Enum_1022,
        
        [EnumNameAttribute("怒气战斗回复速度")]
        Enum_1023,
        
        [EnumNameAttribute("怒气击中回复")]
        Enum_1024,
        
        [EnumNameAttribute("怒气被击中回复")]
        Enum_1025,
        
        [EnumNameAttribute("怒气初始值")]
        Enum_1026,
        
        [EnumNameAttribute("怒气值")]
        Enum_1027,
        
        [EnumNameAttribute("能量上限")]
        Enum_1031,
        
        [EnumNameAttribute("能量非战斗回复速度")]
        Enum_1032,
        
        [EnumNameAttribute("能量战斗回复速度")]
        Enum_1033,
        
        [EnumNameAttribute("能量击中回复")]
        Enum_1034,
        
        [EnumNameAttribute("能量被击中回复")]
        Enum_1035,
        
        [EnumNameAttribute("能量初始值")]
        Enum_1036,
        
        [EnumNameAttribute("能量值")]
        Enum_1037,
        
        [EnumNameAttribute("秘法上限")]
        Enum_1041,
        
        [EnumNameAttribute("秘法非战斗回复速度")]
        Enum_1042,
        
        [EnumNameAttribute("秘法战斗回复速度")]
        Enum_1043,
        
        [EnumNameAttribute("秘法击中回复")]
        Enum_1044,
        
        [EnumNameAttribute("秘法被击中回复")]
        Enum_1045,
        
        [EnumNameAttribute("秘法初始值")]
        Enum_1046,
        
        [EnumNameAttribute("秘法值")]
        Enum_1047,
        
        [EnumNameAttribute("霸体上限")]
        Enum_1051,
        
        [EnumNameAttribute("霸体非战斗回复速度")]
        Enum_1052,
        
        [EnumNameAttribute("霸体战斗回复速度")]
        Enum_1053,
        
        [EnumNameAttribute("霸体击中回复")]
        Enum_1054,
        
        [EnumNameAttribute("霸体被击中回复")]
        Enum_1055,
        
        [EnumNameAttribute("霸体初始值")]
        Enum_1056,
        
        [EnumNameAttribute("霸体值")]
        Enum_1057,
        
        [EnumNameAttribute("子弹上限")]
        Enum_1061,
        
        [EnumNameAttribute("子弹非战斗回复速度")]
        Enum_1062,
        
        [EnumNameAttribute("子弹战斗回复速度")]
        Enum_1063,
        
        [EnumNameAttribute("子弹击中回复")]
        Enum_1064,
        
        [EnumNameAttribute("子弹被击中回复")]
        Enum_1065,
        
        [EnumNameAttribute("子弹初始值")]
        Enum_1066,
        
        [EnumNameAttribute("子弹值")]
        Enum_1067,
        
        [EnumNameAttribute("当前生命")]
        Enum_2001,
        
        [EnumNameAttribute("移动速度")]
        Enum_2002,
        
        [EnumNameAttribute("当前护盾")]
        Enum_2003,
        
        [EnumNameAttribute("物理伤害吸收")]
        Enum_2004,
        
        [EnumNameAttribute("魔法伤害吸收")]
        Enum_2005,
        
        [EnumNameAttribute("盾挡伤害减免")]
        Enum_2006,
        
        [EnumNameAttribute("属性ID最大值")]
        Enum_4096,
    }
    
    public enum ProjectilePropertyEnum
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("生命-攻击次数 int")]
        Enum_hitLife,
        
        [EnumNameAttribute("阵营 int")]
        Enum_camp,
        
        [EnumNameAttribute("存活时间（帧）int")]
        Enum_loop,
        
        [EnumNameAttribute("移动距离（毫米）int")]
        Enum_moveMilesLife,
        
        [EnumNameAttribute("缩放比例,int 千分")]
        Enum_scale, 
        
        [EnumNameAttribute("重复发射index,int")]
        Enum_repeat_index,
        
        [EnumNameAttribute("是否记录位置,bool")]
        Enum_Record_Position,
    }
    
    public enum CalculateIntType
    {
        
        Enum_NULL,
        
        [EnumNameAttribute("向下取整")]
        Enum_floor,
        
        [EnumNameAttribute("四舍五入")]
        Enum_round,
        
        [EnumNameAttribute("向上取整")]
        Enum_ceil,
        
        [EnumNameAttribute("浮点不变")]
        Enum_float,
    }
    
    public enum BattleFieldPropertyEnum
    {
        Enum_NULL,
        
        [EnumNameAttribute("战场状态")]
        Enum_Battle_State,
        
        [EnumNameAttribute("存活的友军数量")]
        Enum_Friend_Hero_Count,
        
        [EnumNameAttribute("存活的敌军数量")]
        Enum_Enemy_Hero_Count
        
    }
    
    public enum DoViewContainerThingEnum
    {
        Enum_NULL,
        
        [EnumNameAttribute("播放动画")]
        Enum_playAnimation,
        
        [EnumNameAttribute("修改速度")]
        Enum_playSpeed,
        [EnumNameAttribute("飞行道具脚本控制")]
        Enum_effectSpecialScriptCtrl,
        
        [EnumNameAttribute("其他，备用1")]
        Enum_other_1 = 100,
        
        [EnumNameAttribute("其他，备用2")]
        Enum_other_2,
        
        [EnumNameAttribute("其他，备用3")]
        Enum_other_3,
        
        [EnumNameAttribute("其他，备用4")]
        Enum_other_4,
        
        [EnumNameAttribute("其他，备用5")]
        Enum_other_5
        
    }
    
    public enum GetOccupyGridType
    {
        [EnumNameAttribute("link")]
        Enum_Custom,
        [EnumNameAttribute("全部")]
        Enum_ALL,
        [EnumNameAttribute("最后一个")]
        Enum_ONE,
        [EnumNameAttribute("第一个")]
        Enum_FIRST_ONE,
        
       
    }

    public enum GetUnitPropertyType
    {
        [EnumNameAttribute("空")]
        Enum_Nothing,
        [EnumNameAttribute("位置")]
        Enum_Pos,
        [EnumNameAttribute("方向")]
        Enum_Direction,
        [EnumNameAttribute("缩放")]
        Enum_Scale,
        [EnumNameAttribute("角色的唯一ID")]
        Enum_CharID,
        [EnumNameAttribute("留空")]
        Enum_test_1 = 100,
        [EnumNameAttribute("留空")]
        Enum_test_2,
    }

    public enum WaitPosControlType
    {
        [EnumNameAttribute("锁")] Enum_Lock,
        [EnumNameAttribute("解锁")] Enum_UnLock,
        [EnumNameAttribute("判断还有空格子")] Enum_CheckHaveEmptyCell,
        [EnumNameAttribute("获取空闲的格子数量")] Enum_GetEmptyCellNum,
        [EnumNameAttribute("获取锁住的格子")] Enum_GetLockCell,
        [EnumNameAttribute("获取空闲的格子")] Enum_GetEmptyCell,
        [EnumNameAttribute("留空")] Enum_test_1 = 100,
        [EnumNameAttribute("留空")] Enum_test_2,
    }
    
    public enum VMotionTypeEnum
    {
        none,
        Track,
        TrackRotateToward,
        TrackWithBezier,
        TrackWithTime,
        Acceleration,
        Gravity,
        GravityAdvance,
        Bind,
        Around,
        ToPosition,
        Direction,
        RoleMoveGrid,
        RoleMoveToTarget,
        //显示motion
        Swallow,
        Zephyr,
        BackNormal,
        TraceToTargetMotion,
        Set11Sett3StarEffect,
    }
}
