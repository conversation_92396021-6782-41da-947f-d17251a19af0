using System;

namespace Lucifer.ActCore
{
    public static class ActCore_SpecializerData<T>
    {
        public static Func<string> m_GetAFCValue;

        public static string GetAFCValue()
        {
            if (m_GetAFCValue != null)
                return m_GetAFCValue();
            else
            {
                Type type = typeof(T);
                return ActCore_SpecializerTypeObject.GetAFCValue(type);
            }
        }

        public static Func<BaseVariableData, T> m_GetData;

        public static T GetData(BaseVariableData variableData)
        {
            if(m_GetData != null)
                return m_GetData(variableData);
            else
            {
                object result = ActCore_SpecializerTypeObject.GetData(variableData);
                if (result == null)
                {
                    return default(T);
                }
                else
                {
#if ACGGAME_CLIENT
                    return (T)result;
#else
                    try
                    {
                        return (T)result;
                    }
                    catch (Exception)
                    {
                        return default(T); 
                    }
#endif
                }
            }
        }

        public static Action<BaseVariableData, T> m_SetData;

        public static void SetData(BaseVariableData variableData, T data)
        {
            if(m_SetData != null)
                m_SetData(variableData, data);
            else
                ActCore_SpecializerTypeObject.SetData(variableData, data);
        }
    }
    
    public static class InitSpecializerType
    {
        public static void Init()
        {
            ActCore_SpecializerTypeBool.Init();
            ActCore_SpecializerTypeFloat.Init();
            ActCore_SpecializerTypeFVector3.Init();
            ActCore_SpecializerTypeInt.Init();
            ActCore_SpecializerTypeString.Init();
            ActCore_SpecializerTypeUint.Init();
            ActCore_SpecializerTypeFixed.Init();
            ActCore_SpecializerTypeGridOffset.Init();

#if ACGGAME_CLIENT && !LOGIC_THREAD
            ActCore_SpecializerTypeGameObject.Init();
#endif
        }
    }
}


