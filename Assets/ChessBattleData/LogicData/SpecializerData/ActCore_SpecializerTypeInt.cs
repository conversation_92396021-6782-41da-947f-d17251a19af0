namespace Lucifer.ActCore
{
    public static class ActCore_SpecializerTypeInt
    {
        public static void Init()
        {
            ActCore_SpecializerData<int>.m_GetAFCValue = GetAFCValue;
            ActCore_SpecializerData<int>.m_GetData = GetData;
            ActCore_SpecializerData<int>.m_SetData = SetData;
        }

        private static string GetAFCValue()
        {
            return AFCValue.int_type;
        }

        private static int GetData(BaseVariableData variableData)
        {
            return variableData.variableValueint;
        }

        private static void SetData(BaseVariableData variableData, int data)
        {
            variableData.variableValueint = data;
        }
    }
}


