using System;
using GameFramework.FMath;

namespace Lucifer.ActCore
{
    public static class ActCore_SpecializerTypeObject
    {
        private static Type actionRunnerType = typeof(IBaseActionRunnerAdapter);
        private static Type afcArrayType = typeof(AFCArray);
        private static Type gridOffsetType = typeof(Lucifer.ActCore.GridOffset);
        
        public static string GetAFCValue(Type type)
        {
            string typeName = type.Name;
            if (type.IsValueType)
            {
                switch (typeName)
                {
                    case "Int32":
                        typeName = AFCValue.int_type;
                        break;
                    case "UInt32":
                        typeName = AFCValue.uint_type;
                        break;
                    case "Boolean":
                        typeName = AFCValue.bool_type;
                        break;
                    case "Single":
                        typeName = AFCValue.float_type;
                        break;
                    case "FVector3":
                        typeName = AFCValue.FVector3_type;
                        break;
                    case "Fix64":
                        typeName = AFCValue.fix64_type;
                        break;
                }
            }
            else
            {
                switch (typeName)
                {
                    case "GameObject":
                        typeName = AFCValue.Gameobject_type;
                        break;
                    case "String":
                        typeName = AFCValue.string_type;
                        break;
                }
                
                if (isChildOrItSelf(type, actionRunnerType))
                {
                    typeName = AFCValue.ActionRunner_type;
                }
                else if (isChildOrItSelf(type, afcArrayType))
                {
                    typeName = AFCValue.Array_type;
                }
                else if (isChildOrItSelf(type, gridOffsetType))
                {
                    typeName = AFCValue.GridOffset_type;
                }
            }
            
            return typeName;
        }

        public static bool isChildOrItSelf(Type type, Type parentType)
        {
            if (type == parentType)
                return true;

            if (type.IsSubclassOf(parentType))
                return true;

            return false;
        }
        
        public static object GetData(BaseVariableData variableData)
        {
            switch (variableData.variableType)
            {
                case AFCValue.int_type:
                {
                    return variableData.variableValueint;
                }
                case AFCValue.float_type:
                {
                    return variableData.variableValuefloat;
                }
                 case AFCValue.fix64_type:
                {
                    return variableData.variableValuefixed;
                }
                case AFCValue.bool_type:
                {
                    return variableData.variableValuebool;
                }
                case AFCValue.string_type:
                {
                    return variableData.variableValuestring;
                }
                case AFCValue.uint_type:
                {
                    return variableData.variableValueuint;
                }
                case AFCValue.ActionRunner_type:
                {
                    return variableData.variableValueActionRunner;
                }
                case AFCValue.Array_type:
                {
                    return variableData.variableValueArray;
                }
                case AFCValue.FVector3_type:
                {
                    return variableData.variableValueFVector3;
                }
                case AFCValue.GridOffset_type:
                {
                    return variableData.variableValueGridOffset;
                }
#if ACGGAME_CLIENT && !LOGIC_THREAD
                case AFCValue.Gameobject_type:
                {
                    return variableData.variableValueGameObject;
                }               
#endif
            }

            return null;
        }
        
        public static void SetData(BaseVariableData variableData, object value)
        {
            switch (variableData.variableType)
            {
                case AFCValue.int_type:
                {
                    variableData.variableValueint = (int) value;
                }
                    break;
                case AFCValue.float_type:
                {
                    variableData.variableValuefloat = (float) value;
                }
                 break;
                case AFCValue.fix64_type:
                {
                    variableData.variableValuefixed = (Fix64) value;
                }
                    break;
                case AFCValue.bool_type:
                {
                    variableData.variableValuebool = (bool) value;
                }
                    break;
                case AFCValue.string_type:
                {
                    variableData.variableValuestring = (string) value;
                }
                    break;
                case AFCValue.uint_type:
                {
                    variableData.variableValueuint = (uint) value;
                }
                    break;
                case AFCValue.ActionRunner_type:
                {
                    variableData.variableValueActionRunner = (IBaseActionRunnerAdapter) value;
                }
                    break;
                case AFCValue.Array_type:
                {
                    variableData.variableValueArray = (AFCArray) value;
                }
                    break;
                case AFCValue.FVector3_type:
                {
                    variableData.variableValueFVector3 = (FVector3) value;
                }
                    break;
                case AFCValue.GridOffset_type:
                {
                    variableData.variableValueGridOffset = (Lucifer.ActCore.GridOffset) value;
                }
                    break;
#if ACGGAME_CLIENT && !LOGIC_THREAD
                case AFCValue.Gameobject_type:
                {
                    variableData.variableValueGameObject = (UnityEngine.GameObject) value;
                }               
                    break;
#endif
            }
        }
    }
}