using System;
using System.Collections.Generic;
using ProtoBuf;

namespace Lucifer.ActCore
{
    public class HitBoxAllAction
    {
        public HitBoxOneActionInput[] hitBoxGroupInputList;
    }
    

    [System.Serializable]
[ProtoContract]
[ProtoInclude(tag:2000,knownType:typeof(Lucifer.ActCore.SetToActionFade))]
[ProtoInclude(tag:2001,knownType:typeof(Lucifer.ActCore.ToAction))]
[ProtoInclude(tag:2002,knownType:typeof(Lucifer.ActCore.SetActive))]
[ProtoInclude(tag:2003,knownType:typeof(Lucifer.ActCore.BreakAction))]
[ProtoInclude(tag:2004,knownType:typeof(Lucifer.ActCore.AddCancelPoint))]
[ProtoInclude(tag:2005,knownType:typeof(Lucifer.ActCore.CameraShake))]
[ProtoInclude(tag:2006,knownType:typeof(Lucifer.ActCore.ChangeDirection))]
[ProtoInclude(tag:2007,knownType:typeof(Lucifer.ActCore.AutoMoveDuration))]
[ProtoInclude(tag:2008,knownType:typeof(Lucifer.ActCore.ControlCharacterMove))]
[ProtoInclude(tag:2009,knownType:typeof(Lucifer.ActCore.MoveByRotation))]
[ProtoInclude(tag:2010,knownType:typeof(Lucifer.ActCore.IsMyHero))]
[ProtoInclude(tag:2011,knownType:typeof(Lucifer.ActCore.Jump))]
[ProtoInclude(tag:2012,knownType:typeof(Lucifer.ActCore.DefenseProperties))]
[ProtoInclude(tag:2013,knownType:typeof(Lucifer.ActCore.AddEffect))]
[ProtoInclude(tag:2014,knownType:typeof(Lucifer.ActCore.RemoveEffect))]
[ProtoInclude(tag:2015,knownType:typeof(Lucifer.ActCore.AddUIEffect))]
[ProtoInclude(tag:2016,knownType:typeof(Lucifer.ActCore.RemoveUIEffect))]
[ProtoInclude(tag:2017,knownType:typeof(Lucifer.ActCore.AddBattleWarning))]
[ProtoInclude(tag:2018,knownType:typeof(Lucifer.ActCore.ProjectileControl))]
[ProtoInclude(tag:2019,knownType:typeof(Lucifer.ActCore.BuffControlByID))]
[ProtoInclude(tag:2020,knownType:typeof(Lucifer.ActCore.LaunchProjectile))]
[ProtoInclude(tag:2021,knownType:typeof(Lucifer.ActCore.RepeatLaunchProjectile))]
[ProtoInclude(tag:2022,knownType:typeof(Lucifer.ActCore.ChangeProjectileAroundRadius))]
[ProtoInclude(tag:2023,knownType:typeof(Lucifer.ActCore.ProjectileChangeView))]
[ProtoInclude(tag:2024,knownType:typeof(Lucifer.ActCore.ControlProjectileView))]
[ProtoInclude(tag:2025,knownType:typeof(Lucifer.ActCore.ProjectileFlyBack))]
[ProtoInclude(tag:2026,knownType:typeof(Lucifer.ActCore.ChangeGravity))]
[ProtoInclude(tag:2027,knownType:typeof(Lucifer.ActCore.AddShock))]
[ProtoInclude(tag:2028,knownType:typeof(Lucifer.ActCore.ActionData))]
[ProtoInclude(tag:2029,knownType:typeof(Lucifer.ActCore.AddComboRespond))]
[ProtoInclude(tag:2030,knownType:typeof(Lucifer.ActCore.CheckKeyState))]
[ProtoInclude(tag:2031,knownType:typeof(Lucifer.ActCore.Teleport))]
[ProtoInclude(tag:2032,knownType:typeof(Lucifer.ActCore.SummonCharacter))]
[ProtoInclude(tag:2033,knownType:typeof(Lucifer.ActCore.DestroyMe))]
[ProtoInclude(tag:2034,knownType:typeof(Lucifer.ActCore.ConditionsLinker))]
[ProtoInclude(tag:2035,knownType:typeof(Lucifer.ActCore.JumpToTarget))]
[ProtoInclude(tag:2036,knownType:typeof(Lucifer.ActCore.ResetActionState))]
[ProtoInclude(tag:2037,knownType:typeof(Lucifer.ActCore.CleanFightProtectState))]
[ProtoInclude(tag:2038,knownType:typeof(Lucifer.ActCore.AddForceField))]
[ProtoInclude(tag:2039,knownType:typeof(Lucifer.ActCore.SwitchTarget))]
[ProtoInclude(tag:2040,knownType:typeof(Lucifer.ActCore.LucianSkill))]
[ProtoInclude(tag:2041,knownType:typeof(Lucifer.ActCore.LucianSkill_5_5))]
[ProtoInclude(tag:2042,knownType:typeof(Lucifer.ActCore.IsCanOverlap))]
[ProtoInclude(tag:2043,knownType:typeof(Lucifer.ActCore.MoveToTarget))]
[ProtoInclude(tag:2044,knownType:typeof(Lucifer.ActCore.MoveWithAMT))]
[ProtoInclude(tag:2045,knownType:typeof(Lucifer.ActCore.MoveAroundTarget))]
[ProtoInclude(tag:2046,knownType:typeof(Lucifer.ActCore.MoveBindTarget))]
[ProtoInclude(tag:2047,knownType:typeof(Lucifer.ActCore.FlyToTarget))]
[ProtoInclude(tag:2048,knownType:typeof(Lucifer.ActCore.RunToTarget))]
[ProtoInclude(tag:2049,knownType:typeof(Lucifer.ActCore.FlyToTargetAdvance))]
[ProtoInclude(tag:2050,knownType:typeof(Lucifer.ActCore.AddMoveMotion))]
[ProtoInclude(tag:2051,knownType:typeof(Lucifer.ActCore.AddViewMotion))]
[ProtoInclude(tag:2052,knownType:typeof(Lucifer.ActCore.SetVariableValue))]
[ProtoInclude(tag:2053,knownType:typeof(Lucifer.ActCore.SetVariableValueAdvance))]
[ProtoInclude(tag:2054,knownType:typeof(Lucifer.ActCore.SetAnimatorVParameter))]
[ProtoInclude(tag:2055,knownType:typeof(Lucifer.ActCore.SetAnimatorLayerWeight))]
[ProtoInclude(tag:2056,knownType:typeof(Lucifer.ActCore.CheckVariableValue))]
[ProtoInclude(tag:2057,knownType:typeof(Lucifer.ActCore.SwitchVariableValue))]
[ProtoInclude(tag:2058,knownType:typeof(Lucifer.ActCore.CameraCustomSetting))]
[ProtoInclude(tag:2059,knownType:typeof(Lucifer.ActCore.CameraCustomSettingWithAnimation))]
[ProtoInclude(tag:2060,knownType:typeof(Lucifer.ActCore.CameraDirectionSetting))]
[ProtoInclude(tag:2061,knownType:typeof(Lucifer.ActCore.ResumeCameraCustomSetting))]
[ProtoInclude(tag:2062,knownType:typeof(Lucifer.ActCore.TurnRound))]
[ProtoInclude(tag:2063,knownType:typeof(Lucifer.ActCore.GrabTarget))]
[ProtoInclude(tag:2064,knownType:typeof(Lucifer.ActCore.ThrowTarget))]
[ProtoInclude(tag:2065,knownType:typeof(Lucifer.ActCore.ExecuteHit))]
[ProtoInclude(tag:2066,knownType:typeof(Lucifer.ActCore.HitGroupControl))]
[ProtoInclude(tag:2067,knownType:typeof(Lucifer.ActCore.EventActionData))]
[ProtoInclude(tag:2068,knownType:typeof(Lucifer.ActCore.SendEvent))]
[ProtoInclude(tag:2069,knownType:typeof(Lucifer.ActCore.SendEventToViewUnit))]
[ProtoInclude(tag:2070,knownType:typeof(Lucifer.ActCore.BulletTime))]
[ProtoInclude(tag:2071,knownType:typeof(Lucifer.ActCore.EffectTweenControl))]
[ProtoInclude(tag:2072,knownType:typeof(Lucifer.ActCore.RoleTransform))]
[ProtoInclude(tag:2073,knownType:typeof(Lucifer.ActCore.ChangeWeapon))]
[ProtoInclude(tag:2074,knownType:typeof(Lucifer.ActCore.ShowOrHideBody))]
[ProtoInclude(tag:2075,knownType:typeof(Lucifer.ActCore.EffectRadialBlur))]
[ProtoInclude(tag:2076,knownType:typeof(Lucifer.ActCore.PlayWWiseSound))]
[ProtoInclude(tag:2077,knownType:typeof(Lucifer.ActCore.StopWWiseSound))]
[ProtoInclude(tag:2078,knownType:typeof(Lucifer.ActCore.LayerCancelLevelLimit))]
[ProtoInclude(tag:2079,knownType:typeof(Lucifer.ActCore.ControlLock))]
[ProtoInclude(tag:2080,knownType:typeof(Lucifer.ActCore.CheckKeystateChangeAngle))]
[ProtoInclude(tag:2081,knownType:typeof(Lucifer.ActCore.CheckTime))]
[ProtoInclude(tag:2082,knownType:typeof(Lucifer.ActCore.RandomNumber))]
[ProtoInclude(tag:2083,knownType:typeof(Lucifer.ActCore.StealthEffect))]
[ProtoInclude(tag:2084,knownType:typeof(Lucifer.ActCore.ChangeCollider))]
[ProtoInclude(tag:2085,knownType:typeof(Lucifer.ActCore.ChangeColliderSelf))]
[ProtoInclude(tag:2086,knownType:typeof(Lucifer.ActCore.ChangeSmoothCollideRate))]
[ProtoInclude(tag:2087,knownType:typeof(Lucifer.ActCore.SkillCostPoint))]
[ProtoInclude(tag:2088,knownType:typeof(Lucifer.ActCore.SkillEndNotify))]
[ProtoInclude(tag:2089,knownType:typeof(Lucifer.ActCore.SkillEndContinue))]
[ProtoInclude(tag:2090,knownType:typeof(Lucifer.ActCore.DoublecastTriggerNotify))]
[ProtoInclude(tag:2091,knownType:typeof(Lucifer.ActCore.NormalAttackNotify))]
[ProtoInclude(tag:2092,knownType:typeof(Lucifer.ActCore.SkillBreakNotify))]
[ProtoInclude(tag:2093,knownType:typeof(Lucifer.ActCore.PlayTurnActionTime))]
[ProtoInclude(tag:2094,knownType:typeof(Lucifer.ActCore.CheckDistance))]
[ProtoInclude(tag:2095,knownType:typeof(Lucifer.ActCore.CheckTargetState))]
[ProtoInclude(tag:2096,knownType:typeof(Lucifer.ActCore.CheckSelfState))]
[ProtoInclude(tag:2097,knownType:typeof(Lucifer.ActCore.CheckSkillGrabState))]
[ProtoInclude(tag:2098,knownType:typeof(Lucifer.ActCore.CheckGridIsEmpty))]
[ProtoInclude(tag:2099,knownType:typeof(Lucifer.ActCore.GetCount))]
[ProtoInclude(tag:2100,knownType:typeof(Lucifer.ActCore.GetDistance))]
[ProtoInclude(tag:2101,knownType:typeof(Lucifer.ActCore.GetRandomNumber))]
[ProtoInclude(tag:2102,knownType:typeof(Lucifer.ActCore.GetActionHitCount))]
[ProtoInclude(tag:2103,knownType:typeof(Lucifer.ActCore.GetCharacterAttribute))]
[ProtoInclude(tag:2104,knownType:typeof(Lucifer.ActCore.GetVariableValue))]
[ProtoInclude(tag:2105,knownType:typeof(Lucifer.ActCore.GetSkillExcelTime))]
[ProtoInclude(tag:2106,knownType:typeof(Lucifer.ActCore.GetBulletCount))]
[ProtoInclude(tag:2107,knownType:typeof(Lucifer.ActCore.MultiCompare))]
[ProtoInclude(tag:2108,knownType:typeof(Lucifer.ActCore.Compare))]
[ProtoInclude(tag:2109,knownType:typeof(Lucifer.ActCore.CalculateInt))]
[ProtoInclude(tag:2110,knownType:typeof(Lucifer.ActCore.CalculateVector3))]
[ProtoInclude(tag:2111,knownType:typeof(Lucifer.ActCore.GetRaycastGridSidePoint))]
[ProtoInclude(tag:2112,knownType:typeof(Lucifer.ActCore.CheckSelfBuff))]
[ProtoInclude(tag:2113,knownType:typeof(Lucifer.ActCore.CheckSelfEquipment))]
[ProtoInclude(tag:2114,knownType:typeof(Lucifer.ActCore.PlayBroke))]
[ProtoInclude(tag:2115,knownType:typeof(Lucifer.ActCore.SwitchBranch))]
[ProtoInclude(tag:2116,knownType:typeof(Lucifer.ActCore.PlayDeadEffect))]
[ProtoInclude(tag:2117,knownType:typeof(Lucifer.ActCore.EffectAfterImage))]
[ProtoInclude(tag:2118,knownType:typeof(Lucifer.ActCore.EffectLightBody))]
[ProtoInclude(tag:2119,knownType:typeof(Lucifer.ActCore.RemoveLightBody))]
[ProtoInclude(tag:2120,knownType:typeof(Lucifer.ActCore.EffectAssassinEnterBattleTransparent))]
[ProtoInclude(tag:2121,knownType:typeof(Lucifer.ActCore.EffectBodyGeneric))]
[ProtoInclude(tag:2122,knownType:typeof(Lucifer.ActCore.ChangeCameraDirection))]
[ProtoInclude(tag:2123,knownType:typeof(Lucifer.ActCore.ResetCameraToDefault))]
[ProtoInclude(tag:2124,knownType:typeof(Lucifer.ActCore.ResetEnemyUnlockTime))]
[ProtoInclude(tag:2125,knownType:typeof(Lucifer.ActCore.PhoneShake))]
[ProtoInclude(tag:2126,knownType:typeof(Lucifer.ActCore.GetArrayUnit))]
[ProtoInclude(tag:2127,knownType:typeof(Lucifer.ActCore.GetArrayNext))]
[ProtoInclude(tag:2128,knownType:typeof(Lucifer.ActCore.GetHeroStar))]
[ProtoInclude(tag:2129,knownType:typeof(Lucifer.ActCore.GetSelfGameObject))]
[ProtoInclude(tag:2130,knownType:typeof(Lucifer.ActCore.GetOwnerPos))]
[ProtoInclude(tag:2131,knownType:typeof(Lucifer.ActCore.GetBcCell))]
[ProtoInclude(tag:2132,knownType:typeof(Lucifer.ActCore.GetBcId))]
[ProtoInclude(tag:2133,knownType:typeof(Lucifer.ActCore.GetBeGraber))]
[ProtoInclude(tag:2134,knownType:typeof(Lucifer.ActCore.GetGraber))]
[ProtoInclude(tag:2135,knownType:typeof(Lucifer.ActCore.IsMe))]
[ProtoInclude(tag:2136,knownType:typeof(Lucifer.ActCore.GetSkillTarget))]
[ProtoInclude(tag:2137,knownType:typeof(Lucifer.ActCore.GetCellPos))]
[ProtoInclude(tag:2138,knownType:typeof(Lucifer.ActCore.SetTarget))]
[ProtoInclude(tag:2139,knownType:typeof(Lucifer.ActCore.SetSkillTarget))]
[ProtoInclude(tag:2140,knownType:typeof(Lucifer.ActCore.AutoReSetSkillTarget))]
[ProtoInclude(tag:2141,knownType:typeof(Lucifer.ActCore.FindTarget))]
[ProtoInclude(tag:2142,knownType:typeof(Lucifer.ActCore.FindSkillTarget))]
[ProtoInclude(tag:2143,knownType:typeof(Lucifer.ActCore.FindCurrentSkillTarget))]
[ProtoInclude(tag:2144,knownType:typeof(Lucifer.ActCore.GetCurrentTarget))]
[ProtoInclude(tag:2145,knownType:typeof(Lucifer.ActCore.FindAITarget))]
[ProtoInclude(tag:2146,knownType:typeof(Lucifer.ActCore.GetActionTriggerRole))]
[ProtoInclude(tag:2147,knownType:typeof(Lucifer.ActCore.GetHitRole))]
[ProtoInclude(tag:2148,knownType:typeof(Lucifer.ActCore.GetSummoner))]
[ProtoInclude(tag:2149,knownType:typeof(Lucifer.ActCore.GetRoleSpeed))]
[ProtoInclude(tag:2150,knownType:typeof(Lucifer.ActCore.AddBuff))]
[ProtoInclude(tag:2151,knownType:typeof(Lucifer.ActCore.RemoveBuff))]
[ProtoInclude(tag:2152,knownType:typeof(Lucifer.ActCore.BatchLaunchProjectile))]
[ProtoInclude(tag:2153,knownType:typeof(Lucifer.ActCore.TansheGroup))]
[ProtoInclude(tag:2154,knownType:typeof(Lucifer.ActCore.ProjectileChangeHitData))]
[ProtoInclude(tag:2155,knownType:typeof(Lucifer.ActCore.HeroSelfChessDataClear))]
[ProtoInclude(tag:2156,knownType:typeof(Lucifer.ActCore.SetBuffStatus))]
[ProtoInclude(tag:2157,knownType:typeof(Lucifer.ActCore.SkillGridVertexClear))]
[ProtoInclude(tag:2158,knownType:typeof(Lucifer.ActCore.ProjectileSaveSelfChessDataGridVertex))]
[ProtoInclude(tag:2159,knownType:typeof(Lucifer.ActCore.ProjectileClearSelfChessDataGridVertex))]
[ProtoInclude(tag:2160,knownType:typeof(Lucifer.ActCore.SwitchHeroStar))]
[ProtoInclude(tag:2161,knownType:typeof(Lucifer.ActCore.GetBFProjectileGroupNum))]
[ProtoInclude(tag:2162,knownType:typeof(Lucifer.ActCore.Add2BFProjectileGroup))]
[ProtoInclude(tag:2163,knownType:typeof(Lucifer.ActCore.RemoveBFProjectileGroup))]
[ProtoInclude(tag:2164,knownType:typeof(Lucifer.ActCore.GetSkillExtendLevel))]
[ProtoInclude(tag:2165,knownType:typeof(Lucifer.ActCore.GetBuffLayer))]
[ProtoInclude(tag:2166,knownType:typeof(Lucifer.ActCore.GetActiveFetter))]
[ProtoInclude(tag:2167,knownType:typeof(Lucifer.ActCore.IsHasBuffTag))]
[ProtoInclude(tag:2168,knownType:typeof(Lucifer.ActCore.SummonWaitHeroEvent))]
[ProtoInclude(tag:2169,knownType:typeof(Lucifer.ActCore.ChangeAI))]
[ProtoInclude(tag:2170,knownType:typeof(Lucifer.ActCore.MultiExcuteEvent))]
[ProtoInclude(tag:2171,knownType:typeof(Lucifer.ActCore.GetRunningAIName))]
[ProtoInclude(tag:2172,knownType:typeof(Lucifer.ActCore.GetMergeHeroLevel))]
[ProtoInclude(tag:2173,knownType:typeof(Lucifer.ActCore.ChangeColor))]
[ProtoInclude(tag:2174,knownType:typeof(Lucifer.ActCore.CheckFindTarget))]
[ProtoInclude(tag:2175,knownType:typeof(Lucifer.ActCore.SetSkillReleaseTagConst))]
[ProtoInclude(tag:2176,knownType:typeof(Lucifer.ActCore.GetSkillReleaseTagConst))]
[ProtoInclude(tag:2177,knownType:typeof(Lucifer.ActCore.StartSkillLock))]
[ProtoInclude(tag:2178,knownType:typeof(Lucifer.ActCore.EndSkillLock))]
[ProtoInclude(tag:2179,knownType:typeof(Lucifer.ActCore.ReleaseSkillTrigger))]
[ProtoInclude(tag:2180,knownType:typeof(Lucifer.ActCore.RefreshBattleFieldFetters))]
[ProtoInclude(tag:2181,knownType:typeof(Lucifer.ActCore.ControlSkillTarget))]
[ProtoInclude(tag:2182,knownType:typeof(Lucifer.ActCore.MoveToNearestGrid))]
[ProtoInclude(tag:2183,knownType:typeof(Lucifer.ActCore.ChangeActionSpeed))]
[ProtoInclude(tag:2184,knownType:typeof(Lucifer.ActCore.FindProjectileTarget))]
[ProtoInclude(tag:2185,knownType:typeof(Lucifer.ActCore.GetProjectile))]
[ProtoInclude(tag:2186,knownType:typeof(Lucifer.ActCore.CheckIsCanKillTarget))]
[ProtoInclude(tag:2187,knownType:typeof(Lucifer.ActCore.CheckHeroIds))]
[ProtoInclude(tag:2188,knownType:typeof(Lucifer.ActCore.CheckNeighborHasTargets))]
[ProtoInclude(tag:2189,knownType:typeof(Lucifer.ActCore.GetTargetAttribute))]
[ProtoInclude(tag:2190,knownType:typeof(Lucifer.ActCore.GetProjectileProperty))]
[ProtoInclude(tag:2191,knownType:typeof(Lucifer.ActCore.SetProjectileProperty))]
[ProtoInclude(tag:2192,knownType:typeof(Lucifer.ActCore.IsSkillTargetValid))]
[ProtoInclude(tag:2193,knownType:typeof(Lucifer.ActCore.IsTargetBcValid))]
[ProtoInclude(tag:2194,knownType:typeof(Lucifer.ActCore.IsCellValid))]
[ProtoInclude(tag:2195,knownType:typeof(Lucifer.ActCore.CheckSummonState))]
[ProtoInclude(tag:2196,knownType:typeof(Lucifer.ActCore.GetSummon))]
[ProtoInclude(tag:2197,knownType:typeof(Lucifer.ActCore.CheckAttackDistance))]
[ProtoInclude(tag:2198,knownType:typeof(Lucifer.ActCore.CheckIsHomeHero))]
[ProtoInclude(tag:2199,knownType:typeof(Lucifer.ActCore.GetBattleFieldState))]
[ProtoInclude(tag:2200,knownType:typeof(Lucifer.ActCore.GetGridUnit))]
[ProtoInclude(tag:2201,knownType:typeof(Lucifer.ActCore.CheckPosOutGrid))]
[ProtoInclude(tag:2202,knownType:typeof(Lucifer.ActCore.GetGeneric))]
[ProtoInclude(tag:2203,knownType:typeof(Lucifer.ActCore.CheckGeneric))]
[ProtoInclude(tag:2204,knownType:typeof(Lucifer.ActCore.ExecuteGeneric))]
[ProtoInclude(tag:2205,knownType:typeof(Lucifer.ActCore.ExecuteGenericView))]
[ProtoInclude(tag:2206,knownType:typeof(Lucifer.ActCore.GetBattleRecordGridX))]
[ProtoInclude(tag:2207,knownType:typeof(Lucifer.ActCore.GetBattleRecordGridY))]
[ProtoInclude(tag:2208,knownType:typeof(Lucifer.ActCore.IsDebonairVIP))]
[ProtoInclude(tag:2209,knownType:typeof(Lucifer.ActCore.GetResultBySymbol))]
[ProtoInclude(tag:2210,knownType:typeof(Lucifer.ActCore.GetCelebrateEffect))]
[ProtoInclude(tag:2211,knownType:typeof(Lucifer.ActCore.GetSkillId_Zoe_S7))]
[ProtoInclude(tag:2212,knownType:typeof(Lucifer.ActCore.RefreshSkill_Zoe_S7))]
[ProtoInclude(tag:2213,knownType:typeof(Lucifer.ActCore.DoViewContainerThing))]
[ProtoInclude(tag:2214,knownType:typeof(Lucifer.ActCore.SetGridState))]
[ProtoInclude(tag:2215,knownType:typeof(Lucifer.ActCore.GetUnitProperty))]
[ProtoInclude(tag:2216,knownType:typeof(Lucifer.ActCore.GetNewArray))]
[ProtoInclude(tag:2217,knownType:typeof(Lucifer.ActCore.WaitPosControl))]

    public class TimeEventProType
    {

        //类型对应的index, runtime时赋值;
        [ProtoMember(1)]
        public int ItemTypeIndex;

        /// <summary>
        /// 开始时间 [0-1] 百分比
        /// </summary>
        [ProtoMember(2)]
        public float StartTime;

        /// <summary>
        /// 开始帧数 0 --- 用于编辑。保存config时会把 frame 转换成 time。
        /// </summary>
        [ProtoMember(3)]
        public int StartFrame;
        /// <summary>
        /// 结束帧数 0 --- --- 用于编辑。保存config时会把 frame 转换成 time。
        /// </summary>
        [ProtoMember(4)]
        public int EndFrame;

        /// <summary>
        /// 执行间隔
        /// </summary>
        [ProtoMember(5)]
        public int durationInterval = 1;

        /// <summary>
        /// 结束时间 [0 - 1]
        /// </summary>
		[ProtoMember(6)]
		public float EndTime;


        /// <summary>
        /// 开始时间 seconds
        /// </summary>
        //public GameFramework.FMath.Fix64 startPosition;
        /// <summary>
        /// 时长 seconds
        /// </summary>
        //public GameFramework.FMath.Fix64 length;


      
        [ProtoMember(7)]
        public int index;
        /// <summary>
        /// 唯一id
        /// </summary>
        [ProtoMember(8)]
        public int gid;
        /// <summary>
        /// 是否在动作切换执行
        /// </summary>
        [ProtoMember(9)]
        public bool switchAction = false;
        /// <summary>
        /// 是否启用 -- 默认为true 启用
        /// </summary>
        [ProtoMember(10)]
        public bool enable = true;

        /// <summary>
        /// 是否是持续运行的 速度越快，执行的次数会越慢 action 的时长多少帧 就会执行多少帧
        /// </summary>
        [ProtoMember(11)]
        public bool isDuration;

        /// <summary>
        /// 另一种 持续运行 -- 永远是执行那么多帧
        /// </summary>
        [ProtoMember(12)]
        public bool isAbsoluteExecuteDuration;


        /// <summary>
        /// 是否技能结束执行
        /// </summary>
        [ProtoMember(13)]
        public bool isSkillEndExecute = false;

        /// <summary>
        /// 是否是开始就执行的
        /// </summary>
        [ProtoMember(14)]
        public bool startAction = false;

        /// <summary>
        /// 一个 执行周期 只执行一次
        /// </summary>
        [ProtoMember(15)]
        public bool onceEvent = false;
        /// <summary>
        /// link name
        /// </summary>
        [ProtoMember(16)]
        public string EventGroupId = "";
        /// <summary>
        /// 切换无视列表
        /// </summary>
        [ProtoMember(17)]
        public string[] IgnoreSwitchActionList;
        
        //		/// <summary>
        //		/// 编辑用，当数值被改变时，用此值参考排序
        //		/// </summary>
        //        public int sortNum = 0;

        /// <summary>
        /// 拥有者  已优化
        /// </summary>
        // public GameObject gameObject;

        //已优化
        //public bool executed;		
        //已优化
        //public Lucifer.ActCore.Action action;

        /// <summary>
        /// 是否已经释放 已优化
        /// </summary>
        //public bool disposed = false;

        /// <summary>
        /// 类型
        /// </summary>
        [ProtoMember(18)]
        public string ItemType;
        
        /// <summary>
        /// 预留的特殊参数列表
        /// </summary>
        [ProtoMember(19)]
        public AFCValue[]  specialParams  ;


        /// <summary>
        /// 条件模块中 取反
        /// </summary>
        [ProtoMember(20)]
        public bool Invert = false;

      
        
        
#if UNITY_EDITOR && !ENABLE_TYPE_TREE_IGNORE
        /// <summary>
        /// 编辑用 基础属性是否展开
        /// </summary>
        public bool isBaseAttrFold = false;
        /// <summary>
        /// 编辑用 派生属性是否展开
        /// </summary>
        public bool isExtendAttrFold = true;
        /// <summary>
        /// 编辑用 优化掉
        /// </summary>
        public bool isFold;
        /// <summary>
        /// 编辑用  优化掉
        /// </summary>
        public bool copyMark = false;

        /// <summary>
        /// 编辑用 优化掉
        /// </summary>
        public bool isShowDebugLog = false;
        /// <summary>
        /// 注释 编辑用  优化掉
        /// </summary>
        public string commondDesc;
        
        public string collectionName = "";
        
        /// <summary>
        /// 编辑用，Action属于哪一Module，Module是一系列Action的组合
        /// </summary>
        public string moduleName = "";
        /// <summary>
        /// 注释 编辑用  优化掉
        /// </summary>
        public string ModuleDesc = "";
        
        /// <summary>
        /// 编辑模板
        /// </summary>
        public string Edit_Module;
        [NonSerialized]
        public bool dirty;

        //
        [NonSerialized]
        public List<string> dirtyAttribute = new List<string>();

        public void Compare(TimeEventProType other)
        {
            
        }

        public void ClearDirtyData()
        {
            dirty = false;
            dirtyAttribute.Clear();
        }
        
#endif

        //#if UNITY_EDITOR
#if UNITY_EDITOR && ACGGAME_CLIENT && !ENABLE_TYPE_TREE_IGNORE

        public TimeEventProType_Desiption Description;

#endif
        //#endif


        public TimeEventProType()
        {
            if (gid == 0)
                GenerateNewGid();
        }

        public void GenerateNewGid()
        {
            gid = GetHashCode();
        }
    }

    public class SortPositionId : IComparer<TimeEventProType>
    {
        int IComparer<TimeEventProType>.Compare(TimeEventProType a, TimeEventProType b)
        {
#if UNITY_EDITOR && ACGGAME_CLIENT && !ENABLE_TYPE_TREE_IGNORE
            if (a.Description != null && b.Description != null)
            {
                return (a.index - b.index) * 100 + (a.Description.sortNum - b.Description.sortNum);
            }
            else
            {
                if (a.index < b.index)
                {
                    return -1;
                }
                else if (a.index > b.index)
                {
                    return 1;
                }
                return 0;
            }
#else
            return (a.index - b.index);
#endif
        }
    }
#if UNITY_EDITOR 
    [Serializable]
    public class ActionModule
    {
        public const string Default_Module = "Default_Module";

        public bool isHide = false;
        
        public string name;
        public string desc;
        
        [NonSerialized]
        public List<TimeEventProType> ModuleEventList;

        public ActionModule(string name, string desc)
        {
            this.name = name;
            this.desc = desc;
            ModuleEventList = new List<TimeEventProType>();
        }
    }
#endif
}