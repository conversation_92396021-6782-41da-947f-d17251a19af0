/* This file is generated by tdr. */
/* No manual modification is permitted. */

/* metalib version: 2 */
/* metalib md5sum: 4f6dd29a07f7fb1693909370f6607710 */

/* creation time: Thu Apr 11 11:12:36 2019 */
/* tdr version: 2.7.12, build at 20151212 */
using System;
using ProtoBuf;
using Lucifer.ActCore;
using static CharacterHangPoint;

namespace TDRConfig
{
    public class MACROS_KEYWORDS
    {
    }


    public enum BATTLE_MESSAGE_TYPE
    {
        BATTLE_MESSAGE_TYPE_BATTLE_DICT = 189999,
        BATTLE_MESSAGE_TYPE_HP_CHANGE = 180000,
        BATTLE_MESSAGE_TYPE_USE_SKILL = 180001,
        BATTLE_MESSAGE_TYPE_NORAML_ATK = 180002,
        BATTLE_MESSAGE_TYPE_SET_TARGET = 180003,
        BATTLE_MESSAGE_TYPE_ENABLE_AI = 180004,
        BATTLE_MESSAGE_TYPE_MOVE = 180005,
        BATTLE_MESSAGE_TYPE_TIRGGER_BUFF = 180006,
        BATTLE_MESSAGE_TYPE_JUMP = 180007,
        BATTLE_MESSAGE_TYPE_LAUNCH_PROJECTILE = 180008,
        BATTLE_MESSAGE_TYPE_ATTR_CHANGE = 180009,
        BATTLE_MESSAGE_TYPE_ADD_BUFF = 180010,
        BATTLE_MESSAGE_TYPE_BUFF_HURT = 180011,
        BATTLE_MESSAGE_TYPE_CLIENT_SYNC = 180012,
        BATTLE_MESSAGE_TYPE_GRID_MOVE = 180013,
        BATTLE_MESSAGE_TYPE_ATTR_SCALE_CHANGE = 180014,
        BATTLE_MESSAGE_TYPE_HERO_DEATH = 180015,
        BATTLE_MESSAGE_TYPE_USE_BP = 180016,
    }

    public enum SEX_DEF
    {
        RES_MACRO_MALE = 0,
        RES_MACRO_FEMALE = 1,
    }

    public enum BODY_DEF
    {
        RES_MACRO_ADULTMALE = 1,
        RES_MACRO_ADULTFEMALE = 2,
        RES_MACRO_LOLI = 3,
        RES_MACRO_ODDUNCLE = 4,
    }

    public enum PARA_TYPE
    {
        [EnumName("数值类型_整数")] PARA_TYPE_INTEGER = 1, // 数值类型_整数
        [EnumName("数值类型_百分比")] PARA_TYPE_PER100 = 2, // 数值类型_百分比
        [EnumName("数值类型_千分比")] PARA_TYPE_PER1000 = 3, // 数值类型_千分比
        [EnumName("数值类型_万分比")] PARA_TYPE_PER10000 = 4, // 数值类型_万分比
        [EnumName("数值类型_ID")] PARA_TYPE_THINGID = 5, // 数值类型_ID
    }

    public enum MONEY_TYPE
    {
        MONEY_KING_TICKET = 1, // 全职点券
        MONEY_KING_GOLD = 2, // 全职金币
        MONEY_GLORY_TICKET = 3, // 荣耀点券
        MONEY_GLORY_GOLD = 4, // 荣耀金币
        MONEY_GLORY_SILVER = 5, // 荣耀银币
        MAX_MONEY_TYPE = 6, // 最大值（无效）
    }

    public enum MONEY_TYPE_MAX
    {
        MAX_KING_MONEY_TYPE = 3, // 全职货币最大值（无效）
    }

    public enum MONEY_EXCHANGE_RATE1
    {
        KING_TICKET_TO_KING_GOLD = 100, // 全职点券兑换全职金币
    }

    public enum MONEY_EXCHANGE_RATE2
    {
        KING_TICKET_TO_GLORY_TICKET = 1, // 全职点券兑换荣耀点券
    }

    public enum MONEY_EXCHANGE_RATE3
    {
        GLORY_TICKET_TO_GLORY_GOLD = 100, // 荣耀点券兑换荣耀金币
    }

    public enum MONEY_EXCHANGE_RATE4
    {
        GLORY_GOLD_TO_GLORY_SILVER = 100, // 荣耀金币兑换荣耀银币
    }

    public enum RESOURCE_TYPE
    {
        RESOURCE_LP = 1, // 体力
        RESOURCE_SKILL_POINT = 2, // 技能点
    }

    public enum BUY_LIMIT_TYPE
    {
        MARKET_BUY_LIMIT_NULL = 0, // 无意义
        MARKET_BUY_LIMIT_DAY = 1, // 每日限购
        MARKET_BUY_LIMIT_WEEK = 2, // 每周限购
        MARKET_BUY_LIMIT_PERMANENT = 3, // 永久限购
        MARKET_BUY_LIMIT_MAX = 4, // 最大值（无效）
    }

    public enum ATTRIBUTE_DEF
    {
        [EnumName("代表无属性")] ATTR_NONE = 0, //代表无属性;
        [EnumName("当前生命")] ATTR_HP_NOW = 1, // 当前生命
        [EnumName("生命上限")] ATTR_HP_MAX = 2, // 生命上限
        [EnumName("当前魔法")] ATTR_MP = 3, // 当前魔法
        [EnumName("魔法上限")] ATTR_MP_MAX = 4, // 魔法上限
        [EnumName("攻击恢复魔法")] ATTR_ATK_REPLY_MP = 5,
        [EnumName("护盾值")] ATTR_SHIELD = 6, // 护盾值
        [EnumName("每秒生命恢复值(废弃)")] ATTR_HP_REGENERATION = 7, // 每秒生命恢复值
        [EnumName("格挡值")] ATTR_BLOCK = 8, // 格挡值
        [EnumName("格挡发生概率")] ATTR_BLOCK_RATE = 9, // 格挡发生概率
        [EnumName("攻击力")] ATTR_ATTACK = 10, // 攻击力
        [EnumName("攻击速度上限")] ATTR_ATK_SPEED_MAX = 11, // 攻击速度上限
        [EnumName("攻击距离")] ATTR_ATK_RANGE = 12, // 攻击距离
        [EnumName("攻击速度")] ATTR_ATK_SPEED = 13, // 攻击速度, 多次减攻速可能为负数
        [EnumName("攻击间隔")] ATTR_ATK_INTERVAL = 14, // 攻击间隔
        [EnumName("护甲抗性")] ATTR_ARMOR_RESISTANCE = 15, // 护甲抗性
        [EnumName("魔法抗性")] ATTR_MAGIC_RESISTANCE = 16, // 魔法抗性
        [EnumName("护甲穿透百分比")] ATTR_ARMOR_PENETRATE_PERCENT = 17, // 护甲穿透百分比
        [EnumName("魔法穿透百分比")] ATTR_MAGIC_PENETRATE_PERCENT = 18, // 魔法穿透百分比
        [EnumName("护甲穿透")] ATTR_ARMOR_PENETRATE = 19, // 护甲穿透
        [EnumName("魔法穿透")] ATTR_MAGIC_PENETRATE = 20, // 魔法穿透
        [EnumName("生命保护（已弃用）")] ATTR_HP_MIN_PROTECT = 21, // 生命保护（低于不伤害）
        [EnumName("普攻附加伤害倍率(千分比)")] ATTR_ATK_ADD_RATE = 22, // 普攻附加伤害倍率(千分比)
        [EnumName("额外法强(千分比)")] ATTR_SKILL_ADD_RATE = 23, // 技能附加伤害倍率(千分比)
        [EnumName("暴击概率(百分比)")] ATTR_CRIT_PROBABILITY = 24, // 暴击概率(百分比)
        [EnumName("暴击倍率(百分比)")] ATTR_CRIT_VALUE = 25, // 暴击倍率(百分比)
        [EnumName("暴击伤害降低(百分比)")] ATTR_CRIT_REDUCTION = 26,
        [EnumName("计算伤害时总护甲百分比加成")] ATTR_ARMOR_RESISTANCE_PERCENT = 27, // 护甲抗性百分比
        [EnumName("计算伤害时总魔抗百分比加成")] ATTR_MAGIC_RESISTANCE_PERCENT = 28, // 魔法抗性百分比
        [EnumName("全附加伤害倍率百分比")] ATTR_ALL_HURT_ADD_RATE = 29, //全附加伤害倍率百分比
        [EnumName("技能附加伤害倍率百分比(失效弃用)")] ATTR_SKILL_ADD_RATE_PERCENT = 30, //技能附加伤害倍率百分比
        [EnumName("躲避技能暴击百分比")] ATTR_SKILL_CRIT_MISS = 32, //躲避技能暴击百分比
        [EnumName("技能附加伤害倍率固定值千分比(失效弃用)")] ATTR_SKILL_ADD_RATE_VALUE = 33, // 技能附加伤害倍率固定值(千分比)
        [EnumName("被暴击概率(百分比)")] ATTR_BE_CRIT_PROBABILITY = 34, // 被暴击概率(百分比)
        [EnumName("闪避概率(百分比)")] ATTR_ATK_MISS = 35, // 闪避概率(百分比)
        [EnumName("攻击必然命中率(百分比)")] ATTR_ATK_MUST_HIT = 36, // 攻击必然命中率(百分比)
        [EnumName("暴击不命中率(百分比)")] ATTR_CRIT_MISS = 37, // 暴击不命中率(百分比)
        [EnumName("全附加伤害倍率百分比")] ATTR_ALL_HURT_ADD_RATE_SEC = 38, //全附加伤害倍率百分比(正义之拳用)
        [EnumName("子弹")] ATTR_BULLET = 39, // 子弹
        [EnumName("子弹Max")] ATTR_BULLET_MAX = 40, // 子弹
        [EnumName("跑动速度")] ATTR_RUN_SPEED = 41, // 跑动速度
        [EnumName("基础跑动速度")] ATTR_BASE_RUN_SPEED = 42, // 基础跑动速度
        [EnumName("普攻吸血百分比值")] ATTR_LIFE_STEAL = 50, // 普攻吸血百分比值
        [EnumName("降低造成伤害的回蓝量比例")] ATTR_SUB_ATTACK_MP_ADD_SCALE = 51, // 降低造成伤害的回蓝量比例
        [EnumName("降低受到攻击时的回蓝量比例(如20表示，减少20%受击回蓝)")] ATTR_SUB_BEATTACKED_MP_ADD_SCALE = 52, // 降低受到攻击时的回蓝量比例
        [EnumName("重伤(减伤治疗效果千分比)")] ATTR_SERIOUS_INJURY = 53, // 重伤(减伤治疗效果千分比)
        [EnumName("技能吸血百分比值")] ATTR_SKILL_LIFE_STEAL = 54, // 技能吸血百分比值
        [EnumName("范围控制buff时间增减比例(百分比)")] ATTR_CONTROL_BUFF_TIME_RATE = 56, // 范围控制buff时间增减比例(百分比)
        [EnumName("伤害上限")] ATTR_HURT_MAX = 58, // 伤害上限
        // [EnumName("全伤害减免")] ATTR_ALL_HURT_REDUCE_VALUE = 59, //全伤害减免
        [EnumName("主动闪避概率(百分比)")] ATTR_ACTIVE_ATK_MISS = 60, // 主动闪避概率(百分比)
        [EnumName("易伤状态(额外受伤害百分比)")] ATTR_BE_ALL_HURT_ADD_RATE_SEC = 61, //全附加受伤害倍率百分比
        [EnumName("减弱护盾效果(千分比)")] ATTR_REDUCE_SHIELD_EFFECT = 62, // 减弱护盾效果(千分比)
        [EnumName("每秒自我灼烧伤害")] ATTR_DAMAGE_PER_SECOND = 63,
        [EnumName("给他人回复、护盾强度(千分比)")] ATTR_HEAL_AND_SHIELD_POWER = 64,
        [EnumName("魔法护盾值")] ATTR_MAGIC_SHIELD = 65,
        [EnumName("ap修正值(千分比)")] ATTR_AP_MODIFIER = 66,
        [EnumName("额外攻击力(千分比)")] ATTR_ATTACK_RATE = 67,
        [EnumName("额外回蓝(千分比)")] ATTR_MAGIC_REPLY_RATE = 68,
        [EnumName("降低的伤害(千分比)")] ATTR_SUB_DAMAGE = 69,
        [EnumName("受到的额外治疗回复强度(千分比)")] ATTR_HEAL_POWER = 70,
        [EnumName("受到的额外添加护盾强度(千分比)")] ATTR_SHIELD_POWER = 71,
        [EnumName("受到的额外护甲加成(千分比)")] ATTR_EXTRA_ARMOR_RESISTANCE_RATE = 72,
        [EnumName("受到的额外魔抗加成(千分比)")] ATTR_EXTRA_MAGIC_RESISTANCE_RATE = 73,
        [EnumName("额外攻击力(整数)(最后加上)")] ATTR_ATTACK_INTEGER = 74,
        [EnumName("带被动的技能可暴击效果数量(仅用于属性转换)")] ATTR_SKILL_CRIT_COUNT_WITH = 75,
        [EnumName("不带被动的技能可暴击效果数量(仅用于属性转换)")] ATTR_SKILL_CRIT_COUNT = 76,
        [EnumName("MAX")] ATTR_END, //自动赋值，无须指定
    }

    //复合型属性(多次处理,非叠加)
    public enum ATTRIBUTE_COMPLEX_DEF
    {
        [EnumName("无")] NONE = 0,

        [EnumName("距离增伤属性(伤害加成)")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_COMPLEX_DIS_ADD_HURT)]
        [ST_Param("类型（1格子距离，2真实距离单位厘米）", defaultValue = 1)]
        [ST_Param("增伤十万分比", defaultValue = 0)]
        [ST_Param("最小十万分比", defaultValue = 0)]
        [ST_Param("攻击类型过滤", defaultValue = AttackType.All)]
        ATTR_COMPLEX_DIS_ADD_HURT = 1, //距离增伤属性 1 | 类型（1格子距离，2真实距离单位厘米） | 增伤十万分比 | 最小十万分比 | attackType(默认-1)

        [EnumName("血量导致暴击结果")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_COMPLEX_COMPARE_HP_CRIT)]
        [ST_Param("数值", defaultValue = 0)]
        [ST_Param("数值类型", defaultValue = PARA_TYPE.PARA_TYPE_INTEGER)]
        [ST_Param("对比（0小于1大于）", defaultValue = 0)]
        [ST_Param("是否是攻击者", defaultValue = false)]
        [ST_Param("对应攻击类型", defaultValue = AttackType.All)]
        ATTR_COMPLEX_COMPARE_HP_CRIT = 3, //血量导致暴击结果 3| 数值 | 数值类型 | 对比（0小于1大于）| 是否是攻击者

        [EnumName("伤害回复")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_COMPLEX_ORGHURT_REPLY)]
        [ST_Param("数值", defaultValue = 0)]
        [ST_Param("数值类型", defaultValue = PARA_TYPE.PARA_TYPE_INTEGER)]
        [ST_Param("飘字下限", defaultValue = 0)]
        [ST_Param("伤害阶段类型", defaultValue = DamageRecordStage.RealDamage)]
        [ST_Param("是否受击生效", defaultValue = false)]
        [ST_Param("对应攻击类型", defaultValue = AttackType.HeroAttack)]
        [ST_Param("回血的目标选择（不选是攻击者）", defaultValue = BUFF_EFFECT_TARGET.NONE)]        
        [ST_Param("过滤类型", defaultValue = AddHurtFilterType.None)]
        [ST_Param("过滤参数1", defaultValue = -1)]
        [ST_Param("过滤参数2", defaultValue = -1)]
        [ST_Param("过滤参数3", defaultValue = -1)]
        [ST_Param("过滤参数4", defaultValue = -1)]
        [ST_Param("过滤参数5", defaultValue = -1)]
        ATTR_COMPLEX_ORGHURT_REPLY = 4, //原始伤害回复（忽略抗性） 4|数值|数值类型|飘字下限|DamageRecordStage

        [EnumName("附加伤害(额外一次攻击流程)")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_COMPLEX_ADDITIONAL_HURT)]
        [ST_Param("对应攻击类型", defaultValue = AttackType.All)]
        [ST_Param("伤害类型", defaultValue = DamageType.None)]
        [ST_Param("伤害千分比", defaultValue = 0)]
        [ST_Param("血量限制", defaultValue = -1)]
        [ST_Param("超出替换伤害千分比", defaultValue = -1)]
        [ST_Param("附加伤害的攻击类型", defaultValue = -1)]
        [ST_Param("原伤害是否取暴击", defaultValue = true)]
        ATTR_COMPLEX_ADDITIONAL_HURT =
            5, //附加伤害 5|对应攻击类型AttackType|伤害类型DamageType|伤害千分比|血量限制|超出替换伤害千分比|附加伤害的攻击类型(-1按原类型)|附加伤害类型(-1按原类型)

        [EnumName("变换对应攻击阶段的伤害类型")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_COMPLEX_CHANGE_DAMAGE_TYPE)]
        [ST_Param("对应攻击类型", defaultValue = AttackType.All)]
        [ST_Param("伤害类型", defaultValue = DamageType.None)]
        [ST_Param("过滤类型", defaultValue = AddHurtFilterType.None)]
        [ST_Param("过滤参数1", defaultValue = -1)]
        [ST_Param("过滤参数2", defaultValue = -1)]
        [ST_Param("过滤参数3", defaultValue = -1)]
        [ST_Param("过滤参数4", defaultValue = -1)]
        [ST_Param("过滤参数5", defaultValue = -1)]
        ATTR_COMPLEX_CHANGE_DAMAGE_TYPE = 6, //变换对应攻击阶段的伤害类型 6|对应攻击类型TriggerAttackType|伤害类型DamageType

        [EnumName("反伤")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_COMPLEX_COUNTER_INJURY)]
        [ST_Param("触发攻击类型", defaultValue = AttackType.All)]
        [ST_Param("触发伤害类型", defaultValue = DamageType.None)]
        [ST_Param("伤害阶段类型", defaultValue = DamageRecordStage.RealDamage)]
        [ST_Param("反伤伤害类型", defaultValue = DamageType.Pure)]
        [ST_Param("反伤千分比", defaultValue = 0)]
        ATTR_COMPLEX_COUNTER_INJURY = 7, //反伤 7 | 触发攻击类型 | 触发伤害类型 | 伤害阶段类型DamageRecordStage|反伤伤害类型|反伤千分比

        [EnumName("复活")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_COMPLEX_REBORN)]
        [ST_Param("hp(比例时为百分比)", defaultValue = 0)]
        [ST_Param("lv", defaultValue = 0)]
        [ST_Param("复活时间（毫秒）", defaultValue = 200)]
        [ST_Param("是否检测场上还有英雄", defaultValue = false)]
        [ST_Param("是否不播放dead动作", defaultValue = false)]
        [ST_Param("是否按比例回血", defaultValue = false)]
        ATTR_COMPLEX_REBORN = 9, //复活相关属性 9 | hp | lv |复活时间（毫秒）|是否检测场上还有英雄（默认false）

        [EnumName("护盾")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_COMPLEX_SHIELD)]
        [ST_Param("数值", defaultValue = 0)]
        [ST_Param("是否ap加成", defaultValue = false)]
        [ST_Param("护盾对应伤害类型", defaultValue = DamageType.None)]
        [ST_Param("是否叠加原有buff护盾", defaultValue = false)]
        [ST_Param("是否忽略护盾加减成、暴击相关计算", defaultValue = 0)]
        ATTR_COMPLEX_SHIELD = 10, //护盾属性 10 | 数值 | 是否ap加成(默认否)|DamageType护盾对应伤害类型|是否叠加原有buff护盾（默认false）

        [EnumName("吸血转换护盾")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_COMPLEX_STEAL_LIFT_TO_SHIELD)]
        [ST_Param("攻击类型", defaultValue = AttackType.All)]
        [ST_Param("触发伤害类型", defaultValue = DamageType.None)]
        [ST_Param("当前吸血条目的数量", defaultValue = 0)]
        [ST_Param("当前条目单件吸血属性基础值", defaultValue = 0)]
        [ST_Param("护盾上限基础值", defaultValue = 0)]
        [ST_Param("是否按条目吸血属性占比计算（0单独条目1全部）", defaultValue = false)]
        [ST_Param("转换比例（百分比）", defaultValue = 100)]
        [ST_Param("buffCfgId(移除触发需配成破盾)", defaultValue = 0)]
        [ST_Param("公式用于护盾上限基础值(默认为吸血条目数量)", defaultValue = false)]
        ATTR_COMPLEX_STEAL_LIFT_TO_SHIELD =
            11, //装备吸血转换护盾 11 | 攻击类型AttackType | 装备cfgId | 吸血属性基础值 | 护盾上限基础值 | 是否计算全部吸血（0单独1全部） | buffCfgId(移除触发需配成破盾)

        [EnumName("伤害减免（多次叠加相乘）")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_COMPLEX_MULT_SKILL_REDUCE_RATE)]
        [ST_Param("触发攻击类型", defaultValue = AttackType.All)]
        [ST_Param("触发伤害类型", defaultValue = DamageType.None)]
        [ST_Param("范围类型过滤", defaultValue = HitRangeType.None)]
        [ST_Param("减免数值", defaultValue = -1)]
        [ST_Param("是否整数值(否则为百分比)", defaultValue = false)]
        [ST_Param("是否检测阻挡（角度）", defaultValue = false)]
        [ST_Param("记录key（用于记录减免值）", defaultValue = -1)]
        [ST_Param("是否ap加成", defaultValue = false)]
        [ST_Param("数据处理方式（用于记录的方式）", defaultValue = VariableValueDealType.Replace)]
        [ST_Param("附加处理", defaultValue = AfterRecordDeadType.None)]
        [ST_Param("参数1", defaultValue = -1)]
        [ST_Param("参数2", defaultValue = -1)]
        [ST_Param("过滤类型", defaultValue = AddHurtFilterType.None)]
        [ST_Param("过滤参数1", defaultValue = -1)]
        [ST_Param("过滤参数2", defaultValue = -1)]
        [ST_Param("过滤参数3", defaultValue = -1)]
        [ST_Param("过滤参数4", defaultValue = -1)]
        [ST_Param("本次减免伤害是否飘字(减到0不飘)", defaultValue = 0)]
        [ST_Param("特殊ap加成(与普通的ap加成只有勾一个)", defaultValue = false)]
        [ST_Param("过滤参数5", defaultValue = -1)]
        [ST_Param("伤害减免的阶段(支持计算护盾前和计算抗性前)", defaultValue = SeckillStageType.BeforeCalShield)]        
        // 保证加不到百分百减伤
        ATTR_COMPLEX_MULT_SKILL_REDUCE_RATE = 13,

        [EnumName("伤害转护盾")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_COMPLEX_HURT_TO_SHIELD)]
        [ST_Param("触发攻击类型", defaultValue = AttackType.All)]
        [ST_Param("触发伤害类型", defaultValue = DamageType.None)]
        [ST_Param("伤害阶段类型", defaultValue = DamageRecordStage.RealDamage)]
        [ST_Param("伤害转换百分比", defaultValue = 0)]
        [ST_Param("对应护盾buffCfgid", defaultValue = 0)]
        [ST_Param("是否刷新护盾buff时间", defaultValue = false)]
        ATTR_COMPLEX_HURT_TO_SHIELD = 14, //伤害转护盾 14|触发攻击类型|触发伤害类型|伤害阶段类型|伤害转换百分比|对应护盾buffCfgid

        [EnumName("附加对相关目标伤害")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_COMPLEX_HURT_TO_BUFF_RECORD_TARGET)]
        [ST_Param("触发攻击类型", defaultValue = AttackType.All)]
        [ST_Param("触发伤害类型", defaultValue = DamageType.None)]
        [ST_Param("伤害阶段类型", defaultValue = DamageRecordStage.RealDamage)]
        [ST_Param("伤害转换百分比", defaultValue = 0)]
        [ST_Param("伤害类型", defaultValue = DamageType.None)]
        [ST_Param("记录key类型", defaultValue = RecordKeyType.None)]
        [ST_Param("记录参数", defaultValue = 0)]
        [ST_Param("是否移除记录", defaultValue = false)]
        ATTR_COMPLEX_HURT_TO_BUFF_RECORD_TARGET =
            15, //附加对相关目标伤害 15|触发攻击类型|触发伤害类型|伤害阶段类型|伤害转换百分比|伤害类型|记录key类型|记录参数|是否移除记录

        [EnumName("斩杀")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_COMPLEX_SECKILL)]
        [ST_Param("触发攻击类型", defaultValue = AttackType.All)]
        [ST_Param("触发伤害类型", defaultValue = DamageType.None)]
        [ST_Param("计算阶段", defaultValue = SeckillStageType.AfterHurt)]
        [ST_Param("hp下限", defaultValue = 0)]
        [ST_Param("是否数值（默认false-百分比）", defaultValue = false)]
        [ST_Param("转换伤害类型（默认不转）", defaultValue = DamageType.None)]
        ATTR_COMPLEX_SECKILL = 17, //斩杀 17|触发攻击类型|触发伤害类型|是否伤害前判断（默认false）|hp下限|是否数值（默认false-百分比）|转换伤害类型（默认不转）

        [EnumName("均摊伤害")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_COMPLEX_SHARE_HURT)]
        [ST_Param("触发攻击类型", defaultValue = AttackType.All)]
        [ST_Param("触发伤害类型", defaultValue = DamageType.None)]
        [ST_Param("伤害阶段类型", defaultValue = DamageRecordStage.RealDamage)]
        [ST_Param("伤害转换百分比", defaultValue = 0)]
        [ST_Param("目标选择参数1", defaultValue = 0)]
        [ST_Param("目标选择参数2", defaultValue = 0)]
        [ST_Param("目标选择参数3", defaultValue = 0)]
        ATTR_COMPLEX_SHARE_HURT = 18, //均摊伤害 18|触发攻击类型|触发伤害类型|伤害阶段类型|伤害转换百分比|BUFF_EFFECT_TARGET|specId|carrerId

        [EnumName("伤害加成")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_COMPLEX_SPECIAL_ADD_HURT)]
        [ST_Param("触发攻击类型", defaultValue = AttackType.All)]
        [ST_Param("触发伤害类型", defaultValue = DamageType.None)]
        [ST_Param("数值", defaultValue = 0)]
        [ST_Param("数值类型", defaultValue = PARA_TYPE.PARA_TYPE_INTEGER)]
        [ST_Param("条件类型", defaultValue = AddHurtFilterType.None)]
        [ST_Param("条件参数1", defaultValue = -1)]
        [ST_Param("条件参数2", defaultValue = -1)]
        [ST_Param("条件参数3", defaultValue = -1)]
        [ST_Param("条件参数4", defaultValue = -1)]
        [ST_Param("条件参数5", defaultValue = -1)]
        [ST_Param("条件参数6", defaultValue = -1)]
        [ST_Param("条件参数7", defaultValue = -1)]
        [ST_Param("条件参数8", defaultValue = -1)]
        ATTR_COMPLEX_SPECIAL_ADD_HURT = 19, // 19|触发攻击类型|触发伤害类型|数值|数值类型|过滤类型AddHurtFilterType|参数1|参数2|参数3..

        [EnumName("伤害记录处理")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_COMPLEX_HURT_RECORD)]
        [ST_Param("触发攻击类型", defaultValue = AttackType.All)]
        [ST_Param("触发伤害类型", defaultValue = DamageType.None)]
        [ST_Param("是否是攻击判定", defaultValue = false)]
        [ST_Param("伤害阶段类型", defaultValue = DamageRecordStage.RealDamage)]
        [ST_Param("记录的key(buff变量记录公式能取到)", defaultValue = 0)]
        [ST_Param("数据处理方式", defaultValue = VariableValueDealType.Replace)]
        [ST_Param("附加处理", defaultValue = AfterRecordDeadType.None)]
        [ST_Param("参数1", defaultValue = -1)]
        [ST_Param("参数2", defaultValue = -1)]
        ATTR_COMPLEX_HURT_RECORD =
            20, // 20|触发攻击类型|触发伤害类型|是否是攻击判定|伤害阶段类型|记录的key|数据处理方式VariableValueDealType|附加处理AfterRecordDeadType|参数1|参数2

        [EnumName("魔法护盾转换")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_MAGIC_SHIELD_TO_NORMAL_SHIELD)]
        [ST_Param("触发攻击类型", defaultValue = AttackType.All)]
        [ST_Param("转换百分比", defaultValue = 0)]
        ATTR_MAGIC_SHIELD_TO_NORMAL_SHIELD = 21,

        [EnumName("抵挡技能")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_RESIST_SKILL_EFFECT)]
        [ST_Param("抵消技能伤害", defaultValue = 0)]
        [ST_Param("是否按百分比计算", defaultValue = false)]
        [ST_Param("触发攻击类型(预留暂不使用)", defaultValue = AttackType.SkillAttack)]
        ATTR_RESIST_SKILL_EFFECT = 22,

        [EnumName("简单伤害预计算（成功触发对应buff事件BT_INJURY_PRE_CALCULATION_SUC）")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_SIMPLE_INJURY_PRE_CALCULATION)]
        [ST_Param("触发攻击类型", defaultValue = AttackType.All)]
        [ST_Param("触发伤害类型", defaultValue = DamageType.None)]
        [ST_Param("伤害阶段", defaultValue = InjuryCalculationStage.None)]
        [ST_Param("触发事件id", defaultValue = 0)]
        [ST_Param("预计算方式", defaultValue = InjuryPreCalType.None)]
        [ST_Param("参数", defaultValue = 0)]
        [ST_Param("参数", defaultValue = 0)]
        [ST_Param("参数", defaultValue = 0)]
        [ST_Param("参数", defaultValue = 0)]
        ATTR_SIMPLE_INJURY_PRE_CALCULATION = 23,

        [EnumName("超出血量限制伤害减免（多次叠加相乘）")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_COMPLEX_REDUCE_DAMAGE_OVER_HP_LIMIT)]
        [ST_Param("触发攻击类型", defaultValue = AttackType.All)]
        [ST_Param("触发伤害类型", defaultValue = DamageType.None)]
        [ST_Param("减免数值", defaultValue = -1)]
        [ST_Param("是否整数值", defaultValue = false)]
        [ST_Param("血量限制数值", defaultValue = -1)]
        [ST_Param("是否整数值", defaultValue = false)]
        [ST_Param("是否ap加成", defaultValue = false)]
        [ST_Param("过滤类型", defaultValue = AddHurtFilterType.None)]
        [ST_Param("过滤参数1", defaultValue = -1)]
        [ST_Param("过滤参数2", defaultValue = -1)]
        [ST_Param("过滤参数3", defaultValue = -1)]
        [ST_Param("过滤参数4", defaultValue = -1)]
        [ST_Param("过滤参数5", defaultValue = -1)]
        ATTR_COMPLEX_REDUCE_DAMAGE_OVER_HP_LIMIT = 24,
        
        [EnumName("伤害传递(本体传递到目标（默认施法者，也可通过buffId去选择目标）)")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_COMPLEX_DAMAGE_TRANSFER)]
        [ST_Param("传递的攻击类型", defaultValue = AttackType.All)]
        [ST_Param("传递的伤害类型", defaultValue = DamageType.None)]
        [ST_Param("传递伤害的阶段(支持扣血前和计算伤害加成前)", defaultValue = SeckillStageType.BeforeHurt)]
        [ST_Param("传递的SkillProtoId", defaultValue = -1)]
        [ST_Param("本体伤害减免", defaultValue = 0)]
        [ST_Param("本体伤害减免的数值类型", defaultValue = PARA_TYPE.PARA_TYPE_PER100)]
        [ST_Param("本体传递到目标的伤害", defaultValue = 0)]
        [ST_Param("本体传递到目标的伤害数值类型", defaultValue = PARA_TYPE.PARA_TYPE_PER100)]
        [ST_Param("伤害参数设置枚举(设置后不计算某些伤害参数)", defaultValue = 0)]
        [ST_Param("伤害key值(用于匹配一些特殊效果,如伤害减免)", defaultValue = 0)]
        [ST_Param("是否传递攻击者来源（1默认传递）", defaultValue = 1)]
        [ST_Param("伤害传递buff的buffTag（0默认不传递）", defaultValue = 0)]
        [ST_Param("是否只传递一次", defaultValue = false)]
        [ST_Param("用于目标选择的buff原型Id(包括施法者)", defaultValue = -1)]
        [ST_Param("目标是否与施法者同阵营", defaultValue = true)]
        [ST_Param("是否飘字", defaultValue = true)]
        [ST_Param("是否所有目标均摊伤害", defaultValue = false)]
        ATTR_COMPLEX_DAMAGE_TRANSFER = 25,
        
        [EnumName("伤害参数开关")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_COMPLEX_DAMAGE_DAMAGE_PARAM)]
        [ST_Param("技能id", defaultValue = 0)]
        [ST_Param("是否作为攻击者(否则为受击者)", defaultValue = true)]
        [ST_Param("伤害参数类型", defaultValue = DamageContextParamType.None)]
        [ST_Param("设置的值", defaultValue = 0)]
        ATTR_COMPLEX_DAMAGE_DAMAGE_PARAM = 26,
        
        [EnumName("伤害类型转换")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.ATTR_COMPLEX_DAMAGE_TYPE_TRANSFORM)]
        [ST_Param("原伤害类型", defaultValue = DamageType.None)]
        [ST_Param("新伤害类型", defaultValue = DamageType.None)]
        [ST_Param("是否作为攻击者是生效(否则为受击者时生效)", defaultValue = true)]
        ATTR_COMPLEX_DAMAGE_TYPE_TRANSFORM = 27,

        [EnumName("预留1")] ATTR_COMPLEX_RESERVE_1 = 31,
        [EnumName("预留2")] ATTR_COMPLEX_RESERVE_2 = 32,
        [EnumName("预留3")] ATTR_COMPLEX_RESERVE_3 = 33,
        [EnumName("预留4")] ATTR_COMPLEX_RESERVE_4 = 34,
        [EnumName("预留5")] ATTR_COMPLEX_RESERVE_5 = 35,
    }


    public enum HERO_ATTRIBUTE_SOURCE_DEF
    {
        ATTR_SOURCE_BASE = 1, // 英雄基本属性
        ATTR_SOURCE_BUFF = 2, // BUFF
        ATTR_SOURCE_LEVEL_UP = 3, // 英雄升级属性成长
        ATTR_SOURCE_EQUIP = 4, // 装备属性
        ATTR_SOURCE_BASIC_ATTR_TRANSIT = 5, // 基础属性折算
        ATTR_SOURCE_TITLE = 6, // 称号属性
        ATTR_SOURCE_FASHION = 7, // 时装属性
    }

    public enum ROLE_ATTRIBUTE_DEF
    {
        ROLE_ATTR_AGILITY = 1, // 手速
        ROLE_ATTR_MENTALITY = 2, // 意识
        ROLE_ATTR_STAMINA = 3, // 体能
        ROLE_ATTR_CHARM = 4, // 魅力
        ROLE_ATTR_SPEECH = 5, // 口才
    }

    public enum ROLE_ATTRIBUTE_SOURCE_DEF
    {
        ROLE_ATTR_SOURCE_BASE = 1, // 角色基本属性
    }

    public enum WORLD_TYPE
    {
        WORLD_ALLCAREER = 1, // 全职
        WORLD_HONOR = 2, // 荣耀
    }

    public enum SCENE_TYPE
    {
        SCENE_TYPE_CITY = 1, // 主城
        SCENE_TYPE_WILD = 2, // 野外
        SCENE_TYPE_PVE = 3, // 副本
        SCENE_TYPE_PVP = 4, // PVP
        SCENE_TYPE_TEAM_PVE = 5, // 组队副本
    }

    public enum SHOP_TYPE
    {
        SHOP_TYPE_NORMAL = 1, // 直购商店，特殊值，不可刷新
    }

    public enum SHOP_PRICE_TYPE
    {
        PRICE_TYPE_DIAMOND = 1, // 钻石价格
    }

    public enum SHOP_GOODS_TYPE
    {
        GOODS_TYPE_ITEM = 1, // 道具商品
        GOODS_TYPE_RES = 2, // 资源商品
    }

    public enum THINGOBJ_TYPE
    {
        T_TYPE_NULL = 0, // 空
        T_TYPE_ARTICLE = 1, // 物品
    }

    public enum ITEM_FUNC_TYPE
    {
        ITEM_FUNC_TYPE_BOX = 1, // 宝箱
    }

    public enum REWARD_TYPE
    {
        HERO_REWARD_TYPE_ITEM = 1, // 荣耀掉落道具
        HERO_REWARD_TYPE_EQUIP = 2, // 荣耀掉落装备
        HERO_REWARD_TYPE_CURRENCY = 3, // 荣耀掉落货币
        REWARD_TYPE_EXT = 4, // 掉落扩展
        REWARD_TYPE_NULL = 5, // 掉落空
        REWARD_TYPE_FASHION = 6, // 荣耀掉落时装
        REWARD_TYPE_TITLE = 7, // 荣耀掉落称号
        ROLE_REWARD_TYPE_ITEM = 8, // 全职掉落道具
        HERO_REWARD_TYPE_RAW = 9, // 荣耀掉落奖励
        ROLE_REWARD_TYPE_RAW = 10, // 全职掉落奖励
        REWARD_TYPE_MAX = 11, // 最大值（无效）
    }

    public enum PVE_TYPE
    {
        PVE_TYPE_NORMAL = 1, // 主线普通副本
    }

    public enum MAIL_TEMPLATE_ID
    {
        MAIL_TID_1 = 1, // 保留ID，策划在相关表配置
    }

    public enum AUTO_TASK_TABLE
    {
        AUTO_TASK_TABLE_ROLE = 1,
        AUTO_TASK_TABLE_HERO = 2,
    }

    public enum AUTO_TASK_TYPE
    {
        AUTO_TASK_REWARD = 1, // 奖励包
        AUTO_TASK_MAIL = 2,
    }

    public enum INSTANCE_THING_TYPE
    {
        INSTANCE_THING_EQUIP = 1, // 装备
    }

    public enum ENUM_MODULE_TYPE
    {
        NONE = 100, // 没有
    }

    public enum LIMIT_DATA_TYPE
    {
        LIMIT_PAYMENT_COIN_TIMES = 1, // 购买钢币次数
    }

    /// <summary>
    /// 装备所属类型;
    /// </summary>
    public enum EquipmentBelongType
    {
        None = -1, //未知;
        Player = 0, //玩家(已经拾取的, 未穿戴到玩家身上的);
        Hero = 1, //英雄;
    }

    public enum SelectTargetUnitType
    {
        [ST_Title("格子")] Cell,
        [ST_Title("英雄")] BC,
        [ST_Title("（已弃用）顶点")] Vertex,
        [ST_Title("准备区域的格子")] ReadyAreaCell, //准备区域的格子;
        [ST_Title("数字")] Fix64, //数字
        [ST_Title("位置")] Pos,
        [ST_Title("准备阶段的战斗实体")] BattleUnit, //准备阶段的战斗实体
        [ST_Title("顶点")] UsingVertex,
    }

    public enum SelectTargetFightType
    {
        Inside, //局内;
        Outside, //局外;
    }

    public enum TargetSelectTypeInvokeType
    {
        [ST_Title("未知")] None = 0,
        [ST_Title("装备")] Equip = 1,
        [ST_Title("Buff")] Buff = 2,
        [ST_Title("Skill")] Skill = 3,
        [ST_Title("CreateSummon")] CreateSummon = 4,
        [ST_Title("SummonWaithero")] SummonWaithero = 5,

        [ST_Title("SkillModule_FindSkillTarget")]
        SkillModule_FindSkillTarget = 6,
        [ST_Title("SkillModule_LucianSkill")] SkillModule_LucianSkill = 7,
        [ST_Title("SkillDurationTarget")] SkillDurationTarget = 8,
        [ST_Title("海克斯buff")] HABuffTarget = 9,
        [ST_Title("节点上下文")] NodeContext = 10,
    }

    /**
         * 注意:ST_Param里的defaultValue改完后，对应的SelectTarget(XXX)Param类里也要修改!!!;
         */
    public enum TARGET_SELECT_TYPE
    {
        [ST_Title("自己")] [ST_Param("isCell", defaultValue = false)]
        SELECT_MYSELF = 1,

        [ST_Title("随机多个参数", "注意：使用内部排序时，不用再次随机，则每次通过该目标选择出来的结果都是一致的，除非不可选或者死亡增加")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("范围", defaultValue = 0)]
        [ST_Param("个数(填max字符串表示所有)", defaultValue = "0")]
        [ST_Param("EquipStatus", defaultValue = EquipStatus.NONE)]
        [ST_Param("是否使用确切的个数", defaultValue = true)]
        [ST_Param("使用内部排序(勾上标识不再随机)", defaultValue = false)]
        [ST_Param("是否可以选中不可选中的单位", defaultValue = false)]
        SELECT_RANDOM_TARGET = 2,

        [ST_Title("寻找空格子")]
        [ST_Param("roleFaceDir", defaultValue = ROLE_FACE_DIR.NONE)]
        [ST_Param("范围", defaultValue = ST_ParamValue_InitType.AutoChessConfig_HEXAGON_FORMATION_LINE_MAX_COUNT)]
        [ST_Param("CheckGridUnitDistanceType", defaultValue = CheckGridUnitDistanceType.FROM_NEAR_TO_FAR)]
        [ST_Param("isContainsSelf", defaultValue = false)]
        [ST_Param("AreaType", defaultValue = AreaType.NONE)]
        [ST_Param("needCanWalkTo", defaultValue = true)]
        [ST_Param("useTargetAsCenterPos", defaultValue = false)]
        [ST_Param("useDirTypeRefTarget", defaultValue = false)]
        SELECT_NEAR_EMPTY_CELL = 3,

        [ST_Title("血量最少的目标")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("范围", defaultValue = 0)]
        [ST_Param("SelectTargetValueType", defaultValue = SelectTargetValueType.RATE100)]
        [ST_Param("maxNum", defaultValue = 1)]
        [ST_Param("valueCompareType", defaultValue = ValueCompareType.SMALL_TO_BIG)]
        [ST_Param("attributeDefCur", defaultValue = ATTRIBUTE_DEF.ATTR_HP_NOW)]
        [ST_Param("attributeDefMax", defaultValue = ATTRIBUTE_DEF.ATTR_HP_MAX)]
        [ST_Param("needCheckEmptyGridInAttackRange", defaultValue = false)]
        [ST_Param("是否可以选中不可选中的单位", defaultValue = false)]
        SELECT_MIN_HP_TARGET = 4,

        [ST_Title("距离最远\\最近目标(已废弃，改用89)")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("范围,-1标识使用攻击距离", defaultValue = 0)]
        [ST_Param("roleFaceDir", defaultValue = ROLE_FACE_DIR.NONE)]
        [ST_Param("distanceType", defaultValue = CheckGridUnitDistanceType.FROM_FAR_TO_NEAR)]
        [ST_Param("个数", defaultValue = 1)]
        [ST_Param("needRealPosToJudge", defaultValue = false)]
        [ST_Param("atLeastReturnMyselfType", defaultValue = AtLeastReturnMyselfType.None)]
        [ST_Param("resultIsCell", defaultValue = false)]
        [ST_Param("cellRefTargetFaceDir", defaultValue = ROLE_FACE_DIR.NONE)]
        [ST_Param("额外要随机寻找的英雄个数", defaultValue = 0)]
        [ST_Param("是否优先使用普通目标(当距离一样时)", defaultValue = false)]
        [ST_Param("返回格子的话，是否必须为空闲格子", defaultValue = true)]
        SELECT_FARTHEST_TARGET = 5,

        [ST_Title("当前攻击目标")]
        [ST_Param("范围(-1标识全地图, -2标识真实攻击距离)",
            defaultValue = ST_ParamValue_InitType.AutoChessConfig_HEXAGON_FORMATION_LINE_MAX_COUNT)]
        [ST_Param("isCell", defaultValue = false)]
        [ST_Param("额外寻找敌人的个数", defaultValue = 0)]
        [ST_Param("额外寻找敌人的方式，none表示随机", defaultValue = CheckGridUnitDistanceType.None)]
        SELECT_ATTACK_AIM = 6,

        [ST_Title("可变长度的最远距离")]
        [ST_Param("directionType", defaultValue = DirectionType.None)]
        [ST_Param("范围", defaultValue = 0)]
        SELECT_VARIABLE_FARTHEST_TARGET = 7,

        [ST_Title("属性过滤")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("范围", defaultValue = ST_ParamValue_InitType.AutoChessConfig_HEXAGON_FORMATION_LINE_MAX_COUNT)]
        [ST_Param("compareType", defaultValue = CompareType.Equal)]
        [ST_Param("propertyFilterType", defaultValue = PropertyFilterType.STARLEVEL)]
        [ST_Param("compareValue[注意：对比值不填就使用的释放者的信息, +号分割多个数值]", defaultValue = "")]
        [ST_Param("findNum((默认不限))", defaultValue = -1)]
        SELECT_PROPERTY_FILTER = 8,

        [ST_Title("密度目标")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("范围", defaultValue = 0)]
        [ST_Param("densityType", defaultValue = DensityType.LESS)]
        [ST_Param("needBc", defaultValue = true)]
        [ST_Param("checkTarget(勾选needBc时生效)", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("英雄影响周围的权重范围", defaultValue = 1)]
        [ST_Param("格子需要是空的", defaultValue = true)]
        SELECT_DENSITY = 9,

        [ST_Title("安全的地方")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("突进距离", defaultValue = 0)]
        [ST_Param("densityType", defaultValue = DensityType.LESS)]
        [ST_Param("damageRange", defaultValue = 0)]
        [ST_Param("isDashRefTarget", defaultValue = false)]
        [ST_Param("isUseNearestTargetAsRef", defaultValue = false)]
        SELECT_SAFE_POS = 10,

        [ST_Title("目标身后")]
        [ST_Param("范围", defaultValue = 1)]
        [ST_Param("checkGridUnitDistanceType", defaultValue = CheckGridUnitDistanceType.FROM_NEAR_TO_FAR)]
        [ST_Param("referCharacterType", defaultValue = ReferCharacterType.TARGET)]
        SELECT_TARGET_BEHIND = 11,

        [ST_Title("选择方向上英雄集合")]
        [ST_Param("范围", defaultValue = 0)]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("directionType", defaultValue = DirectionType.None)]
        SELECT_DIRECTION_UNIT = 12,

        [ST_Title("选择目标-指定方向线(斜左\\斜右\\水平)的英雄集合 [注意：isCell(1是要空格子, 0是要符合EFFECT_TARGET的英雄)]")]
        [ST_Param("范围", defaultValue = 0)]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("directionLineType", defaultValue = DirectionLineType.None)]
        [ST_Param("isCell", defaultValue = false)]
        [ST_Param("needCellEmpty", defaultValue = true)]
        [ST_Param("needRandom", defaultValue = false)]
        SELECT_DIRECTIONLINE_UNIT = 13,

        [ST_Title("选择目标-击打到最多的敌人目标")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("技能范围宽度", defaultValue = 0)]
        [ST_Param("技能长度(往前延伸的方向)", defaultValue = 0)]
        SELECT_SKILL_RANGE = 14,

        [ST_Title("选择目标-镜像")]
        [ST_Param("isCell", defaultValue = false)]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("mirrorPosEmptyFindRange(当镜像点不满足时，在范围内找个最近的点)",
            defaultValue = ST_ParamValue_InitType.AutoChessConfig_HEXAGON_FORMATION_LINE_MAX_COUNT)]
        [ST_Param("filterBuffTag", defaultValue = -1)]
        SELECT_TARGET_MIRROR = 15,

        [ST_Title("选择目标-格子权重")]
        [ST_Param("周围unitRange范围内有n个敌人权重+n", defaultValue = 1)]
        [ST_Param("selectNum表示结果要返回多少个格子", defaultValue = 1)]
        [ST_Param("selectIndexFrom1从1开始的索引，表示要选择第几个", defaultValue = 1)]
        SELECT_TARGET_UNIT_WEIGHT = 16,

        [ST_Title("选择目标-在攻击范围内")] [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        SELECT_TARGET_IN_ATTACKRANGE = 17,

        [ST_Title("选择目标-给定指定方向的选择侧方向的对象")]
        [ST_Param("isCell", defaultValue = false)]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("DirectionType(指定方向)", defaultValue = DirectionType.None)]
        [ST_Param("DirectionType(选择的方向)", defaultValue = DirectionType.None)]
        [ST_Param("范围", defaultValue = 0)]
        [ST_Param("num", defaultValue = 0)]
        SELECT_TARGET_RANDOM_DIRECTION_SIDE = 18,

        [ST_Title("选择目标-在其他目标的攻击范围内")] [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        SELECT_TARGET_INNER_OTHER_ATTACKRANGE = 19,

        [ST_Title("选择目标-属性对比")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("范围", defaultValue = ST_ParamValue_InitType.AutoChessConfig_HEXAGON_FORMATION_LINE_MAX_COUNT)]
        [ST_Param("selectNum", defaultValue = 1)]
        [ST_Param("valueCompareType", defaultValue = ValueCompareType.BIG_TO_SMALL)]
        [ST_Param("attributeDef", defaultValue = ATTRIBUTE_DEF.ATTR_MP)]
        SELECT_TARGET_PROPERTY_COMPARE = 20,

        [ST_Title("选择目标-附近有空闲格子的单位")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ENEMY)]
        [ST_Param("findRange", defaultValue = ST_ParamValue_InitType.AutoChessConfig_HEXAGON_FORMATION_LINE_MAX_COUNT)]
        [ST_Param("neighborRange附近的范围", defaultValue = 2)]
        [ST_Param("checkGridUnitDistanceType", defaultValue = CheckGridUnitDistanceType.None)]
        [ST_Param("isCell", defaultValue = false)]
        SELECT_TARGET_HAS_EMPTY_CELL_TARGET = 21,

        [ST_Title("拥有最多装备的目标")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("范围", defaultValue = 0)]
        SELECT_TARGET_WITH_THE_MOST_EQUIPMENT = 22,

        [ST_Title("选择目标-随机选择个目标和释放者连线寻找格子")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ENEMY)]
        [ST_Param("findRange", defaultValue = ST_ParamValue_InitType.AutoChessConfig_HEXAGON_FORMATION_LINE_MAX_COUNT)]
        [ST_Param("extendLineRange目标点背后延伸多少范围", defaultValue = 1)]
        [ST_Param("randomRangeWhenDistance0", defaultValue = 1)]
        [ST_Param("distanceType", defaultValue = CheckGridUnitDistanceType.FROM_FAR_TO_NEAR)]
        SELECT_TARGET_RANDOM_IN_LINE = 23,

        [ST_Title("选择目标-特质\\职业")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("范围", defaultValue = 0)]
        [ST_Param("classId1+classId2+...(职业ID)", defaultValue = "")]
        [ST_Param("spedId1+spedId2+...(种族ID)", defaultValue = "")]
        [ST_Param("是否考虑假死", defaultValue = false)]
        [ST_Param("是否至少得有一件装备", defaultValue = false)]
        [ST_Param("数量", defaultValue = -1)]
        [ST_Param("排除buff", defaultValue = -1)]
        SELECT_TARGET_CLASS_SPEC = 24,

        [ST_Title("选择目标-边缘")] [ST_Param("num", defaultValue = 1)]
        SELECT_TARGET_EDGE = 25,

        [ST_Title("选择目标-当前攻击目标连线处可攻击到的最大数目的空格子")]
        SELECT_TARGET_AIM_LINE_MAX_CELL = 26,

        [ST_Title("选择目标-当前正在攻击的目标周围随机一个空格")] [ST_Param("范围", defaultValue = 0)]
        SELECT_TARGET_AIM_NEIGHBOR_RANDOM_CELL = 27,

        [ST_Title("选择目标-随机一个目标周围随机一个空格")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ENEMY)]
        [ST_Param("邻居范围", defaultValue = 1)]
        SELECT_TARGET_NEIGHBOR_RANDOM = 28,

        [ST_Title("冰脉格子")] [ST_Param("isCell", defaultValue = false)]
        SELECT_TARGET_ICE_CELL = 29,

        [ST_Title("选择目标: 装备数量")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ENEMY)]
        [ST_Param("densityType", defaultValue = DensityType.LESS)]
        [ST_Param("装备数量一样后，对比属性", defaultValue = ATTRIBUTE_DEF.ATTR_NONE)]
        [ST_Param("属性densityType", defaultValue = DensityType.MORE)]
        [ST_Param("【局外用】装备状态，当装备状态为有或空时，直接返回有无装备的所有目标", defaultValue = EquipStatus.NONE)]
        SELECT_TARGET_EQUIPCOUNT = 30,

        [ST_Title("选择目标: 随机一个目标的顶点【注意】如果是技能用的话，要在技能模块里使用到AFC_SkillGridVertexClear进行顶点的清除;")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ENEMY)]
        [ST_Param("范围", defaultValue = ST_ParamValue_InitType.AutoChessConfig_HEXAGON_FORMATION_LINE_MAX_COUNT)]
        SELECT_TARGET_RANDOMVERTEX = 31,

        [ST_Title("选择格子-当前面朝向不变移动到目标格子后，还能以这个朝向打中敌人")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("范围", defaultValue = 0)]
        [ST_Param("useRealTimePos位置形式（释放位置还是实际英雄位置，卢锡安滑动时需要实时修改位置）", defaultValue = false)]
        SELECT_TARGET_CUR_FACEDIRECTION_CELL = 32,

        [ST_Title("选择目标-当前面朝向不变，攻击范围可以打到的目标")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        SELECT_TARGET_CUR_FACEDIRECTION_BC = 33,

        [ST_Title("选择目标-中间点")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("范围", defaultValue = 0)]
        [ST_Param("classId1+classId2+...(职业ID)", defaultValue = "")]
        [ST_Param("spedId1+spedId2+...(种族ID)", defaultValue = "")]
        [ST_Param("是否考虑假死", defaultValue = false)]
        [ST_Param("是否至少得有一件装备", defaultValue = false)]
        [ST_Param("areaType", defaultValue = AreaType.SELF)]
        SELECT_TARGET_CENTER = 34,

        [ST_Title("选择目标-备战区英雄所在的格子")] 
        [ST_Param("num", defaultValue = 0)]
        [ST_Param("是否选择对方备战区", defaultValue = false)]
        [ST_Param("选择类型", defaultValue = SelectTargetRandomWaitPosSelectType.Random)]
        SELECT_TARGET_RANDOM_WAIT_POS = 35,

        [ST_Title("(娑娜)血量最少的目标，先随机前半部分，再筛后半部分（中间线即为满血的）")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("范围", defaultValue = 0)]
        [ST_Param("valueType", defaultValue = SelectTargetValueType.RATE100)]
        [ST_Param("maxNum", defaultValue = 1)]
        [ST_Param("valueCompareType", defaultValue = ValueCompareType.SMALL_TO_BIG)]
        [ST_Param("attributeDefCur", defaultValue = ATTRIBUTE_DEF.ATTR_HP_NOW)]
        [ST_Param("attributeDefMax((最大，当需要百分比时才需要))", defaultValue = ATTRIBUTE_DEF.ATTR_HP_MAX)]
        SELECT_TARGET_SONA = 36,

        [ST_Title("选择目标-维克兹(大眼)选择一个角度比较小的扇形，并且保证达到最多敌人")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ENEMY)]
        [ST_Param("startDegree(开始角度)", defaultValue = 60)]
        [ST_Param("minusDegreePerTime(每次减少的角度)", defaultValue = 10)]
        [ST_Param("minDegree(最小的角度)", defaultValue = 20)]
        [ST_Param("sectorLength(真实扇形的边长)",
            defaultValue = ST_ParamValue_InitType.AutoChessConfig_HEXAGON_FORMATION_LINE_MAX_COUNT)]
        SELECT_TARGET_VELKOZ = 37,

        [ST_Title("选择目标-维克托 选择场上的相距最远的2个目标格子，第一个格子是离自己最近的")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ENEMY)]
        [ST_Param("gridDistance", defaultValue = 3.125f)]
        SELECT_TARGET_VIKTOR = 38,

        [ST_Title("选择目标-是否有对应的buff(按最近的寻找)")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ENEMY)]
        [ST_Param("checkBuffMethod", defaultValue = CheckBuffMethod.BuffProtoId)]
        [ST_Param("logicCompareType", defaultValue = LogicCompareType.And)]
        [ST_Param("buff比较数组,可以多个,用+分割,如:buffValue1+buffValue2+buffValue3", defaultValue = "")]
        [ST_Param("释放buff的人可以是任何人", defaultValue = false)]
        [ST_Param("是否拥有", defaultValue = CheckOwn.No)]
        [ST_Param("rtnOneAtLeast", defaultValue = true)]
        [ST_Param("range", defaultValue = ST_ParamValue_InitType.AutoChessConfig_HEXAGON_FORMATION_LINE_MAX_COUNT)]
        [ST_Param("findNum", defaultValue = 1)]
        [ST_Param("isFindNeighborEmptyCell", defaultValue = false)]
        [ST_Param("checkBuffOwnerType", defaultValue = CheckBuffOwnerType.Caster)]
        SELECT_TARGET_HAS_BUFF = 39,
        [ST_Title("选择目标-巴德备战区选择")] SELECT_TARGET_BUDD_WAIT_POS = 40,

        [ST_Title("选择目标-选择最远的目标，距离动态增加")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ENEMY)]
        [ST_Param("dyncRange(动态增加的判断距离)", defaultValue = 2)]
        SELECT_TARGET_FARTHEST_DYNCRANGE = 41,

        [ST_Title("随机多个目标的顶点")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ENEMY)]
        [ST_Param("范围", defaultValue = ST_ParamValue_InitType.AutoChessConfig_HEXAGON_FORMATION_LINE_MAX_COUNT)]
        [ST_Param("findNum", defaultValue = 3)]
        SELECT_TARGET_RANDOM_MULTIVERTEX = 42,

        [ST_Title("S3提莫,随机一个目标，周围创建3个不相交的圆")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ENEMY)]
        [ST_Param("lineDistanceByTarget(距离目标的直线距离)", defaultValue = 0)]
        [ST_Param("centerCircleInnerRadius(中心圆的内部半径)", defaultValue = 0)]
        [ST_Param("centerCircleOutterRadius(中心圆的外部半径)", defaultValue = 0)]
        [ST_Param("secondCircleInitRadius(第二个圆的初始半径)", defaultValue = 0)]
        [ST_Param("thirdCircleInnerRadius(第三个圆的内部半径)", defaultValue = 0)]
        [ST_Param("thirdCircleOutterRadius(第三个圆的外部半径)", defaultValue = 0)]
        [ST_Param("thridCircleInvalidPosAdditiveRadiusPerCount(第三个圆选中无效位置时，每次增加的半径值)", defaultValue = 0)]
        SELECT_TARGET_CIRCLE_NONE_INTERSECT = 43,

        [ST_Title("选择目标-目标背后一格内的格子(选择优先顺序为后->左右->前)")] [ST_Param("isCellMustEmpty(格子是否必须为空)", defaultValue = false)]
        SELECT_TARGET_BACKWARD_PRIORITY = 44,

        [ST_Title("选择目标-根据行列位置", "注意：\"是否选择格子\"（是：返回格子；否：返回具体位置坐标；勾选\"选择英雄\"时失效）, 行索引填真实位置x * 1000, 列索引填真实位置y * 1000")]
        [ST_Param("行索引(从0开始)", defaultValue = 0)]
        [ST_Param("列索引(从0开始)", defaultValue = 0)]
        [ST_Param("是否选择格子（是：返回格子；否：返回具体位置坐标；勾选英雄时失效）", defaultValue = true)]
        [ST_Param("如果阵营是敌方，需要镜像", defaultValue = false)]
        [ST_Param("是否选择英雄", defaultValue = false)]
        SELECT_TARGET_SPECIAL_POS = 45,

        [ST_Title("选择目标-属性排序，如获取XX属性最高最低的英雄")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("findNum", defaultValue = 0)]
        [ST_Param("valueCompareType", defaultValue = ValueCompareType.BIG_TO_SMALL)]
        [ST_Param("propertyFilterType", defaultValue = PropertyFilterType.STARLEVEL)]
        [ST_Param("isCell", defaultValue = false)]
        [ST_Param("attrDef", defaultValue = ATTRIBUTE_DEF.ATTR_HP_NOW)]
        [ST_Param("attrDefMax(最大，当需要百分比时才需要)", defaultValue = ATTRIBUTE_DEF.ATTR_HP_MAX)]
        SELECT_TARGET_PROPERTY_SORT = 46,

        [ST_Title("选择目标-按距离棋盘中间点距离顺序增加")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("isCell", defaultValue = false)]
        [ST_Param("findNum", defaultValue = 1)]
        [ST_Param("isRandomAdd", defaultValue = false)]
        [ST_Param("是否返回真实位置(该参数为true则isCell参数必须填false)", defaultValue = false)]
        SELECT_TARGET_CHESSGRID_CENTER = 47,

        [ST_Title("选择目标-释放次数最多的人")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("valueCompareType", defaultValue = ValueCompareType.BIG_TO_SMALL)]
        SELECT_TARGET_ABILITYUSETIMES = 48,

        [ST_Title("选择目标-指定方向")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("dirType", defaultValue = DirectionType.Left)]
        [ST_Param("findNum", defaultValue = 0)]
        [ST_Param("containSelf", defaultValue = false)]
        SELECT_TARGET_SPECIALDIR = 49,

        [ST_Title("选择目标-类型\\特质\\职业")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("specId", defaultValue = 0)]
        [ST_Param("carrerId", defaultValue = 0)]
        SELECT_TARGET_TYPE_SPEC_CAREER = 50,

        [ST_Title("选择目标-最远\\近目标周围的一个随机格子")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("范围", defaultValue = 0)]
        [ST_Param("CheckGridUnitDistanceType", defaultValue = CheckGridUnitDistanceType.FROM_FAR_TO_NEAR)]
        [ST_Param("格子需要是空的", defaultValue = false)]
        [ST_Param("寻找目标周边的范围", defaultValue = 1)]
        [ST_Param("至少返回一个空格子", defaultValue = false)]
        [ST_Param("是否默认包含当前攻击目标", defaultValue = true)]
        [ST_Param("是否需要格子周围还要有目标的友方", defaultValue = false)]
        SELECT_TARGET_FARTHESTTARGET_NEIGHBOR_CELL = 51,
        [ST_Title("选择目标-召唤释放者的召唤者")] SELECT_TARGET_SUMMONER = 52,

        [ST_Title("选择目标-目标集合中选择连线距离最短的一个目标")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("范围", defaultValue = 0)]
        [ST_Param("classId1+classId2+...(职业ID)", defaultValue = "")]
        [ST_Param("spedId1+spedId2+...(种族ID)", defaultValue = "")]
        [ST_Param("是否考虑假死", defaultValue = false)]
        [ST_Param("是否至少得有一件装备", defaultValue = false)]
        SELECT_TARGET_LINECENTER = 53,

        [ST_Title("选择目标-按角度进行寻找空格子")] [ST_Param("lineLen(毫米)", defaultValue = 2800)]
        SELECT_TARGET_FINDBYDEGREE = 54,

        [ST_Title("选择目标-随机寻找目标周围随机的位置")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ENEMY)]
        [ST_Param("地图的宽度(米)", defaultValue = 22)]
        [ST_Param("地图里一排有多少个格子", defaultValue = 100)]
        [ST_Param("多少内圈不进行位置寻找", defaultValue = 2)]
        [ST_Param("检查的圈数", defaultValue = 4)]
        [ST_Param("寻找位置的个数", defaultValue = 5)]
        [ST_Param("位置随机x轴", defaultValue = 0)]
        [ST_Param("位置随机z轴", defaultValue = 0)]
        [ST_Param("是否使用地图中心点作为参考点", defaultValue = false)]
        [ST_Param("是否限制在边界内", defaultValue = false)]
        SELECT_TARGET_TARGETADROUNDRANDOM = 55,

        [ST_Title("选择目标-距离质心的目标")]
        [ST_Param("(用于计算质心的)EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("(用于计算质心的)范围", defaultValue = 0)]
        [ST_Param("(用于计算质心的)classId1+classId2+...(职业ID)", defaultValue = "")]
        [ST_Param("(用于计算质心的)spedId1+spedId2+...(种族ID)", defaultValue = "")]
        [ST_Param("(用于计算质心的)是否考虑假死", defaultValue = false)]
        [ST_Param("(用于计算质心的)是否至少得有一件装备", defaultValue = false)]
        [ST_Param("(距离质心的集合)calcEffectTarget", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("(寻找个数)findNum", defaultValue = 1)]
        SELECT_TARGET_DISNTACE_CENTERPOSMASS = 56,

        [ST_Title("选择目标-当前位置和敌人身后一格连线的中点", "注意：敌人的面朝向选择none的话就是释放者向着敌人的朝向的敌人后方")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ENEMY)]
        [ST_Param("distanceType", defaultValue = CheckGridUnitDistanceType.FROM_FAR_TO_NEAR)]
        [ST_Param("敌人的面朝向", defaultValue = ROLE_FACE_DIR.NONE)]
        [ST_Param("以方向延伸多少格为结束点", defaultValue = 1)]
        SELECT_TARGET_LINE_CENTER_POS = 57,

        [ST_Title("选择目标-指定方向延伸处寻找空格", "延伸处为空格子的话，直接返回，否则从内向外找空格子。\n注意：延伸距离为真实距离(米)")]
        [ST_Param("dirType", defaultValue = DirectionType.Left)]
        [ST_Param("延伸距离(米)", defaultValue = 0)]
        SELECT_TARGET_SPECIAL_DIRCELL = 58,

        [ST_Title("选取以自身为圆心，半径A格的圆与当前攻击目标为圆心半径B格的圆的共同覆盖区域内，离自身最远的空格子")]
        [ST_Param("releaserGridRange", defaultValue = 3)]
        [ST_Param("targetGridRange", defaultValue = 2)]
        [ST_Param("distanceType", defaultValue = CheckGridUnitDistanceType.FROM_FAR_TO_NEAR)]
        SELECT_TARGET_CIRCLE_OVERLAY = 59,

        [ST_Title("当前目标周围随机目标选择")]
        [ST_Param("effectTarget", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ENEMY)]
        [ST_Param("neighborRange", defaultValue = 12)]
        [ST_Param("findNum", defaultValue = 3)]
        [ST_Param("containsTarget", defaultValue = true)]
        [ST_Param("distanceType", defaultValue = CheckGridUnitDistanceType.FROM_NEAR_TO_FAR)]
        SELECT_TARGET_NEIGHBOR_BC = 60,

        [ST_Title("释放者朝向最远目标连线获取空闲点/位置")]
        [ST_Param("farthestTarget", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ENEMY)]
        [ST_Param("extendLine(要填*1000)", defaultValue = 1)]
        [ST_Param("结果需要返回位置", defaultValue = false)]
        SELECT_TARGET_EXTEND_LINE = 61,

        [ST_Title("目标与目标间距最远的集合，如S6扎克")]
        [ST_Param("farthestTarget", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ENEMY)]
        [ST_Param("farthestRange(范围)", defaultValue = 4)]
        [ST_Param("个数", defaultValue = 2)]
        [ST_Param("distanceType", defaultValue = CheckGridUnitDistanceType.FROM_FAR_TO_NEAR)]
        SELECT_TARGET_DISTANCE_TARGET = 62,

        [ST_Title("选择目标-选取地图上的某个格子到所有敌人格子的距离，距离最短的就是敌人的中心格子（空格子）如S6: 金克丝;")]
        [ST_Param("farthestTarget", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ENEMY)]
        [ST_Param("distanceType", defaultValue = CheckGridUnitDistanceType.FROM_NEAR_TO_FAR)]
        [ST_Param("rangeLimit[-1标识使用攻击范围]格子距离target，至少能打中一个", defaultValue = 0)]
        SELECT_TARGET_SUMTARGET_DISTANCE_CELL = 63,

        [ST_Title("敌人目标最多的直线：选取某一个目标，根据碰撞框的长度和宽度大小，检测能碰撞到最多敌人的方向（我跟某个敌人的连线）")]
        [ST_Param("farthestTarget", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ENEMY)]
        [ST_Param("范围", defaultValue = ST_ParamValue_InitType.AutoChessConfig_HEXAGON_FORMATION_LINE_MAX_COUNT)]
        [ST_Param("碰撞类型", defaultValue = TDRConfig.HitShapeType.Rect_Real)]
        [ST_Param("centerRow", defaultValue = 0)]
        [ST_Param("centerCol", defaultValue = 0)]
        [ST_Param("rangeRow", defaultValue = 0)]
        [ST_Param("rangeCol", defaultValue = 0)]
        [ST_Param("boxColliderCenter", defaultValue = ST_ParamValue_InitType.Vector3_Zero)]
        [ST_Param("boxColliderSize", defaultValue = ST_ParamValue_InitType.Vector3_Zero)]
        SELECT_TARGET_TARGET_OVERLAP = 64,

        [ST_Title("选择目标-格子")]
        [ST_Param("effectTarget", defaultValue = EFFECT_TARGET.EFFECT_TARGET_SELF)]
        [ST_Param("范围", defaultValue = 0)]
        [ST_Param("周围effecttarget", defaultValue = EFFECT_TARGET.EFFECT_TARGET_SELF)]
        [ST_Param("包含找到的周围多少圈格子", defaultValue = 1)]
        [ST_Param("是否需要是空格子", defaultValue = false)]
        [ST_Param("effectTarget是自己是否强制加入", defaultValue = false)]
        SELECT_TARGET_CELL = 65,

        [ST_Title("选择目标-回合伤害总量")]
        [ST_Param("影响的目标对象", defaultValue = EFFECT_TARGET.EFFECT_TARGET_SAME_SPEC_FRIEND_OR_ME)]
        [ST_Param("最多伤害或最少伤害", defaultValue = DensityType.MORE)]
        [ST_Param("上一回合或当前回合", defaultValue = SELECT_ROUND.LAST_ROUND)]
        [ST_Param("数量", defaultValue = 0)]
        [ST_Param("是否需要羁绊", defaultValue = 0)]
        [ST_Param("羁绊的Type", defaultValue = 0)]
        [ST_Param("羁绊的CheckId", defaultValue = 0)]
        [ST_Param("排除含某原型BuffId的英雄", defaultValue = -1)]
        SELECT_TARGET_DAMAGE_COUNT = 66,

        [ST_Title("选择目标-行列")]
        [ST_Param("effectTarget", defaultValue = EFFECT_TARGET.EFFECT_TARGET_CUR_TARGET)]
        [ST_Param("位置类型", defaultValue = RowColumnIndexType.Center)]
        [ST_Param("行列类型", defaultValue = RowColumnType.Row)]
        [ST_Param("索引,位置类型为index时才生效", defaultValue = 0)]
        [ST_Param("【局外用】是否获得格子", defaultValue = true)]
        [ST_Param("【局外用】是否获得整行或整列", defaultValue = false)]
        SELECT_TARGET_ROW_COLUMN = 67,

        [ST_Title("选择目标-边缘位置")]
        [ST_Param("需要的个数", defaultValue = 1)]
        [ST_Param("边拆分为多少格", defaultValue = 10)]
        [ST_Param("边头尾各删除多少米(*1000)", defaultValue = 0)]
        SELECT_TARGET_EDGE_POS = 68,

        [ST_Title("选择目标-行列目标")]
        [ST_Param("effectTarget", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ENEMY)]
        [ST_Param("行数据(+号分割)", defaultValue = "1+2")]
        [ST_Param("列数据(+号分割)", defaultValue = "3")]
        [ST_Param("是否要考虑阵营", defaultValue = false)]
        SELECT_TARGET_ROW_COLUMN_BC = 69,

        [ST_Title("选择目标-effectTarget")]
        [ST_Param("target", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("范围", defaultValue = 0)]
        [ST_Param("是否要考虑bc可选性", defaultValue = true)]
        SELECT_TARGET_EFFECT_TARGET = 70,

        [ST_Title("选择目标-公共角色")]
        [ST_Param("己方阵营", defaultValue = true)]
        [ST_Param("是否是支援方(仅支持友方阵营)(弃用)", defaultValue = false)]
        SELECT_TARGET_COMMON_ROLE = 71,

        [ST_Title("选择目标-相对位置的格子或英雄目标")]
        [ST_Param("影响的英雄目标", defaultValue = EFFECT_TARGET.EFFECT_TARGET_FRIEND_NO_ME)]
        [ST_Param("是否返回英雄目标", defaultValue = false)]
        [ST_Param("是否需要空格子，选择格子目标时生效", defaultValue = false)]
        [ST_Param("目标位置数量", defaultValue = 0)]
        [ST_Param("目标相对圈数，参数数量和下一个参数一样，用+号隔开", defaultValue = "")]
        [ST_Param("目标相对方向，参数数量和下一个参数一样，用+号隔开", defaultValue = "")]
        SELECT_TARGET_RELATIVE_CELL = 72,

        [ST_Title("选择目标-bc")]
        [ST_Param("effectTarget", defaultValue = EFFECT_TARGET.EFFECT_TARGET_SELF)]
        [ST_Param("范围", defaultValue = 0)]
        [ST_Param("周围effecttarget", defaultValue = EFFECT_TARGET.EFFECT_TARGET_SELF)]
        [ST_Param("包含找到的周围多少圈格子", defaultValue = 1)]
        [ST_Param("effectTarget是自己是否强制加入", defaultValue = false)]
        [ST_Param("邻居个数", defaultValue = 1)]
        SELECT_TARGET_BC = 73,

        [ST_Title("选择目标-被作为攻击目标的英雄")]
        [ST_Param("beSelectorEffectTarget", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ENEMY)]
        [ST_Param("selectorEffectTarget", defaultValue = EFFECT_TARGET.EFFECT_TARGET_FRIEND_OR_ME)]
        SELECT_TARGET_BE_AS_TARGETCOUNT = 74,

        [ST_Title("选择目标-原子配置方式执行")] [ST_Param("recordCfgId", defaultValue = 0)] [ST_Param("参数", defaultValue = "")]
        SELECT_TARGET_ATOMCFG_EXECUTOR = 75,

        [ST_Title("选择目标-周围一格，特定优先级（如S7传奇）")]
        [ST_Param("是否与职业和种族相等（是与非）", defaultValue = true)]
        [ST_Param("种族", defaultValue = 0)]
        [ST_Param("职业", defaultValue = 0)]
        [ST_Param("优先级，用+隔开，左上0开始顺时针", defaultValue = "4+5+3+0+2+1")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        SELECT_TARGET_LEGEND = 76,

        [ST_Title("距离最远\\最近目标(同样距离允许多个)")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("范围,-1标识使用攻击距离", defaultValue = 0)]
        [ST_Param("roleFaceDir", defaultValue = ROLE_FACE_DIR.NONE)]
        [ST_Param("distanceType", defaultValue = CheckGridUnitDistanceType.FROM_FAR_TO_NEAR)]
        [ST_Param("最大个数", defaultValue = 1)]
        [ST_Param("needRealPosToJudge", defaultValue = false)]
        [ST_Param("atLeastReturnMyselfType", defaultValue = AtLeastReturnMyselfType.None)]
        [ST_Param("resultIsCell", defaultValue = false)]
        [ST_Param("cellRefTargetFaceDir", defaultValue = ROLE_FACE_DIR.NONE)]
        [ST_Param("额外要随机寻找的英雄个数", defaultValue = 0)]
        [ST_Param("是否优先使用普通目标(当距离一样时)", defaultValue = false)]
        [ST_Param("返回格子的话，是否必须为空闲格子", defaultValue = true)]
        SELECT_FARTHEST_TARGET_MULTI = 77,

        [ST_Title("选择目标-相对指定目标的位置")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("相对目标的角度(0-360), 主场时对应为屏幕右方向，逆时针转动", defaultValue = 0)]
        [ST_Param("相对距离, 10单位为内切圆半径", defaultValue = 0)]
        SELECT_TARGET_POS = 78,
        [ST_Title("选择目标-特定英雄组(非最强返回多个目标)")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("英雄组ID（支持多个，_分隔）", defaultValue = 0)]
        [ST_Param("是否包括备战席（准备阶段才生效）", defaultValue = false)]
        [ST_Param("最强（星级最高，装备数最多）", defaultValue = false)]
        [ST_Param("天选（可选参数）", defaultValue = false)]
        SELECT_TARGET_HEROGROUP = 79,
        [ST_Title("选择目标-推到棋盘边缘 S8努努")]
        [ST_Param("临时占位", defaultValue = false)]
        SELECT_TARGET_MAP_BORDER = 80,
        [ST_Title("选择目标-占点")]
        [ST_Param("是否获取但不占位", defaultValue = false)]
        [ST_Param("节点顺序，-1代表暗优先级选取，其他代表选取特定位置", defaultValue = -1)]
        [ST_Param("节点坐标列表, 例如 1000_1 1000_2", defaultValue = "")]
        SELECT_TARGET_OCCUPANCY = 81,
        [ST_Title("选择目标-扇形范围内随机空格子")]
        [ST_Param("起始角度", defaultValue = 0)]
        [ST_Param("结束角度", defaultValue = 0)]
        [ST_Param("最大距离，10单位代表六边形内切圆半径", defaultValue = 0)]
        [ST_Param("最小距离，10单位代表六边形内切圆半径", defaultValue = 0)]
        [ST_Param("无目标返回随机空格子", defaultValue = false)]
        SELECT_TARGET_RANDOM_CELL = 82,
        [ST_Title("选择目标-特定羁绊最强（星级最高，装备数最多）")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("羁绊ID（支持多个，_分隔）", defaultValue = 0)]
        [ST_Param("是否包括备战席（准备阶段才生效）", defaultValue = false)]
        [ST_Param("种族还是职业（true代表种族）", defaultValue = false)]
        SELECT_TARGET_STRONGEST_HERO = 83,
        
        [ST_Title("选择目标-所有相邻的俩个目标(仅备战阶段有效)")]
        BACKUP_0 = 84,
        
        [ST_Title("备用1")]
        BACKUP_1 = 85,
        
        [ST_Title("备用2")]
        BACKUP_2 = 86,
        
        [ST_Title("备用3")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("羁绊ID（支持多个，_分隔）", defaultValue = 0)]
        [ST_Param("是否包括备战席（准备阶段才生效）", defaultValue = false)]
        [ST_Param("种族还是职业（true代表种族）", defaultValue = false)]
        BACKUP_3 = 87,
        
        [ST_Title("备用4")]
        BACKUP_4 = 88,

        [ST_Title("距离最远\\最近目标")]
        [ST_Param("EFFECT_TARGET", defaultValue = EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("范围,-1标识使用攻击距离", defaultValue = 0)]
        [ST_Param("roleFaceDir", defaultValue = ROLE_FACE_DIR.NONE)]
        [ST_Param("distanceType", defaultValue = CheckGridUnitDistanceType.FROM_FAR_TO_NEAR)]
        [ST_Param("个数", defaultValue = 1)]
        [ST_Param("根据真实距离判断", defaultValue = false)]
        [ST_Param("atLeastReturnMyselfType", defaultValue = AtLeastReturnMyselfType.None)]
        [ST_Param("resultIsCell", defaultValue = false)]
        [ST_Param("cellRefTargetFaceDir", defaultValue = ROLE_FACE_DIR.NONE)]
        [ST_Param("额外要随机寻找的英雄个数", defaultValue = 0)]
        [ST_Param("是否优先使用普通目标(当距离一样时)", defaultValue = false)]
        [ST_Param("返回格子的话，是否必须为空闲格子", defaultValue = true)]
        SELECT_FARTHEST_TARGET_NEW = 89,
        
    }

    public enum LogicCompareType
    {
        And = 0, //并且操作，即都要符合条件
        Or = 1, //或操作，即只有一个符合条件就行
    }

    public enum CheckBuffMethod
    {
        BuffProtoId = 0, //buff原型ID
        BuffTag = 1, //buff Tag
    }

    public enum CheckBuffOwnerType
    {
        [EnumName("无所谓")] None = -1,
        [EnumName("释放者")] Caster = 0,
        [EnumName("触发者")] Trigger = 1
    }

    public enum CheckOwn
    {
        No = 0,
        Yes = 1,
    }

    public enum EFFECT_TARGET
    {
        [EnumName("None")] NONE = -1,
        [EnumName("所有")] EFFECT_TARGET_ALL = 0, // 所有
        [EnumName("自己")] EFFECT_TARGET_SELF = 1, // 自己
        [EnumName("敌人")] EFFECT_TARGET_ENEMY = 2, // 敌人
        [EnumName("队友")] EFFECT_TARGET_FRIEND_NO_ME = 3, // 队友
        [EnumName("队友含自己")] EFFECT_TARGET_FRIEND_OR_ME = 4, // 队友含自己
        [EnumName("来源")] EFFECT_TARGET_RESOURCE = 5, // 来源
        [EnumName("敌人(不包含当前目标)")] EFFECT_TARGET_ENEMY_NO_CUR = 6, // 敌人(不包含当前目标)
        [EnumName("召唤物")] EFFECT_TARGET_SUMMER = 7, // 召唤者
        [EnumName("队友(不包含召唤物)")] EFFECT_TARGET_FRIEND_NO_SUMMON = 8, // 队友(不包含召唤物)
        [EnumName("敌人(不包含召唤物)")] EFFECT_TARGET_ENEMY_NO_SUMMON = 9, // 敌人(不包含召唤物)
        [EnumName(" 队友含自己(不包含召唤物)")] EFFECT_TARGET_FRIEND_OR_ME_NO_SUMMON = 10, // 队友含自己(不包含召唤物)
        [EnumName("所有(除了自己)")] EFFECT_TARGET_ALL_NO_ME = 11, // 所有(除了自己)
        [EnumName("所有(除了自己和召唤物)")] EFFECT_TARGET_ALL_NO_ME_AND_SUMMON = 12, // 所有(除了自己和召唤物)
        [EnumName("同种族友军(包含自己)")] EFFECT_TARGET_SAME_SPEC_FRIEND_OR_ME = 13,
        [EnumName("同职业友军(包含自己)")] EFFECT_TARGET_SAME_CLASS_FRIEND_OR_ME = 14,
        [EnumName("同种族+职业友军(包含自己)")] EFFECT_TARGET_SAME_SPEC_CLASS_FRIEND_OR_ME = 15,
        [EnumName("队友(不包含自己的英雄组ID)")] EFFECT_TARGET_FRIEND_NO_ME_NOHEROGROUPID = 16, // 队友
        [EnumName("队友(不包含召唤物)(不包含自己的英雄组ID)")] EFFECT_TARGET_FRIEND_NO_SUMMON_NOHEROGROUPID = 17, // 队友(不包含召唤物)
        [EnumName("当前的目标")] EFFECT_TARGET_CUR_TARGET = 18,
        [EnumName("同种族友军(不包含自己)")] EFFECT_TARGET_SAME_SPEC_FRIEND = 19,
    }

    public enum BUFF_EFFECT_TARGET
    {
        [EnumName("None")] NONE = -1,
        [EnumName("所有")] EFFECT_TARGET_ALL = 0, // 所有
        [EnumName("自己")] EFFECT_TARGET_SELF = 1, // 自己
        [EnumName("来源")] EFFECT_TARGET_RESOURCE = 5, // 来源
        [EnumName("召唤者")] EFFECT_TARGET_SUMMER = 7, // 召唤者
        [EnumName("载体普攻目标")] EFFECT_TARGET_ATK_TARGET = 8,
        [EnumName("buff范围目标")] EFFECT_TARGET_BUFF_RANGE = 20, // buff范围目标(非通用，只在buff中作用)
        [EnumName("buff触发者")] EFFECT_TARGET_BUFF_TRIGGER = 21, // buff触发者(非通用，只在buff中作用)
        [EnumName("buff载体")] EFFECT_TARGET_BUFF_TARGET = 22, // buff载体(非通用，只在buff中作用)
        [EnumName("buff施法者")] EFFECT_TARGET_BUFF_CASTER = 23, // buff施法者(非通用，只在buff中作用)
        [EnumName("buff目标战场公共角色")] EFFECT_TARGET_BUFF_TARGET_BATTLE_ROLE = 24,
        [EnumName("buff施法者战场公共角色")] EFFECT_TARGET_BUFF_CASTER_BATTLE_ROLE = 25,
    }

    //所在半区类型;
    public enum AreaType
    {
        NONE = -1, //无;
        SELF = 0, //自己半区;
        ENEMY = 1, //敌方半区;
    }

    /// <summary>
    /// 参考类型
    /// </summary>
    public enum ReferCharacterType
    {
        SELF = 0, //自己
        TARGET = 1, //目标
    }

    //装备状态;
    public enum EquipStatus
    {
        NONE = -1,
        EMPTY = 0, //空;
        HAS = 1, //拥有;
    }

    public enum SelectTargetValueType
    {
        NORMAL = 0, //正常数值
        RATE100 = 1, //百分比
    }

    public enum ROLE_FACE_DIR
    {
        NONE = -1,
        FORWARD = 0, //面朝前向;
        BACKWARD = 1, //面朝后向;
        TARGET = 2, //目标朝向;
    }

    public enum RowColumnType
    {
        Row = 0,
        Column = 1,
    }

    public enum RowColumnIndexType
    {
        Center = 0, //中间行或列
        Index = 1, //第几行或第几列
        Current = 2, //当前对象的行或列
        TargetNumMax = 3, //最多目标的行或列
    }

    /// <summary>
    /// 方向类型;
    /// </summary>
    public enum DirectionType
    {
        None = -1,
        Left = 0,
        Left_Top = 1,
        Right_Top = 2,
        Right = 3,
        Right_Bottom = 4,
        Left_Bottom = 5,
    }

    /// <summary>
    /// 更详细的方向类型;
    /// </summary>
    public enum DetailedDirectionType
    {
        None = -1,
        Left = 0,
        Left_Top_Angle,
        Left_Top,
        Top_Angle,
        Right_Top,
        Right_Angle,
        Right,
        Right_Bottom_Angle,
        Right_Bottom,
        Bottom_Angle,
        Left_Bottom,
        Left_Bottom_Angle
    }

    public enum DirectionLineType
    {
        None = -1,
        Horizontal = 0, //水平
        Skew_Left = 1, //斜左/
        Skew_Right = 2, //斜右\
        Skew_LeftRight = 3, //斜左+斜右/\
    }

    public enum CheckGridUnitDistanceType
    {
        None = -1,
        FROM_NEAR_TO_FAR = 0, //从近到远;
        FROM_FAR_TO_NEAR = 1, //从远到近;
    }

    public enum AtLeastReturnMyselfType
    {
        None = 0,
        If0ReturnSelf = 1,
        IfNumNotFullReturnSelf = 2,
    }


    //密度类型;
    public enum DensityType
    {
        LESS = 0, //少;
        MORE = 1, //多;
    }

    /// <summary>
    /// 属性过滤类型;
    /// </summary>
    public enum PropertyFilterType
    {
        STARLEVEL = 0, //星级,level
        ACG_HERO_ID = 1, //模板ID;
        ATTRIBUTE = 2, //属性;
        AP_Thousand = 3, //ap的千分比;
        LAST_MOVE_FRAME = 4, //上一次移动的帧索引;
    }

    /// <summary>
    /// 值比较类型;
    /// </summary>
    public enum ValueCompareType
    {
        BIG_TO_SMALL = 0, //大到小;
        SMALL_TO_BIG = 1, //从小到大;
    }

    /// <summary>
    /// 比较类型;
    /// </summary>
    public enum CompareType
    {
        Equal = 0, //==
        LESS = 1, // <
        LEqual = 2, // <=
        GREATER = 3, //>
        GEqual = 4, //>=
    }

    public enum BUFF_TAG
    {
        [EnumName("增益buff")] TAG_BUFF = 1,
        [EnumName("减益buff")] TAG_DEBUFF = 2,
        [EnumName("控制buff")] TAG_CONTROL = 4,
        [EnumName("技能减免buff(暂时只有布隆)")] TAG_SKLL_HURT_REDUCE = 5,
        [EnumName("范围控制buff")] TAG_RANGE_CONTROL = 6,
        [EnumName("放逐")] TAG_BANISHMENT = 7,
        [EnumName("破法")] TAG_SERIOUS_INJURY = 8,
        [EnumName("缴械")] TAG_DISARM = 9,
        [EnumName("降星")] TAG_REDUCE_STAR = 10,
        [EnumName("沉默")] TAG_SILENCE = 11,
        [EnumName("眩晕")] TAG_DIZZY = 12,
        [EnumName("击飞")] TAG_STRIKE_FLY = 13,
        [EnumName("无敌")] TAG_INVINCIBLE = 14,
        [EnumName("装备buff")] TAG_EQUIPMENT = 15,
        [EnumName("护盾")] TAG_SHIELD = 16,
        [EnumName("重伤")] TAG_ADD_MP_MAX = 17,
        [EnumName("羁绊buff")] TAG_FETTERS = 18,
        [EnumName("锁蓝buff")] TAG_LOCKMP = 19,
        [EnumName("献祭buff(不触发新冰心、护卫的护盾等)")] TAG_SACRIFICED = 20,
        [EnumName("临时攻击力buff（特殊作用才标）")] TAG_Attack = 21,
        [EnumName("暗裔附身标志（暗裔可能附身复活）")] TAG_OCCUPY_REBORN = 22,
        [EnumName("技能可暴击tag")] TAG_SKILL_CRIT = 23,
        [EnumName("战斗胜利不计算入血tag")] TAG_NOT_HURT_TINY_HERO = 24,
        [EnumName("灼烧tag(日炎等)")] TAG_BURN = 25,
        [EnumName("最大的Tag标志")] TAG_MAX,
    }

    /// <summary>
    /// 动态攻击添加的buff和飞行道具的，releasetag
    /// </summary>
    public enum DYNAMIC_ATTACK_RELEASE_TAG
    {
        [EnumName("默认")] TAG_none = 0,
        [EnumName("鬼书")] TAG_0 = 1,
        [EnumName("卢登")] TAG_1 = 2,
        [EnumName("电刀")] TAG_2 = 3,
        [EnumName("空")] TAG_3 = 4,
        [EnumName("空")] TAG_4 = 5,
        [EnumName("空")] TAG_5 = 6,
        [EnumName("空")] TAG_6 = 7,
        [EnumName("空")] TAG_7 = 8,
        [EnumName("空")] TAG_8 = 9,
        [EnumName("空")] TAG_9 = 10,
        [EnumName("空")] TAG_10 = 11,
        [EnumName("空")] TAG_11 = 12,
        [EnumName("空")] TAG_12 = 13,
        [EnumName("空")] TAG_13 = 14,
        [EnumName("空")] TAG_14 = 15,
        [EnumName("空")] TAG_15 = 16,
        [EnumName("空")] TAG_16 = 17,
        [EnumName("空")] TAG_17 = 18,
        [EnumName("空")] TAG_18 = 19,
        [EnumName("最大的Tag标志")] TAG_MAX,
    }

    public enum ClearType
    {
        All,
        GridWeight, //只清除格子的权重;
        Grid, //格子权重，格子里的占位信息都清除;
    }

    public enum SELECT_ROUND
    {
        LAST_ROUND, //选择上一回合
        CURRENT_ROUND //选择这一回合
    }

    public enum BUFF_TRIGGER
    {
        [EnumName("空")] BT_NONE = 0, // 不在被触发
        [EnumName("添加时")] BT_ADD = 1, // BT_添加时

        [EnumName("周期")] [ST_Param("周期(毫秒)", defaultValue = 0)][ST_Param("第0秒即生效", defaultValue = false)]
        BT_INTERVAL = 2, // BT_周期(触发参数-毫秒)

        [EnumName("掉血时(参数int1为当前血量,int2为血量上限,uint为掉的血量)")] 
        [ST_Param("小于等于血量千分比", defaultValue = 0)]
        [ST_Param("或小于等于血量具体值", defaultValue = 0)]
        BT_HPCHANGEDONW = 3, // 血量小于(触发参数-血量千分比-血量具体值)

        [EnumName("攻击判定后")] [ST_Param("命中判定--0都可，1命中，2miss", defaultValue = 0)]
        BT_AKT_LAST = 5, // BT_攻击后(触发参数-命中--0都可，1命中，2miss)
        [EnumName("破盾后(当前护盾buff被击破)")] BT_BREAK_SHIELD = 6, // BT_破盾后

        [EnumName("释放技能后(动作执行前)")] [ST_Param("技能动作id(0表示所有技能)", defaultValue = 0)]
        BT_RELEASE_SKILL_LAST_BEFORE_ACTION = 7, // 

        [EnumName("被攻击命中(伤害结算后)unit参数为实际造成伤害")]
        [ST_Param("过滤攻击类型(hit里的additionalAttackType)", defaultValue = AttackType.All)]
        [ST_Param("过滤伤害类型", defaultValue = DamageType.None)]
        BT_BE_HIT = 8, // BT_受击后 (触发参数-AttackType)

        [EnumName("释放技能后")] [ST_Param("技能动作id(0表示所有技能)", defaultValue = 0)]
        BT_RELEASE_SKILL_LAST = 9, // BT_释放技能后(触发参数-技能动作id, 不填表示所有技能)
        [EnumName("删除buff时")] BT_DELETE = 10, // BT_删除时
        [EnumName("所有护盾消失时")] BT_BREAK_ALL_SHIELD = 11,
        [EnumName("技能中断时[结束]")] [ST_Param("技能动作id(0表示所有技能)", defaultValue = 0)] BT_SKILL_BREAK = 12, // BT_技能中断时

        [EnumName("主动触发某个buff时")] [ST_Param("buff原型id", defaultValue = 0)]
        BT_CASTER_TRIGGER = 13, // BT_施法者触发(触发参数-buff原型id)

        [EnumName("攻击命中(伤害结算后)unit参数为实际造成伤害")]
        [ST_Param("过滤攻击类型(hit里的additionalAttackType)", defaultValue = AttackType.All)]
        [ST_Param("过滤伤害类型", defaultValue = DamageType.None)]
        BT_HIT = 14, // BT_受击后 (触发参数-AttackType)
        [EnumName("真正击杀后")]         
        [ST_Param("是否排除召唤物（默认不排除）", defaultValue = 0)]
        [ST_Param("HitId", defaultValue = 0)]
        BT_REAL_KILL_AFTER = 15, // 真正击杀后
        [EnumName("被击杀后(致死伤害，可能复活)")] BT_BE_KILL_AFTER = 16, // 被击杀后
        [EnumName("击杀后(致死伤害，可能复活)")] BT_KILL_AFTER = 17, // 击杀后

        [EnumName("回合开始")] [ST_Param("同阵营优先级(0-5,0最高)", defaultValue = 0)]
        BT_ROUND_START = 18, // 回合开始

        [EnumName("友方单位死亡")] 
        [ST_Param("筛选heroId，0为不筛选", defaultValue = 0)]
        [ST_Param("是否排除召唤物(默认不排除)", defaultValue = 0)]
        BT_FRIEND_DEAD = 19, // 友方单位死亡(筛选heroId，0为不筛选)

        [EnumName("敌方单位死亡")] 
        [ST_Param("筛选heroId，0为不筛选", defaultValue = 0)]
        [ST_Param("是否排除召唤物(默认不排除)", defaultValue = 0)]
        BT_ENEMY_DEAD = 20, // 敌方单位死亡(筛选heroId，0为不筛选)

        [EnumName("他人释放技能(技能动作前)")] [ST_Param("阵营，0为全阵营,1为友方，2为敌方", defaultValue = 0)]
        BT_OTHER_USE_SKILL_BEFORE_ACTION = 21, // 他人释放技能(触发参数-阵营，0为全阵营,1为友方，2为敌方) // uint为skillProtoId

        [EnumName("他人释放技能")] [ST_Param("阵营，0为全阵营,1为友方，2为敌方", defaultValue = 0)]
        BT_OTHER_USE_SKILL = 22, // 他人释放技能(触发参数-阵营，0为全阵营,1为友方，2为敌方)
        [EnumName("普通攻动作完成后")] BT_AKT_ACTION_COMPLETE = 23, // BT_普通攻动作完成后
        [EnumName("变身开始")] BT_ROLE_TRANSFORM_START = 24, // 变身开始
        [EnumName("变身结束")] BT_ROLE_TRANSFORM_END = 25, // 变身结束
        [EnumName(" 普通攻击时")] BT_COMMON_ATKING = 26, // 普通攻击时
        [EnumName(" 普通攻击结束")] BT_COMMON_ATK_COMPLETE = 27, // 普通攻击结束（忽略后摇动作）

        [EnumName("复活完成")] [ST_Param("对应添加复活数据buff的原型id", defaultValue = 0)]
        BT_REBORN_COMPLETE = 28, // 复活完成(触发参数-对应添加复活数据buff的原型id)be

        [EnumName("mp大等于")] [ST_Param("mp千分比", defaultValue = 0)]
        BT_MP_CHANGE_BIGGER = 29, // mp大等于(触发参数-mp千分比)
        [EnumName("助攻")] BT_ASSISTS_KILL = 30, // 助攻

        [EnumName("回合预开始")] [ST_Param("同阵营优先级(0-5,0最高)", defaultValue = 0)]
        BT_PRE_ROUND_START = 31, // 回合预开始
        [EnumName("技能锁蓝结束时）")] BT_SKILL_REAL_COMPLETE = 32, // 技能结束（锁蓝状态完成）
        [EnumName("真正死亡")] BT_REAL_DEAD = 33, // 真正死亡
        [EnumName("召唤者召唤完成时（触发者是召唤物,载体是召唤者）")] BT_SUMMON_END = 34, // 召唤完成（触发者是召唤物，一般载体是召唤者）

        [EnumName("普攻暴击")] [ST_Param("是否过滤装备攻击", defaultValue = false)]
        BT_NORMAL_ATK_CRIT = 35, // 普攻暴击

        [EnumName("普攻被暴击")] [ST_Param("是否过滤装备攻击", defaultValue = false)]
        BT_NORMAL_ATK_BE_CRIT = 36, // 普攻被暴击
        [EnumName("斩杀")] BT_BEHEADING = 37, // 斩杀
        [EnumName("被斩杀")] BT_BE_BEHEADING = 38, // 被斩杀

        [EnumName("触发动态免疫bufftag ")] [ST_Param("免疫bufftag", defaultValue = 0)]
        BT_DYNAMIC_IMMUNE_BUFF_TAG = 39, // 触发动态免疫bufftag (触发参数-免疫bufftag)
        [EnumName("屏蔽某个技能时触发 ")] BT_SHIELD_SKILL = 40, // 屏蔽某个技能时触发

        [EnumName("普通技能暴击时 ")] [ST_Param("是否过滤装备攻击", defaultValue = false)]
        BT_COMMON_SKILL_CRIT = 41, // 普通技能暴击时
        [EnumName("闪避他人攻击时 ")] BT_OTHER_ATTACK_MISSED = 42, //闪避他人攻击时

        [EnumName("普通技能被暴击时 ")] [ST_Param("是否过滤装备攻击", defaultValue = false)]
        BT_COMMON_SKILL_BE_CRIT = 43, // 普通技能被暴击时
        [EnumName("系魂进入假死状态 ")] BT_SOUL_MATE_FAKE_DEAD = 44, // 系魂进入假死状态
        [EnumName("系魂进真死 ")] BT_SOUL_MATE_REAL_DEAD = 45, // 系魂进真死

        [EnumName("复活开始 ")] [ST_Param("对应添加复活数据buff的原型id", defaultValue = 0)]
        BT_REBORN_START = 46, // 复活开始 (触发参数-对应添加复活数据buff的原型id)
        [EnumName("触发伤害上限 ")] BT_REACH_HURT_MAX = 47, // 触发伤害上限
        [EnumName("双重施法触发 ")] BT_DOUBLE_SKILL = 48, // 双重施法触发

        [EnumName("受击触发（攻击命中后开始阶段，miss不触发） ")] [ST_Param("触发类型(Hit的TriggerBuffType)", defaultValue = TriggerAttackType_Enum.Nothing)]
        BT_BE_HIT_START = 49, // 受击触发（攻击命中后开始阶段，miss不触发）参数TriggerAttackType_Enum

        [EnumName("添加装备 ")] [ST_Param("装备配置id", defaultValue = 0)] [ST_Param("装备唯一id", defaultValue = 0)]
        BT_ADD_EQUIPMENT = 50, // 添加装备

        [EnumName("攻击触发 （攻击命中后开始阶段，miss不触发）")] [ST_Param("触发类型(Hit的TriggerBuffType)", defaultValue = TriggerAttackType_Enum.Nothing)]
        BT_HIT_START = 51, // 攻击触发（攻击命中后开始阶段，miss不触发）参数TriggerAttackType_Enum

        [EnumName("添加或移除buff成功 ")]
        [ST_Param("buff原型id", defaultValue = 0)]
        [ST_Param("添加还是移除（0-不判断，1添加，2移除）", defaultValue = 0)]
        BT_ADD_OR_REMOVE_BUFF_SUC = 52, // 添加或移除buff成功 参数buff原型id
        [EnumName("被作为攻击目标 ")] BT_BECOME_TARGETBC_ADD = 53, //被作为攻击目标;
        [EnumName("取消作为攻击目标之前 ")] BT_BECOME_TARGETBC_REMOVE_BEFORE = 54, //取消作为攻击目标之前;

        [EnumName("回合开始(预触发，为实现装备、羁绊的统一触发顺序) ")]
        BT_ROUND_START_PRE = 55, // 回合开始(预触发，为实现装备、羁绊的统一触发顺序)
        [EnumName("buff开始运行 ")] BT_BUFF_START_UPDATE = 56, // buff开始运行

        [EnumName("加血时(参数int1为当前血量,int2为血量上限,uint为加的血量)")]
        [ST_Param("血量总量大于某血量千分比", defaultValue = 0)]
        BT_HP_CHANGE_UP = 57, // 回血 血量大于(触发参数-血量千分比)
        [EnumName("冲锋 ")] BT_SPRINT_START = 58, // 冲锋

        [EnumName("暴击--结算前触发")]
        [ST_Param("过滤攻击类型", defaultValue = AttackType.All)]
        [ST_Param("过滤伤害类型", defaultValue = DamageType.None)]
        BT_ATK_CRIT_BEFORE_CAL_HURT = 59, // 暴击

        [EnumName("被暴击--结算前触发")]
        [ST_Param("过滤攻击类型", defaultValue = AttackType.All)]
        [ST_Param("过滤伤害类型", defaultValue = DamageType.None)]
        BT_ATK_BE_CRIT_BEFORE_CAL_HURT = 60, // 暴击

        [EnumName("召唤源召唤完成时（触发者是召唤物，载体是召唤源）")]
        BT_SUMMON_END_NOTIFY_SOURCE = 61, // 召唤完成（触发者是召唤物，一般载体是召唤者）
        [EnumName("取消作为攻击目标之后 ")] BT_BECOME_TARGETBC_REMOVE_AFTER = 62, //取消作为攻击目标之后;
        [EnumName("战斗回合结束(胜负结算)(公共角色、活着的英雄会触发)")] BT_BATTLE_ROUND_END = 63, //战斗回合结束;

        [EnumName("攻击目标触发飞行道具")] [ST_Param("对应hitid，0就不过滤", defaultValue = 0)]
        BT_AFTER_HIT_ADD_PROJECTILE = 64,

        [EnumName("攻击目标触发buff")] [ST_Param("buff原型id", defaultValue = 0)]
        BT_AFTER_HIT_ADD_BUFF = 65,

        [EnumName("属性变化通知(有可能数值没变化)")]
        [ST_Param("指定属性", defaultValue = ATTRIBUTE_DEF.ATTR_NONE)]
        [ST_Param("变化类型", defaultValue = CalChangeType.Add)]

        BT_CHANGE_PROPERTY = 66,
        [EnumName("星级变化")] BT_CHANGE_HERO_STAR = 67,
        [EnumName("血量上限变化")] BT_HP_MAX_CHANGE = 68,       

        [EnumName("伤害预计算成功(只触发一次)")] [ST_Param("对应触发id", defaultValue = 0)]
        BT_INJURY_PRE_CALCULATION_SUC = 69,
        [EnumName("被作为技能攻击目标 ")] BT_BECOME_SKILLTARGETBC_ADD = 70, //被作为技能攻击目标;
        [EnumName("取消作为技能攻击目标之前 ")] BT_BECOME_SKILLTARGETBC_REMOVE_BEFORE = 71, //取消作为技能攻击目标之前;
        [EnumName("取消作为技能攻击目标之后 ")] BT_BECOME_SKILLTARGETBC_REMOVE_AFTER = 72, //取消作为技能攻击目标之后;
        [EnumName("双人支援开始")] BT_TEAM_ASSIST_START = 73, // 双人支援开始
        [EnumName("通知我方开始支援其他战场")] BT_I_ASSIST_OTHER_BF = 74, // 
        [EnumName("战斗回合结束(切回准备阶段)(死亡的英雄不会触发)")] BT_CLOSE_BATTLE = 75, //战斗回合结束;
        [EnumName("改变普攻目标")] BT_CHANGE_ATK_TARGET = 76,
        [EnumName("中断施法，非正常打断")] BT_BREAK_SKILL_UNUSUAL = 77,
        [EnumName("装备层数变化时")] BT_CHANGE_EQUIP_LAYER = 78,
        [EnumName("受到治疗时(包括过量治疗)")] // ThisBuff_TriggerParaValue公式获得uint将用于治疗
        BT_BE_CURED = 80,
        [EnumName("真正死亡-开始")] 
        BT_REAL_DEAD_START = 81, // 真正死亡
        [EnumName("buff添加时(仅准备阶段生效用于表现)")] 
        BT_READY_STAGE_ADD = 82, // 准备阶段添加时，这样可以只执行准备阶段的触发表现效果
        [EnumName("护盾添加时(护盾值从0到有)")] 
        BT_ADD_SHEILD = 83,
        [EnumName("当前血量变化时")] 
        BT_HP_NOW_CHANGE = 84,
        [EnumName("主动释放技能-前")] 
        BT_AI_RELEASE_SKILL_BEFORE = 86,
        [EnumName("技能飞行道具发射时")] 
        BT_SKILLATTACK_PROJECTILE_FIRE = 87,
        [EnumName("普攻飞行道具发射时")] 
        BT_NORMALATTACK_PROJECTILE_FIRE = 88,
        [EnumName("被击飞时")] 
        BT_BE_FLOAT_HIT = 89, 
        [EnumName("被击到地底时")] 
        BT_BE_KNOCK_HIT = 90,
        [EnumName("己方胜利时")] 
        BT_BE_VICTORY = 91,
        [EnumName("友军召唤成功时")]
        BT_BE_FRIEND_SUMMON_SUCCESS = 92,
        [EnumName("敌军召唤成功时")]
        BT_BE_ENEMY_SUMMON_SUCCESS = 93,
        [EnumName("死亡开始(buff死亡清除前触发)")] 
        BT_DEAD_START = 94, // 真正死亡
        [EnumName("该buff生命保护触发时(该buff带添加生命保护效果)")]
        BT_HP_PROTECT = 95,
        [EnumName("自己被召唤时(触发者是召唤物,载体是召唤物)")] BT_BE_SUMMON = 96,
        [EnumName("消耗技能所需能量释放技能时")] BT_BE_USE_EXPEND_SKILL = 97,
        [EnumName("设置buff状态时")]  
        [ST_Param("buff状态", defaultValue = 0)]
        [ST_Param("buff状态拓展2", defaultValue = 0)]
        BT_BE_STATUS = 98,
        [EnumName("将自己装备复制给别人成功时")]  
        [ST_Param("规则类型", defaultValue = DropEquipRule.None)]
        BT_BE_DROP_EQUIPMENT_SUCCESS = 99,
        [EnumName("准备阶段全局buff刷新类型的触发器")]  
        [ST_Param("准备阶段全局刷新类型枚举(需要触发的枚举)", defaultValue = 0)]
        BT_WAIT_REFRESH_TRIGGER = 100,
        [EnumName("技能选择目标时(thisBuff.triggerParaValue(5)=0表示选择失败")]  
        [ST_Param("技能Id", defaultValue = 0)]
        BT_SKILL_SELECT_TARGET = 101,
        
        [EnumName("战斗阶段全局buff刷新类型的触发器")]
        [ST_Param("战斗全局刷新类型枚举(需要触发的枚举)", defaultValue = 0)]
        BT_BATTLE_REFRESH_TRIGGER = 102,
        
        [EnumName("Buff被免疫时(buff添加失败)")]
        [ST_Param("buff原型", defaultValue = 0)]
        BT_IMMUNE = 103,
        
        [EnumName("友方单位死亡开始时")]
        [ST_Param("筛选heroId，0为不筛选", defaultValue = 0)]
        [ST_Param("是否排除召唤物(默认不排除)", defaultValue = 0)]        
        BT_FRIEND_DEAD_BEGIN = 104,
        
        [EnumName("敌方单位死亡开始时")]
        [ST_Param("筛选heroId，0为不筛选", defaultValue = 0)]
        [ST_Param("是否排除召唤物(默认不排除)", defaultValue = 0)]        
        BT_ENEMY_DEAD_BEGIN = 105,
        
        [EnumName("预留1")] BT_RESERVE_1 = 110,
        [EnumName("预留2")] BT_RESERVE_2 = 111,
        [EnumName("预留3")] BT_RESERVE_3 = 112,
        [EnumName("预留4")] BT_RESERVE_4 = 113,
        [EnumName("预留5")] BT_RESERVE_5 = 114,
        [EnumName("不在被触发")] BT_MAX, // 不在被触发
    }

    public enum BUFF_EFFECT
    {
        [EnumName("空")] NONE = 0,

        [EnumName("改变属性")]
        [ST_Param("属性", defaultValue = ATTRIBUTE_DEF.ATTR_NONE)]
        [ST_Param("数值", defaultValue = 0)]
        [ST_Param("数值类型(变化类型，和属性意义无关)", defaultValue = PARA_TYPE.PARA_TYPE_INTEGER)]
        [ST_Param("目标类型", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("伤害类型(只在伤害时有效)", defaultValue = DamageType.Magical)]
        [ST_Param("是否自动设置对应实时属性（默认是，例如设置mp最大值自动设置mp）", defaultValue = true)]
        [ST_Param("是否ap加成", defaultValue = false)]
        [ST_Param("是否记录属性变化", defaultValue = false)]
        [ST_Param("是否取当前属性计算加成（否:取初始属性）", defaultValue = false)]
        [ST_Param("属性记录类型(key=原型Id*100+属性)", defaultValue = BuffAttributeRecordType.RealChange)]
        [ST_Param("属性记录数值处理类型", defaultValue = VariableValueDealType.Add)]
        [ST_Param("改变属性处理类型", defaultValue = ValueDealType.Add)]
        BE_ATTR = 1,

        [EnumName("改变复杂属性")]
        [ST_Param("复杂属性类型", defaultValue = ATTRIBUTE_COMPLEX_DEF.NONE)]
        [ST_Param("对应参数(请使用复杂属性编辑)", defaultValue = 0)]
        BE_COMPLEX_ATTR = 2,

        [EnumName("状态")]
        [ST_Param("状态类型(BUFF_STATUS编辑)", defaultValue = 0)]
        [ST_Param("是否打断当前状态", defaultValue = false)]
        [ST_Param("状态类型2(BUFF_STATUS_2编辑)", defaultValue = 0)]
        BE_STATUS = 3,

        [EnumName("添加Buff")]
        [ST_Param("buffCfgId", defaultValue = 0)]
        [ST_Param("目标类型", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("层数", defaultValue = 1)]
        [ST_Param("是否延时执行", defaultValue = false)]
        [ST_Param("是否传递施法者", defaultValue = false)]
        [ST_Param("是否用公式表示buffCfgId(否:公式表示层数)", defaultValue = false)]
        [ST_Param("是否传递触发数据", defaultValue = false)]
        [ST_Param("是否多次添加（一般共存使用，层数对应添加次数）", defaultValue = false)]
        [ST_Param("是否传递技能动作数据(暂时不支持延时添加)", defaultValue = false)]
        BE_ADD = 4,

        [EnumName("删除Buff")]
        [ST_Param("buff原型Id", defaultValue = 0)]
        [ST_Param("目标类型", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("层数(填-1删除所有层数)", defaultValue = 1)]
        [ST_Param("是否延时执行", defaultValue = false)]
        [ST_Param("匹配移除buff释放目标类型", defaultValue = BUFF_EFFECT_TARGET.NONE)]
        [ST_Param("是否只移除第一个匹配buff", defaultValue = false)]
        BE_DEL = 5,

        [EnumName("使用技能")]
        [ST_Param("skillid", defaultValue = 0)]
        [ST_Param("是否skillActionId（默认是，否的话为bindskillId）", defaultValue = true)]
        [ST_Param("是否执行技能流程", defaultValue = false)]
        [ST_Param("传递目标类型", defaultValue = BUFF_EFFECT_TARGET.NONE)]
        [ST_Param("是否检测技能释放状态", defaultValue = false)]
        BE_USE_SKILL = 6,

        [EnumName("修改技能")] [ST_Param("原技能actionId", defaultValue = 0)] [ST_Param("技能actionId", defaultValue = 0)]
        BE_CHANGE_SKILL = 7, // 原技能actionId|技能actionId

        [EnumName("添加特效")]
        [ST_Param("目标类型", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("特效名称", defaultValue = "")]
        [ST_Param("特效位置", defaultValue = CharacterHangPoint.SupportHangPointType.GROUND_LOC)]
        [ST_Param("是否循环特效(是:buff控制；否:特效自己控制)", defaultValue = false)]
        [ST_Param("是否绑定点", defaultValue = true)]
        [ST_Param("是否是绑定格子特效", defaultValue = false)]
        [ST_Param("是否随死亡移除", defaultValue = true)]
        [ST_Param("是否绑定角度", defaultValue = false)]
        [ST_Param("是否绑定scale", defaultValue = true)]
        [ST_Param("是否绑定绑定初始角度", defaultValue = true)]
        [ST_Param("皮肤配置参数index(从1开始)", defaultValue = -1)]
        [ST_Param("皮肤配置DataId", defaultValue = -1)]
        [ST_Param("特效脚本使用(挂点特效用)", defaultValue = EffectSpecialScriptType.None)]
        [ST_Param("脚本参数1", defaultValue = 0)]
        [ST_Param("脚本参数2", defaultValue = 0)]
        BE_ADD_EFFECT =
            8, // SKILL_TARGET | 特效名称 | 特效位置 | 是否随buff生效（默认是）|是否绑定点（默认是） | 是否是地面特效（默认否）|是否随死亡移除（默认是）|是否绑定角度（默认是false）|是否绑定scale（默认是true）|是否绑定绑定初始角度（默认是true）

        [EnumName("修改动作")] [ST_Param("原动作名字", defaultValue = "")] [ST_Param("新动作名字", defaultValue = "")]
        [ST_Param("是否替换公共动作", defaultValue = false)]
        BE_CHANGE_ACTION = 9, // 修改动作 原动作名字 | 新动作名字

        [EnumName("释放动作")]
        [ST_Param("动作名字", defaultValue = "")]
        [ST_Param("是否默认动作", defaultValue = false)]
        [ST_Param("动作id（有的话以id起效）", defaultValue = 0)]
        [ST_Param("通用技能的传入目标", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("施法目标类型", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("是否替换公共动作", defaultValue = false)]
        BE_USE_ACTION =
            10, // 释放动作   动作名字|是否默认动作（默认否）|动作id（有的话以id起效）| BUFF_EFFECT_TARGET通用技能的传入目标（默认无）| 施法目标类型BUFF_EFFECT_TARGET（默认自己）

        [EnumName("添加音效")]
        [ST_Param("参数音效名字", defaultValue = "")]
        [ST_Param("音效包名字", defaultValue = "")]
        [ST_Param("是否通过buff移除", defaultValue = false)]
        [ST_Param("是否死亡自动移除", defaultValue = false)]
        [ST_Param("皮肤配置参数index(从1开始)", defaultValue = -1)]
        BE_ADD_SOUND = 12, // 添加音效  参数音效名字 | 音效包名字 | 是否通过buff移除（默认否）|是否死亡自动移除（默认否）

        [EnumName("通过特殊key记录相关对象")]
        [ST_Param("记录key类型", defaultValue = RecordKeyType.None)]
        [ST_Param("key参数", defaultValue = 0)]
        [ST_Param("是否不需要移除", defaultValue = false)]
        BE_RECORD_TARGET = 13, // 通过特殊key记录相关对象  记录key类型（1技能tag，2buff原型id）|key参数|是否不需要移除（默认否）
        
        [EnumName("添加临时装备")]
        [ST_Param("装备池子id", defaultValue = 0)]
        [ST_Param("添加临时装备个数", defaultValue = 0)]
        [ST_Param("生成类型(默认0，ryze填10)", defaultValue = 0)]
        BE_ADD_TEMP_EQUIPMENT = 14, 

        [EnumName("添加组合效果Buff(根据功能来源实时添加)")]
        [ST_Param("组合触发器buff原型Id", defaultValue = 0)]
        [ST_Param("组合buff的功能来源", defaultValue = BuffCombineKey.Other)]
        [ST_Param("参数1", defaultValue = 0)]
        BE_ADD_COMBINE_EFFECT_BUFF = 15, 

        [EnumName("添加组合触发Buff(根据功能来源实时添加)")]
        [ST_Param("组合buff的功能来源", defaultValue = BuffCombineKey.Other)]
        BE_ADD_COMBINE_TRIGGER_BUFF = 16,

        [EnumName("修改Buff时间")]
        [ST_Param("目标", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("buff原型Id", defaultValue = 0)]
        [ST_Param("数值", defaultValue = 0)]
        [ST_Param("数值类型", defaultValue = PARA_TYPE.PARA_TYPE_INTEGER)]
        [ST_Param("是否增加", defaultValue = false)]
        [ST_Param("是否修改buff的最大时间", defaultValue = false)]
        BE_CHANGE_BUFF_TIME = 17,

        [EnumName("添加飘字效果")]
        [ST_Param("飘字类型", defaultValue = FloatTextType.None)]
        [ST_Param("值", defaultValue = 0)]
        BE_ADD_FLOAT_TEXT = 18,
        
        [EnumName("删除对应buffTag的buff")] [ST_Param("buffTag", defaultValue = 0)]
        BE_DEL_BUFF_TAG = 19, // buffTag 

        [EnumName("免疫buff标签")] [ST_Param("buffTag", defaultValue = 0)] [ST_Param("是否动态免疫(能被触发器39触发)", defaultValue = false)]
        BE_IMMUNE_BUFF_TAG = 20, // 免疫buff标签 buffTag | 是否单次免疫效果（默认否）

        [EnumName("变形")]
        [ST_Param("原型heroid", defaultValue = 0)]
        [ST_Param("变形开始动作", defaultValue = "")]
        [ST_Param("变形回来动作", defaultValue = "")]
        [ST_Param("是否不设置属性和hitdata", defaultValue = false)]
        [ST_Param("是否重置自身AI", defaultValue = false)]
        [ST_Param("是否只是变换模型", defaultValue = false)]
        [ST_Param("是否打断当前技能", defaultValue = false)]
        [ST_Param("特殊规则来源表", defaultValue = HeroTransformRule.None)]
        BE_SKILL_CHANGE_SHAPE = 22, // 技能变形 原型heroid | 变形开始动作 | 变形回来动作 | 是否不设置属性（默认否）|是否重置自身AI（默认否）|是否只是变换模型（默认否）

        [EnumName("缩放比例")]
        [ST_Param("参数千分比例", defaultValue = 1000)]
        [ST_Param("是否tween动画", defaultValue = false)]
        [ST_Param("动画时间（毫秒）", defaultValue = 0)]
        [ST_Param("死亡是否失效", defaultValue = true)]
        BE_CHANGE_SCALE = 23, // 缩放比例 参数千分比例 | 是否tween动画（默认否） | 动画时间（毫秒）

        [EnumName("召唤")]
        [ST_Param("召唤id", defaultValue = 0)]
        [ST_Param("召唤者，默认buff目标", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("飞行道具名字", defaultValue = "")]
        [ST_Param("召唤数据来源（默认无，取召唤者）", defaultValue = BUFF_EFFECT_TARGET.NONE)]
        [ST_Param("是否拷贝召唤数据源英雄id", defaultValue = false)]
        [ST_Param("召唤起点飞行道具拥有者", defaultValue = BUFF_EFFECT_TARGET.NONE)]
        [ST_Param("召唤起点飞行道具名字", defaultValue = "")]
        [ST_Param("是否使用buff目标选择召唤位置(覆盖召唤表的目标选择)", defaultValue = false)]
        BE_SUMMON = 24,

        [EnumName("使用飞行道具")]
        [ST_Param("名字", defaultValue = "")]
        [ST_Param("hitId", defaultValue = 0)]
        [ST_Param("目标类型", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("是否加到普攻上(否添加技能hit)", defaultValue = true)]
        [ST_Param("单次技能最大发射次数", defaultValue = -1)]
        [ST_Param("是否绑定目标身上", defaultValue = true)]
        [ST_Param("全局最大发射次数", defaultValue = -1)]
        [ST_Param("触发阶段", defaultValue = HitBuffTrigger_Enum.ATK_LAST)]
        [ST_Param("是否飞行道具单独数据", defaultValue = false)]
        [ST_Param("触发tag[和hitdata的DonotTriggerReleaseTag对应]",
            defaultValue = TDRConfig.DYNAMIC_ATTACK_RELEASE_TAG.TAG_none)]
        [ST_Param("触发伤害类型,默认none", defaultValue = DamageType.None)]
        BE_USE_OWNER_PROJECTILE =
            25, // 使用飞行道具 名字 | hitId | BUFF_EFFECT_TARGET | 是否加到普攻上(默认1， 0否添加技能hit) | 单次技能最大发射次数(默认无限) | 是否绑定目标身上（默认是）| 全局最大发射次数(默认无限) | 触发阶段（默认攻击后，0攻击前，1攻击后）

        [EnumName("攻击数据添加buff")]
        [ST_Param("buffCfgId", defaultValue = 0)]
        [ST_Param("是否到时移除", defaultValue = true)]
        [ST_Param("buff目标（0受击目标1自己，默认0）", defaultValue = HitBuffTarget_Enum.Target)]
        [ST_Param("触发类型（对应hit中的triggerBuffType）", defaultValue = TriggerAttackType_Enum.Trigger_NormalAttack)]
        [ST_Param("触发阶段", defaultValue = HitBuffTrigger_Enum.ATK_LAST)]
        [ST_Param("单次技能最大触发数", defaultValue = -1)]
        [ST_Param("是否传递施法者", defaultValue = false)]
        [ST_Param("buff触发数", defaultValue = -1)]
        [ST_Param("单次技能是否是限制受击者", defaultValue = false)]
        [ST_Param("是否拷贝攻击数据到飞行道具", defaultValue = false)]
        [ST_Param("触发tag[和hitdata的DonotTriggerReleaseTag对应]",
            defaultValue = TDRConfig.DYNAMIC_ATTACK_RELEASE_TAG.TAG_none)]
        [ST_Param("触发伤害类型,默认none", defaultValue = DamageType.None)]
        BE_ADD_ATK_BUFF =
            26, // 普攻数据添加buff buffId | 是否到时移除 | buff目标（0受击目标1自己，默认0）| 触发类型(默认1普攻，2技能，3全部) | 触发阶段（0攻击前，1攻击后）| 单次技能最大触发数（-1无限）|是否传递施法者(默认否)|buff触发数（-1无限）|单次技能是否是限制受击者（默认否）|是否拷贝攻击数据到飞行道具（默认否）

        [EnumName("改变星级")] [ST_Param("星级变化", defaultValue = 0)]
        BE_CHANGE_STAR = 28, // 改变星级 星级变化

        [EnumName("使用通用技能")]
        [ST_Param("名字", defaultValue = "")]
        [ST_Param("通用技能的传入目标（默认无）", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_ALL)]
        [ST_Param("通用技能使用者", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        BE_USE_COMMON_SKILL = 29, // 使用通用技能 名字 | BUFF_EFFECT_TARGET通用技能的传入目标（默认无）

        [EnumName("放逐动画")]
        [ST_Param("开始时间", defaultValue = 0)]
        [ST_Param("滞空时间", defaultValue = 0)]
        [ST_Param("落下时间（毫秒）", defaultValue = 0)]
        [ST_Param("高度(厘米默认300)", defaultValue = 300)]
        BE_EXILE_ANIMATION = 30, // 放逐动画 开始时间|滞空时间|落下时间（毫秒）|高度(厘米默认300)

        [EnumName("添加溅射")]
        [ST_Param("形状", defaultValue = SplashAttackType_Enum.Back)]
        [ST_Param("hitId", defaultValue = 0)]
        [ST_Param("范围", defaultValue = 0)]
        [ST_Param("衰减百分比(默认20%)", defaultValue = 20)]
        BE_ADD_SPLASH_ATK = 31, // 添加溅射 形状（0背后1扇形）| hitId | 范围 | 衰减百分比(默认20%)

        [EnumName("动态添加命中特效")]
        [ST_Param("特效名字", defaultValue = "")]
        [ST_Param("特效挂点", defaultValue = SupportHangPointType.GROUND_LOC)]
        [ST_Param("普攻还是技能", defaultValue = TriggerAttackType_Enum.Trigger_NormalAttack)]
        BE_ADD_DYNAMIC_ATK_EFFFECT = 32, // 动态添加命中特效  特效名字 | 特效挂点 | 普攻还是技能(1普攻2技能)

        [EnumName("伤害")]
        [ST_Param("数值类型", defaultValue = PARA_TYPE.PARA_TYPE_INTEGER)]
        [ST_Param("数值", defaultValue = 0)]
        [ST_Param("目标", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("伤害类型", defaultValue = DamageType.Magical)]
        [ST_Param("伤害显示类型", defaultValue = FloatTextType.None)]
        [ST_Param("是否直接设置血量", defaultValue = false)]
        [ST_Param("是否ap加成", defaultValue = false)]
        [ST_Param("是否记录本次伤害", defaultValue = false)]
        [ST_Param("是否使用该效果首次确定的伤害值(周期伤害时保持每次伤害一致)", defaultValue = false)]
        [ST_Param("伤害类型", defaultValue = AttackType.SkillAttack)]
        [ST_Param("伤害参数设置枚举(设置后不计算某些参数)", defaultValue = 0)]
        BE_HURT = 33, //伤害 数值类型PARA_TYPE | 数值 |  SKILL_TARGET(默认自己) |DamageType(默认技能伤害)|FloatTextType伤害显示类型(-1表示自动设置)|是否直接设置血量（默认false）| 是否ap加成（默认是false）|是否记录本次伤害（默认false）

        [EnumName("播放shader效果")]
        [ST_Param("shader类型", defaultValue = BUFF_SHADER_EFFECT.COMMON)]
        [ST_Param("shader名字", defaultValue = "")]
        [ST_Param("是否buff移除同时移除", defaultValue = true)]
        [ST_Param("是否shader支持buff多层", defaultValue = false)]
        [ST_Param("是否自动带入星级", defaultValue = false)]
        [ST_Param("皮肤配置参数index(从1开始)", defaultValue = -1)]
        BE_PLAY_SHADER_EFFECT =
            34, // 播放shader效果（shader类型0通用1刺客隐身2眩晕变色3curveType）| shader名字(暂时通用) | 是否buff移除同时移除（默认是）|是否shader支持buff多层（默认false）|是否自动带入星级（默认false）

        [EnumName("修改属性放大系数")]
        [ST_Param("属性类型", defaultValue = ATTRIBUTE_DEF.ATTR_NONE)]
        [ST_Param("数值(千分比)", defaultValue = 0)]
        [ST_Param("目标", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("是否按加减计算（否就是乘除）", defaultValue = false)]
        [ST_Param("是否自动设置对应实时属性（默认是，例如设置mp最大值自动设置mp）", defaultValue = true)]
        BE_ATTR_SCALE = 35, // 修改属性放大系数 ATTRIBUTE_DEF | 数值(2000表示2倍) | SKILL_TARGET(默认自己)

        [EnumName("设置仇恨目标")]
        [ST_Param("设置目标", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("仇恨目标", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_CASTER)]
        [ST_Param("仇恨类型(是嘲讽/否清除仇恨)", defaultValue = true)]
        [ST_Param("仇恨数值(默认不填/>0嘲讽/<0清除)", defaultValue = 0)]
        BE_SET_HATE_TARGET = 36, // 设置仇恨目标 SKILL_TARGET(默认自己) |  SKILL_TARGET(默认施法者) | 仇恨值 默认1000/-1000就是消除仇恨

        [EnumName("周围格子加特效")]
        [ST_Param("特效名称", defaultValue = "")]
        [ST_Param("范围", defaultValue = 0)]
        [ST_Param("中点选取类型（0buff载体1buff施法者2指定点，默认0）", defaultValue = 0)]
        [ST_Param("行", defaultValue = 0)]
        [ST_Param("列", defaultValue = 0)]
        BE_ADD_GRID_EFFECT = 37, // 周围格子加特效 特效名称 | 范围 | 中点选取类型（0buff载体1buff施法者2指定点，默认0）| 行 | 列

        [EnumName("执行hit")] [ST_Param("hitid", defaultValue = 0)]
        BE_EXCUTE_HIT = 38, // 执行hit hitid

        [EnumName("执行ui变化")]
        [ST_Param("变化类型", defaultValue = CHANGE_TYPE.HIDE_INFOBAR)]
        [ST_Param("参数1无，2Mp_UI_Status<1锁蓝2中毒3破法4羁绊染色>, 3State_Effect<1减速2沉默3重伤4致盲>,4State_Scale<100%百分比缩放>", defaultValue = 0)]        
        [ST_Param("参数2，额外的参数", defaultValue = "")]
        BE_UI_CHANGE = 39, // 执行ui变化 (1隐藏信息栏,2设置mp状态3异常状态ui显示) | (1无，2Mp_UI_Status<1锁蓝2中毒3破法>, 3State_Effect<1减速2沉默3重伤4致盲>) 
        [EnumName("删除当前buff")] BE_DEL_MYSELF = 40, // 删除自己
        [EnumName("可以再次攻击")] BE_ENABLE_NORMAL_ATK = 41, // 可以再次攻击

        [EnumName("增加召唤生物的时间")]
        [ST_Param("数值", defaultValue = 0)]
        [ST_Param("数值类型", defaultValue = PARA_TYPE.PARA_TYPE_INTEGER)]
        BE_ADD_SUMMON_LIFT_TIME = 42, // 增加召唤生物的时间 数值 | 数值类型PARA_TYPE

        [EnumName("拷贝自己")]
        [ST_Param("召唤配置id", defaultValue = 0)]
        [ST_Param("召唤者", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("飞行道具名字（默认无，有则通过召唤者找对应飞行道具）", defaultValue = "")]
        [ST_Param("key(用于准备阶段也存在的召唤物对应)", defaultValue = 0)]
        BE_COPY_HERO = 43, // 拷贝自己 召唤配置id|BUFF_EFFECT_TARGET(召唤者，默认buff目标)|飞行道具名字（默认无，有则通过召唤者找对应飞行道具）

        [EnumName("增加技能无消耗释放次数")] [ST_Param("次数", defaultValue = 0)] [ST_Param("是否 - 非法师的额外释放次数", defaultValue = false)]
        BE_ADD_SKILL_COUNT = 44, // 增加技能无消耗释放次数 次数

        [EnumName("添加装备效果使用cd")] [ST_Param("equipId", defaultValue = 0)] [ST_Param("cd(毫秒，默认0无限)", defaultValue = 0)]
        BE_ADD_EQUIPMENT_USE_RECORD = 45, //添加装备效果使用cd equipId | cd(毫秒，默认0无限)

        [EnumName("替换普攻动作")] [ST_Param("替换的动作名字", defaultValue = "")]
        BE_REPLACE_COMMON_ATK = 46, //替换普攻动作 替换的动作名字

        [EnumName("设置英雄羁绊开关")]
        [ST_Param("目标类型", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("是否开启", defaultValue = false)]
        BE_SET_FETTER_ACTIVE = 47, //BUFF_EFFECT_TARGET目标类型|是否开启（默认false）

        [EnumName("增加装备buff使用层数")]
        [ST_Param("层数", defaultValue = 1)]
        [ST_Param("是否覆盖层数（默认否）。是:层数直接设置; 否:层数用于添加", defaultValue = false)]
        [ST_Param("是否刷新装备效果（默认是）。", defaultValue = true)]
        BE_ADD_EQUIPMENT_BUFF_LAYER = 48, //增加装备buff使用层数 equipId
        [EnumName("激活系魂系统")] BE_ACTIVE_SOUL_MATE = 49, //激活系魂系统

        [EnumName("设置属性比例开关")]
        [ST_Param("属性", defaultValue = ATTRIBUTE_DEF.ATTR_NONE)]
        [ST_Param("是否关闭", defaultValue = true)]
        [ST_Param("目标类型", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        BE_ATTR_SET_CLOSE_FLAG = 52, //设置属性比例开关，关闭则属性比例为0  参数：ATTRIBUTE_DEF | 是否关闭(1关闭默认关闭)| SKILL_TARGET(默认自己)

        [EnumName("添加战场buff")]
        [ST_Param("buffCfgid", defaultValue = 0)]
        [ST_Param("添加方式", defaultValue = BattleFieldAddBuffMoment.Cd)]
        [ST_Param("参数（1对应周期毫秒）", defaultValue = 0)]
        [ST_Param("是否包含添加者", defaultValue = true)]
        [ST_Param("对象类型", defaultValue = BattleFieldBuffTargetType.ALL)]
        [ST_Param("是否随buff移除", defaultValue = true)]
        BE_ADD_BATTLE_FIELD_BUFF =
            53, //添加战场buff buffid|添加方式：1周期|参数（1对应周期毫秒）|是否包含添加者（默认是）|对象类型BattleFieldAddBuffType（1同阵营2敌阵营3全部，默认同阵营）|是否随buff移除（默认是）

        [EnumName("添加技能累计飘字显示")]
        [ST_Param("key", defaultValue = 0)]
        [ST_Param("cd", defaultValue = 0)]
        [ST_Param("飘字类型", defaultValue = 0)]
        BE_ADD_SKILL_TEXT_RECORD = 54, //添加技能累计飘字显示 key | cd | floatTextType

        [EnumName("棋子相关显示或隐藏")]
        [ST_Param("是否显示", defaultValue = false)]
        [ST_Param("控制种类", defaultValue = UnitShowHideTargetType.Shadow)]
        BE_SHOW_OR_HIDE_UNIT = 55,

        [EnumName("替换所有飞行道具特效")]
        [ST_Param("特效", defaultValue = "")]
        [ST_Param("替换类型（1普攻，2技能，3全部，默认1）", defaultValue = 1)]
        [ST_Param("条件（1暴击，2技能暴击，0全部，默认0）", defaultValue = 0)]
        BE_REPLACE_PROJECTILE_EFFECT = 56, //替换所有飞行道具特效 特效|替换类型（1普攻，2技能，3全部，默认1）

        [EnumName("金币和基础装备（当赛季的1、2级装备）的伪概率掉落")]
        [ST_Param("type（1金币，2装备）", defaultValue = 1)]
        [ST_Param("掉落初始概率", defaultValue = 50)]
        [ST_Param("掉落后减少的掉落概率", defaultValue = 17)]
        [ST_Param("未掉落增加的掉落概率", defaultValue = 17)]
        [ST_Param("目标类型", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TRIGGER)]
        [ST_Param("掉落数量", defaultValue = 1)]
        [ST_Param("掉落后是否恢复初始概率", defaultValue = true)]
        [ST_Param("野怪回合是否生效", defaultValue = false)]
        BE_DO_SPACEPIRATE_DROP = 57, // 太空海盗掉落 type（1金币，2装备）|掉落初始概率|掉落后减少的掉落概率|未掉落增加的掉落概率|掉落数量

        [EnumName("飞行道具控制")]
        [ST_Param("名字", defaultValue = "")]
        [ST_Param("ctrlType", defaultValue = "")]
        [ST_Param("ctrlCount（默认“ALL”）", defaultValue = "ALL")]
        [ST_Param("customActionLabel（默认“”）", defaultValue = "")]
        BE_PROJECTILE_CONTROLL = 58, //飞行道具控制 名字|ctrlType|ctrlCount（默认“ALL”）|customActionLabel（默认“”）

        [EnumName("改变材质颜色")]
        [ST_Param("是否是粒子系统", defaultValue = true)]
        [ST_Param("componentName", defaultValue = "")]
        [ST_Param("颜色(r*1000000 + g*1000 + b)", defaultValue = 0)]
        BE_CHANGE_MAT_COLOR = 59, //改变材质颜色 是否是粒子系统|componentName|颜色(r*1000000 + g*1000 + b)

        [EnumName("结束技能锁定")] [ST_Param("是否触发技能结束流程（否只触发buff触发器，不真正结束技能流程）", defaultValue = 0)]
        BE_END_SKILL_LOCK = 60, //结束技能锁定（期间锁蓝，结束时触发技能完成事件）

        [EnumName("设置技能释放次数常量")] [ST_Param("数值", defaultValue = 0)]
        BE_SET_SKILL_RELEASETAG_CONST = 61, //设置技能tag常量 数值

        [EnumName("永久修改属性")]
        [ST_Param("属性", defaultValue = ATTRIBUTE_DEF.ATTR_NONE)]
        [ST_Param("数值", defaultValue = 0)]
        [ST_Param("数值类型", defaultValue = PARA_TYPE.PARA_TYPE_INTEGER)]
        [ST_Param("在双人援护中是否修改原始数据", defaultValue = true)]
        BE_CHANGE_ATTR_WHOLE = 62, //永久修改属性 ATTRIBUTE_DEF | 数值 | 数值类型PARA_TYPE

        [EnumName("改变骨骼显示状态")] [ST_Param("节点名字", defaultValue = "")] [ST_Param("是否显示", defaultValue = false)]
        BE_CHANGE_BONE_VISIBLE = 63, //改变骨骼显示状态 节点名字|是否显示

        [EnumName("替换受击特效")]
        [ST_Param("特效名", defaultValue = "")]
        [ST_Param("攻击类型", defaultValue = AttackType.NormalAttack)]
        BE_REPLACE_BEAR_EFFECT = 64, //替换受击特效 特效名|AttackType

        [EnumName("属性变化通知")] [ST_Param("属性", defaultValue = ATTRIBUTE_DEF.ATTR_NONE)]
        BE_ATTR_CHANGE_NOTIFY = 65, //属性变化通知（用在某些非属性变化时机） ATTRIBUTE_DEF

        [EnumName("设置受击按次数计算数据")] [ST_Param("次数", defaultValue = 0)] [ST_Param("是否受击次数使用完后还计算hp", defaultValue = false)]
        BE_SET_HURT_BY_COUNT = 66, //设置受击按次数计算数据 次数 | 是否受击次数使用完后还计算hp（默认false）
        [EnumName("改变阵营")] BE_CHANGE_CAMP = 67, //改变阵营

        [EnumName("修改护盾值")]
        [ST_Param("buff原型id(-1所有护盾)", defaultValue = 0)]
        [ST_Param("数值", defaultValue = 0)]
        [ST_Param("数值类型", defaultValue = PARA_TYPE.PARA_TYPE_INTEGER)]
        BE_CHANGE_SHIELD_VALUE = 68, //buff原型id| 数值 | 数值类型PARA_TYPE

        [EnumName("修改debuff属性")]
        [ST_Param("配置id（生命保护12已弃用）", defaultValue = 0)]
        [ST_Param("数值", defaultValue = 0)]
        [ST_Param("目标类型", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("伤害类型", defaultValue = DamageType.Magical)]
        [ST_Param("攻击类型", defaultValue = AttackType.SkillAttack)]
        [ST_Param("是否ap加成", defaultValue = false)]
        [ST_Param("数值扩展使用方式", defaultValue = ValueExtendType.None)]
        BE_DEBUFF_ATTR = 69, //配置id | 数值 | SKILL_TARGET(默认自己) |DamageType|AttackType|是否ap加成|ValueExtendType

        [EnumName("绑定属性")]
        [ST_Param("属性", defaultValue = ATTRIBUTE_DEF.ATTR_NONE)]
        [ST_Param("谁绑定", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("绑定谁", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_CASTER)]
        BE_BIND_ATTR = 70, //ATTRIBUTE_DEF | SKILL_TARGET(谁绑定)| SKILL_TARGET(绑定谁)

        [EnumName("修改移动模式")]
        [ST_Param("移动类型", defaultValue = AI_MOVE_MODEL.None)]
        [ST_Param("移动动作名字", defaultValue = "run")]
        [ST_Param("移动速度", defaultValue = 120)]
        BE_CHANGE_MOVE_MODEL = 71, //移动类型(AI_MOVE_MODEL)|移动动作名字|移动速度

        [EnumName("从其他人copy装备")]
        [ST_Param("范围目标", defaultValue = BUFF_EFFECT_TARGET.NONE)]
        [ST_Param("数量", defaultValue = 0)]
        [ST_Param("copy对象使用公共技能", defaultValue = "")]
        [ST_Param("copy装备的规则(参数用:隔开)", defaultValue = CopyEquipRuleType.DarkLight_S7)]
        [ST_Param("copy装备的规则参数", defaultValue = "")]
        BE_COPY_EQUIPMENT_FROM_OTHER = 72, //数量|copy对象使用公共技能|筛选specId|筛选classId|使用类型（1召唤用）

        [EnumName("移除buff变量记录")]
        [ST_Param("key", defaultValue = 0)]
        [ST_Param("目标类型", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("附加处理", defaultValue = AfterRecordDeadType.None)]
        [ST_Param("参数1", defaultValue = 0)]
        [ST_Param("参数2", defaultValue = 0)]
        BE_REMOVE_BUFF_VAR_RECORD = 73, //key|SKILL_TARGET|附加处理AfterRecordDeadType | 参数1 | 参数2

        [EnumName("添加buff变量记录")]
        [ST_Param("key", defaultValue = 0)]
        [ST_Param("value", defaultValue = 0)]
        [ST_Param("目标类型", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("数据处理类型", defaultValue = VariableValueDealType.Replace)]
        [ST_Param("附加处理", defaultValue = AfterRecordDeadType.None)]
        [ST_Param("参数1", defaultValue = 0)]
        [ST_Param("参数2", defaultValue = 0)]
        [ST_Param("是否延时添加记录（后续处理会无效）", defaultValue = false)]
        [ST_Param("公式是否用于key值", defaultValue = false)]
        BE_ADD_BUFF_VAR_RECORD = 74, //key|value|SKILL_TARGET|附加处理AfterRecordDeadType | 参数1 | 参数2

        [EnumName("添加准备阶段预览特效(战斗不生效)")]
        [ST_Param("特效名称", defaultValue = "")]
        [ST_Param("特效位置(挂点特效用)", defaultValue = CharacterHangPoint.SupportHangPointType.GROUND_LOC)]
        [ST_Param("是否循环", defaultValue = false)]
        [ST_Param("特效脚本使用(挂点特效用)", defaultValue = EffectSpecialScriptType.None)]
        [ST_Param("脚本参数1", defaultValue = 0)]
        [ST_Param("脚本参数2", defaultValue = 0)]
        [ST_Param("特效类型", defaultValue = StageEffectType.HangPoint)]
        [ST_Param("格子目标选择(格子特效用)", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("屏幕位置(UI特效用)", defaultValue = ScreenLocation.Center)]
        [ST_Param("特效Ab包路径(UI特效用)", defaultValue = EffectAbPath.None)]
        [ST_Param("皮肤表的buff效果索引", defaultValue = 0)]
        [ST_Param("皮肤表的DataId", defaultValue = 0)]
        BE_ADD_READYSTAGE_EFFECT = 75,

        [EnumName("立即杀死")]
        [ST_Param("目标类型", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("攻击目标类型", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_CASTER)]
        [ST_Param("是否屏蔽后续创建单位", defaultValue = false)]
        [ST_Param("是否忽略复活(是的话，有复活也不起效)", defaultValue = true)]
        [ST_Param("是否检查战场状态", defaultValue = true)]
        BE_MAKE_IMMEDIATELY_DEAD = 76,

        [EnumName("主动触发目标对应buff")]
        [ST_Param("目标类型", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("buff原型id", defaultValue = 0)]
        BE_CASTER_TRIGGER_BUFF = 77,

        [EnumName("设置buff禁用状态")]
        [ST_Param("buff原型id(填0，自动取当前buff原型id)", defaultValue = 0)]
        [ST_Param("是否禁用", defaultValue = 1)]
        [ST_Param("来源", defaultValue = BuffAddSource.None)]
        BE_SET_BAN_BUFF_INFO = 78,

        [EnumName("设置ai启动延时帧")] [ST_Param("最小值", defaultValue = 0)] [ST_Param("最大值", defaultValue = 0)]
        BE_SET_AI_START_DELAY_FRAME = 79,

        [EnumName("替换飞行道具")]
        [ST_Param("替换类型", defaultValue = ProjectileReplaceType.NORMAL_ATK)]
        [ST_Param("数据来源", defaultValue = "")]
        [ST_Param("使用次数(-1无限)", defaultValue = -1)]
        [ST_Param("hitid(type 是hitid 才使用)", defaultValue = 0)]
        BE_REPLACE_PROJECTILE = 80,

        [EnumName("属性转换")]
        [ST_Param("原属性类型", defaultValue = ATTRIBUTE_DEF.ATTR_NONE)]
        [ST_Param("目标属性类型", defaultValue = ATTRIBUTE_DEF.ATTR_NONE)]
        [ST_Param("转换比例（千分比）", defaultValue = 1000)]
        [ST_Param("比对属性值", defaultValue = 0)]
        [ST_Param("转换比对类型", defaultValue = TransforAttrCompareType.None)]
        [ST_Param("目标", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("转换计算方法", defaultValue = TransforAttrCalType.Normal)]
        BE_SET_TRANSFOR_ATTR = 81,

        [EnumName("修改技能触发次数记录")]
        [ST_Param("目标", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("SkillReleaseTag来源", defaultValue = BuffSkillReleaseTagResource.FromTriggerParam)]
        [ST_Param("记录key1", defaultValue = 0)]
        [ST_Param("记录key2", defaultValue = 0)]
        [ST_Param("公式替换对应key", defaultValue = 1)]
        BE_CHANGE_SKILL_TRIGGER_CNT = 82,

        [EnumName("修改英雄全局数据记录")]
        [ST_Param("目标", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("记录key", defaultValue = 0)]
        [ST_Param("改变值", defaultValue = 0)]
        [ST_Param("max", defaultValue = 0)]
        [ST_Param("在双人援护中是否修改原始数据", defaultValue = true)]
        [ST_Param("处理方式", defaultValue = VariableValueDealType.Add)]
        [ST_Param("min(大于等于0)", defaultValue = 0)]
        BE_CHANGE_HERO_FOREVER_RECORD = 83,

        [EnumName("S6塔姆吞噬掉落")]
        [ST_Param("是否计算野怪(1-计算)", defaultValue = 1)]
        [ST_Param("是否计算召唤物(1-计算)", defaultValue = 1)]
        [ST_Param("掉落方式(预留)", defaultValue = 0)]
        BE_DO_TAHM_DROP = 84,

        [EnumName("添加额外参数（配合一些功能参数可能受限制）")]
        [ST_Param("类型", defaultValue = AdditionalParams.None)]
        [ST_Param("key", defaultValue = 0)]
        [ST_Param("param1", defaultValue = 0)]
        [ST_Param("param2", defaultValue = 0)]
        [ST_Param("param3", defaultValue = 0)]
        [ST_Param("param4", defaultValue = 0)]
        [ST_Param("param5", defaultValue = 0)]
        [ST_Param("param6", defaultValue = 0)]
        [ST_Param("param7", defaultValue = 0)]
        [ST_Param("param8", defaultValue = 0)]
        [ST_Param("param9", defaultValue = 0)]
        [ST_Param("param10", defaultValue = 0)]
        BE_ADD_ADDITIONAL_PARAMS = 85,

        [EnumName("添加对应羁绊内容")]
        [ST_Param("羁绊检测类型", defaultValue = SpecClassType.Spec)]
        [ST_Param("羁绊检测id", defaultValue = 0)]
        [ST_Param("是否剔除原有职业种族", defaultValue = true)]
        [ST_Param("按条件获得羁绊检测类型和id(自填的id会失效)", defaultValue = AddFetterConditionType.Customize)]
        [ST_Param("目标类型(羁绊来源)", defaultValue = BUFF_EFFECT_TARGET.NONE)]
        [ST_Param("添加的羁绊内容", defaultValue = AddFetterContentType.FetterBuff)]
        [ST_Param("Str参数1", defaultValue = "")]
        BE_ADD_FETTER_BUFF = 86,

        [EnumName("添加玩家指定效果")]
        [ST_Param("数值", defaultValue = 0)]
        [ST_Param("类型", defaultValue = BuffFuncForPlayer.AddPlayerHp)]
        [ST_Param("参数1", defaultValue = 0)]
        [ST_Param("参数2", defaultValue = 0)]
        [ST_Param("string参数1", defaultValue = "")]
        [ST_Param("string参数2", defaultValue = "")]
        BE_ADD_PLAYER_EFFECT = 87,

        [EnumName("S7吟游诗人掉落")]
        [ST_Param("掉落概率", defaultValue = 20)]
        [ST_Param("目标类型", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TRIGGER)]
        BE_DO_BARD_DROP = 88,

        [EnumName("批量处理属性")]
        [ST_Param("转换方式", defaultValue = AttrTransformType.AddToTarget)]
        [ST_Param("目标", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TARGET)]
        [ST_Param("目标属性类型", defaultValue = HeroAttributePartType.None)]
        [ST_Param("来源", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_CASTER)]
        [ST_Param("来源属性类型", defaultValue = HeroAttributePartType.None)]
        [ST_Param("转换百分比", defaultValue = 100)]
        [ST_Param("是否剔除（是就是剔除选中属性）", defaultValue = false)]
        [ST_Param("属性掩码1", defaultValue = 0)]
        [ST_Param("属性掩码2", defaultValue = 0)]
        [ST_Param("属性掩码3", defaultValue = 0)]
        [ST_Param("属性掩码4", defaultValue = 0)]
        [ST_Param("属性掩码5", defaultValue = 0)]
        BE_ATTR_BATCH_TRANSFORM = 89,

        [EnumName("通用掉落")]
        [ST_Param("掉落参数", defaultValue = "")]
        [ST_Param("目标", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TRIGGER)]
        [ST_Param("法球皮肤", defaultValue = -1)]
        [ST_Param("法球皮肤等级", defaultValue = 2)]
        [ST_Param("掉落类型（默认2为从英雄身上掉落）", defaultValue = 2)]
        [ST_Param("是否从敌人身上掉出来（0不是，1是）", defaultValue = 0)]
        [ST_Param("停止时间", defaultValue = 0)]
        [ST_Param("公式结果是否替换掉落参数", defaultValue = false)]
        [ST_Param("替换第几个参数(从1开始)", defaultValue = 0)]
        BE_DO_COMMON_DROP = 90,

        [EnumName("改变技能耗蓝方式")]
        [ST_Param("耗蓝方式(默认一次性清空)", defaultValue = SKILL_EXPEND_STYLE.CLEAR_ONCE)]
        [ST_Param("持续时间(毫秒，缓慢掉蓝时生效，期间锁蓝)", defaultValue = 0)]
        BE_CHANGE_SKILL_EXPEND_MP = 91,

        [EnumName("按key和目标数掉落")]
        [ST_Param("KEY", defaultValue = 0)]
        [ST_Param("目标数值", defaultValue = 0)]
        [ST_Param("目标", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_TRIGGER)]
        BE_DO_TARGET_DROP = 92,

        [EnumName("设置角色的ai行为像某个carrer一样")] [ST_Param("carrer", defaultValue = 0)]
        //设置 角色 的 ai行为 ，让他像某个 carrer 一样。比如像刺客
        BE_SET_AI_BEHAVIOR_AS_CARRERID = 93,

        [EnumName("复制角色表现（仅准备阶段生效）")]
        [ST_Param("复制的目标对象类型", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_SELF)]
        [ST_Param("自定义key值", defaultValue = 0)]
        BE_COPY_HERO_VIEW = 94,

        [EnumName("修改角色魔法条功能")]
        [ST_Param("魔法条表现参数(战场颜色-备战席颜色-一格MP量)", defaultValue = "")]
        [ST_Param("技能消耗方式(消耗类型-持续时间)", defaultValue = "")]
        [ST_Param("攻击回复参数(回复量-攻击次数)", defaultValue = "")]
        [ST_Param("是否受击回复", defaultValue = true)]
        [ST_Param("是否受改变属性buff回复", defaultValue = true)]
        [ST_Param("是否受装备基础属性改变", defaultValue = true)]
        BE_CHANGE_MAGICPOINT_TYPE = 95,

        [EnumName("修改全局数据记录")]
        [ST_Param("全局数据类型", defaultValue = ForeverRecordType.Fetter)]
        [ST_Param("唯一id(如羁绊id、海克斯id等)", defaultValue = 0)]
        [ST_Param("记录key(自定义)", defaultValue = 0)]
        [ST_Param("记录value", defaultValue = 0)]
        [ST_Param("max(填0则不生效)", defaultValue = 0)]
        [ST_Param("处理类型", defaultValue = VariableValueDealType.Add)]
        [ST_Param("min(大于等于0)", defaultValue = 0)]
        [ST_Param("触发配置id(对应ACGRecordBuffTrigger表，默认0不生效)", defaultValue = 0)]
        [ST_Param("是否把buff公式用于key值", defaultValue = false)]
        BE_CHANGE_FOREVER_RECORD = 96,

        [EnumName("丢出一个临时装备给英雄装上")]
        [ST_Param("目标", defaultValue = BUFF_EFFECT_TARGET.NONE)]
        [ST_Param("装备列表(id_id_id)", defaultValue = "")]
        [ST_Param("掉装备使用的公共技能", defaultValue = "")]
        BE_DROP_EQUIPMENT_TO_OTHER = 97,

        [EnumName("特殊记录战斗流程数据（指定特殊记录公式取）")]
        [ST_Param("战斗数据类型(对应参数1不同)", defaultValue = RecordBattleDataType.RealDamage)]   
        [ST_Param("记录目标(记录到谁身上,只能选1个)", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_CASTER)]
        [ST_Param("key", defaultValue = 0)] 
        [ST_Param("每次记录的处理类型", defaultValue = ValueDealType.Add)] 
        [ST_Param("参数1", defaultValue = 0)]
        [ST_Param("参数2", defaultValue = 0)]
        [ST_Param("(勾选后效果为清空该key的数据，而非原来的开关效果)", defaultValue = false)]
        BE_RECORD_BATTLE_DATA = 98,

        [EnumName("后添加海克斯效果")]
        BE_ADD_HERO_AUGMENT = 99,
        
        [EnumName("添加生命保护(触发时自动删除该buff，该buff不能有其他效果)")]
        [ST_Param("数值类型", defaultValue = PARA_TYPE.PARA_TYPE_PER100)]
        [ST_Param("数值", defaultValue = 0)]
        BE_HP_PROTECT = 100,
        
        [EnumName("模拟预计算技能伤害并记录")]
        [ST_Param("绑定的技能ID", defaultValue = 0)]
        [ST_Param("key(填2位数)", defaultValue = 0)]
        [ST_Param("数据记录方式", defaultValue = ValueDealType.Add)]
        [ST_Param("是否考虑暴击（是则要考虑暴击", defaultValue = true)]
        [ST_Param("是否忽略护盾计算", defaultValue = true)]
        [ST_Param("是否忽略伤害免疫计算", defaultValue = true)]
        [ST_Param("是否忽略斩杀计算", defaultValue = true)]
        [ST_Param("是否忽略技能护盾(女妖", defaultValue = true)]
        BE_PRE_SKILL_RECORD = 101,

        [EnumName("SpoilOfWar专用")]
        [ST_Param("海克斯等级", defaultValue = 0)]
        [ST_Param("计算公式里的x", defaultValue = 0)]
        [ST_Param("计算公式里的a", defaultValue = 0)]
        [ST_Param("计算公式里的b", defaultValue = 0)]
        [ST_Param("掉落池", defaultValue = "")]
        [ST_Param("hexid(用来做历史记录和次数记录)", defaultValue = 0)]
        [ST_Param("概率列", defaultValue = "")]
        BE_SPOIL_OF_WAR = 102,

        [EnumName("飓风掉落(瑞兹恕瑞玛)")]
        [ST_Param("金币掉落概率", defaultValue = 0)]
        [ST_Param("金币掉落数", defaultValue = 0)]
        [ST_Param("装备掉落概率(优先于金币判定)", defaultValue = 0)]
        [ST_Param("装备掉落列表", defaultValue = "")]
        BE_HURRICANE_DROP = 103,

        [EnumName("生成假人")]
        [ST_Param("假人id", defaultValue = 0)]
        [ST_Param("CombatEffectId", defaultValue = 0)]
        BE_ADD_FAKE_HERO = 104,

        [EnumName("加推荐装备")]
        [ST_Param("生产类型（5和10都是临时装备，回合结束会回收，但10有特殊边框）", defaultValue = 5)]
        [ST_Param("给装备使用公共技能", defaultValue = "")]
        [ST_Param("是否升级为光明装备", defaultValue = 0)]
        BE_ADD_RECOMMEND_EQUIPMENT = 105,

        [EnumName("设置英雄血量(一次性效果,不触发治疗流程和伤害流程)")]
        [ST_Param("血量值", defaultValue = -1)]
        [ST_Param("预留参数1", defaultValue = 0)]
        [ST_Param("预留参数2", defaultValue = 1)]
        BE_SET_BC_HP = 106,

        [EnumName("立即杀死该buff载体的友方/敌方所有人")]
        [ST_Param("预留参数1", defaultValue = 0)]
        [ST_Param("预留参数2", defaultValue = 0)]
        BE_KILL_CAMP_HERO = 107,

        [EnumName("设置当前buff数据禁用类型")]
        [ST_Param("是否屏蔽触发器(双人、复活会继承)", defaultValue = true)]
        [ST_Param("是否禁止被添加(双人、复活会继承)", defaultValue = false)]
        BE_BAN_TRIGGER = 108,
        
        [EnumName("记录变量数据到buffdata")]
        [ST_Param("key", defaultValue = 0)]
        [ST_Param("处理类型", defaultValue = VariableValueDealType.Add)]
        [ST_Param("值", defaultValue = 0)]
        BE_RECORD_BUFF_VALUE = 109,
        
        [EnumName("将自身的装备丢给指定规则的目标")]
        [ST_Param("目标类型", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_RANGE)]
        [ST_Param("特殊规则类型", defaultValue = DropEquipRule.None)]
        [ST_Param("buff载体使用公共技能", defaultValue = "")]
        BE_DROP_EQUIPMENT_TARGET = 110,
        
        [EnumName("天选效果")]
        [ST_Param("预留参数1", defaultValue = 0)]
        [ST_Param("预留参数2", defaultValue = 0)]
        BE_HEAVEN_CHOOSER = 111,

        [EnumName("重置动作状态")]
        [ST_Param("目标类型", defaultValue = BUFF_EFFECT_TARGET.EFFECT_TARGET_BUFF_RANGE)]
        [ST_Param("类型", defaultValue = ResetActionStateType.Enum_NULL)]
        [ST_Param("预留参数", defaultValue = 0)]
        BE_RESET_ACTION_STATE = 112,

        [EnumName("激活特殊羁绊效果（暂未使用）")]
        [ST_Param("羁绊Id", defaultValue = 0)]
        [ST_Param("特殊羁绊类型", defaultValue = SpecialFetterType.None)]
        [ST_Param("int参数1", defaultValue = 0)]
        [ST_Param("int参数2", defaultValue = 0)]
        [ST_Param("string参数", defaultValue = "")]
        BE_ACTIVE_SPECIAL_FETTER = 115,
        
        [EnumName("强制结束战斗，不杀死棋子的情况下判定己方失败")]
        [ST_Param("预留参数1", defaultValue = 0)]
        [ST_Param("预留参数2", defaultValue = 0)]
        BE_SPECIAL_END_BATTLE = 118,

        [EnumName("当前英雄的属性转换效开关果")]
        [ST_Param("是否开启", defaultValue = false)]
        [ST_Param("参数2", defaultValue = 0)]
        [ST_Param("参数3", defaultValue = 0)]
        [ST_Param("参数4", defaultValue = 0)]
        [ST_Param("参数5", defaultValue = 0)]
        [ST_Param("参数6", defaultValue = 0)]
        [ST_Param("参数7", defaultValue = 0)]
        [ST_Param("参数8", defaultValue = 0)]
        BE_RESERVE_EFFECT_1 = 130,

        [EnumName("预留效果1")]
        [ST_Param("参数1", defaultValue = 0)]
        [ST_Param("参数2", defaultValue = 0)]
        [ST_Param("参数3", defaultValue = 0)]
        [ST_Param("参数4", defaultValue = 0)]
        [ST_Param("参数5", defaultValue = 0)]
        [ST_Param("参数6", defaultValue = 0)]
        [ST_Param("参数7", defaultValue = 0)]
        [ST_Param("参数8", defaultValue = 0)]
        BE_RESERVE_EFFECT_2 = 131,

        [EnumName("预留效果2")]
        [ST_Param("参数1", defaultValue = 0)]
        [ST_Param("参数2", defaultValue = 0)]
        [ST_Param("参数3", defaultValue = 0)]
        [ST_Param("参数4", defaultValue = 0)]
        [ST_Param("参数5", defaultValue = 0)]
        [ST_Param("参数6", defaultValue = 0)]
        [ST_Param("参数7", defaultValue = 0)]
        [ST_Param("参数8", defaultValue = 0)]
        BE_RESERVE_EFFECT_4 = 132,

        [EnumName("预留效果3")]
        [ST_Param("参数1", defaultValue = 0)]
        [ST_Param("参数2", defaultValue = 0)]
        [ST_Param("参数3", defaultValue = 0)]
        [ST_Param("参数4", defaultValue = 0)]
        [ST_Param("参数5", defaultValue = 0)]
        [ST_Param("参数6", defaultValue = 0)]
        [ST_Param("参数7", defaultValue = 0)]
        [ST_Param("参数8", defaultValue = 0)]
        BE_RESERVE_EFFECT_5 = 133,
        [EnumName("最大类型数量")] BE_EFFECT_MAX_NUM, // 最大类型数量
    }

    public enum BUFF_STATUS
    {
        [EnumName("不可移动")] BS_NO_MOVE = 1,
        [EnumName("不可转向")] BS_NO_TURN = 2,
        [EnumName("不可释放技能")] BS_NO_SKILL = 3,
        [EnumName("不可浮空")] BS_NO_FLOAT = 4,
        [EnumName("不可抓取")] BS_NO_CATCH = 5,
        [EnumName("不可硬直")] BS_NO_STIFF = 6,
        [EnumName("不可击退")] BS_NO_REPEL = 7,
        [EnumName("不受魔法伤害(buff)")] BS_NO_MAGIC_HURT = 8,
        [EnumName("不受物理伤害(buff)")] BS_NO_PHY_HURT = 9,
        [EnumName("不可加buff")] BS_NO_BUFF = 10,
        [EnumName("敌方不可选中")] BS_NO_CHOOSE = 11,
        [EnumName("不可AI控制")] BS_NO_CONTROL = 13,
        [EnumName(" 不可被攻击")] BS_UNBEATABLE = 14,
        [EnumName(" 不可普攻")] BS_NO_ATK = 15,
        [EnumName(" 不可播放动画")] BS_NO_ANIMATION = 16,
        [EnumName(" 不可攻击受击回复蓝")] BS_NO_ATK_BEAR_REPLY_MP = 17,
        [EnumName("全真实伤害（除装备）-- 废弃，使用复杂属性实现")] BS_ALL_PURE_HURT = 18,
        [EnumName("不受真实伤害")] BS_NO_PURE_HURT = 19,
        [EnumName("禁止使用装备")] BS_NO_USE_EQUIPMENT = 20,
        [EnumName(" 不可buff回复蓝")] BS_NO_BUFF_REPLY_MP = 21,
        [EnumName(" 屏蔽普通技能")] BS_NO_BEAR_COMMON_SKILL = 22,
        [EnumName(" 普通技能可以暴击")] BS_COMMON_SKILL_CAN_CRIT = 23,
        [EnumName("不受暴击伤害(废弃，使用暴击减免属性代替！！)")] BS_NO_CRIT_HURT = 24,
        [EnumName(" 不可致死回复蓝")] BS_NO_DEAD_REPLY_MP = 25,
        [EnumName(" 不可变身")] BS_NO_ROLE_TRANSFORM = 26,
        [EnumName(" 己方不可选中")] BS_FRIEND_NO_CHOOSE = 27,
        [EnumName(" 技能不可选中")] BS_SKILL_NO_CHOOSE = 28,
        [EnumName(" 次选目标状态")] BS_SECONDARY_SELECTION_TARGET = 29,
        [EnumName("护盾、治疗能暴击")] BS_HEAL_AND_SHIELD_CAN_CRIT = 30,
        [EnumName("MAX")] BS_MAX,
    }

    public enum BUFF_STATUS_2
    {
        [EnumName("不可友军攻击框命中")] BS_FRIEND_NO_HIT = 1,
        [EnumName("不可敌军攻击框命中")] BS_ENEMY_NO_HIT = 2,
        [EnumName("不可被斩杀")] BS_NO_SECKILL = 3,
        BS_MAX,
    }

    public enum BUFF_RELATION
    {
        [EnumName(" 共存")] BR_COEXIST = 1, // 共存
        [EnumName(" 覆盖自己")] BR_COVER_ME = 2, // 覆盖自己
        [EnumName(" 覆盖全部")] BR_COVER_ALL = 3, // 覆盖全部
        [EnumName(" 叠加自己")] BR_SUPER_ME = 4, // 叠加自己
        [EnumName(" 叠加全部")] BR_SUPER_ALL = 5, // 叠加全部
        [EnumName(" 同源覆盖共存")] BR_COEXIST_SAME_COVER = 6, // 同源覆盖共存
    }

    public enum BUFF_SUPERPOSITION_MODEL
    {
        [EnumName(" 重置效果")] RESET = 1, //重置效果
        [EnumName(" 再次添加效果")] ADD_AGAIN = 2, //再次添加效果
    }

    public enum BUFF_COVER_MODEL
    {
        [EnumName("无")] None = 0, //无覆盖条件
        [EnumName("施法者法强高的覆盖重置")] AP_Higher = 1,
        [EnumName("施法者星级高的覆盖重置")] Star_Higher = 2
    }

    /// <summary>
    /// 英雄全局记录数据在升星时的处理方式（S7奥拉夫要叠加）
    /// </summary>
    public enum HERO_FOREVER_RECORD_STARUP_TYPE
    {
        [EnumName("无")] NONE = 0,
        [EnumName("叠加")] ADD = 1,
        [EnumName("最大值")] MAX = 2,
        ENUM_MAX // 最大的枚举值
    }

    public enum EFFECT_POS
    {
        EFFECT_POS_UP = 1, // 上部
        EFFECT_POS_CENTER = 2, // 中部
        EFFECT_POS_DOWN = 3, // 下部
        EFFECT_POS_4 = 4, // 槽4
        EFFECT_POS_5 = 5, // 槽5
        EFFECT_POS_6 = 6, // 槽6
        EFFECT_POS_7 = 7, // 槽7
        EFFECT_POS_8 = 8, // 槽8
        EFFECT_POS_9 = 9, // 槽9
        EFFECT_POS_10 = 10, // 槽10
    }

    public enum SKILL_DAMAGE_TYPE
    {
        SKILL_DAMAGE_PHYSIC = 1, // 物理伤害
        SKILL_DAMAGE_MAGIC = 2, // 法术伤害
        SKILL_DAMAGE_MYSTERIOUS = 3, // 魔能伤害
    }

    public enum SKILL_TARGET_TYPE
    {
        SKILL_NONE,
        SKILL_ATTACK = 1, // 技能释放者
        SKILL_BEAR = 2, // 技能受击者
    }

    public enum SKILL_PACKAGE_INFLUENCE_TYPE
    {
        SKILL_PROTO_ID = 1, // 技能原型Id
        SKILL_TAG = 2, // 技能Tag
    }

    public enum SKILL_PACKAGE_EFFECT_TYPE
    {
        SKILL_ATTRIBUTE = 1, // 改变技能属性,参数1-SKILL_ATTR_TYPE，参数2-数值，参数3-数值类型
    }

    public enum SKILL_ATTR_TYPE
    {
        SKILL_ATTR_LEVEL = 1, // 技能等级
        SKILL_ATTR_USE_COUNT = 2, // 充能次数
        SKILL_ATTR_CD = 3, // 冷却时间
        SKILL_ATTR_RATIO = 4, // 技能总伤害倍率
        SKILL_ATTR_DAMAGE = 5, // 技能附加伤害
        SKILL_ATTR_CRIT = 6, // 技能额外暴击率
        SKILL_ATTR_CRIT_DAMAGE = 7, // 技能额外暴击伤害
    }

    public enum SKILL_HIT_SIGN
    {
        SKILL_HIT_SIGN_HIT = 1, // 命中
        SKILL_HIT_SIGN_DODGE = 2, // 闪避
        SKILL_HIT_SIGN_CRITICAL = 3, // 暴击
        SKILL_HIT_SIGN_LJ = 4, // 连击
        SKILL_HIT_SIGN_GD = 5, // 格挡
        SKILL_HIT_SIGN_PJ = 6, // 破甲
        SKILL_HIT_SIGN_ZM = 7, // 致命
    }

    public enum SKILL_COND_FUNC
    {
        SKILL_COND_FUNC_SKILLRELA = 1, // xx技能触发
        SKILL_COND_FUNC_DIRECT = 2, // 目标朝向
        SKILL_COND_FUNC_ATTR_LESS = 3, // 属性小于
        SKILL_COND_FUNC_ATTR_MORE = 4, // 属性大于
        SKILL_COND_FUNC_DISTANCE_LESS = 5, // 距离小于
        SKILL_COND_FUNC_DISTANCE_MORE = 6, // 距离大于
        SKILL_COND_FUNC_HAS_STATUS = 7, // 拥有某状态
        SKILL_COND_FUNC_WARRES_LESS = 8, // 资源小于
        SKILL_COND_FUNC_WARRES_MORE = 9, // 资源大于
        SKILL_COND_FUNC_CAREER_EFFECT = 10, // 针对职业生效
        SKILL_COND_FUNC_SEX_EFFECT = 11, // 针对性别生效
        SKILL_COND_FUNC_HIT_SIGN = 12, // 目标击中标记判断
        SKILL_COND_FUNC_HAS_BUFF = 13, // 拥有buff
    }

    public enum SKILL_RELEASE_CONDITION
    {
        SKILL_NEED_BUFF_TAG = 1, // 所需buffTag，参数1bufftag
        SKILL_NEED_BUFF_ID = 2, // 所需buff原型id，参数1buff原型id
        SKILL_NEED_ACTION_STATUS = 3, // 所需动作状态，参数1动作状态位ACTION_STATUS_BIT
        SKILL_NEED_MORE_ATTRIBUTE = 4, // 大于属性，参数1属性，参数2属性千分比
        SKILL_NEED_LESS_ATTRIBUTE = 5, // 小于属性，参数1属性，参数2属性千分比
        SKILL_NEED_NO_BUFF_STATUS = 6, // 非禁止状态，参数状态
    }

    public enum SKILL_EXPEND_TYPE
    {
        ATTRIBUTE_PERCENT = 1, // 消耗千分比属性,参数1属性，参数2属性千分比
        ITEM = 2, // 消耗物品,参数1物品id，参数2数量
        ENERGY = 3, // 消耗能量,参数1能量id，参数2数量
    }

    public enum SKILL_EXPEND_STYLE
    {
        [EnumName("一次性扣除")] CLEAR_ONCE = 1, // 默认一次性扣除
        [EnumName("缓慢耗蓝(期间锁蓝)")] SLOW_REDUCE_MP = 2 // 缓慢减少(蓝)条，配合几秒内清空的参数使用
    }

    public enum SKILL_COND_CHANGE_TYPE
    {
        MY_ACTION_STATUS = 1, // 动作状态修改技能id,参数1动作状态
        ATTR_LESS = 2, // 属性小于,属性type | 数值 | 数值类型
        TARGET_IS_ALONE = 4, // 目标处于孤立无援(注意目标持续性)
    }

    public enum ACTION_STATUS_BIT
    {
        [EnumName("无")] None = 0,
        [EnumName("僵直")] NUMB = 1, // 僵直
        [EnumName("倒地")] LIE = 2, // 倒地
        [EnumName("低浮空")] LOWFLY = 3, // 低浮空
        [EnumName("起身")] GETUPING = 4, // 起身
        [EnumName("浮空")] INAIR = 5, // 浮空
        [EnumName("死亡")] DEAD = 6, // 死亡
    }

    public enum BATTLE_RES_TYPE
    {
        BATTLE_RES_TYPE_HP = 1, // 血
        BATTLE_RES_TYPE_HD = 2, // 护盾
        BATTLE_RES_TYPE_NQ = 3, // 怒气
        BATTLE_RES_TYPE_LJ = 4, // 连击
        BATTLE_RES_TYPE_MAX = 20, // max
    }

    public enum EQUIP_DRESS_SLOT
    {
        EQUIP_DRESS_SLOT1 = 1, // 帽子
        EQUIP_DRESS_SLOT2 = 2, // 上衣
        EQUIP_DRESS_SLOT3 = 3, // 腰部
        EQUIP_DRESS_SLOT4 = 4, // 裤子
        EQUIP_DRESS_SLOT5 = 5, // 鞋子
        EQUIP_DRESS_SLOT6 = 6, // 徽章
        EQUIP_DRESS_SLOT7 = 7, // 吊坠
        EQUIP_DRESS_SLOT8 = 8, // 武器
        EQUIP_DRESS_SLOT9 = 9, // 护臂
        EQUIP_DRESS_SLOT10 = 10, // 手套
        EQUIP_DRESS_SLOT11 = 11, // 戒指
        EQUIP_DRESS_SLOT12 = 12, // 副武器
    }

    public enum EQUIP_OP_CATEGORY
    {
        EQUIP_DISTILL = 1, // 淬炼
        EQUIP_COLLECT = 2, // 收集
        EQUIP_BAOSHI = 3, // 宝石
        EQUIP_STRENGTHEN = 4, // 强化
        EQUIP_FUMO = 5, // 附魔
    }

    public enum TASK_GOAL_TYPE
    {
        TASK_GOAL_ROLE_LEVEL = 1, // role等级任务，参数1：role等级
        TASK_GOAL_HERO_LEVEL = 2, // hero等级任务，参数1：hero等级
        TASK_GOAL_ROLE_NPC_DIG = 3, // role对话npc任务，参数1：1 默认值，前台决定完成与否，后台记录value为1，与默认值比较表示完成
        TASK_GOAL_HERO_NPC_DIG = 4, // hero对话npc任务，参数1：1 默认值，前台决定完成与否，后台记录value为1，与默认值比较表示完成
        TASK_GOAL_PARENT_MULTI = 5, // 多目标并列父子任务，父任务目标，参数1：子任务完成数量
        TASK_GOAL_PARENT_INORDER = 6, // 多目标序列父子任务，父任务目标，参数1：子任务数量
        TASK_GOAL_PARENT_RANDOM = 7, // 多目标随机父子任务，父任务目标，参数1：子任务完成数量
        TASK_GOAL_KILL_MONSTER_CNT = 8, // 杀怪数量任务，参数1：杀怪数量，参数2：副本id
        TASK_GOAL_HERO_ITEM_COUNT = 9, // 道具采集任务，参数1：道具数量，参数2：道具id，特指任务道具
        TASK_GOAL_SUB_ACHVMNT_FINISHED = 10, // 子成就达成任务，参数1：成就id
        TASK_GOAL_HERO_NORMAL_ITEM_COUNT = 11, // 普通道具采集任务，参数1：道具数量，参数2：道具id，普通道具
        TASK_GOAL_STORY_PVE_FINISHED = 12, // 剧情副本通关，参数1：1 默认值 参数2：pveid，前台决定完成与否，后台记录value为1，与默认值比较表示完成
    }

    public enum TASK_TYPE
    {
        TASK_MAINLINE_TYPE = 1, // 主线任务
        TASK_EXTENSION_TYPE = 2, // 支线任务
        TASK_DAILY_TYPE = 3, // 日常任务
    }

    public enum TASK_CYCLE_PERIOD
    {
        TASK_CYCLE_DAY = 1, // 日循环
        TASK_CYCLE_WEEK = 2, // 周循环
        TASK_CYCLE_MONTH = 3, // 月循环
    }

    public enum TASK_STATUS
    {
        TASK_CANACCEPT = 0, // 可接取
        TASK_ACCEPTED = 1, // 已接取
        TASK_HAS_FINISH = 2, // 任务结束
        TASK_GOT_REWARD = 3, // 奖励可领取
        TASK_TIMEOUT = 4, // 任务超时
        TASK_GIVEUP = 5, // 任务放弃
        TASK_CANSUBMIT = 6, // 可提交
    }

    public enum TASK_CHIRDSEQUENCE_TYPE
    {
        CHIRDSEQUENCE_TOGETHER = 1, // 并列处理
        CHIRDSEQUENCE_ORDER = 2, // 顺序处理
        CHIRDSEQUENCE_RANDOM = 3, // 随机
    }

    public enum TASK_TRIGGERTYPE
    {
        TASK_TRIGGERTYPE_TASK = 0, // 任务链触发
        TASK_TRIGGERTYPE_NPC = 1, // npc触发
        TASK_TRIGGERTYPE_REGION = 2, // 区域触发
        TASK_TRIGGERTYPE_ITEM = 3, // 道具触发
        TASK_TRIGGERTYPE_REGIONITEM = 4, // 区域道具触发
        TASK_TRIGGERTYPE_LOGICITEM = 5, // 逻辑物触发
        TASK_TRIGGERTYPE_NOTIFYRECEIVE = 6, // 界面接取
    }

    public enum TASK_TARGETTYPE
    {
        TASK_TARGETTYPE_EASY = 0, // 无目标类型直接提交
        TASK_TARGETTYPE_NPC = 1, // 对话型
        TASK_DIALOG = 4, // 对话提交
        TASK_GOAL_KILL_MONSTER = 8, // 杀怪型
        TASK_GOAL_ITEM_COUNT = 9, // 采集型
    }

    public enum TASK_SUBMITTYPE
    {
        TASK_SUBMITTYPE_NPC = 1, // npc提交
    }

    public enum MONSTER_GROUP_TYPE
    {
        MONSTER_GROUP_GOBLIN = 1, // 哥布林
        MONSTER_GROUP_BEAST = 2, // 野兽
        MONSTER_GROUP_HUMAN = 3, // 人型
    }

    public enum MONSTER_TYPE
    {
        MONSTER_NORMAL = 1, // 普通怪
        MONSTER_ELITE = 2, // 精英怪
        MONSTER_BOSS = 3, // BOSS怪
        MONSTER_NPC = 4, // NPC
        MONSTER_DEVICE = 5, // 机关
        MONSTER_BROKEN = 6, // 击碎物
        MONSTER_COLLECT = 7, // 采集物
        MONSTER_GOLDDROP = 8, // 掉钱怪
    }

    public enum CARD_PROTYPE
    {
        CARD_PROTYPE_DASH = 1,
        CARD_PROTYPE_SHIELD = 2,
        CARD_PROTYPE = 3,
    }

    public enum MAPBOX_TYPE
    {
        MAPBOX_SCENE = 1,
        MAPBOX_LOGIN = 2,
        MAPBOX_PVEPVP = 30,
        MAPBOX_BIGMAP = 40,
        MAPBOX_PHONE_CALL = 103,
        MAPBOX_PHONE_MESS = 104,
        MAPBOX_STORY = 105,
    }

    public enum CONSTANT_TYPE
    {
        ENERGY_REPLY_INTERVAL = 1, // 能量回复间隔
        CRITICAL_BASE_VALUE = 2, // 暴击伤害基础值
        CRITICAL_RATE_MAX = 3, // 暴击率上限
        HURT_DEC_RATE_MAX = 4, // 伤害减免率上限
        BLOCK_BASE_VALUE = 5, // 格挡伤害基础值
        HURT_RANDOM_MAX = 6, // 伤害浮动上限
        HURT_RANDOM_MIN = 7, // 伤害浮动下限
        PLAYER_WEIGHT = 8, // 玩家体重
        MONSTER_DOWN_TIME = 9, // 怪物通用倒地时间
        NUMB_RATE_MAX = 10, // 僵直增强上限
        NUMB_RATE_MIN = 11, // 僵直抵抗下限
        FIGHT_PROTECT_TIME = 12, // 战斗保护持续时间
        FIGHT_PROTECT_NUMB_TIME = 13, // 战斗保护最大连续受控时间
        FIGHT_PROTECT_HP = 14, // 战斗保护千分比血量
        FIGHT_PROTECT_BACKFORCE = 15, // 战斗保护击退力
        FIGHT_PROTECT_AIR_HP = 16, // 战斗浮空保护千分比血量
        FIGHT_PROTECT_HIT_MIN_TIM = 17, // 战斗保护最小间隔
        PVP_DOUBLE_HIT_INTERVAL = 18, // PVP连击间隔
        MULTI_SKILL_CUT_TIME = 19, // 一键连招中断时间
        FIGHT_LIETOAIR_RATIO = 20, // 通用倒地浮空系数
        FIGHT_LOWFLYTOAIR_RATIO = 21, // 通用低浮空系数
        FIGHT_GETUP_BUFF = 22, // 通用起身无敌
        FIGHT_PROTECT_LIE_HP = 23, // 战斗倒地保护千分比血量
    }

    public enum SKILL_TYPE
    {
        NORMAL_ATTACK = 1, // 普通攻击
        PROFESSION_SKILL = 2, // 职业技能
        SP_SKILL = 3, // sp技能
        LP_SKILL = 4, // 体力技能
        SPECIAL_SKILL = 5, // 特殊技能
        BUFF_SKILL = 6, // 主动buff技能
        PASSIVE_SKILL = 7, // 被动技能
    }

    public enum MYSTERIOUS_TYPE
    {
        AIR = 1, // 魔能空
        EARTH = 2, // 魔能地
        ARCANE = 3, // 魔能奥
        DOOM = 4, // 魔能灭
    }

    public enum CARD_SKILL_TARGET_TEAM
    {
        SKILL_TARGET_TEAM_ENEMY = 1,
        SKILL_TARGET_TEAM_FRIEND = 2,
    }

    public enum CARD_SKILL_EFFECT
    {
        CSE_HURT = 1,
        CSE_CURE = 2,
        CSE_BUFF = 3,
        CSE_REVIVE = 4,
        CSE_MOD_ATTR = 5,
        CSE_MOD_STATUS = 6,
        CSE_PROTECT = 7,
        CSE_PROTECTED = 8,
        CSE_MULTI_HIT = 9,
        CSE_MOD_ATTR_BY_ALL = 10,
        CSE_MAD_SWORD = 11,
        CSE_IGNORE_ATTR = 12,
    }

    public enum CARD_BUFF_STATUS
    {
        CBS_STUN = 1, // 眩晕
        CBS_SILENCE = 2, // 沉默
        CBS_PROTECTED = 3, // 被守护
        CBS_BLIND = 4, // 致盲
    }

    public enum CARD_SKILL_CONDITION
    {
        CSC_RATE = 1,
        CSC_KILL = 2,
        CSC_ALIVE = 3,
    }

    public enum CARD_ATTRIBUTE_DEF
    {
        CA_ATK = 1,
        CA_DEF = 2,
        CA_MAX_HP = 3,
        CA_HP = 4,
        CA_CRITICAL = 5,
        CA_BLOCK = 6,
        CA_CRITICAL_VALUE = 7,
        CA_BLOCK_VALUE = 8,
        CA_HURT_ADD = 9,
        CA_HURT_REDUCE = 10,
        CA_ANTI_CRIT = 11,
        CA_BREAK = 12,
        CA_ACCURATE = 13,
        CA_DODGE = 14,
    }

    public enum CARD_SKILL_ACT_TIME
    {
        CS_ACT_BATTLE_START = 1,
        CS_ACT_ROUND_START = 2,
        CS_ACT_ROUND_END = 3,
        CS_ACT_ACTIVE_BUMP = 4,
        CS_ACT_PASSIVE_BUMP = 5,
        CS_ACT_EVENT = 6,
    }

    public enum CARD_BUFF_ACT_TIME
    {
        CBA_ROUND_START = 1,
        CBA_ACTION = 2,
    }

    public enum CARD_TECHNIC_TYPE
    {
        CTT_INITIATIVE = 1,
        CTT_PASSIVE = 2,
        CTT_COMMON = 3,
    }

    public enum ENTITY_STATUS_POS
    {
        ENTITY_STATUS_POS_DEFAULT = 0,
        ENTITY_STATUS_POS_FLY = 1,
        ENTITY_STATUS_POS_LIEDOWN = 2,
    }

    public enum ENTITY_STATUS_MOVE
    {
        ENTITY_STATUS_MOVE_DEFAULT = 0,
        ENTITY_STATUS_MOVE_STAND = 1,
        ENTITY_STATUS_MOVE_RUN = 2,
        ENTITY_STATUS_MOVE_WALK = 3,
        ENTITY_STATUS_MOVE_SKILL = 4,
        ENTITY_STATUS_MOVE_JUMP = 5,
    }

    public enum CAMP_RELATION_TYPE
    {
        CAMP_NONE = 0, // 未定
        CAMP_FRIENDLY = 1, // 友善
        CAMP_ENEMY = 2, // 敌对
        CAMP_NEUTRAL = 3, // 中立
    }

    public enum MAP2D_POINT_TYPE
    {
        MAP2D_POINT_TYPE_NONE = 0, // 无
        MAP2D_POINT_TYPE_CITY = 1, // 城镇入口
        MAP2D_POINT_TYPE_JUDIAN = 2, // 据点入口
        MAP2D_POINT_TYPE_FUBEN = 3, // 副本入口
        MAP2D_POINT_TYPE_EVENT = 4, // 事件
        MAP2D_POINT_TYPE_ROUTE = 5, // 路线
    }

    public enum LOADING_TEX_TYPE
    {
        LTT_IN_BATTLE = 0, // 副本内
        LTT_FADE = 1, // 消融
        LTT_PROGRESS = 2, // 进度条
        LTT_PVP = 3, // pvp
    }

    public enum SPECIAL_FUNCTION_TYPE
    {
        SPECIAL_FUNCTION_ADD_BREAK_SKILLGROUP = 1, // 添加打断技能组
    }

    public enum BUFF_ICON_SHOW_TYPE
    {
        HIDE = 0, // 隐藏buff图标
        SHOW_BUFF_BARR = 1, // buff栏显示
        SHOW_EFFECT_BAR = 2, // 效果栏显示
    }

    public enum BUFF_EQUIP_POS
    {
        ADD = 1, // 直接添加身上
        ADD_ATK = 2, // 添加普攻上，攻击前判定
        ADD_ATK_AFTER = 3, // 添加普攻上，攻击后判定（miss不起效）
    }

    public enum SKILL_KEY_SET
    {
        COMBO_SKILL_SLOT01 = 3, // 连招技能1
        COMBO_SKILL_SLOT02 = 4, // 连招技能2
        COMBO_SKILL_SLOT03 = 5, // 连招技能3
        SKILL_KEY_NUM = 12, // 技能快捷键数
    }

    public enum FASHION_TYPE
    {
        FASHION_NONE = 0, // 无部位
        FASHION_HEAD = 1, // 头部
        FASHION_SHOULDER = 2, // 肩膀
        FASHION_COAT = 3, // 上衣
        FASHION_PANTS = 4, // 下衣
        FASHION_SHOES = 5, // 鞋子
    }

    public enum ITEM_TYPE
    {
        ITEM_TYPE_NORMAL = 0, // 普通道具
        ITEM_TYPE_EQUIP = 1, // 装备道具
        ITEM_TYPE_CURRENCY = 2, // 货币
        ITEM_TYPE_FASHION = 3, // 时装道具
    }

    public enum SHOW_TYPE
    {
        SHOW_TYPE_HIDE = 0, // 不显示
        SHOW_TYPE_SHOW = 1, // 显示
    }

    public enum POS_TYPE
    {
        POS_TYPE_BASE = 1, // 基础属性
        POS_TYPE_OTHER = 2, // 其他属性
    }

    public enum INDICATOR_TYPE
    {
        INDICATOR_TYPE_NONE = 0, // 无
        INDICATOR_TYPE_CIRCLE = 1, // 圆形指示器
        INDICATOR_TYPE_ARROW = 2, // 箭头指示器
        INDICATOR_TYPE_SECTOR = 3, // 扇形指示器
    }

    public enum NPC_FUNCTION
    {
        FUNCTION_NONE = 0, // 无功能
        FUNCTION_PRODUCTION = 1, // 生产
        FUNCTION_SHOPPING = 2, // 商店
        FUNCTION_STRENGTHEN = 3, // 强化
        FUNCTION_DECOMPOSE = 4, // 分解
        FUNCTION_STORE = 5, // 仓库
    }

    public enum SINGLE_ID
    {
        SI_STAR_SHOP_LEVEL = 1, // 明星赠礼直购开放等级
        SI_STAR_DAILY_FAVOR = 2, // 明星赠礼每日好感度上限
        SI_STALL_SELL_RATE = 3, // 摆摊摊位费比率
        SI_STALL_SELL_LIMIT = 4, // 摆摊摊位费最低
        SI_STALL_SELL_MAX = 5, // 摆摊摊位费最高
        SI_STALL_SELL_BUY_RATE = 6, // 摆摊税率
        SI_VFIGHT_REPORT_DURATION_SOLO = 7, // 播报_单挑期_延迟时间(毫秒)
        SI_VFIGHT_REPORT_DURATION_TEAM = 8, // 播报_团战期_延迟时间(毫秒)
        SI_VFIGHT_RESULTUI_DELAY = 9, // 结算界面打开延迟时间(毫秒)
        SI_STALL_PRICE_LOW_RATE100 = 10, // 摆摊售卖价格低点(百分比)
        SI_STALL_PRICE_HIGH_RATE100 = 11, // 摆摊售卖价格低点(百分比)
        SI_STALL_PRICE_CHANGE_RATE100_PER_ONCE = 12, // 摆摊售卖价格每次修改值(百分比)
        ST_STALL_SHOW_OTHER_SAME_ITEMCOUNT = 13, // 其他玩家出售情况最多显示个数
    }

    public enum STAR_GIFT_TRIGGER_TYPE
    {
        SGT_SEND_COUNT = 1,
    }

    public enum STAR_GIFT_EFFECT_TYPE
    {
        SGE_TASK = 1,
    }

    public enum STALL_STORE_TYPE
    {
        STALL_STORE_TYPE_EQUIP_1 = 1,
        STALL_STORE_TYPE_EQUIP_2 = 2,
        STALL_STORE_TYPE_EQUIP_3 = 3,
        STALL_STORE_TYPE_EQUIP_4 = 4,
        STALL_STORE_TYPE_EQUIP_5 = 5,
        STALL_STORE_TYPE_EQUIP_6 = 6,
        STALL_STORE_TYPE_EQUIP_7 = 7,
        STALL_STORE_TYPE_EQUIP_8 = 8,
        STALL_STORE_TYPE_EQUIP_9 = 9,
        STALL_STORE_TYPE_EQUIP_10 = 10,
        STALL_STORE_TYPE_EQUIP_11 = 11,
        STALL_STORE_TYPE_EQUIP_12 = 12,
        STALL_STORE_TYPE_ITEM_1 = 31,
        STALL_STORE_TYPE_ITEM_2 = 32,
        STALL_STORE_TYPE_ITEM_3 = 33,
        STALL_STORE_TYPE_ITEM_4 = 34,
        STALL_STORE_TYPE_ITEM_5 = 35,
        STALL_STORE_TYPE_MAX_NUM = 99,
        STSALL_STORE_TYPE_EQUIP_MIN = 1001,
        STSALL_STORE_TYPE_EQUIP_MAX = 1012,
        STSALL_STORE_TYPE_ITEM_MIN = 1031,
        STSALL_STORE_TYPE_ITEM_MAX = 1035,
    }

    public enum STALL_STORE_CATEGORY
    {
        STALL_STORE_CATEGORY_EQUIP = 1,
        STALL_STORE_CATEGORY_ITEM = 2,
    }

    public enum STALL_TABLE_TYPE
    {
        STALL_TABLE_TYPE_STALL = 1,
        STALL_TABLE_TYPE_AUCTION = 2,
    }

    public enum AUCTION_TYPE
    {
        AUCTION_TYPE_UNION = 1, // 工会拍卖
        AUCTION_TYPE_GLOBAL = 2, // 全服拍卖
    }

    public enum AUCTION_PAGE
    {
        AUCTION_PAGE_INTEREST = -1, // 我的关注页签
        AUCTION_PAGE_ALL = 0, // 所有类型页签
        AUCTION_PAGE_BASE = 1000, // 拍卖页签基数
    }

    public enum TRADE_RECORD_TYPE
    {
        TRADE_RECORD_TYPE_STALL = 1, // 摆摊记录
        TRADE_RECORD_TYPE_AUCTION = 2, // 拍卖记录
    }

    public enum AUCTION_DEAL_RESULT
    {
        AUCTION_DEAL_RESULT_SUCCESS = 0, // 交易成功
        AUCTION_DEAL_RESULT_FAIL = 1, // 流拍
    }

    public enum AUCTION_RECORD_TYPE
    {
        AUCTION_RECORD_TYPE_PERSON = 1, // 个人
        AUCTION_RECORD_TYPE_UNION = 2, // 工会
        AUCTION_RECORD_TYPE_GLOBAL = 3, // 全服
    }

    public enum UNION_POS
    {
        COMMANDER = 1,
        VICECOMMANDER = 2,
        ELITE = 3,
        NORMAL_MEMBER = 4,
        UNION_POS_MAX = 5,
    }

    public enum UNION_LOG_TYPE
    {
        UNION_LOG_CREATE = 1, // 创建
        UNION_LOG_JOIN = 2, // 加入
        UNION_LOG_QUIT = 3, // 退出
        UNION_LOG_FIRE = 4, // 驱逐
        UNION_LOG_UPGRADE = 5, // 升级
    }

    public enum STAR_GIFT_TRIGGER_EFF
    {
        SGTE_TEXT = 1,
    }

    /// <summary>
    /// 羁绊激活的前提条件类型
    /// </summary>
    public enum FETTERS_CHECK_COND_TYPE
    {
        NONE = 0,
        NOT_EXIST_SPEC_ID = 1, // 不存在某种族
        NOT_EXIST_CLASS_ID = 2, // 不存在某职业
    }

    public enum FETTERS_TARGET_TYPE
    {
        NONE = 0,
        ALL_FILTER_PRO = 1, //全部当前职业 
        ALL_FILTER_PRO_RANDOM = 2, //当前职业随机 参数-随机数量
        ALL_RANDOM = 3, //全部随机 参数-随机数量
        ALL = 4, //全部
        FILTER_PRO_SORT_BY_COST_AND_EQUIPMENT = 5, //同职业选择费用最高（同费装备最多者）
        FILTER_PRO_CENTER_FOR_ALL_HERO = 6, //同职业中心位(针对所有人)
    }

    /// <summary>
    /// 特殊羁绊类型
    /// </summary>
    public enum FETTERS_MORE_FUNC_TYPE
    {
        NONE = 0,
        ROUND_WIN_DROP_BOX = 1, //回合结束胜利掉落宝箱（野怪关不掉）
        INHERIT_BATTLE_FETTERS = 4, //继承战场上已有羁绊
        ROUND_DROP_BOX = 5, //回合结束掉落宝箱（野怪关掉）
        ROUND_END_RECORD_ACTIVE_FETTERS = 7, // 记录上一场激活羁绊（羁绊失效会清除记录）
        
        MERGE_HERO = 2, //合体
        SUMMON_ON_READY = 3, //准备阶段召唤   召唤配置id|FetterSummonAdditionType
        START_GAME_RANDOM_FETTER = 6, //游戏开始随机一个羁绊效果(fetterProtoId|fetterProtoId..)
        RANDOM_TERRAIN_GRID = 8, // 随机地形格子
        MERCENARY_DROP_BOX = 9, //S6赏金猎人宝箱
        CONTROL_RECORD_GRID = 10, //准备阶段记录特殊格子信息(操控对应英雄id|初始位置)
        SHIMMERSCALE_EQUIPMENTS = 11, //S7微光之鳞武器
        ASTRAL_HERO = 12, //S7星系
        DRAGONGOD_FETTER_EFFECT = 13, //S7龙神， 羁绊激活添加激活英雄的基础职业或种族数量（SpecClassType，数量，特效名字） S7.5龙神增加人口--注意要准备阶段记录羁绊激活状态才会处理
        DropSphere = 14, // 羁绊激活时掉落法球,法球只能在首次激活时掉落
        PlayAppointEffect = 15, // 羁绊激活时播放指定特效，S8淘气包随机, S10超级波普指定
        OpenEquipStore = 16, // 羁绊激活时打开装备商店，S8AI程序
        ReplaceHero = 17, // 羁绊激活时替换英雄所有配置（包括模型等）
        NeedRecordActiveLevel = 18,
    }

    public enum CHESS_UNIT_TYPE
    {
        [EnumName("英雄")]HERO = 0,
        [EnumName("召唤物")]SUMMON = 1, //召唤物
        [EnumName("野怪")]MONSTER = 2, //野怪
        [EnumName("合成英雄，如超级魔装机神（盖伦）")]POLYMERIZATION_HERO = 3, //合成英雄，如超级魔装机神（盖伦）
        [EnumName("天选之人")]HEAVEN_BUFF_HERO = 4, //天选之人
        [EnumName("物件（不可控制）")]SCENCE_OBJ = 5, //物件（不可控制）
        [EnumName("不可上场(只能在备战席)")]WAIT_ONLY = 6, //不能上阵的(s10的礼物盒)
        [EnumName("无")]NONE = 10, //其他
    }

    /// <summary>
    /// 英雄特殊功能类型
    /// </summary>
    public enum HeroMoreFunType
    {
        None = 0,
        SummonWithFetter = 1, // 召唤物带羁绊功能
        UpHeroOpenStore = 2,   // 上场英雄打开装备商店功能
        SkillMode = 3,         // 表现面板只展示固定属性
        BeSummonedAddFetter = 4, // 被召唤时增加羁绊面板
        HeroTransform = 5, // 变身功能，参数int: 英雄变身的条件类型(HeroTransformCondType); 参数字符见HeroTransformCondType枚举: 
        SummonNoHeroInfoPanel = 6, //不显示英雄信息面板
        HideHpBar = 7,             //不显示血条
        Breakout = 8, //变形英雄的特殊羁绊展示 参数 [英雄id]|[不可共存羁绊]
        SummonNoHeroInfoAndHpbar = 9, //不显示英雄信息面板和学校
        HeroRecordMax = 10, //升星取最大
    }

    /// <summary>
    /// 英雄变身的条件类型
    /// </summary>
    public enum HeroTransformCondType
    {
        PlayerExpLevel = 0,  // 玩家经验等级相关 (S9天使变身)  参数字符:（到达的玩家经验等级，变身英雄id）
        HeroMove = 1,        // 英雄移动变身（S6杰斯变身） 参数字符:(哪一行，变身英雄id，变身动作)
        CombatEffect = 2,         // 英雄添加效果包时变身（S10 KDA）
        TurnCount = 3,       //随着回合数变化的变身
    }
    
    /// <summary>
    /// 特殊变形规则来源
    /// </summary>
    public enum HeroTransformRule
    {
        [EnumName("无")]
        None,
        [EnumName("英雄表变身")]
        ACGHeroRule,
    }

    public enum HeroTransformType
    {
        NORMAL = 0,
        GRID,
    }
    
    public enum HERO_ABILITY_TYPE
    {
        TANK = 0,
        AD = 1,
        AP = 2,
    }

    public enum UNIT_PREVIEW_EFFECT
    {
        NONE = 0,
        FLOOR_FLASH = 1, //地板选中闪光 参数：特效时间（毫秒）
        ADD_TILE_EFFECT = 2, //添加格子特效 参数：特效名字
        ADD_HERO_EFFECT = 3, //添加英雄特效 参数：特效名字
        ADD_PREVIEW_BUFF = 4, //使用预览buff
        ONLY_PREVIEW_NO_EFFECT = 20, //只做预览选择，不做特效处理
    }

    //交叉类型;
    public enum InterestType
    {
        None,
        Cross,
        In,
    }

    public enum HEAVEN_BUFF_HERO_TYPE
    {
        ADD = 0, //添加
        REMOVE = 1, //移除
        MOVE = 2, //移动
    }


    public enum HEAVEN_BUFF_SELECT_TARGET_TYPE
    {
        None = 0,
        All_Friend = 1, //全部友方
        All_Enemy = 2, //全部敌方
    }

    public enum HEAVEN_BUFF_FUNC_TYPE
    {
        None = 0,
    }

    public enum REBORN_SOURCE_TYPE
    {
        None = 0,
        Buff = 1,
    }


    public enum ATKBonusType
    {
        Invalid = 0,
        Base, // 基础攻击加成
        Extra // 额外攻击力加成
    }

    public enum ATKBonusNumType
    {
        Invalid = 0,
        Percentage, // 百分比加成
        Exact // 具体数值加成
    }

    public enum DamageType
    {
        [EnumName("NONE")] None = 0,
        [EnumName("物理攻击")] Physical = 1, // 物理攻击
        [EnumName("魔法攻击")] Magical = 2, // 魔法攻击
        [EnumName("真实伤害")] Pure = 3, // 真实伤害
    }

    /// <summary>
    /// 特殊效果添加的羁绊类型
    /// </summary>
    public enum AddSpecClassType
    {
        None = 0, // 未启用时放的枚举位置
        Hex = 1, // 海克斯添加
        DragonGod = 2, // 龙神添加
    }

    //治疗类型
    public enum HealType
    {
        Life = 1, //血量
        Shield = 2, //护盾
    }

    public enum AttackType
    {
        [EnumName("无")] None = -1, // 
        [EnumName("普通攻击")] NormalAttack = 0, // 普通攻击
        [EnumName("技能攻击")] SkillAttack = 1, // 技能攻击
        [EnumName("装备或羁绊攻击")] EquipmentOrFetterAttack = 2, //装备或羁绊攻击
        [EnumName("英雄攻击(包括普攻和技能)")] HeroAttack = 3, //包括普攻和技能
        [EnumName("羁绊攻击")] FettersAttack = 4,
        [EnumName("装备攻击")] EquipmentAttack = 5, //装备攻击
        [EnumName("全部")] All = 10, //全部
    }

    public enum NotHeroAttackSourceType
    {
        [EnumName("无")] None = 0,
        [EnumName("装备")] Equipment = 1,
        [EnumName("羁绊")] Fetters = 2,
    }

    //伤害阶段
    public enum DamageRecordStage
    {
        [EnumName("初始伤害(计算过暴击)")] OrgDamage = 0, //初始伤害
        [EnumName("增幅后伤害")] DamageAfterAdd, //增幅后伤害
        [EnumName("抗性计算后伤害")] DamageAfterResistance, //抗性计算后伤害
        [EnumName("实际伤害")] RealDamage, //实际伤害
        [EnumName("超出伤害(击杀溢出伤害)")] OverDamage, //超出伤害
        [EnumName("减免伤害")] ReduceDamage, //减免伤害
        [EnumName("斩杀伤害")] SeckillDamage, //斩杀伤害
        [EnumName("实际伤血(受击者真正掉的血量)")] DamageHp, //实际伤血
        [EnumName("护盾减免伤害")] ShieldReduceDamage,
        [EnumName("实际伤血+护盾减免）")] FinalDamageAttrChange,
        [EnumName("实际伤害+护盾减免")] RealDamageAndShieldReduceDamage,
        [EnumName("护盾减免前实际伤害")] RealDamageBeforeShieldReduce,
        [EnumName("原始伤害(未计算暴击)")] OrgDamageWithoutCalCrit,
        [EnumName("是否暴击")] IsCrit,
        [EnumName("伤害类型(1物理;2魔法;3真实)")] DamageType,
        [EnumName("Max")] Max,
    }

    public enum RecordKeyType
    {
        [EnumName("None")] None = 0,
        [EnumName("技能释放tag")] SkillTag, //技能释放tag
        [EnumName("buff原型id")] BuffProtoId, //buff原型id
    }

    [Serializable]
    [ProtoContract(ImplicitFields = ImplicitFields.AllPublic)]
    public enum TriggerAttackType_Enum
    {
        [EnumName("无")] Nothing,
        [EnumName("普攻")] Trigger_NormalAttack,
        [EnumName("技能")] Trigger_SkillAttack,
        [EnumName("全部")] Trigger_All,
        [EnumName("普攻或技能(仅在buff触发器的条件中使用)")] Trigger_NormalSkillAttack,
    }

    public enum AddHurtFilterType
    {
        [EnumName("无")] None = 0,
        [EnumName("羁绊")] Fetters = 1,
        [EnumName("种族")] Spec = 2,
        [EnumName("职业")] Class = 3,
        [EnumName("小于血量(比较值|是否百分比(默认否)|是否是攻击者(默认否))")] HpSmall = 4, //参数1：比较值  参数2：是否百分比(默认否)

        [EnumName("受击者判断buff层数(buff原型id|要求层数|是否判定攻击者是施法者|是否小于等于层数)")]
        VictimHasBuff = 5, //buff原型id|要求层数|是否判定自己
        [EnumName("种族或职业")] SpecOrClass = 6, //参数id规则同羁绊

        [EnumName("最大血量比较(比较值|是否小于(默认大于等于)|是否是攻击者的血量(默认否)|是否不考虑等于(默认考虑))")] // 可以配 大于、大于等于、小于，原来可以配小于、大于等于
        MaxHpCompare = 7, //参数1：比较值  参数2：是否小于
        [EnumName("是否阻挡（角度）")] CheckBlock = 8,

        [EnumName("攻击者判断buff层数(buff原型id|要求层数|是否判定受击者是施法者|是否小于等于层数)")]
        AttackerHasBuff = 9,
        
        [EnumName("基于受击者已损失生命百分比线性确定加成(最低伤害加成百分比|最高伤害加成百分比)")]
        VictimLostHp = 10,
        [EnumName("比较某属性值时加成(属性类型|值|是否是攻击者的血量(默认否)|是否小于(0表大于)|是否不考虑等于(0表示考虑))")]
        CompareProperty = 11,
        [EnumName("单体/AOE伤害生效判定(0表示都生效，1表示只生效单体，2表示只生效aoe)")]
        JudgeAoeDamage = 12,
        [EnumName("星级差确定伤害加成(是否攻击者大于受击者|星级差1星的数值|星级差2星以上的数值)")]
        CompareStar = 13,
    }

    public enum AI_MOVE_MODEL
    {
        [EnumName("无")] None = 0,
        [EnumName("通用")] Normal = 1,
        [EnumName("直接移动")] Straight_Move = 2,
    }

    public enum BuffVariableKey
    {
        [EnumName("无")] None = 0,
        [EnumName("buff原型id")] BuffProtoId = 1,
        [EnumName("buff原型id+属性")] BuffProtoIdAndAttribute = 2,
    }

    public enum VariableValueDealType
    {
        [EnumName("无")] None = 0,
        [EnumName("替换")] Replace = 1,
        [EnumName("累加")] Add = 2,
    }

    public enum ValueDealType
    {
        [EnumName("累加")] Add = 0,
        [EnumName("设置")] Set = 1,
    }

    public enum AfterRecordDeadType
    {
        [EnumName("无")] None = 0,
        [EnumName("通知装备伤害记录")] NotifyEquipmentDamageChange = 1, //参数是装备配置id
        [EnumName("自身加buff")] AddBuff = 2, //参数是buffcfgid
        [EnumName("技能飘字")] SkillPlayText = 3,
        [EnumName("流血（参数：对应流程配置id）")] Bleeding = 4,
        [EnumName("多共存buff比对记录值，大于触发BT_BIGGER_THAN_HURT_RECORD（参数：比对值）")]
        BiggerThanRecord = 5,
        [EnumName("减免伤害传递（参数：伤害key值）")]
        DamageTransfer = 6,
    }

    public enum CSoTargetSelectType
    {
        [EnumName("无")] None = 0,
        [EnumName("周围一个空格子")] NearEmptyGrid = 1, //参数：范围
        [EnumName("指定格子")] Grid = 2, //参数：行|列
        [EnumName("从左下起始外扩")] START_ORIG_POS = 3, //参数：范围
        [EnumName("实际坐标值")] True_Pos = 4, //参数：范围
    }

    //攻击数据来源
    public enum AttackerSourceType
    {
        [EnumName("无")] None = 0,
        [EnumName("使用召唤者作为攻击来源")] Summoner = 1,
    }

    public enum HitRangeType
    {
        [EnumName("无")] None = 0,
        [EnumName("单体")] Single = 1,
        [EnumName("群体")] Aoe = 2,
    }

    /// <summary>
    /// 羁绊预览效果显示时机类型
    /// </summary>
    public enum FetterPriewShowTimeType
    {
        [EnumName("无")] None = 0,
        [EnumName("添加时")] Add = 1,
        [EnumName("当前英雄移动时")] Hero_Move = 2,
    }

    /// <summary>
    /// 改变羁绊预览目标时机类型
    /// </summary>
    public enum ChangeFetterPriewTargetTimeType
    {
        [EnumName("无")] None = 0,
        [EnumName("当前英雄移动时")] Hero_Move = 1,
        [EnumName("所有英雄移动时")] All_Hero_Move = 2,
        [EnumName("所有英雄传装备时")] All_Hero_Equip = 3,
    }

    /// <summary>
    /// 特效相关特殊脚本类型
    /// </summary>
    public enum EffectSpecialScriptType
    {
        [EnumName("无")] None = 0,
        [EnumName("随机特效")] RandomEffect = 1,
        [EnumName("多层特效（传入层数）")] MultiLayerEffect = 2,
    }

    /// <summary>
    /// buff添加来源
    /// </summary>
    public enum BuffAddSource
    {
        [EnumName("无")] None = 0,
        [EnumName("随机特效")] Equipment = 1,
        [EnumName("羁绊")] Fetters = 2,
    }

    public enum TransforAttrCompareType
    {
        [EnumName("无")] None = 0,
        [EnumName("一直转换")] Always = 1,
        [EnumName("超出才转换")] Bigger = 2,
        [EnumName("低于才转换")] Smaller = 3,
        [EnumName("等于才转换")] Equal = 4,
    }
    
    public enum TransforAttrCalType
    {
        [EnumName("默认:(原属性值-比对属性值)*转换比例/1000")] Normal = 0,
        [EnumName("固定值: 转换比例/1000")]  TransforScale= 1,
    }

    public enum BuffTriggerFilterGroup
    {
        [EnumName("无")] None = 0,
        [EnumName("Set5小炮")] Group = 1,
    }

    //SkillReleaseTag来源
    public enum BuffSkillReleaseTagResource
    {
        [EnumName("无")] None = 0,
        [EnumName("触发参数获得")] FromTriggerParam = 1,
        [EnumName("buffData获得")] FromBuffData = 2,
    }

    public enum BuffAttributeRecordType
    {
        [EnumName("无")] None = 0,
        [EnumName("实际变化值")] RealChange = 1,
        [EnumName("溢出值")] Overflow = 2,
        [EnumName("原始修改值")] OrigChange = 3,
    }

    public enum BattleHeroMoveType
    {
        None = 0,
        Up,
        Down,
        ChangeClass,
    }

    public enum AdditionalParams
    {
        [EnumName("无")] None = 0,
        [EnumName("流血模块")] BleedingModel = 1,
    }

    /// <summary>
    /// 海克斯buff刷新时机
    /// </summary>
    public enum HexagonBuffRefreshType
    {
        [EnumName("无")] None = 0,
        [EnumName("英雄移动")] MoveHero,
        [EnumName("增加英雄")] AddHero,
        [EnumName("移除英雄")] RemoveHero,
        [EnumName("羁绊装备变更")] ChangeFettersEquipment,
        [EnumName("任意英雄装备变更")] AllHeroChangeEquipment,
        [EnumName("备战席添加英雄时")] WaitHeroAdd,
        [EnumName("备战席移除英雄时")] WaitHeroRemove,
        Max,
    }

    /// <summary>
    /// 显示单元buff全局刷新类型
    /// </summary>
    public enum ChessUnitBuffRefreshType
    {
        [EnumName("无")] None = 0,
        [EnumName("上场当前英雄")] AddHero = 1, // 此时羁绊还没变化
        [EnumName("下场当前英雄(到备战席或售卖)")] RemoveHero = 2, // 此时羁绊还没变化
        [EnumName("在战场上移动己方任一英雄")] MoveMyHero = 3,
        [EnumName("当前英雄的激活羁绊信息改变")] MyFettersChange = 4,
        [EnumName("金币变化时")] MyCoinChange = 5,
        [EnumName("移动当前英雄")] MoveCurHero = 6,
        [EnumName("当前英雄血量上限变化")] HeroHpChanged = 7,
        [EnumName("当前英雄蓝量上限变化")] HeroMpChanged = 8,
        [EnumName("当前英雄装备变化")] HeroEquipChanged = 9,
        [EnumName("玩家种族职业信息栏改变")] SpecClassChanged = 10,
        [EnumName("任一英雄血量上限变化")] MyHeroHpChanged = 11,
        [EnumName("备战席添加英雄时")] WaitHeroAdd = 12,
        [EnumName("备战席移除英雄时")] WaitHeroRemove = 13,
        [EnumName("效果包buff添加或删除时")] CombatEffectPackageChanged = 14,
        [EnumName("准备阶段或抵达阶段(跳客场)英雄加载完成后")] ReadyStageBodyShow = 15,
        [EnumName("全局数据记录变化时(需要参数(type|uniqueId|key))")] ForeverRecordDataSet = 16,
        [EnumName("max(不可选择)")] Max,
    }

    /// <summary>
    /// 战斗阶段buff全局刷新类型
    /// </summary>
    public enum BCBuffRefreshType
    {
        [EnumName("无")] None = 0,
        [EnumName("金币变化时")] MyCoinChange = 1,
        [EnumName("组合触发buff变化时")] CombineBuffTriggerChanged = 2,
        [EnumName("备战席添加英雄时")] WaitHeroAdd = 3,
        [EnumName("备战席移除英雄时")] WaitHeroRemove = 4,
        [EnumName("玩家通过加经验升级时")] AddPlayerExpLevel = 5,
        [EnumName("全局数据记录变化时(需填参数(type|uniqueId|key))")] ForeverRecordChange = 6,
        [EnumName("玩家种族职业信息栏改变")] FetterInfoChange = 7,
        [EnumName("max(不可选择)")] Max,
    }

    public enum UnitShowHideTargetType
    {
        [EnumName("无")] None = 0,
        [EnumName("阴影")] Shadow = 1,
        [EnumName("模型")] Body = 2,
    }

    public enum SeckillStageType
    {
        [EnumName("扣血后")] AfterHurt = 0,
        [EnumName("扣血前")] BeforeHurt = 1,
        [EnumName("计算护盾前")] BeforeCalShield = 2,
        [EnumName("计算伤害加成前(原始伤害)")] BeforeCalDamageBonus = 3,
        [EnumName("计算抗性前")] BeforeCalResistance = 4,
    }

    public enum InjuryCalculationStage
    {
        [EnumName("无")] None = 0,
        [EnumName("计算护盾前")] BeforeCalShield = 1,
        [EnumName("计算伤害前")] BeforeCalHurt = 2,
    }

    public enum InjuryPreCalType
    {
        [EnumName("无")] None = 0,

        [EnumName("预计伤害后是否小等于血量限制（护盾加血量）")]
        [ST_Param("血量限制", defaultValue = 0)]
        [ST_Param("是否百分比（否表示数值）", defaultValue = 0)]
        CalAfterHurtIsLessHpLimit = 1,
    }

    //特殊hit类型
    public enum SpecialHitType
    {
        [EnumName("无")] None = 0,
        [EnumName("无伤害流程")] NO_HURT_PASS = 1,
        [EnumName("不触发技能暴击")] NO_TRIGGER_BUFF_SKILL_CRIT = 2,
    }

    //羁绊召唤附加功能类型
    public enum FetterSummonAdditionType
    {
        [EnumName("无")] None = 0,
        [EnumName("驯龙师")] DRAGON_TRAINER = 1,
        [EnumName("场外非英雄单位")] OUT_BATTLE_UNIT = 2,
        [EnumName("S8至高天扎克")] RIFT_WALK = 3,
        [EnumName("S9黑默丁格炮台")] HEIMER_DINGER = 4,
        [EnumName("S9皮尔特沃夫哥斯拉")] PILTOVER_DRAGON = 5,
        [EnumName("S10俄洛伊触手")] ILLAOI_DRUM = 6,
        [EnumName("场外英雄单位")] OUT_HERO = 7,
        [EnumName("S11说书人")] STORY_WEAVER = 8,
    }

    //英雄属性类型
    public enum HeroAttributePartType
    {
        [EnumName("全部")] None = 0,
        [EnumName("基础配置属性")] Base = 1,
        [EnumName("附加属性")] Addition = 2,
    }

    //属性转换类型
    public enum AttrTransformType
    {
        [EnumName("None")] None = 0,
        [EnumName("增加到目标")] AddToTarget = 1,
        [EnumName("复制到目标")] CopyToTarget = 2,
    }

    /// <summary>
    /// 复制装备的规则
    /// </summary>
    public enum CopyEquipRuleType
    {
        [EnumName("S5丧尸规则")]
        Zombie_S5, // S5丧尸
        [EnumName("S7.5黯灵龙规则")]
        DarkLight_S7, // S7.5黯灵龙
        [EnumName("通用随机一件规则(成装优先，指定某些装备类型)")]
        CommonRandom,
        [EnumName("S8.5未来战士规则")]
        Infiniteam,
    }

    public enum DropEquipRule
    {
        [EnumName("None")]
        None,
        [EnumName("S10奇亚娜规则(将自身所有装备按推荐丢给目标)")]
        QiYana_S10, // S5丧尸
    }
    
    public enum StageEffectType
    {
        [EnumName("英雄挂点特效")] HangPoint = 0, // 英雄挂点特效
        [EnumName("格子特效")] Grid = 1, // 格子特效
        [EnumName("UI特效")] UI = 2, // UI特效
    }

    public enum ScreenLocation
    {
        [EnumName("中心")] Center = 0,
        [EnumName("左上")] LeftTop = 1,
        [EnumName("右上")] RightTop = 2,
        [EnumName("左下")] LeftDown = 3,
        [EnumName("右下")] RigthDown = 4,
    }

    public enum EffectAbPath
    {
        [EnumName("None")] None = 0,

        [EnumName("art_tft_raw\\effects\\battle\\set\\set7")]
        BattleSet7 = 1,
    }

    /// <summary>
    /// 添加羁绊的条件类型
    /// </summary>
    public enum AddFetterConditionType
    {
        [EnumName("自填羁绊检查类型和id")] Customize = 0,
        [EnumName("目标的第一个种族或职业")] FirstSpecOrClass = 1,
        [EnumName("目标的所有种族和职业")] AllSpecClass = 2,
        [EnumName("从buff公式获取")] GetFromExp = 3,
        [EnumName("指定的玩家永久变量中获取")]  GetFromForeverRecord= 4,
        [EnumName("目标的最后一个种族或职业(str参数为排除的羁绊)")]  LastSpecOrClass= 5,
    }

    /// <summary>
    /// 添加羁绊的效果类型
    /// </summary>
    public enum AddFetterContentType
    {
        [EnumName("羁绊buff")] FetterBuff = 0,
        [EnumName("种族职业的固有buff")] SpecClassBuff = 1,
        [EnumName("羁绊buff、种族职业固有buff")] FetterSpecClassBuff = 2,
        [EnumName("羁绊buff、种族职业固有buff、隐藏添加")] FetterSpecClassBuffHide = 3,
        [EnumName("羁绊buff、种族职业固有buff、显示添加")] FetterSpecClassBuffObvious = 4,
    }

    public enum SkillNoCostType
    {
        [EnumName("全部")] None,
        [EnumName("法师双重施法")] Magic,
        [EnumName("其他")] Other
    }
    
    /// <summary>
    /// 全局变量类型
    /// </summary>
    public enum ForeverRecordType
    {
        [EnumName("其他")] Other = 0,
        [EnumName("羁绊")] Fetter = 1,
        [EnumName("海克斯")] Hex = 2,
        [EnumName("英雄")] Hero = 3,
        [EnumName("组合Buff")] CombineBuff = 4,
        [EnumName("各种触发器")] HexJobTrigger = 5,
    }
    
    public enum BuffCombineKey
    {
        [EnumName("S8管理者(参数为种族id)")] Manager = 1000,
        // xx = 2000
        [EnumName("其他")] Other = 9000,
    }

    public enum RecordBattleDataType
    {
        [EnumName("实际造成伤害(参数1:技能表里的Id)")] RealDamage = 0,
        [EnumName("反伤(无参数)")] CounterInjury = 1,
        [EnumName("多重伤害减免(参数1:复杂属性的key)")] ReduceHurt = 2,
        [EnumName("全能吸血(参数1:百分比)")] HurtToHeal = 3, // 经过所有减治疗等处理的总的生命偷取回复值
        [EnumName("直接治疗(参数1:治疗的buffProtoId,参数2:是否包括过量治疗)")] Heal = 4, // BE_ATTR里的治疗
        [EnumName("实际受到伤害(参数1:时间ms(可不填)")]  BearRealDamageByTime = 5,
        [EnumName("实际受到伤害包括护盾(参数1:攻击者身上要带某buff,参数2:hit的触发类型TriggerBuffType)")]  BearRealDamageWithShield = 6,
        [EnumName("全能吸血溢出治疗(参数1:百分比)")]  HurtToHealOverFlow = 7,
        [EnumName("实际造成伤害(参数1:Hit里的AttackType)")] RealDamageByTriggerType = 8,
        [EnumName("实际受到伤害包括护盾(参数1:时间ms(可不填)")]  BearDamageWithShieldByTime = 9,

    }

    public enum AccuracyType
    {
        [EnumName("伤害加成精度")]
        DamageBonus = 0, // 对应伤害流程的CalDamageBonus
        [EnumName("伤害千分比精度")]
        DamageCoefficient = 1, // 对应hitdata里的damageCoefficient
        [EnumName("回复当前蓝量")]
        ReplyMpNow = 2 // 对应改变蓝量属性、攻击受击回复蓝量
    }

    public enum BuffFuncForPlayer
    {
        [EnumName("治疗小小英雄血量(参数1为1表示回合可重复的加血;参数2为1表示野怪回合也能加血)")]
        AddPlayerHp = 0,
        [EnumName("增加经验")]
        AddExperience = 1,
        [EnumName("增加商店刷新次数(参数:持续回合数)")]
        AddHeroStoreCnt = 2,
        [EnumName("触发控制器（参数：控制器类型，触发类型）")]
        TriggerPlayerCtrl = 3,
        [EnumName("弹出错误码")]
        ShowErrorCode = 4,
        [EnumName("执行场外单位特效/动画")]
        AddOutFieldEffect = 5,
        [EnumName("添加类似巴德木灵单位")]
        AddBuddUnit = 6,
        [EnumName("添加小小英雄特效(Str参数1用 特效名:挂点)")]
        AddTinyHeroEffect = 7,
        [EnumName("增加小小英雄血量上限")]
        AddPlayerMaxHp = 8,
    }

    public enum CalChangeType
    {
        [EnumName("None")]
        None,
        [EnumName("相等")]
        Equal,
        [EnumName("增加")]
        Add,
        [EnumName("减少")]
        Reduce,
    }

    public enum SpecialSummonType
    {
        None, // 普通类型
        RebornHero, // 被复活的英雄，bc的isSummon会被认为是英雄，伤害会记录到数据源英雄上，会继承数据源的死亡不清除的buff
    }

    public enum SkinType
    { 
        Hero = 0, // 英雄皮肤,dataId为英雄groupid
        Fetter = 1, // 羁绊皮肤,dataId为羁绊id
        Terrain = 2, // 地形皮肤,dataId为地形id
        FetterSummonHero = 3, // 召唤英雄羁绊类皮肤,dataId为英雄groupId，data1为羁绊id
        FetterOutFieldSummon = 4, // 召唤场外单位羁绊类皮肤, dataId为英雄groupId，data1为羁绊id
        FetterDrop = 5, // 羁绊掉落皮肤
        FetterStoreView = 6, //商店皮肤
    }

    public enum FetterClientEffectType
    {
        HangPointEffect = 0, // 挂点特效
        FloorEffect = 1, // 地板特效
        PositionEffect = 2  // 真实位置特效
    }
    
    public enum HexJobCalType
    {
        None = 0,
        LinearCal = 1, 
    }
    
    /// <summary>
    /// 特殊羁绊类型
    /// 之后将用于准备阶段的buff效果，类似于复杂属性的效果
    /// </summary>
    public enum SpecialFetterType
    {
        // CheckNeedRecordActiveLevel = 0, // 需要记录已激活等级，重回时要根据已激活等级检测效果
        None = 0,
        CheckMergeHero = 1, // 英雄合成
        CheckSummonHero = 2, // 准备阶段召唤
        CheckRoundActiveFetters = 3, // 游戏开始随机一个羁绊效果
        CheckRandomTerrainGrid = 4, // 随机地形
        CheckMercenaryChest = 5,   // S6赏金猎人宝箱
        CheckControlRecordGrid = 6,// 准备阶段记录特殊格子信息
        CheckShimmerscaleEquipments = 7, // 微光之麟武器
        CheckAstralHero = 8, // S7星系
        DragonGodFetterEffect = 9, //S7龙神， 羁绊激活添加激活英雄的基础职业或种族数量（SpecClassType，数量，特效名字） S7.5龙神增加人口--注意要准备阶段记录羁绊激活状态才会处理
        DropSphere = 10, // 羁绊激活时掉落法球,法球只能在首次激活时掉落
        PlayRandEffect = 11, // 羁绊激活时播放随机特效，S8淘气包
        OpenEquipStore = 12, // 羁绊激活时打开装备商店，S8AI程序
        ReplaceHero = 13, // 羁绊激活时替换英雄所有配置（包括模型等）
        Max,
    }

    public enum SpecialCalIntSymbolType
    {
        [EnumName("None")]
        None = 0,
        [EnumName("计算所有位数的和（参数1:无; 参数2:无）")]
        SumAllDigitValue = 1,
        [EnumName("覆盖某一位的值（参数1:第几位; 参数2:小于10的值）")]
        CoverDigitValue = 2,
        [EnumName("阶乘计算")]
        Factorial = 3,
    }

    public enum TerrainGridSpecialType
    {
        [EnumName("None")]
        None = 0,
        [EnumName("野怪选秀后一回合重新生成")] // 淡紫之海规则：每次野怪或选秀后随机出一个位置，并与之前的不重复
        TheLavenderSea = 1,
        [EnumName("多生成一个格子")] // 诺克萨斯角斗场规则，每次选择海克斯时多生成一个格子
        Noxkraya = 2,
        [EnumName("前俩排不同列的随机格子")]
        DifferColFirstTwoRow = 3,
        [EnumName("不同行列的随机格子")]
        DifferColRow = 4,
        [EnumName("需要变身的格子")]
        NeedChangeShape = 5,
    }

    // S11天命添加buff规则枚举
    public enum FateAddBuffRule
    {
        LinkFateGetLinkBuff = 1, // 连线天命获得连线天命buff
        AllFateGetLinkBuff = 2, // 所有天命获得连线天命buff 
        AllFateGetAllBuff = 3, // 所有天命获得所有天命buff
    }
    
    public enum DamageContextParamType
    {
        [EnumName("None")]
        None = 0,
        [EnumName("是否忽略所有伤害加成")]
        IsIgnoreCalAdd = 1,
        [EnumName("是否忽略计算抗性")]
        IsIgnoreCalResistance = 2,
        [EnumName("是否忽略计算反伤")]
        IsIgnoreCalCounterInjury = 3,
        [EnumName("是否忽略战斗流程结束判断特殊流程(伤转护盾/附加对记录对象伤害)")]
        IsIgnoreCalOnHurtEnd = 4,
        [EnumName("是否忽略护盾计算")]
        IsIgnoreShield = 5,
        [EnumName("是否忽略无敌计算")]
        IsIgnoreImmune = 6,
        [EnumName("是否忽略斩杀")]
        IsIgnoreSec = 7,
        [EnumName("是否忽略女妖")]
        IsIgnoreSkillShield = 8,
        [EnumName("是否忽略固定数值伤害减免")]
        IsIgnoreCalFixedHurtReduce = 9,
        [EnumName("是否忽略所有伤害减免")]
        IsIgnoreHurtReduce = 10,
        [EnumName("预留参数1")]
        ReversedParam1 = 18,
        [EnumName("预留参数2")]
        ReversedParam2 = 19,
        [EnumName("预留参数3")]
        ReversedParam3 = 20,
        Max,
    }
    
    public enum HitShapeType
    {
        [HitShapeAttribute("格子范围(按圈查找, range=1标识周围一圈)")]
        Rect = 0,
        [HitShapeAttribute("圆形(已禁用)", false)]
        Circle = 1,
        [HitShapeAttribute("扇形(已禁用)", false)]
        Sector = 2,
        [HitShapeAttribute("线与新矩形(已禁用)", false)]
        Line_Rect = 3,
        [HitShapeAttribute("穿透身后一格(已禁用)", false)]
        CrossOverBodyBackOneGrid = 4,
        [HitShapeAttribute("真实碰撞-矩形")]
        Rect_Real = 5,
        [HitShapeAttribute("真实碰撞-扇形")]
        Sector_Real = 6,
        [HitShapeAttribute("面前一格为圆心的圈范围(已禁用)", false)]
        Circle_AtBodyForwardOneGrid = 7,
        [HitShapeAttribute("真实碰撞-圆形")]
        Circle_Real = 8,
        [HitShapeAttribute("真实碰撞-圆环")]
        Ring_Real = 9,
        [HitShapeAttribute("(新)真实碰撞-矩形(支持浮点数)")]
        Rect_Real_New = 10,
    }
    
    public enum SelectTargetRandomWaitPosSelectType
    {
        [EnumName("随机")]
        Random = 0,
        [EnumName("根据品质选择）")]
        SoryByQuality = 1,
        [EnumName("根据星级选择")]
        SoryByStar = 2,
        [EnumName("空格子")]
        EmptyCell = 10,
    }
}

#region 需要把枚举统一到这里
public enum FloatTextType
{
    [EnumName("默认")]
    None = 0,                               //无
    [EnumName("一次性物理伤害")]
    NormalAttack,                           //普通攻击
    [EnumName("正常暴击")]
    NormalCritAttack,                       //正常暴击
    [EnumName("高倍暴击")]
    HighCritAttack,                         //高倍暴击
    [EnumName("一次性魔法伤害")]
    SkillDamage,                            //一次性技能伤害
    [EnumName("持续性法术伤害")]
    ContinuedSkillDamage,                   //持续性法术伤害
    [EnumName("高爆发技能伤害")]
    CritSkillDamage,                        //高爆发技能伤害
    [EnumName("真实伤害")]
    TrueDamage,                             //真实伤害
    [EnumName("格挡")]
    Block,                                  //格挡
    [EnumName("闪避")]
    Dodge,                                  //闪避
    [EnumName("未命中")]
    Miss,                                   //未命中
    [EnumName("治疗")]
    Heal,                                   //治疗
    [EnumName("技能暴击伤害")]
    SkillCritDamage,                        //技能暴击伤害
    [EnumName("真实暴击伤害")]
    TrueCritDamage,                         //真实暴击伤害
    [EnumName("免疫魔法伤害（盖伦）")]
    ImmuneMagic,                            //免疫魔法伤害（盖伦）
    [EnumName("免疫全体伤害（剑姬、天使）")]
    ImmuneAll,                              //免疫全体伤害（剑姬、天使）
    [EnumName("伤害上限减免伤害")]
    Reduction,                              //伤害上限减免伤害
    [EnumName("小小英雄受击伤害")]
    PlayerDmg = 17,                         // 小小英雄受击伤害
    [EnumName("小小英雄受击总伤害")]
    PlayerDmgAll = 18,                      // 小小英雄受击总伤害
    [EnumName("全伤害减免")]
    AllHurtReduction = 19,                  //全伤害减免
    [EnumName("免疫控制效果（剑姬、天使）")]
    ImmuneControl = 20,                     //免疫控制效果（剑姬、天使）
    [EnumName("低于阈值(50)的物理伤害")]
    LightPhysicalDamage = 21,               //低于阈值(50)的物理伤害
    [EnumName("低于阈值(50)的暴击物理伤害")]
    LightCritPhysicalDamage = 22,           //低于阈值(50)的暴击物理伤害
    [EnumName("低于阈值(50)的魔法伤害")]
    LightMagicDamage = 23,                  //低于阈值(50)的魔法伤害
    [EnumName("低于阈值(50)的暴击魔法伤害")]
    LightCritMagicDamage = 24,              //低于阈值(50)的暴击魔法伤害
    [EnumName("低于阈值(50)的真实伤害")]
    LightTrueDamage = 25,                   //低于阈值(50)的真实伤害
    [EnumName("低于阈值(50)的暴击真实伤害")]
    LightCritTrueDamage = 26,               //低于阈值(50)的暴击真实伤害
    [EnumName("低于阈值(50)的生命回复")]
    LightHeal = 27,                         //低于阈值(50)的生命回复
    [EnumName("小小英雄最后一发受击")]
    PlayerDmgAllEnd = 28,                   //小小英雄最后一发受击
    [EnumName("持续性物理伤害")]
    ContinuedPhysicalDamage = 29,                //持续性物理伤害
    [EnumName("持续性真实伤害")]
    ContinuedRealDamage = 30,                //持续性真实伤害
    [EnumName("名气层数（羁绊用飘字")]
                                             // ShiledReduceDamage = 31,                 //护盾吸收的伤害,废弃了
    FetterBuffStack = 32,                         //名气层数（羁绊用飘字
}

public enum BUFF_SHADER_EFFECT
{
    COMMON = 0,
    ASSASSIN_INVISIBLE = 1,//刺客隐身
    DIZZY = 2,//眩晕变色
    CURVE_TYPE = 3,//curveType（可以单个英雄重写效果）
}

public enum CHANGE_TYPE
{
    [EnumName("隐藏信息栏")]
    HIDE_INFOBAR = 1,//隐藏信息栏
    [EnumName("设置mp状态")]
    SET_MP_STATUS = 2,//设置mp状态
    [EnumName("设置异常状态ui显示")]
    SET_STATUS_EFFECT = 3,//设置异常状态ui显示
    [EnumName("设置血条ui的缩放比例")]
    SET_STATUS_SCALE = 4,//设置血条ui的缩放比例
}

public enum BattleFieldAddBuffMoment
{
    [EnumName("None")]
    None = 0,
    [EnumName("周期")]
    Cd = 1,//周期
}

public enum BattleFieldBuffTargetType
{
    [EnumName("NONE")]
    NONE = 0,
    [EnumName("同一阵营全体")]
    FRIEND_ALL = 1,//同一阵营全体
    [EnumName("敌方全体")]
    ENEMY_ALL = 2,//敌方全体
    [EnumName("战场全体")]
    ALL = 3,//战场全体
}

public enum ValueExtendType
{
    None = 0,
    NowHpMaxThousandRatio = 1,//当前最大血量千分比
    NowHpThousandRatio = 2,//当前血量千分比
    OrgHpMaxThousandRatio = 3,//原始最大血量千分比
    OrgHpThousandRatio = 4,//原始血量千分比
}

public enum ProjectileReplaceType
{
    NORMAL_ATK,
    SPELL_ATK,
    ALL,
    HIT_ID,
    NOTHING = 999
}

public enum SpecClassType
{
    [EnumName("种族")]
    Spec = 0,
    [EnumName("职业")]
    Class = 1,
}

public enum BeatenStateEnum
{
    Normal,//正常
    Defend,//防御姿态
    UnSelect,//不可选择
    UnAttack,//不可攻击
    WitchTimeTrigger,//魔女时间触发
}

public enum TargetSelectExtendType
{
    [ST_Title("默认")]
    None = 0,
    [ST_Title("支援者寻找友方只考虑支援者友方")]
    AssistTeamPlayerFriendOnly = 1,
}

public enum Projectile_StartAngle_Relate
{
    [EnumNameAttribute("发射者")]
    Self,//自己
    [EnumNameAttribute("绝对角度 客场会翻转")]
    World,//绝对角度
    [EnumNameAttribute("发射者(可飞行道具)和目标")]
    Self2Target,//发射者(可以是飞行道具) 和 目标
    [EnumNameAttribute("飞行道具和目标")]
    ProjectileSelf2Target,// 飞行道具 和 目标
    [EnumNameAttribute("发射者(只能是人)和目标")]
    ProjectileOwner2Target,//发射者 （只能是人）和 目标
    [EnumNameAttribute("自己(飞行道具)")]
    ProjectileSelf,//发射者 （只能是人）和
    [EnumNameAttribute("目标(人和飞行道具)")]
    TargetSelf,
    
    [EnumNameAttribute("真.绝对角度")]
    World_Real,//绝对角度
    /* Target,*/
}

public enum BroadcastType
{
    BattleBroadcast = 0,            // 通用战场播报
    AnimationBroadcast = 1,             // 英雄专属动画播报
    BadgeBroadcast = 2,                 //新增英雄评级播报
}

// 根据策划设定的优先级从低到高排序
public enum BattleBroadcastType
{
    None = 0,                           // 无
    TripleKill = 1,                     // 三杀
    QuadraKill = 2,                     // 四杀
    PentaKill = 3,                      // 五杀
    Dominating = 4,                     // 主宰比赛, 击杀六名敌方单位
    Unstoppable = 5,                    // 无人能挡, 击杀七名敌方单位
    GodLike = 6,                        // 击杀八名敌方单位
    Legendary = 7,                      // 击杀九名及以上敌方单位
    ShutDown = 8,                       // 击杀对方五杀以上的单位
    Badge = 9,                          //头顶徽章
    Count
}

// 战场播报皮肤类型(商业化预留)
public enum BattleBroadcastSkinType
{
    BaseOneStar = 0,                // 基础一星播报皮肤
    BaseTwoStar = 1,                // 基础二星播报皮肤
    TwoStarDragon = 2,               // 二星巨龙之巢
    TwoStarFist = 3,                //二星怪兽入侵
    TwoStarPoro = 4,                //二星魄罗
    TwoStarSet10 = 5,               //S10二星播报
    TwoStarSet11 = 6,               //S11二星播报
    Count,
    Badge = 5000,                   
    
}



public enum AnimationBroadcastType
{
    Kill = 1000,                   // 主宰比赛
    Shield = 1001,                 // 坚毅壁垒
    Guard = 1002,                  // 神圣守护
    Control = 1003,                // 掌控全场
    BadgeLevelUp = 1004,           //徽章等级提升
}

public enum AnimationBroadcastDirection
{
    Left = 1,                   // 往左飘
    Right = 2,                  // 往右飘
    Left_OneStar = 3,           //一星左播报
    Right_OneStar = 4,          //一星右播报
    Left_ZZ = 5,
    Right_ZZ = 6,
}

public enum MotionDisposeType
{
    Normal,
    ProjectileRelaunch
}

#endregion