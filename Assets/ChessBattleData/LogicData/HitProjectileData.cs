using TDRConfig;
#if !ACGGAME_CLIENT || LOGIC_THREAD
using UnityEngine4Server;
#else
using UnityEngine; 
#endif
namespace Lucifer.ActCore
{
    public class HitProjectileData
    {
        /// <summary>
        /// 飞行道具名称
        /// </summary>
        public string projectileName;

        public int hitID;

        public Vector3 positionOffset;

        public bool isBindPosition;

        public int MaxCountInOneSkill = -1;

        public TriggerAttackType_Enum triggerType = TriggerAttackType_Enum.Trigger_All;

        public HitBuffTrigger_Enum trigger = HitBuffTrigger_Enum.ATK_LAST;

        public int MaxCountGlobal = -1;

        public int CurrentCountGloabl = 0;

        public bool Enable = true;
        
        /// <summary>
        /// 动态效果 发送给飞行道具，即发送者移除了动态效果 但是飞行道具还会保留
        /// </summary>
        public bool CopyToProjectile = false;
        
        /// <summary>
        /// 只在特定伤害类型才会触发，none 表示所有，默认是 none
        /// </summary>
        public DamageType damageType = DamageType.None;
        /// <summary>
        /// 开始的位置是否在目标上
        /// </summary>
        public bool isStartOnTarget = true;
        //动态释放的tag
        public int ReleaseTag = 0;
        public HitProjectileData(string projectileName, int hitID, bool isBindPosition, Vector3 positionOffset, HitBuffTrigger_Enum trigger,
            int maxCount = -1,bool isStartOnTarget = true)
        {
            this.projectileName = projectileName;
            this.hitID = hitID;
            this.isBindPosition = isBindPosition;
            this.positionOffset = positionOffset;
            this.MaxCountInOneSkill = maxCount;
            this.trigger = trigger;
            this.isStartOnTarget = isStartOnTarget;
        }
        public HitProjectileData(HitProjectileData other)
        {
            projectileName = other.projectileName;;
            
            hitID=other.hitID;
            
            positionOffset=other.positionOffset;
            
            isBindPosition=other.isBindPosition;
            
            MaxCountInOneSkill=other.MaxCountInOneSkill ;
            
            triggerType =other.triggerType ;
            
            trigger  =other.trigger  ;
            
            MaxCountGlobal =other.MaxCountGlobal ;
            
            CurrentCountGloabl=other.CurrentCountGloabl ;
            
            Enable =other.Enable ;
            
            CopyToProjectile =other.CopyToProjectile ;

            ReleaseTag = other.ReleaseTag;

            isStartOnTarget = other.isStartOnTarget;

            damageType = other.damageType;
        }

    }
}
