#if ACGGAME_CLIENT
using UnityEngine;
#else
using UnityEngine4Server;
#endif
using System.Collections.Generic;
using System;
using ZGameChess;

/// <summary>
/// 角色挂点位置信息
/// </summary>
public partial class CharacterHangPoint : MonoBehaviour
{

#if ACGGAME_CLIENT

    #region 挂点的基本属性集合

    public Vector3 posOffset;
    [ReadOnly]public int initStandEulerY = 225;//初始化站时欧拉角Y;
    [ReadOnly]public int initRightNowStandEulerY = 45;
    public AnimationCurve standEulerChangeCurve = AnimationCurve.Linear(0, 0, 1, 1);
    public float standEulerChangeDuration = 1.0f;
    [Header("新的动画优化系统，不使用多layer控制动画")]
    public bool NewAnimatorSystem = false;
    
    [HideInInspector]
    public Vector3 defaultScale; //默认尺寸;
    [Header("星级信息")]
    public List<CharacterStarLevelData> starLevelDataList;
    [Header("系统信息")]
    public List<CharacterSystemScaleData> systemScaleDataList;
    [Header("初始化特效信息")]
    [SerializeField] private List<CharacterInitEffectData> initEffectDataList;
    [Header("动作光效-特效配置文件名")]
    public string actionEventName;
    [SerializeField] private ChessPlayerEffectEventCfg actionEventCfg;    
    [Header("动作光效-材质配置文件名")]
    public string actionMaterialName;
    [SerializeField] private ChessPlayerMaterialCurveCfg actionMaterialCfg;
    [Header("动作声音配置文件名")]
    [SerializeField] private ChessPlayerSoundCfg actionSoundCfg;
    [Header("挂点信息")]
    [HideInInspector]
    public List<CharacterHangPointData> pointPosData = new List<CharacterHangPointData>();
    [HideInInspector]
    public bool pointPosDataFoldOutEditor;
    // public bool EnableOptimizeTransformHierarchy = false;
    //由编辑器自动赋值的;
    [Header("以下为缓存的组件，避免show里的组件需要实时GetComponent")]
    public UnityChan_LION.SpringManager springManager;
    [HideInInspector]
    public CharacterHangPointUpdate characterHangPointUpdate;
    //暴露的骨骼;
    [HideInInspector] public List<string> extractBoneList_editor = new List<string>();
    [Header("挂点映射(没首选时用次选)")] public List<HangPointReflect> hangPointReflects = new List<HangPointReflect>(){new HangPointReflect(SupportHangPointType.T01_LOC, SupportHangPointType.HAND_R_LOC)};

    private LittleLegendCfg m_cfg;
    #endregion

    #region 获取配置接口

    public bool TryClearOwnerLittleLegendCfgs()  // 编辑器专用 删除自身的cfg列表 因为用了新配置
    {
        bool needRemove = initEffectDataList.Count != 0 || actionEventCfg != null || actionMaterialCfg != null || actionSoundCfg != null;
        initEffectDataList.Clear();
        actionEventName = "";
        actionEventCfg = null;
        actionMaterialName = "";
        actionMaterialCfg = null;
        actionSoundCfg = null;
        return needRemove;
    }

    public LittleLegendCfg GetLittleLegendCfg()
    {
        return m_cfg;
    }

    public void SetLittleLegendCfg(LittleLegendCfg cfg)
    {
        m_cfg = cfg;
    }

    public List<CharacterInitEffectData> GetInitEffectDatas()
    {
        if (m_cfg != null)
            return m_cfg.initEffectDataList;
        if (initEffectDataList == null)
            initEffectDataList = new List<CharacterInitEffectData>();
        return initEffectDataList;
    }

    public void SetInitEffectDatas(List<CharacterInitEffectData> initEffectDatas)
    {
        if (m_cfg != null)
            m_cfg.initEffectDataList = initEffectDatas;
        else
            initEffectDataList = initEffectDatas;
    }

    public ChessPlayerEffectEventCfg GetActionEventCfg()
    {
        if (m_cfg != null)
            return m_cfg.actionEventCfg;
        return actionEventCfg;
    }

    public void SetActionEventCfg(ChessPlayerEffectEventCfg eventCfg)
    {
        if (m_cfg != null)
            m_cfg.actionEventCfg = eventCfg;
        else
            actionEventCfg = eventCfg;
    }

    public ChessPlayerMaterialCurveCfg GetMaterialCurveCfg()
    {
        if (m_cfg != null)
            return m_cfg.actionMaterialCfg;
        return actionMaterialCfg;
    }

    public void SetMaterialCurveCfg(ChessPlayerMaterialCurveCfg eventCfg)
    {
        if (m_cfg != null)
            m_cfg.actionMaterialCfg = eventCfg;
        else
            actionMaterialCfg = eventCfg;
    }

    public ChessPlayerSoundCfg GetSoundCfg()
    {
        if (m_cfg != null)
            return m_cfg.actionSoundCfg;
        return actionSoundCfg;
    }

    public void SetSoundCfg(ChessPlayerSoundCfg eventCfg)
    {
        if (m_cfg != null)
            m_cfg.actionSoundCfg = eventCfg;
        else
            actionSoundCfg = eventCfg;
    }

#if UNITY_EDITOR
    public void ClearAllCfg()
    {
        initEffectDataList = new List<CharacterInitEffectData>();
        actionEventName = string.Empty;
        actionEventCfg = null;
        actionMaterialName = string.Empty;
        actionMaterialCfg = null;
        actionSoundCfg = null;
    }
#endif

#endregion

    #region 获取挂点信息的接口
    /// <summary>
    /// 
    /// </summary>
    /// <param name="supportHangPointType"></param>
    /// <param name="actionName"></param>
    /// <returns></returns>
    public virtual CharacterHangPointData GetPosDataByAnimationName(SupportHangPointType supportHangPointType, string actionName, float starHeroModelScaleRate)
    {
        CharacterHangPointData data = this.__GetReallyPosData(supportHangPointType, actionName, starHeroModelScaleRate);
        if (data != null)
        {
            return data;
        }
        return new CharacterHangPointData(transform);
    }

    /// <summary>
    /// 如果要是哟个animaation来做判断的话，要使用GetPosDataByAnimationName
    /// </summary>
    /// <param name="supportHangPointType"></param>
    /// <returns></returns>
    public virtual CharacterHangPointData GetPosData(SupportHangPointType supportHangPointType, string actionLabel = null, float starHeroModelScaleRate = 1.0f)
    {
        if (this == null || this.Equals(null) || gameObject == null)
        {
            return new CharacterHangPointData(transform);
        }
        CharacterHangPointData data = this.__GetReallyPosData(supportHangPointType, actionLabel, starHeroModelScaleRate);
        if (data != null)
        {
            return data;
        }
        return new CharacterHangPointData(transform);
    }

    public CharacterHangPointData __GetReallyPosData(SupportHangPointType supportHangPointType, string actionName = null, float starHeroModelScaleRate = 1.0f, bool needReserverData = true, bool isUseSecond = true)
    {
        SupportHangPointType secondHangPointType = SupportHangPointType.GROUND_LOC;
        for (int i = 0; i < hangPointReflects.Count; i++)
        {
            if (supportHangPointType == hangPointReflects[i].firstChoice)
            {
                secondHangPointType = hangPointReflects[i].secondChoice;
                break;
            }
        }
        return __GetReallyPosData(this.transform, this.pointPosData, supportHangPointType, actionName, starHeroModelScaleRate, needReserverData, secondHangPointType, isUseSecond);
    }
    
    public static CharacterHangPointData __GetReallyPosData(Transform chpTrans, List<CharacterHangPointData> chpDataList, SupportHangPointType supportHangPointType, string actionName = null, float starHeroModelScaleRate = 1.0f, bool needReserverData = true, SupportHangPointType secondHangPointType= SupportHangPointType.GROUND_LOC, bool isUseSecond = true)
    {
        
        CharacterHangPointData reserverData = null;
        bool isNeedAnimationCheck = !string.IsNullOrEmpty(actionName);
        bool isHasHangPoint = false;
        
        for (int i = 0, len = chpDataList.Count; i < len; i++)
        {
            CharacterHangPointData tmpChp = chpDataList[i]; 
            if (tmpChp.supportHangPointType == supportHangPointType)
            {
                isHasHangPoint = true;
                if (!tmpChp.isTransformDataFromAnimation)
                {
                    reserverData = tmpChp;                    
                }
                if (!isNeedAnimationCheck)
                {
                    return tmpChp;   
                }
                else
                {
                    if (tmpChp.isTransformDataFromAnimation && actionName.Equals(tmpChp.dependActionlabel))
                    {
                        tmpChp.starHeroModelScaleRate = starHeroModelScaleRate;
                        tmpChp.groundWorldPos = chpTrans.position;
                        return tmpChp;
                    }
                }
            }
        }

        if (!isHasHangPoint && isUseSecond) //首选没有，走次选的挂点
        {
            for (int i = 0, len = chpDataList.Count; i < len; i++)
            {
                CharacterHangPointData tmpChp = chpDataList[i]; 
                if (tmpChp.supportHangPointType == secondHangPointType)
                {
                    if (!tmpChp.isTransformDataFromAnimation)
                    {
                        reserverData = tmpChp;                    
                    }
                    if (!isNeedAnimationCheck)
                    {
                        return tmpChp;   
                    }
                    else
                    {
                        if (tmpChp.isTransformDataFromAnimation && actionName.Equals(tmpChp.dependActionlabel))
                        {
                            tmpChp.starHeroModelScaleRate = starHeroModelScaleRate;
                            tmpChp.groundWorldPos = chpTrans.position;
                            return tmpChp;
                        }
                    }
                }
            }
        }

        if (isNeedAnimationCheck && needReserverData)
        {
            return reserverData;
        }
        return null;
    }
    public bool ContainHangPointType(CharacterHangPoint.SupportHangPointType supportHangPointType)
    {
        bool containFlag = false;
        for (int i = 0, len = pointPosData.Count; i < len; i++)
        {
            CharacterHangPointData tmpChp = pointPosData[i];
            if (tmpChp.supportHangPointType == supportHangPointType)
            {
                containFlag = true;
                break;
            }
        }
        return containFlag;
    }
    public void __RemovePosData(SupportHangPointType supportHangPointType)
    {
        for (int i = pointPosData.Count - 1; i >= 0; i--)
        {
            if (pointPosData[i].supportHangPointType == supportHangPointType)
            {
                pointPosData.RemoveAt(i);
            }
        }
    }

    public void __RemovePosData(CharacterHangPointData data)
    {
        int findIndex = pointPosData.IndexOf(data);
        if (findIndex >= 0)
        {
            pointPosData.RemoveAt(findIndex);
        }
    }

    public static List<SupportHangPointType> GetSupportHangPointTypeByName(string locName, bool atLeastOne)
    {
        List<SupportHangPointType> shpTypeList = new List<SupportHangPointType>();
        Array enumValueArr = Enum.GetValues(typeof(SupportHangPointType));
        for (int i = 0, len = enumValueArr.Length; i < len; i++)
        {
            SupportHangPointType curShpType = (SupportHangPointType) enumValueArr.GetValue(i);
            string curLocName = ParticalCommonUtil.GetEnumNameAttributeValue(curShpType);
            
            if (curLocName.Equals(locName))
            {
                shpTypeList.Add(curShpType);
            }
        }

        if (shpTypeList.Count > 0)
        {
            return shpTypeList;
        }

        if (atLeastOne)
        {
            shpTypeList.Add(SupportHangPointType.GROUND_LOC);   
        }
        return shpTypeList;
    }

    public static CharacterHangPointData GetHangPointData(GameObject go, SupportHangPointType supportHangPointType, string actionLabel = null, float starHeroModelScaleRate = 1.0f)
    {
        if (go == null)
            return new CharacterHangPointData();
        CharacterHangPoint hangPoint = go.GetComponent<CharacterHangPoint>();
        if (hangPoint != null)
        {
            return hangPoint.GetPosData(supportHangPointType, actionLabel, starHeroModelScaleRate);
        }
        return new CharacterHangPointData(go.transform);
    }
    #endregion

    #region 挂点编辑器下的操作接口
    
#if !ENABLE_TYPE_TREE_IGNORE
    // 编辑用骨骼点
    [System.NonSerialized]
    public List<GameObjectInfo> points = new List<GameObjectInfo>();
#endif
#if UNITY_EDITOR && !ENABLE_TYPE_TREE_IGNORE
    // 显示红球
    private void OnDrawGizmos()
    {
        float radius = 0.1f;
        for (int i = 0; i < points.Count; i++)
        {
            GameObject bone = points[i].gameObject;
            if (bone == null)
            {
                //Log.infoError("OnDrawGizmos：bone 为空");
                continue;
            }
            Gizmos.color = Color.red;
            Vector3 pos = bone.transform.position;

            Gizmos.DrawSphere(pos, radius);
        }
    }
#endif
    
    [Serializable]
    public class GameObjectInfo
    {
        public GameObject gameObject;
        public SupportHangPointType supportHangPointType = SupportHangPointType.GROUND_LOC;
        public bool isManualCreate = false;
        
        #region isTransformDataFromAnimation true才用到;

        public bool isTransformDataFromAnimation = false;
        public PointDataTransform pointDataTransAtScale1 = new PointDataTransform();
        public string dependAnimationName;
        public int dependAnimationNameIndex_Editor;
        public float dependAnimationSampleTimeS;
        public float depengAnimationDurationS;
        public float depengAnimationFrameRateEditor;
        public string dependActionLabel;
        #endregion
    }


    #endregion

    /// <summary>
    /// 被放置在格子上【操作放置】
    /// </summary>
    public void OnPlaceTile()
    {
        //
    }
}

[Serializable]
public class CharacterStarLevelData
{
    // public float scaleRate = 1.0f;
    public CharacterHangPoint.SupportHangPointType supportHangPointType = CharacterHangPoint.SupportHangPointType.GROUND_LOC;
    public GameObject prefab;
}

[Serializable]
public class CharacterSystemScaleData
{
    public float scaleRate = 1.0f;
    public CharacterHangPoint.SupportSystemType supportSystemType = CharacterHangPoint.SupportSystemType.RECRUIT;
}

[Serializable]
public class CharacterInitEffectData {
    public CharacterHangPoint.SupportHangPointType supportHangPointType = CharacterHangPoint.SupportHangPointType.GROUND_LOC;
    public GameObject prefab;
    //是否需要随着骨骼一起旋转;
    public bool needRotateWithBone = true;
    //是否需要适配角色的缩放;
    public bool needScaleWithCharacter = true;
    //不显示特效的动作(分号;分割)
    public string hideAction = "";
}

[Serializable]
public class CharacterActionPartMaterial
{
    public string actionLabel;
    public CharacterHangPoint.SupportActionPart actionPart = CharacterHangPoint.SupportActionPart.BODY;
    public float duration;
    public Material material;
    public List<CharacterActionMaterialPropertyFloat> floatValueList;
}

[Serializable]
public class CharacterActionMaterialPropertyFloat
{
    public string materialPropertyName; //材质的属性名字;
    public float fromValue;
    public float toValue;
}

[Serializable]
public class PointDataTransform
{
    public Vector3 pos;
    public Vector3 scale;
    public Vector3 eulerAngles;
}

[Serializable]
public class HangPointReflect
{
    public CharacterHangPoint.SupportHangPointType firstChoice;
    public CharacterHangPoint.SupportHangPointType secondChoice;

    public HangPointReflect(CharacterHangPoint.SupportHangPointType firstChoice, CharacterHangPoint.SupportHangPointType secondChoice)
    {
        this.firstChoice = firstChoice;
        this.secondChoice = secondChoice;
    }
}

// 位置数据
[Serializable]
public class CharacterHangPointData
{
    public Vector3 pos;

    public Vector3 scale;
    
    public Vector3 eulerAngles;
    //[HideInInspector]
    public Transform bindTrans;
    //[HideInInspector]
    public CharacterHangPoint.SupportHangPointType supportHangPointType;
    [Header("是否忽略高度")]
    public bool IsIgnoreHeight = false;
    public bool isTransformDataFromAnimation = false;
    [Header("是否相对于父节点【特指 ground loc之类】")]
    public bool IsRelativeParent = false;

    #region isTransformDataFromAnimation true才用到;
    public PointDataTransform pointDataTransAtScale1 = new PointDataTransform();
    public string dependAnimationName = "";
    [NonSerialized]
    public int dependAnimationNameIndex_Editor = -1;
    public float dependAnimationSampleTimeS;
    public float depengAnimationDurationS;
    public float depengAnimationFrameRateEditor;
    #endregion
    public float starHeroModelScaleRate = 1.0f;
    public Vector3 groundWorldPos = Vector3.zero;
    private static Vector3 vec3Zero = Vector3.zero;
    public string _dependActionLabel = "";
 
    //依赖的动作标签;
    public string dependActionlabel
    {
        get
        {
            if (string.IsNullOrEmpty(_dependActionLabel))
            {
                return dependAnimationName;
            }
            return _dependActionLabel;
        }
        set { _dependActionLabel = value; }
    }

    public CharacterHangPointData Clone(){
        CharacterHangPointData newData = new CharacterHangPointData();
        newData.pos = new UnityEngine.Vector3(this.pos.x, this.pos.y, this.pos.z);//this.pos.Clone();
        newData.scale = new UnityEngine.Vector3(this.scale.x, this.scale.y, this.scale.z);//this.scale.Clone();
        newData.eulerAngles = new UnityEngine.Vector3(this.eulerAngles.x, this.eulerAngles.y, this.eulerAngles.z);//this.eulerAngles.Clone();
        newData.bindTrans = this.bindTrans;
        newData.supportHangPointType = this.supportHangPointType;
        newData.IsIgnoreHeight = this.IsIgnoreHeight;
        newData.IsRelativeParent = this.IsRelativeParent;
        newData.pointDataTransAtScale1 = this.pointDataTransAtScale1;
        newData.isTransformDataFromAnimation = this.isTransformDataFromAnimation;
        newData.dependAnimationName = this.dependAnimationName;
        newData.dependAnimationNameIndex_Editor = this.dependAnimationNameIndex_Editor;
        newData.dependAnimationSampleTimeS = this.dependAnimationSampleTimeS;
        newData.depengAnimationDurationS = this.depengAnimationDurationS;
        newData.depengAnimationFrameRateEditor = this.depengAnimationFrameRateEditor;
        newData.starHeroModelScaleRate = this.starHeroModelScaleRate;
        newData.groundWorldPos = this.groundWorldPos;
        newData.dependActionlabel = this.dependActionlabel;
        return newData;
    }
    
    public CharacterHangPointData()
    {

    }

    public CharacterHangPointData(Transform bindTrans)
    {
        this.bindTrans = bindTrans;
        //this.bindTrans = null;
        this.supportHangPointType = CharacterHangPoint.SupportHangPointType.GROUND_LOC;
        pos = eulerAngles = Vector3.zero;
        scale = Vector3.one;
    }

    public CharacterHangPointData(Transform bindTrans, Vector3 pos, Vector3 scale, Vector3 eulerAngles, CharacterHangPoint.SupportHangPointType supportHangPointType)
    {
        this.bindTrans = bindTrans;
        //this.bindTrans = null;
        this.pos = pos;
        this.scale = scale;
        this.eulerAngles = eulerAngles;
        this.supportHangPointType = supportHangPointType;
    }
    public CharacterHangPointData(CharacterHangPointData pointdata)
    {
        this.bindTrans = pointdata.bindTrans;
        //this.bindTrans = null;
        this.pos = pointdata.pos;
        this.scale = pointdata.scale;
        this.eulerAngles = pointdata.eulerAngles;
        this.supportHangPointType = pointdata.supportHangPointType;
    }

    //因为挂点是纯显示用途，和逻辑无关，直接返回Vec3即可;
    public Vector3 GetPos(CharacterHangPoint chp)
    {
        if (bindTrans != null)
        {
            Vector3 worldPos;
            if (this.isTransformDataFromAnimation && chp != null)
            {
                Transform viewTrans = chp.transform;
                // Vector3 viewTransPlancePos = viewTrans.position;
                // viewTransPlancePos.y = SimplePhysicsManager.PlanePos.ToVector3().y;
                Vector3 scaleRatePos = pointDataTransAtScale1.pos * starHeroModelScaleRate;
                Vector3 convertPos = scaleRatePos + groundWorldPos;
                //跟随英雄的旋转;
                Vector3 viewUnitLocalEuler = new Vector3(0, viewTrans.eulerAngles.y, 0);
                worldPos = RotatePointAroundPivot(convertPos, groundWorldPos, viewUnitLocalEuler);
            }
            else
            {
                Vector3 bindPostion;

                //是否相对于父物体
                if (!this.IsRelativeParent)
                {
                    bindPostion = bindTrans.position;
                }
                else
                {
                    if(chp.transform.parent != null)
                        bindPostion = chp.transform.parent.position;
                    else
                    {
                        bindPostion = bindTrans.position;
                    }
                }
                
                
                if (!pos.Equals(vec3Zero))
                {
                    if (chp != null)
                    {
                        Transform viewTrans = chp.transform;
                        Vector3 convertPos = pos + bindPostion;
                        //跟随英雄的旋转;
                        Vector3 viewUnitLocalEuler = new Vector3(0, viewTrans.eulerAngles.y, 0);           
                        worldPos = RotatePointAroundPivot(convertPos, bindPostion, viewUnitLocalEuler);                        
                    }
                    else
                    {
                        worldPos = pos + bindPostion;
                    }
                }
                else
                {
                    worldPos = bindPostion;    
                }
            }
            if(this.IsIgnoreHeight)
            {
                worldPos.y = 0f;//SimplePhysicsManager.PlanePos.ToVector3().y;//PlanePos这里没有赋值
            }
            return worldPos;
        }
        return pos;
    }

    public Vector3 GetLocalPos(CharacterHangPoint chp)
    {
        if (bindTrans != null)
        {
            return pos + bindTrans.localPosition;
        }
        return pos;
    }


    public Transform GetBindTransform(CharacterHangPoint chp)
    {
        if (this.IsRelativeParent)
        {
            return chp.transform.parent;
        }
        return bindTrans;

    }
    
    public Vector3 RotatePointAroundPivot(Vector3 point, Vector3 pivot, Vector3 angles) {
        return Quaternion.Euler(angles) * (point - pivot) + pivot;
    }
#endif
}
