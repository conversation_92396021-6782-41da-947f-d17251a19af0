using System.Collections.Generic;
using Lucifer.ActCore;
using ProtoBuf;

[System.Serializable]
[ProtoContract(ImplicitFields = ImplicitFields.AllPublic)]
public class ServerActionConfigData
#if ACGGAME_CLIENT
          : UnityEngine.ScriptableObject
#endif
{
    //所有的角色
    public ServerCharacterConfigData[] AllCharacters;
    //所有的飞行道具
    public ServerProjectileConfigData[] AllProjectiles;
}

[System.Serializable]
[ProtoContract(ImplicitFields = ImplicitFields.AllPublic)]
public class ServerCharacterConfigData
{
    public string name;
    public string ConfigName;
    public int runSpeed = 180;
    public int walkSpeed = 120;
    public int fastRunSpeed = 200;
    public int timeStampFrame = 30;
    public int initStandEulerY = 45;
    public int initRightNowStandEulerY = 45;
    public bool IsIndependentChessGrid = false;
    public string tag = null;
    public string ActionConfigPath;
    public string ActionConfigPath_Pre;
    public string HitDataConfigPath;
    public string HitDataConfigPath_Pre;
}

[System.Serializable]
[ProtoContract(ImplicitFields = ImplicitFields.AllPublic)]
public class ServerProjectileConfigData
{
    public string name;
    public string id;
    public int hitLife = 1;
    public float Life_Time_Max;
    public float Life_Time_Min;
    public float lifeTime = 1;
    public string ConfigName;//
    public string effectName;
    public string opponentEffectName;
    public float MoveMilesLife = 0;
    public bool NeedShowAgain = false;
    public bool NeddPlayAnimation = false;
    public bool FollowRotation = true;
    public bool NeedNotifyVar = false;
    public bool OutOfBattleFiledAutoDestory = true;
    public string tag = null;//
    public string ActionConfigPath;//
    public string ActionConfigPath_Pre;//

    //for server  for multi thread
    public void FillProjectilConfig(Lucifer.ActCore.ProjectileConfig projectileConfig)
    {
        projectileConfig.name = ConfigName;
        projectileConfig.id = id;
        projectileConfig.hitLife = hitLife;
        projectileConfig.Life_Time_Min = Life_Time_Min;
        projectileConfig.Life_Time_Max = Life_Time_Max;
        projectileConfig.lifeTime = lifeTime;
        projectileConfig.effectName = effectName;
        projectileConfig.opponentEffectName = opponentEffectName;
        projectileConfig.MoveMilesLife = MoveMilesLife;
        projectileConfig.NeedShowAgain = NeedShowAgain;
        projectileConfig.NeddPlayAnimation = NeddPlayAnimation;
        projectileConfig.FollowRotation = FollowRotation;
        projectileConfig.NeedNotifyVar = NeedNotifyVar;
        projectileConfig.OutOfBattleFiledAutoDestory = OutOfBattleFiledAutoDestory;
    }
}

