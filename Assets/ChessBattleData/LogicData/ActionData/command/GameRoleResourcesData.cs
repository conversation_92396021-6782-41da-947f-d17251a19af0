using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using ProtoBuf;

[System.Serializable]
[ProtoContract(SkipConstructor = true)]
public class GameRoleResourcesData
#if ACGGAME_CLIENT && !LOGIC_THREAD
: ScriptableObject
#endif
{
    [System.Serializable]
	public class RoleEffect_Data
	{
		public string packageName;

		public string effectName;

		public RoleEffect_Data(string packageName,string effectName)
		{
			this.packageName = packageName;
			this.effectName = effectName;
		}
    }

    [System.Serializable]
    public class RolePrejectile_Data
    {

        public string effectName;

        public RolePrejectile_Data(string modelName)
        {
            this.effectName = modelName;

        }
    }

    [System.Serializable]
	public class RolePreModel_Data
	{
		
		public string modelName;
		
		public RolePreModel_Data(string modelName)
		{
			this.modelName = modelName;
		
		}
	}

	[System.Serializable]
	public class RoleAnimation_Data
	{
		public string animationName;
		
		public string path;

        //res_animation_camera  res_animation_move res_animation
        public string clipType;

		public RoleAnimation_Data(string animationName,string path, string clipType)
		{
			this.animationName = animationName;
			this.path = path;
            this.clipType = clipType;

        }
	}
	
	[System.Serializable]
	[ProtoContract(ImplicitFields = ImplicitFields.AllPublic)]
	public class PrefabProtoData
	{
		public int protoId;
		public string prefabName;

		public PrefabProtoData()
		{
			
		}

		public PrefabProtoData(int protoId, string prefabName)
		{
			this.protoId = protoId;
			this.prefabName = prefabName;
		}
	}

    public List<RolePrejectile_Data> prejectile_Datas = null;
    public void addPrejectileRes(string prefabName)
    {
	    if(prejectile_Datas == null)
			prejectile_Datas = new List<RolePrejectile_Data>();
        RolePrejectile_Data data = new RolePrejectile_Data(prefabName);
        if (!prejectile_Datas.Contains(data))
        {
            prejectile_Datas.Add(data);
        }
    }


	public List<RoleEffect_Data> effects = null;

	public void AddEffect(string packageName,string effectName)
	{
		if (effects == null)
			effects = new List<RoleEffect_Data>();
		for (int i = 0,len = effects.Count; i < len; i++) 
		{
			if(effects[i].effectName == effectName)
			{
				return;
			}
		}

		effects.Add (new RoleEffect_Data (packageName, effectName));
	}

	public string prefaName;
	[ProtoMember(1)]
	public string perModel;
	[ProtoMember(2)]
	public List<PrefabProtoData> prePrefabProtoDatas = null;

    public void Awake()
    {
        if (TKFrame.StringUtil.UseStringIntern)
        {
	        if (effects == null)
		        return;
	        
	        for(int i = 0; i < effects.Count; i++)
	        {
		        if(effects[i] != null)
		        {
			        if(effects[i].packageName != null)
				        effects[i].packageName = string.Intern(effects[i].packageName);
			        if(effects[i].effectName != null)
				        effects[i].effectName = string.Intern(effects[i].effectName);
		        }
	        }
        }
    }

    //	[System.Serializable]
    //	public RolePreModel_Data[] preModels;
    public void AddModel(int protoId, string prefabName)
    {
	    if (prePrefabProtoDatas == null)
		    prePrefabProtoDatas = new List<PrefabProtoData>();
		foreach (var kv in prePrefabProtoDatas)
		{
			if (kv.prefabName == prefabName) return;
		}
		prePrefabProtoDatas.Add (new PrefabProtoData(protoId, prefabName));
	}

	public List<int> SummonIdList = null;
	
	public void AddSummonId(int heroId)
	{
		if (SummonIdList == null)
			SummonIdList = new List<int>();
		if (!SummonIdList.Contains(heroId))
		{
			SummonIdList.Add(heroId);
		}
	}


	public List<RoleAnimation_Data> animations = null;

	public void AddAnimation(string path,string clipName, string clipType)
	{
		if (animations == null)
			animations = new List<RoleAnimation_Data>();
		
        path = path.ToLower();
        clipName = clipName.ToLower();
        string[] pathSplits = path.Split ('/');

		for (int i = 0,len = animations.Count; i < len; i++) 
		{
			if(animations[i].animationName == clipName)
			{
				return;
			}
		}

		if(path.Contains(".FBX"))
		{
			path = pathSplits [pathSplits.Length - 2];// + "@" + clipName;
		}else
		{
			path = pathSplits [pathSplits.Length - 3];// + "@" + clipName;
		}


		animations.Add (new RoleAnimation_Data(clipName,path, clipType));
	}

	public void AddAnimation(string clipName,string clipType )
	{
        clipName = clipName.ToLower();
        
        if (animations == null)
	        animations = new List<RoleAnimation_Data>();

        for (int i = 0,len = animations.Count; i < len; i++) 
		{
			if(animations[i].animationName == clipName && animations[i].clipType == clipType)
			{
				return;
			}
		}
		string path = clipName.Split('@')[0];
        path = clipType + "/" + path;

        animations.Add (new RoleAnimation_Data(clipName,path, clipType));

	}
}
