using System;
#if !ACGGAME_CLIENT || LOGIC_THREAD
using UnityEngine4Server;
#else
using UnityEngine;
using GraphProcessor;
#endif
using System.Collections.Generic;
using ProtoBuf;

namespace Lucifer.ActCore
{
    
	//DataStructModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckSwitchData
	{
		
		[ProtoMember(1)]
		public CompairisonOperatorEnum  operator_1Enum   = CompairisonOperatorEnum.Enum_greaterThan;
		[ProtoMember(2)]
		public CompairisonOperatorEnum  operator_2Enum   = CompairisonOperatorEnum.Enum_greaterThan;
		[ProtoMember(3)]
		public string[]  successLinkList  ;
		[ProtoMember(4)]
		public string  value_1   = "";
		[ProtoMember(5)]
		public string  value_2   = "";

	}
	//DataStructModelEnd//DataStructModelStart
	[System.Serializable]
	public class DoubleCompreData
	{
		
		public CompairisonOperatorEnum  operator_1Enum   = CompairisonOperatorEnum.Enum_greaterThan;
		public AFCValue  value_1  ;
		public CompairisonOperatorEnum  operator_2Enum   = CompairisonOperatorEnum.Enum_greaterThan;
		public AFCValue  value_2  ;
		public string[]  successLinkList  ;

	}
	//DataStructModelEnd//DataStructModelStart
	[System.Serializable]
[ProtoContract]
	public class MultiCompreData
	{
		
		[ProtoMember(1)]
		public SingleCompreData[]  CompareList  ;
		[ProtoMember(2)]
		public string[]  successLinkList  ;

	}
	//DataStructModelEnd//DataStructModelStart
	[System.Serializable]
[ProtoContract]
	public class SingleCompreData
	{
		
		[ProtoMember(1)]
		public CompairisonOperatorEnum  operatorTypeEnum   = CompairisonOperatorEnum.Enum_greaterThan;
		[ProtoMember(2)]
		public AFCValue  value  ;

	}
	//DataStructModelEnd//DataStructModelStart
	[System.Serializable]
[ProtoContract]
	public class MotionAccelerationLink
	{
		
		[ProtoMember(1)]
		public Vector3  velocity  ;
		[ProtoMember(2)]
		public Vector3  AcceleratedVelocity  ;

	}
	//DataStructModelEnd//DataStructModelStart
	[System.Serializable]
[ProtoContract]
	public class SwitchBranchData
	{
		
		[ProtoMember(1)]
		public string[]  conditionList  ;
		[ProtoMember(2)]
		public string[]  successLinkList  ;

	}
	//DataStructModelEnd//DataStructModelStart
	[System.Serializable]
[ProtoContract]
	public class CommandActionObject
	{
		
		[ProtoMember(1)]
		public bool  isValueFromLink   = false;
		[ProtoMember(2)]
		public EffectTarget  TargetTypeEnum   = EffectTarget.Enum_self;
		[ProtoMember(3)]
		public string  valueLink   = "";

	}
	//DataStructModelEnd//DataStructModelStart
	[System.Serializable]
[ProtoContract]
	public class SummonHeroKuData
	{
		
		[ProtoMember(1)]
		public int  summonQuality   = 1;
		[ProtoMember(2)]
		public int  summonRate   = 0;
		[ProtoMember(3)]
		public int  summonStar   = 0;

	}
	//DataStructModelEnd  

	//TimeEventIndexModelStart
	[System.Serializable]
	[ProtoContract]
	public class TimeEventIndex
	{
	    [ProtoMember(1)]
		public float MoviceTimeLength;
		[ProtoMember(2)]
		[NoServerCfgLoaderDecode]
		public List<TimeEventProType> _eventList;
		#if UNITY_EDITOR || !ACGGAME_CLIENT
		public SetToActionFade[] _SetToActionFade;
		public ToAction[] _ToAction;
		public SetActive[] _SetActive;
		public BreakAction[] _BreakAction;
		public AddCancelPoint[] _AddCancelPoint;
		public CameraShake[] _CameraShake;
		public ChangeDirection[] _ChangeDirection;
		public AutoMoveDuration[] _AutoMoveDuration;
		public ControlCharacterMove[] _ControlCharacterMove;
		public MoveByRotation[] _MoveByRotation;
		public IsMyHero[] _IsMyHero;
		public Jump[] _Jump;
		public DefenseProperties[] _DefenseProperties;
		public AddEffect[] _AddEffect;
		public RemoveEffect[] _RemoveEffect;
		public AddUIEffect[] _AddUIEffect;
		public RemoveUIEffect[] _RemoveUIEffect;
		public AddBattleWarning[] _AddBattleWarning;
		public ProjectileControl[] _ProjectileControl;
		public BuffControlByID[] _BuffControlByID;
		public LaunchProjectile[] _LaunchProjectile;
		public RepeatLaunchProjectile[] _RepeatLaunchProjectile;
		public ChangeProjectileAroundRadius[] _ChangeProjectileAroundRadius;
		public ProjectileChangeView[] _ProjectileChangeView;
		public ControlProjectileView[] _ControlProjectileView;
		public ProjectileFlyBack[] _ProjectileFlyBack;
		public ChangeGravity[] _ChangeGravity;
		public AddShock[] _AddShock;
		public ActionData[] _ActionData;
		public AddComboRespond[] _AddComboRespond;
		public CheckKeyState[] _CheckKeyState;
		public Teleport[] _Teleport;
		public SummonCharacter[] _SummonCharacter;
		public DestroyMe[] _DestroyMe;
		public ConditionsLinker[] _ConditionsLinker;
		public JumpToTarget[] _JumpToTarget;
		public ResetActionState[] _ResetActionState;
		public CleanFightProtectState[] _CleanFightProtectState;
		public AddForceField[] _AddForceField;
		public SwitchTarget[] _SwitchTarget;
		public LucianSkill[] _LucianSkill;
		public LucianSkill_5_5[] _LucianSkill_5_5;
		public IsCanOverlap[] _IsCanOverlap;
		public MoveToTarget[] _MoveToTarget;
		public MoveWithAMT[] _MoveWithAMT;
		public MoveAroundTarget[] _MoveAroundTarget;
		public MoveBindTarget[] _MoveBindTarget;
		public FlyToTarget[] _FlyToTarget;
		public RunToTarget[] _RunToTarget;
		public FlyToTargetAdvance[] _FlyToTargetAdvance;
		public AddMoveMotion[] _AddMoveMotion;
		public AddViewMotion[] _AddViewMotion;
		public SetVariableValue[] _SetVariableValue;
		public SetVariableValueAdvance[] _SetVariableValueAdvance;
		public SetAnimatorVParameter[] _SetAnimatorVParameter;
		public SetAnimatorLayerWeight[] _SetAnimatorLayerWeight;
		public CheckVariableValue[] _CheckVariableValue;
		public SwitchVariableValue[] _SwitchVariableValue;
		public CameraCustomSetting[] _CameraCustomSetting;
		public CameraCustomSettingWithAnimation[] _CameraCustomSettingWithAnimation;
		public CameraDirectionSetting[] _CameraDirectionSetting;
		public ResumeCameraCustomSetting[] _ResumeCameraCustomSetting;
		public TurnRound[] _TurnRound;
		public GrabTarget[] _GrabTarget;
		public ThrowTarget[] _ThrowTarget;
		public ExecuteHit[] _ExecuteHit;
		public HitGroupControl[] _HitGroupControl;
		public EventActionData[] _EventActionData;
		public SendEvent[] _SendEvent;
		public SendEventToViewUnit[] _SendEventToViewUnit;
		public BulletTime[] _BulletTime;
		public EffectTweenControl[] _EffectTweenControl;
		public RoleTransform[] _RoleTransform;
		public ChangeWeapon[] _ChangeWeapon;
		public ShowOrHideBody[] _ShowOrHideBody;
		public EffectRadialBlur[] _EffectRadialBlur;
		public PlayWWiseSound[] _PlayWWiseSound;
		public StopWWiseSound[] _StopWWiseSound;
		public LayerCancelLevelLimit[] _LayerCancelLevelLimit;
		public ControlLock[] _ControlLock;
		public CheckKeystateChangeAngle[] _CheckKeystateChangeAngle;
		public CheckTime[] _CheckTime;
		public RandomNumber[] _RandomNumber;
		public StealthEffect[] _StealthEffect;
		public ChangeCollider[] _ChangeCollider;
		public ChangeColliderSelf[] _ChangeColliderSelf;
		public ChangeSmoothCollideRate[] _ChangeSmoothCollideRate;
		public SkillCostPoint[] _SkillCostPoint;
		public SkillEndNotify[] _SkillEndNotify;
		public SkillEndContinue[] _SkillEndContinue;
		public DoublecastTriggerNotify[] _DoublecastTriggerNotify;
		public NormalAttackNotify[] _NormalAttackNotify;
		public SkillBreakNotify[] _SkillBreakNotify;
		public PlayTurnActionTime[] _PlayTurnActionTime;
		public CheckDistance[] _CheckDistance;
		public CheckTargetState[] _CheckTargetState;
		public CheckSelfState[] _CheckSelfState;
		public CheckSkillGrabState[] _CheckSkillGrabState;
		public CheckGridIsEmpty[] _CheckGridIsEmpty;
		public GetCount[] _GetCount;
		public GetDistance[] _GetDistance;
		public GetRandomNumber[] _GetRandomNumber;
		public GetActionHitCount[] _GetActionHitCount;
		public GetCharacterAttribute[] _GetCharacterAttribute;
		public GetVariableValue[] _GetVariableValue;
		public GetSkillExcelTime[] _GetSkillExcelTime;
		public GetBulletCount[] _GetBulletCount;
		public MultiCompare[] _MultiCompare;
		public Compare[] _Compare;
		public CalculateInt[] _CalculateInt;
		public CalculateVector3[] _CalculateVector3;
		public GetRaycastGridSidePoint[] _GetRaycastGridSidePoint;
		public CheckSelfBuff[] _CheckSelfBuff;
		public CheckSelfEquipment[] _CheckSelfEquipment;
		public PlayBroke[] _PlayBroke;
		public SwitchBranch[] _SwitchBranch;
		public PlayDeadEffect[] _PlayDeadEffect;
		public EffectAfterImage[] _EffectAfterImage;
		public EffectLightBody[] _EffectLightBody;
		public RemoveLightBody[] _RemoveLightBody;
		public EffectAssassinEnterBattleTransparent[] _EffectAssassinEnterBattleTransparent;
		public EffectBodyGeneric[] _EffectBodyGeneric;
		public ChangeCameraDirection[] _ChangeCameraDirection;
		public ResetCameraToDefault[] _ResetCameraToDefault;
		public ResetEnemyUnlockTime[] _ResetEnemyUnlockTime;
		public PhoneShake[] _PhoneShake;
		public GetArrayUnit[] _GetArrayUnit;
		public GetArrayNext[] _GetArrayNext;
		public GetHeroStar[] _GetHeroStar;
		public GetSelfGameObject[] _GetSelfGameObject;
		public GetOwnerPos[] _GetOwnerPos;
		public GetBcCell[] _GetBcCell;
		public GetBcId[] _GetBcId;
		public GetBeGraber[] _GetBeGraber;
		public GetGraber[] _GetGraber;
		public IsMe[] _IsMe;
		public GetSkillTarget[] _GetSkillTarget;
		public GetCellPos[] _GetCellPos;
		public SetTarget[] _SetTarget;
		public SetSkillTarget[] _SetSkillTarget;
		public AutoReSetSkillTarget[] _AutoReSetSkillTarget;
		public FindTarget[] _FindTarget;
		public FindSkillTarget[] _FindSkillTarget;
		public FindCurrentSkillTarget[] _FindCurrentSkillTarget;
		public GetCurrentTarget[] _GetCurrentTarget;
		public FindAITarget[] _FindAITarget;
		public GetActionTriggerRole[] _GetActionTriggerRole;
		public GetHitRole[] _GetHitRole;
		public GetSummoner[] _GetSummoner;
		public GetRoleSpeed[] _GetRoleSpeed;
		public AddBuff[] _AddBuff;
		public RemoveBuff[] _RemoveBuff;
		public BatchLaunchProjectile[] _BatchLaunchProjectile;
		public TansheGroup[] _TansheGroup;
		public ProjectileChangeHitData[] _ProjectileChangeHitData;
		public HeroSelfChessDataClear[] _HeroSelfChessDataClear;
		public SetBuffStatus[] _SetBuffStatus;
		public SkillGridVertexClear[] _SkillGridVertexClear;
		public ProjectileSaveSelfChessDataGridVertex[] _ProjectileSaveSelfChessDataGridVertex;
		public ProjectileClearSelfChessDataGridVertex[] _ProjectileClearSelfChessDataGridVertex;
		public SwitchHeroStar[] _SwitchHeroStar;
		public GetBFProjectileGroupNum[] _GetBFProjectileGroupNum;
		public Add2BFProjectileGroup[] _Add2BFProjectileGroup;
		public RemoveBFProjectileGroup[] _RemoveBFProjectileGroup;
		public GetSkillExtendLevel[] _GetSkillExtendLevel;
		public GetBuffLayer[] _GetBuffLayer;
		public GetActiveFetter[] _GetActiveFetter;
		public IsHasBuffTag[] _IsHasBuffTag;
		public SummonWaitHeroEvent[] _SummonWaitHeroEvent;
		public ChangeAI[] _ChangeAI;
		public MultiExcuteEvent[] _MultiExcuteEvent;
		public GetRunningAIName[] _GetRunningAIName;
		public GetMergeHeroLevel[] _GetMergeHeroLevel;
		public ChangeColor[] _ChangeColor;
		public CheckFindTarget[] _CheckFindTarget;
		public SetSkillReleaseTagConst[] _SetSkillReleaseTagConst;
		public GetSkillReleaseTagConst[] _GetSkillReleaseTagConst;
		public StartSkillLock[] _StartSkillLock;
		public EndSkillLock[] _EndSkillLock;
		public ReleaseSkillTrigger[] _ReleaseSkillTrigger;
		public RefreshBattleFieldFetters[] _RefreshBattleFieldFetters;
		public ControlSkillTarget[] _ControlSkillTarget;
		public MoveToNearestGrid[] _MoveToNearestGrid;
		public ChangeActionSpeed[] _ChangeActionSpeed;
		public FindProjectileTarget[] _FindProjectileTarget;
		public GetProjectile[] _GetProjectile;
		public CheckIsCanKillTarget[] _CheckIsCanKillTarget;
		public CheckHeroIds[] _CheckHeroIds;
		public CheckNeighborHasTargets[] _CheckNeighborHasTargets;
		public GetTargetAttribute[] _GetTargetAttribute;
		public GetProjectileProperty[] _GetProjectileProperty;
		public SetProjectileProperty[] _SetProjectileProperty;
		public IsSkillTargetValid[] _IsSkillTargetValid;
		public IsTargetBcValid[] _IsTargetBcValid;
		public IsCellValid[] _IsCellValid;
		public CheckSummonState[] _CheckSummonState;
		public GetSummon[] _GetSummon;
		public CheckAttackDistance[] _CheckAttackDistance;
		public CheckIsHomeHero[] _CheckIsHomeHero;
		public GetBattleFieldState[] _GetBattleFieldState;
		public GetGridUnit[] _GetGridUnit;
		public CheckPosOutGrid[] _CheckPosOutGrid;
		public GetGeneric[] _GetGeneric;
		public CheckGeneric[] _CheckGeneric;
		public ExecuteGeneric[] _ExecuteGeneric;
		public ExecuteGenericView[] _ExecuteGenericView;
		public GetBattleRecordGridX[] _GetBattleRecordGridX;
		public GetBattleRecordGridY[] _GetBattleRecordGridY;
		public IsDebonairVIP[] _IsDebonairVIP;
		public GetResultBySymbol[] _GetResultBySymbol;
		public GetCelebrateEffect[] _GetCelebrateEffect;
		public GetSkillId_Zoe_S7[] _GetSkillId_Zoe_S7;
		public RefreshSkill_Zoe_S7[] _RefreshSkill_Zoe_S7;
		public DoViewContainerThing[] _DoViewContainerThing;
		public SetGridState[] _SetGridState;
		public GetUnitProperty[] _GetUnitProperty;
		public GetNewArray[] _GetNewArray;
		public WaitPosControl[] _WaitPosControl;
		
		 #endif

#if UNITY_EDITOR && !ENABLE_TYPE_TREE_IGNORE       

        [NonSerialized]
        public List<string> dirtyAttribute = new List<string>();
        
        public List< ActionModule> ActionModulesList = new List< ActionModule>();

        public ActionModule GetActionModule(string name)
        {
            for (int i = 0; i < ActionModulesList.Count; i++)
            {
                if (ActionModulesList[i].name == name)
                    return ActionModulesList[i];
            }

            return null;
        }
        
        
 #endif
 
		public void Init()
		{
#if UNITY_EDITOR || !ACGGAME_CLIENT
			if(_eventList == null)
				_eventList = new List<TimeEventProType>(64);
			else
				_eventList.Clear();
            //event_start
                AddToEventList(_SetToActionFade);
            //event_end//event_start
                AddToEventList(_ToAction);
            //event_end//event_start
                AddToEventList(_SetActive);
            //event_end//event_start
                AddToEventList(_BreakAction);
            //event_end//event_start
                AddToEventList(_AddCancelPoint);
            //event_end//event_start
                AddToEventList(_CameraShake);
            //event_end//event_start
                AddToEventList(_ChangeDirection);
            //event_end//event_start
                AddToEventList(_AutoMoveDuration);
            //event_end//event_start
                AddToEventList(_ControlCharacterMove);
            //event_end//event_start
                AddToEventList(_MoveByRotation);
            //event_end//event_start
                AddToEventList(_IsMyHero);
            //event_end//event_start
                AddToEventList(_Jump);
            //event_end//event_start
                AddToEventList(_DefenseProperties);
            //event_end//event_start
                AddToEventList(_AddEffect);
            //event_end//event_start
                AddToEventList(_RemoveEffect);
            //event_end//event_start
                AddToEventList(_AddUIEffect);
            //event_end//event_start
                AddToEventList(_RemoveUIEffect);
            //event_end//event_start
                AddToEventList(_AddBattleWarning);
            //event_end//event_start
                AddToEventList(_ProjectileControl);
            //event_end//event_start
                AddToEventList(_BuffControlByID);
            //event_end//event_start
                AddToEventList(_LaunchProjectile);
            //event_end//event_start
                AddToEventList(_RepeatLaunchProjectile);
            //event_end//event_start
                AddToEventList(_ChangeProjectileAroundRadius);
            //event_end//event_start
                AddToEventList(_ProjectileChangeView);
            //event_end//event_start
                AddToEventList(_ControlProjectileView);
            //event_end//event_start
                AddToEventList(_ProjectileFlyBack);
            //event_end//event_start
                AddToEventList(_ChangeGravity);
            //event_end//event_start
                AddToEventList(_AddShock);
            //event_end//event_start
                AddToEventList(_ActionData);
            //event_end//event_start
                AddToEventList(_AddComboRespond);
            //event_end//event_start
                AddToEventList(_CheckKeyState);
            //event_end//event_start
                AddToEventList(_Teleport);
            //event_end//event_start
                AddToEventList(_SummonCharacter);
            //event_end//event_start
                AddToEventList(_DestroyMe);
            //event_end//event_start
                AddToEventList(_ConditionsLinker);
            //event_end//event_start
                AddToEventList(_JumpToTarget);
            //event_end//event_start
                AddToEventList(_ResetActionState);
            //event_end//event_start
                AddToEventList(_CleanFightProtectState);
            //event_end//event_start
                AddToEventList(_AddForceField);
            //event_end//event_start
                AddToEventList(_SwitchTarget);
            //event_end//event_start
                AddToEventList(_LucianSkill);
            //event_end//event_start
                AddToEventList(_LucianSkill_5_5);
            //event_end//event_start
                AddToEventList(_IsCanOverlap);
            //event_end//event_start
                AddToEventList(_MoveToTarget);
            //event_end//event_start
                AddToEventList(_MoveWithAMT);
            //event_end//event_start
                AddToEventList(_MoveAroundTarget);
            //event_end//event_start
                AddToEventList(_MoveBindTarget);
            //event_end//event_start
                AddToEventList(_FlyToTarget);
            //event_end//event_start
                AddToEventList(_RunToTarget);
            //event_end//event_start
                AddToEventList(_FlyToTargetAdvance);
            //event_end//event_start
                AddToEventList(_AddMoveMotion);
            //event_end//event_start
                AddToEventList(_AddViewMotion);
            //event_end//event_start
                AddToEventList(_SetVariableValue);
            //event_end//event_start
                AddToEventList(_SetVariableValueAdvance);
            //event_end//event_start
                AddToEventList(_SetAnimatorVParameter);
            //event_end//event_start
                AddToEventList(_SetAnimatorLayerWeight);
            //event_end//event_start
                AddToEventList(_CheckVariableValue);
            //event_end//event_start
                AddToEventList(_SwitchVariableValue);
            //event_end//event_start
                AddToEventList(_CameraCustomSetting);
            //event_end//event_start
                AddToEventList(_CameraCustomSettingWithAnimation);
            //event_end//event_start
                AddToEventList(_CameraDirectionSetting);
            //event_end//event_start
                AddToEventList(_ResumeCameraCustomSetting);
            //event_end//event_start
                AddToEventList(_TurnRound);
            //event_end//event_start
                AddToEventList(_GrabTarget);
            //event_end//event_start
                AddToEventList(_ThrowTarget);
            //event_end//event_start
                AddToEventList(_ExecuteHit);
            //event_end//event_start
                AddToEventList(_HitGroupControl);
            //event_end//event_start
                AddToEventList(_EventActionData);
            //event_end//event_start
                AddToEventList(_SendEvent);
            //event_end//event_start
                AddToEventList(_SendEventToViewUnit);
            //event_end//event_start
                AddToEventList(_BulletTime);
            //event_end//event_start
                AddToEventList(_EffectTweenControl);
            //event_end//event_start
                AddToEventList(_RoleTransform);
            //event_end//event_start
                AddToEventList(_ChangeWeapon);
            //event_end//event_start
                AddToEventList(_ShowOrHideBody);
            //event_end//event_start
                AddToEventList(_EffectRadialBlur);
            //event_end//event_start
                AddToEventList(_PlayWWiseSound);
            //event_end//event_start
                AddToEventList(_StopWWiseSound);
            //event_end//event_start
                AddToEventList(_LayerCancelLevelLimit);
            //event_end//event_start
                AddToEventList(_ControlLock);
            //event_end//event_start
                AddToEventList(_CheckKeystateChangeAngle);
            //event_end//event_start
                AddToEventList(_CheckTime);
            //event_end//event_start
                AddToEventList(_RandomNumber);
            //event_end//event_start
                AddToEventList(_StealthEffect);
            //event_end//event_start
                AddToEventList(_ChangeCollider);
            //event_end//event_start
                AddToEventList(_ChangeColliderSelf);
            //event_end//event_start
                AddToEventList(_ChangeSmoothCollideRate);
            //event_end//event_start
                AddToEventList(_SkillCostPoint);
            //event_end//event_start
                AddToEventList(_SkillEndNotify);
            //event_end//event_start
                AddToEventList(_SkillEndContinue);
            //event_end//event_start
                AddToEventList(_DoublecastTriggerNotify);
            //event_end//event_start
                AddToEventList(_NormalAttackNotify);
            //event_end//event_start
                AddToEventList(_SkillBreakNotify);
            //event_end//event_start
                AddToEventList(_PlayTurnActionTime);
            //event_end//event_start
                AddToEventList(_CheckDistance);
            //event_end//event_start
                AddToEventList(_CheckTargetState);
            //event_end//event_start
                AddToEventList(_CheckSelfState);
            //event_end//event_start
                AddToEventList(_CheckSkillGrabState);
            //event_end//event_start
                AddToEventList(_CheckGridIsEmpty);
            //event_end//event_start
                AddToEventList(_GetCount);
            //event_end//event_start
                AddToEventList(_GetDistance);
            //event_end//event_start
                AddToEventList(_GetRandomNumber);
            //event_end//event_start
                AddToEventList(_GetActionHitCount);
            //event_end//event_start
                AddToEventList(_GetCharacterAttribute);
            //event_end//event_start
                AddToEventList(_GetVariableValue);
            //event_end//event_start
                AddToEventList(_GetSkillExcelTime);
            //event_end//event_start
                AddToEventList(_GetBulletCount);
            //event_end//event_start
                AddToEventList(_MultiCompare);
            //event_end//event_start
                AddToEventList(_Compare);
            //event_end//event_start
                AddToEventList(_CalculateInt);
            //event_end//event_start
                AddToEventList(_CalculateVector3);
            //event_end//event_start
                AddToEventList(_GetRaycastGridSidePoint);
            //event_end//event_start
                AddToEventList(_CheckSelfBuff);
            //event_end//event_start
                AddToEventList(_CheckSelfEquipment);
            //event_end//event_start
                AddToEventList(_PlayBroke);
            //event_end//event_start
                AddToEventList(_SwitchBranch);
            //event_end//event_start
                AddToEventList(_PlayDeadEffect);
            //event_end//event_start
                AddToEventList(_EffectAfterImage);
            //event_end//event_start
                AddToEventList(_EffectLightBody);
            //event_end//event_start
                AddToEventList(_RemoveLightBody);
            //event_end//event_start
                AddToEventList(_EffectAssassinEnterBattleTransparent);
            //event_end//event_start
                AddToEventList(_EffectBodyGeneric);
            //event_end//event_start
                AddToEventList(_ChangeCameraDirection);
            //event_end//event_start
                AddToEventList(_ResetCameraToDefault);
            //event_end//event_start
                AddToEventList(_ResetEnemyUnlockTime);
            //event_end//event_start
                AddToEventList(_PhoneShake);
            //event_end//event_start
                AddToEventList(_GetArrayUnit);
            //event_end//event_start
                AddToEventList(_GetArrayNext);
            //event_end//event_start
                AddToEventList(_GetHeroStar);
            //event_end//event_start
                AddToEventList(_GetSelfGameObject);
            //event_end//event_start
                AddToEventList(_GetOwnerPos);
            //event_end//event_start
                AddToEventList(_GetBcCell);
            //event_end//event_start
                AddToEventList(_GetBcId);
            //event_end//event_start
                AddToEventList(_GetBeGraber);
            //event_end//event_start
                AddToEventList(_GetGraber);
            //event_end//event_start
                AddToEventList(_IsMe);
            //event_end//event_start
                AddToEventList(_GetSkillTarget);
            //event_end//event_start
                AddToEventList(_GetCellPos);
            //event_end//event_start
                AddToEventList(_SetTarget);
            //event_end//event_start
                AddToEventList(_SetSkillTarget);
            //event_end//event_start
                AddToEventList(_AutoReSetSkillTarget);
            //event_end//event_start
                AddToEventList(_FindTarget);
            //event_end//event_start
                AddToEventList(_FindSkillTarget);
            //event_end//event_start
                AddToEventList(_FindCurrentSkillTarget);
            //event_end//event_start
                AddToEventList(_GetCurrentTarget);
            //event_end//event_start
                AddToEventList(_FindAITarget);
            //event_end//event_start
                AddToEventList(_GetActionTriggerRole);
            //event_end//event_start
                AddToEventList(_GetHitRole);
            //event_end//event_start
                AddToEventList(_GetSummoner);
            //event_end//event_start
                AddToEventList(_GetRoleSpeed);
            //event_end//event_start
                AddToEventList(_AddBuff);
            //event_end//event_start
                AddToEventList(_RemoveBuff);
            //event_end//event_start
                AddToEventList(_BatchLaunchProjectile);
            //event_end//event_start
                AddToEventList(_TansheGroup);
            //event_end//event_start
                AddToEventList(_ProjectileChangeHitData);
            //event_end//event_start
                AddToEventList(_HeroSelfChessDataClear);
            //event_end//event_start
                AddToEventList(_SetBuffStatus);
            //event_end//event_start
                AddToEventList(_SkillGridVertexClear);
            //event_end//event_start
                AddToEventList(_ProjectileSaveSelfChessDataGridVertex);
            //event_end//event_start
                AddToEventList(_ProjectileClearSelfChessDataGridVertex);
            //event_end//event_start
                AddToEventList(_SwitchHeroStar);
            //event_end//event_start
                AddToEventList(_GetBFProjectileGroupNum);
            //event_end//event_start
                AddToEventList(_Add2BFProjectileGroup);
            //event_end//event_start
                AddToEventList(_RemoveBFProjectileGroup);
            //event_end//event_start
                AddToEventList(_GetSkillExtendLevel);
            //event_end//event_start
                AddToEventList(_GetBuffLayer);
            //event_end//event_start
                AddToEventList(_GetActiveFetter);
            //event_end//event_start
                AddToEventList(_IsHasBuffTag);
            //event_end//event_start
                AddToEventList(_SummonWaitHeroEvent);
            //event_end//event_start
                AddToEventList(_ChangeAI);
            //event_end//event_start
                AddToEventList(_MultiExcuteEvent);
            //event_end//event_start
                AddToEventList(_GetRunningAIName);
            //event_end//event_start
                AddToEventList(_GetMergeHeroLevel);
            //event_end//event_start
                AddToEventList(_ChangeColor);
            //event_end//event_start
                AddToEventList(_CheckFindTarget);
            //event_end//event_start
                AddToEventList(_SetSkillReleaseTagConst);
            //event_end//event_start
                AddToEventList(_GetSkillReleaseTagConst);
            //event_end//event_start
                AddToEventList(_StartSkillLock);
            //event_end//event_start
                AddToEventList(_EndSkillLock);
            //event_end//event_start
                AddToEventList(_ReleaseSkillTrigger);
            //event_end//event_start
                AddToEventList(_RefreshBattleFieldFetters);
            //event_end//event_start
                AddToEventList(_ControlSkillTarget);
            //event_end//event_start
                AddToEventList(_MoveToNearestGrid);
            //event_end//event_start
                AddToEventList(_ChangeActionSpeed);
            //event_end//event_start
                AddToEventList(_FindProjectileTarget);
            //event_end//event_start
                AddToEventList(_GetProjectile);
            //event_end//event_start
                AddToEventList(_CheckIsCanKillTarget);
            //event_end//event_start
                AddToEventList(_CheckHeroIds);
            //event_end//event_start
                AddToEventList(_CheckNeighborHasTargets);
            //event_end//event_start
                AddToEventList(_GetTargetAttribute);
            //event_end//event_start
                AddToEventList(_GetProjectileProperty);
            //event_end//event_start
                AddToEventList(_SetProjectileProperty);
            //event_end//event_start
                AddToEventList(_IsSkillTargetValid);
            //event_end//event_start
                AddToEventList(_IsTargetBcValid);
            //event_end//event_start
                AddToEventList(_IsCellValid);
            //event_end//event_start
                AddToEventList(_CheckSummonState);
            //event_end//event_start
                AddToEventList(_GetSummon);
            //event_end//event_start
                AddToEventList(_CheckAttackDistance);
            //event_end//event_start
                AddToEventList(_CheckIsHomeHero);
            //event_end//event_start
                AddToEventList(_GetBattleFieldState);
            //event_end//event_start
                AddToEventList(_GetGridUnit);
            //event_end//event_start
                AddToEventList(_CheckPosOutGrid);
            //event_end//event_start
                AddToEventList(_GetGeneric);
            //event_end//event_start
                AddToEventList(_CheckGeneric);
            //event_end//event_start
                AddToEventList(_ExecuteGeneric);
            //event_end//event_start
                AddToEventList(_ExecuteGenericView);
            //event_end//event_start
                AddToEventList(_GetBattleRecordGridX);
            //event_end//event_start
                AddToEventList(_GetBattleRecordGridY);
            //event_end//event_start
                AddToEventList(_IsDebonairVIP);
            //event_end//event_start
                AddToEventList(_GetResultBySymbol);
            //event_end//event_start
                AddToEventList(_GetCelebrateEffect);
            //event_end//event_start
                AddToEventList(_GetSkillId_Zoe_S7);
            //event_end//event_start
                AddToEventList(_RefreshSkill_Zoe_S7);
            //event_end//event_start
                AddToEventList(_DoViewContainerThing);
            //event_end//event_start
                AddToEventList(_SetGridState);
            //event_end//event_start
                AddToEventList(_GetUnitProperty);
            //event_end//event_start
                AddToEventList(_GetNewArray);
            //event_end//event_start
                AddToEventList(_WaitPosControl);
            //event_end

            SortPositionId sotp = new SortPositionId();
            _eventList.Sort(sotp);
            
#if UNITY_EDITOR && !ENABLE_TYPE_TREE_IGNORE
            //ActionModulesList.Clear();
            if (ActionModulesList == null)
                ActionModulesList = new List<ActionModule>();
            if ( ActionModulesList.Count == 0) 
            {
                //ActionModulesList = new TKDictionary<string, ActionModule>();
                
                ActionModulesList.Add(new ActionModule(ActionModule.Default_Module,""));
            }
            else
            {
                for (int i = 0; i < ActionModulesList.Count; i++)
                {
                    if(ActionModulesList[i].ModuleEventList != null)
                        ActionModulesList[i].ModuleEventList.Clear();
                    else
                    {
                        
                        ActionModulesList[i].ModuleEventList = new List<TimeEventProType>();
                    }
                }
            }
            
            //if (ModuleEventList == null)
                //ModuleEventList = new List<List<TimeEventProType>>();
            //ModuleEventList.Clear();
            for (int i = 0, n = _eventList.Count; i < n; i++)
            {
                TimeEventProType atInfo = _eventList[i];
#if UNITY_EDITOR && ACGGAME_CLIENT
                if (atInfo.Description != null)
                {
                    atInfo.Description.sortNum = 0;
                } 
#endif
                atInfo.index = i;

                if (string.IsNullOrEmpty(atInfo.moduleName) || atInfo.moduleName == ActionModule.Default_Module)
                {

                    ActionModule actionModule = GetActionModule(ActionModule.Default_Module);
                    if(!actionModule.ModuleEventList.Contains(atInfo))
                        actionModule.ModuleEventList.Add(atInfo);
                    // ModuleEventList.Add(new List<TimeEventProType>());
                    // ModuleEventList[ModuleEventList.Count - 1].Add(atInfo);
                }
                else
                {
                    ActionModule actionModule = GetActionModule(atInfo.moduleName);
                    if (actionModule == null)
                    {
                        actionModule = new ActionModule(atInfo.moduleName, atInfo.ModuleDesc);
                        ActionModulesList.Add(actionModule);
                    }
                    
                    if(!actionModule.ModuleEventList.Contains(atInfo))
                        actionModule.ModuleEventList.Add(atInfo);
                }
            }
#endif             
#endif
		}
 
		public List<TimeEventProType> eventList 
		{ 
			get 
			{ 
				if(_eventList == null || _eventList.Count == 0)
				{
					Init();					
				}

				return _eventList;
			}
			set 
			{ 
				_eventList = value;
			} 
		}		

		private void AddToEventList(TimeEventProType[] list)
        {
            if(list != null && list.Length > 0)
            {
                _eventList.AddRange(list);
            }
        }
	}	
	//TimeEventIndexModelEnd

	//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SetToActionFade : TimeEventProType
	{
		
		[ProtoMember(1)]
		public float  crossFadeTime   = 0.0f;
		[ProtoMember(2)]
		public string  actionName   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ToAction : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  startFrame   = 0;
		[ProtoMember(2)]
		public bool  checkSkill   = false;
		[ProtoMember(3)]
		public bool  useCommonAction   = false;
		[ProtoMember(4)]
		public CommandActionObject  actionObject  ;
		[ProtoMember(5)]
		public string  actionName   = "";
		[ProtoMember(6)]
		public string  checkSkillFailActionName   = "";
		[ProtoMember(7)]
		public  bool  multiUnit   = false;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SetActive : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  active   = -1;
		[ProtoMember(2)]
		public int  openAI   = -1;
		[ProtoMember(3)]
		public int  openBuffUnit   = -1;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class BreakAction : TimeEventProType
	{
		
		[ProtoMember(1)]
		public CommandActionObject  actionObject  ;
		[ProtoMember(2)]
		public ActionLayerName  layerNameEnum   = ActionLayerName.Enum_Current;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class AddCancelPoint : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  cancelLevel   = 0;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CameraShake : TimeEventProType
	{
		
		[ProtoMember(1)]
		public string  key   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ChangeDirection : TimeEventProType
	{
		
		[ProtoMember(1)]
		public float  angleLimit   = 360f;
		[ProtoMember(2)]
		public float  angle   = 0f;
		[ProtoMember(3)]
		public bool  immediate   = false;
		[ProtoMember(4)]
		public ChangeDirectionType  changeTypeEnum   = ChangeDirectionType.Enum_targetFirst;
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = true)]
#endif
		[ProtoMember(5)]
		public string  customTargetLink   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class AutoMoveDuration : TimeEventProType
	{
		
		[ProtoMember(1)]
		public float  speedX   = 0.0f;
		[ProtoMember(2)]
		public float  speedZ   = 0.0f;
		[ProtoMember(3)]
		public float  accelerationX   = 0.0f;
		[ProtoMember(4)]
		public float  accelerationZ   = 0.0f;
		[ProtoMember(5)]
		public float  rate   = 1.0f;
		[ProtoMember(6)]
		public bool  speedForwadByDirection   = true;
		[ProtoMember(7)]
		public bool  useSpeedRate   = false;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ControlCharacterMove : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  lockLimitDistance   = 0;
		[ProtoMember(2)]
		public float  speedForwad   = 0.0f;
		[ProtoMember(3)]
		public float  speedSide   = 0.0f;
		[ProtoMember(4)]
		public float  speedBack   = 0.0f;
		[ProtoMember(5)]
		public float  angleLimit   = 360f;
		[ProtoMember(6)]
		public bool  useSpeedRate   = false;
		[ProtoMember(7)]
		public MoveSpeedTypeEnum  speedTypeEnum   = MoveSpeedTypeEnum.Enum_WalkSpeed;
		[ProtoMember(8)]
		public BlendTypeEnum  BlendTypeEnum   = BlendTypeEnum.Enum_Additive;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class MoveByRotation : TimeEventProType
	{
		
		[ProtoMember(1)]
		public Vector3  targetPos  ;
		[ProtoMember(2)]
		public int  speed   = 0;
		[ProtoMember(3)]
		public float  speedRotation   = 0.0f;
		[ProtoMember(4)]
		public float  faceRotation   = 0.0f;
		[ProtoMember(5)]
		public float  stopTime   = 0.0f;
		[ProtoMember(6)]
		public bool  isMoveByTarget   = false;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class IsMyHero : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class Jump : TimeEventProType
	{
		
		[ProtoMember(1)]
		public float  powerRate   = 0.0f;
		[ProtoMember(2)]
		public string  riseType   = "jump";
		[ProtoMember(3)]
		public string  riseLoopType   = "jumpriseloop";
		[ProtoMember(4)]
		public string  fallType   = "jumpfall";
		[ProtoMember(5)]
		public string  fallLoopType   = "jumpfallloop";
		[ProtoMember(6)]
		public string  toGroundType   = "jumpland";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class DefenseProperties : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  defendLevel   = 0;
		[ProtoMember(2)]
		public int  defendAngle   = 0;
		[ProtoMember(3)]
		public int  defendAngleRange   = 0;
		[ProtoMember(4)]
		public int  defendHurtPercent   = 0;
		[ProtoMember(5)]
		public int  defendHitbackPercent   = 100;
		[ProtoMember(6)]
		public CharacterHangPoint.SupportHangPointType  hangPoint   = CharacterHangPoint.SupportHangPointType.CUSTOM_LOC;
		[ProtoMember(7)]
		public BeatenMode  beatenModeEnum   = BeatenMode.Enum_normal;
		[ProtoMember(8)]
		public string  defendEffect   = "";
		[ProtoMember(9)]
		public string  defendSound   = "";
		[ProtoMember(10)]
		public string  defendHurtAction   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class AddEffect : TimeEventProType
	{
		
		[ProtoMember(1)]
		public Vector3  position  ;
		[ProtoMember(2)]
		public Vector3  scale  ;
		[ProtoMember(3)]
		public Vector3  rotation  ;
		[ProtoMember(4)]
		public float  effectPlayspeed   = -1f;
		[ProtoMember(5)]
		public float  saveTime   = 0.0f;
		[ProtoMember(6)]
		public float  liveTime   = 0.0f;
		[ProtoMember(7)]
		public bool  bindBone   = false;
		[ProtoMember(8)]
		public bool  isCasterUseCurrentModule   = false;
		[ProtoMember(9)]
		public bool  isUseHangPointRotateAtStart   = false;
		[ProtoMember(10)]
		public bool  isHangPointUseAction   = false;
		[ProtoMember(11)]
		public bool  isLoop   = false;
		[ProtoMember(12)]
		public bool  isBuffEffect   = false;
		[ProtoMember(13)]
		public bool  isBindActionSpeed   = true;
		[ProtoMember(14)]
		public bool  isBindScale   = true;
		[ProtoMember(15)]
		public bool  isBindAction   = false;
		[ProtoMember(16)]
		public bool  isGlobalEffect   = false;
		[ProtoMember(17)]
		public bool  isBindCaster   = true;
		[ProtoMember(18)]
		public bool  isBindFrame   = false;
		[ProtoMember(19)]
		public bool  isBindPosition   = false;
		[ProtoMember(20)]
		public bool  startRotationWithTarget   = true;
		[ProtoMember(21)]
		public bool  placeToFoothol   = false;
		[ProtoMember(22)]
		public bool  isAbsolutePosition   = false;
		[ProtoMember(23)]
		public bool  isPreciseLineRender   = false;
		[ProtoMember(24)]
		public bool  isBindRotation   = false;
		[ProtoMember(25)]
		public bool  isCasterUseHeroOwnerWhenProjectile   = false;
		[ProtoMember(26)]
		public CommandActionObject  actionObject  ;
		[ProtoMember(27)]
		public CharacterHangPoint.SupportHangPointType  hangPoint   = CharacterHangPoint.SupportHangPointType.CUSTOM_LOC;
		[ProtoMember(28)]
		public AFCValue  effectNameLink  ;
		[NonSerialized]public ulong  casterObjectId  ;
		[ProtoMember(29)]
		public string  globalEffectUID   = "";
		[ProtoMember(30)]
		public string  bindName   = "";
		[ProtoMember(31)]
		public string  effectName   = "";
		[ProtoMember(32)]
		public string  buffEffectEffectKey   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class RemoveEffect : TimeEventProType
	{
		
		[ProtoMember(1)]
		public string  effectName   = "";
		[ProtoMember(2)]
		public string  globalEffectUID   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class AddUIEffect : TimeEventProType
	{
		
		[ProtoMember(1)]
		public Vector3  position  ;
		[ProtoMember(2)]
		public Vector3  scale  ;
		[ProtoMember(3)]
		public Vector3  rotation  ;
		[ProtoMember(4)]
		public float  liveTime   = 0.0f;
		[ProtoMember(5)]
		public bool  autoDestroy   = true;
		[ProtoMember(6)]
		public string  effectName   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class RemoveUIEffect : TimeEventProType
	{
		
		[ProtoMember(1)]
		public string  effectName   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class AddBattleWarning : TimeEventProType
	{
		
		[ProtoMember(1)]
		public Vector3  posOffset  ;
		[ProtoMember(2)]
		public Vector3  rotationOffset  ;
		[ProtoMember(3)]
		public float  time   = 0.0f;
		[ProtoMember(4)]
		public float  radius   = 0.0f;
		[ProtoMember(5)]
		public float  degree   = 0.0f;
		[ProtoMember(6)]
		public float  effectLength   = 0.0f;
		[ProtoMember(7)]
		public float  width   = 0.0f;
		[ProtoMember(8)]
		public WarningEnum  warningModeEnum   = WarningEnum.Enum_warning_circle;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ProjectileControl : TimeEventProType
	{
		
		[ProtoMember(1)]
		public ProjectileControlCnt  controlCountEnum   = ProjectileControlCnt.Enum_ONE;
		[ProtoMember(2)]
		public ProjectileControlType  controlTypeEnum   = ProjectileControlType.Enum_Detonate;
		[ProtoMember(3)]
		public string  projectileTarget   = "";
		[ProtoMember(4)]
		public string  actionLabel   = "";
		[ProtoMember(5)]
		public string  projectileTargetLink   = "";
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class BuffControlByID : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  buffID   = 0;
		[ProtoMember(2)]
		public float  radius   = 0f;
		[ProtoMember(3)]
		public BuffControlType  controlTypeEnum   = BuffControlType.Enum_Remove;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class LaunchProjectile : TimeEventProType
	{
		
		[ProtoMember(1)]
		public Vector3  launchPositionRandomMin  ;
		[ProtoMember(2)]
		public Vector3  targetPositionRandomMin  ;
		[ProtoMember(3)]
		public Vector3  launchPosition  ;
		[ProtoMember(4)]
		public Vector3  launchPositionRandomMax  ;
		[ProtoMember(5)]
		public Vector3  launchPositionIntervalChange  ;
		[ProtoMember(6)]
		public Vector3  posBezier_2  ;
		[ProtoMember(7)]
		public Vector3  launchAngleIntervalChange  ;
		[ProtoMember(8)]
		public Vector3  posBezier_1  ;
		[ProtoMember(9)]
		public Vector3  startAngle  ;
		[ProtoMember(10)]
		public Vector3  randomAngleRangeMin  ;
		[ProtoMember(11)]
		public Vector3  randomAngleRangeMax  ;
		[ProtoMember(12)]
		public Vector3  targetPositionRandomMax  ;
		[ProtoMember(13)]
		public int  minSpeed   = -1;
		[ProtoMember(14)]
		public int  maxSpeed   = -1;
		[ProtoMember(15)]
		public int  IntervalChangeResetCount   = 0;
		[ProtoMember(16)]
		public int  moveFrame   = 0;
		[ProtoMember(17)]
		public int  acceleration   = 0;
		[ProtoMember(18)]
		public int  startSpeed   = 300;
		[ProtoMember(19)]
		public int  trackingRate   = 0;
		[ProtoMember(20)]
		public int  launchIndex   = 1;
		[ProtoMember(21)]
		public int  launchInterval   = 1;
		[ProtoMember(22)]
		public int  bulletGroupType   = 0;
		[ProtoMember(23)]
		public int  hitId   = 0;
		[ProtoMember(24)]
		public int  launchCount   = 1;
		[ProtoMember(25)]
		public bool  relaunch_change_position   = false;
		[ProtoMember(26)]
		public bool  relaunch_change_rotation   = false;
		[ProtoMember(27)]
		public bool  autoChangeAngle   = false;
		[ProtoMember(28)]
		public bool  HitButNoDamage   = false;
		[ProtoMember(29)]
		public bool  inheritHitID   = false;
		[ProtoMember(30)]
		public bool  startPointOnFloor   = true;
		[ProtoMember(31)]
		public bool  launchBindAction   = true;
		[ProtoMember(32)]
		public bool  placeToFoothol   = false;
		[ProtoMember(33)]
		public bool  useParamsFromLauncher   = false;
		[ProtoMember(34)]
		public bool  GroupOnBatchLaunch   = false;
		[ProtoMember(35)]
		public bool  bindBone   = false;
		[ProtoMember(36)]
		public bool  isPositionChangeByRotation   = false;
		[ProtoMember(37)]
		public bool  GroupOnAction   = false;
		[ProtoMember(38)]
		public bool  followTarget   = false;
		[ProtoMember(39)]
		public bool  islaunchPositionLink   = false;
		[ProtoMember(40)]
		public bool  startOnTarget   = false;
		[ProtoMember(41)]
		public bool  launchBindOwnerLive   = false;
		[ProtoMember(42)]
		public CharacterHangPoint.SupportHangPointType  targetHangPoint   = CharacterHangPoint.SupportHangPointType.CUSTOM_LOC;
		[ProtoMember(43)]
		public LaunchProjectileLineType  typeNameEnum   = LaunchProjectileLineType.Enum_Static;
		[ProtoMember(44)]
		public AFCVector3  projectileScale  ;
		[ProtoMember(45)]
		public MotionAccelerationLink  extendMotion  ;
		[ProtoMember(46)]
		public SelectTargetEnum  selectTargetEnum   = SelectTargetEnum.Enum_currentTarget;
		[ProtoMember(47)]
		public LaunchProjectileSourceType  projectileSourceTypeEnum   = LaunchProjectileSourceType.Enum_New;
		[ProtoMember(48)]
		public AFCVector3  startAngleLink  ;
		[ProtoMember(49)]
		public AFCInt  hitIDLink  ;
		[ProtoMember(50)]
		public AFCInt  launchCountLink  ;
		[ProtoMember(51)]
		public AFCInt  launchIntervalLink  ;
		[ProtoMember(52)]
		public Projectile_StartAngle_Relate  startAngleRelate  ;
		[ProtoMember(53)]
		public AFCInt  launchDelayTime  ;
		[ProtoMember(54)]
		public AFCVector3  launchAngleIntervalChangeLink  ;
		[ProtoMember(55)]
		public ProjectileLaunchTypeEnum  projectileLaunchTypeEnum   = ProjectileLaunchTypeEnum.Enum_NULL;
		[ProtoMember(56)]
		public CharacterHangPoint.SupportHangPointType  hangPoint   = CharacterHangPoint.SupportHangPointType.CUSTOM_LOC;
		[ProtoMember(57)]
		public CampEnum  campEnum   = CampEnum.Enum_CURRENT_CAMP;
		[ProtoMember(58)]
		public AFCInt  startSpeedLink  ;
		[ProtoMember(59)]
		public string  bindName   = "";
		[ProtoMember(60)]
		public string  replaceProjectileView   = "";
		[ProtoMember(61)]
		public string  completeAction   = "fly_complete";
		[ProtoMember(62)]
		public string  prefabName   = "";
		[ProtoMember(63)]
		public string  carryOnVariableKeyFrom   = "";
		[ProtoMember(64)]
		public string  carryOnVariableKeyTo   = "";
		[ProtoMember(65)]
		public string  launchPositionLink   = "";
		[ProtoMember(66)]
		public string  projectileTarget   = "";
		[ProtoMember(67)]
		public bool  moveDelayFrame   = false;
		[ProtoMember(68)]
		public Projectile_StartAngle_Relate  startPositionRelate = Projectile_StartAngle_Relate.Self  ;
		
		[ProtoMember(69)]
		public AFCVector3  posBezier_1_link  ;
		[ProtoMember(70)]
		public AFCVector3  posBezier_2_link  ;
		[ProtoMember(71)]
		public bool  isIntelligenceBezier  = false;
		[ProtoMember(72)]
		public AFCInt  bezierStandardDistance;
		[ProtoMember(73)]
		public AFCInt  bezierStandardScale;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class RepeatLaunchProjectile : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  hitId   = 0;
		[ProtoMember(2)]
		public bool  launchBindOwnerLive   = true;
		[ProtoMember(3)]
		public ProjectileLaunchTypeEnum  projectileLaunchTypeEnum   = ProjectileLaunchTypeEnum.Enum_NULL;
		[ProtoMember(4)]
		public AFCInt  launchCountLink  ;
		[ProtoMember(5)]
		public AFCInt  launchIntervalLink  ;
		[ProtoMember(6)]
		public AFCInt  launchDelayTime  ;
		[ProtoMember(7)]
		public string  prefabName   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ChangeProjectileAroundRadius : TimeEventProType
	{
		
		[ProtoMember(1)]
		public AFCValue  AroundRadiusLink  ;
		[ProtoMember(2)]
		public AFCValue  AroundHeightLink  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ProjectileChangeView : TimeEventProType
	{
		
		[ProtoMember(1)]
		public string  replaceProjectileView   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ControlProjectileView : TimeEventProType
	{
		
		[ProtoMember(1)]
		public ControlProjectileViewType  typeNameEnum   = ControlProjectileViewType.Enum_Destroy;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ProjectileFlyBack : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  startSpeed   = 5;
		[ProtoMember(2)]
		public int  acceleration   = 0;
		[ProtoMember(3)]
		public int  minSpeed   = -1;
		[ProtoMember(4)]
		public int  maxSpeed   = -1;
		[ProtoMember(5)]
		public int  moveFrame   = 0;
		[ProtoMember(6)]
		public bool  inherit   = true;
		[ProtoMember(7)]
		public bool  reLaunch   = false;
		[ProtoMember(8)]
		public SelectTargetEnum  selectTargetEnum   = SelectTargetEnum.Enum_currentTarget;
		[ProtoMember(9)]
		public ProjectileFlyBackLineType  flyTypeNameEnum   = ProjectileFlyBackLineType.Enum_Default;
		[ProtoMember(10)]
		public string  completeAction   = "dispose";
		[ProtoMember(11)]
		public string  projectileTarget   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ChangeGravity : TimeEventProType
	{
		
		[ProtoMember(1)]
		public float  speedY   = 0.0f;
		[ProtoMember(2)]
		public float  gravity   = 0.0f;
		[ProtoMember(3)]
		public bool  reset   = false;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class AddShock : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  frequency   = 1;
		[ProtoMember(2)]
		public float  power   = 2f;
		[ProtoMember(3)]
		public float  angle   = 0f;
		[ProtoMember(4)]
		public float  time   = 1f;
		[ProtoMember(5)]
		public float  decay   = 0.98f;
		[ProtoMember(6)]
		public float  minRange   = 40.0f;
		[ProtoMember(7)]
		public float  maxRange   = 80.0f;
		[ProtoMember(8)]
		public bool  randomAngle   = false;
		[ProtoMember(9)]
		public bool  isEffectAir   = false;
		[ProtoMember(10)]
		public bool  isOnlyEffect2Player   = true;
		[ProtoMember(11)]
		public bool  isRelativePlayer   = false;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ActionData : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  bandSkillId   = 0;
		[ProtoMember(2)]
		public int  skillRange   = 0;
		[ProtoMember(3)]
		public int  cancel_Priority   = 0;
		[ProtoMember(4)]
		public int  cancel_PriorityLimit   = -1;
		[ProtoMember(5)]
		public float  speed   = 1.0f;
		[ProtoMember(6)]
		public float  fadeLength   = 0.3f;
		[ProtoMember(7)]
		public bool  loop   = false;
		[ProtoMember(8)]
		public bool  ignoreDeathBreak   = false;
		[ProtoMember(9)]
		public bool  linkUpFrame   = false;
		[ProtoMember(10)]
		public ActionUseModeEnum  useTypeEnum   = ActionUseModeEnum.Enum_LAND;
		[ProtoMember(11)]
		public ActionLayerEnum  LayerNameEnum   = ActionLayerEnum.Enum_BaseLayer;
		[ProtoMember(12)]
		public string  actionLabel   = "";
		[ProtoMember(13)]
		public string  animationName   = "";
		[ProtoMember(14)]
		public string  actionName   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class AddComboRespond : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  duration   = 0;
		[ProtoMember(2)]
		public int  saveFrame   = 0;
		[ProtoMember(3)]
		public int  successActionFrame   = 0;
		[ProtoMember(4)]
		public int  failActionFrame   = 0;
		[ProtoMember(5)]
		public bool  skillButtonTip   = false;
		[ProtoMember(6)]
		public string  comboHash  ;
		[ProtoMember(7)]
		public KeyStateEnum  keyStateEnum   = KeyStateEnum.Enum_KD;
		[ProtoMember(8)]
		public string[]  successLinkList  ;
		[ProtoMember(9)]
		public string[]  failLinkList  ;
		[ProtoMember(10)]
		public string[]  conditionList  ;
		[ProtoMember(11)]
		public string  successAction   = "";
		[ProtoMember(12)]
		public string  failAction   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckKeyState : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  duration   = 0;
		[ProtoMember(2)]
		public string  comboName  ;
		[ProtoMember(3)]
		public KeyStateEnum  keyStateEnum   = KeyStateEnum.Enum_KD;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class Teleport : TimeEventProType
	{
		
		[ProtoMember(1)]
		public Vector3  positionOffset  ;
		[ProtoMember(2)]
		public bool  isChangeGrid   = false;
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = true)]
#endif
		[ProtoMember(3)]
		public string  cell_link   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SummonCharacter : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  summonCfgID1   = 0;
		[ProtoMember(2)]
		public int  summonCfgID2   = 0;
		[ProtoMember(3)]
		public int  summonCfgID3   = 0;
		[ProtoMember(4)]
		public bool  isCustomStartPos   = false;
		[ProtoMember(5)]
		public SummonTargetSelect  targetSelectEnum   = SummonTargetSelect.Enum_summer;
		[ProtoMember(6)]
		public ActionSummonSourceDataType  actionSummonSourceDataType   = ActionSummonSourceDataType.None;
		[ProtoMember(7)]
		public CommandActionObject  actionObject  ;
		[ProtoMember(8)]
		public SummonHeroKuData[]  heroKuDatas  ;
		[ProtoMember(9)]
		public string  bornPos_link   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class DestroyMe : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ConditionsLinker : TimeEventProType
	{
		
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = true)]
#endif
		[ProtoMember(1)]
		public string[]  conditionList  ;
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Output(name = "outlink", allowMultiple = true)]
#endif
		[ProtoMember(2)]
		public string[]  successLinkList  ;
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Output(name = "outlink", allowMultiple = true)]
#endif
		[ProtoMember(3)]
		public string[]  failLinkList  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class JumpToTarget : TimeEventProType
	{
		
		[ProtoMember(1)]
		public Vector3  offset  ;
		[ProtoMember(2)]
		public int  startSpeed   = 0;
		[ProtoMember(3)]
		public int  duration   = 0;
		[ProtoMember(4)]
		public float  powerRate   = 1.0f;
		[ProtoMember(5)]
		public string  riseType   = "jump";
		[ProtoMember(6)]
		public string  riseLoopType   = "";
		[ProtoMember(7)]
		public string  fallType   = "";
		[ProtoMember(8)]
		public string  fallLoopType   = "";
		[ProtoMember(9)]
		public string  toGroundType   = "idle";
		[ProtoMember(10)]
		public string  toGroundCommonType   = "";
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = true)]
#endif
		[ProtoMember(11)]
		public string  targetLink   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ResetActionState : TimeEventProType
	{
		
		[ProtoMember(1)]
		public ResetActionStateType  stateEnum   = ResetActionStateType.Enum_STAND;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CleanFightProtectState : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class AddForceField : TimeEventProType
	{
		
		[ProtoMember(1)]
		public Vector3  offset  ;
		[ProtoMember(2)]
		public Vector3  power  ;
		[ProtoMember(3)]
		public Vector3  acceleration  ;
		[ProtoMember(4)]
		public float  radius    = 0.0f;
		[ProtoMember(5)]
		public bool  isCenterPower   = false;
		[ProtoMember(6)]
		public bool  lockDirection   = false;
		[ProtoMember(7)]
		public CampEnum  targetCampEnum   = CampEnum.Enum_CURRENT_CAMP;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SwitchTarget : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  radius   = 0;
		[ProtoMember(2)]
		public int  height   = 0;
		[ProtoMember(3)]
		public int  degree   = 0;
		[ProtoMember(4)]
		public bool  isJoystick   = true;
		[ProtoMember(5)]
		public bool  isComboClickJoystick   = true;
		[ProtoMember(6)]
		public CampEnum  targetCampEnum   = CampEnum.Enum_CURRENT_CAMP;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class LucianSkill : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  totalTime   = 400;
		[ProtoMember(2)]
		public int  moveFrames   = 10;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class LucianSkill_5_5 : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  x_width   = 2000;
		[ProtoMember(2)]
		public int  z_height   = 10000;
		[ProtoMember(3)]
		public int  moveFrames   = 10;
		[ProtoMember(4)]
		public int  specialRange   = 2;
		[ProtoMember(5)]
		public string[]  startMoveLinkArray  ;
		[ProtoMember(6)]
		public string[]  moveCompleteListArray  ;
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = false)]
#endif
		[ProtoMember(7)]
		public string  hitNothingLink   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class IsCanOverlap : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  centerRow   = 0;
		[ProtoMember(2)]
		public int  centerCol   = 0;
		[ProtoMember(3)]
		public int  rangeRow   = 0;
		[ProtoMember(4)]
		public int  rangeCol   = 0;
		[ProtoMember(5)]
		public bool  isUseCurActionAttackBox   = true;
		[ProtoMember(6)]
		public TDRConfig.EFFECT_TARGET  checkEffectType   = 0;
		[ProtoMember(7)]
		public TDRConfig.HitShapeType  shapeType   = 0;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class MoveToTarget : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  moveSpeed   = 0;
		[ProtoMember(2)]
		public bool  isNeedValidGridUnit   = true;
		[ProtoMember(3)]
		public bool  ifFind0FindNeighbor   = true;
		[ProtoMember(4)]
		public bool  isIgnoreHeight   = true;
		[ProtoMember(5)]
		public bool  isAutoChangeDirection   = true;
		[ProtoMember(6)]
		public bool  isIgnoreStun   = false;
		[ProtoMember(7)]
		public bool  superArmor   = false;
		[ProtoMember(8)]
		public bool  isCellIsCurrentCanSuccessLink   = false;
		[ProtoMember(9)]
		public MoveToTargetSelectTarget  selectTargetEnum   = MoveToTargetSelectTarget.Enum_targets;
		[ProtoMember(10)]
		public MoveToTargetTargetPos  targetPosEnum   = MoveToTargetTargetPos.Enum_nothing;
		[ProtoMember(11)]
		public MoveToTargetSpeedType  speedTypeEnum   = MoveToTargetSpeedType.Enum_CustomInput;
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Output(name = "outlink", allowMultiple = true)]
#endif
		[ProtoMember(12)]
		public string[]  successLinkList  ;
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = true)]
#endif
		[ProtoMember(13)]
		public string  targetLink   = "";
		
		[ProtoMember(14)]
		public bool  doNotOccupyGrid   = false;
		
		[ProtoMember(15)]
		public int  superArmorLevel   = 20000;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class MoveWithAMT : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  ignoreScaleOffset   = false;
		[ProtoMember(2)]
		public MoveWithAMTRelativeNode  RelativeNodeEnum   = MoveWithAMTRelativeNode.Enum_Default;
		[ProtoMember(3)]
		public string  AnimationName   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class MoveAroundTarget : TimeEventProType
	{
		
		[ProtoMember(1)]
		public float  degreeSpeed   = 0f;
		[ProtoMember(2)]
		public float  maxAngle   = 0f;
		[ProtoMember(3)]
		public bool  stayGrid   = false;
		[ProtoMember(4)]
		public bool  isLookTarget   = true;
		[ProtoMember(5)]
		public EffectTarget  selectTargetEnum   = EffectTarget.Enum_self;
		[ProtoMember(6)]
		public string[]  successLinkList  ;
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = true)]
#endif
		[ProtoMember(7)]
		public string  targetLink   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class MoveBindTarget : TimeEventProType
	{
		
		[ProtoMember(1)]
		public Vector3  posOffset  ;
		[ProtoMember(2)]
		public float  followRate   = 0.3f;
		[ProtoMember(3)]
		public bool  isBindRotation   = true;
		[ProtoMember(4)]
		public bool  isUpdateHeroGrid   = false;
		[ProtoMember(5)]
		public EffectTarget  selectTargetEnum   = EffectTarget.Enum_self;
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = true)]
#endif
		[ProtoMember(6)]
		public string  targetLink   = "";
		
		[ProtoMember(7)]
        public Vector3  rotationOffset  ;
        [ProtoMember(8)]
        public float  followRotationRate   = 0.3f;
        [ProtoMember(9)]
        public bool  isBindBone   = false;
        [ProtoMember(10)]
        public CharacterHangPoint.SupportHangPointType  hangPoint   = CharacterHangPoint.SupportHangPointType.CUSTOM_LOC;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class FlyToTarget : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  moveSpeed   = 0;
		[ProtoMember(2)]
		public int  extendMoveFrame   = 0;
		[ProtoMember(3)]
		public float  disOffset    = 0.0f;
		[ProtoMember(4)]
		public float  disXOffset    = 0.0f;
		[ProtoMember(5)]
		public bool  autoChangeDirection   = false;
		[ProtoMember(6)]
		public AutoChangeDirectionAttackNode  AttackNodeEnum   = AutoChangeDirectionAttackNode.Enum_Default;
		[ProtoMember(7)]
		public string[]  arriveLinkList  ;
		[ProtoMember(8)]
		public string[]  successLinkList  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class RunToTarget : TimeEventProType
	{
		
		[ProtoMember(1)]
		public Vector3  eularAngle  ;
		[ProtoMember(2)]
		public int  moveSpeed   = 0;
		[ProtoMember(3)]
		public bool  lockRotation   = false;
		[ProtoMember(4)]
		public string[]  successLinkList  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class FlyToTargetAdvance : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  moveSpeed   = 0;
		[ProtoMember(2)]
		public int  maxSpeed   = 999;
		[ProtoMember(3)]
		public int  minSpeed   = 0;
		[ProtoMember(4)]
		public int  startPercent   = 0;
		[ProtoMember(5)]
		public int  currentMoveSpeed   = 0;
		[ProtoMember(6)]
		public int  moveTotalFrame   = 0;
		[ProtoMember(7)]
		public int  movePercentFrame   = 0;
		[ProtoMember(8)]
		public float  changeValue   = 1.0f;
		[ProtoMember(9)]
		public FlyToTargetAdvanceChangeType  changeTypeEnum   = FlyToTargetAdvanceChangeType.Enum_NONE;
		[ProtoMember(10)]
		public string[]  successLinkList  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class AddMoveMotion : TimeEventProType
	{
		
		[ProtoMember(1)]
		public float  moveSpeed   = 0f;
		[ProtoMember(2)]
		public float  moveSpeedAccelerate   = 0f;
		[ProtoMember(3)]
		public float  maxSpeedRate   = 0f;
		[ProtoMember(4)]
		public float  minSpeedRate   = 0f;
		[ProtoMember(5)]
		public AddMoveMotionMoveDirection  moveDirectionEnum   = AddMoveMotionMoveDirection.Enum_LEFT;
		[ProtoMember(6)]
		public string  getTarget   = "findTarget";
		[ProtoMember(7)]
		public string  condition_dispose   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class AddViewMotion : TimeEventProType
	{
		
		[ProtoMember(1)]
		public float  startSpeed   = 0f;
		[ProtoMember(2)]
		public float  acceleration   = 0f;
		[ProtoMember(3)]
		public float  maxSpeedRate   = 0f;
		[ProtoMember(4)]
		public float  minSpeedRate   = 0f;
		[ProtoMember(5)]
		public float  duration   = 0f;
		[ProtoMember(6)]
		public float  tracingRotateRate   = 0f;
		[ProtoMember(7)]
		public bool  autodirection   = false;
		[ProtoMember(8)]
		public bool  ignoreHeight   = true;
		[ProtoMember(9)]
		public EffectTarget  ownerEnum   = EffectTarget.Enum_self;
		[ProtoMember(10)]
		public EffectTarget  targetEnum   = EffectTarget.Enum_self;
		[ProtoMember(11)]
		public AddViewMotionMotionType  motionTypeEnum   = AddViewMotionMotionType.Enum_Swallow;
		[ProtoMember(12)]
		public AddViewMotionModityType  modityTypeEnum   = AddViewMotionModityType.Enum_Modify;
		
		[ProtoMember(13)]
		public CharacterHangPoint.SupportHangPointType hangPoint = CharacterHangPoint.SupportHangPointType.CUSTOM_LOC;

		[ProtoMember(14)]
		public string  customOwnerLink   = "";	
		[ProtoMember(15)]
		public string  customTargetLink   = "";
	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SetVariableValue : TimeEventProType
	{
		
		[ProtoMember(1)]
		public string  keyName  ;
		[ProtoMember(2)]
		public OperatorTypeEnum  operatorTypeEnum   = OperatorTypeEnum.Enum_equal;
		[ProtoMember(3)]
		public ValueTypeEnum  valueTypeEnum   = ValueTypeEnum.Enum_int;
		[ProtoMember(4)]
		public string  value   = "0";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SetVariableValueAdvance : TimeEventProType
	{
		
		[ProtoMember(1)]
		public string  keyName  ;
		[ProtoMember(2)]
		public SetVariableValueAdvanceEffectTarget  effectTargetEnum   = SetVariableValueAdvanceEffectTarget.Enum_self;
		[ProtoMember(3)]
		public OperatorTypeEnum  operatorTypeEnum   = OperatorTypeEnum.Enum_equal;
		[ProtoMember(4)]
		public AFCValue  value  ;
		[ProtoMember(5)]
		public string  customLink   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SetAnimatorVParameter : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  ignoreShowInBattleField   = false;
		[ProtoMember(2)]
		public OperatorTypeEnum  operatorTypeEnum   = OperatorTypeEnum.Enum_equal;
		[ProtoMember(3)]
		public SetAnimatorVParameterValueSource  valueSourceEnum   = SetAnimatorVParameterValueSource.Enum_userinput;
		[ProtoMember(4)]
		public ValueTypeEnum  valueTypeEnum   = ValueTypeEnum.Enum_int;
		[ProtoMember(5)]
		public string  keyName   = "";
		[ProtoMember(6)]
		public string  value   = "0";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SetAnimatorLayerWeight : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  ignoreShowInBattleField   = false;
		[ProtoMember(2)]
		public AFCValue  Weight  ;
		[ProtoMember(3)]
		public AFCValue  LayerIndex  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckVariableValue : TimeEventProType
	{
		
		[ProtoMember(1)]
		public string  checkName  ;
		[ProtoMember(2)]
		public string  checkKeyName  ;
		[ProtoMember(3)]
		public ValueTypeEnum  checkValueTypeEnum   = ValueTypeEnum.Enum_int;
		[ProtoMember(4)]
		public CompairisonOperatorEnum  checkTypeEnum   = CompairisonOperatorEnum.Enum_greaterThan;
		[ProtoMember(5)]
		public string  checkValue   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SwitchVariableValue : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  useLink   = false;
		[ProtoMember(2)]
		public string  checkName  ;
		[ProtoMember(3)]
		public ValueTypeEnum  checkValueTypeEnum   = ValueTypeEnum.Enum_int;
		[ProtoMember(4)]
		public string[]  failLinkList  ;
		[ProtoMember(5)]
		public CheckSwitchData[]  condtionList  ;
		[ProtoMember(6)]
		public string  checkValueLink   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CameraCustomSetting : TimeEventProType
	{
		
		[ProtoMember(1)]
		public float  cameraDistance   = 0.0f;
		[ProtoMember(2)]
		public float  changeDistanceRatio   = 0.0f;
		[ProtoMember(3)]
		public float  cameraFieldOfView   = 0.0f;
		[ProtoMember(4)]
		public float  changeFovRatio   = 0.0f;
		[ProtoMember(5)]
		public float  defaultAngleVertical   = 0.0f;
		[ProtoMember(6)]
		public float  defaultAngleHorizontal   = 0.0f;
		[ProtoMember(7)]
		public float  lookAtPosRadio   = 0.0f;
		[ProtoMember(8)]
		public float  lookAtPosRadio_changeRatio   = 0.0f;
		[ProtoMember(9)]
		public bool  IschangeCameraDistance   = false;
		[ProtoMember(10)]
		public bool  IschangeCameraFieldOfView   = false;
		[ProtoMember(11)]
		public bool  IschangeDefaultAngleVertical   = false;
		[ProtoMember(12)]
		public bool  IschangeDefaultHorizontal   = false;
		[ProtoMember(13)]
		public bool  IschangeLookAtPosRadio   = false;
		[ProtoMember(14)]
		public bool  isResume   = false;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CameraCustomSettingWithAnimation : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  backTweenFrame   = 0;
		[ProtoMember(2)]
		public float  fieldOfView   = 32f;
		[ProtoMember(3)]
		public bool  ignoreScaleOffset   = false;
		[ProtoMember(4)]
		public bool  alwaysForceInitDirection   = false;
		[ProtoMember(5)]
		public bool  autoDestroy   = true;
		[ProtoMember(6)]
		public bool  toBackCamera   = false;
		[ProtoMember(7)]
		public bool  isSmoothToBackCamera   = false;
		[ProtoMember(8)]
		public CameraCustomSettingWithAnimationRelativeNode  RelativeNodeEnum   = CameraCustomSettingWithAnimationRelativeNode.Enum_Default;
		[ProtoMember(9)]
		public string  animationName   = "";
		[ProtoMember(10)]
		public string  nextCameraLink   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CameraDirectionSetting : TimeEventProType
	{
		
		[ProtoMember(1)]
		public Vector3  lookOffset  ;
		[ProtoMember(2)]
		public Vector3  rotateOffset  ;
		[ProtoMember(3)]
		public int  duration   = 0;
		[ProtoMember(4)]
		public int  intTime   = 0;
		[ProtoMember(5)]
		public int  outTime   = 0;
		[ProtoMember(6)]
		public float  cameraFieldOfView   = 0.0f;
		[ProtoMember(7)]
		public float  cameraDistance   = 0.0f;
		[ProtoMember(8)]
		public bool  isResume   = false;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ResumeCameraCustomSetting : TimeEventProType
	{
		
		[ProtoMember(1)]
		public float  changeToBattleCameraTime   = 0.0f;
		[ProtoMember(2)]
		public bool  ignoreHorizontalLimit   = false;
		[ProtoMember(3)]
		public bool  ignoreVerticalLimit   = false;
		[ProtoMember(4)]
		public bool  ignoreDistanceLimit   = false;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class TurnRound : TimeEventProType
	{
		
		[ProtoMember(1)]
		public float  turnSpeed   = 0.0f;
		[ProtoMember(2)]
		public TurnRoundTurnType  turnTypeEnum   = TurnRoundTurnType.Enum_custom;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GrabTarget : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  hitId   = 0;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ThrowTarget : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  startSpeed   = 300;
		[ProtoMember(2)]
		public int  acceleration   = 0;
		[ProtoMember(3)]
		public int  minSpeed   = -1;
		[ProtoMember(4)]
		public int  maxSpeed   = -1;
		[ProtoMember(5)]
		public int  moveFrame   = 0;
		[ProtoMember(6)]
		public bool  chekThrowEnable   = false;
		[ProtoMember(7)]
		public ThrowTargetName  throwTargetNameEnum   = ThrowTargetName.Enum_grabtargets;
		[ProtoMember(8)]
		public ThrowTargetTypeName  typeNameEnum   = ThrowTargetTypeName.Enum_Nothing;
		[ProtoMember(9)]
		public EffectTarget  throwTargetObjectEnum   = EffectTarget.Enum_self;
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = true)]
#endif
		[ProtoMember(10)]
		public string  targetLink   = "";
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = false)]
#endif
		[ProtoMember(11)]
		public string  lauchLink   = "";
		[ProtoMember(12)]
		public string  throwHurtAction   = "";
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = true)]
#endif
		[ProtoMember(13)]
		public string  throwPosLink   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ExecuteHit : TimeEventProType
	{
		
		[ProtoMember(1)]
		public AFCValue  hitId   = null;
		[ProtoMember(2)]
		public CommandActionObject  bear   = null;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class HitGroupControl : TimeEventProType
	{
		
		[ProtoMember(1)]
		public AFCValue  GroupIndex  ;
		[ProtoMember(2)]
		public HitGroupControlGroupType  GroupTypeEnum   = HitGroupControlGroupType.Enum_Attack;
		[ProtoMember(3)]
		public HitGroupControlControlType  ControlTypeEnum   = HitGroupControlControlType.Enum_Enable;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class EventActionData : TimeEventProType
	{
		
		[ProtoMember(1)]
		public EventActionDataType  EventListenEnum   = EventActionDataType.Enum_Nothing;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SendEvent : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  param1   = 0;
		[ProtoMember(2)]
		public ActionSendEventName  EventNameEnum   = ActionSendEventName.Enum_Nothing;
		[ProtoMember(3)]
		public string  EventName   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SendEventToViewUnit : TimeEventProType
	{
		
		[ProtoMember(1)]
		public float  duration   = 0.0f;
		[ProtoMember(2)]
		public bool  showOrHide   = false;
		[ProtoMember(3)]
		public string  EventName   = "";
		[ProtoMember(4)]
		public string  effectName   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class BulletTime : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  duration   = 0;
		[ProtoMember(2)]
		public float  toValue   = 0.05f;
		[ProtoMember(3)]
		public BulletTimeTargetType  targetTypeEnum   = BulletTimeTargetType.Enum_Enemy;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class EffectTweenControl : TimeEventProType
	{
		
#if ACGGAME_CLIENT

		public int  tweenIn   = 0;
		public int  tweenOut   = 0;
		public int  duration   = 0;
		public float  from   = 0.0f;
		public float  to   = 0.0f;
		public bool  hideMap   = false;
		public EffectTweenControlEffect  EffectEnum   = EffectTweenControlEffect.Enum_BlackScreen;

#endif

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class RoleTransform : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  heroCfgID   = 0;
		[ProtoMember(2)]
		public string  StartActionName   = "idle";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ChangeWeapon : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  show   = true;
		[ProtoMember(2)]
		public string  WeaponNodeName   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ShowOrHideBody : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  level   = 0;
		[ProtoMember(2)]
		public bool  isShow   = true;
		[ProtoMember(3)]
		public CommandActionObject  actionObject  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class EffectRadialBlur : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  tweenIn   = 0;
		[ProtoMember(2)]
		public int  tweenOut   = 0;
		[ProtoMember(3)]
		public int  duration   = 0;
		[ProtoMember(4)]
		public float  dist_from   = 0.0f;
		[ProtoMember(5)]
		public float  dist_to   = 0.0f;
		[ProtoMember(6)]
		public float  strength_from   = 0.0f;
		[ProtoMember(7)]
		public float  strength_to   = 0.0f;
		[ProtoMember(8)]
		public bool  open   = true;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class PlayWWiseSound : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  threeD   = true;
		[ProtoMember(2)]
		public bool  skillSwitchRemove   = false;
		[ProtoMember(3)]
		public bool  isDeadClear   = false;
		[ProtoMember(4)]
		public string  bankName   = "";
		[ProtoMember(5)]
		public string  eventName   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class StopWWiseSound : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  threeD   = true;
		[ProtoMember(2)]
		public string  eventName   = "";
		[ProtoMember(3)]
		public string  bankName   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class LayerCancelLevelLimit : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  cancelLevel   = 0;
		[ProtoMember(2)]
		public ActionLayerEnum  LayerNameEnum   = ActionLayerEnum.Enum_BaseLayer;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ControlLock : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckKeystateChangeAngle : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  checkAngle   = 0;
		[ProtoMember(2)]
		public CompairisonOperatorEnum  checkTypeEnum   = CompairisonOperatorEnum.Enum_greaterThan;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckTime : TimeEventProType
	{
		
		[ProtoMember(1)]
		public AFCValue  compareTime  ;
		[ProtoMember(2)]
		public CheckTimeSource  checkSourceEnum   = CheckTimeSource.Enum_actionTime;
		[ProtoMember(3)]
		public CompairisonOperatorEnum  checkTypeEnum   = CompairisonOperatorEnum.Enum_greaterThan;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class RandomNumber : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  minNumber   = 0;
		[ProtoMember(2)]
		public int  maxNumber   = 0;
		[ProtoMember(3)]
		public string  toLink   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class StealthEffect : TimeEventProType
	{
		
#if ACGGAME_CLIENT

		public int  duration   = 0;
		public bool  isHide   = true;

#endif

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ChangeCollider : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  closeCharacterCollide   = false;
		[ProtoMember(2)]
		public bool  closeBuildingCollide   = false;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ChangeColliderSelf : TimeEventProType
	{
		
		[ProtoMember(1)]
		public ChangeColliderSelfColliderType  colliderTypeEnum   = ChangeColliderSelfColliderType.Enum_none;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ChangeSmoothCollideRate : TimeEventProType
	{
		
		[ProtoMember(1)]
		public float  colliderRate   = 0.0f;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SkillCostPoint : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  isCostCD   = true;
		[ProtoMember(2)]
		public bool  isCostEnergy   = true;
		[ProtoMember(3)]
		public SkillCostPointCostType  costTypeEnum   = SkillCostPointCostType.Enum_normal;
		[ProtoMember(4)]
		public string  failAction   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SkillEndNotify : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SkillEndContinue : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  isChangeMoveAble   = true;
		[ProtoMember(2)]
		public bool  moveAble   = true;
		[ProtoMember(3)]
		public bool  isChangeAttackAble   = true;
		[ProtoMember(4)]
		public bool  attackAble   = true;
		[ProtoMember(5)]
		public bool  isChangeSkillAble   = true;
		[ProtoMember(6)]
		public bool  skillAble   = true;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class DoublecastTriggerNotify : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  backTime   = 500;
		[ProtoMember(2)]
		public int  actionid   = 0;
		[ProtoMember(3)]
		public DoublecastTriggerNotifySelectType  targetReSelectTypeEnum   = DoublecastTriggerNotifySelectType.Enum_reSelect;
		[ProtoMember(4)]
		public string[]  successLinkList  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class NormalAttackNotify : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  attacking_trigger_tag   = 0;
		[ProtoMember(2)]
		public bool  onlyBuffTrigger   = false;
		[ProtoMember(3)]
		public bool  notDelayTrigger   = false;
		[ProtoMember(4)]
		public bool  preCheckTargetSwitchEnable   = false;
		[ProtoMember(5)]
		public CommandActionObject  actionObject  ;
		[ProtoMember(6)]
		public NormalAttackNotifyType  NotifyTypeEnum   = NormalAttackNotifyType.Enum_attacking;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SkillBreakNotify : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class PlayTurnActionTime : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckDistance : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  checkDistance   = 0;
		[ProtoMember(2)]
		public CommandActionObject  leftActionObject  ;
		[ProtoMember(3)]
		public CompairisonOperatorEnum  checkTypeEnum   = CompairisonOperatorEnum.Enum_greaterThan;
		[ProtoMember(4)]
		public CommandActionObject  actionObject  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckTargetState : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  buffStatus   = 0;
		[ProtoMember(2)]
		public ActionStateEnum  stateEnum   = ActionStateEnum.Enum_exist;
		[ProtoMember(3)]
		public CommandActionObject  actionObject  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckSelfState : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  buffStatus   = 0;
		[ProtoMember(2)]
		public ActionStateEnum  stateEnum   = ActionStateEnum.Enum_exist;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckSkillGrabState : TimeEventProType
	{
		
		[ProtoMember(1)]
		public CheckSkillGrabStateType  grabStateEnum   = CheckSkillGrabStateType.Enum_miss;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckGridIsEmpty : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  isSelfGrid   = false;
		[ProtoMember(2)]
		public string  gridLink   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetCount : TimeEventProType
	{
		
		[ProtoMember(1)]
		public GetCountReturnType  returnTypeEnum   = GetCountReturnType.Enum_totalCount;
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = true)]
#endif
		[ProtoMember(2)]
		public string  link   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetDistance : TimeEventProType
	{
		
		[ProtoMember(1)]
		public CommandActionObject  actionObject  ;
		[ProtoMember(2)]
		public string  projectileTarget   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetRandomNumber : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  minNumber   = 0;
		[ProtoMember(2)]
		public int  maxNumber   = 0;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetActionHitCount : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetCharacterAttribute : TimeEventProType
	{
		
		[ProtoMember(1)]
		public CommandActionObject  actionObject  ;
		[ProtoMember(2)]
		public TDRConfig.ATTRIBUTE_DEF  attributeName   = 0;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetVariableValue : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  arrayIndex   = -1;
		[ProtoMember(2)]
		public string  keyName  ;
		[ProtoMember(3)]
		public GetVariableValueEffectTarget  effectTargetEnum   = GetVariableValueEffectTarget.Enum_self;
		[ProtoMember(4)]
		public ValueTypeEnum  valueTypeEnum   = ValueTypeEnum.Enum_int;
		[ProtoMember(5)]
		public string  customLink   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetSkillExcelTime : TimeEventProType
	{
		
		[ProtoMember(1)]
		public GetSkillExcelTimeType  timeTypeEnum   = GetSkillExcelTimeType.Enum_CastTime;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetBulletCount : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  bulletGroupType   = 0;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class MultiCompare : TimeEventProType
	{
		
		[ProtoMember(1)]
		public AFCValue  checkValue  ;
		[ProtoMember(2)]
		public MultiCompreData[]  condtionList  ;
		[ProtoMember(3)]
		public string[]  failLinkList  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class Compare : TimeEventProType
	{
		
		[ProtoMember(1)]
		public AFCValue  checkValue  ;
		[ProtoMember(2)]
		public CompairisonOperatorEnum  operatorTypeEnum   = CompairisonOperatorEnum.Enum_greaterThan;
		[ProtoMember(3)]
		public AFCValue  value  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CalculateInt : TimeEventProType
	{
		
		[ProtoMember(1)]
		public AFCValue  value_1  ;
		[ProtoMember(2)]
		public OperatorTypeEnum  operatorTypeEnum   = OperatorTypeEnum.Enum_equal;
		[ProtoMember(3)]
		public AFCValue  value_2  ;
		[ProtoMember(4)]
		public CalculateIntType  toIntTypeEnum   = CalculateIntType.Enum_floor;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CalculateVector3 : TimeEventProType
	{
		
		[ProtoMember(1)]
		public AFCVector3  value_1  ;
		[ProtoMember(2)]
		public VectorOperatorTypeEnum  operatorTypeEnum   = VectorOperatorTypeEnum.Enum_equal;
		[ProtoMember(3)]
		public AFCValue  value_2  ;
		[ProtoMember(4)]
		public AFCVector3  value_3  ;
		[ProtoMember(5)]
		public CalculateVectorValueType  valueTypeEnum   = CalculateVectorValueType.Enum_vector3;
		[ProtoMember(6)]
		public CalculateIntType  toIntTypeEnum   = CalculateIntType.Enum_floor;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetRaycastGridSidePoint : TimeEventProType
	{
		
		[ProtoMember(1)]
		public AFCVector3  value_statrPoint  ;
		[ProtoMember(2)]
		public AFCVector3  value_endPoint  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckSelfBuff : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  checkLevel   = false;
		[ProtoMember(2)]
		public AFCValue  buffProtoID  ;
		[ProtoMember(3)]
		public AFCValue  buffLevel  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckSelfEquipment : TimeEventProType
	{
		
		[ProtoMember(1)]
		public AFCValue  EquipmentID  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class PlayBroke : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  isSelfExplosion   = false;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SwitchBranch : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  isRandomSeq   = false;
		[ProtoMember(2)]
		public SwitchBranchData[]  caseList  ;
		[ProtoMember(3)]
		public string[]  defaultList  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class PlayDeadEffect : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class EffectAfterImage : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  timeInterval   = 0;
		[ProtoMember(2)]
		public int  timeDuration   = 0;
		[ProtoMember(3)]
		public int  lifeTime   = 0;
		[ProtoMember(4)]
		public int  maxCount   = 0;
		[ProtoMember(5)]
		public bool  isClose   = false;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class EffectLightBody : TimeEventProType
	{
		
		[ProtoMember(1)]
		public float  saveTime   = 0.0f;
		[ProtoMember(2)]
		public string  effectName   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class RemoveLightBody : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class EffectAssassinEnterBattleTransparent : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class EffectBodyGeneric : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  isClose   = false;
		[ProtoMember(2)]
		public string  effectName   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ChangeCameraDirection : TimeEventProType
	{
		
		[ProtoMember(1)]
		public Vector3  lookAtOffsetPos  ;
		[ProtoMember(2)]
		public Vector3  lookAtOffsetAngle  ;
		[ProtoMember(3)]
		public int  toFov   = 50;
		[ProtoMember(4)]
		public float  resetTime   = 1500f;
		[ProtoMember(5)]
		public float  specialLerpSpeed   = 0f;
		[ProtoMember(6)]
		public float  distanceLerpLimit   = 0f;
		[ProtoMember(7)]
		public bool  useSpecialSpeed   = false;
		[ProtoMember(8)]
		public bool  resetToBack   = false;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ResetCameraToDefault : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  isReleaseLockTime   = true;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ResetEnemyUnlockTime : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class PhoneShake : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  count   = 1;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetArrayUnit : TimeEventProType
	{
		
		[ProtoMember(1)]
		public AFCValue  arrayValue  ;
		[ProtoMember(2)]
		public AFCValue  arrayIndex  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetArrayNext : TimeEventProType
	{
		
		[ProtoMember(1)]
		public AFCValue  arrayValue  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetHeroStar : TimeEventProType
	{
		
		[ProtoMember(1)]
		public CommandActionObject  actionObject  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetSelfGameObject : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetOwnerPos : TimeEventProType
	{
		
		[ProtoMember(1)]
		public CommandActionObject  actionObject  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetBcCell : TimeEventProType
	{
		
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = true)]
#endif
		[ProtoMember(1)]
		public string  getBcLink   = "";
		
		[ProtoMember(2)]
		public string  getGridLink   = "";
		
		[ProtoMember(3)]
		public bool    isGetBc = false;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetBcId : TimeEventProType
	{
		
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = true)]
#endif
		[ProtoMember(1)]
		public string  getBcLink   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetBeGraber : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetGraber : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class IsMe : TimeEventProType
	{
		
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = true)]
#endif
		[ProtoMember(1)]
		public string  judegeLink   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetSkillTarget : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  findRange   = 1;
		[ProtoMember(2)]
		public int  originSkillId   = 0;
		[ProtoMember(3)]
		public bool  isNeedCurSkillTargetNeighborEmptyCell   = false;
		[ProtoMember(4)]
		public GetSkillTargetFindType  findTypeEnum   = GetSkillTargetFindType.Enum_random;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetCellPos : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  useTargetUnit   = false;
		[ProtoMember(2)]
		public bool  isValidGridUnit   = true;
		[ProtoMember(3)]
		public CommandActionObject  actionObject  ;
		[ProtoMember(4)]
		public GetCellPosTargetPos  targetPosEnum   = GetCellPosTargetPos.Enum_None;
		[ProtoMember(5)]
		public GetCellPosDirectionType  directionTypeEnum   = GetCellPosDirectionType.Enum_default;
		[ProtoMember(6)]
		public GetCellPosDirectionType2  directionType2Enum   = GetCellPosDirectionType2.Enum_front;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SetTarget : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  isRootOwner   = false;
		[ProtoMember(2)]
		public bool  canSetNull   = false;
		[ProtoMember(3)]
		public bool  isSetHitDataLocateTarget   = false;
		[ProtoMember(4)]
		public AFCValue  target  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SetSkillTarget : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  originSkillId   = 0;
		[ProtoMember(2)]
		public bool  canSetNull   = false;
		[ProtoMember(3)]
		public AFCValue  target  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class AutoReSetSkillTarget : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  forceReset   = false;
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = false)]
#endif
		[ProtoMember(2)]
		public string  setCompleteLink   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class FindTarget : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  NeedFillCount   = false;
		[ProtoMember(2)]
		public bool  repeatTarget   = false;
		[ProtoMember(3)]
		public bool  IncludeSelfUnit   = false;
		[ProtoMember(4)]
		public bool  UseRealPosition   = false;
		[ProtoMember(5)]
		public bool  IncludeCurrentTarget   = true;
		[ProtoMember(6)]
		public bool  isFindFriend   = false;
		[ProtoMember(7)]
		public AFCValue  Radius  ;
		[ProtoMember(8)]
		public AFCValue  Count  ;
		[ProtoMember(9)]
		public FindTargetSortType  sortTypeEnum   = FindTargetSortType.Enum_nothing;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class FindSkillTarget : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  UseProjetileAsReleaserIfHas   = false;
		[ProtoMember(2)]
		public bool  useSrcSelectTargetParamStr   = false;
		[ProtoMember(3)]
		public TDRConfig.TARGET_SELECT_TYPE  selectTargetType  ;
		[ProtoMember(4)]
		public TargetSelectExtendType  selectTargetExtendParam  ;
		[ProtoMember(5)]
		public AFCValue[]  findParams  ;
		[ProtoMember(6)]
		public string  selectTargetParamStr   = "";
		[ProtoMember(7)]
		public bool  isReturnArray   = true;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class FindCurrentSkillTarget : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetCurrentTarget : TimeEventProType
	{
		
		[ProtoMember(1)]
		public CommandActionObject  actionObject  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class FindAITarget : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetActionTriggerRole : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetHitRole : TimeEventProType
	{
		
		[ProtoMember(1)]
		public EffectTarget  targetEnum   = EffectTarget.Enum_self;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetSummoner : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetRoleSpeed : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  IsGenericNomalAttackAction   = true;
		[ProtoMember(2)]
		public GetRoleSpeedSpeedType  SpeedTypeEnum   = GetRoleSpeedSpeedType.Enum_normal_attack;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class AddBuff : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  targetGetNotWithCaster   = false;
		[ProtoMember(2)]
		public bool  isProjectileUse   = false;
		[ProtoMember(3)]
		public bool  useHeroStarLevel   = true;
		[ProtoMember(4)]
		public bool  skillSwitchRemove   = false;
		[ProtoMember(5)]
		public bool  useHitContext   = false;
		[ProtoMember(6)]
		public CommandActionObject  actionObject  ;
		[ProtoMember(7)]
		public AFCValue  buffID  ;
		[ProtoMember(8)]
		public AFCValue  buffDuration  ;
		[ProtoMember(9)]
		public AFCValue  buffLayerCount  ;
		[ProtoMember(10)]
		public EffectTarget  targetEnum   = EffectTarget.Enum_self;
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = true)]
#endif
		[ProtoMember(11)]
		public string  customTargetLink   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class RemoveBuff : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  deleteNum   = 1;
		[ProtoMember(2)]
		public bool  isProjectileUse   = false;
		[ProtoMember(3)]
		public AFCValue  buffID  ;
		[ProtoMember(4)]
		public EffectTarget  targetEnum   = EffectTarget.Enum_self;
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = true)]
#endif
		[ProtoMember(5)]
		public string  customTargetLink   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class BatchLaunchProjectile : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  isRelaunch   = false;
		[ProtoMember(2)]
		public EffectTarget  targetEnum   = EffectTarget.Enum_self;
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = false)]
#endif
		[ProtoMember(3)]
		public string  customTargetLink   = "";
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = false)]
#endif
		[ProtoMember(4)]
		public string  lauchLink   = "";
		
		[ProtoMember(5)]
		public string[]  launchFailLinks = null;
		
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class TansheGroup : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  TotalCount   = 0;
		[ProtoMember(2)]
		public int  DelayCount   = 0;
		[ProtoMember(3)]
		public int  radius   = 0;
		[ProtoMember(4)]
		public bool  isDynamicSet   = false;
		[ProtoMember(5)]
		public bool  targetRepeat   = true;
		[ProtoMember(6)]
		public bool  isSortByHit   = true;
		[ProtoMember(7)]
		public bool  AndSortByDistance   = false;
		[ProtoMember(8)]
		public AFCValue  TotalCount_d  ;
		[ProtoMember(9)]
		public AFCValue  radius_d  ;
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = false)]
#endif
		[ProtoMember(10)]
		public string  lauchLink   = "";
		[ProtoMember(11)]
		public string  targetLink   = "";
		[ProtoMember(12)]		
		public string[]  tansheCompleteLinks = null;
		[ProtoMember(13)]		
		public string[]  tansheFailLinks = null;
		
		[ProtoMember(14)]
		public AFCValue  maxCountOfProjectile ;
		
		[ProtoMember(15)]
		public bool  isSortByProjectileHit   = false;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ProjectileChangeHitData : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  hitId   = 0;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class HeroSelfChessDataClear : TimeEventProType
	{
		
		[ProtoMember(1)]
		public TDRConfig.ClearType  clearType  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SetBuffStatus : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  isSet   = true;
		[ProtoMember(2)]
		public BuffStatusEnum  statusEnum   = BuffStatusEnum.Enum_Lock_Mp;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SkillGridVertexClear : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ProjectileSaveSelfChessDataGridVertex : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ProjectileClearSelfChessDataGridVertex : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SwitchHeroStar : TimeEventProType
	{
		
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Output(name = "outlink", allowMultiple = true)]
#endif
		[ProtoMember(1)]
		public string[]  star_1_list  ;
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Output(name = "outlink", allowMultiple = true)]
#endif
		[ProtoMember(2)]
		public string[]  star_2_list  ;
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Output(name = "outlink", allowMultiple = true)]
#endif
		[ProtoMember(3)]
		public string[]  star_3_list  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetBFProjectileGroupNum : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  isDispose   = true;
		[ProtoMember(2)]
		public string  groupName   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class Add2BFProjectileGroup : TimeEventProType
	{
		
		[ProtoMember(1)]
		public string  groupName   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class RemoveBFProjectileGroup : TimeEventProType
	{
		
		[ProtoMember(1)]
		public string  groupName   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetSkillExtendLevel : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  skillExtendId   = 0;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetBuffLayer : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  buffProtoId   = 0;
		[ProtoMember(2)]
		public bool  isCheckCaster   = false;
		[ProtoMember(3)]
		public CommandActionObject  actionObject  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetActiveFetter : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  fetterId   = 0;
		[ProtoMember(2)]
		public CommandActionObject  actionObject  ;
		[ProtoMember(3)]
		public SpecClassType  specClassType  ;
		[ProtoMember(4)]
		public GetActiveFetterDataType  getDataTypeEnum   = GetActiveFetterDataType.Enum_Level;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class IsHasBuffTag : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  tag   = 0;
		[ProtoMember(2)]
		public CommandActionObject  actionObject  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SummonWaitHeroEvent : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  summonCfgId1   = 0;
		[ProtoMember(2)]
		public int  summonCfgId2   = 0;
		[ProtoMember(3)]
		public int  summonCfgId3   = 0;
		[ProtoMember(4)]
		public bool  needOpponent   = false;
		[ProtoMember(5)]
		public AFCInt  summonHeroStar  ;
		[ProtoMember(6)]
		public string  otherSelectLink   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ChangeAI : TimeEventProType
	{
		
		[ProtoMember(1)]
		public string  aiName   = "";
		
		
		[ProtoMember(2)]
		public bool  needAntiMatchFixing   = true;
	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class MultiExcuteEvent : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  isCalculate   = false;
		[ProtoMember(2)]
		public bool  isTiled   = false;
		[ProtoMember(3)]
		public bool  isCalculateActionSpeed   = false;
		[ProtoMember(4)]
		public AFCValue  excuteNum  ;
		[ProtoMember(5)]
		public AFCValue  interval  ;
		[ProtoMember(6)]
		public AFCValue  sum  ;
		[ProtoMember(7)]
		public string[]  linkName  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetRunningAIName : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetMergeHeroLevel : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ChangeColor : TimeEventProType
	{
		
#if ACGGAME_CLIENT

		public bool  isParticleSystem   = false;
		public string  componentName   = "";

#endif

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckFindTarget : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SetSkillReleaseTagConst : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  useActionTag   = false;
		[ProtoMember(2)]
		public AFCValue  value  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetSkillReleaseTagConst : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class StartSkillLock : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  time   = 0;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class EndSkillLock : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  isOnlyExecuteTrigger   = true;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ReleaseSkillTrigger : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  actionId   = 0;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class RefreshBattleFieldFetters : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ControlSkillTarget : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  moveTime   = 60;
		[ProtoMember(2)]
		public int  moveCheckDis   = 0;
		[ProtoMember(3)]
		public CommandActionObject  actionObject  ;
		[ProtoMember(4)]
		public ControlTargetMoveType  moveTypeEnum   = ControlTargetMoveType.Enum_none;
		[ProtoMember(5)]
		public ControlTargetDirectionType  directionTypeEnum   = ControlTargetDirectionType.Enum_none;
		[ProtoMember(6)]
		public string  moveCompleteAction   = "";
		[ProtoMember(7)]
		public string  doAction   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class MoveToNearestGrid : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  moveFrame   = 0;
		[ProtoMember(2)]
		public int  moveSpeed   = 0;
		[ProtoMember(3)]
		public bool  autoDirection   = false;
		[ProtoMember(4)]
		public EffectTarget  targetEnum   = EffectTarget.Enum_self;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ChangeActionSpeed : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  changeViewSpeed   = false;
		[ProtoMember(2)]
		public AFCValue  Speed  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class FindProjectileTarget : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetProjectile : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  getCondition   = false;
		[ProtoMember(2)]
		public GetProjectileType  getTypeEnum   = GetProjectileType.Enum_ONE;
		[ProtoMember(3)]
		public string  keyName  ;
		[ProtoMember(4)]
		public ValueTypeEnum  checkValueTypeEnum   = ValueTypeEnum.Enum_int;
		[ProtoMember(5)]
		public CompairisonOperatorEnum  operatorTypeEnum   = CompairisonOperatorEnum.Enum_greaterThan;
		[ProtoMember(6)]
		public AFCValue  value  ;
		[ProtoMember(7)]
		public string  projectileTarget   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckIsCanKillTarget : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  hitDataId   = 0;
		[ProtoMember(2)]
		public CommandActionObject  actionObject  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckHeroIds : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  isBasic   = false;
		[ProtoMember(2)]
		public CommandActionObject  actionObject  ;
#if ACGGAME_CLIENT && !LOGIC_THREAD 
		[Input(name = "inlink", allowMultiple = true)]
#endif
		[ProtoMember(3)]
		public string[]  heroIdArr  ;
		[ProtoMember(4)]
		public string  heroId   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckNeighborHasTargets : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  range   = 1;
		[ProtoMember(2)]
		public TDRConfig.EFFECT_TARGET  effectTarget  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetTargetAttribute : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  isOrg   = false;
		[ProtoMember(2)]
		public TDRConfig.ATTRIBUTE_DEF  attributeName   = 0;
		[ProtoMember(3)]
		public CommandActionObject  actionObject  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetProjectileProperty : TimeEventProType
	{
		
		[ProtoMember(1)]
		public ProjectilePropertyEnum  propertyNameEnum   = ProjectilePropertyEnum.Enum_hitLife;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SetProjectileProperty : TimeEventProType
	{
		
		[ProtoMember(1)]
		public AFCValue  propertyValue  ;
		[ProtoMember(2)]
		public ProjectilePropertyEnum  propertyNameEnum   = ProjectilePropertyEnum.Enum_hitLife;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class IsSkillTargetValid : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class IsTargetBcValid : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class IsCellValid : TimeEventProType
	{
		
		[ProtoMember(1)]
		public string  cellLink   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckSummonState : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  AliveCount   = 1;
		[ProtoMember(2)]
		public CheckSummonStateType  SummonStateEnum   = CheckSummonStateType.Enum_Death;
		[ProtoMember(3)]
		public CheckSummonStateCompareCondition  CompareConditionEnum   = CheckSummonStateCompareCondition.Enum_Greater;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetSummon : TimeEventProType
	{
		
		[ProtoMember(1)]
		public GetSummonType  getSummonTypeEnum   = GetSummonType.Enum_One;
		[ProtoMember(2)]
		public int  heroID   = 0;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckAttackDistance : TimeEventProType
	{
		
		[ProtoMember(1)]
		public SelectTargetEnum  selectTargetEnum   = SelectTargetEnum.Enum_currentTarget;
		[ProtoMember(2)]
		public string  targetName   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckIsHomeHero : TimeEventProType
	{
		
		[ProtoMember(1)]
		public CommandActionObject  actionObject  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetBattleFieldState : TimeEventProType
	{
		
		[ProtoMember(1)]
		public BattleFieldPropertyEnum  propertyType   = BattleFieldPropertyEnum.Enum_Battle_State;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetGridUnit : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  mustRole   = true;
		[ProtoMember(2)]
		public bool  needEmpty   = true;
		[ProtoMember(3)]
		public GetGridUnitGridType  getGridTypeEnum   = GetGridUnitGridType.Enum_EmptyUnitByOutRange;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckPosOutGrid : TimeEventProType
	{
		
		[ProtoMember(1)]
		public float  outDis   = 0.0f;
		[ProtoMember(2)]
		public CommandActionObject  actionObject  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetGeneric : TimeEventProType
	{
		
		[ProtoMember(1)]
		public CommandActionObject  actionObject  ;
		[ProtoMember(2)]
		public GenericType  genericTypeEnum   = GenericType.Enum_nothing;
		[ProtoMember(3)]
		public AFCValue[]  dataParams  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class CheckGeneric : TimeEventProType
	{
		
		[ProtoMember(1)]
		public CommandActionObject  actionObject  ;
		[ProtoMember(2)]
		public ConditionGenericType  genericTypeEnum   = ConditionGenericType.Enum_nothing;
		[ProtoMember(3)]
		public AFCValue[]  dataParams  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ExecuteGeneric : TimeEventProType
	{
		
		[ProtoMember(1)]
		public CommandActionObject  actionObject  ;
		[ProtoMember(2)]
		public ExecuteGenericType  genericTypeEnum   = ExecuteGenericType.Enum_nothing;
		[ProtoMember(3)]
		public AFCValue[]  dataParams  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class ExecuteGenericView : TimeEventProType
	{
		
		[ProtoMember(1)]
		public CommandActionObject  actionObject  ;
		[ProtoMember(2)]
		public GenericViewType  genericTypeEnum   = GenericViewType.Enum_nothing;
		[ProtoMember(3)]
		public AFCValue[]  dataParams  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetBattleRecordGridX : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  recordKey   = 0;
		[ProtoMember(2)]
		public bool  isX   = false;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetBattleRecordGridY : TimeEventProType
	{
		
		[ProtoMember(1)]
		public int  recordKey   = 0;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class IsDebonairVIP : TimeEventProType
	{
		
		[ProtoMember(1)]
		public CommandActionObject  actionObject  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetResultBySymbol : TimeEventProType
	{
		
		[ProtoMember(1)]
		public CommandActionObject  actionObject1  ;
		[ProtoMember(2)]
		public CommandActionObject  actionObject2  ;
		[ProtoMember(3)]
		public AFCValue  symbolContext  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetCelebrateEffect : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetSkillId_Zoe_S7 : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class RefreshSkill_Zoe_S7 : TimeEventProType
	{
		

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class DoViewContainerThing : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  ignoreShowInBattleField   = false;
		[ProtoMember(2)]
		public DoViewContainerThingEnum  thingType   = DoViewContainerThingEnum.Enum_playAnimation;
		[ProtoMember(3)]
		public AFCValue[]  dataParams  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class SetGridState : TimeEventProType
	{
		
		[ProtoMember(1)]
		public bool  occupyOrRelease   = false;
		[ProtoMember(2)]
		public CommandActionObject  actionObject  ;
		[ProtoMember(3)]
		public GetOccupyGridType  controlCountEnum   = GetOccupyGridType.Enum_ALL;
		[ProtoMember(4)]
		public string  cellLink   = "";

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetUnitProperty : TimeEventProType
	{
		
		[ProtoMember(1)]
		public CommandActionObject  actionObject  ;
		[ProtoMember(2)]
		public GetUnitPropertyType  unitPropertyEnum   = GetUnitPropertyType.Enum_Nothing;
		
		[ProtoMember(3)]
		public bool  isTinyHero   = false;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class GetNewArray : TimeEventProType
	{
		
		[ProtoMember(1)]
		public AFCValue[]  arrayValueList  ;

	}
	//CommandModelEnd//CommandModelStart
	[System.Serializable]
[ProtoContract]
	public class WaitPosControl : TimeEventProType
	{
		
		[ProtoMember(1)]
		public WaitPosControlType  ctrlType   = WaitPosControlType.Enum_Lock;
		[ProtoMember(2)]
		public AFCValue  param_1  ;
		[ProtoMember(3)]
		public AFCValue  param_2  ;

	}
	//CommandModelEnd

}