using GameFramework.FMath;
using ProtoBuf;
using System;
using System.Text;

namespace Lucifer.ActCore
{
    [System.Serializable]
[ProtoContract]
[ProtoInclude(tag:2000,knownType:typeof(Lucifer.ActCore.AFCValue))]
[ProtoInclude(tag:2001,knownType:typeof(Lucifer.ActCore.VariableData))]
    public class BaseVariableData
    {
        /**
         * 变量的值
         */
        [ProtoMember(1)]
        public int variableValueint;

        /**
         * 变量的值
         */
        [ProtoMember(2)]
        public float variableValuefloat;
        
        [System.NonSerialized] 
        public Fix64 variableValuefixed;

        /**
         * 变量的值
         */
        [ProtoMember(3)]
        public uint variableValueuint;

        /**
         * 变量的值
         */
        [ProtoMember(4)]
        public bool variableValuebool;
        /**
         * 类型
         */
        [ProtoMember(5)]
        public string variableType = "";

        /**
         * 变量的值
         */
        [ProtoMember(6)]
        public string variableValuestring;
#if ACGGAME_CLIENT && !LOGIC_THREAD
        /**
         * 变量的值
         */
        [System.NonSerialized]
        public UnityEngine.GameObject variableValueGameObject;
#endif


        /**
        * 变量的值
        */
        [System.NonSerialized] public IBaseActionRunnerAdapter variableValueActionRunner;

        [System.NonSerialized] public GridOffset variableValueGridOffset;
        [System.NonSerialized] public FVector3 variableValueFVector3;

        /// <summary>
        /// 数组
        /// </summary>
        [System.NonSerialized]
        public AFCArray variableValueArray;

        static BaseVariableData()
        {
            InitSpecializerType.Init();
        }

        public bool isNull()
        {
            return string.IsNullOrEmpty(variableType);
        }
        
        #region 转换为Fix64
        
        public Fix64 GetValueToFix64()
        {
            Fix64 result = Fix64.zero;
            switch (this.variableType)
            {
                case AFCValue.int_type:
                    result = this.variableValueint.ToFix64();
                    break;    
                case AFCValue.float_type:
                    result = this.variableValuefloat.ToFix64();
                    break;
                case AFCValue.uint_type:
                    result = this.variableValueuint.ToFix64();
                    break;
                case AFCValue.fix64_type:
                    result = this.variableValuefixed;
                    break;
                
            }

            return result;
        }
        
        #endregion

        #region 转换为String
        
        public string GetValueToString()
        {
            switch (this.variableType)
            {
                case AFCValue.int_type:
                {
                    return this.variableValueint.ToString();
                }
                    break;
                case AFCValue.float_type:
                {
                    return this.variableValuefloat.ToString();
                }
                    break;
                
                case AFCValue.fix64_type:
                {
                    return this.variableValuefixed.ToString();
                }
                    break;
                case AFCValue.bool_type:
                {
                    return this.variableValuebool.ToString();
                }
                    break;
                case AFCValue.string_type:
                {
                    return this.variableValuestring;
                }
                    break;
                case AFCValue.uint_type:
                {
                    return this.variableValueuint.ToString();
                }
                    break;
                case AFCValue.ActionRunner_type:
                {
                    return this.variableValueActionRunner == null?"null":this.variableValueActionRunner.ToString();
                }
                    break;
                case AFCValue.Array_type:
                {
                    StringBuilder sb = new StringBuilder();
                    if (this.variableValueArray != null || this.variableValueArray.arrayValue != null || this.variableValueArray.arrayValue.Length > 0)
                    {
                        for (int i = 0, len = this.variableValueArray.arrayValue.Length; i < len; i++)
                        {
                            AFCValue tmpAFCValue = this.variableValueArray.arrayValue[i];
                            if (tmpAFCValue != null)
                            {
                                sb.Append(tmpAFCValue.GetValueToString());
                                if (i + 1 < len)
                                {
                                    sb.Append("$");
                                }
                            }
                           
                        }
                    }
                    else
                    {
                        sb.Append("空数组");
                    }

                    
                    return sb.ToString();
                }
                    break;
                case AFCValue.FVector3_type:
                {
                    return this.variableValueFVector3.ToString();
                }
                    break;
                case AFCValue.GridOffset_type:
                {
                    //return this.variableValueGridOffset.ToString();
                    return this.variableValueGridOffset == null?"null":this.variableValueGridOffset.ToString();
                }
                    break;
#if ACGGAME_CLIENT && !LOGIC_THREAD
                case AFCValue.Gameobject_type:
                {
                  return this.variableValueGameObject.ToString();
                }               
                 break;
#endif
            }

            return null;
        }
        
        #endregion
        
        #region Object类型
        
        
        
        
        
        #endregion
    }

    #region GridOffset
    [Serializable]
    public class GridOffset
    {
        public static GridOffset Zero = new GridOffset();
        public int row;
        public int col;

        public GridOffset()
        {
        }

        public GridOffset(int row, int col)
        {
            this.row = row;
            this.col = col;
        }

        public GridOffset Clone()
        {
            GridOffset gridOffset = new GridOffset();
            gridOffset.SetData(this);
            return gridOffset;
        }

        public void SetData(GridOffset value)
        {
            this.row = value.row;
            this.col = value.col;
        }

        public void SetData(int row, int col)
        {
            this.row = row;
            this.col = col;
        }

        //这样就可以使用list.contains, indexOf查找了;
        public override bool Equals(object obj)
        {
            GridOffset gridOffset = (GridOffset)obj;
            if (gridOffset != null && gridOffset.row == this.row && gridOffset.col == this.col)
            {
                return true;
            }

            return false;
        }

        public override int GetHashCode()
        {
            return row.GetHashCode() + col.GetHashCode();
        }

        public int Value
        {
            get { return this.row * 100 + col; }
        }

        public override string ToString()
        {
            return this.row + "," + this.col;
        }
    }
    #endregion
}