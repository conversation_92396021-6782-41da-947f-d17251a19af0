#if TKF_EDITOR && (TKF_ALL_EXTEND)//TKFrame Auto Gen
using System;
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Reflection;
using System.Text;
using Microsoft.Win32;
using UnityEditor;

public class ResourceCheckUtility
{

    /// <summary>
    /// 将menSize转换为MB显示
    /// </summary>
    /// <param name="memSizeKB"></param>
    /// <returns></returns>
    public static string FormatSizeString(int memSizeKB)
    {
        if (memSizeKB < 1024) return "" + memSizeKB + "k";
        else
        {
            float memSizeMB = ((float)memSizeKB) / 1024.0f;
            return memSizeMB.ToString("0.00") + "Mb";
        }
    }

    /// <summary>
    /// 判断当前给定路径是否是一个合法的Texture
    /// </summary>
    /// <param name="filePath"></param>
    /// <returns></returns>
    public static bool IsValidTextureFile(string filePath)
    {
        int nExtNameStart = filePath.LastIndexOf('.');
        if (nExtNameStart > 0)
        {
            string extName = filePath.Substring(nExtNameStart);
            if (
                (string.Compare(extName, ".png", true) == 0) ||
                (string.Compare(extName, ".tga", true) == 0) ||
                (string.Compare(extName, ".jpg", true) == 0)
                )
            {
                return true;
            }
        }

        return false;
    }


    /// <summary>
    /// 写注册表
    /// </summary>
    /// <param name="key"></param>
    /// <param name="value"></param>
    public static void WriteRegistry(string keyPath,string key, string value)
    {
        RegistryKey lm = Registry.LocalMachine;
        //对应HKEY_LOCAL_MACHINE基项分支
        RegistryKey software = lm.OpenSubKey("SOFTWARE", true);
        RegistryKey TUT_ResourceCheck = null;
        RegistryKey TUT_ResourceCheck_Path = null;
        //如果不存在TUT_ComponentCopy项，则创建该项
        try
        {
            TUT_ResourceCheck = lm.OpenSubKey("TUT_ResourceCheck", true);
            //试图打开不存在的项TUT_ComponentCopy
            if (TUT_ResourceCheck == null)
            {
                TUT_ResourceCheck = software.CreateSubKey("TUT_ResourceCheck");
            }
            TUT_ResourceCheck_Path = TUT_ResourceCheck.OpenSubKey(keyPath,true);
            if (TUT_ResourceCheck_Path == null)
            {
                TUT_ResourceCheck_Path = TUT_ResourceCheck.CreateSubKey(keyPath);
            }
        }
        catch (Exception)
        {
            Console.WriteLine("An Error happened when create registrykey");
        }
        //清空注册表中的数据
        
        //写数据
        TUT_ResourceCheck_Path.SetValue(key,value);
        //关闭注册表
        TUT_ResourceCheck.Close();
        TUT_ResourceCheck_Path.Close();
        software.Close();
        lm.Close();
    }
    /// <summary>
    /// 读取注册表中指定的key的值
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public static string ReadRegistry(string keyPath,string keyValue)
    {
        string value = "";
        //注册表操作
        RegistryKey lm = Registry.LocalMachine;
        //对应HKEY_LOCAL_MACHINE基项分支
        RegistryKey software = lm.OpenSubKey("SOFTWARE", true);
        RegistryKey TUT_ResourceCheck = software.OpenSubKey("TUT_ResourceCheck", true);
        
        if (TUT_ResourceCheck != null)
        {
            //
            RegistryKey TUT_ResouceCheck_Path = TUT_ResourceCheck.OpenSubKey(keyPath, true);
            if (TUT_ResouceCheck_Path != null)
            {
                //读取值
                value = TUT_ResouceCheck_Path.GetValue(keyValue) as string;
            }
            else
            {
                return "";
            }
        }
        else
        {
            return "";
        }
        return value;
    }

}

public class WindowHelper
{
    public static int SHOW_WINDOW_WIDTH = 800;
    public static int ROW_HEIGHT = 25;
    public static int ROW_SHOW_NUM = 20;

    /// <summary>
    /// 显示列表的头部
    /// </summary>
    /// <param name="Name">要显示的头部的内容</param>
    /// <param name="Ratio">每一列显示所占用的比例</param>
    public static void ShowHeaderList(string[] Name, float[] Ratio,bool isTexture = false)
    {
        //显示检查结果列表
        if(!isTexture)
        {
            EditorGUILayout.BeginHorizontal();
            GUILayout.Space(20);
        }
      
        GUIStyle toolbarDropDownStyle = new GUIStyle(EditorStyles.whiteLabel);
        toolbarDropDownStyle.fixedHeight = 30;
        toolbarDropDownStyle.fontSize = 12;
        toolbarDropDownStyle.alignment = TextAnchor.UpperLeft;

        for (int i = 0; i < Name.Length; i++)
        {
            GUILayout.Label(Name[i], toolbarDropDownStyle,GUILayout.Width(80));
            if (i != Name.Length - 1)
            {
                GUILayout.Space(Ratio[i] * WindowHelper.SHOW_WINDOW_WIDTH - 80 + 10);
            }
        }

        //显示检查结果列表
        if (!isTexture)
        {
            EditorGUILayout.EndHorizontal();
        }   
    }

    public delegate void ScrollViewContent(int FirstIndex);//委托，显示ScrollView中的内容
    /// <summary>
    /// 快速显示ScrollView，解决数据量大时，ScrollView卡壳的问题
    /// </summary>
    /// <param name="_scrollPosition">scrollView的位置</param>
    /// <param name="count">要显示的数量的总量</param>
    /// <param name="scrollViewContent">委托函数，执行真正的显示内容工作</param>
    public static void FastScrollView(ref Vector2 _scrollPosition, int count, ScrollViewContent scrollViewContent)
    {
        _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition, GUIStyle.none, GUI.skin.verticalScrollbar, GUILayout.ExpandWidth(true), GUILayout.ExpandHeight(true));
        _scrollPosition.y = Mathf.Clamp(_scrollPosition.y, 0, count * WindowHelper.ROW_HEIGHT);
        int FirstIndex = (int)(_scrollPosition.y / WindowHelper.ROW_HEIGHT);//获取第一个显示的列表
        FirstIndex = Mathf.Clamp(FirstIndex, 0, Mathf.Max(0, count - WindowHelper.ROW_SHOW_NUM));
        GUILayout.Space(FirstIndex * WindowHelper.ROW_HEIGHT);//把FirstIndex之前的数据都显示为空白
        scrollViewContent(FirstIndex);
        GUILayout.Space(Mathf.Max(0, (count - FirstIndex - WindowHelper.ROW_SHOW_NUM) * WindowHelper.ROW_HEIGHT));
        EditorGUILayout.EndScrollView();
    }
    /// <summary>
    /// 带条件显示一列内容
    /// </summary>
    public static void ShowOneCollumnWithCond(bool illegal, string showInfo, float ratio, GUIStyle guiStyle)
    {
        if (illegal)
        {
            GUILayout.Label(showInfo, guiStyle, GUILayout.Width(ratio * WindowHelper.SHOW_WINDOW_WIDTH));
        }
        else
        {
            GUILayout.Label(showInfo, GUILayout.Width(ratio * WindowHelper.SHOW_WINDOW_WIDTH));
        }
    }
    /// <summary>
    /// 带图片显示一列内容
    /// </summary>
    public static void ShowOneCollumnWithPic(string picName, string fileName, float ratio)
    {
        GUILayout.Label(EditorGUIUtility.FindTexture(picName), GUILayout.Width(17), GUILayout.Height(20));
        GUILayout.Label(fileName, GUILayout.Width(ratio * WindowHelper.SHOW_WINDOW_WIDTH - 17));
    }

}
#endif //TKFrame Auto Gen
