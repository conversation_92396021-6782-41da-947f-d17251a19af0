#if TKF_EDITOR && (TKF_ALL_EXTEND)//TKFrame Auto Gen
using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using System.Text;
using TUT;
using UnityEditor;

/// <summary>
/// 实现在GameObject上进行“选择性赋值粘贴”功能的类
/// </summary>
public class ObjectCopier : MonoBehaviour
{


    /// <summary>
    /// 增加对象上的右键菜单操作
    /// 获得选中对象的所有components的SerializedProperty
    /// </summary>
    /// 
    [MenuItem("Assets/选择复制Components",false,51)]
    [MenuItem("GameObject/选择复制Components %#C",false,21)]
    private static void CopyComponents()
    {
        TUTFuncLogger.GetInstance().Log(TUTFuncLogger.MODE_COMPONENTCOPY,TUTFuncLogger.FUNC_COMPONENTCOPY_OBJECT);
        if (!Selection.activeGameObject)
        {
            EditorUtility.DisplayDialog("Nothing selected", "没有选中对象(如果在Asset下，请选择prefab).", "确定");
            return;
        }

        Component[] components = Selection.activeGameObject.GetComponents<Component>();

        GlobalData.CopyGameObject = Selection.activeGameObject;
        //获得所有组件的serializedObjects
        GlobalData.SerializedObjectList = new SerializedObject[components.Length];
        GlobalData.ComponentNameList = new string[components.Length];
        for (int i = 0; i < components.Length; i++)
        {
            GlobalData.ComponentNameList[i] = components[i].GetType().Name;
#if !(UNITY_5_0||UNITY_5_1||UNITY_5_2) //4.6版本下，ParticleEmitter的显示需要单独处理
                if (components[i].GetType().Name.Equals("ParticleEmitter"))
                        {
                            //对于ParticleEmitter，决定显示的名称是Ellipsoid ParticleEmitter,还是Mesh ParticleEmitter
                            SerializedObject serializedObj = new SerializedObject(components[i]);
                            if (serializedObj.FindProperty("m_Systematic") != null)
                            {
                                GlobalData.ComponentNameList[i] = "Mesh Particle Emitter";
                            }
                            else 
                            {
                                GlobalData.ComponentNameList[i] = "Ellipsoid Particle Emitter";
                            }
                        }
             if (components[i].GetType().Name.Equals("Behaviour"))
            {
                //决定显示名称
                SerializedObject serializedObject = new SerializedObject(components[i]);
                if (serializedObject.FindProperty("m_Color") != null)
                {
                    // 
                    GlobalData.ComponentNameList[i] = "Halo";
                }
                else
                {
                    GlobalData.ComponentNameList[i] = "Flare Layer";
                }
            }
#endif

            GlobalData.SerializedObjectList[i] = new SerializedObject(components[i]);
        }

        //获得所有组件的serializedProperties
        GlobalData.SerializedPropertyIterList = new SerializedProperty[components.Length];
        for (int i = 0; i < GlobalData.SerializedObjectList.Length; i++)
        {
            GlobalData.SerializedPropertyIterList[i] = GlobalData.SerializedObjectList[i].GetIterator();
        }

        //创建并显示一个窗口
        EditorWindow.GetWindow(typeof(ObjectCopierWindow), true, "选择需要复制的组件和属性", true);

    }

    /// <summary>
    /// 实现对components的粘贴操作
    /// </summary>
    [MenuItem("Assets/选择粘贴Components",false,52)]
    [MenuItem("GameObject/选择粘贴Components %#V", false,22)]
    public static void PasteComponents()
    {
        TUTFuncLogger.GetInstance().Log(TUTFuncLogger.MODE_COMPONENTCOPY,TUTFuncLogger.FUNC_COMPONENTCOPY_PASTE);
        if (!Selection.activeGameObject)
        {
            EditorUtility.DisplayDialog("Nothing selected", "没有选中对象（如果在Asset目录下，请选择prefab）.", "确定");
            return;
        }

        if (GlobalData.ToShowProperties != null || GlobalData.CopyDataList != null)
        {
            AddComponentsLack();
            PasteSelectedComponentsData();
            PasteHideProperty();
        }
        else
        {
            EditorUtility.DisplayDialog("没有复制", "请先复制，再粘贴", "确定");
            return;
        }
        
    }


    /// <summary>
    /// 完成对选择properties的copy功能
    /// </summary>
    private static void PasteSelectedComponentsData()
    {
        foreach (CopyData copyData in GlobalData.CopyDataList)
        {
            if (copyData.ComponetType.Equals("Component"))
            {
                //当前选中的是World Particle Collider
                EditorUtility.DisplayDialog("粘贴错误", "抱歉，暂不支持对World Particle Collider的支持，如果您有想法，可以联系我们！", "确定");
                continue;
            }
            Component componentToCopy = null;
#if !(UNITY_5_0||UNITY_5_1||UNITY_5_2)
            if (copyData.ComponetType.Equals("Mesh Particle Emitter"))
            {
                //componentToCopy = Selection.activeGameObject.GetComponent<ParticleEmitter>();
                Component[] componentCopys = Selection.activeGameObject.GetComponents<ParticleEmitter>();
                foreach (Component component in componentCopys)
                {
                    SerializedObject serializedObj = new SerializedObject(component);
                    if (serializedObj.FindProperty("m_Systematic") != null)
                    {
                        componentToCopy = component;
                    }
                }

            }
            else if (copyData.ComponetType.Equals("Ellipsoid Particle Emitter"))
            {
                Component[] componentCopys = Selection.activeGameObject.GetComponents<ParticleEmitter>();
                foreach (Component component in componentCopys)
                {
                    SerializedObject serializedObj = new SerializedObject(component);
                    if (serializedObj.FindProperty("m_Systematic") == null)
                    {
                        componentToCopy = component;
                    }
                }
            }
            else
            {
                componentToCopy = Selection.activeGameObject.GetComponent(copyData.ComponetType);
            }
#endif
#if (UNITY_5_0||UNITY_5_1||UNITY_5_2)
           componentToCopy = Selection.activeGameObject.GetComponent(copyData.ComponetType);
#endif

            //将选中数据copy到获得的componentToCopy中
            if (componentToCopy == null )
            {
                //弹出框提示用户，出错，检查是否存在冲突的component
                EditorUtility.DisplayDialog("粘贴错误", "创建属性时出错，请查看是否存在有冲突的属性，并删除;如果是Halo、Mesh Particle Emitter或者Ellipsoid Particle Emitter属性，请手动添加后再复制，谢谢！", "确定");
                return;
            }
            SerializedObject serializedObject = new SerializedObject(componentToCopy); 
            SerializedProperty serializedProperty = GlobalData.SerializedPropertyIterList[copyData.ComponentIndex];
            
            serializedObject.Update();
            serializedProperty.Next(true);
            int tempIndex = 0;
            
            while (serializedProperty.NextVisible(true))
            {
                if (copyData.ProperitiesIndex.Contains(tempIndex))
                {
                    //不是Rotation类型的，直接采用SerializedProperty进行粘贴值
                    if(!IsTransformRotation(serializedProperty,ref componentToCopy))
                        serializedObject.CopyFromSerializedProperty(serializedProperty);
                }
                tempIndex++;
            }
            serializedObject.ApplyModifiedProperties();
            serializedObject.SetIsDifferentCacheDirty();
            serializedProperty.Reset();
            //这里检测是否是Rotation值，如果是，则粘贴
            CopyTransformRotationData(ref serializedProperty,ref componentToCopy,copyData);
            
        }
    }

    private static void CopyTransformRotationData(ref SerializedProperty serializedProperty, ref Component transformComponent, CopyData copyData)
    {
        Transform transform = transformComponent as Transform;
        if (transform == null)
            return ;
        if (GlobalData.CopyGameObject == null)
            return ;
        Transform sourceTransform = GlobalData.CopyGameObject.GetComponent<Transform>();
        int tempIndex = 0;
        while (serializedProperty.NextVisible(true))
        {
            if (copyData.ProperitiesIndex.Contains(tempIndex))
            {
                if (serializedProperty.propertyPath.Equals("m_LocalRotation"))
                {
                    //粘贴所有属性值
                    Vector3 vector3 = new Vector3();
                    //Debug.Log(sourceTransform.localEulerAngles.x + "+" + sourceTransform.localEulerAngles.y + "+" + sourceTransform.localEulerAngles.z);
                    vector3.x = sourceTransform.localEulerAngles.x;
                    vector3.y = sourceTransform.localEulerAngles.y;
                    vector3.z = sourceTransform.localEulerAngles.z;
                    transform.localEulerAngles = vector3;
                }
                else if (serializedProperty.propertyPath.Equals("m_LocalRotation.x"))
                {
                    Vector3 vector3 = new Vector3();
                    vector3.x = sourceTransform.localEulerAngles.x;
                    vector3.y = transform.localEulerAngles.y;
                    vector3.z = transform.localEulerAngles.z;
                    transform.localEulerAngles = vector3;
                }
                else if (serializedProperty.propertyPath.Equals("m_LocalRotation.y"))
                {
                    Vector3 vector3 = new Vector3();
                    vector3.x = transform.localEulerAngles.x;
                    vector3.y = sourceTransform.localEulerAngles.y;
                    vector3.z = transform.localEulerAngles.z;
                    transform.localEulerAngles = vector3;
                }
                else if (serializedProperty.propertyPath.Equals("m_LocalRotation.z"))
                {
                    Vector3 vector3 = new Vector3();
                    vector3.x = transform.localEulerAngles.x;
                    vector3.y = transform.localEulerAngles.y;
                    vector3.z = sourceTransform.localEulerAngles.z;
                    transform.localEulerAngles = vector3;
                }
            }
            tempIndex++;
        }
        serializedProperty.Reset();
    }

    private static bool IsTransformRotation(SerializedProperty serializedProperty,ref Component transformComponent)
    {
        Transform transform = transformComponent as Transform;
        if (transform == null)
            return false;
        if (GlobalData.CopyGameObject == null)
            return false;
        if (serializedProperty.propertyPath.Equals("m_LocalRotation"))
        {
            return true;
        }
        else if (serializedProperty.propertyPath.Equals("m_LocalRotation.x"))
        {
            return true;
        }
        else if (serializedProperty.propertyPath.Equals("m_LocalRotation.y"))
        {
            return true;
        }
        else if (serializedProperty.propertyPath.Equals("m_LocalRotation.z"))
        {
            return true;
        }
        return false;
    }

    /// <summary>
    /// 完成对不可见得属性（不是通过SerializedProperty属性让其可见的）的paste功能
    /// </summary>
    private static void PasteHideProperty()
    {
        //添加当前gameobject中缺少的component组件
        foreach (ToShowProperty toShowProperty in GlobalData.ToShowProperties)
        {
            AddComponentsLack(toShowProperty.ComponentName);
        }

        foreach (ToShowProperty toShowProperty in GlobalData.ToShowProperties)
        {
            Component componentToCopy = Selection.activeGameObject.GetComponent(toShowProperty.ComponentName);
            SerializedObject serializedObjectToPaset = new SerializedObject(componentToCopy);
            SerializedObject serializedObject = GlobalData.SerializedObjectList[toShowProperty.ObjectIndex];
            SerializedProperty serializedProperty = serializedObject.FindProperty(toShowProperty.PropertyName);
            serializedObjectToPaset.Update();
            serializedObjectToPaset.CopyFromSerializedProperty(serializedProperty);
            serializedObjectToPaset.ApplyModifiedProperties();
            serializedObjectToPaset.SetIsDifferentCacheDirty();
        }
    }

    /// <summary>
    /// 添加不可见属性中缺少的组件
    /// </summary>
    /// <param name="componentName"></param>
    private static void AddComponentsLack(string componentName)
    {
        bool isExit = false;
        Component componentToCopy = Selection.activeGameObject.GetComponent(componentName);
        Component[] components = Selection.activeGameObject.GetComponents<Component>();
        for (int i = 0; i < components.Length; i++)
        {
            if (components[i].GetType().Name.Equals(componentToCopy.GetType().Name))
            {
                //当前object下存在目标Component
                isExit = true;
                break;
            }
        }
        if (isExit == false)
        {
            //当前缺少组件，需要将改组件增加上
            Selection.activeGameObject.AddComponent(componentToCopy.GetType());
            //UnityEngineInternal.APIUpdaterRuntimeServices.AddComponent(Selection.activeGameObject, "Assets/Component Copy New/Editor/ObjectCopier.cs (74,13)", name);
        }
    }

    /// <summary>
    /// 增加缺少的components组件
    /// </summary>
    private static void AddComponentsLack()
    {
        Component[] components = Selection.activeGameObject.GetComponents<Component>();
        //将当前选中对象缺少的component加上
        string[] componentStrings = new string[components.Length];
        for (int i = 0; i < componentStrings.Length; i++)
        {
            componentStrings[i] = components[i].GetType().Name;
        }
        if (GlobalData.RemainComponentNameList == null)
        {
            EditorUtility.DisplayDialog("没有复制", "请先复制，再粘贴", "确定");
            return;
        }
        var newAddComponents = GlobalData.RemainComponentNameList.Except(componentStrings);
        if (newAddComponents.Any())
        {
            foreach (string name in newAddComponents)
            {
                if (!name.Equals("Behaviour") && !name.Equals("Component") && !name.Equals("Mesh Particle Emitter") && !name.Equals("Ellipsoid Particle Emitter"))
                {
                    //弹出框提示用户，出错，检查是否存在冲突的component
#if !(UNITY_5_0||UNITY_5_1||UNITY_5_2)
                    UnityEngineInternal.APIUpdaterRuntimeServices.AddComponent(Selection.activeGameObject, "Assets/Editor/TUT/Editor/ComponentCopy/ObjectCopier.cs (351,21)", name);
#endif
#if UNITY_5_0||UNITY_5_1||UNITY_5_2
                    UnityEngineInternal.APIUpdaterRuntimeServices.AddComponent(Selection.activeGameObject, "Assets/TUT/ComponentCopy/Editor/ObjectCopier.cs", name);              
#endif

                }
                
            }
        }
    }

    /// <summary>
    /// 遍历SerializedProperty数组，得到其中的值
    /// </summary>
    /// <param name="serializedProperty">被遍历的property</param>
    private static void TraversalSerializedProperty(SerializedProperty serializedProperty)
    {
        serializedProperty.Next(true);
        while (serializedProperty.NextVisible(true))
        {
            //打印Component下所有序列化属性（即显示在Inspector上的所有属性）的名称和路径
            Debug.Log("Name = "+serializedProperty.displayName+" EndProperty = "+serializedProperty.propertyPath );
        }
    }

    [MenuItem("GameObject/生成UIObject代码到剪贴板(Ctrl+Alt+G) %&g", false, 20)]
    private static void GenUIObjectCode()
    {
        if (Selection.gameObjects == null)
        {
            EditorUtility.DisplayDialog("Nothing selected", "没有选中对象", "确定");
            return;
        }

        var sb = new System.Text.StringBuilder();
        foreach (var go in Selection.gameObjects)
        {
            bool haveUIComp = false;
            var comps = go.GetComponents<Component>();
            foreach (var comp in comps)
            {
                System.Type type = comp.GetType();
                if (type.Namespace == "UnityEngine.UI")
                {
                    GenOneUIObject(sb, type.Name, go.name);
                    haveUIComp = true;
                    break;
                }
            }

            if (!haveUIComp)
            {
                GenOneUIObject(sb, "GameObject", go.name);
            }
        }

        EditorGUIUtility.systemCopyBuffer = sb.ToString();;
        Debug.Log("已经生成UIObject代码到剪切板---------------------");
    }

    private static void GenOneUIObject(StringBuilder sb,string typeName, string name)
    {
        //[UIObject("BtnClose")]
        //public Button BtnClose
        //{
        //    get;
        //    set;
        //}
        sb.Append("[UIObject(\""+ name + "\")]\n");
        sb.Append("public "+ typeName + " "+ name + "\n");
        sb.Append("{\n");
        sb.Append("\tget;\n");
        sb.Append("\tset;\n");
        sb.Append("}\n");
        sb.Append("\n");
    }
}
#endif //TKFrame Auto Gen
