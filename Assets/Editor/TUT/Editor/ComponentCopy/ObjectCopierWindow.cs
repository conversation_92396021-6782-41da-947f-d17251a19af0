#if TKF_EDITOR && (TKF_ALL_EXTEND)//TKFrame Auto Gen
using System;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Win32;
using TUT;
using UnityEditor;


public class ObjectCopierWindow : EditorWindow {

    public PropertyNameModel ChangeModel;
    public SerializedDataModel SerializedModel;

    private Vector2 _scrollPosition; //存储scrollbar的位置
    private bool[] _objectSelected;
    private bool[] _showFirstChildProperty;//"属性列表"是否选中状态
    private bool[][] _allChildProperty;//存储所有property是否被选中
    private Dictionary<int, int>[] _beginAndEndPosition;//存储有层次关系的property的开始和结束位置 

    private string _objectName;


    private void OnEnable()
    {
        //对copy中要使用的数据进行初始化工作
        _objectSelected = new bool[GlobalData.SerializedObjectList.Length];
        _showFirstChildProperty = new bool[GlobalData.SerializedObjectList.Length];
        _allChildProperty = new bool[GlobalData.SerializedObjectList.Length][];
        _beginAndEndPosition = new Dictionary<int, int>[GlobalData.SerializedObjectList.Length];
        GlobalData.ToShowProperties = new List<ToShowProperty>();

        //初始化PropertyNameList
        SerializedModel = new SerializedDataModel(GlobalData.SerializedObjectList.Length);
        SerializedModel.InitPropertyNameSet(GlobalData.SerializedObjectList);

        for (int i = 0; i < _objectSelected.Length; i++)
        {
            _objectSelected[i] = false;
            _showFirstChildProperty[i] = false;
        }
        for (int i = 0; i < GlobalData.SerializedPropertyIterList.Length; i++)
        {
            _allChildProperty[i] = new bool[getPropertyNum(GlobalData.SerializedPropertyIterList[i])];
        }

        for (int i = 0; i < GlobalData.SerializedPropertyIterList.Length; i++)
        {
            getBeginAndEnd(GlobalData.SerializedPropertyIterList[i], i);
        }

        ChangeModel = new PropertyNameModel();
        //showChangeModel.InitChangeData();

        //得到树状信息
        SerializedModel.InitPropertyFullPath(GlobalData.SerializedPropertyIterList, GlobalData.ComponentNameList);
        //比较树状信息，替换propertyName中的属性
        SerializedModel.ChangePropertyShowRule(ChangeModel);
        //初始化“serializedproperty”中无法遍历的属性
        SerializedModel.InitToShowHideProperty();

        _objectName = Selection.activeGameObject.name;

        InitToogleStateByReg();
    }

    


    /// <summary>
    /// 获取每个component的property数量
    /// </summary>
    /// <param name="property"></param>
    /// <returns></returns>
    private int getPropertyNum(SerializedProperty property)
    {
        int num = 0;
        property.Next(true);
        while (property.NextVisible(true))
        {
            num++;
        }
        property.Reset();
        return num;
    }

    /// <summary>
    /// 获取一个component中的所有父子节点关系
    /// </summary>
    /// <param name="property"></param>
    /// <param name="componentIndex"></param>
    private void getBeginAndEnd(SerializedProperty property, int componentIndex)
    {
        //建立一个组件的所有属性关系
        int i = 0;
        Dictionary<int, string> BeginEndDictionary = new Dictionary<int, string>();
        _beginAndEndPosition[componentIndex] = new Dictionary<int, int>();

        property.Next(true);
        while (property.NextVisible(true))
        {
            if (property.hasVisibleChildren)
            {
                //有孩子节点，找到下一个不是孩子的节点
                SerializedProperty propertyEnd = property.GetEndProperty();
                if (String.IsNullOrEmpty(propertyEnd.propertyPath))
                {
                    BeginEndDictionary.Add(i, "copyend");
                }
                else
                {
                    BeginEndDictionary.Add(i, propertyEnd.propertyPath);
                }
                _beginAndEndPosition[componentIndex].Add(i, 0);
            }
            i++;
        }
        property.Reset();
        int j = 0;
        property.Next(true);
        while (property.NextVisible(true))
        {
            string FullPathName = property.propertyPath;
            if (BeginEndDictionary.Values.Contains(FullPathName))
            {
                //是一个孩子节点的结束
                foreach (KeyValuePair<int, string> pair in BeginEndDictionary)
                {
                    if (pair.Value.Equals(FullPathName))
                    {
                        //找到位置
                        _beginAndEndPosition[componentIndex][pair.Key] = j - 1;
                        break;
                    }
                }
            }
            j++;
        }
        if (BeginEndDictionary.Values.Contains("copyend"))
        {
            //是一个孩子节点的结束
            foreach (KeyValuePair<int, string> pair in BeginEndDictionary)
            {
                if (pair.Value.Equals("copyend"))
                {
                    //找到位置
                    _beginAndEndPosition[componentIndex][pair.Key] = j - 1;
                    break;
                }
            }
        }
        property.Reset();
    }

    /// <summary>
    /// 自动调用，完成copy窗口绘制工作
    /// </summary>
    private void OnGUI()
    {
        //创建可滚动的scrollbar
        _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition, GUILayout.ExpandWidth(true), GUILayout.ExpandHeight(true));
        //显示标题
        EditorGUILayout.TextField(_objectName+" 组件属性列表",EditorStyles.boldLabel);
        EditorGUILayout.Space();

        ShowPropertyList();

        EditorGUILayout.EndScrollView();
        EditorGUILayout.BeginHorizontal();
        ShowCopyButton();
        //一键取消所有选中的属性,并清空注册表
        ClearToogleButton();
        EditorGUILayout.EndHorizontal();
    }

#if UNITY_5_0||UNITY_5_1||UNITY_5_2
    private void OnLostFocus()
    {
        this.Close();
    }
#endif

    /// <summary>
    /// 被OnGUI调用，是显示property的入口
    /// </summary>
    private void ShowPropertyList()
    {
        for (int i = 0; i < GlobalData.SerializedPropertyIterList.Length; i++)
        {
            _showFirstChildProperty[i] = EditorGUILayout.Foldout(_showFirstChildProperty[i], "属性列表 (" + ChangeComponentName(GlobalData.ComponentNameList[i]) + " )");
            
            
            //显示孩子列表
            if (_showFirstChildProperty[i])
            {
                EditorGUI.indentLevel = 2;
                SelectAllToggle(i);
                //显示具体属性
                //showPropertyOfComponent(i,4,sb,treeDepth);
                ShowPropertyOfOneComponent(i, 4);

                //遍历特殊的几个，进行显示
                ShowHideProperty(i);
                //EditorGUILayout.EndToggleGroup();
                EditorGUI.indentLevel = 0; //取消缩进

                EditorGUILayout.Space();
            }
            
        }

    }

    /// <summary>
    /// 全选按钮功能实现
    /// </summary>
    /// <param name="componentIndex">当前全选按钮所在的component ID</param>
    private void SelectAllToggle(int componentIndex)
    {
        if (_allChildProperty[componentIndex].Length == 0)
            return;
        bool prePropertyState = _objectSelected[componentIndex];
        _objectSelected[componentIndex] = EditorGUILayout.ToggleLeft("全选", _objectSelected[componentIndex]);
        if (_objectSelected[componentIndex] != prePropertyState)
        {
            //“全选”按钮被点击，修改所有孩子属性是否选中的值
            for (int j = 0; j < _allChildProperty[componentIndex].Length; j++)
            {
                _allChildProperty[componentIndex][j] = _objectSelected[componentIndex];
            }
            //显示几个特殊的属性是否被选中
            foreach (ToShowProperty toShowProperty in SerializedModel.ToShowPropertList)
            {
                if (toShowProperty.ComponentName.Equals(GlobalData.ComponentNameList[componentIndex]))
                {
                    toShowProperty.IsChoosed = _objectSelected[componentIndex];
                }
            }
        }
    }

    /// <summary>
    /// 根据SerializedModel.ToShowPropertList中的数据显示“serializedproperty”中无法遍历的属性
    /// </summary>
    /// <param name="i">标识当前属性属于第i个serializedobject</param>
    private void ShowHideProperty(int i)
    {
        foreach (ToShowProperty toShowProperty in SerializedModel.ToShowPropertList)
        {
            if (toShowProperty.ComponentName.Equals(GlobalData.ComponentNameList[i]))
            {
                //找到
                EditorGUI.indentLevel = toShowProperty.Indent;
                toShowProperty.ObjectIndex = i;
                if (toShowProperty.DisplayFunc == null)
                {
                    toShowProperty.IsChoosed = EditorGUILayout.ToggleLeft(toShowProperty.ShowName, toShowProperty.IsChoosed,
                    GUILayout.ExpandWidth(true));
                }
                else
                {
                    toShowProperty.IsChoosed = toShowProperty.DisplayFunc(GlobalData.SerializedObjectList[i], toShowProperty.ShowName, toShowProperty.ShowName, toShowProperty.IsChoosed);
                }
                
            }
        }
        EditorGUI.indentLevel = 0;
    }

    /// <summary>
    /// 几个特殊的，需要修改Component显示名称的对象
    /// </summary>
    /// <param name="componentName"></param>
    /// <returns></returns>
    private string ChangeComponentName(string componentName)
    {
        if (componentName.Equals("Behaviour"))
        {
            return "Halo";
        }
        else if (componentName.Equals("Component"))
        {
            return "World Particle Collider";
        }
        else
        {
            return componentName;
        }
    }

    /// <summary>
    /// 显示一个component的所有属性
    /// </summary>
    /// <param name="index"></param>
    /// <param name="indentIndex"></param>
    private void ShowPropertyOfOneComponent(int index, int indentIndex)
    {

        List<ShowData> propertyToShowList = SerializedModel.PropertyNameList[index];
        SerializedProperty serializedProperty = GlobalData.SerializedPropertyIterList[index];
        int j = 0;
        foreach (ShowData showData in propertyToShowList)
        {
             TUTLog.log(showData.DepthIndent+"---"+showData.PropertyName);
             EditorGUI.indentLevel = indentIndex + showData.DepthIndent*2;

             bool prePropertyState = _allChildProperty[index][j];
             _allChildProperty[index][j] = ShowPropertyAsRule(showData, _allChildProperty[index][j], GlobalData.SerializedObjectList[index]);
             if (_beginAndEndPosition[index].ContainsKey(j))
            {
                //当前是一个父属性点
                if (prePropertyState != _allChildProperty[index][j])
                {
                    //获得所有孩子节点,并修改显示状态
                    int endProperty = _beginAndEndPosition[index][j];
                    for (int t = j + 1; t <= endProperty; t++)
                    {
                        _allChildProperty[index][t] = _allChildProperty[index][j];
                    }
                }
            }
            j++;

        }
    }

    /// <summary>
    /// 按照需要修改的规则，显示具体的属性
    /// </summary>
    /// <param name="showData"></param>
    /// <param name="toogleState"></param>
    /// <param name="serializedObject"></param>
    /// <returns></returns>
    private bool ShowPropertyAsRule(ShowData showData, bool toogleState,SerializedObject serializedObject)
    {
        bool toogleStateNew = false;
        bool findinRule = false;
        if (showData.DisplayFunc != null)
        {
            showData.ArgModel.ToogleState = toogleState;
            toogleStateNew = showData.DisplayFunc(serializedObject, showData.ArgModel);
            //toogleStateNew = showData.DisplayFunc(serializedObject,showData.ChangeName, showData.PropertyName, toogleState);
            findinRule = true;
        }
        //不是特殊的，需要改变的属性名称
        if (!findinRule)
        {
            toogleStateNew = ShowNormalProperty(showData.PropertyName, toogleState);
        }
        return toogleStateNew;
    }
    /// <summary>
    /// 显示不需要修改的属性
    /// </summary>
    /// <param name="propertyName"></param>
    /// <param name="toogleState"></param>
    /// <returns></returns>
    public bool ShowNormalProperty(string propertyName, bool toogleState)
    {
        return EditorGUILayout.ToggleLeft(propertyName, toogleState, GUILayout.ExpandWidth(true));
    }


    /// <summary>
    /// 显示copybutton
    /// 实现copy的业务逻辑
    /// </summary>
    private void ShowCopyButton()
    {
        if (GUILayout.Button("复制选中属性", GUILayout.Height(30)))
        {
            CollectionSelectedData();
            StoreToReg();
#if UNITY_4_6||UNITY_4_5
            this.Close();
#endif
        }
    }

    private void ClearToogleButton()
    {
        if (GUILayout.Button("清空选中属性", GUILayout.Height(30)))
        {
            for (int i = 0; i < _objectSelected.Length; i++)
            {
                //清空全选按钮
                _objectSelected[i] = false;
            }
            for (int i = 0; i < _allChildProperty.Length; i++)
                for (int j = 0; j < _allChildProperty[i].Length; j++)
                {
                    _allChildProperty[i][j] = false;
                }
            foreach (ToShowProperty toShowProperty in SerializedModel.ToShowPropertList)
            {
                toShowProperty.IsChoosed = false;
            }
            for (int i = 0; i < _showFirstChildProperty.Length; i++)
            {
                //折叠所有的下拉框
                _showFirstChildProperty[i] = false;
            }
            //清空注册表
            RegistryKey lm = Registry.LocalMachine;
            //对应HKEY_LOCAL_MACHINE基项分支
            RegistryKey software = lm.OpenSubKey("SOFTWARE", true);
            RegistryKey TUT_ComponentCopy = software.OpenSubKey("TUT_ComponentCopy", true);
            if (TUT_ComponentCopy != null)
            {
                TUT_ComponentCopy.SetValue("componentcopy", "");
                TUT_ComponentCopy.SetValue("componentcopy_hide","");
                TUT_ComponentCopy.Close();
            }
            software.Close();
            lm.Close();

#if UNITY_4_6||UNITY_4_5
            this.Close();
#endif
        }
    }

    /// <summary>
    /// 收集所有选中的property
    /// </summary>
    private void CollectionSelectedData()
    {
        //收集需要复制的properties信息
        GlobalData.CopyDataList = new List<CopyData>();
        GlobalData.RemainComponentNameList = new List<string>();

        for (int i = 0; i < _allChildProperty.Length; i++)
        {
            CopyData copyData = new CopyData();
            copyData.ComponentIndex = i;
            copyData.ComponetType = GlobalData.ComponentNameList[i];
            copyData.ProperitiesIndex = new List<int>();
            for (int j = 0; j < _allChildProperty[i].Length; j++)
            {
                //遍历一个component下的所有property的状态，并保存起来
                if (_allChildProperty[i][j])
                {
                    //当前状态为选择状态；
                    copyData.ProperitiesIndex.Add(j);
                }

            }
            if (copyData.ProperitiesIndex.Count>0)
            {
                //存在选中的property
                GlobalData.RemainComponentNameList.Add(GlobalData.ComponentNameList[i]);
                GlobalData.CopyDataList.Add(copyData);
            }
        }

        //复制SerializedModel.ToShowPropertList中的数据显示“serializedproperty”中无法遍历的属性
        foreach (ToShowProperty toShowProperty in SerializedModel.ToShowPropertList)
        {
            if (toShowProperty.IsChoosed == true)
            {
                GlobalData.ToShowProperties.Add(toShowProperty);
            }
        }
    }

    /// <summary>
    /// 存储当前用户选中的property
    /// </summary>
    private void StoreToReg()
    {
        RegistryKey lm = Registry.LocalMachine;
        //对应HKEY_LOCAL_MACHINE基项分支
        RegistryKey software = lm.OpenSubKey("SOFTWARE", true);
        RegistryKey TUT_ComponentCopy = null;
        //如果不存在TUT_ComponentCopy项，则创建该项
        try
        {
            TUT_ComponentCopy = lm.OpenSubKey("TUT_ComponentCopy", true);
            //试图打开不存在的项TUT_ComponentCopy
            if (TUT_ComponentCopy == null)
            {
                TUT_ComponentCopy = software.CreateSubKey("TUT_ComponentCopy");
            }
        }
        catch (Exception)
        {
            Console.WriteLine("An Error happened when create registrykey");
        }
        //清空注册表中的数据
        TUT_ComponentCopy.SetValue("componentcopy","");
        TUT_ComponentCopy.SetValue("componentcopy_hide","");

        StringBuilder sb = new StringBuilder();
        sb = GetSelectedIndex(sb);
        TUT_ComponentCopy.SetValue("componentcopy",sb.ToString());
        StringBuilder sb2 = new StringBuilder();
        sb2 = GetSelectedIndex_Hide(sb2);
        TUT_ComponentCopy.SetValue("componentcopy_hide",sb2.ToString());
        TUT_ComponentCopy.Close();
        software.Close();
        lm.Close();
    }

    /// <summary>
    /// 遍历当前_allChildProperty的状态，得到所有选中的属性和对应的Component名称，并存储到sb中
    /// 存储格式为：componentname:index1,index2;component2:index1,index2;
    /// </summary>
    /// <param name="sb"></param>
    /// <returns></returns>
    private StringBuilder GetSelectedIndex(StringBuilder sb)
    {
        //创建一个key，并存储值
        Dictionary<string, string> tempDictionary = new Dictionary<string, string>();
        for (int i = 0; i < _allChildProperty.Length; i++)
        {
            for (int j = 0; j < _allChildProperty[i].Length; j++)
            {
                if (_allChildProperty[i][j])
                {
                    //该项被选中，则存储到注册表中
                    if (tempDictionary.ContainsKey(GlobalData.ComponentNameList[i]))
                    {
                        string value = tempDictionary[GlobalData.ComponentNameList[i]];
                        tempDictionary[GlobalData.ComponentNameList[i]] = value + "," + j;
                    }
                    else
                    {
                        tempDictionary.Add(GlobalData.ComponentNameList[i], j + "");
                    }
                }
            }
        }
        foreach (KeyValuePair<string, string> keyValuePair in tempDictionary)
        {
            sb.Append(keyValuePair.Key);
            sb.Append(":");
            sb.Append(keyValuePair.Value);
            sb.Append(";");
        }
        return sb;
    }

    /// <summary>
    /// 遍历当前ToShowPropertList的状态，得到所有选中的属性和对应的Component名称，并存储到sb中
    /// 存储格式为：componentname:properetyname1,properetyname2;componentname2:properetyname1,properetyname2;
    /// </summary>
    /// <param name="sb"></param>
    /// <returns></returns>
    private StringBuilder GetSelectedIndex_Hide(StringBuilder sb)
    {
        Dictionary<string, string> tempDictionary = new Dictionary<string, string>();
        foreach (ToShowProperty toShowProperty in SerializedModel.ToShowPropertList)
        {
            if (toShowProperty.IsChoosed == true)
            {
                if (tempDictionary.ContainsKey(toShowProperty.ComponentName))
                {
                    string value = tempDictionary[toShowProperty.ComponentName];
                    tempDictionary[toShowProperty.ComponentName] = value + "," + toShowProperty.PropertyName;
                }
                else
                {
                    tempDictionary.Add(toShowProperty.ComponentName,  toShowProperty.PropertyName);
                }
            }
        }
        foreach (KeyValuePair<string, string> keyValuePair in tempDictionary)
        {
            sb.Append(keyValuePair.Key);
            sb.Append(":");
            sb.Append(keyValuePair.Value);
            sb.Append(";");
        }
        return sb;
    }

    /// <summary>
    /// 初始化所有列表是否需要被选中
    /// </summary>
    private void InitToogleStateByReg()
    {
        RegistryKey lm = Registry.LocalMachine;
        //对应HKEY_LOCAL_MACHINE基项分支
        RegistryKey software = lm.OpenSubKey("SOFTWARE", true);
        RegistryKey TUT_ComponentCopy = software.OpenSubKey("TUT_ComponentCopy", true);
        if (TUT_ComponentCopy != null)
        {
            //初始化_allChildProperty
            InitAllChildProperty(TUT_ComponentCopy);
            //初始化ToShowPropertList
            InitToShowPropertList(TUT_ComponentCopy);
            TUT_ComponentCopy.Close();
        }
        
        software.Close();
        lm.Close();
    }

    /// <summary>
    /// 读取注册表TUT_ComponentCopy中注册数据，并读取其中的项“componentcopy”中的数据，解析之后，对_allChildProperty数组进行赋值
    /// </summary>
    /// <param name="TUT_ComponentCopy"></param>
    private void InitAllChildProperty(RegistryKey TUT_ComponentCopy)
    {
        //读取值
        string value = (string) TUT_ComponentCopy.GetValue("componentcopy");
        if (!String.IsNullOrEmpty(value))
        {
            //tempDictionary存储所有的<ComponentName,选中的下标>
            Dictionary<string, string> tempDictionary = new Dictionary<string, string>();
            string[] SemicolonValue = value.Split(';');
            for (int i = 0; i < SemicolonValue.Length; i++)
            {
                if (SemicolonValue[i] != "")
                {
                    string[] ColonValue = SemicolonValue[i].Split(':');
                    tempDictionary.Add(ColonValue[0], ColonValue[1]);
                }
            }
            for (int i = 0; i < GlobalData.ComponentNameList.Length; i++)
            {
                if (tempDictionary.ContainsKey(GlobalData.ComponentNameList[i]))
                {
                    //找到一个对应的Component名字,分割出所有下标
                    string[] selectIndexValue = tempDictionary[GlobalData.ComponentNameList[i]].Split(',');
                    //设置_allChildProperty中的值
                    for (int j = 0; j < selectIndexValue.Length; j++)
                    {
                        _allChildProperty[i][Int32.Parse(selectIndexValue[j])] = true;
                        _showFirstChildProperty[i] = true;
                    }
                }
            }
        }
    }
    
    /// <summary>
    /// 读取注册表TUT_ComponentCopy中注册数据，并读取其中的项“componentcopy_hide”中的数据，解析之后，对ToShowPropertList列表进行赋值
    /// </summary>
    /// <param name="TUT_ComponentCopy"></param>
    private void InitToShowPropertList(RegistryKey TUT_ComponentCopy)
    {
        //读取值
        string value = (string)TUT_ComponentCopy.GetValue("componentcopy_hide");
        if (!String.IsNullOrEmpty(value))
        {
            //tempDictionary存储所有的<ComponentName,选中的下标>
            Dictionary<string, string> tempDictionary = new Dictionary<string, string>();
            string[] SemicolonValue = value.Split(';');
            for (int i = 0; i < SemicolonValue.Length; i++)
            {
                if (SemicolonValue[i] != "")
                {
                    string[] ColonValue = SemicolonValue[i].Split(':');
                    tempDictionary.Add(ColonValue[0], ColonValue[1]);
                }
            }
            foreach (ToShowProperty toShowProperty in SerializedModel.ToShowPropertList)
            {
                //读取注册表中状态赋值的条件：（1）是一个隐藏的Component；（2）当前GameObject有这个Component
                if (tempDictionary.ContainsKey(toShowProperty.ComponentName) && GlobalData.ComponentNameList.Contains(toShowProperty.ComponentName))
                {
                    //找到一个对应的Component名字,分割出所有下标
                    string[] selectIndexValue = tempDictionary[toShowProperty.ComponentName].Split(',');
                    //设置ToShowPropertList中的IsChoosed值
                    for (int j = 0; j < selectIndexValue.Length; j++)
                    {
                        foreach (ToShowProperty toShowPropertyIn in SerializedModel.ToShowPropertList)
                        {
                            if (toShowPropertyIn.ComponentName.Equals(toShowProperty.ComponentName)&&toShowPropertyIn.PropertyName.Equals(selectIndexValue[j]))
                            {
                                toShowPropertyIn.IsChoosed = true;
                            }
                        }
                    }
                    //找到_showFirstChildProperty对应的下标位置，并设置为true
                    for (int tempIndex = 0; tempIndex < GlobalData.ComponentNameList.Length; tempIndex++)
                    {
                        if (GlobalData.ComponentNameList[tempIndex].Equals(toShowProperty.ComponentName))
                        {
                            _showFirstChildProperty[tempIndex] = true;
                            break;
                        }
                    }
                }
            }
        }
    }
} 
#endif //TKFrame Auto Gen
