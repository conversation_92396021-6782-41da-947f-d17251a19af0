
//#if !LOGIC_THREAD 
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

/// <summary>
/// 增加子panel自动适配管理
/// </summary>
public class EditorWindowBaseEx : EditorWindowBase
{
    public class LayoutAdapterContainer
    {
        public Vector2 size;
        public Vector2 maxSize;

        public List<EditorSubPanelBase> subPanelList = new List<EditorSubPanelBase>();

        public void Reposition(float positionX, float paddingY, float offsetY)
        {
            float positionY = offsetY;
            for (int i = 0; i < subPanelList.Count; ++i)
            {
                var subPanel = subPanelList[i];

                var rect = subPanel.RuntimeRect;
                rect.x = positionX;
                rect.y = positionY;
                subPanel.RuntimeRect = rect;

                positionY += subPanel.RuntimeRect.height + paddingY;
            }
        }

        public void AutoHeight(float paddingY, float offsetY)
        {
            if (size.y > maxSize.y)
            {
                float diffHeight = size.y - maxSize.y + paddingY * subPanelList.Count - offsetY * 2;

                for (int i = 0; i < subPanelList.Count; ++i)
                {
                    var subPanel = subPanelList[i];
                    var subPanelDiffHeight = subPanel.Size.y - subPanel.MinSize.y;
                    if (subPanelDiffHeight >= diffHeight)
                    {
                        var rect = subPanel.RuntimeRect;
                        rect.height = subPanel.Size.y - diffHeight;
                        subPanel.RuntimeRect = rect;
                        break;
                    }
                    else
                    {
                        var rect = subPanel.RuntimeRect;
                        rect.size = subPanel.MinSize;
                        subPanel.RuntimeRect = rect;

                        diffHeight -= subPanelDiffHeight;
                    }
                }
            }
            else if (size.y < maxSize.y)
            {
                float diffHeight = maxSize.y - size.y + paddingY * subPanelList.Count - offsetY * 2;
                for (int i = 0; i < subPanelList.Count; ++i)
                {
                    var subPanel = subPanelList[i];
                    var subPanelDiffHeight = subPanel.MaxSize.y - subPanel.Size.y;
                    if (subPanelDiffHeight >= diffHeight)
                    {
                        var rect = subPanel.RuntimeRect;
                        rect.height = subPanel.Size.y + diffHeight;
                        subPanel.RuntimeRect = rect;
                        break;
                    }
                    else
                    {
                        var rect = subPanel.RuntimeRect;
                        rect.size = subPanel.MaxSize;
                        subPanel.RuntimeRect = rect;

                        diffHeight -= subPanelDiffHeight;
                    }
                }
            }
        }

        public void AutoWidth()
        {
            float width = size.x;

            for (int i = 0; i < subPanelList.Count; ++i)
            {
                var subPanel = subPanelList[i];
                float subPanelWidth = width;

                if (subPanel.MaxSize.x < width)
                {
                    subPanelWidth = subPanel.MaxSize.x;
                }
                if (subPanel.MinSize.x > width)
                {
                    subPanelWidth = subPanel.MinSize.x;
                }

                var rect = subPanel.RuntimeRect;
                rect.width = subPanelWidth;
                subPanel.RuntimeRect = rect;
            }
        }

        public float MinWidth()
        {
            float minWidth = 0;
            for (int i = 0; i < subPanelList.Count; ++i)
            {
                minWidth = Mathf.Max(subPanelList[i].MinSize.x, minWidth);
            }
            return minWidth;
        }

        public float MaxWidth()
        {
            float maxWidth = 0;
            for (int i = 0; i < subPanelList.Count; ++i)
            {
                maxWidth = Mathf.Max(subPanelList[i].MaxSize.x, maxWidth);
            }
            return maxWidth;
        }
    }
    // 左右间距
    protected Vector2 offset = new Vector2(5, 5);

    // 两个sub panel之间的间距
    protected Vector2 padding = new Vector2(5, 5);

    private Vector2 m_Size = Vector2.zero;
    protected List<EditorSubPanelBase> m_SubPanelList = new List<EditorSubPanelBase>();

    public void AddSubPanel(EditorSubPanelBase subPanel)
    {
        m_SubPanelList.Add(subPanel);
    }

    public T GetSubPanelByName<T>(string name) where T : EditorSubPanelBase
    {
        for (int i = 0; i < m_SubPanelList.Count; ++i)
        {
            if (m_SubPanelList[i].Name == name)
                return m_SubPanelList[i] as T;
        }
        return null;
    }

    protected override void OnGUI()
    {
        OnPreDraw();

        base.OnGUI();

        if (m_SubPanelList != null)
        {
            if (m_Size != position.size)
            {
                m_Size = position.size;

                ReLayout();
            }

            for (int i = 0; i < m_SubPanelList.Count; ++i)
            {
                var subPanel = m_SubPanelList[i];
                subPanel.Draw();
            }
        }

        OnPostDraw();
    }

    protected virtual void OnPreDraw()
    {

    }

    protected virtual void OnPostDraw()
    {

    }

    protected T CreateSubPanel<T>(string name, Vector2 size, E_SubPanelAnchor anchor, Vector2 minSize, Vector2 maxSize) where T : EditorSubPanelBase
    {
        T subPanel = Activator.CreateInstance<T>();
        subPanel.Name = name;
        subPanel.Size = size;
        subPanel.Anchor = anchor;
        subPanel.MinSize = minSize.x <= size.x && minSize.y <= size.y ? minSize : size;
        subPanel.MaxSize = maxSize.x >= size.x && maxSize.y >= size.y ? maxSize : size;
        subPanel.Owner = this;
        subPanel.RuntimeRect = new Rect(Vector2.zero, size);
        m_SubPanelList.Add(subPanel);
        return subPanel;
    }

    protected T CreateSubPanel<T>(string name, Vector2 size, E_SubPanelAnchor anchor, bool autoSize) where T : EditorSubPanelBase
    {
        T subPanel = Activator.CreateInstance<T>();
        subPanel.Name = name;
        subPanel.Size = size;
        subPanel.Anchor = anchor;
        subPanel.MinSize = autoSize ? Vector2.zero : size;
        subPanel.MaxSize = autoSize ?  position.size : size;
        subPanel.Owner = this;
        subPanel.RuntimeRect = new Rect(Vector2.zero, size);
        m_SubPanelList.Add(subPanel);
        return subPanel;
    }

    protected void ReLayout()
    {
        Dictionary<int, LayoutAdapterContainer> adapterDict = new Dictionary<int, LayoutAdapterContainer>();
        for (int i = 0; i < 3; ++i)
            adapterDict.Add(i, new LayoutAdapterContainer() { maxSize = m_Size });

        for (int i = 0; i < m_SubPanelList.Count; ++i)
        {
            var subPanel = m_SubPanelList[i];
            LayoutAdapterContainer container = adapterDict[(int)subPanel.Anchor];
            container.size.x = Math.Max(container.size.x, subPanel.Size.x);
            container.size.y += subPanel.Size.y + padding.y;
            container.subPanelList.Add(subPanel);
        }

        // 高度自动适配
        float width = offset.x;
        foreach (var adapter in adapterDict)
        {
            adapter.Value.size.y += (offset.y * 2);
            adapter.Value.AutoHeight(padding.y, offset.y);
            width += adapter.Value.size.x + padding.x;
        }
        width += offset.x;

        // 宽度自动适配
        if (width > m_Size.x)
        {
            float diffWidth = width - m_Size.x + padding.x * adapterDict.Count - offset.x * 2;
            foreach (var adapter in adapterDict)
            {
                float minWidth = adapter.Value.MinWidth();
                float curWidth = adapter.Value.size.x;
                float diffWidth1 = curWidth - minWidth;
                if (diffWidth1 >= diffWidth)
                {
                    adapter.Value.size.x -= diffWidth;
                    adapter.Value.AutoWidth();

                    break;
                }
                else
                {
                    diffWidth -= diffWidth1;
                    adapter.Value.size.x = minWidth;
                    adapter.Value.AutoWidth();
                }
            }
        }
        else if (width < m_Size.x)
        {
            float diffWidth = m_Size.x - width + padding.x * adapterDict.Count - offset.x * 2;
            foreach (var adapter in adapterDict)
            {
                float maxWidth = adapter.Value.MaxWidth();
                float curWidth = adapter.Value.size.x;
                float diffWidth1 = maxWidth - curWidth;
                if (diffWidth1 >= diffWidth)
                {
                    adapter.Value.size.x += diffWidth;
                    adapter.Value.AutoWidth();

                    break;
                }
                else
                {
                    diffWidth -= diffWidth1;
                    adapter.Value.size.x = maxWidth;
                    adapter.Value.AutoWidth();
                }
            }
        }

        // 重新设置一下每个子panel的起始绘制点
        float positionX = offset.x;
        foreach (var adapter in adapterDict)
        {
            adapter.Value.Reposition(positionX, padding.y, offset.y);
            positionX += (adapter.Value.size.x + padding.x);
        }
    }
}

//#endif