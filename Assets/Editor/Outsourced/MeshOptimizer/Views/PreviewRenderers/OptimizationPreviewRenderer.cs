using UnityEngine;
using UnityEditor;
using MeshOptimizer.Models;
using MeshOptimizer.Utilities;

namespace MeshOptimizer.Views.PreviewRenderers
{
    /// <summary>
    /// 优化预览渲染器，用于渲染优化后的网格预览
    /// </summary>
    public class OptimizationPreviewRenderer : PreviewRendererBase
    {
        // 预览相机
        private Camera _previewCamera;

        // 预览场景
        private PreviewRenderUtility _previewUtility;

        // 旋转角度
        private Vector2 _rotation = new Vector2(20f, 30f);

        // 缩放系数
        private float _zoom = 1.0f;

        // 当前显示的网格数据
        private MeshProcessingData _meshData;

        /// <summary>
        /// 构造函数
        /// </summary>
        public OptimizationPreviewRenderer()
        {
        }

        /// <summary>
        /// 初始化预览渲染器
        /// </summary>
        public override void Initialize()
        {
            if (_isInitialized)
                return;



            // 使用MeshPreviewUtility创建预览工具，确保它被正确跟踪和清理
            _previewUtility = MeshOptimizer.Utilities.MeshPreviewUtility.CreatePreviewUtility();
            _previewUtility.cameraFieldOfView = 30f; // 与MeshPreviewRenderer保持一致
            _previewUtility.camera.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 1f);
            _previewUtility.camera.clearFlags = CameraClearFlags.Color;
            _previewUtility.camera.nearClipPlane = 0.1f; // 使用较小的近裁剪面，避免剔除
            _previewUtility.camera.farClipPlane = 1000f;

            // 保存预览相机引用
            _previewCamera = _previewUtility.camera;

            base.Initialize();
        }

        /// <summary>
        /// 设置要预览的网格数据
        /// </summary>
        /// <param name="meshData">网格处理数据</param>
        public void SetMeshData(MeshProcessingData meshData)
        {
            _meshData = meshData;
        }

        /// <summary>
        /// 设置旋转和缩放
        /// </summary>
        /// <param name="rotation">旋转角度</param>
        /// <param name="zoom">缩放系数</param>
        public void SetRotationAndZoom(Vector2 rotation, float zoom)
        {
            _rotation = rotation;
            _zoom = zoom;
        }

        /// <summary>
        /// 绘制预览
        /// </summary>
        public override void DrawGUI()
        {
            if (!_isInitialized)
                Initialize();

            // 获取预览区域 - 使用200高度，与网格预览窗口保持一致
            _previewRect = EditorGUILayout.GetControlRect(false, 200);

            // 绘制预览背景
            GUI.Box(_previewRect, GUIContent.none, EditorStyles.helpBox);

            // 绘制预览内容
            DrawPreview();
        }

        /// <summary>
        /// 绘制预览，使用指定的预览区域
        /// </summary>
        /// <param name="previewRect">预览区域</param>
        public void DrawGUI(Rect previewRect)
        {
            if (!_isInitialized)
                Initialize();

            // 使用传入的预览区域
            _previewRect = previewRect;

            // 绘制预览内容
            DrawPreview();
        }



        /// <summary>
        /// 绘制预览内容
        /// </summary>
        protected override void DrawPreview()
        {
            if (_meshData == null)
            {
                // 如果没有网格数据，显示提示信息
                base.DrawCenteredText("请选择要预览的对象");
                return;
            }

            // 处理鼠标拖拽旋转
            HandleRotation();

            // 渲染网格预览
            RenderMeshPreview();

            // 绘制网格信息
            DrawMeshInfo();
        }

        /// <summary>
        /// 绘制居中文本
        /// </summary>
        /// <param name="text">要显示的文本</param>
        private new void DrawCenteredText(string text)
        {
            GUIStyle centeredStyle = new GUIStyle(GUI.skin.label);
            centeredStyle.alignment = TextAnchor.MiddleCenter;
            centeredStyle.fontStyle = FontStyle.Bold;

            GUI.Label(_previewRect, text, centeredStyle);
        }

        /// <summary>
        /// 处理鼠标交互，包括旋转、缩放和重置视图
        /// </summary>
        private void HandleRotation()
        {
            Event e = Event.current;
            if (!_previewRect.Contains(e.mousePosition))
                return;

            bool handled = false;

            // 处理鼠标左键拖拽旋转（与网格预览窗口保持一致）
            if (e.type == EventType.MouseDrag && e.button == 0)
            {
                _rotation.y += e.delta.x * 0.4f;
                _rotation.x -= e.delta.y * 0.4f;
                _rotation.x = Mathf.Clamp(_rotation.x, -90f, 90f);
                handled = true;
            }

            // 处理鼠标中键拖拽缩放
            if (e.type == EventType.MouseDrag && e.button == 2)
            {
                _zoom += e.delta.y * 0.01f;
                _zoom = Mathf.Clamp(_zoom, 0.1f, 10f);
                handled = true;
            }

            // 处理鼠标滚轮缩放
            if (e.type == EventType.ScrollWheel)
            {
                _zoom -= e.delta.y * 0.05f;
                _zoom = Mathf.Clamp(_zoom, 0.1f, 10f);
                handled = true;
            }

            // 处理鼠标右键点击重置视图
            if (e.type == EventType.MouseDown && e.button == 1)
            {
                _rotation = new Vector2(30f, -30f);
                _zoom = 1.0f;
                handled = true;
            }

            // 如果处理了事件，标记为已使用
            if (handled)
            {
                e.Use();
                // 强制重绘窗口
                EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();
            }
        }

        /// <summary>
        /// 渲染网格预览
        /// </summary>
        private void RenderMeshPreview()
        {
            if (_previewUtility == null || _meshData == null)
            {
                // 如果预览工具或网格数据不可用，显示占位框
                GUI.Box(_previewRect, "无法预览网格");
                return;
            }

            // 获取要预览的网格 - 优先使用优化后的网格
            Mesh meshToPreview = null;

            // 优先使用优化后的网格，即使已经进行了UV打包
            // 这确保了减面预览始终显示原始的优化网格，而不是UV打包后的网格
            if (_meshData.IsOptimized && _meshData.OptimizedMesh != null)
            {
                meshToPreview = _meshData.OptimizedMesh;
            }
            // 其次使用剔除后的网格
            else if (_meshData.IsCulled && _meshData.CulledMesh != null)
            {
                meshToPreview = _meshData.CulledMesh;
            }
            // 再次使用处理后的网格
            else if (_meshData.ProcessedMesh != null)
            {
                meshToPreview = _meshData.ProcessedMesh;
            }
            // 最后使用原始网格
            else
            {
                meshToPreview = _meshData.OriginalMesh;
            }

            if (meshToPreview == null)
            {
                GUI.Box(_previewRect, "无法预览网格");
                return;
            }


            // 获取材质
            Material material = null;
            Material wireframeMaterial = null;

            try
            {
                // 使用半透明材质，从MeshProcessingData中获取贴图
                Texture mainTexture = MeshPreviewUtility.GetTextureFromMeshProcessingData(_meshData);

                // 创建半透明材质
                material = MeshPreviewUtility.CreateTransparentMaterial(mainTexture, 0.7f);

                // 如果无法创建材质，使用默认材质
                if (material == null)
                {
                    // 使用默认材质
                    material = MeshPreviewUtility.CreateTransparentMaterial(null, 0.7f);
                }

                // 创建线框材质
                wireframeMaterial = MeshPreviewUtility.CreateWireframeMaterial();
            }
            catch (System.Exception)
            {
                // 忽略异常
                return;
            }

            // 设置预览相机
            _previewUtility.BeginPreview(_previewRect, GUIStyle.none);

            // 计算相机位置
            float objSize = meshToPreview.bounds.size.magnitude;
            if (objSize < 0.001f) objSize = 1.0f; // 防止网格太小

            // 计算合适的相机距离 - 与MeshPreviewRenderer保持一致
            float distance = objSize * 2.0f / Mathf.Tan(_previewUtility.camera.fieldOfView * 0.5f * Mathf.Deg2Rad);

            // 设置相机位置和旋转 - 与细分预览保持一致
            _previewUtility.camera.transform.position = new Vector3(0, 0, -6); // 使用固定距离，与细分预览保持一致
            _previewUtility.camera.transform.rotation = Quaternion.identity;
            _previewUtility.camera.nearClipPlane = 0.1f; // 使用较小的近裁剪面，避免剔除
            _previewUtility.camera.farClipPlane = 1000f;

            // 创建临时游戏对象
            GameObject previewObject = new GameObject("PreviewObject");
            MeshFilter meshFilter = previewObject.AddComponent<MeshFilter>();
            meshFilter.sharedMesh = meshToPreview;

            // 计算合适的缩放比例 - 与细分预览保持一致
            float scaleFactor = 4.0f / objSize; // 放大为原来的4倍，解决mesh较小的问题

            // 设置对象变换 - 确保对象在相机前方，旋转180度使正面朝向摄像机
            previewObject.transform.position = Vector3.zero;
            previewObject.transform.rotation = Quaternion.Euler(_rotation.x, _rotation.y + 180f, 0f);
            previewObject.transform.localScale = Vector3.one * scaleFactor * _zoom;

            // 添加渲染器组件
            MeshRenderer meshRenderer = previewObject.AddComponent<MeshRenderer>();
            meshRenderer.sharedMaterial = material;

            // 渲染模型 - 使用更简单的方法
            try
            {
                // 设置相机参数
                _previewUtility.camera.clearFlags = CameraClearFlags.SolidColor;
                _previewUtility.camera.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 1f);

                // 使用AddSingleGO方法，与细分预览保持一致
                _previewUtility.AddSingleGO(previewObject);

                // 渲染并获取结果
                _previewUtility.camera.Render();
                Texture resultRender = _previewUtility.EndPreview();

                // 绘制结果
                GUI.DrawTexture(_previewRect, resultRender, ScaleMode.StretchToFill, false);
            }
            catch (System.Exception)
            {
                // 渲染模型时出错
                _previewUtility.EndPreview();
            }

            // 渲染线框 - 使用基于重心坐标的线框渲染
            try
            {
                // 创建带有重心坐标的网格
                Mesh wireframeMesh = BarycentricWireframeUtility.AddBarycentricCoordinates(meshToPreview);

                // 更新网格过滤器
                meshFilter.sharedMesh = wireframeMesh;

                // 创建线框材质
                Material barycentricWireframeMaterial = BarycentricWireframeUtility.CreateWireframeMaterial(
                    Color.black,           // 线框颜色
                    new Color(0, 0, 0, 0), // 填充颜色（透明）
                    0.05f,                 // 线框粗细
                    true                   // 使用屏幕空间线宽
                );

                _previewUtility.BeginPreview(_previewRect, GUIStyle.none);
                _previewUtility.camera.clearFlags = CameraClearFlags.SolidColor;
                _previewUtility.camera.backgroundColor = new Color(0, 0, 0, 0);

                // 设置线框材质
                meshRenderer.sharedMaterial = barycentricWireframeMaterial;

                // 使用AddSingleGO方法，与细分预览保持一致
                _previewUtility.AddSingleGO(previewObject);

                // 渲染并获取结果
                _previewUtility.camera.Render();
                Texture wireframeRender = _previewUtility.EndPreview();

                // 绘制结果
                GUI.DrawTexture(_previewRect, wireframeRender, ScaleMode.StretchToFill, true);

                // 清理资源
                if (wireframeMesh != null)
                {
                    Object.DestroyImmediate(wireframeMesh);
                }

                if (barycentricWireframeMaterial != null)
                {
                    Object.DestroyImmediate(barycentricWireframeMaterial);
                }
            }
            catch (System.Exception e)
            {
                // 渲染线框时出错
                Debug.LogError("渲染线框时出错: " + e.Message);
                _previewUtility.EndPreview();
            }

            try
            {
                // 清理资源
                if (previewObject != null)
                {
                    Object.DestroyImmediate(previewObject);
                }

                // 减少材质引用计数
                if (material != null)
                {
                    ResourceManager.Instance.ReleaseMaterial(material);
                }

                // 减少线框材质引用计数
                if (wireframeMaterial != null)
                {
                    ResourceManager.Instance.ReleaseMaterial(wireframeMaterial);
                }
            }
            catch (System.Exception)
            {
                // 清理资源时出错
            }
        }



        /// <summary>
        /// 绘制网格信息
        /// </summary>
        private void DrawMeshInfo()
        {
            if (_meshData == null)
                return;

            // 获取要显示信息的网格
            Mesh meshToDisplay = null;

            // 优先使用优化后的网格，与RenderMeshPreview保持一致
            // 这确保了减面预览始终显示原始的优化网格，而不是UV打包后的网格
            if (_meshData.IsOptimized && _meshData.OptimizedMesh != null)
            {
                meshToDisplay = _meshData.OptimizedMesh;
            }
            // 其次使用剔除后的网格
            else if (_meshData.IsCulled && _meshData.CulledMesh != null)
            {
                meshToDisplay = _meshData.CulledMesh;
            }
            // 再次使用处理后的网格
            else if (_meshData.ProcessedMesh != null)
            {
                meshToDisplay = _meshData.ProcessedMesh;
            }
            // 最后使用原始网格
            else
            {
                meshToDisplay = _meshData.OriginalMesh;
            }

            if (meshToDisplay == null)
                return;

            // 显示网格信息，与"网格预览"窗口保持一致的样式和排版 - 水平排版
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"顶点数：{meshToDisplay.vertexCount}", EditorStyles.miniLabel);
            EditorGUILayout.LabelField($"三角面数：{meshToDisplay.triangles.Length / 3}", EditorStyles.miniLabel);
            EditorGUILayout.EndHorizontal();
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public override void Cleanup()
        {
            if (_previewUtility != null)
            {
                // 使用MeshPreviewUtility.CleanupPreviewUtility确保它被正确从跟踪列表中移除
                MeshOptimizer.Utilities.MeshPreviewUtility.CleanupPreviewUtility(_previewUtility);
                _previewUtility = null;
            }

            base.Cleanup();
        }
    }
}
