using UnityEngine;
using UnityEditor;
using MeshOptimizer.Models;
using MeshOptimizer.Utilities;

namespace MeshOptimizer.Views.PreviewRenderers
{
    /// <summary>
    /// UV预览渲染器，用于显示UV布局
    /// </summary>
    public class UVPreviewRenderer : PreviewRendererBase
    {
        // 网格数据
        private MeshProcessingData _meshData;

        // 调试用临时Mesh
        private Mesh _debugMesh;
        private bool _useDebugMesh = false;

        // UV通道选择 (0=UV0, 1=UV1, 2=UV2, 3=UV3)
        private int _uvChannel = 0;

        // 预览控制 - 自动缩放（已弃用，不再使用）
        // private float _autoScaleFactor = 1.0f;

        // 网格背景
        private Texture2D _gridTexture;
        private Color _gridColor = new Color(0.5f, 0.5f, 0.5f, 0.3f);
        private Color _gridLineColor = new Color(0.7f, 0.7f, 0.7f, 0.5f);
        private int _gridSize = 10;

        /// <summary>
        /// 初始化预览渲染器
        /// </summary>
        public override void Initialize()
        {
            if (_isInitialized)
                return;

            // 创建网格背景纹理
            CreateGridTexture();

            base.Initialize();
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public override void Cleanup()
        {
            // 销毁网格背景纹理
            if (_gridTexture != null)
            {
                Object.DestroyImmediate(_gridTexture);
                _gridTexture = null;
            }

            base.Cleanup();
        }

        /// <summary>
        /// 设置要预览的网格数据
        /// </summary>
        /// <param name="meshData">网格处理数据</param>
        public void SetMeshData(MeshProcessingData meshData)
        {
            _meshData = meshData;
            // 当设置了正常的网格数据时，禁用调试模式
            _useDebugMesh = false;
        }

        /// <summary>
        /// 设置调试用临时Mesh
        /// </summary>
        /// <param name="mesh">调试用Mesh</param>
        public void SetDebugMesh(Mesh mesh)
        {
            _debugMesh = mesh;
            _useDebugMesh = (_debugMesh != null);
        }

        /// <summary>
        /// 设置UV通道
        /// </summary>
        /// <param name="channel">UV通道 (0=UV0, 1=UV1, 2=UV2, 3=UV3)</param>
        public void SetUVChannel(int channel)
        {
            _uvChannel = Mathf.Clamp(channel, 0, 3);
        }

        /// <summary>
        /// 使用指定的预览区域绘制预览
        /// </summary>
        /// <param name="previewRect">预览区域矩形</param>
        public void DrawGUI(Rect previewRect)
        {
            if (!_isInitialized)
                Initialize();

            // 使用传入的预览区域
            _previewRect = previewRect;

            // 绘制预览背景
            GUI.Box(_previewRect, GUIContent.none, EditorStyles.helpBox);

            // 绘制预览区域边界（调试用）
            DrawPreviewBorder();

            // 绘制预览内容
            DrawPreview();
        }

        /// <summary>
        /// 绘制预览区域边界（调试用）
        /// </summary>
        private void DrawPreviewBorder()
        {
            // 绘制预览区域边界
            Handles.BeginGUI();
            Handles.color = Color.red;
            Handles.DrawLine(new Vector2(_previewRect.x, _previewRect.y), new Vector2(_previewRect.x + _previewRect.width, _previewRect.y));
            Handles.DrawLine(new Vector2(_previewRect.x + _previewRect.width, _previewRect.y), new Vector2(_previewRect.x + _previewRect.width, _previewRect.y + _previewRect.height));
            Handles.DrawLine(new Vector2(_previewRect.x + _previewRect.width, _previewRect.y + _previewRect.height), new Vector2(_previewRect.x, _previewRect.y + _previewRect.height));
            Handles.DrawLine(new Vector2(_previewRect.x, _previewRect.y + _previewRect.height), new Vector2(_previewRect.x, _previewRect.y));
            Handles.EndGUI();
        }

        /// <summary>
        /// 绘制预览
        /// </summary>
        protected override void DrawPreview()
        {
            // 检查是否使用调试Mesh
            if (_useDebugMesh && _debugMesh != null)
            {
                // 绘制网格背景
                DrawGrid();

                // 绘制调试Mesh的UV线框
                DrawUVWireframe(_debugMesh);

                return;
            }

            // 正常模式
            if (_meshData == null)
            {
                // 如果没有网格数据，显示提示信息
                base.DrawCenteredText("请选择要预览的对象");
                return;
            }

            Mesh mesh = GetLatestMesh();
            if (mesh == null)
            {
                // 如果没有可用的网格，显示提示信息
                base.DrawCenteredText("无可用的网格数据");
                return;
            }

            // 绘制网格背景
            DrawGrid();

            // 绘制UV线框
            DrawUVWireframe(mesh);

            // 在左上角显示当前显示的mesh类型
            string meshType = GetMeshTypeString(mesh);
            if (!string.IsNullOrEmpty(meshType))
            {
                Rect infoRect = new Rect(_previewRect.x + 5, _previewRect.y + 5, 200, 20);
                GUI.Label(infoRect, meshType);
            }
        }

        /// <summary>
        /// 获取当前显示的mesh类型字符串
        /// </summary>
        /// <param name="mesh">当前显示的mesh</param>
        /// <returns>mesh类型字符串</returns>
        private string GetMeshTypeString(Mesh mesh)
        {
            if (_meshData == null || mesh == null)
                return string.Empty;

            if (_meshData.IsUVPacked && _meshData.UVPackedMesh == mesh)
                return "UV打包后的Mesh";
            else if (_meshData.IsOptimized && _meshData.OptimizedMesh == mesh)
                return "优化后的Mesh";
            else if (_meshData.IsCulled && _meshData.CulledMesh == mesh)
                return "剔除后的Mesh";
            else if (_meshData.ProcessedMesh == mesh)
                return "处理后的Mesh";
            else if (_meshData.OriginalMesh == mesh)
                return "原始Mesh";

            return "未知Mesh";
        }

        /// <summary>
        /// 获取要预览的最新Mesh
        /// </summary>
        /// <returns>最新的Mesh</returns>
        private Mesh GetLatestMesh()
        {
            if (_meshData == null)
                return null;

            // 优先使用UV打包后的Mesh
            if (_meshData.IsUVPacked && _meshData.UVPackedMesh != null)
                return _meshData.UVPackedMesh;

            // 其次使用优化后的Mesh
            if (_meshData.IsOptimized && _meshData.OptimizedMesh != null)
                return _meshData.OptimizedMesh;

            // 再次使用剔除后的Mesh
            if (_meshData.IsCulled && _meshData.CulledMesh != null)
                return _meshData.CulledMesh;

            // 然后使用处理后的Mesh
            if (_meshData.ProcessedMesh != null)
                return _meshData.ProcessedMesh;

            // 最后使用原始Mesh
            return _meshData.OriginalMesh;
        }

        /// <summary>
        /// 计算自动缩放因子，使UV布局适合预览区域
        /// </summary>
        /// <param name="mesh">要预览的网格</param>
        private void CalculateAutoScale(Mesh mesh)
        {
            // 这个方法现在不再需要，因为我们在TransformUV方法中直接计算缩放因子
            // 保留此方法是为了兼容性
        }

        /// <summary>
        /// 创建网格背景纹理
        /// </summary>
        private void CreateGridTexture()
        {
            int textureSize = _gridSize * 10;
            _gridTexture = new Texture2D(textureSize, textureSize);
            _gridTexture.filterMode = FilterMode.Point;
            _gridTexture.wrapMode = TextureWrapMode.Repeat;

            Color[] colors = new Color[textureSize * textureSize];
            for (int y = 0; y < textureSize; y++)
            {
                for (int x = 0; x < textureSize; x++)
                {
                    // 主网格线
                    if (x % _gridSize == 0 || y % _gridSize == 0)
                    {
                        colors[y * textureSize + x] = _gridLineColor;
                    }
                    // 背景
                    else
                    {
                        colors[y * textureSize + x] = _gridColor;
                    }
                }
            }

            _gridTexture.SetPixels(colors);
            _gridTexture.Apply();
        }

        /// <summary>
        /// 绘制网格背景
        /// </summary>
        private void DrawGrid()
        {
            if (_gridTexture == null)
            {
                CreateGridTexture();
            }

            // 绘制纯色背景
            EditorGUI.DrawRect(_previewRect, new Color(0.2f, 0.2f, 0.2f, 1f));
        }

        /// <summary>
        /// 绘制UV线框
        /// </summary>
        /// <param name="mesh">要绘制的网格</param>
        private void DrawUVWireframe(Mesh mesh)
        {
            if (mesh == null)
                return;

            // 根据选择的UV通道获取UV数据
            Vector2[] uvs = GetUVsByChannel(mesh, _uvChannel);
            int[] triangles = mesh.triangles;

            if (uvs == null || uvs.Length == 0 || triangles == null || triangles.Length == 0)
                return;

            // 保存当前GUI颜色
            Color oldColor = GUI.color;
            GUI.color = Color.green;

            // 绘制UV边界框
            DrawUVBoundingBox(uvs);

            // 绘制UV线框
            for (int i = 0; i < triangles.Length; i += 3)
            {
                if (i + 2 < triangles.Length)
                {
                    int index1 = triangles[i];
                    int index2 = triangles[i + 1];
                    int index3 = triangles[i + 2];

                    if (index1 < uvs.Length && index2 < uvs.Length && index3 < uvs.Length)
                    {
                        Vector2 uv1 = TransformUV(uvs[index1]);
                        Vector2 uv2 = TransformUV(uvs[index2]);
                        Vector2 uv3 = TransformUV(uvs[index3]);

                        // 绘制三角形边
                        DrawUVLine(uv1, uv2);
                        DrawUVLine(uv2, uv3);
                        DrawUVLine(uv3, uv1);
                    }
                }
            }

            // 恢复GUI颜色
            GUI.color = oldColor;
        }

        /// <summary>
        /// 绘制UV边界框
        /// </summary>
        private void DrawUVBoundingBox(Vector2[] uvs)
        {
            if (uvs == null || uvs.Length == 0)
                return;

            // 计算UV边界
            Vector2 min = new Vector2(float.MaxValue, float.MaxValue);
            Vector2 max = new Vector2(float.MinValue, float.MinValue);

            foreach (Vector2 uv in uvs)
            {
                min.x = Mathf.Min(min.x, uv.x);
                min.y = Mathf.Min(min.y, uv.y);
                max.x = Mathf.Max(max.x, uv.x);
                max.y = Mathf.Max(max.y, uv.y);
            }

            // 绘制UV边界框
            Vector2 topLeft = TransformUV(new Vector2(min.x, max.y));
            Vector2 topRight = TransformUV(new Vector2(max.x, max.y));
            Vector2 bottomLeft = TransformUV(new Vector2(min.x, min.y));
            Vector2 bottomRight = TransformUV(new Vector2(max.x, min.y));

            // 保存当前颜色
            Color oldColor = Handles.color;
            Handles.color = Color.yellow;

            // 绘制边界框
            Handles.BeginGUI();
            Handles.DrawLine(topLeft, topRight);
            Handles.DrawLine(topRight, bottomRight);
            Handles.DrawLine(bottomRight, bottomLeft);
            Handles.DrawLine(bottomLeft, topLeft);
            Handles.EndGUI();

            // 恢复颜色
            Handles.color = oldColor;
        }

        /// <summary>
        /// 变换UV坐标到屏幕空间
        /// </summary>
        /// <param name="uv">UV坐标</param>
        /// <returns>屏幕空间坐标</returns>
        private Vector2 TransformUV(Vector2 uv)
        {
            // 计算UV边界
            Vector2 min = Vector2.zero;
            Vector2 max = Vector2.one;

            // 计算UV中心点
            Vector2 center = (min + max) * 0.5f;

            // 计算UV尺寸
            Vector2 size = max - min;

            // 计算缩放因子，使UV布局适合预览区域，并留出一些边距
            float margin = 0.1f; // 10%的边距
            float scaleX = (1.0f - 2.0f * margin) * _previewRect.width / size.x;
            float scaleY = (1.0f - 2.0f * margin) * _previewRect.height / size.y;
            float scale = Mathf.Min(scaleX, scaleY);

            // 应用缩放，并居中显示
            Vector2 transformedUV = (uv - center) * scale;

            // 映射到预览区域中心
            float x = _previewRect.x + _previewRect.width * 0.5f + transformedUV.x;
            float y = _previewRect.y + _previewRect.height * 0.5f - transformedUV.y; // 注意Y轴翻转

            return new Vector2(x, y);
        }

        /// <summary>
        /// 绘制UV线
        /// </summary>
        /// <param name="start">起点</param>
        /// <param name="end">终点</param>
        private void DrawUVLine(Vector2 start, Vector2 end)
        {
            // 确保线条在预览区域内
            if (!IsPointInRect(start, _previewRect) && !IsPointInRect(end, _previewRect))
            {
                // 如果两个点都在预览区域外，检查线段是否与预览区域相交
                if (!DoesLineIntersectRect(start, end, _previewRect))
                {
                    return; // 线段完全在预览区域外，不绘制
                }
            }

            // 裁剪线段到预览区域
            if (ClipLineToRect(ref start, ref end, _previewRect))
            {
                Handles.BeginGUI();
                Handles.color = Color.green;
                Handles.DrawLine(start, end);
                Handles.EndGUI();
            }
        }

        /// <summary>
        /// 判断点是否在矩形内
        /// </summary>
        private bool IsPointInRect(Vector2 point, Rect rect)
        {
            return point.x >= rect.x && point.x <= rect.x + rect.width &&
                   point.y >= rect.y && point.y <= rect.y + rect.height;
        }

        /// <summary>
        /// 判断线段是否与矩形相交
        /// </summary>
        private bool DoesLineIntersectRect(Vector2 start, Vector2 end, Rect rect)
        {
            // 检查线段是否与矩形的四条边相交
            return LineIntersectsLine(start, end, new Vector2(rect.x, rect.y), new Vector2(rect.x + rect.width, rect.y)) ||
                   LineIntersectsLine(start, end, new Vector2(rect.x + rect.width, rect.y), new Vector2(rect.x + rect.width, rect.y + rect.height)) ||
                   LineIntersectsLine(start, end, new Vector2(rect.x + rect.width, rect.y + rect.height), new Vector2(rect.x, rect.y + rect.height)) ||
                   LineIntersectsLine(start, end, new Vector2(rect.x, rect.y + rect.height), new Vector2(rect.x, rect.y));
        }

        /// <summary>
        /// 判断两条线段是否相交
        /// </summary>
        private bool LineIntersectsLine(Vector2 a1, Vector2 a2, Vector2 b1, Vector2 b2)
        {
            Vector2 b = a2 - a1;
            Vector2 d = b2 - b1;
            float bDotDPerp = b.x * d.y - b.y * d.x;

            // 如果b和d平行，则它们不相交
            if (bDotDPerp == 0)
                return false;

            Vector2 c = b1 - a1;
            float t = (c.x * d.y - c.y * d.x) / bDotDPerp;
            if (t < 0 || t > 1)
                return false;

            float u = (c.x * b.y - c.y * b.x) / bDotDPerp;
            if (u < 0 || u > 1)
                return false;

            return true;
        }

        /// <summary>
        /// 根据UV通道获取UV数据
        /// </summary>
        /// <param name="mesh">网格</param>
        /// <param name="channel">UV通道 (0=UV0, 1=UV1, 2=UV2, 3=UV3)</param>
        /// <returns>UV数据</returns>
        private Vector2[] GetUVsByChannel(Mesh mesh, int channel)
        {
            switch (channel)
            {
                case 0:
                    return mesh.uv;
                case 1:
                    return mesh.uv2;
                case 2:
                    return mesh.uv3;
                case 3:
                    return mesh.uv4;
                default:
                    return mesh.uv;
            }
        }

        /// <summary>
        /// 裁剪线段到矩形
        /// </summary>
        private bool ClipLineToRect(ref Vector2 start, ref Vector2 end, Rect rect)
        {
            // Cohen-Sutherland线段裁剪算法
            const int INSIDE = 0; // 0000
            const int LEFT = 1;   // 0001
            const int RIGHT = 2;  // 0010
            const int BOTTOM = 4; // 0100
            const int TOP = 8;    // 1000

            // 计算点的区域码
            int ComputeOutCode(Vector2 p)
            {
                int code = INSIDE;

                if (p.x < rect.x)
                    code |= LEFT;
                else if (p.x > rect.x + rect.width)
                    code |= RIGHT;

                if (p.y < rect.y)
                    code |= BOTTOM;
                else if (p.y > rect.y + rect.height)
                    code |= TOP;

                return code;
            }

            int outcode0 = ComputeOutCode(start);
            int outcode1 = ComputeOutCode(end);
            bool accept = false;

            while (true)
            {
                if ((outcode0 | outcode1) == 0)
                {
                    // 两个点都在矩形内
                    accept = true;
                    break;
                }
                else if ((outcode0 & outcode1) != 0)
                {
                    // 两个点都在矩形外的同一侧，线段完全在矩形外
                    break;
                }
                else
                {
                    // 计算交点
                    Vector2 p = Vector2.zero;

                    // 选择在矩形外的点
                    int outcodeOut = outcode0 != 0 ? outcode0 : outcode1;

                    // 计算交点
                    if ((outcodeOut & TOP) != 0)
                    {
                        // 与上边相交
                        p.x = start.x + (end.x - start.x) * (rect.y + rect.height - start.y) / (end.y - start.y);
                        p.y = rect.y + rect.height;
                    }
                    else if ((outcodeOut & BOTTOM) != 0)
                    {
                        // 与下边相交
                        p.x = start.x + (end.x - start.x) * (rect.y - start.y) / (end.y - start.y);
                        p.y = rect.y;
                    }
                    else if ((outcodeOut & RIGHT) != 0)
                    {
                        // 与右边相交
                        p.y = start.y + (end.y - start.y) * (rect.x + rect.width - start.x) / (end.x - start.x);
                        p.x = rect.x + rect.width;
                    }
                    else if ((outcodeOut & LEFT) != 0)
                    {
                        // 与左边相交
                        p.y = start.y + (end.y - start.y) * (rect.x - start.x) / (end.x - start.x);
                        p.x = rect.x;
                    }

                    // 更新点
                    if (outcodeOut == outcode0)
                    {
                        start = p;
                        outcode0 = ComputeOutCode(start);
                    }
                    else
                    {
                        end = p;
                        outcode1 = ComputeOutCode(end);
                    }
                }
            }

            return accept;
        }


    }
}
