using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using MeshOptimizer.Controllers;
using MeshOptimizer.Models;
using MeshOptimizer.Utilities;
using MeshOptimizer.Repository;
using MeshOptimizer.Events;
using MeshOptimizer.Services;

namespace MeshOptimizer.Views.UIComponents
{
    /// <summary>
    /// 网格优化面板
    /// </summary>
    public class OptimizationPanel : PanelBase
    {
        // 工作流控制器
        private WorkflowController _workflowController;

        // 数据仓库
        private IProcessingDataRepository _repository;

        // 预览控制器
        private PreviewController _previewController;

        // 透明度控制器
        private TransparencyController _transparencyController;

        // 优化控制器
        private OptimizationController _optimizationController;

        // 事件系统
        private IEventSystem _eventSystem;

        // 选中对象索引
        private int _selectedIndex = -1;

        // 预览旋转角度
        private Vector2 _previewRotation = new Vector2(20f, 30f);

        // 预览缩放
        private float _previewZoom = 1.0f;

        // 处理参数
        private ProcessingParameters _parameters;



        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="title">面板标题</param>
        public OptimizationPanel(string title) : base(title)
        {
        }

        /// <summary>
        /// 初始化面板
        /// </summary>
        public override void Initialize()
        {
            if (_isInitialized)
                return;

            // 获取工作流控制器
            _workflowController = WorkflowController.Instance;

            // 获取数据仓库
            _repository = _workflowController.GetRepository();

            // 获取预览控制器
            _previewController = PreviewController.Instance;

            // 获取透明度控制器
            _transparencyController = TransparencyController.Instance;

            // 获取优化控制器
            _optimizationController = OptimizationController.Instance;

            // 获取事件系统
            _eventSystem = EventSystem.Instance;

            // 获取处理参数
            _parameters = _repository.GetParameters();

            base.Initialize();
        }

        /// <summary>
        /// 绘制面板内容
        /// </summary>
        protected override void DrawContent()
        {
            // 检查参数是否有效
            if (_parameters == null)
            {
                EditorGUILayout.HelpBox("处理参数未初始化", MessageType.Error);
                return;
            }

            // 检查是否有选中的对象
            if (!_workflowController.HasSelectedObjects)
            {
                EditorGUILayout.HelpBox("请先选择要处理的对象", MessageType.Info);
                return;
            }

            // 绘制优化参数
            DrawOptimizationParameters();

            // 预览内容
            DrawPreviewContent();

            // 绘制处理按钮
            DrawProcessButtons();
        }

        /// <summary>
        /// 直接绘制面板，不使用折叠栏
        /// </summary>
        public void DrawPanelDirect()
        {
            // 检查参数是否有效
            if (_parameters == null)
            {
                EditorGUILayout.HelpBox("处理参数未初始化", MessageType.Error);
                return;
            }

            // 检查是否有选中的对象
            if (!_workflowController.HasSelectedObjects)
            {
                EditorGUILayout.HelpBox("请先选择要处理的对象", MessageType.Info);
                return;
            }

            // 更新选中索引
            _selectedIndex = _workflowController.GetSelectedObjectIndex();

            // 绘制优化参数
            DrawOptimizationParameters();

            // 预览内容
            DrawPreviewContent();

            // 绘制处理按钮
            DrawProcessButtons();
        }

        /// <summary>
        /// 绘制优化参数
        /// </summary>
        private void DrawOptimizationParameters()
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            EditorGUILayout.LabelField("优化参数", EditorStyles.boldLabel);

            // QEM减面阈值 - 使用自定义绘制方法实现gamma映射和不限制范围的滑动条
            float currentOptimizationStrength = _parameters.OptimizationStrength;

            // 获取矩形区域 - 输入框放在滑杆右侧
            Rect rect = EditorGUILayout.GetControlRect();
            Rect labelRect = new Rect(rect.x, rect.y, 100, rect.height);
            // 滑杆放在标签后面
            Rect sliderRect = new Rect(rect.x + 100, rect.y,
                                      rect.width - 100 - 55, rect.height);
            // 输入框放在最右侧
            Rect fieldRect = new Rect(rect.x + rect.width - 50, rect.y, 50, rect.height);

            // 绘制标签
            EditorGUI.LabelField(labelRect, "QEM减面阈值:");

            // 绘制输入框（不限制范围）
            EditorGUI.BeginChangeCheck();
            float inputValue = EditorGUI.DelayedFloatField(fieldRect, GUIContent.none, currentOptimizationStrength);
            bool inputChanged = EditorGUI.EndChangeCheck();

            // 将原始值转换为gamma空间用于滑动条
            float min = 0f;
            float max = 0.2f;
            float gamma = 2.2f;
            float normalizedValue = Mathf.InverseLerp(min, max, Mathf.Clamp(currentOptimizationStrength, min, max));
            float gammaValue = Mathf.Pow(normalizedValue, 1.0f / gamma);

            // 绘制滑动条（在gamma空间）- 不显示当前值
            EditorGUI.BeginChangeCheck();
            // 使用GUI.HorizontalSlider代替EditorGUI.Slider，不显示当前值
            float newGammaValue = GUI.HorizontalSlider(sliderRect, gammaValue, 0f, 1f);
            bool sliderChanged = EditorGUI.EndChangeCheck();

            // 更新属性值
            float newOptimizationStrength = currentOptimizationStrength;
            if (sliderChanged)
            {
                // 将gamma空间的值转换回原始空间
                float newNormalizedValue = Mathf.Pow(newGammaValue, gamma);
                newOptimizationStrength = Mathf.Lerp(min, max, newNormalizedValue);
            }
            else if (inputChanged)
            {
                // 直接使用输入值，不限制范围
                newOptimizationStrength = inputValue;
            }

            // 如果值发生变化，更新参数
            if (newOptimizationStrength != currentOptimizationStrength)
            {
                _parameters.OptimizationStrength = newOptimizationStrength;
            }

            EditorGUILayout.HelpBox("暴力减面，可能会破面。", MessageType.Info);

            EditorGUILayout.EndVertical();
        }

        /// <summary>
        /// 绘制预览内容
        /// </summary>
        private void DrawPreviewContent()
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            EditorGUILayout.LabelField("减面预览", EditorStyles.boldLabel);

            // 获取处理数据列表
            List<MeshProcessingData> processingDataList = _repository.GetAllProcessingData();

            // 预览渲染
            if (_selectedIndex >= 0 && _selectedIndex < processingDataList.Count)
            {
                var selectedData = processingDataList[_selectedIndex];
                if (selectedData != null)
                {
                    // 确保控制器已初始化
                    if (_previewController == null)
                    {
                        _previewController = PreviewController.Instance;
                        _previewController.Initialize();
                    }

                    // 获取预览区域，使用自适应宽度
                    Rect previewRect = GUILayoutUtility.GetRect(GUIContent.none, GUIStyle.none, GUILayout.Height(200));

                    // 绘制背景
                    GUI.Box(previewRect, GUIContent.none, EditorStyles.helpBox);

                    // 处理预览区域的鼠标输入 - 修复鼠标操作问题
                    MeshPreviewUtility.HandlePreviewInput(previewRect, ref _previewRotation, ref _previewZoom);

                    // 使用PreviewController的RenderOptimizationPreview方法渲染优化预览
                    _previewController.RenderOptimizationPreview(
                        previewRect, // 传入预览区域，而不是让渲染器自己创建
                        _selectedIndex,
                        _previewRotation,
                        _previewZoom);

                    // 只在鼠标拖动时重绘，避免持续抢夺焦点
                    if (Event.current.type == EventType.MouseDrag)
                    {
                        EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();
                    }
                }
                else
                {
                    EditorGUILayout.LabelField("选中的对象无效", EditorStyles.centeredGreyMiniLabel);
                }
            }
            else
            {
                EditorGUILayout.LabelField("请选择要预览的对象", EditorStyles.centeredGreyMiniLabel);
            }

            EditorGUILayout.EndVertical();
        }

        /// <summary>
        /// 绘制处理按钮
        /// </summary>
        private void DrawProcessButtons()
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            // 确保优化控制器已初始化
            if (_optimizationController == null)
            {
                _optimizationController = OptimizationController.Instance;
                _optimizationController.Initialize();
            }

            // 显示优化进度
            if (_optimizationController.IsOptimizing)
            {
                EditorGUI.ProgressBar(
                    EditorGUILayout.GetControlRect(false, 20),
                    _optimizationController.OptimizingProgress,
                    _optimizationController.CurrentStep);

                EditorGUILayout.Space();
            }

            // 处理按钮
            EditorGUILayout.BeginHorizontal();

            GUI.enabled = !_optimizationController.IsOptimizing;
            if (GUILayout.Button("优化选中对象"))
            {
                ProcessSelectedObject();
            }

            if (GUILayout.Button("优化所有对象"))
            {
                ProcessAllObjects();
            }
            GUI.enabled = true;

            EditorGUILayout.EndHorizontal();

            // 保存按钮
            EditorGUILayout.BeginHorizontal();

            // 只有当选中对象已优化时才启用保存按钮
            bool canSaveSelected = false;
            if (_selectedIndex >= 0)
            {
                var selectedData = _repository.GetProcessingData(_selectedIndex);
                canSaveSelected = selectedData != null && selectedData.IsOptimized && selectedData.OptimizedMesh != null;
            }

            GUI.enabled = canSaveSelected && !_optimizationController.IsOptimizing;
            if (GUILayout.Button("保存当前优化后的mesh"))
            {
                SaveSelectedOptimizedMesh();
            }
            GUI.enabled = true;

            // 检查是否有任何对象已优化
            bool canSaveAll = false;
            var allData = _repository.GetAllProcessingData();
            if (allData != null && allData.Count > 0)
            {
                foreach (var data in allData)
                {
                    if (data != null && data.IsOptimized && data.OptimizedMesh != null)
                    {
                        canSaveAll = true;
                        break;
                    }
                }
            }

            GUI.enabled = canSaveAll && !_optimizationController.IsOptimizing;
            if (GUILayout.Button("保存所有优化后的mesh"))
            {
                SaveAllOptimizedMeshes();
            }
            GUI.enabled = true;

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndVertical();
        }

        /// <summary>
        /// 处理选中对象
        /// </summary>
        private void ProcessSelectedObject()
        {
            if (_selectedIndex < 0)
            {
                EditorUtility.DisplayDialog("错误", "请先选择要处理的对象", "确定");
                return;
            }

            // 确保优化控制器已初始化
            if (_optimizationController == null)
            {
                _optimizationController = OptimizationController.Instance;
            }

            // 初始化优化控制器
            _optimizationController.Initialize();

            // 直接调用控制器方法，不显示处理状态
            _optimizationController.OptimizeSelectedObject(OnOptimizationCompleted);
        }

        /// <summary>
        /// 优化完成回调
        /// </summary>
        /// <param name="result">处理结果</param>
        private void OnOptimizationCompleted(ProcessingResult result)
        {
            if (!result.IsSuccessful)
            {
                // 只在失败时显示错误信息
                EditorUtility.DisplayDialog("优化失败", result.ErrorMessage, "确定");
            }

            // 强制重绘窗口
            EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();
        }

        /// <summary>
        /// 处理所有对象
        /// </summary>
        private void ProcessAllObjects()
        {
            // 确保优化控制器已初始化
            if (_optimizationController == null)
            {
                _optimizationController = OptimizationController.Instance;
            }

            // 初始化优化控制器
            _optimizationController.Initialize();

            // 直接调用控制器的 OptimizeAllObjects 方法，使用异步处理
            _optimizationController.OptimizeAllObjects(OnAllOptimizationCompleted);
        }

        /// <summary>
        /// 所有对象优化完成回调
        /// </summary>
        /// <param name="results">处理结果列表</param>
        private void OnAllOptimizationCompleted(List<ProcessingResult> results)
        {
            // 统计成功和失败的数量
            int successCount = 0;
            int failCount = 0;

            foreach (var result in results)
            {
                if (result.IsSuccessful)
                {
                    successCount++;
                }
                else
                {
                    failCount++;
                }
            }

            // 只在有失败时显示结果对话框
            if (failCount > 0)
            {
                EditorUtility.DisplayDialog("优化结果", $"成功: {successCount}个, 失败: {failCount}个", "确定");
            }

            // 强制重绘窗口
            EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();
        }



        /// <summary>
        /// 更新面板
        /// </summary>
        public override void UpdateView()
        {
            // 更新选中索引
            _selectedIndex = _workflowController.GetSelectedObjectIndex();

            // 确保优化控制器已初始化
            if (_optimizationController == null)
            {
                _optimizationController = OptimizationController.Instance;
                _optimizationController.Initialize();
            }

            // 如果正在优化，每隔一段时间重绘窗口以更新进度条，避免频繁重绘
            if (_optimizationController.IsOptimizing && EditorApplication.timeSinceStartup % 0.5 < 0.1)
            {
                EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();
            }
        }

        /// <summary>
        /// 保存选中对象的优化后mesh
        /// </summary>
        private void SaveSelectedOptimizedMesh()
        {
            if (_selectedIndex < 0)
            {
                return;
            }

            var selectedData = _repository.GetProcessingData(_selectedIndex);
            if (selectedData == null || !selectedData.IsOptimized || selectedData.OptimizedMesh == null)
            {
                return;
            }

            // 保存mesh
            SaveMeshAsset(selectedData.OptimizedMesh, selectedData.OriginalGameObject);
        }

        /// <summary>
        /// 保存所有优化后的mesh
        /// </summary>
        private void SaveAllOptimizedMeshes()
        {
            var allData = _repository.GetAllProcessingData();
            if (allData == null || allData.Count == 0)
            {
                return;
            }

            int savedCount = 0;
            int failedCount = 0;

            foreach (var data in allData)
            {
                if (data != null && data.IsOptimized && data.OptimizedMesh != null)
                {
                    try
                    {
                        // 保存mesh
                        SaveMeshAsset(data.OptimizedMesh, data.OriginalGameObject);
                        savedCount++;
                    }
                    catch (System.Exception)
                    {
                        failedCount++;
                    }
                }
            }
        }

        /// <summary>
        /// 将mesh保存为asset文件
        /// </summary>
        /// <param name="mesh">要保存的mesh</param>
        /// <param name="sourceGameObject">原始游戏对象</param>
        private void SaveMeshAsset(Mesh mesh, GameObject sourceGameObject)
        {
            if (mesh == null)
            {
                return;
            }

            // 获取原始资产路径
            string originalPath = "Assets";

            try
            {
                // 尝试获取原始游戏对象的资产路径
                if (sourceGameObject != null)
                {
                    // 先尝试从游戏对象本身获取路径
                    string objectPath = AssetDatabase.GetAssetPath(sourceGameObject);

                    // 如果游戏对象没有路径，尝试从其Mesh获取路径
                    if (string.IsNullOrEmpty(objectPath))
                    {
                        MeshFilter meshFilter = sourceGameObject.GetComponent<MeshFilter>();
                        if (meshFilter != null && meshFilter.sharedMesh != null)
                        {
                            objectPath = AssetDatabase.GetAssetPath(meshFilter.sharedMesh);
                        }
                    }

                    // 如果找到了有效路径，记录下来
                    if (!string.IsNullOrEmpty(objectPath))
                    {
                        originalPath = objectPath;
                    }
                }
            }
            catch (System.Exception)
            {
                // 获取资产路径时出错，使用默认路径
                originalPath = "Assets";
            }

            // 使用AssetPathUtility获取安全的保存目录
            string directory = AssetPathUtility.GetSafeSaveDirectory(originalPath);

            // 生成文件名
            string baseName = (sourceGameObject != null) ? sourceGameObject.name + "_optimized" : "mesh_optimized";
            string fileName = baseName;
            string filePath = System.IO.Path.Combine(directory, fileName + ".asset");

            // 检查文件是否已存在，如果存在则添加序号
            int index = 1;
            while (AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(filePath) != null)
            {
                fileName = baseName + "_" + index.ToString("00");
                filePath = System.IO.Path.Combine(directory, fileName + ".asset");
                index++;

                // 防止无限循环
                if (index > 99)
                {
                    return;
                }
            }

            // 创建mesh的副本
            Mesh meshToSave = Object.Instantiate(mesh);

            // 清除顶点色RGB部分，只保留Alpha通道
            meshToSave = MaterialUpdateService.ClearVertexColorRGB(meshToSave);

            // 保存mesh
            AssetDatabase.CreateAsset(meshToSave, filePath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // 选中新创建的资源
            var savedAsset = AssetDatabase.LoadAssetAtPath<Mesh>(filePath);
            if (savedAsset != null)
            {
                Selection.activeObject = savedAsset;
            }
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public override void Cleanup()
        {
            base.Cleanup();
        }
    }
}
