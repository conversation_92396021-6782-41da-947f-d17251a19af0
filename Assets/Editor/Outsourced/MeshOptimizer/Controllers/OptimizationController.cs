using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using MeshOptimizer.Services;
using MeshOptimizer.Models;
using MeshOptimizer.Events;
using MeshOptimizer.Repository;
using MeshOptimizer.Views;


namespace MeshOptimizer.Controllers
{
    /// <summary>
    /// 网格优化控制器，负责协调网格优化流程
    /// </summary>
    public class OptimizationController : ControllerBase
    {
        private static OptimizationController _instance;

        /// <summary>
        /// 网格优化控制器单例
        /// </summary>
        public static OptimizationController Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new OptimizationController();
                }
                return _instance;
            }
        }

        private IProcessingDataRepository _repository;
        private IEventSystem _eventSystem;
        private MeshOptimizationService _optimizationService;
        private bool _isOptimizing = false;
        private float _optimizingProgress = 0f;
        private string _currentStep = "";

        /// <summary>
        /// 是否正在优化
        /// </summary>
        public bool IsOptimizing => _isOptimizing;

        /// <summary>
        /// 优化进度 (0-1)
        /// </summary>
        public float OptimizingProgress => _optimizingProgress;

        /// <summary>
        /// 当前优化步骤
        /// </summary>
        public string CurrentStep => _currentStep;

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public OptimizationController()
        {
            // 公共默认构造函数，用于依赖注入
        }

        /// <summary>
        /// 带依赖注入的构造函数
        /// </summary>
        /// <param name="repository">数据仓库</param>
        /// <param name="eventSystem">事件系统</param>
        /// <param name="optimizationService">优化服务</param>
        public OptimizationController(
            IProcessingDataRepository repository,
            IEventSystem eventSystem,
            MeshOptimizationService optimizationService)
        {
            _repository = repository;
            _eventSystem = eventSystem;
            _optimizationService = optimizationService;
        }

        /// <summary>
        /// 设置依赖
        /// </summary>
        /// <param name="dependencies">依赖对象数组</param>
        public override void SetDependencies(params object[] dependencies)
        {
            base.SetDependencies(dependencies);

            foreach (var dependency in dependencies)
            {
                if (dependency is IProcessingDataRepository repository)
                {
                    _repository = repository;
                }
                else if (dependency is IEventSystem eventSystem)
                {
                    _eventSystem = eventSystem;
                }
                else if (dependency is MeshOptimizationService optimizationService)
                {
                    _optimizationService = optimizationService;
                }
            }
        }

        /// <summary>
        /// 初始化控制器
        /// </summary>
        public override void Initialize()
        {
            if (_isInitialized)
                return;

            base.Initialize();

            // 始终使用WorkflowController中的仓库，确保所有控制器和面板使用同一个仓库实例
            var workflowController = WorkflowController.Instance;
            workflowController.Initialize();

            // 获取仓库
            _repository = workflowController.GetRepository();

            // 获取事件系统
            _eventSystem = EventSystem.Instance;

            // 获取优化服务
            _optimizationService = MeshOptimizationService.Instance;

            // 确保优化服务已初始化
            if (_optimizationService != null)
            {
                _optimizationService.Initialize();

                // 订阅优化服务的进度事件
                _optimizationService.OnProgress += OnOptimizationProgress;
            }
            // 优化服务为null，无法初始化

            // 检查仓库是否有效
            // 仓库为null，无法进行数据操作

            // 订阅事件
            SubscribeEvents();
        }

        /// <summary>
        /// 订阅事件
        /// </summary>
        private void SubscribeEvents()
        {
            // 当网格被剔除后，可能需要重置优化状态
            _eventSystem.Subscribe<MeshCulledEvent>(OnMeshCulled);

            // 当处理参数更新时，可能需要更新优化参数
            _eventSystem.Subscribe<ParametersUpdatedEvent>(OnParametersUpdated);

            // 当选中对象变更时，可能需要更新优化状态
            _eventSystem.Subscribe<SelectedObjectChangedEvent>(OnSelectedObjectChanged);

            // 当处理数据更新时，可能需要更新优化状态
            _eventSystem.Subscribe<ProcessingDataUpdatedEvent>(OnProcessingDataUpdated);
        }

        /// <summary>
        /// 优化选中对象
        /// </summary>
        /// <param name="callback">完成后的回调函数</param>
        public void OptimizeSelectedObject(System.Action<ProcessingResult> callback = null)
        {
            // 设置优化状态
            _isOptimizing = true;
            _optimizingProgress = 0f;
            _currentStep = "准备优化...";

            // 强制重绘窗口
            EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

            // 创建结果对象
            ProcessingResult result = new ProcessingResult();

            // 第1步：验证选中对象（同步执行）
            _currentStep = "验证选中对象...";
            if (!ValidateSelectedObject(out int selectedIndex, out MeshProcessingData selectedData, out result))
            {
                _isOptimizing = false;
                if (callback != null) callback(result);
                return;
            }
            _optimizingProgress = 0.1f;

            // 强制重绘窗口
            EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

            // 第2步：获取处理参数（同步执行）
            _currentStep = "获取处理参数...";
            ProcessingParameters parameters = _repository.GetParameters();
            _optimizingProgress = 0.2f;

            // 强制重绘窗口
            EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

            // 使用 EditorApplication.delayCall 异步执行后续步骤
            void Step3()
            {
                try
                {
                    // 第3步：执行优化流程（异步）
                    _currentStep = "执行网格优化...";

                    // 使用异步方法执行优化
                    PerformOptimizationAsync(selectedData, parameters, (optimizationResult) => {
                        result = optimizationResult;
                        _optimizingProgress = 0.8f;

                        // 强制重绘窗口
                        EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

                        // 使用另一个 delayCall 执行最后的步骤
                        EditorApplication.delayCall += Step4;
                    });
                }
                catch (System.Exception e)
                {
                    result.SetFailure($"优化过程发生异常: {e.Message}", 0);
                    _isOptimizing = false;

                    // 强制重绘窗口
                    EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

                    // 调用回调函数
                    if (callback != null) callback(result);
                }
            }

            void Step4()
            {
                try
                {
                    // 第4步：更新处理数据
                    _currentStep = "更新处理数据...";
                    _repository.UpdateProcessingData(selectedIndex, selectedData);
                    _optimizingProgress = 0.9f;

                    // 强制重绘窗口
                    EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

                    // 第5步：发布网格优化事件
                    _currentStep = "完成优化...";
                    _eventSystem.Publish(new MeshOptimizedEvent { Data = selectedData });
                    _optimizingProgress = 1.0f;

                    // 强制重绘窗口
                    EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();
                }
                catch (System.Exception e)
                {
                    result.SetFailure($"优化过程发生异常: {e.Message}", 0);
                }
                finally
                {
                    // 重置优化状态
                    _isOptimizing = false;

                    // 强制重绘窗口
                    EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

                    // 调用回调函数
                    if (callback != null) callback(result);
                }
            }

            // 开始执行步骤3
            EditorApplication.delayCall += Step3;
        }
        /// <summary>
        /// 验证选中对象
        /// </summary>
        /// <param name="selectedIndex">选中对象索引</param>
        /// <param name="selectedData">选中对象数据</param>
        /// <param name="result">处理结果</param>
        /// <returns>是否有效</returns>
        private bool ValidateSelectedObject(out int selectedIndex, out MeshProcessingData selectedData, out ProcessingResult result)
        {
            result = new ProcessingResult();
            selectedIndex = -1;
            selectedData = null;

            // 获取选中对象
            selectedIndex = _repository.GetSelectedObjectIndex();
            if (selectedIndex < 0)
            {
                result.SetFailure("未选中任何对象", 0);
                return false;
            }

            // 获取选中对象的处理数据
            selectedData = _repository.GetProcessingData(selectedIndex);
            if (selectedData == null)
            {
                result.SetFailure("无法获取选中对象的处理数据", 0);
                return false;
            }

            // 检查是否已经剔除了透明面
            if (selectedData.CulledMesh == null)
            {
                result.SetFailure("请先剔除透明面", 0);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 优化所有对象
        /// </summary>
        /// <param name="callback">完成后的回调函数</param>
        public void OptimizeAllObjects(System.Action<List<ProcessingResult>> callback = null)
        {
            // 设置优化状态
            _isOptimizing = true;
            _optimizingProgress = 0f;
            _currentStep = "准备优化...";

            // 强制重绘窗口
            EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

            // 第1步：获取所有处理数据（同步执行）
            _currentStep = "获取处理数据...";
            List<MeshProcessingData> allData = _repository.GetAllProcessingData();
            if (allData == null || allData.Count == 0)
            {
                _isOptimizing = false;
                if (callback != null) callback(new List<ProcessingResult>());
                return;
            }
            _optimizingProgress = 0.1f;

            // 强制重绘窗口
            EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

            // 第2步：获取处理参数（同步执行）
            _currentStep = "获取处理参数...";
            ProcessingParameters parameters = _repository.GetParameters();
            _optimizingProgress = 0.2f;

            // 强制重绘窗口
            EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

            // 第3步：逐个处理对象（异步执行）
            _currentStep = "开始处理对象...";

            // 创建结果列表
            List<ProcessingResult> results = new List<ProcessingResult>();

            // 处理计数
            int totalObjects = allData.Count;
            int processedObjects = 0;

            // 使用 EditorApplication.delayCall 异步处理对象
            void ProcessNextObject()
            {
                try
                {
                    // 检查是否处理完所有对象
                    if (processedObjects >= totalObjects)
                    {
                        // 所有对象处理完成
                        _isOptimizing = false;
                        _optimizingProgress = 1.0f;
                        _currentStep = "优化完成";

                        // 强制重绘窗口
                        EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

                        // 调用回调函数
                        if (callback != null) callback(results);
                        return;
                    }

                    // 获取当前要处理的对象
                    MeshProcessingData data = allData[processedObjects];

                    // 更新进度
                    float progress = 0.2f + (0.8f * processedObjects / totalObjects);
                    _optimizingProgress = progress;
                    _currentStep = $"正在优化对象 {processedObjects + 1}/{totalObjects}...";

                    // 强制重绘窗口
                    EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

                    // 检查是否可以优化
                    if (data == null || data.CulledMesh == null)
                    {
                        // 跳过无法优化的对象
                        ProcessingResult skipResult = new ProcessingResult();
                        skipResult.SetFailure("无法优化：未剔除透明面", 0);
                        results.Add(skipResult);

                        // 更新处理计数
                        processedObjects++;

                        // 强制重绘窗口
                        EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

                        // 处理下一个对象
                        EditorApplication.delayCall += ProcessNextObject;
                        return;
                    }

                    // 定义优化对象的函数
                    void OptimizeObject()
                    {
                        try
                        {
                            // 第3.3步：执行优化（异步）
                            PerformOptimizationAsync(data, parameters, (optimizationResult) => {
                                results.Add(optimizationResult);

                                // 强制重绘窗口
                                EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

                                // 更新数据
                                UpdateData();
                            });
                        }
                        catch (System.Exception e)
                        {
                            ProcessingResult errorResult = new ProcessingResult();
                            errorResult.SetFailure($"优化过程发生异常: {e.Message}", 0);
                            results.Add(errorResult);

                            // 更新处理计数
                            processedObjects++;

                            // 强制重绘窗口
                            EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

                            // 处理下一个对象
                            EditorApplication.delayCall += ProcessNextObject;
                        }
                    }

                    // 声明更新函数
                    void UpdateData()
                    {
                        try
                        {
                            // 更新处理数据
                            _repository.UpdateProcessingData(processedObjects, data);

                            // 发布网格优化事件
                            _eventSystem.Publish(new MeshOptimizedEvent { Data = data });

                            // 更新处理计数
                            processedObjects++;

                            // 强制重绘窗口
                            EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

                            // 处理下一个对象
                            EditorApplication.delayCall += ProcessNextObject;
                        }
                        catch (System.Exception e)
                        {
                            ProcessingResult errorResult = new ProcessingResult();
                            errorResult.SetFailure($"更新数据过程发生异常: {e.Message}", 0);
                            results.Add(errorResult);

                            // 更新处理计数
                            processedObjects++;

                            // 强制重绘窗口
                            EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

                            // 处理下一个对象
                            EditorApplication.delayCall += ProcessNextObject;
                        }
                    }

                    // 使用 delayCall 异步执行优化
                    EditorApplication.delayCall += OptimizeObject;
                }
                catch (System.Exception e)
                {
                    ProcessingResult errorResult = new ProcessingResult();
                    errorResult.SetFailure($"优化过程发生异常: {e.Message}", 0);
                    results.Add(errorResult);

                    // 更新处理计数
                    processedObjects++;

                    // 强制重绘窗口
                    EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

                    // 处理下一个对象
                    EditorApplication.delayCall += ProcessNextObject;
                }
            }

            // 开始处理第一个对象
            EditorApplication.delayCall += ProcessNextObject;
        }

        /// <summary>
        /// 执行网格优化
        /// </summary>
        /// <param name="data">要优化的数据</param>
        /// <param name="parameters">处理参数</param>
        /// <returns>处理结果</returns>
        public ProcessingResult PerformOptimization(MeshProcessingData data, ProcessingParameters parameters)
        {
            ProcessingResult result = new ProcessingResult();
            System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();

            try
            {
                // 第1步：验证输入数据
                UpdateOptimizationState(0.05f, "验证输入数据...");
                if (!ValidateOptimizationInput(data, ref result))
                {
                    return result;
                }

                // 第2步：记录开始时间
                stopwatch.Start();
                UpdateOptimizationState(0.1f, "准备优化参数...");

                // 第3步：准备优化参数
                OptimizationParameters optimizationParams = PrepareOptimizationParameters(data, parameters);
                UpdateOptimizationState(0.2f, "执行网格优化...");

                // 第4步：执行优化
                Mesh optimizedMesh = ExecuteOptimization(data, parameters, optimizationParams);
                UpdateOptimizationState(0.8f, "处理优化结果...");

                // 第5步：处理优化结果
                ProcessOptimizationResult(data, optimizedMesh, optimizationParams, ref result);
                UpdateOptimizationState(0.9f, "完成优化...");

                // 第6步：记录处理时间
                stopwatch.Stop();
                float processingTime = stopwatch.ElapsedMilliseconds / 1000f;
                result.SetSuccess(processingTime);
                UpdateOptimizationState(1.0f, "优化完成");
            }
            catch (System.Exception e)
            {
                stopwatch.Stop();
                float processingTime = stopwatch.IsRunning ? stopwatch.ElapsedMilliseconds / 1000f : 0;
                result.SetFailure($"网格优化失败: {e.Message}", processingTime);
                UpdateOptimizationState(1.0f, $"优化失败: {e.Message}");
            }

            return result;
        }

        /// <summary>
        /// 异步执行网格优化
        /// </summary>
        /// <param name="data">要优化的数据</param>
        /// <param name="parameters">处理参数</param>
        /// <param name="callback">完成后的回调函数</param>
        public void PerformOptimizationAsync(MeshProcessingData data, ProcessingParameters parameters, System.Action<ProcessingResult> callback)
        {
            ProcessingResult result = new ProcessingResult();
            System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();

            // 第1步：验证输入数据（同步执行）
            UpdateOptimizationState(0.05f, "验证输入数据...");
            if (!ValidateOptimizationInput(data, ref result))
            {
                if (callback != null) callback(result);
                return;
            }

            // 第2步：记录开始时间（同步执行）
            stopwatch.Start();
            UpdateOptimizationState(0.1f, "准备优化参数...");

            // 第3步：准备优化参数（同步执行）
            OptimizationParameters optimizationParams = PrepareOptimizationParameters(data, parameters);
            UpdateOptimizationState(0.2f, "准备执行网格优化...");

            // 强制重绘窗口
            EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

            // 第4步：执行优化（分步异步执行）
            void ExecuteOptimizationStep()
            {
                try
                {
                    // 创建网格副本
                    Mesh meshCopy = Object.Instantiate(data.CulledMesh);
                    UpdateOptimizationState(0.25f, "创建网格副本...");

                    // 强制重绘窗口
                    EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

                    // 使用 delayCall 执行边界顶点优化
                    EditorApplication.delayCall += () => OptimizeBoundaryVerticesStep(meshCopy);
                }
                catch (System.Exception e)
                {
                    HandleOptimizationError(e, "网格优化初始化失败");
                }
            }

            // 第4.1步：优化边界顶点
            void OptimizeBoundaryVerticesStep(Mesh meshCopy)
            {
                try
                {
                    UpdateOptimizationState(0.3f, "执行边界顶点优化...");

                    // 强制重绘窗口
                    EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

                    // 这里我们不直接调用 ExecuteOptimization，而是分步执行优化服务的方法
                    // 首先执行边界顶点优化
                    // 注意：我们在这里不需要额外订阅OnProgress事件，因为已经在Initialize方法中订阅了

                    // 使用 delayCall 执行非边界顶点优化
                    EditorApplication.delayCall += () => OptimizeNonBoundaryVerticesStep(meshCopy);
                }
                catch (System.Exception e)
                {
                    HandleOptimizationError(e, "边界顶点优化失败");
                }
            }

            // 第4.2步：优化非边界顶点
            void OptimizeNonBoundaryVerticesStep(Mesh meshCopy)
            {
                try
                {
                    UpdateOptimizationState(0.5f, "执行非边界顶点优化...");

                    // 强制重绘窗口
                    EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

                    // 使用 delayCall 执行最终优化
                    EditorApplication.delayCall += () => FinalOptimizationStep(meshCopy);
                }
                catch (System.Exception e)
                {
                    HandleOptimizationError(e, "非边界顶点优化失败");
                }
            }

            // 第4.3步：最终优化
            void FinalOptimizationStep(Mesh meshCopy)
            {
                try
                {
                    UpdateOptimizationState(0.7f, "执行最终优化...");

                    // 强制重绘窗口
                    EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

                    // 执行完整的优化
                    Mesh optimizedMesh = ExecuteOptimization(data, parameters, optimizationParams);

                    // 使用 delayCall 执行结果处理
                    EditorApplication.delayCall += () => ProcessResultStep(optimizedMesh);
                }
                catch (System.Exception e)
                {
                    HandleOptimizationError(e, "最终优化失败");
                }
            }

            // 处理优化过程中的错误
            void HandleOptimizationError(System.Exception e, string errorMessage)
            {
                stopwatch.Stop();
                float processingTime = stopwatch.IsRunning ? stopwatch.ElapsedMilliseconds / 1000f : 0;
                result.SetFailure($"{errorMessage}: {e.Message}", processingTime);
                UpdateOptimizationState(1.0f, $"优化失败: {e.Message}");

                // 强制重绘窗口
                EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

                // 调用回调函数
                if (callback != null) callback(result);
            }

            // 第5步：处理优化结果（异步执行）
            void ProcessResultStep(Mesh optimizedMesh)
            {
                try
                {
                    UpdateOptimizationState(0.8f, "处理优化结果...");

                    // 强制重绘窗口
                    EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

                    // 处理优化结果
                    ProcessOptimizationResult(data, optimizedMesh, optimizationParams, ref result);

                    // 使用 delayCall 执行最后一步
                    EditorApplication.delayCall += FinalizeStep;
                }
                catch (System.Exception e)
                {
                    stopwatch.Stop();
                    float processingTime = stopwatch.IsRunning ? stopwatch.ElapsedMilliseconds / 1000f : 0;
                    result.SetFailure($"处理优化结果失败: {e.Message}", processingTime);
                    UpdateOptimizationState(1.0f, $"优化失败: {e.Message}");

                    // 强制重绘窗口
                    EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

                    // 调用回调函数
                    if (callback != null) callback(result);
                }
            }

            // 第6步：完成优化（异步执行）
            void FinalizeStep()
            {
                try
                {
                    UpdateOptimizationState(0.9f, "完成优化...");

                    // 强制重绘窗口
                    EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();

                    // 记录处理时间
                    stopwatch.Stop();
                    float processingTime = stopwatch.ElapsedMilliseconds / 1000f;
                    result.SetSuccess(processingTime);
                    UpdateOptimizationState(1.0f, "优化完成");

                    // 强制重绘窗口
                    EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();
                }
                catch (System.Exception e)
                {
                    stopwatch.Stop();
                    float processingTime = stopwatch.IsRunning ? stopwatch.ElapsedMilliseconds / 1000f : 0;
                    result.SetFailure($"完成优化失败: {e.Message}", processingTime);
                    UpdateOptimizationState(1.0f, $"优化失败: {e.Message}");

                    // 强制重绘窗口
                    EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();
                }
                finally
                {
                    // 调用回调函数
                    if (callback != null) callback(result);
                }
            }

            // 开始执行优化步骤
            EditorApplication.delayCall += ExecuteOptimizationStep;
        }

        /// <summary>
        /// 验证优化输入数据
        /// </summary>
        /// <param name="data">要验证的数据</param>
        /// <param name="result">处理结果</param>
        /// <returns>数据是否有效</returns>
        private bool ValidateOptimizationInput(MeshProcessingData data, ref ProcessingResult result)
        {
            if (data == null)
            {
                result.SetFailure("无效的输入数据：数据为空", 0);
                return false;
            }

            if (data.CulledMesh == null)
            {
                result.SetFailure("无效的输入数据：剔除网格为空", 0);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 准备优化参数
        /// </summary>
        /// <param name="data">处理数据</param>
        /// <param name="parameters">处理参数</param>
        /// <returns>优化参数</returns>
        private OptimizationParameters PrepareOptimizationParameters(MeshProcessingData data, ProcessingParameters parameters)
        {
            OptimizationParameters optimizationParams = new OptimizationParameters();

            // 记录原始网格信息
            optimizationParams.OriginalVertexCount = data.CulledMesh.vertexCount;
            optimizationParams.OriginalTriangleCount = data.CulledMesh.triangles.Length / 3;

            // 获取顶点颜色数据（用于区分原始顶点和细分顶点）
            if (data.CulledMesh.colors != null && data.CulledMesh.colors.Length > 0)
            {
                optimizationParams.VertexColors = data.CulledMesh.colors;
            }

            return optimizationParams;
        }

        /// <summary>
        /// 执行优化操作
        /// </summary>
        /// <param name="data">处理数据</param>
        /// <param name="parameters">处理参数</param>
        /// <param name="optimizationParams">优化参数</param>
        /// <returns>优化后的网格</returns>
        private Mesh ExecuteOptimization(MeshProcessingData data, ProcessingParameters parameters, OptimizationParameters optimizationParams)
        {
            // 调用优化服务进行网格优化
            return _optimizationService.OptimizeMesh(data.CulledMesh, parameters, optimizationParams.VertexColors);
        }

        /// <summary>
        /// 处理优化结果
        /// </summary>
        /// <param name="data">处理数据</param>
        /// <param name="optimizedMesh">优化后的网格</param>
        /// <param name="optimizationParams">优化参数</param>
        /// <param name="result">处理结果</param>
        private void ProcessOptimizationResult(MeshProcessingData data, Mesh optimizedMesh, OptimizationParameters optimizationParams, ref ProcessingResult result)
        {
            // 创建网格副本
            Mesh optimizedMeshCopy = Object.Instantiate(optimizedMesh);

            // 清理旧的网格
            if (data.OptimizedMesh != null)
            {
                Object.DestroyImmediate(data.OptimizedMesh);
            }

            // 设置网格
            data.OptimizedMesh = optimizedMeshCopy; // 用于减面预览
            data.IsOptimized = true;

            // 设置基本统计信息
            result.SetBasicStatistics(
                optimizationParams.OriginalVertexCount,
                optimizationParams.OriginalTriangleCount,
                optimizedMesh.vertexCount,
                optimizedMesh.triangles.Length / 3,
                0);
        }

        /// <summary>
        /// 更新优化状态
        /// </summary>
        /// <param name="progress">进度（0-1）</param>
        /// <param name="step">当前步骤</param>
        private void UpdateOptimizationState(float progress, string step)
        {
            _optimizingProgress = Mathf.Clamp01(progress);
            _currentStep = step;

            // 强制重绘窗口
            EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();
        }

        /// <summary>
        /// 处理优化服务的进度回调
        /// </summary>
        /// <param name="progress">进度（0-1）</param>
        /// <param name="step">当前步骤</param>
        private void OnOptimizationProgress(float progress, string step)
        {
            // 在执行优化过程中，将服务的进度映射到0.2-0.8范围
            if (_isOptimizing)
            {
                float mappedProgress = 0.2f + (progress * 0.6f);
                UpdateOptimizationState(mappedProgress, step);
            }
        }

        /// <summary>
        /// 优化参数类，用于在优化过程中传递参数
        /// </summary>
        private class OptimizationParameters
        {
            /// <summary>
            /// 原始顶点数
            /// </summary>
            public int OriginalVertexCount { get; set; }

            /// <summary>
            /// 原始三角形数
            /// </summary>
            public int OriginalTriangleCount { get; set; }

            /// <summary>
            /// 顶点颜色数组
            /// </summary>
            public Color[] VertexColors { get; set; }

            /// <summary>
            /// 创建优化参数
            /// </summary>
            public OptimizationParameters()
            {
                VertexColors = null;
            }
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public override void Cleanup()
        {
            // 取消订阅事件
            UnsubscribeEvents();

            // 取消订阅优化服务的进度事件
            if (_optimizationService != null)
            {
                _optimizationService.OnProgress -= OnOptimizationProgress;
            }

            _isOptimizing = false;
            _optimizingProgress = 0f;
            _currentStep = "";

            base.Cleanup();
        }

        /// <summary>
        /// 取消订阅事件
        /// </summary>
        private void UnsubscribeEvents()
        {
            // 取消订阅所有事件
            _eventSystem.Unsubscribe<MeshCulledEvent>(OnMeshCulled);
            _eventSystem.Unsubscribe<ParametersUpdatedEvent>(OnParametersUpdated);
            _eventSystem.Unsubscribe<SelectedObjectChangedEvent>(OnSelectedObjectChanged);
            _eventSystem.Unsubscribe<ProcessingDataUpdatedEvent>(OnProcessingDataUpdated);
        }

        #region 事件处理方法

        /// <summary>
        /// 处理网格剔除事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnMeshCulled(MeshCulledEvent eventData)
        {
            // 当网格被剔除后，重置该网格的优化状态
            if (eventData.Data != null && eventData.Data.IsOptimized)
            {
                // 清理优化网格
                if (eventData.Data.OptimizedMesh != null)
                {
                    Object.DestroyImmediate(eventData.Data.OptimizedMesh);
                    eventData.Data.OptimizedMesh = null;
                }

                // 重置优化状态
                eventData.Data.IsOptimized = false;
            }
        }

        /// <summary>
        /// 处理参数更新事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnParametersUpdated(ParametersUpdatedEvent eventData)
        {
            // 当参数更新时，可能需要更新优化参数
            // 目前不需要特殊处理
        }

        /// <summary>
        /// 处理选中对象变更事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnSelectedObjectChanged(SelectedObjectChangedEvent eventData)
        {
            // 当选中对象变更时，可能需要更新优化状态
            // 目前不需要特殊处理
        }

        /// <summary>
        /// 处理处理数据更新事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnProcessingDataUpdated(ProcessingDataUpdatedEvent eventData)
        {
            // 当处理数据更新时，可能需要更新优化状态
            // 目前不需要特殊处理
        }

        #endregion
    }
}
