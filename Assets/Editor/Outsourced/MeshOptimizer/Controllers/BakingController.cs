using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System;
using System.IO;
using MeshOptimizer.Models;
using MeshOptimizer.Services;
using MeshOptimizer.Events;
using MeshOptimizer.Repository;
using MeshOptimizer.Utilities;

namespace MeshOptimizer.Controllers
{
    /// <summary>
    /// 贴图烘焙控制器，负责协调贴图烘焙流程
    /// </summary>
    public class BakingController : ControllerBase
    {
        private static BakingController _instance;

        /// <summary>
        /// 贴图烘焙控制器单例
        /// </summary>
        public static BakingController Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new BakingController();
                }
                return _instance;
            }
        }

        private IProcessingDataRepository _repository;
        private IEventSystem _eventSystem;
        private UVPackingController _uvPackingController;
        private RenderTextureManager _renderTextureManager;
        private MultiMaterialRenderService _renderService;
        private UVIslandDilationService _dilationService;
        private MaterialUpdateService _materialUpdateService;
        private MobileTextureBakingService _mobileTextureBakingService;

        /// <summary>
        /// 检查当前平台是否支持高级渲染功能
        /// </summary>
        /// <returns>是否支持高级渲染功能</returns>
        private bool IsAdvancedRenderingSupported()
        {
            // 检查是否支持RenderTexture
            bool supportsRenderTexture = SystemInfo.supportsRenderTextures;

            // 检查是否为移动平台
            bool isMobilePlatform = Application.isMobilePlatform;

            // 检查图形API
            bool isOpenGLES = SystemInfo.graphicsDeviceType == UnityEngine.Rendering.GraphicsDeviceType.OpenGLES2 ||
                             SystemInfo.graphicsDeviceType == UnityEngine.Rendering.GraphicsDeviceType.OpenGLES3;

            // 如果是移动平台或使用OpenGLES或不支持RenderTexture，则返回false
            return !isMobilePlatform && !isOpenGLES && supportsRenderTexture;
        }

        private bool _isProcessing = false;
        private float _processingProgress = 0f;
        private string _currentStep = "";

        // 烘焙结果
        private Texture2D _bakedAtlas;
        private string _bakedAtlasPath;

        /// <summary>
        /// 是否正在处理
        /// </summary>
        public bool IsProcessing => _isProcessing;

        /// <summary>
        /// 处理进度 (0-1)
        /// </summary>
        public float ProcessingProgress => _processingProgress;

        /// <summary>
        /// 当前处理步骤
        /// </summary>
        public string CurrentStep => _currentStep;

        /// <summary>
        /// 烘焙后的Atlas贴图
        /// </summary>
        public Texture2D BakedAtlas => _bakedAtlas;

        /// <summary>
        /// 烘焙后的Atlas贴图路径
        /// </summary>
        public string BakedAtlasPath => _bakedAtlasPath;

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public BakingController()
        {
            // 公共默认构造函数，用于依赖注入
        }

        /// <summary>
        /// 初始化控制器
        /// </summary>
        public override void Initialize()
        {
            if (_isInitialized)
                return;

            // 获取数据仓库
            _repository = DependencyContainer.Instance.Resolve<IProcessingDataRepository>();
            if (_repository == null)
            {
                // 尝试从WorkflowController获取仓库
                var workflowController = WorkflowController.Instance;
                workflowController.Initialize();
                _repository = workflowController.GetRepository();

                // 如果仍然为空，则创建一个新的实例
                if (_repository == null)
                {
                    _repository = new ProcessingDataRepository(EventSystem.Instance);
                }
            }

            // 获取事件系统
            _eventSystem = EventSystem.Instance;

            // 获取UV打包控制器
            _uvPackingController = UVPackingController.Instance;
            _uvPackingController.Initialize();

            // 创建服务实例
            _renderTextureManager = new RenderTextureManager();
            _renderService = new MultiMaterialRenderService();
            _dilationService = new UVIslandDilationService();
            _materialUpdateService = new MaterialUpdateService();
            _mobileTextureBakingService = new MobileTextureBakingService();

            _isInitialized = true;
        }

        /// <summary>
        /// 烘焙贴图
        /// </summary>
        /// <param name="callback">完成后的回调函数</param>
        public void BakeTexture(Action<ProcessingResult> callback = null)
        {
            // 同步执行，不使用异步操作
            ProcessingResult result = new ProcessingResult();

            try
            {
                // 第1步：获取UV打包控制器的合并Mesh和材质
                if (_uvPackingController == null || _uvPackingController.CombinedMesh == null)
                {
                    result.SetFailure("无法获取合并后的Mesh，请先执行UV打包", 0);
                    if (callback != null) callback(result);
                    return;
                }

                Mesh combinedMesh = _uvPackingController.CombinedMesh;
                Material[] materials = _uvPackingController.CombinedMaterials;

                if (materials == null || materials.Length == 0)
                {
                    result.SetFailure("无法获取合并后的材质", 0);
                    if (callback != null) callback(result);
                    return;
                }

                // 第2步：获取处理参数
                ProcessingParameters parameters = _repository.GetParameters();
                if (parameters == null)
                {
                    // 如果从仓库获取失败，尝试从WorkflowController获取
                    var workflowController = WorkflowController.Instance;
                    parameters = workflowController.SessionController.Parameters;

                    if (parameters == null)
                    {
                        result.SetFailure("无法获取处理参数", 0);
                        if (callback != null) callback(result);
                        return;
                    }
                }

                // 第3步：检查平台兼容性
                _isProcessing = true;
                _currentStep = "检查平台兼容性";
                _processingProgress = 0.1f;

                int textureSize = parameters.TextureSize;
                bool useAdvancedRendering = IsAdvancedRenderingSupported();

                if (useAdvancedRendering)
                {
                    // 高级平台路径 - 使用RenderTexture

                    // 创建RenderTexture
                    _currentStep = "创建RenderTexture";
                    RenderTexture uvMaskRT = _renderTextureManager.CreateRenderTexture(textureSize, textureSize, RenderTextureFormat.ARGB32, "UVMask");
                    RenderTexture atlasRT = _renderTextureManager.CreateRenderTexture(textureSize, textureSize, RenderTextureFormat.ARGB32, "Atlas");

                    // 第4步：渲染UV Mask
                    _currentStep = "渲染UV Mask";
                    _processingProgress = 0.2f;

                    bool maskSuccess = _renderService.RenderUVMask(combinedMesh, uvMaskRT);
                    if (!maskSuccess)
                    {
                        result.SetFailure("UV Mask渲染失败", 0);
                        _isProcessing = false;
                        if (callback != null) callback(result);
                        return;
                    }

                    // 第5步：渲染Atlas贴图
                    _currentStep = "渲染Atlas贴图";
                    _processingProgress = 0.4f;

                    bool atlasSuccess = _renderService.RenderTexture(combinedMesh, materials, atlasRT);
                    if (!atlasSuccess)
                    {
                        result.SetFailure("Atlas贴图渲染失败", 0);
                        _isProcessing = false;
                        if (callback != null) callback(result);
                        return;
                    }

                    // 第6步：执行UV岛扩边
                    _currentStep = "执行UV岛扩边";
                    _processingProgress = 0.6f;

                    // 计算扩边像素数，为UV岛间距的一半
                    int dilationPixels = Mathf.Max(1, Mathf.RoundToInt(parameters.UVPadding * textureSize / 2));

                    // 创建临时RenderTexture用于扩边结果
                    RenderTexture dilatedRT = _renderTextureManager.CreateRenderTexture(textureSize, textureSize, RenderTextureFormat.ARGB32, "Dilated");

                    // 执行8方向扩边
                    bool dilationSuccess = _dilationService.Dilate8DirectionsUVIslands(atlasRT, uvMaskRT, dilatedRT, dilationPixels);
     /*               if (!dilationSuccess)
                    {
                        result.SetFailure("UV岛扩边失败", 0);
                        _isProcessing = false;
                        if (callback != null) callback(result);
                        return;
                    }
    */
                    // 第7步：将RenderTexture转换为Texture2D并保存
                    _currentStep = "保存贴图";
                    _processingProgress = 0.8f;

                    // 转换为Texture2D
                    _bakedAtlas = _renderTextureManager.ConvertToTexture2D(dilatedRT, TextureFormat.RGBA32);
                }
                else
                {
                    // 低端平台路径 - 使用CPU端实现

                    // 第4步：使用CPU端实现烘焙贴图
                    _currentStep = "CPU端烘焙贴图";
                    _processingProgress = 0.4f;

                    // 计算扩边像素数，为UV岛间距的一半
                    int dilationPixels = Mathf.Max(1, Mathf.RoundToInt(parameters.UVPadding * textureSize / 2));

                    // 使用移动平台贴图烘焙服务
                    _bakedAtlas = _mobileTextureBakingService.BakeTexture(combinedMesh, materials, textureSize, parameters.UVPadding);

                    // 检查烘焙结果
                    if (_bakedAtlas == null)
                    {
                        result.SetFailure("CPU端贴图烘焙失败", 0);
                        _isProcessing = false;
                        if (callback != null) callback(result);
                        return;
                    }

                    // 第7步：保存贴图
                    _currentStep = "保存贴图";
                    _processingProgress = 0.8f;
                }
                if (_bakedAtlas == null)
                {
                    result.SetFailure("贴图转换失败", 0);
                    _isProcessing = false;
                    if (callback != null) callback(result);
                    return;
                }

                // 确定保存路径
                string savePath = GetSavePath(materials);
                if (string.IsNullOrEmpty(savePath))
                {
                    result.SetFailure("无法确定保存路径", 0);
                    _isProcessing = false;
                    if (callback != null) callback(result);
                    return;
                }

                // 保存贴图
                bool saveSuccess = _renderTextureManager.SaveTextureAsTGA(_bakedAtlas, savePath);
                if (!saveSuccess)
                {
                    result.SetFailure("贴图保存失败", 0);
                    _isProcessing = false;
                    if (callback != null) callback(result);
                    return;
                }

                _bakedAtlasPath = savePath;

                // 从资产数据库加载保存的贴图，确保使用的是资产数据库中的贴图
                Texture2D loadedAtlas = AssetDatabase.LoadAssetAtPath<Texture2D>(_bakedAtlasPath);
                if (loadedAtlas != null)
                {
                    _bakedAtlas = loadedAtlas;
                }
                else
                {
                    // 无法从路径加载烘焙贴图，使用内存中的贴图
                }

                // 第8步：发布贴图烘焙事件
                _eventSystem.Publish(new TextureBakedEvent { BakedAtlas = _bakedAtlas, BakedAtlasPath = _bakedAtlasPath });

                // 完成处理
                _isProcessing = false;
                _processingProgress = 1.0f;
                result.SetSuccess(0);
                result.SetBakingInfo(_bakedAtlasPath, textureSize);
            }
            catch (System.Exception e)
            {
                _isProcessing = false;
                result.SetFailure($"贴图烘焙过程中发生异常: {e.Message}", 0);
            }

            // 调用回调函数
            if (callback != null) callback(result);
        }

        /// <summary>
        /// 获取贴图保存路径
        /// </summary>
        /// <param name="materials">材质列表</param>
        /// <returns>保存路径</returns>
        private string GetSavePath(Material[] materials)
        {
            if (materials == null || materials.Length == 0)
                return null;

            // 获取第一个材质的主贴图
            Material firstMaterial = materials[0];
            if (firstMaterial == null || !firstMaterial.HasProperty("_MainTex"))
                return null;

            Texture mainTex = firstMaterial.GetTexture("_MainTex");
            if (mainTex == null)
                return null;

            // 获取主贴图的路径
            string mainTexPath = AssetDatabase.GetAssetPath(mainTex);
            if (string.IsNullOrEmpty(mainTexPath))
                return null;

            // 获取文件名
            string fileName = System.IO.Path.GetFileNameWithoutExtension(mainTexPath) + "_atlas";

            // 使用AssetPathUtility获取安全的保存路径
            string savePath = AssetPathUtility.GetSafeSavePath(mainTexPath, fileName, ".tga");

            return savePath;
        }

        /// <summary>
        /// 应用结果到场景中的游戏对象
        /// </summary>
        /// <param name="callback">完成后的回调函数</param>
        public void ApplyResults(Action<ProcessingResult> callback = null)
        {
            // 同步执行，不使用异步操作
            ProcessingResult result = new ProcessingResult();

            try
            {
                // 第1步：检查是否已烘焙贴图
                if (_bakedAtlas == null || string.IsNullOrEmpty(_bakedAtlasPath))
                {
                    result.SetFailure("尚未烘焙贴图，请先执行贴图烘焙", 0);
                    if (callback != null) callback(result);
                    return;
                }

                // 确认烘焙贴图是否正确加载

                // 尝试从路径重新加载贴图，确保使用的是资产数据库中的贴图
                Texture2D loadedAtlas = AssetDatabase.LoadAssetAtPath<Texture2D>(_bakedAtlasPath);
                if (loadedAtlas != null)
                {
                    _bakedAtlas = loadedAtlas;
                }
                else
                {
                    // 无法从路径加载贴图，使用内存中的贴图
                }

                // 第2步：获取处理数据列表
                List<MeshProcessingData> processingDataList = null;

                // 尝试从WorkflowController获取处理数据
                var workflowController = WorkflowController.Instance;
                workflowController.Initialize();

                // 直接从SelectionController获取处理数据列表
                processingDataList = workflowController.SelectionController.ProcessingDataList;

                if (processingDataList == null || processingDataList.Count == 0)
                {
                    // 如果从WorkflowController获取失败，尝试从仓库获取
                    processingDataList = _repository.GetAllProcessingData();
                }

                if (processingDataList == null || processingDataList.Count == 0)
                {
                    result.SetFailure("无处理数据，请先选择对象", 0);
                    if (callback != null) callback(result);
                    return;
                }

                // 第3步：更新拆分Mesh的UV并保存
                _isProcessing = true;
                _currentStep = "更新拆分Mesh的UV";
                _processingProgress = 0.2f;

                bool saveSuccess = _materialUpdateService.SaveSplitMeshes(processingDataList);
                if (!saveSuccess)
                {
                    result.SetFailure("保存拆分Mesh失败", 0);
                    _isProcessing = false;
                    if (callback != null) callback(result);
                    return;
                }

                // 第4步：复制并更新材质
                _currentStep = "复制并更新材质";
                _processingProgress = 0.5f;

                List<Material> newMaterials = _materialUpdateService.CopyAndUpdateMaterials(processingDataList, _bakedAtlas, _bakedAtlasPath);
                if (newMaterials == null || newMaterials.Count == 0)
                {
                    result.SetFailure("复制材质失败", 0);
                    _isProcessing = false;
                    if (callback != null) callback(result);
                    return;
                }

                // 第5步：应用结果到场景
                _currentStep = "应用结果到场景";
                _processingProgress = 0.8f;

                bool applySuccess = _materialUpdateService.ApplyResultsToScene(processingDataList, newMaterials, _bakedAtlas);
                if (!applySuccess)
                {
                    result.SetFailure("应用结果失败", 0);
                    _isProcessing = false;
                    if (callback != null) callback(result);
                    return;
                }

                // 第6步：发布结果应用事件
                _eventSystem.Publish(new ResultsAppliedEvent { ProcessingDataList = processingDataList, Materials = newMaterials });

                // 完成处理
                _isProcessing = false;
                _processingProgress = 1.0f;
                result.SetSuccess(0);
            }
            catch (System.Exception e)
            {
                _isProcessing = false;
                result.SetFailure($"应用结果过程中发生异常: {e.Message}", 0);
            }

            // 调用回调函数
            if (callback != null) callback(result);
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public override void Cleanup()
        {
            // 清理烘焙结果
            if (_bakedAtlas != null)
            {
                UnityEngine.Object.DestroyImmediate(_bakedAtlas);
                _bakedAtlas = null;
            }

            // 清理服务
            if (_renderTextureManager != null)
            {
                _renderTextureManager.Cleanup();
            }

            if (_renderService != null)
            {
                _renderService.Cleanup();
            }

            if (_dilationService != null)
            {
                _dilationService.Cleanup();
            }

            // MaterialUpdateService不需要特殊清理

            // MobileTextureBakingService不需要特殊清理

            // 重置状态
            _isProcessing = false;
            _processingProgress = 0f;
            _currentStep = "";
            _bakedAtlasPath = null;

            base.Cleanup();
        }
    }
}
