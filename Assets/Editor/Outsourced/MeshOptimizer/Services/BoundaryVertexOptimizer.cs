using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using MeshOptimizer.Models;

namespace MeshOptimizer.Services
{
    /// <summary>
    /// 边界顶点优化器，提供边界顶点优化功能
    /// </summary>
    public class BoundaryVertexOptimizer
    {
        // 边对处理器实例，用于检测三角形重叠
        private EdgePairProcessor _edgePairProcessor = new EdgePairProcessor();
        /// <summary>
        /// 边结构体，用于表示网格中的边
        /// </summary>
        public struct Edge
        {
            public int v1;
            public int v2;

            public Edge(int v1, int v2)
            {
                this.v1 = v1;
                this.v2 = v2;
            }

            public override bool Equals(object obj)
            {
                if (!(obj is Edge))
                    return false;

                Edge other = (Edge)obj;
                return (v1 == other.v1 && v2 == other.v2);
            }

            public override int GetHashCode()
            {
                return v1.GetHashCode() ^ v2.GetHashCode();
            }
        }

        /// <summary>
        /// 添加边到边缘字典
        /// </summary>
        /// <param name="edgeUsage">边缘使用次数字典</param>
        /// <param name="v1">顶点1</param>
        /// <param name="v2">顶点2</param>
        private void AddEdge(Dictionary<Edge, int> edgeUsage, int v1, int v2)
        {
            // 确保v1 < v2，使边的表示唯一
            if (v1 > v2)
            {
                int temp = v1;
                v1 = v2;
                v2 = temp;
            }

            Edge edge = new Edge(v1, v2);

            if (edgeUsage.ContainsKey(edge))
            {
                edgeUsage[edge]++;
            }
            else
            {
                edgeUsage[edge] = 1;
            }
        }

        /// <summary>
        /// 计算两条边的夹角
        /// </summary>
        /// <param name="mesh">网格</param>
        /// <param name="edge1">边1</param>
        /// <param name="edge2">边2</param>
        /// <returns>夹角（度）</returns>
        private float CalculateAngleBetweenEdges(Mesh mesh, Edge edge1, Edge edge2)
        {
            // 获取顶点坐标
            Vector3[] vertices = mesh.vertices;

            // 找出共享顶点
            int sharedVertex = -1;
            if (edge1.v1 == edge2.v1 || edge1.v1 == edge2.v2)
            {
                sharedVertex = edge1.v1;
            }
            else if (edge1.v2 == edge2.v1 || edge1.v2 == edge2.v2)
            {
                sharedVertex = edge1.v2;
            }

            if (sharedVertex == -1)
            {
                // 边不相连
                return -1f;
            }

            // 计算边的方向向量
            Vector3 dir1, dir2;

            if (sharedVertex == edge1.v1)
            {
                dir1 = vertices[edge1.v2] - vertices[sharedVertex];
            }
            else
            {
                dir1 = vertices[edge1.v1] - vertices[sharedVertex];
            }

            if (sharedVertex == edge2.v1)
            {
                dir2 = vertices[edge2.v2] - vertices[sharedVertex];
            }
            else
            {
                dir2 = vertices[edge2.v1] - vertices[sharedVertex];
            }

            // 归一化方向向量
            dir1.Normalize();
            dir2.Normalize();

            // 计算夹角
            float dot = Vector3.Dot(dir1, dir2);
            dot = Mathf.Clamp(dot, -1f, 1f); // 确保dot在[-1,1]范围内

            float angle = Mathf.Acos(dot) * Mathf.Rad2Deg;

            return angle;
        }

        /// <summary>
        /// 构建顶点邻接关系
        /// </summary>
        /// <param name="triangles">三角形数组</param>
        /// <returns>顶点邻接字典</returns>
        private Dictionary<int, List<int>> BuildVertexAdjacency(int[] triangles)
        {
            Dictionary<int, List<int>> adjacency = new Dictionary<int, List<int>>();

            for (int i = 0; i < triangles.Length; i += 3)
            {
                int v1 = triangles[i];
                int v2 = triangles[i + 1];
                int v3 = triangles[i + 2];

                // 添加邻接关系
                AddAdjacency(adjacency, v1, v2);
                AddAdjacency(adjacency, v1, v3);
                AddAdjacency(adjacency, v2, v1);
                AddAdjacency(adjacency, v2, v3);
                AddAdjacency(adjacency, v3, v1);
                AddAdjacency(adjacency, v3, v2);
            }

            return adjacency;
        }

        /// <summary>
        /// 添加顶点邻接关系
        /// </summary>
        /// <param name="adjacency">邻接字典</param>
        /// <param name="v1">顶点1</param>
        /// <param name="v2">顶点2</param>
        private void AddAdjacency(Dictionary<int, List<int>> adjacency, int v1, int v2)
        {
            if (!adjacency.ContainsKey(v1))
            {
                adjacency[v1] = new List<int>();
            }

            if (!adjacency[v1].Contains(v2))
            {
                adjacency[v1].Add(v2);
            }
        }

        /// <summary>
        /// 构建顶点到三角形的映射
        /// </summary>
        /// <param name="triangles">三角形数组</param>
        /// <returns>顶点到三角形的映射字典</returns>
        private Dictionary<int, List<int>> BuildVertexToTriangles(int[] triangles)
        {
            Dictionary<int, List<int>> vertexToTriangles = new Dictionary<int, List<int>>();

            for (int i = 0; i < triangles.Length; i += 3)
            {
                int triangleIndex = i / 3;
                int v1 = triangles[i];
                int v2 = triangles[i + 1];
                int v3 = triangles[i + 2];

                // 添加顶点到三角形的映射
                AddVertexToTriangle(vertexToTriangles, v1, triangleIndex);
                AddVertexToTriangle(vertexToTriangles, v2, triangleIndex);
                AddVertexToTriangle(vertexToTriangles, v3, triangleIndex);
            }

            return vertexToTriangles;
        }

        /// <summary>
        /// 添加顶点到三角形的映射
        /// </summary>
        /// <param name="vertexToTriangles">顶点到三角形的映射字典</param>
        /// <param name="vertexIndex">顶点索引</param>
        /// <param name="triangleIndex">三角形索引</param>
        private void AddVertexToTriangle(Dictionary<int, List<int>> vertexToTriangles, int vertexIndex, int triangleIndex)
        {
            if (!vertexToTriangles.ContainsKey(vertexIndex))
            {
                vertexToTriangles[vertexIndex] = new List<int>();
            }

            vertexToTriangles[vertexIndex].Add(triangleIndex);
        }

        /// <summary>
        /// 优化边界上的细分顶点（基于拐点识别）
        /// </summary>
        /// <param name="mesh">要优化的网格</param>
        /// <param name="vertexTypes">顶点类型字典</param>
        /// <param name="boundaryVertices">边界顶点集合</param>
        /// <returns>优化后的网格</returns>
        /// <remarks>
        /// 该方法专门用于优化网格边界上的细分顶点，通过识别拐点和非拐点，将非拐点合并到拐点来简化边界轮廓。
        ///
        /// 算法原理：
        /// 在网格细分过程中，边界上会添加许多新顶点。我们可以通过识别边界上的拐点（边界线有明显转折的点）
        /// 和非拐点（位于近似直线上的点），将非拐点合并到相邻的拐点，从而简化边界形状。
        ///
        /// 算法步骤：
        /// 1. 准备阶段：
        ///    - 识别所有边界上的细分顶点
        ///    - 创建网格副本用于迭代优化
        ///
        /// 2. 迭代优化（最多MAX_ITERATIONS次）：
        ///    - 构建边缘使用字典，识别边界边
        ///    - 识别边界顶点链
        ///    - 标记拐点和非拐点
        ///    - 将非拐点合并到相邻的拐点
        ///    - 应用顶点映射，更新三角形
        ///    - 移除退化的三角形
        ///    - 交替合并方向，确保均匀处理
        ///    - 如果没有顶点被处理，结束迭代
        ///
        /// 3. 后处理：
        ///    - 清理游离顶点
        ///    - 返回优化后的网格
        ///
        /// 特点：
        /// - 只处理边界上的细分顶点，保留原始边界顶点
        /// - 通过拐点识别保证保持边界的关键形状特征
        /// - 交替合并方向，确保均匀处理
        /// - 迭代处理，确保处理复杂情况
        /// </remarks>
        public Mesh OptimizeBoundarySubdividedVertices(Mesh mesh, Dictionary<int, MeshOptimizationService.VertexTypeFlags> vertexTypes, HashSet<int> boundaryVertices)
        {
            if (mesh == null || vertexTypes == null || boundaryVertices == null)
                return mesh;

            // 获取网格数据
            Vector3[] vertices = mesh.vertices;
            Vector2[] uvs = mesh.uv;
            Vector3[] normals = mesh.normals;
            Color[] colors = mesh.colors;
            int[] triangles = mesh.triangles;

            // 创建要处理的顶点列表（轮廓上的细分顶点）
            List<int> boundarySubdividedVertices = new List<int>();

            // 找出所有轮廓上的细分顶点
            foreach (int vertexIndex in boundaryVertices)
            {
                if (vertexTypes.ContainsKey(vertexIndex) &&
                    (vertexTypes[vertexIndex] & MeshOptimizationService.VertexTypeFlags.Subdivided) != 0 &&
                    (vertexTypes[vertexIndex] & MeshOptimizationService.VertexTypeFlags.Boundary) != 0)
                {
                    boundarySubdividedVertices.Add(vertexIndex);
                }
            }

            // 如果没有要处理的顶点，直接返回原始网格
            if (boundarySubdividedVertices.Count == 0)
            {
                return mesh;
            }

            // 创建一个新的网格，用于迭代优化
            Mesh optimizedMesh = Object.Instantiate(mesh);
            Vector3[] optimizedVertices = optimizedMesh.vertices;
            int[] optimizedTriangles = optimizedMesh.triangles;

            // 设置最大迭代次数，防止无限循环
            const int MAX_ITERATIONS = 10;
            int iterationCount = 0;
            int totalProcessedCount = 0;

            // 合并方向标志，用于交替合并方向
            bool forwardDirection = true;

            // 迭代处理，直到没有顶点可以合并或达到最大迭代次数
            while (iterationCount < MAX_ITERATIONS)
            {
                iterationCount++;

                // 构建边缘字典，记录每条边被使用的次数
                Dictionary<Edge, int> edgeUsage = new Dictionary<Edge, int>();

                // 遍历所有三角形，统计边的使用次数
                for (int i = 0; i < optimizedTriangles.Length; i += 3)
                {
                    int v1 = optimizedTriangles[i];
                    int v2 = optimizedTriangles[i + 1];
                    int v3 = optimizedTriangles[i + 2];

                    // 添加三条边
                    AddEdge(edgeUsage, v1, v2);
                    AddEdge(edgeUsage, v2, v3);
                    AddEdge(edgeUsage, v3, v1);
                }

                // 找出所有边界边（只被使用一次的边）
                Dictionary<Edge, int> boundaryEdges = new Dictionary<Edge, int>();
                foreach (var kvp in edgeUsage)
                {
                    if (kvp.Value == 1)
                    {
                        boundaryEdges[kvp.Key] = kvp.Value;
                    }
                }

                // 重新检测边界顶点
                HashSet<int> currentBoundaryVertices = new HashSet<int>();
                foreach (var edge in boundaryEdges.Keys)
                {
                    currentBoundaryVertices.Add(edge.v1);
                    currentBoundaryVertices.Add(edge.v2);
                }

                // 重新识别边界细分顶点
                List<int> currentBoundarySubdividedVertices = new List<int>();
                foreach (int vertexIndex in currentBoundaryVertices)
                {
                    if (vertexTypes.ContainsKey(vertexIndex) &&
                        (vertexTypes[vertexIndex] & MeshOptimizationService.VertexTypeFlags.Subdivided) != 0 &&
                        (vertexTypes[vertexIndex] & MeshOptimizationService.VertexTypeFlags.Boundary) != 0)
                    {
                        currentBoundarySubdividedVertices.Add(vertexIndex);
                    }
                }

                if (currentBoundarySubdividedVertices.Count == 0)
                {
                    break;
                }

                // 构建顶点到边的映射
                Dictionary<int, List<Edge>> vertexToEdges = new Dictionary<int, List<Edge>>();
                foreach (var edge in boundaryEdges.Keys)
                {
                    // 添加边到v1的映射
                    if (!vertexToEdges.ContainsKey(edge.v1))
                    {
                        vertexToEdges[edge.v1] = new List<Edge>();
                    }
                    vertexToEdges[edge.v1].Add(edge);

                    // 添加边到v2的映射
                    if (!vertexToEdges.ContainsKey(edge.v2))
                    {
                        vertexToEdges[edge.v2] = new List<Edge>();
                    }
                    vertexToEdges[edge.v2].Add(edge);
                }

                // 构建顶点到三角形的映射
                Dictionary<int, List<int>> vertexToTriangles = BuildVertexToTriangles(optimizedTriangles);

                // 步骤1：识别边界顶点链
                List<List<int>> boundaryChains = IdentifyBoundaryChains(currentBoundaryVertices, boundaryEdges);

                // 步骤2：标记拐点和非拐点
                Dictionary<int, bool> isCornerVertex = MarkCornerVertices(optimizedMesh, boundaryChains, vertexToEdges, boundaryEdges);

                // 步骤3：基于拐点合并非拐点
                Dictionary<int, int> vertexMap = MergeNonCornerVertices(optimizedMesh, boundaryChains, isCornerVertex,
                    vertexTypes, vertexToTriangles, forwardDirection);

                // 计算处理的顶点数量
                int processedCount = 0;
                foreach (var kvp in vertexMap)
                {
                    if (kvp.Key != kvp.Value)
                    {
                        processedCount++;
                    }
                }

                totalProcessedCount += processedCount;

                // 处理顶点映射
                if (processedCount > 0)
                {
                    // 创建新的三角形数组，应用顶点映射
                    int[] newTriangles = new int[optimizedTriangles.Length];
                    for (int i = 0; i < optimizedTriangles.Length; i++)
                    {
                        newTriangles[i] = vertexMap[optimizedTriangles[i]];
                    }

                    // 移除退化的三角形（三个顶点相同或者两个顶点相同）
                    List<int> validTriangles = new List<int>();
                    for (int i = 0; i < newTriangles.Length; i += 3)
                    {
                        int v1 = newTriangles[i];
                        int v2 = newTriangles[i + 1];
                        int v3 = newTriangles[i + 2];

                        // 如果三个顶点都不同，保留这个三角形
                        if (v1 != v2 && v2 != v3 && v3 != v1)
                        {
                            validTriangles.Add(v1);
                            validTriangles.Add(v2);
                            validTriangles.Add(v3);
                        }
                    }

                    // 更新优化后的三角形数组
                    optimizedTriangles = validTriangles.ToArray();
                    optimizedMesh.triangles = optimizedTriangles;

                    // 交替合并方向
                    forwardDirection = !forwardDirection;
                }
                else
                {
                    // 如果没有处理任何顶点，结束迭代
                    break;
                }
            }

            // 如果没有处理任何顶点，直接返回原始网格
            if (totalProcessedCount == 0)
                return mesh;

            // 清理游离顶点
            optimizedMesh = RemoveUnusedVertices(optimizedMesh);

            return optimizedMesh;
        }

        /// <summary>
        /// 识别边界顶点链
        /// </summary>
        /// <param name="boundaryVertices">边界顶点集合</param>
        /// <param name="boundaryEdges">边界边字典</param>
        /// <returns>边界顶点链列表，每个链是一个有序的顶点索引列表</returns>
        /// <remarks>
        /// 该方法用于将边界顶点组织成有序的链，每个链代表一个连续的边界轮廓。
        ///
        /// 算法步骤：
        /// 1. 构建顶点到边的映射
        /// 2. 从任意边界顶点开始，沿着边界边行走，收集顶点
        /// 3. 当回到起点或无法继续时，完成一个链的构建
        /// 4. 从剩余未访问的边界顶点中选择一个，重复步骤2-3
        /// 5. 直到所有边界顶点都被访问过
        /// </remarks>
        private List<List<int>> IdentifyBoundaryChains(HashSet<int> boundaryVertices, Dictionary<Edge, int> boundaryEdges)
        {
            List<List<int>> boundaryChains = new List<List<int>>();

            // 如果没有边界顶点或边界边，返回空列表
            if (boundaryVertices == null || boundaryVertices.Count == 0 ||
                boundaryEdges == null || boundaryEdges.Count == 0)
            {
                return boundaryChains;
            }

            // 构建顶点到边的映射
            Dictionary<int, List<Edge>> vertexToEdges = new Dictionary<int, List<Edge>>();
            foreach (var edge in boundaryEdges.Keys)
            {
                // 添加边到v1的映射
                if (!vertexToEdges.ContainsKey(edge.v1))
                {
                    vertexToEdges[edge.v1] = new List<Edge>();
                }
                vertexToEdges[edge.v1].Add(edge);

                // 添加边到v2的映射
                if (!vertexToEdges.ContainsKey(edge.v2))
                {
                    vertexToEdges[edge.v2] = new List<Edge>();
                }
                vertexToEdges[edge.v2].Add(edge);
            }

            // 创建已访问顶点集合
            HashSet<int> visitedVertices = new HashSet<int>();

            // 遍历所有边界顶点，构建边界链
            foreach (int startVertex in boundaryVertices)
            {
                // 如果该顶点已被访问，跳过
                if (visitedVertices.Contains(startVertex))
                {
                    continue;
                }

                // 创建新的边界链
                List<int> chain = new List<int>();
                chain.Add(startVertex);
                visitedVertices.Add(startVertex);

                // 当前顶点
                int currentVertex = startVertex;
                bool chainContinues = true;

                // 沿着边界边行走，构建链
                while (chainContinues)
                {
                    chainContinues = false;

                    // 获取与当前顶点相连的所有边界边
                    if (!vertexToEdges.ContainsKey(currentVertex))
                    {
                        break;
                    }

                    List<Edge> connectedEdges = vertexToEdges[currentVertex];

                    // 遍历所有相连的边
                    foreach (Edge edge in connectedEdges)
                    {
                        // 获取边的另一个顶点
                        int nextVertex = (edge.v1 == currentVertex) ? edge.v2 : edge.v1;

                        // 如果该顶点未被访问，添加到链中
                        if (!visitedVertices.Contains(nextVertex))
                        {
                            chain.Add(nextVertex);
                            visitedVertices.Add(nextVertex);
                            currentVertex = nextVertex;
                            chainContinues = true;
                            break;
                        }
                    }
                }

                // 如果链中有多个顶点，添加到边界链列表
                if (chain.Count > 1)
                {
                    boundaryChains.Add(chain);
                }
            }

            return boundaryChains;
        }

        /// <summary>
        /// 标记边界顶点是否为拐点
        /// </summary>
        /// <param name="mesh">网格</param>
        /// <param name="boundaryChains">边界顶点链</param>
        /// <param name="vertexToEdges">顶点到边的映射</param>
        /// <param name="boundaryEdges">边界边字典</param>
        /// <returns>顶点是否为拐点的字典</returns>
        /// <remarks>
        /// 该方法用于标记边界顶点是否为拐点。拐点的定义为：
        /// 1. 与顶点相连的边界边数量不等于2（即顶点位于边界的端点或交叉点）
        /// 2. 或者，与顶点相连的两条边界边的夹角不在175°~185°范围内（即边界在该点处有明显的转折）
        /// </remarks>
        private Dictionary<int, bool> MarkCornerVertices(Mesh mesh, List<List<int>> boundaryChains,
            Dictionary<int, List<Edge>> vertexToEdges, Dictionary<Edge, int> boundaryEdges)
        {
            Dictionary<int, bool> isCornerVertex = new Dictionary<int, bool>();

            // 如果没有边界链，返回空字典
            if (boundaryChains == null || boundaryChains.Count == 0)
            {
                return isCornerVertex;
            }

            // 获取顶点坐标
            Vector3[] vertices = mesh.vertices;

            // 遍历所有边界链
            foreach (List<int> chain in boundaryChains)
            {
                // 遍历链中的每个顶点
                for (int i = 0; i < chain.Count; i++)
                {
                    int vertexIndex = chain[i];

                    // 获取与该顶点相连的所有边界边
                    if (!vertexToEdges.ContainsKey(vertexIndex))
                    {
                        // 如果顶点没有相连的边，默认标记为拐点
                        isCornerVertex[vertexIndex] = true;
                        continue;
                    }

                    List<Edge> connectedEdges = vertexToEdges[vertexIndex];
                    List<Edge> boundaryConnectedEdges = new List<Edge>();

                    // 筛选出边界边
                    foreach (Edge edge in connectedEdges)
                    {
                        if (boundaryEdges.ContainsKey(edge))
                        {
                            boundaryConnectedEdges.Add(edge);
                        }
                    }

                    // 如果边界边数量不等于2，标记为拐点
                    if (boundaryConnectedEdges.Count != 2)
                    {
                        isCornerVertex[vertexIndex] = true;
                        continue;
                    }

                    // 获取两条边界边
                    Edge edge1 = boundaryConnectedEdges[0];
                    Edge edge2 = boundaryConnectedEdges[1];

                    // 计算两条边的夹角
                    float angle = CalculateAngleBetweenEdges(mesh, edge1, edge2);

                    // 如果夹角不在175°~185°范围内，标记为拐点
                    if (angle < 175f || angle > 185f)
                    {
                        isCornerVertex[vertexIndex] = true;
                    }
                    else
                    {
                        // 否则标记为非拐点
                        isCornerVertex[vertexIndex] = false;
                    }
                }
            }

            return isCornerVertex;
        }

        /// <summary>
        /// 基于拐点合并非拐点
        /// </summary>
        /// <param name="mesh">网格</param>
        /// <param name="boundaryChains">边界顶点链</param>
        /// <param name="isCornerVertex">顶点是否为拐点的字典</param>
        /// <param name="vertexTypes">顶点类型字典</param>
        /// <param name="vertexToTriangles">顶点到三角形的映射</param>
        /// <param name="forwardDirection">是否按前向方向合并</param>
        /// <returns>顶点映射字典</returns>
        /// <remarks>
        /// 该方法用于将非拐点合并到相邻的拐点。
        ///
        /// 算法步骤：
        /// 1. 遍历所有边界链
        /// 2. 对于每个链，根据合并方向（前向或后向）遍历顶点
        /// 3. 对于每个拐点，查找其相邻的非拐点
        /// 4. 如果找到非拐点，检查合并是否会导致三角形重叠
        /// 5. 如果不会导致重叠，将非拐点合并到拐点
        /// </remarks>
        private Dictionary<int, int> MergeNonCornerVertices(Mesh mesh, List<List<int>> boundaryChains,
            Dictionary<int, bool> isCornerVertex, Dictionary<int, MeshOptimizationService.VertexTypeFlags> vertexTypes,
            Dictionary<int, List<int>> vertexToTriangles, bool forwardDirection)
        {
            Dictionary<int, int> vertexMap = new Dictionary<int, int>();

            // 如果没有边界链或拐点信息，返回空映射
            if (boundaryChains == null || boundaryChains.Count == 0 || isCornerVertex == null || isCornerVertex.Count == 0)
            {
                return vertexMap;
            }

            // 获取顶点坐标和三角形
            Vector3[] vertices = mesh.vertices;
            int[] triangles = mesh.triangles;

            // 初始化顶点映射，每个顶点映射到自身
            for (int i = 0; i < vertices.Length; i++)
            {
                vertexMap[i] = i;
            }

            // 遍历所有边界链
            foreach (List<int> chain in boundaryChains)
            {
                // 如果链太短，跳过
                if (chain.Count < 3)
                {
                    continue;
                }

                // 根据合并方向遍历链
                if (forwardDirection)
                {
                    // 前向遍历（从头到尾）
                    for (int i = 0; i < chain.Count; i++)
                    {
                        int currentVertex = chain[i];

                        // 如果当前顶点是拐点，查找其后的非拐点
                        if (isCornerVertex.ContainsKey(currentVertex) && isCornerVertex[currentVertex])
                        {
                            // 查找后一个顶点
                            int nextIndex = (i + 1) % chain.Count;
                            int nextVertex = chain[nextIndex];

                            // 如果下一个顶点是非拐点且是细分顶点，尝试合并
                            if (isCornerVertex.ContainsKey(nextVertex) && !isCornerVertex[nextVertex] &&
                                vertexTypes.ContainsKey(nextVertex) &&
                                (vertexTypes[nextVertex] & MeshOptimizationService.VertexTypeFlags.Subdivided) != 0)
                            {
                                // 检查合并是否会导致三角形重叠
                                bool willCauseOverlap = _edgePairProcessor.WillCauseTriangleOverlap(
                                    vertices, triangles, vertexToTriangles, nextVertex, currentVertex);

                                // 如果不会导致重叠，执行合并
                                if (!willCauseOverlap)
                                {
                                    vertexMap[nextVertex] = currentVertex;
                                }
                            }
                        }
                    }
                }
                else
                {
                    // 后向遍历（从尾到头）
                    for (int i = chain.Count - 1; i >= 0; i--)
                    {
                        int currentVertex = chain[i];

                        // 如果当前顶点是拐点，查找其前的非拐点
                        if (isCornerVertex.ContainsKey(currentVertex) && isCornerVertex[currentVertex])
                        {
                            // 查找前一个顶点
                            int prevIndex = (i - 1 + chain.Count) % chain.Count;
                            int prevVertex = chain[prevIndex];

                            // 如果前一个顶点是非拐点且是细分顶点，尝试合并
                            if (isCornerVertex.ContainsKey(prevVertex) && !isCornerVertex[prevVertex] &&
                                vertexTypes.ContainsKey(prevVertex) &&
                                (vertexTypes[prevVertex] & MeshOptimizationService.VertexTypeFlags.Subdivided) != 0)
                            {
                                // 检查合并是否会导致三角形重叠
                                bool willCauseOverlap = _edgePairProcessor.WillCauseTriangleOverlap(
                                    vertices, triangles, vertexToTriangles, prevVertex, currentVertex);

                                // 如果不会导致重叠，执行合并
                                if (!willCauseOverlap)
                                {
                                    vertexMap[prevVertex] = currentVertex;
                                }
                            }
                        }
                    }
                }
            }

            return vertexMap;
        }

        /// <summary>
        /// 清理网格中未被任何三角面引用的游离顶点
        /// </summary>
        /// <param name="mesh">待清理的网格</param>
        /// <returns>清理后的新网格</returns>
        private Mesh RemoveUnusedVertices(Mesh mesh)
        {
            if (mesh == null)
                return null;

            // 获取网格数据
            Vector3[] vertices = mesh.vertices;
            int[] triangles = mesh.triangles;
            Vector2[] uvs = mesh.uv;
            Vector3[] normals = mesh.normals;
            Color[] colors = mesh.colors;

            // 如果没有三角形，则返回一个空网格
            if (triangles.Length == 0)
            {
                Mesh emptyMesh = new Mesh();
                emptyMesh.name = mesh.name + "_Empty";
                return emptyMesh;
            }

            // 创建一个集合，存储所有被三角形引用的顶点索引
            HashSet<int> usedVertices = new HashSet<int>();
            for (int i = 0; i < triangles.Length; i++)
            {
                usedVertices.Add(triangles[i]);
            }

            // 如果所有顶点都被使用，则直接返回原始网格
            if (usedVertices.Count == vertices.Length)
            {
                return mesh;
            }

            // 创建新的顶点数组和旧索引到新索引的映射
            List<Vector3> newVertices = new List<Vector3>();
            List<Vector2> newUVs = new List<Vector2>();
            List<Vector3> newNormals = new List<Vector3>();
            List<Color> newColors = new List<Color>();
            Dictionary<int, int> oldToNewIndexMap = new Dictionary<int, int>();

            // 遍历所有被使用的顶点，创建新的顶点数组
            foreach (int oldIndex in usedVertices.OrderBy(i => i))
            {
                // 添加顶点到新数组
                newVertices.Add(vertices[oldIndex]);

                // 添加UV到新数组（如果有）
                if (uvs != null && uvs.Length > 0)
                {
                    newUVs.Add(uvs[oldIndex]);
                }

                // 添加法线到新数组（如果有）
                if (normals != null && normals.Length == vertices.Length)
                {
                    newNormals.Add(normals[oldIndex]);
                }

                // 添加顶点颜色到新数组（如果有）
                if (colors != null && colors.Length == vertices.Length)
                {
                    newColors.Add(colors[oldIndex]);
                }

                // 记录旧索引到新索引的映射
                oldToNewIndexMap[oldIndex] = newVertices.Count - 1;
            }

            // 创建新的三角形索引数组，使用新的顶点索引
            int[] newTriangles = new int[triangles.Length];
            for (int i = 0; i < triangles.Length; i++)
            {
                newTriangles[i] = oldToNewIndexMap[triangles[i]];
            }

            // 创建新的网格
            Mesh cleanedMesh = new Mesh();
            cleanedMesh.name = mesh.name;
            cleanedMesh.vertices = newVertices.ToArray();
            cleanedMesh.triangles = newTriangles;

            // 设置其他网格属性
            if (newUVs.Count > 0)
            {
                cleanedMesh.uv = newUVs.ToArray();
            }

            if (newNormals.Count > 0)
            {
                cleanedMesh.normals = newNormals.ToArray();
            }

            if (newColors.Count > 0)
            {
                cleanedMesh.colors = newColors.ToArray();
            }

            // 重新计算边界
            cleanedMesh.RecalculateBounds();

            return cleanedMesh;
        }
    }
}
