using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using MeshOptimizer.Models;

namespace MeshOptimizer.Services
{
    /// <summary>
    /// Atlas贴图烘焙准备服务，用于准备烘焙图集所需的资源
    /// </summary>
    public class AtlasBakingPreparation
    {
        private static AtlasBakingPreparation _instance;

        /// <summary>
        /// 单例实例
        /// </summary>
        public static AtlasBakingPreparation Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new AtlasBakingPreparation();
                }
                return _instance;
            }
        }

        // 烘焙用的半透明材质
        private Material _bakingMaterial;

        // 烘焙用的UV可视化材质
        private Material _uvVisualizationMaterial;

        /// <summary>
        /// 构造函数
        /// </summary>
        public AtlasBakingPreparation()
        {
            // 创建烘焙用的材质
            CreateBakingMaterials();
        }

        /// <summary>
        /// 创建烘焙用的材质
        /// </summary>
        private void CreateBakingMaterials()
        {
            // 创建烘焙用的半透明材质
            Shader unlitShader = Shader.Find("Unlit/Transparent");
            if (unlitShader == null)
            {
                return;
            }

            _bakingMaterial = new Material(unlitShader);
            _bakingMaterial.name = "AtlasBakingMaterial";

            // 创建UV可视化材质
            Shader uvVisShader = Shader.Find("Unlit/Color");
            if (uvVisShader == null)
            {
                return;
            }

            _uvVisualizationMaterial = new Material(uvVisShader);
            _uvVisualizationMaterial.name = "UVVisualizationMaterial";
            _uvVisualizationMaterial.color = Color.white;
        }

        /// <summary>
        /// 准备烘焙数据
        /// </summary>
        /// <param name="combinedMesh">合并后的网格</param>
        /// <param name="materials">材质列表</param>
        /// <param name="parameters">处理参数</param>
        /// <returns>烘焙数据</returns>
        public BakingData PrepareBakingData(Mesh combinedMesh, Material[] materials, ProcessingParameters parameters)
        {
            if (combinedMesh == null || materials == null || materials.Length == 0)
            {
                return null;
            }

            // 创建烘焙数据
            BakingData bakingData = new BakingData
            {
                CombinedMesh = combinedMesh,
                Materials = materials,
                TextureSize = parameters.TextureSize,
                UVPadding = parameters.UVPadding,
                BakingMaterial = _bakingMaterial,
                UVVisualizationMaterial = _uvVisualizationMaterial
            };

            // 收集所有贴图
            CollectTextures(bakingData);

            return bakingData;
        }

        /// <summary>
        /// 收集所有贴图
        /// </summary>
        /// <param name="bakingData">烘焙数据</param>
        private void CollectTextures(BakingData bakingData)
        {
            if (bakingData == null || bakingData.Materials == null)
                return;

            List<Texture2D> textures = new List<Texture2D>();
            List<string> texturePropertyNames = new List<string>();

            foreach (var material in bakingData.Materials)
            {
                if (material == null)
                    continue;

                // 获取材质的所有属性
                Shader shader = material.shader;
                int propertyCount = ShaderUtil.GetPropertyCount(shader);

                for (int i = 0; i < propertyCount; i++)
                {
                    // 只处理纹理属性
                    if (ShaderUtil.GetPropertyType(shader, i) == ShaderUtil.ShaderPropertyType.TexEnv)
                    {
                        string propertyName = ShaderUtil.GetPropertyName(shader, i);
                        Texture texture = material.GetTexture(propertyName);

                        if (texture != null && texture is Texture2D)
                        {
                            textures.Add(texture as Texture2D);
                            texturePropertyNames.Add(propertyName);
                        }
                    }
                }
            }

            bakingData.Textures = textures.ToArray();
            bakingData.TexturePropertyNames = texturePropertyNames.ToArray();
        }

        /// <summary>
        /// 烘焙数据类，存储烘焙所需的所有数据
        /// </summary>
        public class BakingData
        {
            // 合并后的网格
            public Mesh CombinedMesh { get; set; }

            // 材质列表
            public Material[] Materials { get; set; }

            // 贴图列表
            public Texture2D[] Textures { get; set; }

            // 贴图属性名称列表
            public string[] TexturePropertyNames { get; set; }

            // 贴图大小
            public int TextureSize { get; set; }

            // UV边距
            public float UVPadding { get; set; }

            // 烘焙用的半透明材质
            public Material BakingMaterial { get; set; }

            // 烘焙用的UV可视化材质
            public Material UVVisualizationMaterial { get; set; }

            /// <summary>
            /// 获取主贴图
            /// </summary>
            /// <returns>主贴图</returns>
            public Texture2D GetMainTexture()
            {
                if (Textures == null || Textures.Length == 0)
                    return null;

                // 尝试找到名为"_MainTex"的贴图
                for (int i = 0; i < TexturePropertyNames.Length; i++)
                {
                    if (TexturePropertyNames[i] == "_MainTex" && i < Textures.Length)
                    {
                        return Textures[i];
                    }
                }

                // 如果没有找到，返回第一个贴图
                return Textures[0];
            }

            /// <summary>
            /// 获取主贴图所在的文件夹路径
            /// </summary>
            /// <returns>主贴图所在的文件夹路径</returns>
            public string GetMainTextureFolderPath()
            {
                Texture2D mainTexture = GetMainTexture();
                if (mainTexture == null)
                    return "Assets";

                string path = AssetDatabase.GetAssetPath(mainTexture);
                if (string.IsNullOrEmpty(path))
                    return "Assets";

                return System.IO.Path.GetDirectoryName(path);
            }

            /// <summary>
            /// 获取Atlas贴图保存路径
            /// </summary>
            /// <returns>Atlas贴图保存路径</returns>
            public string GetAtlasTexturePath()
            {
                Texture2D mainTexture = GetMainTexture();
                if (mainTexture == null)
                    return "Assets/Atlas.tga";

                string path = AssetDatabase.GetAssetPath(mainTexture);
                if (string.IsNullOrEmpty(path))
                    return "Assets/Atlas.tga";

                string directory = System.IO.Path.GetDirectoryName(path);
                string fileName = System.IO.Path.GetFileNameWithoutExtension(path);

                return $"{directory}/{fileName}_atlas.tga";
            }
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public void Cleanup()
        {
            // 清理烘焙用的材质
            if (_bakingMaterial != null)
            {
                Object.DestroyImmediate(_bakingMaterial);
                _bakingMaterial = null;
            }

            if (_uvVisualizationMaterial != null)
            {
                Object.DestroyImmediate(_uvVisualizationMaterial);
                _uvVisualizationMaterial = null;
            }
        }
    }
}
