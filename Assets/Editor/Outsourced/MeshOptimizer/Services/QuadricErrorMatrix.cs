using UnityEngine;

namespace MeshOptimizer.Services
{
    /// <summary>
    /// 二次误差矩阵，用于QEM算法
    /// </summary>
    public class QuadricErrorMatrix
    {
        // 4x4矩阵，存储为一维数组以提高性能
        public float[] Matrix { get; private set; }

        /// <summary>
        /// 创建一个空的二次误差矩阵
        /// </summary>
        public QuadricErrorMatrix()
        {
            Matrix = new float[16];
            for (int i = 0; i < 16; i++)
            {
                Matrix[i] = 0f;
            }
        }

        /// <summary>
        /// 从平面方程创建二次误差矩阵
        /// </summary>
        /// <param name="plane">平面方程(a,b,c,d)，其中(a,b,c)是单位法向量，d是平面常数</param>
        public QuadricErrorMatrix(Vector4 plane)
        {
            Matrix = new float[16];

            // 计算平面方程系数的外积，形成二次误差矩阵
            // Q = pp^T，其中p是平面方程(a,b,c,d)
            Matrix[0] = plane.x * plane.x;  // a²
            Matrix[1] = plane.x * plane.y;  // ab
            Matrix[2] = plane.x * plane.z;  // ac
            Matrix[3] = plane.x * plane.w;  // ad

            Matrix[4] = plane.y * plane.x;  // ba
            Matrix[5] = plane.y * plane.y;  // b²
            Matrix[6] = plane.y * plane.z;  // bc
            Matrix[7] = plane.y * plane.w;  // bd

            Matrix[8] = plane.z * plane.x;  // ca
            Matrix[9] = plane.z * plane.y;  // cb
            Matrix[10] = plane.z * plane.z; // c²
            Matrix[11] = plane.z * plane.w; // cd

            Matrix[12] = plane.w * plane.x; // da
            Matrix[13] = plane.w * plane.y; // db
            Matrix[14] = plane.w * plane.z; // dc
            Matrix[15] = plane.w * plane.w; // d²
        }

        /// <summary>
        /// 添加另一个二次误差矩阵
        /// </summary>
        /// <param name="other">要添加的矩阵</param>
        public void Add(QuadricErrorMatrix other)
        {
            for (int i = 0; i < 16; i++)
            {
                Matrix[i] += other.Matrix[i];
            }
        }

        /// <summary>
        /// 计算点v在此二次误差矩阵下的误差
        /// </summary>
        /// <param name="v">要计算误差的点</param>
        /// <returns>误差值</returns>
        public float CalculateError(Vector3 v)
        {
            // 计算v^T * Q * v
            float error = 0f;

            // 展开矩阵乘法，避免创建临时向量
            float vx = v.x;
            float vy = v.y;
            float vz = v.z;
            float vw = 1f;

            // 计算 Q * v
            float qv0 = Matrix[0] * vx + Matrix[1] * vy + Matrix[2] * vz + Matrix[3] * vw;
            float qv1 = Matrix[4] * vx + Matrix[5] * vy + Matrix[6] * vz + Matrix[7] * vw;
            float qv2 = Matrix[8] * vx + Matrix[9] * vy + Matrix[10] * vz + Matrix[11] * vw;
            float qv3 = Matrix[12] * vx + Matrix[13] * vy + Matrix[14] * vz + Matrix[15] * vw;

            // 计算 v^T * (Q * v)
            error = vx * qv0 + vy * qv1 + vz * qv2 + vw * qv3;

            return error;
        }

        /// <summary>
        /// 计算最优合并位置
        /// </summary>
        /// <returns>最优合并位置，如果无法计算则返回零向量</returns>
        /// <remarks>
        /// 该方法实现了QEM（Quadric Error Metrics）算法中的关键步骤，用于计算合并两个顶点时的最优位置。
        ///
        /// 算法步骤：
        /// 1. 从二次误差矩阵中提取3x3矩阵A和3x1向量b，表示线性方程组Ax=b
        /// 2. 计算矩阵A的行列式，判断是否可逆
        /// 3. 如果矩阵可逆，计算逆矩阵A^-1
        /// 4. 计算最优位置x = A^-1 * b
        /// 5. 验证结果是否有效（非NaN、非无穷大）
        ///
        /// 数学原理：
        /// - 二次误差函数可表示为 E(v) = v^T * Q * v，其中v是位置向量[x,y,z,1]^T
        /// - 对E(v)求导并令其为零，得到线性方程组 Ax = b
        /// - 其中A是Q的左上3x3子矩阵，b是Q第四列的前三个元素的负值
        /// - 解这个方程组得到的x即为最优合并位置
        ///
        /// 特殊情况处理：
        /// - 如果矩阵A不可逆（行列式接近零），返回零向量
        /// - 如果计算过程中出现异常，返回零向量
        /// - 如果结果包含NaN或无穷大值，返回零向量
        /// </remarks>
        public Vector3 CalculateOptimalPosition()
        {
            // 步骤1：从二次误差矩阵中提取3x3矩阵A和3x1向量b
            // 矩阵A是二次误差矩阵Q的左上3x3子矩阵
            float[,] A = new float[3, 3];
            A[0, 0] = Matrix[0];  // a²
            A[0, 1] = Matrix[1];  // ab
            A[0, 2] = Matrix[2];  // ac
            A[1, 0] = Matrix[4];  // ba
            A[1, 1] = Matrix[5];  // b²
            A[1, 2] = Matrix[6];  // bc
            A[2, 0] = Matrix[8];  // ca
            A[2, 1] = Matrix[9];  // cb
            A[2, 2] = Matrix[10]; // c²

            // 向量b是Q第四列的前三个元素的负值
            float[] b = new float[3];
            b[0] = -Matrix[3];  // -ad
            b[1] = -Matrix[7];  // -bd
            b[2] = -Matrix[11]; // -cd

            // 步骤2-5：求解线性方程组 A * x = b
            try
            {
                // 步骤2：计算行列式，判断矩阵是否可逆
                float det = A[0, 0] * (A[1, 1] * A[2, 2] - A[1, 2] * A[2, 1]) -
                            A[0, 1] * (A[1, 0] * A[2, 2] - A[1, 2] * A[2, 0]) +
                            A[0, 2] * (A[1, 0] * A[2, 1] - A[1, 1] * A[2, 0]);

                // 如果行列式接近零，矩阵不可逆，返回零向量
                // 这种情况通常发生在所有平面近似平行时
                if (Mathf.Abs(det) < 1e-10f)
                    return Vector3.zero;

                // 步骤3：计算逆矩阵 A^-1 = adj(A)/det
                float invDet = 1.0f / det;
                float[,] invA = new float[3, 3];

                // 计算伴随矩阵的元素
                invA[0, 0] = (A[1, 1] * A[2, 2] - A[1, 2] * A[2, 1]) * invDet;
                invA[0, 1] = (A[0, 2] * A[2, 1] - A[0, 1] * A[2, 2]) * invDet;
                invA[0, 2] = (A[0, 1] * A[1, 2] - A[0, 2] * A[1, 1]) * invDet;
                invA[1, 0] = (A[1, 2] * A[2, 0] - A[1, 0] * A[2, 2]) * invDet;
                invA[1, 1] = (A[0, 0] * A[2, 2] - A[0, 2] * A[2, 0]) * invDet;
                invA[1, 2] = (A[0, 2] * A[1, 0] - A[0, 0] * A[1, 2]) * invDet;
                invA[2, 0] = (A[1, 0] * A[2, 1] - A[1, 1] * A[2, 0]) * invDet;
                invA[2, 1] = (A[0, 1] * A[2, 0] - A[0, 0] * A[2, 1]) * invDet;
                invA[2, 2] = (A[0, 0] * A[1, 1] - A[0, 1] * A[1, 0]) * invDet;

                // 步骤4：计算最优位置 x = invA * b
                float x = invA[0, 0] * b[0] + invA[0, 1] * b[1] + invA[0, 2] * b[2];
                float y = invA[1, 0] * b[0] + invA[1, 1] * b[1] + invA[1, 2] * b[2];
                float z = invA[2, 0] * b[0] + invA[2, 1] * b[1] + invA[2, 2] * b[2];

                // 步骤5：验证结果是否有效
                if (float.IsNaN(x) || float.IsNaN(y) || float.IsNaN(z) ||
                    float.IsInfinity(x) || float.IsInfinity(y) || float.IsInfinity(z))
                    return Vector3.zero;

                return new Vector3(x, y, z);
            }
            catch
            {
                // 如果计算过程中出现异常，返回零向量
                return Vector3.zero;
            }
        }
    }
}
