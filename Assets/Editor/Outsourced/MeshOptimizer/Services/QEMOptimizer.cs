using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using MeshOptimizer.Models;

namespace MeshOptimizer.Services
{
    /// <summary>
    /// 二次误差矩阵(QEM)优化器，提供基于QEM算法的网格优化功能
    /// </summary>
    public class QEMOptimizer
    {
        #region 字段和属性

        // 边对处理器实例
        private EdgePairProcessor _edgePairProcessor;

        #endregion

        #region 构造和初始化

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public QEMOptimizer()
        {
            _edgePairProcessor = new EdgePairProcessor();
        }

        #endregion

        #region QEM算法实现

        /// <summary>
        /// 使用QEM算法优化非轮廓细分顶点，保持三角面完整性
        /// </summary>
        /// <param name="mesh">要优化的网格</param>
        /// <param name="vertexTypes">顶点类型字典</param>
        /// <param name="boundaryVertices">边界顶点集合</param>
        /// <param name="optimizationStrength">优化强度，0-1之间（注：对于非轮廓细分顶点，始终全部处理，不应用此参数）</param>
        /// <returns>优化后的网格</returns>
        public Mesh OptimizeNonBoundarySubdividedVerticesWithQEM(Mesh mesh, Dictionary<int, MeshOptimizationService.VertexTypeFlags> vertexTypes, HashSet<int> boundaryVertices, float optimizationStrength)
        {
            if (mesh == null || vertexTypes == null)
                return mesh;

            Vector3[] vertices = mesh.vertices;
            Vector2[] uvs = mesh.uv;
            Vector3[] normals = mesh.normals;
            Color[] colors = mesh.colors;
            int[] triangles = mesh.triangles;

            // 筛选出所有非边界的细分顶点
            List<int> verticesToProcess = new List<int>();
            foreach (var kvp in vertexTypes)
            {
                int vertexIndex = kvp.Key;
                MeshOptimizationService.VertexTypeFlags vertexType = kvp.Value;

                if ((vertexType & MeshOptimizationService.VertexTypeFlags.Subdivided) != 0 && (vertexType & MeshOptimizationService.VertexTypeFlags.Boundary) == 0)
                {
                    verticesToProcess.Add(vertexIndex);
                }
            }

            if (verticesToProcess.Count == 0)
                return mesh;

            // 对于非边界细分顶点，始终处理所有顶点，不考虑优化强度参数

            // 构建顶点邻接关系
            Dictionary<int, List<int>> vertexAdjacency = _edgePairProcessor.BuildVertexAdjacency(triangles);

            // 构建顶点到三角形的映射
            Dictionary<int, List<int>> vertexToTriangles = _edgePairProcessor.BuildVertexToTriangles(triangles);

            // 创建顶点映射（旧索引 -> 新索引）
            Dictionary<int, int> vertexMap = new Dictionary<int, int>();
            for (int i = 0; i < vertices.Length; i++)
            {
                vertexMap[i] = i; // 初始时，每个顶点映射到自身
            }

            // 处理每个要优化的顶点
            foreach (int vertexIndex in verticesToProcess)
            {
                // 获取邻接顶点
                List<int> adjacentVertices = vertexAdjacency.ContainsKey(vertexIndex) ? vertexAdjacency[vertexIndex] : new List<int>();

                // 如果没有邻接顶点，跳过
                if (adjacentVertices.Count == 0)
                    continue;

                // 找出最佳合并目标（优先选择原始顶点或边界顶点）
                int bestTargetIndex = -1;
                float bestError = float.MaxValue;

                foreach (int adjVertex in adjacentVertices)
                {
                    // 跳过已经被处理的顶点
                    if (vertexMap[adjVertex] != adjVertex)
                        continue;

                    // 优先选择原始顶点或边界顶点
                    if (vertexTypes.ContainsKey(adjVertex) &&
                        ((vertexTypes[adjVertex] & MeshOptimizationService.VertexTypeFlags.Original) != 0 || (vertexTypes[adjVertex] & MeshOptimizationService.VertexTypeFlags.Boundary) != 0))
                    {
                        // 计算合并误差
                        float error = _edgePairProcessor.CalculateMergeError(vertices[vertexIndex], vertices[adjVertex]);

                        if (error < bestError)
                        {
                            bestError = error;
                            bestTargetIndex = adjVertex;
                        }
                    }
                }

                // 如果没有找到合适的原始顶点或边界顶点，选择距离最近的邻接顶点
                if (bestTargetIndex == -1)
                {
                    foreach (int adjVertex in adjacentVertices)
                    {
                        // 跳过已经被处理的顶点
                        if (vertexMap[adjVertex] != adjVertex)
                            continue;

                        // 计算顶点间距离作为合并误差
                        float distance = Vector3.Distance(vertices[vertexIndex], vertices[adjVertex]);

                        if (distance < bestError)
                        {
                            bestError = distance;
                            bestTargetIndex = adjVertex;
                        }
                    }
                }

                // 如果找到了合并目标，检查是否会导致三角面重叠
                if (bestTargetIndex != -1)
                {
                    // 检查合并是否会导致三角面重叠
                    bool willCauseTriangleOverlap = _edgePairProcessor.WillCauseTriangleOverlap(
                        vertices, triangles, vertexToTriangles, vertexIndex, bestTargetIndex);

                    // 如果不会导致三角面重叠，则更新顶点映射
                    if (!willCauseTriangleOverlap)
                    {
                        vertexMap[vertexIndex] = bestTargetIndex;
                    }
                    // 否则跳过此顶点，留到下一次迭代处理
                }
            }

            // 创建新的顶点数组
            List<Vector3> newVertices = new List<Vector3>();
            List<Vector2> newUVs = new List<Vector2>();
            List<Vector3> newNormals = new List<Vector3>();
            List<Color> newColors = new List<Color>();

            // 创建新的顶点映射（旧索引 -> 新数组中的索引）
            Dictionary<int, int> finalVertexMap = new Dictionary<int, int>();
            HashSet<int> processedVertices = new HashSet<int>();

            // 处理所有顶点
            for (int i = 0; i < vertices.Length; i++)
            {
                // 获取最终映射目标
                int targetIndex = _edgePairProcessor.GetFinalTargetIndex(i, vertexMap);

                // 如果这个目标顶点已经处理过，跳过
                if (processedVertices.Contains(targetIndex))
                {
                    finalVertexMap[i] = finalVertexMap[targetIndex];
                    continue;
                }

                // 添加到新数组中，并记录新索引
                finalVertexMap[i] = newVertices.Count;
                finalVertexMap[targetIndex] = newVertices.Count;
                processedVertices.Add(targetIndex);

                newVertices.Add(vertices[targetIndex]);

                // 添加其他顶点属性
                if (uvs != null && uvs.Length > 0)
                    newUVs.Add(uvs[targetIndex]);

                if (normals != null && normals.Length > 0)
                    newNormals.Add(normals[targetIndex]);

                if (colors != null && colors.Length > 0)
                    newColors.Add(colors[targetIndex]);
            }

            // 创建新的三角形数组，更新顶点索引
            List<int> newTriangles = new List<int>();

            for (int i = 0; i < triangles.Length; i += 3)
            {
                int v1 = triangles[i];
                int v2 = triangles[i + 1];
                int v3 = triangles[i + 2];

                // 获取新的顶点索引
                int newV1 = finalVertexMap[v1];
                int newV2 = finalVertexMap[v2];
                int newV3 = finalVertexMap[v3];

                // 如果三角形退化（三个顶点映射到同一个新顶点），跳过
                if (newV1 == newV2 || newV2 == newV3 || newV3 == newV1)
                    continue;

                // 添加使用新顶点索引的三角形
                newTriangles.Add(newV1);
                newTriangles.Add(newV2);
                newTriangles.Add(newV3);
            }

            // 创建新的网格
            Mesh optimizedMesh = new Mesh();
            optimizedMesh.vertices = newVertices.ToArray();
            optimizedMesh.triangles = newTriangles.ToArray();

            // 设置其他网格属性
            if (newUVs.Count > 0)
                optimizedMesh.uv = newUVs.ToArray();

            if (newNormals.Count > 0)
                optimizedMesh.normals = newNormals.ToArray();

            if (newColors.Count > 0)
                optimizedMesh.colors = newColors.ToArray();

            // 重新计算边界
            optimizedMesh.RecalculateBounds();

            // 如果没有法线，重新计算法线
            if (newNormals.Count == 0)
                optimizedMesh.RecalculateNormals();

            return optimizedMesh;
        }


        /// <summary>
        /// 使用QEM算法优化网格所有顶点
        /// </summary>
        /// <param name="mesh">要优化的网格</param>
        /// <param name="vertexTypes">顶点类型字典</param>
        /// <param name="boundaryVertices">边界顶点集合</param>
        /// <param name="optimizationStrength">优化强度，0-1之间</param>
        /// <returns>优化后的网格</returns>
        /// <remarks>
        /// 该方法实现了基于QEM（Quadric Error Metrics）的网格简化算法，可以处理网格中的所有顶点。
        ///
        /// 算法步骤：
        /// 1. 准备阶段：
        ///    - 收集所有顶点
        ///    - 根据优化强度确定要处理的顶点数量
        ///    - 构建顶点邻接关系和顶点到三角形的映射
        ///    - 计算每个顶点的二次误差矩阵
        ///
        /// 2. 边对生成与排序：
        ///    - 为每对相邻顶点创建边对
        ///    - 计算每个边对的合并成本和最优合并位置
        ///    - 按合并成本从小到大排序边对
        ///
        /// 3. 顶点合并：
        ///    - 创建顶点映射字典，初始时每个顶点映射到自身
        ///    - 按成本顺序处理边对，直到达到目标简化程度
        ///    - 对于每个边对，将一个顶点合并到另一个顶点
        ///    - 特别处理边界顶点，增加保护措施减少边界变形
        ///
        /// 4. 网格重建：
        ///    - 创建新的顶点和三角形数组
        ///    - 应用顶点映射，更新顶点索引
        ///    - 移除退化的三角形（三个顶点中有两个或更多相同的三角形）
        ///    - 创建新的网格对象
        ///
        /// 5. 后处理：
        ///    - 重新计算法线和边界
        ///    - 返回优化后的网格
        ///
        /// 优化强度参数控制简化的程度：
        /// - 0表示不进行简化
        /// - 1表示最大程度简化
        /// - 中间值按比例控制处理的顶点数量
        /// </remarks>
        public Mesh OptimizeAllVerticesWithQEM(Mesh mesh, Dictionary<int, MeshOptimizationService.VertexTypeFlags> vertexTypes, HashSet<int> boundaryVertices, float optimizationStrength)
        {
            if (mesh == null || vertexTypes == null)
                return mesh;

            Vector3[] vertices = mesh.vertices;
            Vector2[] uvs = mesh.uv;
            Vector3[] normals = mesh.normals;
            Color[] colors = mesh.colors;
            int[] triangles = mesh.triangles;

            // 收集所有顶点
            List<int> allVertices = new List<int>(vertices.Length);
            for (int i = 0; i < vertices.Length; i++)
            {
                allVertices.Add(i);
            }

            if (allVertices.Count == 0)
                return mesh;

            // 根据优化强度确定处理的顶点数量
            int verticesToProcessCount = Mathf.RoundToInt(allVertices.Count * optimizationStrength);
            if (verticesToProcessCount <= 0)
                return mesh;

            List<int> verticesToProcess = allVertices;

            // 构建顶点邻接关系
            Dictionary<int, List<int>> vertexAdjacency = _edgePairProcessor.BuildVertexAdjacency(triangles);

            // 构建顶点到三角形的映射
            Dictionary<int, List<int>> vertexToTriangles = _edgePairProcessor.BuildVertexToTriangles(triangles);

            // 计算每个顶点的二次误差矩阵
            Dictionary<int, QuadricErrorMatrix> vertexQuadrics = _edgePairProcessor.CalculateVertexQuadrics(mesh, vertexToTriangles);

            // 创建边对列表，用于QEM算法
            List<EdgePairProcessor.EdgePair> edgePairs = _edgePairProcessor.CreateEdgePairs(verticesToProcess, vertexAdjacency, vertexQuadrics, vertices, boundaryVertices);



            // 按合并成本排序边对
            edgePairs.Sort((a, b) => a.cost.CompareTo(b.cost));

            // 创建顶点映射（旧索引 -> 新索引）
            Dictionary<int, int> vertexMap = new Dictionary<int, int>();
            for (int i = 0; i < vertices.Length; i++)
            {
                vertexMap[i] = i; // 初始时，每个顶点映射到自身
            }

            // 创建已处理顶点集合
            HashSet<int> processedVertices = new HashSet<int>();

            // 创建顶点UV映射，用于保存合并时的UV坐标
            Dictionary<int, Vector2> vertexUVMap = new Dictionary<int, Vector2>();
            if (uvs != null && uvs.Length > 0)
            {
                for (int i = 0; i < uvs.Length; i++)
                {
                    vertexUVMap[i] = uvs[i];
                }
            }

            // 限制处理的边对数量，根据优化强度
            int edgePairsToProcess = Mathf.Min(edgePairs.Count, verticesToProcessCount);

            // 处理边对
            int processedPairs = 0;
            foreach (EdgePairProcessor.EdgePair edgePair in edgePairs)
            {
                // 如果已经处理了足够多的边对，退出循环
                if (processedPairs >= edgePairsToProcess)
                    break;

                int v1 = edgePair.v1;
                int v2 = edgePair.v2;

                // 获取最终映射目标
                int targetV1 = _edgePairProcessor.GetFinalTargetIndex(v1, vertexMap);
                int targetV2 = _edgePairProcessor.GetFinalTargetIndex(v2, vertexMap);

                // 如果两个顶点已经映射到同一个目标，跳过
                if (targetV1 == targetV2)
                    continue;

                // 如果已经处理过这两个顶点，跳过
                if (processedVertices.Contains(targetV1) || processedVertices.Contains(targetV2))
                    continue;

                // 对于边界顶点，增加保护措施
                bool isBoundaryPair = boundaryVertices.Contains(targetV1) && boundaryVertices.Contains(targetV2);
                if (isBoundaryPair)
                {
                    // 如果两个顶点都是边界顶点，只有在成本很低的情况下才合并
                    if (edgePair.cost > 0.01f) // 设置一个较低的阈值
                        continue;
                }

                // 检查合并是否会导致三角面重叠
                bool willCauseTriangleOverlap = _edgePairProcessor.WillCauseTriangleOverlap(
                    vertices, triangles, vertexToTriangles, targetV2, targetV1);

                // 如果会导致三角面重叠，跳过此边对
                if (willCauseTriangleOverlap)
                    continue;

                // 标记这两个顶点为已处理
                processedVertices.Add(targetV1);
                processedVertices.Add(targetV2);

                // 更新顶点映射，将v2映射到v1
                vertexMap[targetV2] = targetV1;

                // 更新v1的位置为最优合并位置
                vertices[targetV1] = edgePair.optimalPosition;

                // 如果有UV坐标，计算合并后的UV（使用加权平均）
                if (uvs != null && uvs.Length > 0 && vertexUVMap.ContainsKey(targetV1) && vertexUVMap.ContainsKey(targetV2))
                {
                    // 使用简单平均作为合并后的UV坐标
                    Vector2 mergedUV = (vertexUVMap[targetV1] + vertexUVMap[targetV2]) * 0.5f;
                    vertexUVMap[targetV1] = mergedUV;
                }

                processedPairs++;
            }

            // 收集实际被合并的顶点对信息
            List<string> mergedPairs = new List<string>();
            foreach (EdgePairProcessor.EdgePair edgePair in edgePairs)
            {
                int v1 = edgePair.v1;
                int v2 = edgePair.v2;

                // 获取最终映射目标
                int targetV1 = _edgePairProcessor.GetFinalTargetIndex(v1, vertexMap);
                int targetV2 = _edgePairProcessor.GetFinalTargetIndex(v2, vertexMap);

                // 如果v2被映射到v1，说明这对顶点被合并了
                if (targetV1 != targetV2 && vertexMap[targetV2] == targetV1)
                {
                    mergedPairs.Add($"{v2}->{v1}");
                }
            }

            // 创建新的顶点数组
            List<Vector3> newVertices = new List<Vector3>();
            List<Vector2> newUVs = new List<Vector2>();
            List<Vector3> newNormals = new List<Vector3>();
            List<Color> newColors = new List<Color>();

            // 创建新的顶点映射（旧索引 -> 新数组中的索引）
            Dictionary<int, int> finalVertexMap = new Dictionary<int, int>();
            HashSet<int> finalProcessedVertices = new HashSet<int>();

            // 处理所有顶点
            for (int i = 0; i < vertices.Length; i++)
            {
                // 获取最终映射目标
                int targetIndex = _edgePairProcessor.GetFinalTargetIndex(i, vertexMap);

                // 如果这个目标顶点已经处理过，跳过
                if (finalProcessedVertices.Contains(targetIndex))
                {
                    finalVertexMap[i] = finalVertexMap[targetIndex];
                    continue;
                }

                // 添加到新数组中，并记录新索引
                finalVertexMap[i] = newVertices.Count;
                finalVertexMap[targetIndex] = newVertices.Count;
                finalProcessedVertices.Add(targetIndex);

                newVertices.Add(vertices[targetIndex]);

                // 添加其他顶点属性
                if (uvs != null && uvs.Length > 0)
                {
                    // 使用合并后的UV坐标（如果存在）
                    if (vertexUVMap.ContainsKey(targetIndex))
                        newUVs.Add(vertexUVMap[targetIndex]);
                    else
                        newUVs.Add(uvs[targetIndex]);
                }

                if (normals != null && normals.Length > 0)
                    newNormals.Add(normals[targetIndex]);

                if (colors != null && colors.Length > 0)
                    newColors.Add(colors[targetIndex]);
            }

            // 创建新的三角形数组，更新顶点索引
            List<int> newTriangles = new List<int>();

            for (int i = 0; i < triangles.Length; i += 3)
            {
                int v1 = triangles[i];
                int v2 = triangles[i + 1];
                int v3 = triangles[i + 2];

                // 获取新的顶点索引
                int newV1 = finalVertexMap[v1];
                int newV2 = finalVertexMap[v2];
                int newV3 = finalVertexMap[v3];

                // 如果三角形退化（三个顶点映射到同一个新顶点），跳过
                if (newV1 == newV2 || newV2 == newV3 || newV3 == newV1)
                    continue;

                // 添加使用新顶点索引的三角形
                newTriangles.Add(newV1);
                newTriangles.Add(newV2);
                newTriangles.Add(newV3);
            }

            // 创建新的网格
            Mesh optimizedMesh = new Mesh();
            optimizedMesh.vertices = newVertices.ToArray();
            optimizedMesh.triangles = newTriangles.ToArray();

            // 设置其他网格属性
            if (newUVs.Count > 0)
                optimizedMesh.uv = newUVs.ToArray();

            if (newNormals.Count > 0)
                optimizedMesh.normals = newNormals.ToArray();

            if (newColors.Count > 0)
                optimizedMesh.colors = newColors.ToArray();

            // 重新计算边界
            optimizedMesh.RecalculateBounds();

            // 如果没有法线，重新计算法线
            if (newNormals.Count == 0)
                optimizedMesh.RecalculateNormals();

            return optimizedMesh;
        }

        #endregion
    }
}
