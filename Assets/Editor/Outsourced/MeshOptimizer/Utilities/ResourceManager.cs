using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;

namespace MeshOptimizer.Utilities
{












    /// <summary>
    /// 资源管理器，负责管理和缓存材质、ComputeBuffer等资源
    /// </summary>
    public class ResourceManager
    {
        #region 单例实现
        private static ResourceManager _instance;
        public static ResourceManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new ResourceManager();
                }
                return _instance;
            }
        }
        #endregion

        // 资源池
        private ResourcePool<MaterialResource> _materialPool = new ResourcePool<MaterialResource>();
        private ResourcePool<ComputeBufferResource> _computeBufferPool = new ResourcePool<ComputeBufferResource>();
        private ResourcePool<RenderTextureResource> _renderTexturePool = new ResourcePool<RenderTextureResource>();
        private ResourcePool<Texture2DResource> _texture2DPool = new ResourcePool<Texture2DResource>();
        private ResourcePool<MeshResource> _meshPool = new ResourcePool<MeshResource>();

        // 反向映射字典，用于快速查找资源
        private Dictionary<Material, MaterialResource> _materialResourceMap = new Dictionary<Material, MaterialResource>();
        private Dictionary<ComputeBuffer, ComputeBufferResource> _computeBufferResourceMap = new Dictionary<ComputeBuffer, ComputeBufferResource>();
        private Dictionary<RenderTexture, RenderTextureResource> _renderTextureResourceMap = new Dictionary<RenderTexture, RenderTextureResource>();
        private Dictionary<Texture2D, Texture2DResource> _texture2DResourceMap = new Dictionary<Texture2D, Texture2DResource>();
        private Dictionary<Mesh, MeshResource> _meshResourceMap = new Dictionary<Mesh, MeshResource>();

        // 自动清理计时器
        private DateTime _lastCleanupTime = DateTime.Now;
        private TimeSpan _cleanupInterval = TimeSpan.FromMinutes(30); // 增加清理间隔到30分钟

        // 资源使用统计
        private Dictionary<string, int> _resourceTypeUsageCount = new Dictionary<string, int>();

        /// <summary>
        /// 获取或创建材质
        /// </summary>
        /// <param name="shaderName">着色器名称</param>
        /// <param name="setupAction">材质设置回调</param>
        /// <returns>材质实例</returns>
        public Material GetMaterial(string shaderName, Action<Material> setupAction = null)
        {
            // 生成资源ID
            string resourceId = $"Material_{shaderName}";

            // 尝试从资源池获取
            MaterialResource resource = _materialPool.Get(resourceId);

            // 如果资源不存在，创建新资源
            if (resource == null)
            {
                // 创建新材质
                Shader shader = Shader.Find(shaderName);
                if (shader == null)
                {
                    return null;
                }

                Material material = new Material(shader);

                // 创建资源并添加到资源池
                resource = new MaterialResource(resourceId, material, shaderName);
                _materialPool.Add(resource);

                // 更新统计信息
                IncrementResourceTypeUsage("Material");
            }

            // 应用设置 - 确保材质有效再调用回调
            if (resource.Material != null && setupAction != null)
            {
                try
                {
                    setupAction(resource.Material);
                }
                catch (System.Exception)
                {
                    // 设置材质属性时出错
                }
            }

            // 检查是否需要清理资源
            CheckAndCleanupResources();

            return resource.Material;
        }

        /// <summary>
        /// 获取或创建带有特定设置的材质
        /// </summary>
        /// <param name="shaderName">着色器名称</param>
        /// <param name="materialId">材质ID</param>
        /// <param name="setupAction">材质设置回调</param>
        /// <returns>材质实例</returns>
        public Material GetMaterial(string shaderName, string materialId, Action<Material> setupAction = null)
        {
            // 生成资源ID
            string resourceId = $"Material_{shaderName}_{materialId}";

            // 尝试从资源池获取
            MaterialResource resource = _materialPool.Get(resourceId);

            // 如果资源不存在，创建新资源
            if (resource == null)
            {
                // 创建新材质
                Shader shader = Shader.Find(shaderName);
                if (shader == null)
                {
                    return null;
                }

                Material material = new Material(shader);

                // 创建资源并添加到资源池
                resource = new MaterialResource(resourceId, material, shaderName);
                _materialPool.Add(resource);

                // 更新统计信息
                IncrementResourceTypeUsage("Material");
            }

            // 应用设置 - 确保材质有效再调用回调
            if (resource.Material != null && setupAction != null)
            {
                try
                {
                    setupAction(resource.Material);
                }
                catch (System.Exception)
                {
                    // 设置材质属性时出错
                }
            }

            // 检查是否需要清理资源
            CheckAndCleanupResources();

            return resource.Material;
        }

        /// <summary>
        /// 创建预览专用的材质副本，确保它不会被"应用结果"按钮修改
        /// </summary>
        /// <param name="shaderName">着色器名称</param>
        /// <param name="materialId">材质ID</param>
        /// <param name="setupAction">材质设置回调</param>
        /// <returns>材质实例</returns>
        public Material CreatePreviewMaterial(string shaderName, string materialId, Action<Material> setupAction = null)
        {
            // 创建新材质
            Shader shader = Shader.Find(shaderName);
            if (shader == null)
            {
                return null;
            }

            // 创建完全独立的材质副本，不放入资源池
            Material material = new Material(shader);
            material.name = $"PreviewMaterial_{materialId}";

            // 如果是TriangleAlphaVisualizer着色器，设置默认值
            if (shaderName == "Hidden/MeshOptimizer/TriangleAlphaVisualizer")
            {
                // 设置默认值
                material.SetFloat("_AlphaThreshold", 0.5f);
                material.SetFloat("_ShowTriangleAlpha", 1.0f); // 默认开启
                material.SetFloat("_AlphaVisStrength", 0.7f);
            }

            // 应用设置
            if (material != null && setupAction != null)
            {
                try
                {
                    setupAction(material);
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"设置材质属性时出错: {ex.Message}");
                }
            }

            return material;
        }

        /// <summary>
        /// 创建并注册ComputeBuffer
        /// </summary>
        /// <param name="count">元素数量</param>
        /// <param name="stride">元素大小</param>
        /// <returns>ComputeBuffer实例</returns>
        public ComputeBuffer CreateComputeBuffer(int count, int stride)
        {
            // 生成资源ID
            string resourceId = $"ComputeBuffer_{count}_{stride}_{Guid.NewGuid()}";

            // 创建ComputeBuffer
            ComputeBuffer buffer = new ComputeBuffer(count, stride);

            // 创建资源并添加到资源池
            ComputeBufferResource resource = new ComputeBufferResource(resourceId, buffer, count, stride);
            _computeBufferPool.Add(resource);

            // 更新统计信息
            IncrementResourceTypeUsage("ComputeBuffer");

            // 检查是否需要清理资源
            CheckAndCleanupResources();

            return buffer;
        }

        /// <summary>
        /// 释放ComputeBuffer
        /// </summary>
        /// <param name="buffer">要释放的ComputeBuffer</param>
        public void ReleaseComputeBuffer(ComputeBuffer buffer)
        {
            if (buffer == null)
                return;

            // 从反向映射中查找资源
            ComputeBufferResource resourceToRemove = null;
            if (_computeBufferResourceMap.TryGetValue(buffer, out resourceToRemove))
            {
                _computeBufferPool.Remove(resourceToRemove.Id);
                _computeBufferResourceMap.Remove(buffer);
            }
            else
            {
                // 如果没有找到资源，直接释放buffer
                buffer.Release();
            }
        }

        /// <summary>
        /// 创建并注册RenderTexture
        /// </summary>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <param name="format">格式</param>
        /// <returns>RenderTexture实例</returns>
        public RenderTexture CreateRenderTexture(int width, int height, RenderTextureFormat format = RenderTextureFormat.ARGB32)
        {
            // 生成资源ID
            string resourceId = $"RenderTexture_{width}_{height}_{format}_{Guid.NewGuid()}";

            // 创建RenderTexture
            RenderTexture texture = new RenderTexture(width, height, 0, format);
            texture.Create();

            // 创建资源并添加到资源池
            RenderTextureResource resource = new RenderTextureResource(resourceId, texture);
            _renderTexturePool.Add(resource);

            // 更新统计信息
            IncrementResourceTypeUsage("RenderTexture");

            // 检查是否需要清理资源
            CheckAndCleanupResources();

            return texture;
        }

        /// <summary>
        /// 释放RenderTexture
        /// </summary>
        /// <param name="texture">要释放的RenderTexture</param>
        public void ReleaseRenderTexture(RenderTexture texture)
        {
            if (texture == null)
                return;

            // 从反向映射中查找资源
            RenderTextureResource resourceToRemove = null;
            if (_renderTextureResourceMap.TryGetValue(texture, out resourceToRemove))
            {
                _renderTexturePool.Remove(resourceToRemove.Id);
                _renderTextureResourceMap.Remove(texture);
            }
            else
            {
                // 如果没有找到资源，直接释放texture
                texture.Release();
                UnityEngine.Object.DestroyImmediate(texture);
            }
        }

        /// <summary>
        /// 减少资源引用计数
        /// </summary>
        /// <param name="material">材质</param>
        public void ReleaseMaterial(Material material)
        {
            if (material == null)
                return;

            // 从反向映射中查找资源
            MaterialResource resourceToRelease = null;
            if (_materialResourceMap.TryGetValue(material, out resourceToRelease))
            {
                resourceToRelease.RemoveReference();
            }
        }

        /// <summary>
        /// 减少资源引用计数
        /// </summary>
        /// <param name="buffer">ComputeBuffer</param>
        public void ReleaseComputeBufferReference(ComputeBuffer buffer)
        {
            if (buffer == null)
                return;

            // 从反向映射中查找资源
            ComputeBufferResource resourceToRelease = null;
            if (_computeBufferResourceMap.TryGetValue(buffer, out resourceToRelease))
            {
                resourceToRelease.RemoveReference();
            }
        }

        /// <summary>
        /// 减少资源引用计数
        /// </summary>
        /// <param name="texture">RenderTexture</param>
        public void ReleaseRenderTextureReference(RenderTexture texture)
        {
            if (texture == null)
                return;

            // 从反向映射中查找资源
            RenderTextureResource resourceToRelease = null;
            if (_renderTextureResourceMap.TryGetValue(texture, out resourceToRelease))
            {
                resourceToRelease.RemoveReference();
            }
        }

        /// <summary>
        /// 获取或创建Texture2D
        /// </summary>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <param name="format">格式</param>
        /// <param name="mipChain">是否生成mipmap</param>
        /// <param name="linear">是否使用线性颜色空间</param>
        /// <param name="setupAction">纹理设置回调</param>
        /// <returns>Texture2D实例</returns>
        public Texture2D GetTexture2D(int width, int height, TextureFormat format = TextureFormat.RGBA32, bool mipChain = false, bool linear = false, Action<Texture2D> setupAction = null)
        {
            // 生成资源ID
            string resourceId = $"Texture2D_{width}_{height}_{format}_{mipChain}_{linear}_{Guid.NewGuid()}";

            // 创建新纹理
            Texture2D texture = new Texture2D(width, height, format, mipChain, linear);

            // 创建资源并添加到资源池
            Texture2DResource resource = new Texture2DResource(resourceId, texture);
            _texture2DPool.Add(resource);

            // 添加到反向映射
            _texture2DResourceMap[texture] = resource;

            // 更新统计信息
            IncrementResourceTypeUsage("Texture2D");

            // 应用设置 - 确保纹理有效再调用回调
            if (texture != null && setupAction != null)
            {
                try
                {
                    setupAction(texture);
                }
                catch (System.Exception)
                {
                    // 设置纹理属性时出错
                }
            }

            // 检查是否需要清理资源
            CheckAndCleanupResources();

            return texture;
        }

        /// <summary>
        /// 释放Texture2D
        /// </summary>
        /// <param name="texture">要释放的Texture2D</param>
        public void ReleaseTexture2D(Texture2D texture)
        {
            if (texture == null)
                return;

            // 从反向映射中查找资源
            Texture2DResource resourceToRemove = null;
            if (_texture2DResourceMap.TryGetValue(texture, out resourceToRemove))
            {
                _texture2DPool.Remove(resourceToRemove.Id);
                _texture2DResourceMap.Remove(texture);
            }
            else
            {
                // 如果没有找到资源，直接销毁纹理
                UnityEngine.Object.DestroyImmediate(texture);
            }
        }

        /// <summary>
        /// 减少Texture2D引用计数
        /// </summary>
        /// <param name="texture">Texture2D</param>
        public void ReleaseTexture2DReference(Texture2D texture)
        {
            if (texture == null)
                return;

            // 从反向映射中查找资源
            Texture2DResource resourceToRelease = null;
            if (_texture2DResourceMap.TryGetValue(texture, out resourceToRelease))
            {
                resourceToRelease.RemoveReference();
            }
        }

        /// <summary>
        /// 获取或创建Mesh
        /// </summary>
        /// <param name="sourceMesh">源网格，如果提供则复制它</param>
        /// <param name="setupAction">网格设置回调</param>
        /// <returns>Mesh实例</returns>
        public Mesh GetMesh(Mesh sourceMesh = null, Action<Mesh> setupAction = null)
        {
            // 生成资源ID
            string resourceId = $"Mesh_{(sourceMesh != null ? sourceMesh.GetInstanceID() : 0)}_{Guid.NewGuid()}";

            // 创建新网格
            Mesh mesh = null;
            if (sourceMesh != null)
            {
                mesh = UnityEngine.Object.Instantiate(sourceMesh);
                mesh.name = sourceMesh.name + "_Copy";
            }
            else
            {
                mesh = new Mesh();
                mesh.name = "New_Mesh";
            }

            // 创建资源并添加到资源池
            MeshResource resource = new MeshResource(resourceId, mesh);
            _meshPool.Add(resource);

            // 添加到反向映射
            _meshResourceMap[mesh] = resource;

            // 更新统计信息
            IncrementResourceTypeUsage("Mesh");

            // 应用设置 - 确保网格有效再调用回调
            if (mesh != null && setupAction != null)
            {
                try
                {
                    setupAction(mesh);
                }
                catch (System.Exception)
                {
                    // 设置网格属性时出错
                }
            }

            // 检查是否需要清理资源
            CheckAndCleanupResources();

            return mesh;
        }

        /// <summary>
        /// 释放Mesh
        /// </summary>
        /// <param name="mesh">要释放的Mesh</param>
        public void ReleaseMesh(Mesh mesh)
        {
            if (mesh == null)
                return;

            // 从反向映射中查找资源
            MeshResource resourceToRemove = null;
            if (_meshResourceMap.TryGetValue(mesh, out resourceToRemove))
            {
                _meshPool.Remove(resourceToRemove.Id);
                _meshResourceMap.Remove(mesh);
            }
            else
            {
                // 如果没有找到资源，直接销毁网格
                UnityEngine.Object.DestroyImmediate(mesh);
            }
        }

        /// <summary>
        /// 减少Mesh引用计数
        /// </summary>
        /// <param name="mesh">Mesh</param>
        public void ReleaseMeshReference(Mesh mesh)
        {
            if (mesh == null)
                return;

            // 从反向映射中查找资源
            MeshResource resourceToRelease = null;
            if (_meshResourceMap.TryGetValue(mesh, out resourceToRelease))
            {
                resourceToRelease.RemoveReference();
            }
        }

        /// <summary>
        /// 释放所有资源
        /// </summary>
        public void ReleaseResources()
        {
            // 释放所有材质
            _materialPool.ReleaseAll();
            _materialResourceMap.Clear();

            // 释放所有ComputeBuffer
            _computeBufferPool.ReleaseAll();
            _computeBufferResourceMap.Clear();

            // 释放所有RenderTexture
            _renderTexturePool.ReleaseAll();
            _renderTextureResourceMap.Clear();

            // 释放所有Texture2D
            _texture2DPool.ReleaseAll();
            _texture2DResourceMap.Clear();

            // 释放所有Mesh
            _meshPool.ReleaseAll();
            _meshResourceMap.Clear();

            // 清空统计信息
            _resourceTypeUsageCount.Clear();
        }

        /// <summary>
        /// 检查并清理未使用的资源
        /// </summary>
        private void CheckAndCleanupResources()
        {
            try
            {
                // 检查是否需要清理资源
                if (DateTime.Now - _lastCleanupTime > _cleanupInterval)
                {
                    // 清理未使用的资源
                    _materialPool.CleanupUnused(30); // 30分钟未使用的资源才会被清理
                    _computeBufferPool.CleanupUnused(30);
                    _renderTexturePool.CleanupUnused(30);
                    _texture2DPool.CleanupUnused(30);
                    _meshPool.CleanupUnused(30);

                    // 更新清理时间
                    _lastCleanupTime = DateTime.Now;

                    // 不再输出资源统计信息
                }
            }
            catch (System.Exception)
            {
                // 清理资源时出错
            }
        }

        /// <summary>
        /// 增加资源类型使用计数
        /// </summary>
        /// <param name="resourceType">资源类型</param>
        private void IncrementResourceTypeUsage(string resourceType)
        {
            if (!_resourceTypeUsageCount.ContainsKey(resourceType))
            {
                _resourceTypeUsageCount[resourceType] = 0;
            }

            _resourceTypeUsageCount[resourceType]++;
        }

        /// <summary>
        /// 获取资源统计信息
        /// </summary>
        /// <returns>资源统计信息</returns>
        public Dictionary<string, ResourceStatistics> GetResourceStatistics()
        {
            Dictionary<string, ResourceStatistics> statistics = new Dictionary<string, ResourceStatistics>();

            statistics["Material"] = _materialPool.GetStatistics();
            statistics["ComputeBuffer"] = _computeBufferPool.GetStatistics();
            statistics["RenderTexture"] = _renderTexturePool.GetStatistics();
            statistics["Texture2D"] = _texture2DPool.GetStatistics();
            statistics["Mesh"] = _meshPool.GetStatistics();

            return statistics;
        }

        /// <summary>
        /// 获取资源使用统计信息
        /// </summary>
        /// <returns>资源使用统计信息</returns>
        public Dictionary<string, int> GetResourceUsageStatistics()
        {
            return new Dictionary<string, int>(_resourceTypeUsageCount);
        }

        /// <summary>
        /// 打印资源统计信息
        /// </summary>
        public void PrintResourceStatistics()
        {
            // 不再输出资源统计信息
        }
    }
}
