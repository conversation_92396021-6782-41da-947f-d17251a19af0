using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKPlugins;
using TKPlugins_Extend;
using UnityEditor;
using UnityEditor.Animations;
using UnityEngine;

public static class TeamLeaderCfgGenTool
{
    private const string DEFAULT_CFG_PATH = "Assets/Art_TFT_Raw/cfg/team_leader_cfg/default_cfg.asset";
    public const string CFG_DIR = "Assets/Art_TFT_Raw/cfg/team_leader_cfg";

    public static DataGroupAsset GenNewCfg(GameObject model, string cfgName, string cfgTemplatePath, out bool success)
    {
        string dstPath = CFG_DIR + "/" + cfgName + ".asset";
        if (File.Exists(dstPath))
        {
            if (!Application.isBatchMode)
                EditorUtility.DisplayDialog("提示", dstPath + " 已经存在", "确定");
            DataGroupAsset cfg = AssetDatabase.LoadAssetAtPath<DataGroupAsset>(dstPath);
            success = true;
            return cfg;
        }

        if (!string.IsNullOrEmpty(cfgTemplatePath))
            AssetDatabase.CopyAsset(cfgTemplatePath, dstPath);
        else
            AssetDatabase.CopyAsset(DEFAULT_CFG_PATH, dstPath);

        {
            DataGroupAsset cfg = AssetDatabase.LoadAssetAtPath<DataGroupAsset>(dstPath);
            success = SetConfig(model, cfg);
            return cfg;
        }
    }
    private static bool SetConfig(GameObject model, DataGroupAsset cfg)
    {
        if (cfg == null)
        {
            Debug.LogError("[TeamLeaderCfgGenTool.SetConfig]配置不存在");
            return false;
        }
        if  (model == null)
        {
            Debug.LogError("[TeamLeaderCfgGenTool.SetConfig]模型不存在");
            return false;
        }

        AnimatorStateMachine stateMachine = TeamLeaderAnimatorUtil.GetAnimatorMachine(model);
        if (stateMachine == null)
        {
            Debug.LogError("[TeamLeaderCfgGenTool.SetConfig]找不到该模型的动作状态机 " + model.name);
            return false;
        }

        string groupName = "";

        #region run

        groupName = "Run";
        float walkSpeed = cfg.GetDataItem(groupName, "run_Speed").floatVal;
        TeamLeaderCfgChange.UpdateAnimationCurve(stateMachine, walkSpeed, cfg, groupName, "run_start", model.name);
        TeamLeaderCfgChange.UpdateAnimationCurve(stateMachine, walkSpeed, cfg, groupName, "run_stop", model.name);
        TeamLeaderCfgChange.UpdateAnimationCurve(stateMachine, walkSpeed, cfg, groupName, "run_stop_02", model.name);

        #endregion

        #region run_fast

        groupName = "RunFast";
        float runSpeed = cfg.GetDataItem(groupName, "run_fast_Speed").floatVal;
        TeamLeaderCfgChange.UpdateAnimationCurve(stateMachine, runSpeed, cfg, groupName, "run_fast_start", model.name);
        TeamLeaderCfgChange.UpdateAnimationCurve(stateMachine, runSpeed, cfg, groupName, "run_fast_stop", model.name);
        TeamLeaderCfgChange.UpdateAnimationCurve(stateMachine, runSpeed, cfg, groupName, "run_fast_stop_02", model.name);

        #endregion

        #region rush

        groupName = "Rush";
        TeamLeaderCfgChange.UpdateAnimationCurve(stateMachine, runSpeed, cfg, groupName, "rush", model.name);

        #endregion

        #region Hurt

        groupName = "Hurt";
        TeamLeaderCfgChange.UpdateAnimationCurve(stateMachine, 0.0f, cfg, groupName, "hurt_01", model.name, false);
        TeamLeaderCfgChange.UpdateAnimationCurve(stateMachine, 0.0f, cfg, groupName, "hurt_02", model.name, false);

        #endregion

        #region AnimationList

        List<string> newAnim = GetNewAnimationList(stateMachine);
        TeamLeaderCfgChange.ResetAnimtionList(cfg, newAnim);

        #endregion

        #region AnimCrossFadeTime

        TeamLeaderCfgChange.RebuildAnimCrossFadeTime(cfg);

        #endregion

        #region AnimSpringSwitch

        TeamLeaderCfgChange.RebuildSpringSwitch(cfg);

        #endregion

        DataGroupAssetEditor.Save(cfg);

        return true;
    }

    private static List<string> GetNewAnimationList(AnimatorStateMachine stateMachine)
    {
        List<string> animNames = new List<string>();
        foreach (ChildAnimatorState childAnimatorState in stateMachine.states)
        {
            animNames.Add(childAnimatorState.state.name);
        }

        foreach (ChildAnimatorStateMachine childAnimatorStateMachine in stateMachine.stateMachines)
        {
            foreach (ChildAnimatorState childAnimatorState in childAnimatorStateMachine.stateMachine.states)
            {
                animNames.Add(childAnimatorState.state.name);
            }
        }

        animNames.Sort();

        return animNames;
    }
}

