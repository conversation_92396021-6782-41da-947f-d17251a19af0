using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace TKFrame.MeshSimplification
{
    public class MSVertex
    {
        public int index;
        public Vector3 position;
        public Vector2 uv;
        public Vector3 normal;

        public MSHalfEdge hedge { get; private set; }  // 这个顶点的任意一个半边

        public void SetHEdge(MSHalfEdge hedge)
        {
            if (this.hedge != null)
            {
                TKFrame.Diagnostic.Error(this + ", hedge has value");
            }
            this.hedge = hedge;
        }

        public override string ToString()
        {
            return string.Format("idx: {0} pos: {1} uv: {2} n: {3} hedge: {4}", index, position, uv, normal, hedge);
        }
    }
}
