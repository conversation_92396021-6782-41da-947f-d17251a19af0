using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;
using UnityEngine.Playables;
using ZGame;
using ZGameChess;

[CustomEditor(typeof(TinyPreviewSceneEditorController))]
public class TinyPreviewSceneEditorControllerInspector : Editor
{
    string searchValue = "";
    string[] tinyList = new string[0];
    protected int m_selectTiny = 0;
    protected TinySceneUIType m_uiType = TinySceneUIType.Warehouse;

    static string m_curTestInfo = string.Empty;
    static bool m_autoTesting = false;

    protected bool m_ingoreDebug = true;
    protected bool m_ingoreSpecial = true;          // 忽略掉只有部分小小英雄出现的场景，只测试藏品、商城、排行榜等所有小小英雄都有的场景
    protected bool m_sameSceneOnlyOne = true;       // 相同场景只测一次

    protected List<TinySceneUIType> m_commonUIType = new List<TinySceneUIType>()
    {
        TinySceneUIType.Rank,
        TinySceneUIType.Warehouse,
        TinySceneUIType.UserInfo,
        TinySceneUIType.BattleEnd_First,
        TinySceneUIType.BattleEnd_BattlePass,
        TinySceneUIType.GetNewTiny,
    };

    private void OnEnable()
    {
        searchValue = EditorPrefs.GetString("TinyPreviewSceneEditorControllerInspector_searchValue");
        m_uiType = (TinySceneUIType)EditorPrefs.GetInt("TinyPreviewSceneEditorControllerInspector_m_uiType");
        m_selectTiny = EditorPrefs.GetInt("TinyPreviewSceneEditorControllerInspector_m_selectTiny");

        RefershTinyList();
    }

    private void RefershTinyList()
    {
        if (EditorApplication.isPlaying)
        {
            List<string> list = new List<string>();
            var tinyCfgs = DataBaseManager.Instance.GetTinyHeros();
            foreach (var item in tinyCfgs)
            {
                var itemCfg = DataBaseManager.Instance.SearchACGItem(item.Value.iID);
                var name = itemCfg != null ? itemCfg.sItemName : "";
                if (!string.IsNullOrEmpty(searchValue))
                {
                    if (name.Contains(searchValue))
                    {
                        list.Add(itemCfg.iID + " " + name);
                        //tinyList.Add(itemCfg.iID + " " + name);
                    }
                    else if (int.TryParse(searchValue, out int searchId) && searchId == itemCfg.iID)
                    {
                        list.Add(itemCfg.iID + " " + name);
                    }
                }
                else
                {
                    list.Add(itemCfg.iID + " " + name);
                    //tinyList.Add(itemCfg.iID + " " + name);
                }
            }

            if (m_selectTiny >= list.Count)
            {
                m_selectTiny = 0;
                EditorPrefs.SetInt("TinyPreviewSceneEditorControllerInspector_m_selectTiny", m_selectTiny);
            }
            tinyList = list.ToArray();
        }
    }

    public override void OnInspectorGUI()
    {
        TinyPreviewSceneEditorController tc = target as TinyPreviewSceneEditorController;

        if (!EditorApplication.isPlaying)
            tc.m_uiRoot = (Transform)EditorGUILayout.ObjectField("UI Root", tc.m_uiRoot, typeof(Transform), true);

        if (EditorApplication.isPlaying && tc.Inited)
        {
            EditorGUI.BeginChangeCheck();
            EditorGUILayout.BeginVertical("box");
            {
                EditorGUILayout.BeginHorizontal();
                searchValue = EditorGUILayout.TextField(searchValue);
                if (GUILayout.Button("搜索"))
                {
                    RefershTinyList();
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();

                m_selectTiny = EditorGUILayout.Popup(m_selectTiny, tinyList);

                if (GUILayout.Button("加载"))
                {
                    ReloadTinyScene();
                }
                EditorGUILayout.EndHorizontal();
            }
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space();
            EditorGUILayout.BeginVertical("box");
            {
                EditorGUILayout.BeginHorizontal();
                m_uiType = (TinySceneUIType)EditorGUILayout.EnumPopup("展示UI", m_uiType);

                if (GUILayout.Button("切换"))
                {
                    ReloadTinyScene();
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                TkEditorScreenController.Draw(tc.gameObject, ReloadTinyScene);
                EditorGUILayout.EndHorizontal();
            }
            EditorGUILayout.EndVertical();

            if (EditorGUI.EndChangeCheck())
            {
                EditorPrefs.SetString("TinyPreviewSceneEditorControllerInspector_searchValue", searchValue);
                EditorPrefs.SetInt("TinyPreviewSceneEditorControllerInspector_m_uiType", (int)m_uiType);
                EditorPrefs.SetInt("TinyPreviewSceneEditorControllerInspector_m_selectTiny", m_selectTiny);
            }

            EditorGUILayout.Space();
            EditorGUILayout.BeginVertical("box");
            {
                if (!m_autoTesting)
                {
                    m_ingoreDebug = EditorGUILayout.Toggle("忽视没上架的", m_ingoreDebug);
                    m_ingoreSpecial = EditorGUILayout.Toggle("只测常见场景", m_ingoreSpecial);
                    m_sameSceneOnlyOne = EditorGUILayout.Toggle("相同场景只测一只小小英雄", m_sameSceneOnlyOne);

                    if (GUILayout.Button("自动测试"))
                    {
                        m_autoTesting = true;
                        tc.StartCoroutine(AutoTest());
                    }
                }
                else
                {
                    if (GUILayout.Button("停止测试"))
                    {
                        tc.StopCoroutine(AutoTest());
                        m_autoTesting = false;
                        m_curTestInfo = string.Empty;
                    }

                    EditorGUILayout.LabelField(m_curTestInfo, GUILayout.Height(100));
                }
            }
            EditorGUILayout.EndVertical();
        }
    }

    private void ReloadTinyScene()
    {
        TinyPreviewSceneEditorController tc = target as TinyPreviewSceneEditorController;
        var tinyId = int.Parse(tinyList[m_selectTiny].Split(" ")[0]);
        tc.ShowPreviewScene(tinyId, m_uiType);

        m_selectTiny = EditorPrefs.GetInt("TinyPreviewSceneEditorControllerInspector_m_selectTiny");
    }

    private IEnumerator AutoTest()
    {
        m_autoTesting = true;

        List<string> scanScenes = new List<string>();

        TinyPreviewSceneEditorController tc = target as TinyPreviewSceneEditorController;
        for (int i = 0; i < tinyList.Length; ++i)
        {
            m_selectTiny = i;
            EditorPrefs.SetInt("TinyPreviewSceneEditorControllerInspector_m_selectTiny", m_selectTiny);

            var tinyId = int.Parse(tinyList[m_selectTiny].Split(" ")[0]);
            var cfg = DataBaseManager.Instance.SearchACGItem(tinyId);
            if (cfg == null || cfg.iDebugMode == 1)
                continue;

            TinyModelPreviewSceneService.GetTinyResName(tinyId, out string mapAb, out string mapAsset, out string tinyAb, out string tinyAsset);

            if (m_sameSceneOnlyOne && scanScenes.Contains(mapAsset))
                continue;
            scanScenes.Add(mapAsset);

            for (int j = (int)TinySceneUIType.None + 1; j < (int)TinySceneUIType.Max; ++j)
            {
                m_uiType = (TinySceneUIType)j;
                if (m_ingoreSpecial && !m_commonUIType.Contains(m_uiType))
                    continue;
                var screens = TkEditorScreenController.Screens;
                for (int k = 0; k < screens.Count; ++k)
                {
                    var s = screens[k];
                    m_curTestInfo = string.Format("小小英雄: {0}\n \n 界面: {1}\n 屏幕类型: {2}\n 测试进度: {3}/{4}", tinyList[m_selectTiny], m_uiType, s.name, m_selectTiny, tinyList.Length);

                    bool changedScreen = false;
                    s.action(tc.gameObject, () =>
                    {
                        ReloadTinyScene();
                        changedScreen = true;
                    });

                    while (!changedScreen)
                        yield return null;

                    //float maxWait = 1f;

                    while (TinyPreviewSceneManager.Instance.GetCurPreview().TinyGo == null 
                        || TinyPreviewSceneManager.Instance.GetCurPreview().Animator == null 
                        || TinyPreviewSceneManager.Instance.GetCurPreview().Animator.GetCurrentAnimatorStateInfo(0).IsName("enter_UI"))
                    {
                        //maxWait -= Time.deltaTime;
                        yield return null;
                    }

                    var timelines = TinyPreviewSceneManager.Instance.GetCurPreview().GetComponentsInChildren<PlayableDirector>();
                    if (timelines.Length > 0)
                    {
                        bool hasPlayTimeline = false;
                        do
                        {
                            yield return null;
                            hasPlayTimeline = false;
                            foreach (var item in timelines)
                            {
                                if (item.state == PlayState.Playing && item.time < item.duration)
                                    hasPlayTimeline = true;
                            }
                        } while (hasPlayTimeline);
                    }
 
                    yield return new WaitForSeconds(0.5f);

                    ShotScreen(string.Format("{0}_{1}_{2}", tinyList[m_selectTiny], m_uiType, s.name));

                    yield return new WaitForSeconds(0.1f);

                    if (!m_autoTesting)
                        yield break;
                }
            }
        }
        m_autoTesting = false;
    }

    private void ShotScreen(string fileName)
    {
        var dir = Application.dataPath + "/../_小小英雄预览/";
        if (!System.IO.Directory.Exists(dir))
        {
            System.IO.Directory.CreateDirectory(dir);
        }
        ScreenCapture.CaptureScreenshot(dir + fileName + ".png");
    }
}
