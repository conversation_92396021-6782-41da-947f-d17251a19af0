using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;
using UnityEngine.Playables;

namespace ZGameChess
{
    [CustomEditor(typeof(TinySceneTimeline))]
    public class TinySceneTimelineInspector : Editor
    {
        private UnityEditorInternal.ReorderableList m_paramList;

        private SerializedProperty m_script;

        private void OnEnable()
        {
            m_script = serializedObject.FindProperty("m_Script");
            var timeline = target as TinySceneTimeline;

            m_paramList = new UnityEditorInternal.ReorderableList(timeline.m_timelines, typeof(string), false, true, true, true);
            m_paramList.elementHeight = 84f;
            m_paramList.drawElementCallback = delegate (Rect rect, int index, bool selected, bool focused)
            {
                TinySceneTimeline.TimelineInfo state = timeline.m_timelines[index];

                float oneHeight = (rect.height - 4f) / 3.0f;
                if (state.triggerType == TinySceneTimeline.TriggerType.Action)
                    oneHeight = (rect.height - 4f) / 4.0f;

                Rect animationRect = rect;
                animationRect.height = oneHeight;
                state.triggerType = (TinySceneTimeline.TriggerType)EditorGUI.EnumPopup(animationRect, "触发条件", state.triggerType);

                Rect weightRect = rect;
                weightRect.yMin = animationRect.yMax + 2f;
                weightRect.height = oneHeight;
                if (state.triggerType == TinySceneTimeline.TriggerType.Action)
                {
                    state.animatorControllerName = EditorGUI.TextField(weightRect, "小小英雄名称", state.animatorControllerName);
                    weightRect.yMin = weightRect.yMax + 2;
                    weightRect.height = oneHeight;
                    state.actionName = EditorGUI.TextField(weightRect, "动作名称", state.actionName);
                }
                else if (state.triggerType == TinySceneTimeline.TriggerType.Click)
                {
                    state.actionName = EditorGUI.TextField(weightRect, "触发节点", state.actionName);
                }

                Rect precentRect = rect;
                precentRect.yMin = weightRect.yMax + 2f;
                precentRect.height = oneHeight;
                state.timeline = (PlayableDirector)EditorGUI.ObjectField(precentRect, "Timeline轨道", state.timeline, typeof(PlayableDirector), true);
            };

            m_paramList.onAddCallback = delegate (UnityEditorInternal.ReorderableList list)
            {
                AddItem();
            };

            m_paramList.onRemoveCallback = delegate (UnityEditorInternal.ReorderableList list)
            {
                DeleteItem(list.index);
            };

            m_paramList.drawHeaderCallback = (Rect rect) =>
            {
                GUI.Label(rect, "配置列表");
            };
        }

        private void DeleteItem(int index)
        {
            var timeline = target as TinySceneTimeline;
            timeline.m_timelines.RemoveAt(index);
        }

        private void AddItem()
        {
            var timeline = target as TinySceneTimeline;
            timeline.m_timelines.Add(new TinySceneTimeline.TimelineInfo());
        }

        public override void OnInspectorGUI()
        {
            //base.OnInspectorGUI();

            GUI.enabled = false;
            EditorGUILayout.PropertyField(m_script);
            GUI.enabled = true;

            m_paramList.DoLayoutList();
        }
    }
}
