using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;

[CustomEditor(typeof(ChessAttackLineBind))]
public class ChessAttackLineBindInspector : Editor
{
    private UnityEditorInternal.ReorderableList m_paramList;

    private void OnEnable()
    {
        var bind = target as ChessAttackLineBind;

        m_paramList = new UnityEditorInternal.ReorderableList(bind.m_nodeList, typeof(string), false, true, true, true);
        m_paramList.elementHeight = 64f;
        m_paramList.drawElementCallback = delegate (Rect rect, int index, bool selected, bool focused)
        {
            var node = bind.m_nodeList[index];

            float oneHeight = (rect.height - 4f) / 3.0f;

            Rect typeRect = rect;
            typeRect.height = oneHeight;
            node.m_spawnPos = (ChessAttackItemConfig.EffectSpawnPos)EditorGUI.EnumPopup(typeRect, "绑定类型", node.m_spawnPos);

            Rect weightRect = rect;
            weightRect.yMin = typeRect.yMax + 2f;
            weightRect.height = oneHeight;
            if (node.m_spawnPos == ChessAttackItemConfig.EffectSpawnPos.DistancePoint)
            {
                node.m_percent = EditorGUI.Slider(weightRect, "百分比", node.m_percent, 0, 1);
            }
            else if (node.m_spawnPos == ChessAttackItemConfig.EffectSpawnPos.Bullet)
            {
                node.m_percent = EditorGUI.IntField(weightRect, "子弹序号:(从0开始)", (int)node.m_percent);
            }
            else
            {
                node.m_spwanLoc = (CharacterHangPoint.SupportHangPointType)EditorGUI.EnumPopup(weightRect, "绑定挂点", node.m_spwanLoc);
            }

            Rect precentRect = rect;
            precentRect.yMin = weightRect.yMax + 2f;
            precentRect.height = oneHeight;
            node.offset = EditorGUI.Vector3Field(precentRect, "偏移", node.offset);
        };

        m_paramList.onAddCallback = delegate (UnityEditorInternal.ReorderableList list)
        {
            bind.m_nodeList.Add(new ChessAttackLineBind.Node());
        };

        m_paramList.onRemoveCallback = delegate (UnityEditorInternal.ReorderableList list)
        {
            bind.m_nodeList.RemoveAt(list.index);
        };

        m_paramList.drawHeaderCallback = (Rect rect) =>
        {
            GUI.Label(rect, "链接点列表");
        };
    }

    public override void OnInspectorGUI()
    {
        //base.OnInspectorGUI();

        m_paramList.DoLayoutList();
    }
}

