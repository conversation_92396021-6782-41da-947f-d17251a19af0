 /********************************************************************************
 *	创建人：	   刘帅
 *	创建时间：   2019-02-18
 *
 *	功能说明：   自发光材质球UI界面的脚本
 *	
 *	修改记录：   2019-02-18  添加UI界面脚本
*********************************************************************************/

using System;
using UnityEngine;

namespace UnityEditor
{
    public class ACGameIlluminShaderGUI : ShaderGUI
    {

        //property name.
        private static class Styles
        {
            public static GUIContent illuminColor = EditorGUIUtility.TrTextContent("自发光颜色");
            public static GUIContent illuminTexture = EditorGUIUtility.TrTextContent("纹理(RGB),遮罩(A)");
            public static GUIContent emission = EditorGUIUtility.TrTextContent("自发光强度");
            public static GUIContent globalIllumin = EditorGUIUtility.TrTextContent("全局光照");
        }

        //properties.
        private MaterialProperty m_illuminCol = null;
        private MaterialProperty m_illuminTex = null;
        private MaterialProperty m_emission = null;
        //private MaterialProperty m_globalIllumin = null;
        private int m_flag = 0;

        private MaterialEditor m_MaterialEditor = null;
        private GUIContent[] m_popupLabels = new GUIContent[]
            {
                new GUIContent("None"),
                new GUIContent("Realtime"),
                new GUIContent("Baked")
            };
        private int[] m_popupIndex = new int[] { 0, 1, 2 };

        //functions.
        public void FindProperties(MaterialProperty[] props)
        {
            m_illuminCol = ShaderGUI.FindProperty("_Color", props);
            m_illuminTex = ShaderGUI.FindProperty("_MainTex", props);
            m_emission = ShaderGUI.FindProperty("_Emission", props);  
        }

        public override void OnGUI(MaterialEditor materialEditor, MaterialProperty[] properties)
        {
            materialEditor.SetDefaultGUIWidths();

            FindProperties(properties);
            m_MaterialEditor = materialEditor;
            Material material = materialEditor.target as Material;

            m_flag = (int)material.globalIlluminationFlags;

            m_MaterialEditor.ShaderProperty(m_illuminCol, Styles.illuminColor);
            m_MaterialEditor.ShaderProperty(m_illuminTex, Styles.illuminTexture);
            m_MaterialEditor.ShaderProperty(m_emission, Styles.emission);
            m_flag = EditorGUILayout.IntPopup(Styles.globalIllumin, m_flag, m_popupLabels, m_popupIndex);
            material.globalIlluminationFlags = (MaterialGlobalIlluminationFlags)m_flag;
        }
    }
}
