using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;

public class CharacterSettings : EditorWindow
{
    private static EditorWindow window = null;

    [MenuItem("Window/Character Settings")]
    private static void Init()
    {
        window = GetWindow<CharacterSettings>();
        window.Show();
    }

    #region Editor Parameters
    private string suffixPath = "/Art_TFT_Raw/model_res/hero/";
    //private int[] sets = new int[] { 1, 2, 3, 4, 5 };
    private string[] setsDisplay = new string[] { "Set 1", "Set 2", "Set 3", "Set 4", "Set 5" };
    //private string[] Sets = new string[] { "1", "2", "3", "4", "5" };
    private int currentSet = 0;
    //private int[] levels = new int[] { 1, 2, 3 };
    private string[] levelsDisplay = new string[] { "Level 1", "Level 2", "Level 3" };
    //private int[] levels = new int[] { 1,2,3};
    private int currentLevel = 0;
    private Material templateMat = null;
    #endregion

    #region Shader Parameters
    private int paramCount = 21;
    private int setCount = 5;
    private int levelCount = 3;
    //private float[] _Void;
    private bool[] _toggles = new bool[] { true, true, true, true, true, true, true, true, true, true,
                                           true, true, true, true, true, true, true, true, true, true,
                                           true};
    private bool[] _Void = new bool[] { false, false, false };
    private Color[] _VoidColor = new Color[] { Color.black, Color.black, Color.black };
    private float[] _VoidScreenSpeedX = new float[] { 0, 0, 0 };
    private float[] _VoidScreenSpeedY = new float[] { 0, 0, 0 };
    private Color[] _VoidFlowingColor = new Color[] { Color.black, Color.black, Color.black };
    private Color[] _MainCol = new Color[] { Color.white, Color.white, Color.white };
    private float[] _ScreenSpeedX = new float[] { 0, 0, 0 };
    private float[] _ScreenSpeedY = new float[] { 0, 0, 0 };
    private Color[] _FlowingColor = new Color[] { Color.white, Color.white, Color.white };
    private float[] _SpecularExponent = new float[] { 1, 1, 1 };
    private Color[] _SpecularColor = new Color[] { Color.white, Color.white, Color.white };
    private float[] _SpecularScale = new float[] { 1, 1, 1 };
    //private float[] _CorrectMatcap;
    private bool[] _CorrectMatcap = new bool[] { false, false, false };
    //private Vector4[] _SideLightDirection = new Vector4[] { Vector4.zero, Vector4.zero, Vector4.zero };
    private Color[] _SideLightColor = new Color[] { Color.white, Color.white, Color.white };
    private float[] _SpeedFresnel = new float[] { 1, 1, 1 };
    //private float[] _FXFresnel;
    private bool[] _FXFresnel = new bool[] { false, false, false };
    private float[] _FresnelExponent = new float[] { 1, 1, 1 };
    private Color[] _FresnelColor = new Color[] { Color.white, Color.white, Color.white };
    private float[] _FresnelScale = new float[] { 1, 1, 1 };
    private float[] _SinAmplitude = new float[] { 1, 1, 1 };
    private Vector4[] _ShadowPlane = new Vector4[] { new Vector4(0, 1, 0, 0), new Vector4(0, 1, 0, 0), new Vector4(0, 1, 0, 0) };
    #endregion

    private void OnGUI()
    {
        GUILayout.BeginVertical();
        currentSet = EditorGUILayout.Popup("赛季", currentSet, setsDisplay);
        currentLevel = EditorGUILayout.Popup("等级", currentLevel, levelsDisplay);
        LevelPanel(currentLevel);
        if (GUILayout.Button("Process"))
        {
            HeroProcess(currentSet + 1,currentLevel + 1);
        }
        GUILayout.BeginVertical();
    }

    private void LevelPanel(int panelIndex)
    {
        templateMat = EditorGUILayout.ObjectField(templateMat, typeof(Material),true) as Material;
        if (templateMat != null)
        {
            if (GUILayout.Button("模板赋值"))
            {
                TemplateAssignment();
            }
        }
        _toggles[0] = EditorGUILayout.Toggle("", _toggles[0]);
        if (_Void[panelIndex] = EditorGUILayout.Toggle("开启虚空羁绊", _Void[panelIndex])) 
        {
            _toggles[1] = EditorGUILayout.Toggle("", _toggles[1]);
            _VoidColor[panelIndex] = EditorGUILayout.ColorField(new GUIContent("Void Color"), _VoidColor[panelIndex], true, true, true);
            _toggles[2] = EditorGUILayout.Toggle("", _toggles[2]);
            _VoidScreenSpeedX[panelIndex] = EditorGUILayout.Slider("Void Screen SpeedX", _VoidScreenSpeedX[panelIndex], -5.0f, 5.0f);
            _toggles[3] = EditorGUILayout.Toggle("", _toggles[3]);
            _VoidScreenSpeedY[panelIndex] = EditorGUILayout.Slider("Void Screen SpeedY", _VoidScreenSpeedY[panelIndex], -5.0f, 5.0f);
            _toggles[4] = EditorGUILayout.Toggle("", _toggles[4]);
            _VoidFlowingColor[panelIndex] = EditorGUILayout.ColorField(new GUIContent("Void Flowing Color"), _VoidFlowingColor[panelIndex], true, true, true);
        }
        _toggles[5] = EditorGUILayout.Toggle("", _toggles[5]);
        _MainCol[panelIndex] = EditorGUILayout.ColorField(new GUIContent("Main Color"), _MainCol[panelIndex], true, true, true);
        _toggles[6] = EditorGUILayout.Toggle("", _toggles[6]);
        _ScreenSpeedX[panelIndex] = EditorGUILayout.Slider("Screen SpeedX", _ScreenSpeedX[panelIndex], -5.0f, 5.0f);
        _toggles[7] = EditorGUILayout.Toggle("", _toggles[7]);
        _ScreenSpeedY[panelIndex] = EditorGUILayout.Slider("Screen SpeedY", _ScreenSpeedY[panelIndex], -5.0f, 5.0f);
        _toggles[8] = EditorGUILayout.Toggle("", _toggles[8]);
        _FlowingColor[panelIndex] = EditorGUILayout.ColorField(new GUIContent("Flowing Color"), _FlowingColor[panelIndex], true, true, true);
        _toggles[9] = EditorGUILayout.Toggle("", _toggles[9]);
        _SpecularExponent[panelIndex] = EditorGUILayout.Slider("Specular Exponent", _SpecularExponent[panelIndex], 0.001f, 16.0f);
        _toggles[10] = EditorGUILayout.Toggle("", _toggles[10]);
        _SpecularColor[panelIndex] = EditorGUILayout.ColorField(new GUIContent("Specular Color"), _SpecularColor[panelIndex], true, true, true);
        _toggles[11] = EditorGUILayout.Toggle("", _toggles[11]);
        _SpecularScale[panelIndex] = EditorGUILayout.Slider("Specular Scale", _SpecularScale[panelIndex], 0.0f, 10.0f);
        _toggles[12] = EditorGUILayout.Toggle("", _toggles[12]);
        if (_CorrectMatcap[panelIndex] = EditorGUILayout.Toggle("使用Matcap", _CorrectMatcap[panelIndex])) 
        {
            GUILayout.TextField("使用Specular Texture Green Channel控制侧光方向");
        }
        else
        {
            GUILayout.TextField("关闭侧光");
            //_SideLightDirection[panelIndex] = EditorGUILayout.Vector4Field("SideLight Direction", _SideLightDirection[panelIndex]);
        }
        _toggles[13] = EditorGUILayout.Toggle("", _toggles[13]);
        _SideLightColor[panelIndex] = EditorGUILayout.ColorField(new GUIContent("SideLight Color"), _SideLightColor[panelIndex], true, true, true);

        _toggles[14] = EditorGUILayout.Toggle("", _toggles[14]);
        _SpeedFresnel[panelIndex] = EditorGUILayout.FloatField("Speed Fresnel", _SpeedFresnel[panelIndex]);
        _toggles[15] = EditorGUILayout.Toggle("", _toggles[15]);
        _FXFresnel[panelIndex] = EditorGUILayout.Toggle(_FXFresnel[panelIndex],"FX Fresnel");
        _toggles[16] = EditorGUILayout.Toggle("", _toggles[16]);
        _FresnelExponent[panelIndex] = EditorGUILayout.Slider("Fresnel Exponent", _FresnelExponent[panelIndex], 0.01f, 8.0f);
        _toggles[17] = EditorGUILayout.Toggle("", _toggles[17]);
        _FresnelColor[panelIndex] = EditorGUILayout.ColorField(new GUIContent("Fresnel Color"), _FresnelColor[panelIndex], true, true, true);
        _toggles[18] = EditorGUILayout.Toggle("", _toggles[18]);
        _FresnelScale[panelIndex] = EditorGUILayout.Slider("Fresnel Scale", _FresnelScale[panelIndex], 0.1f, 10.0f);
        _toggles[19] = EditorGUILayout.Toggle("", _toggles[19]);
        _SinAmplitude[panelIndex] = EditorGUILayout.Slider("Sin Amplitude", _SinAmplitude[panelIndex], 0.0f, 1.0f);
        _toggles[20] = EditorGUILayout.Toggle("", _toggles[20]);
        _ShadowPlane[panelIndex] = EditorGUILayout.Vector4Field("Shadow Plane", _ShadowPlane[panelIndex]);
    }

    private void HeroProcess(int set, int level)
    {
        string assetPath = Application.dataPath;//...\...\Assets\
        int assetPathIndex = assetPath.Length;
        string path = assetPath + suffixPath + "Set" + set.ToString() + "/";
        var files = Directory.GetFiles(path, "*", SearchOption.AllDirectories)
            .Where(item => Regex.IsMatch(item, "[\\/\\\\]Materials[\\/\\\\]"))
            .Where(item => Path.GetExtension(item).Equals(".mat"))
            .Where(item => Regex.IsMatch(item, "_" + level + ".mat"))
            .Select(item => item.Replace("\\", "/"))
            .Select(item => item.Substring(assetPathIndex-6))
            //.Select(item => AssetDatabase.AssetPathToGUID(item))
            .ToArray();
        List<Material> mats = new List<Material>();
        for (int i = 0; i < files.Length; ++i)
        {
            Material mat = AssetDatabase.LoadAssetAtPath(files[i], typeof(Material)) as Material;
            if (!mats.Contains(mat))
            {
                mats.Add(mat);
            }
        }
        float percentage = 0.0f;
        for (int i = 0; i < mats.Count; ++i)
        {
            MaterialAssignment(mats[i]);
            percentage = i+1 / mats.Count;
            if (percentage < 1.0f)
            {
                EditorUtility.DisplayCancelableProgressBar("角色材质批量设置工具", "Processing..." + mats[i].name, percentage);
            }
            else
            {
                EditorUtility.DisplayCancelableProgressBar("角色材质批量设置工具", "Processing..." + mats[i].name, 1.0f);
            }
        }
        if (percentage != 0.0f)
        {
            EditorUtility.ClearProgressBar();
        }
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }

    private Material MaterialAssignment(Material mat)
    {
        if (_toggles[0])
        {
            if (_Void[currentLevel])
            {
                mat.EnableKeyword("_VOID_ON");
            }
            else
            {
                mat.DisableKeyword("_VOID_ON");
            }
            //mat.SetFloat("_Void", Convert.ToSingle(_Void[currentLevel]));
        }
        if (_toggles[1])
            mat.SetColor("_VoidColor", _VoidColor[currentLevel]);
        if (_toggles[2])
            mat.SetFloat("_VoidScreenSpeedX", _VoidScreenSpeedX[currentLevel]);
        if (_toggles[3])
            mat.SetFloat("_VoidScreenSpeedY", _VoidScreenSpeedY[currentLevel]);
        if (_toggles[4])
            mat.SetColor("_VoidFlowingColor", _VoidFlowingColor[currentLevel]);
        if (_toggles[5])
            mat.SetColor("_MainCol", _MainCol[currentLevel]);
        if (_toggles[6])
            mat.SetFloat("_ScreenSpeedX", _ScreenSpeedX[currentLevel]);
        if (_toggles[7])
            mat.SetFloat("_ScreenSpeedY", _ScreenSpeedY[currentLevel]);
        if (_toggles[8])
            mat.SetColor("_FlowingColor", _FlowingColor[currentLevel]);
        if (_toggles[9])
            mat.SetFloat("_SpecularExponent", _SpecularExponent[currentLevel]);
        if (_toggles[10])
            mat.SetColor("_SpecularColor", _SpecularColor[currentLevel]);
        if (_toggles[11])
            mat.SetFloat("_SpecularScale", _SpecularScale[currentLevel]);
        if (_toggles[12])
        {
            if (_CorrectMatcap[currentLevel])
            {
                mat.EnableKeyword("_CORRECTMATCAP_ON");
            }
            else
            {
                mat.DisableKeyword("_CORRECTMATCAP_ON");
            }
            //mat.SetFloat("_CorrectMatcap", Convert.ToSingle(_CorrectMatcap[currentLevel]));
        }
        if (_toggles[13])
            mat.SetColor("_SideLightColor", _SideLightColor[currentLevel]);
        if (_toggles[14])
            mat.SetFloat("_SpeedFresnel", _SpeedFresnel[currentLevel]);
        if (_toggles[15])
            mat.SetFloat("_FXFresnel", Convert.ToSingle(_FXFresnel[currentLevel]));
        if (_toggles[16])
            mat.SetFloat("_FresnelExponent", _FresnelExponent[currentLevel]);
        if (_toggles[17])
            mat.SetColor("_FresnelColor", _FresnelColor[currentLevel]);
        if (_toggles[18])
            mat.SetFloat("_FresnelScale", _FresnelScale[currentLevel]);
        if (_toggles[19])
            mat.SetFloat("_SinAmplitude", _SinAmplitude[currentLevel]);
        if (_toggles[20])
            mat.SetVector("_ShadowPlane", _ShadowPlane[currentLevel]);
        return mat;
    }

    private void TemplateAssignment()
    {
        if (templateMat)
        {
            _Void[currentLevel] = Convert.ToBoolean(templateMat.GetFloat("_Void"));
            _VoidColor[currentLevel] = templateMat.GetColor("_VoidColor");
            _VoidScreenSpeedX[currentLevel] = templateMat.GetFloat("_VoidScreenSpeedX");
            _VoidScreenSpeedY[currentLevel] = templateMat.GetFloat("_VoidScreenSpeedY");
            _VoidFlowingColor[currentLevel] = templateMat.GetColor("_VoidFlowingColor");
            _MainCol[currentLevel] = templateMat.GetColor("_MainCol");
            _ScreenSpeedX[currentLevel] = templateMat.GetFloat("_ScreenSpeedX");
            _ScreenSpeedY[currentLevel] = templateMat.GetFloat("_ScreenSpeedY");
            _FlowingColor[currentLevel] = templateMat.GetColor("_FlowingColor");
            _SpecularExponent[currentLevel] = templateMat.GetFloat("_SpecularExponent");
            _SpecularColor[currentLevel] = templateMat.GetColor("_SpecularColor");
            _SpecularScale[currentLevel] = templateMat.GetFloat("_SpecularScale");
            _CorrectMatcap[currentLevel] = Convert.ToBoolean(templateMat.GetFloat("_CorrectMatcap"));
            //_SideLightDirection[currentLevel] = templateMat.GetVector("_SideLightDirection");
            _SideLightColor[currentLevel] = templateMat.GetColor("_SideLightColor");
            _SpeedFresnel[currentLevel] = templateMat.GetFloat("_SpeedFresnel");
            _FXFresnel[currentLevel] = Convert.ToBoolean(templateMat.GetFloat("_FXFresnel"));
            _FresnelExponent[currentLevel] = templateMat.GetFloat("_FresnelExponent");
            _FresnelColor[currentLevel] = templateMat.GetColor("_FresnelColor");
            _FresnelScale[currentLevel] = templateMat.GetFloat("_FresnelScale");
            _SinAmplitude[currentLevel] = templateMat.GetFloat("_SinAmplitude");
            _ShadowPlane[currentLevel] = templateMat.GetVector("_ShadowPlane");
        }
    }
}
