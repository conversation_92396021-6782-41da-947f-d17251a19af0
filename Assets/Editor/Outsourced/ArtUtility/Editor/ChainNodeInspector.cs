using UnityEditor;
using UnityEngine;

[CustomEditor(typeof(ChainNode))]
public class ChainNodeInspector : Editor
{
    private ChainNode _chainNode;
    private string errorCode = "检测结果";
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        _chainNode = (ChainNode)target;
        if (this._chainNode.type == ChainNode_Type_Enum.FatherAndSons)
        {
            if (this._chainNode.childType == ChainNode_Child_Type_Enum.Parent)
            {
                _chainNode.lookAtType = (ChainNode_LookAtType)EditorGUILayout.EnumPopup("视角方向跟随", _chainNode.lookAtType);
                _chainNode.lookOffsetEuler = EditorGUILayout.Vector3Field("旋转偏移", _chainNode.lookOffsetEuler);
            }
        }

        if (GUILayout.Button("检测修复连线特效"))
        {
            if (this._chainNode.type == ChainNode_Type_Enum.Sequence)
            {
                errorCode = "暂不支持Sequence类型";
            }
            else
            {
                errorCode = "";
                if (string.IsNullOrEmpty(_chainNode.KeyName))
                {
                    errorCode = "KeyName没填\n";
                }
                if (this._chainNode.lineRenders.Count < 1)
                {
                    errorCode = "lineRenders没有设置\n";
                }
            }
        }
        
        if (GUILayout.Button("一键修复"))
        {
            if (this._chainNode.type == ChainNode_Type_Enum.FatherAndSons)
            {
                errorCode = "修复完毕";
                this._chainNode.lineRenders.Clear();
                LineRenderer[] lineRendererArr = this._chainNode.GetComponentsInChildren<LineRenderer>(true);
                for (int i = 0, len = lineRendererArr.Length; i < len; i++)
                {
                    LineRenderer lr = lineRendererArr[i];
                    if (lr != null)
                    {
                        lr.transform.localPosition = Vector3.zero;
                        ClassicLineRender clr = lr.gameObject.TryGetComponent<ClassicLineRender>();
                        clr.lineRenderer = lr;
                        this._chainNode.lineRenders.Add(clr);
                    }
                }
            }
            else
            {
                errorCode = "暂不支持sequence类型";
            }
        }

        if (this._chainNode.type == ChainNode_Type_Enum.FatherAndSons)
        {
            if (this._chainNode.childType == ChainNode_Child_Type_Enum.Parent)
            {
                if (GUILayout.Button("连线测试"))
                {
                    ChainNodeFinder.Release();
                    ChainNodeFinder.FindEffectChainAndSetInfo(this._chainNode.gameObject, 1);
                    ChainNode[] chainNodeArr = GameObject.FindObjectsOfType<ChainNode>();
                    for (int i = 0, len = chainNodeArr.Length; i < len; i++)
                    {
                        if (chainNodeArr[i] != this._chainNode &&
                            chainNodeArr[i].type == this._chainNode.type &&
                            chainNodeArr[i].childType == ChainNode_Child_Type_Enum.Child &&
                            chainNodeArr[i].KeyName == this._chainNode.KeyName)
                        {
                            ChainNodeFinder.FindEffectChainAndSetInfo(chainNodeArr[i].gameObject, 1);
                        }
                    }
                }
            }
        }
        if (this._chainNode.type == ChainNode_Type_Enum.Sequence)
        {
            if (this._chainNode.childType == ChainNode_Child_Type_Enum.Parent)
            {
                if (GUILayout.Button("连线测试"))
                {
                    ChainNodeFinder.Release();
                    ChainNodeFinder.FindEffectChainAndSetInfo(this._chainNode.gameObject, 1);
                    ChainNode[] chainNodeArr = GameObject.FindObjectsOfType<ChainNode>();
                    for (int i = 0, len = chainNodeArr.Length; i < len; i++)
                    {
                        if (chainNodeArr[i] != this._chainNode &&
                            chainNodeArr[i].type == this._chainNode.type &&
                            this._chainNode.name.Contains(chainNodeArr[i].name) &&
                            chainNodeArr[i].KeyName == this._chainNode.KeyName)
                        {
                            ChainNodeFinder.FindEffectChainAndSetInfo(chainNodeArr[i].gameObject, 1);
                        }
                    }
                }
            }
        }

        EditorGUILayout.LabelField(errorCode);
    }
}