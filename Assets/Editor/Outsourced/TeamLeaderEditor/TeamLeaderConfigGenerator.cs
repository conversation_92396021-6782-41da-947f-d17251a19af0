using TKPlugins;
using TKPlugins_Extend;
using UnityEditor;
using UnityEditor.Animations;
using UnityEngine;
using UnityEngine.Windows;
using Wup.Jce;
using ZGameClient;

public class TeamLeaderConfigGenerator : EditorWindow
{
    [MenuItem("Assets/小小英雄/生成配置")]
    public static void Open()
    {
        EditorWindow.GetWindow<TeamLeaderConfigGenerator>("小小英雄配置生成");
    }
    
    private static string m_defaultConfigPath = "Assets/Art_TFT_Raw/cfg/team_leader_cfg/default_cfg.asset";
    private static string m_cfgDir = "Assets/Art_TFT_Raw/cfg/team_leader_cfg";
    private static string[] m_options = {"重新生成配置", "生成新配置"};
    
    private GameObject m_heroPrefab = null;
    private DataGroupAsset m_heroData = null;
    private string m_heroConfigName = string.Empty;

    private GUIContent m_heroContent = new GUIContent("英雄Prefab");
    private GUIContent m_configContent = new GUIContent("英雄配置");
    private GUIContent m_configNameContent = new GUIContent("英雄配置名称");

    private int m_index = 0;

    private TAllGameCfgClient m_allLocalData = null;
    

    private string[] allStates = {"run_start", "run_stop", "run_fast_start", "run_fast_stop" };

    private void OnEnable()
    {
        m_heroPrefab = Selection.activeGameObject;
        m_heroData = LoadExistConfig();
    }

    private void OnGUI()
    {
        EditorGUILayout.Space();
        
        EditorGUI.BeginChangeCheck();
        m_heroPrefab = EditorGUILayout.ObjectField(m_heroContent, m_heroPrefab, typeof(GameObject), false) as GameObject;
        if(EditorGUI.EndChangeCheck())
        {
            ChangeConfig();
        }
        
        EditorGUILayout.Space();
        
        EditorGUI.BeginChangeCheck();
        m_index = EditorGUILayout.Popup(m_index, m_options);
        if(EditorGUI.EndChangeCheck())
        {
            ChangeConfig();
        }       

        EditorGUILayout.Space();

        switch (m_index)
        {
            case 0:
                ShowResetConfig();
                break;    
            case 1:
                ShowNewConfig();
                break;
            default:
                break;
        }
    }
    
    private void ChangeConfig()
    {
        switch (m_index)
        {
            case 0:
                m_heroData = LoadExistConfig();
                break;
            case 1:
                m_heroConfigName = GenerateName();
                break;
            default:
                break;
        }
    }

    private void ShowNewConfig()
    {
        m_heroConfigName = EditorGUILayout.TextField(m_configNameContent, m_heroConfigName);
        
        EditorGUILayout.Space();
        
        if (GUILayout.Button("生成新配置"))
        {
            GenerateNewConfig();
        }
    }

    private void ShowResetConfig()
    {
        GUI.enabled = false;
        m_heroData =
            EditorGUILayout.ObjectField(m_configContent, m_heroData, typeof(DataGroupAsset), false) as
                DataGroupAsset;
        GUI.enabled = true;

        EditorGUILayout.Space();

        if (GUILayout.Button("重新生成配置"))
        {
            ResetConfig();
        }
    }

    private void GenerateNewConfig()
    {
        m_heroData = GetNewConfig();
        SetConfig();
    }
    
    private void ResetConfig()
    {
        m_heroData = LoadExistConfig();
        SetConfig();
    }
    
    private string GenerateName()
    {
        string tmp = m_heroPrefab.name;
        int first = tmp.IndexOf('_', 0);
        int second = tmp.IndexOf('_', first + 1);
        tmp = tmp.Substring(first, second - first + 1);
        tmp = "t" + tmp + "cfg";
        return tmp;
    }   
    
    private void SetConfig()
    {
        if(m_heroData == null || m_heroPrefab == null)
        {
            return;
        }
            
        AnimatorStateMachine stateMachine = TeamLeaderAnimatorUtil.GetAnimatorMachine(m_heroPrefab);
        if (stateMachine == null)
        {
            return;
        }

        AnimatorState walkStart = TeamLeaderAnimatorUtil.GetChildState(stateMachine, "run_start");
        AnimationClip walkStartClip = walkStart.motion as AnimationClip;
        float walkSpeed = m_heroData.GetDataItem("Run", "runSpeed").floatVal;
        m_heroData.GetDataItem("Run", "runStartTime").floatVal = walkStartClip.length;
        m_heroData.GetDataItem("Run", "runStartDis").floatVal = walkStartClip.length * walkSpeed;

        AnimatorState walkStop = TeamLeaderAnimatorUtil.GetChildState(stateMachine, "run_stop");
        AnimationClip walkStopClip = walkStop.motion as AnimationClip;
        m_heroData.GetDataItem("Run", "runStopTime").floatVal = walkStopClip.length;
        m_heroData.GetDataItem("Run", "runStopDis").floatVal = walkStopClip.length * walkSpeed;
        
        AnimatorState runStart = TeamLeaderAnimatorUtil.GetChildState(stateMachine, "run_fast_start");
        AnimationClip runStartClip = walkStart.motion as AnimationClip;
        float runSpeed = m_heroData.GetDataItem("RunFast", "runFastSpeed").floatVal;
        m_heroData.GetDataItem("RunFast", "runFastStartTime").floatVal = runStartClip.length;
        m_heroData.GetDataItem("RunFast", "runFastStartDis").floatVal = runStartClip.length * runSpeed;
            
        AnimatorState runStop = TeamLeaderAnimatorUtil.GetChildState(stateMachine, "run_fast_stop");
        AnimationClip runStopClip = runStop.motion as AnimationClip;
        m_heroData.GetDataItem("RunFast", "runFastStopTime").floatVal = runStopClip.length;
        m_heroData.GetDataItem("RunFast", "runFastStopDis").floatVal = runStopClip.length * runSpeed;
        
        AnimatorState rush = TeamLeaderAnimatorUtil.GetChildState(stateMachine, "rush");
        AnimationClip rushClip = rush.motion as AnimationClip;
        m_heroData.GetDataItem("Rush", "rushTime").floatVal = rushClip.length;

        EditorUtility.SetDirty(m_heroData);
        
        AssetDatabase.SaveAssets();
        
        DataGroupAssetEditor.ConvertToJson(m_heroData);
    }

    #region  加载配置

    private DataGroupAsset GetNewConfig()
    {
        string dstPath = m_cfgDir + "/" + m_heroConfigName + ".asset";
        if (File.Exists(dstPath))
        {
            EditorUtility.DisplayDialog("错误", dstPath + " 已经存在 ", "确定");
            return null;
        }
        
        AssetDatabase.CopyAsset(m_defaultConfigPath, dstPath);
        m_heroData = AssetDatabase.LoadAssetAtPath<DataGroupAsset>(dstPath);

        return m_heroData;
    }
    
    private DataGroupAsset LoadExistConfig()
    {
        LoadTableData();
        TACG_Item_Client result = null;
        foreach (TACG_Item_Client item in m_allLocalData.mapACG_Item_Client.Values)
        {
            if(item.sPreviewResource == m_heroPrefab.name)
            {
                result = item;
                break;
            }
        }

        if (result == null)
        {
            EditorUtility.DisplayDialog("错误", "表格没有配置 "+ m_heroPrefab.name + " 的参数", "确定");
            return null;
        }
            

        string dstPath = m_cfgDir + "/" + result.sTeamLeaderCfg + ".asset";
        m_heroData = AssetDatabase.LoadAssetAtPath<DataGroupAsset>(dstPath);

        if (m_heroData == null)
        {
            EditorUtility.DisplayDialog("错误",dstPath + " 配置不存在", "确定");
            return null;
        }

        return m_heroData;
    }

    private void LoadTableData()
    {
        if (m_allLocalData == null)
        {
            TextAsset textAsset = AssetDatabase.LoadAssetAtPath<TextAsset>("Assets/Resources/ClientDataTable/all_table_client.bytes");
        
            JceInputStream msgIS = new JceInputStream();
            msgIS.warp(textAsset.bytes, 0);
            m_allLocalData = new TAllGameCfgClient();
            m_allLocalData.ReadFrom(msgIS);
        }
    }

    #endregion
    
}
