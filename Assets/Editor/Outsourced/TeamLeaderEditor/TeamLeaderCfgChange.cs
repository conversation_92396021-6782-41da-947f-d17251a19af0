using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text;
using System.Text.RegularExpressions;
using GfxFramework;
using ProtoBuf;
using TKFrame;
using TKPlugins;
using TKPlugins_Extend;
using UnityEditor;
using UnityEditor.Animations;
using UnityEngine;
using ZGame;
using ZGameClient;
using Object = UnityEngine.Object;

public class TeamLeaderCfgChange
{
    [MenuItem("Assets/小小英雄/GenerateAnimSheetConfig")]
    public static void GenerateAnimSheetConfig()
    {
        Dictionary<string, string> allCfg = new Dictionary<string, string>();
        
        DataBaseManager.Instance.Initialize();
        
        var allTinyHero = DataBaseManager.Instance.GetTinyHeros();
        
        if (allTinyHero != null)
        {
            foreach (TACG_TinyHero_Client item in allTinyHero.Values)
            {
                TACG_Item_Client client = DataBaseManager.Instance.SearchACGItem(item.iID);
                if (client != null)
                {
                    if (!allCfg.ContainsKey(client.sTeamLeaderCfg))
                    {
                        allCfg.Add(client.sTeamLeaderCfg, client.sPreviewResource);
                    }
                }
            }
        }

        string cfgDir = "Assets/Art_TFT_Raw/cfg/team_leader_cfg";
        string prefabDir = "Assets/Art_TFT_Raw/teamleader_show";
        foreach (KeyValuePair<string, string> pair in allCfg)
        {
            string cfgName = pair.Key;
            string prefab = pair.Value;

            int index = GetLastIndex(prefab, '_', 2);
            string subDir = prefab.Substring(0, index);
            
            Debug.Log("cfg " + cfgName + " subDir " + subDir);

            string prefabPath = prefabDir + "/" + subDir + "/" + prefab + ".prefab";

            GameObject obj = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);

            if (obj == null)
            {
                Debug.LogError("error empty prefab " + prefabPath);
                continue;
            }
            
            AnimatorController animatorController = TeamLeaderAnimatorUtil.GetAnimatorController(obj);

            if (animatorController == null)
            {
                Debug.LogError("error empty controller");  
                continue;
            }

            AnimatorStateMachine stateMachine = TeamLeaderAnimatorUtil.GetStateMachine(animatorController, 0);
                
            AnimSheetConfig sheetConfig = new AnimSheetConfig();

            int i = 0;
            foreach (ChildAnimatorState childAnimatorState in stateMachine.states)
            {
                //sheetConfig.m_animIds.Add(childAnimatorState.state.name, i);
                i++;
            }

            int len = i;

            sheetConfig.m_crossFades = new float[len, len];

            for (int n = 0; n  < len; n ++)
            {
                for (int m = 0; m < len; m++)
                {
                    sheetConfig.m_crossFades[n, m] = 0.1f;
                }
            }

            string cfgPath = cfgDir + "/" + cfgName + ".asset";

            DataGroupAsset dataGroupAsset = AssetDatabase.LoadAssetAtPath<DataGroupAsset>(cfgPath);
            DataGroupAsset.DataGroup dataGroup = dataGroupAsset.AddDataGroup("AnimCrossFadeTime");

            //dataGroupAsset.AddDataItem(dataGroup.dataList, "CrossFadeTime", sheetConfig.WriteByte(), string.Empty);
                
            EditorUtility.SetDirty(dataGroupAsset);
        }
        
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }

    public static int GetLastIndex(string str, char c, int num)
    {
        int n = 0;
        int count = str.Length;
        int index = count - 1;

        for (; index >= 0; index--)
        {
            char a = str[index];
            if (a == c)
            {
                n++;
                if (n == num)
                {
                    break;
                }
            }
        }

        return index;
    }

    [MenuItem("Assets/小小英雄/刷新cfg动画")]
    public static void RefreshAnimTime()
    {
        string guid = Selection.assetGUIDs[0];
        string path = AssetDatabase.GUIDToAssetPath(guid);

        if (path.EndsWith(".asset"))
        {
            DataGroupAsset dataAsset = AssetDatabase.LoadAssetAtPath<DataGroupAsset>(path);

            ChangeAnimationList(dataAsset);
            
            DataGroupAssetEditor.Save(dataAsset);
                
            EditorUtility.SetDirty(dataAsset);
            
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
    }
    
    [MenuItem("Assets/小小英雄/刷新所有cfg动画")]
    public static void RefreshAllAnimTime()
    {
        string guid = Selection.assetGUIDs[0];
        string path = AssetDatabase.GUIDToAssetPath(guid);
        
        string dir = Path.GetDirectoryName(path);

        string[] allFiles = Directory.GetFiles(dir);
        foreach (string file in allFiles)
        {
            if (file.EndsWith(".asset"))
            {
                DataGroupAsset dataAsset = AssetDatabase.LoadAssetAtPath<DataGroupAsset>(file);
                
                try
                {
                    ChangeAnimationList(dataAsset);
                
                    DataGroupAssetEditor.Save(dataAsset);
                
                    EditorUtility.SetDirty(dataAsset);
                }
                catch (Exception e)
                {
                    Debug.LogError(e.Message);
                    Debug.LogError(e.StackTrace);
                }

            }
        }
        
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }
    
    [MenuItem("Assets/小小英雄/更新CFG字段/修改所有CFG")]
    public static void ChangeAllCfg()
    {
        string guid = Selection.assetGUIDs[0];
        string path = AssetDatabase.GUIDToAssetPath(guid);
        
        string dir = Path.GetDirectoryName(path);

        string[] allFiles = Directory.GetFiles(dir);
        foreach (string file in allFiles)
        {
            if (file.EndsWith(".asset"))
            {
                Debug.Log("lucky fuck " + file);
                
                DataGroupAsset dataAsset = AssetDatabase.LoadAssetAtPath<DataGroupAsset>(file);
                
                try
                {
                    RefreshAnimatinList(dataAsset);
                
                    DataGroupAssetEditor.Save(dataAsset);
                
                    EditorUtility.SetDirty(dataAsset);
                }
                catch (Exception e)
                {
                    Debug.LogError(e.Message);
                    Debug.LogError(e.StackTrace);
                }

            }
        }
        
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }
    
    [MenuItem("Assets/小小英雄/更新CFG字段/修改单个CFG")]
    public static void ChangeOneCfg()
    {
        string guid = Selection.assetGUIDs[0];
        string path = AssetDatabase.GUIDToAssetPath(guid);

        if (path.EndsWith(".asset"))
        {
            DataGroupAsset dataAsset = AssetDatabase.LoadAssetAtPath<DataGroupAsset>(path);

            RefreshAnimatinList(dataAsset);
            
            DataGroupAssetEditor.Save(dataAsset);
                
            EditorUtility.SetDirty(dataAsset);
            
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
    }

    private static List<DataGroupAsset.DataItem> GetDataList(DataGroupAsset dataGroupAsset, string type)
    {
        DataGroupAsset.DataGroup dataGroup = dataGroupAsset.GetDataGroup(type);
        if (dataGroup != null)
        {
            return dataGroup.dataList;
        }

        return null;
    }

    #region 更新动画后
    
    private static void ChangeAnimationList(DataGroupAsset dataGroupAsset)
    {
        
        TKDictionary<string, int> oldAnimDic = GetOldAnimationList(dataGroupAsset);

        if (oldAnimDic == null)
            return;

        List<string> newAnim = GetNewAnimationList(dataGroupAsset);

        if (newAnim == null)
            return;

        ResetSpringConfig(dataGroupAsset, newAnim, oldAnimDic);
        
        ResetAnimCrossFadeTime(dataGroupAsset, newAnim, oldAnimDic);

        ResetAnimtionList(dataGroupAsset, newAnim);
        
        RefreshAnimatinList(dataGroupAsset);
    }
    
    #region 刷新动画时间

    public static void RefreshAnimatinList(DataGroupAsset dataGroupAsset)
    {
        string cfg = dataGroupAsset.name;
        
        AnimatorStateMachine stateMachine = CfgFindAnimator(cfg);

        if (stateMachine == null)
            return;

        Dictionary<string, string[]> animDic = new Dictionary<string, string[]>();
        
        animDic.Add("Run", new string[]{ "run_start", "run_stop", "run_stop_02"});
        animDic.Add("RunFast", new string[]{ "run_fast_start", "run_fast_stop", "run_fast_stop_02"});
        animDic.Add("Rush", new string[]{ "rush"});
        animDic.Add("Hurt", new string[]{ "hurt_01", "hurt_02"});

        foreach (var pair in animDic)
        {
            string group = pair.Key;

            float speed = 0.0f;
            bool isChangeDis = false;
            switch (group)
            {
                case "Run":
                    speed = dataGroupAsset.GetDataItem("Run", "run_Speed").floatVal;
                    break;
                case "RunFast":
                case "Rush":
                    speed = dataGroupAsset.GetDataItem("RunFast", "run_fast_Speed").floatVal;
                    break;
                case "Hurt":
                    isChangeDis = false;
                    break;
                default:
                    break;
            }

            foreach (string animName in pair.Value)
            {
                if (!UpdateAnimationCurve(stateMachine, speed, dataGroupAsset, group, animName, string.Empty, isChangeDis))
                {
                    DataGroupAsset.DataItem dataItem = dataGroupAsset.GetDataItem(group, animName+"_Curve");
                    UpdateCurveTime(dataItem, 0f, 0f, 0f);
                    UpdateCurveValue(dataItem, 0f, 0f, 0f);
                }
            }
        }
    }
    
    #endregion

    #region 获取旧动画列表

    public static TKDictionary<string, int> GetOldAnimationList(DataGroupAsset dataGroupAsset)
    {
        DataGroupAsset.DataGroup dataGroup = dataGroupAsset.GetDataGroup("AnimationList");

        if (dataGroup == null)
            return null;
        
        TKDictionary<string, int> animDic = new TKDictionary<string, int>();

        List<DataGroupAsset.DataItem> dataItems = dataGroup.dataList;
        for (int i = 0; i < dataItems.Count; i++)
        {
            DataGroupAsset.DataItem dataItem = dataItems[i];
            
            animDic.Add(dataItem.strVal, i);
        }

        return animDic;
    }

    #endregion

    #region 获取新动画列表
    
    public static List<string> GetNewAnimationList(DataGroupAsset dataGroupAsset)
    {
        string cfg = dataGroupAsset.name;
        
        AnimatorStateMachine stateMachine = CfgFindAnimator(cfg);

        if (stateMachine == null)
            return null;
        
        List<string> animNames = new List<string>();
        foreach (ChildAnimatorState childAnimatorState in stateMachine.states)
        {
            animNames.Add(childAnimatorState.state.name);
        }

        foreach (ChildAnimatorStateMachine childAnimatorStateMachine in stateMachine.stateMachines)
        {
            foreach (ChildAnimatorState childAnimatorState in childAnimatorStateMachine.stateMachine.states)
            {
                animNames.Add(childAnimatorState.state.name);
            }
        }

        animNames.Sort();
        
        return animNames;
    }
    
    #endregion
    
    #region 更新动态骨骼开关

    private static void ResetSpringConfig(DataGroupAsset dataGroupAsset, List<string> newAnim,
        TKDictionary<string, int> oldAnim)
    {
        DataGroupAsset.DataItem springDataItem = dataGroupAsset.GetDataItem("AnimSpringSwitch", string.Empty);
        if (springDataItem == null)
            return;
        AnimSpringManagerConfig animSpringManagerConfig = new AnimSpringManagerConfig();
        animSpringManagerConfig.ReadByte(springDataItem.binaryData);
        animSpringManagerConfig.ChangeAnimList(oldAnim, newAnim);
        springDataItem.binaryData = animSpringManagerConfig.WriteByte();
    }
    
    #endregion
    
    #region 更新动画过渡时间
    
    private static void ResetAnimCrossFadeTime(DataGroupAsset dataGroupAsset, List<string> newAnim,
        TKDictionary<string, int> oldAnim)
    {
        DataGroupAsset.DataItem crossTimeDataItem = dataGroupAsset.GetDataItem("AnimCrossFadeTime", string.Empty);
        if (crossTimeDataItem == null)
            return;
        AnimSheetConfig animSheetConfig = new AnimSheetConfig();
        animSheetConfig.ReadByte(crossTimeDataItem.binaryData);
        animSheetConfig.ChangeAnimList(oldAnim, newAnim);
        crossTimeDataItem.binaryData = animSheetConfig.WriteByte();
    }
    
    #endregion
    
    #region 更新动画列表

    public static void ResetAnimtionList(DataGroupAsset dataGroupAsset, List<string> newAnim)
    {
        dataGroupAsset.RemoveDataGroup("AnimationList");
        
        DataGroupAsset.DataGroup dataGroup = dataGroupAsset.AddDataGroup("AnimationList");
        
        foreach (string anim in newAnim)
        {
            DataGroupAsset.DataItem dataItem = new DataGroupAsset.DataItem();
            dataItem.type = DataTypeEnum.Animation;
            dataItem.strVal = anim;
            
            dataGroup.dataList.Add(dataItem);
        }
    }
    
    #endregion

    #region 根据Cfg找Animator的状态机
    
    private static AnimatorStateMachine CfgFindAnimator(string cfg)
    {
        DataBaseManager.Instance.Initialize();

        TACG_Item_Client itemClientResult = null;
        foreach(TACG_Item_Client client in DataBaseManager.allLocalData.mapACG_Item_Client.Values)
        {
            if (client.sTeamLeaderCfg == cfg)
            {
                itemClientResult = client;
                break;
            }
        }

        if (itemClientResult == null)
        {
            Debug.LogError(cfg + "找不到对应的TACG_Item_Client");
            return null;
        }
            

        string prefab = itemClientResult.sPreviewResource;

        string dir = "Assets/Art_TFT_Raw/teamleader_show/";

        string[] allFiles = Directory.GetFiles(dir, "*.prefab", SearchOption.AllDirectories);

        string fileResult = null;
        foreach (string file in allFiles)
        {
            if (file.Contains(prefab))
            {
                fileResult = file;
                break;
            }
        }

        if (string.IsNullOrEmpty(fileResult))
        {
            Debug.LogError(cfg + "找不到对应的prefab");
            return null;
        }
        
        Debug.Log("file " + fileResult);
        
        GameObject obj = AssetDatabase.LoadAssetAtPath<GameObject>(fileResult);

        AnimatorStateMachine stateMachine = TeamLeaderAnimatorUtil.GetAnimatorMachine(obj);
        if (stateMachine == null)
        {
            Debug.LogError("找不到该模型的动作状态机 " + obj.name);
            return null;
        }

        return stateMachine;
    }
    
    #endregion
    
    #endregion

    #region 曲线相关
    
    public static bool UpdateAnimationCurve(AnimatorStateMachine stateMachine, float speed, DataGroupAsset dataGroupAsset, string group, string animName, string modelName, bool isChangeDis = true)
    {
        AnimatorState state = TeamLeaderAnimatorUtil.GetChildState(stateMachine, animName);
        if (state == null)
        {
            Debug.LogError(modelName + " 不存在 " + animName + " 这个状态");
            return false;
        }
        
        AnimationClip clip = state.motion as AnimationClip;
        if (clip == null)
        {
            Debug.LogError(modelName + " " + animName + "这个状态的动作为空");
            return false;
        }
        
        UpdateAnimationCurveTimeAndDis(clip, speed, dataGroupAsset, group, animName+"_Curve", isChangeDis);
        return true;
    }

    private static void UpdateAnimationCurveTimeAndDis(AnimationClip clip, float speed, DataGroupAsset dataGroupAsset, string group, string param, bool isChangeDis)
    {
        DataGroupAsset.DataItem dataItem = dataGroupAsset.GetDataItem(group, param);

        CheckCurve(dataItem);
        
        float oldTime = 0.0f;
        float oldDis = 0.0f;
        GetTimeAndDis(dataItem, ref oldTime, ref oldDis);

        float newTime = clip.length;
        UpdateCurveTime(dataItem, newTime, oldTime, oldDis);

        if (isChangeDis)
        {
            float newDis = clip.length * speed;
            UpdateCurveValue(dataItem, newDis, oldDis, newTime);
        }
    }

    private static void CheckCurve(DataGroupAsset.DataItem dataItem)
    {
        AnimationCurve curve = dataItem.curve;
        if (curve == null || curve.keys.Length < 2)
        {
            dataItem.curve = CreateDefaultCurve(1.0f, 1.0f);
        }
    }

    private static void GetTimeAndDis(DataGroupAsset.DataItem dataItem, ref float time, ref float dis)
    {
        AnimationCurve curve = dataItem.curve;
        Keyframe[] keys = curve.keys;
        int len = keys.Length;

        Keyframe lastFrame = keys[len - 1];

        time = lastFrame.time;
        dis = lastFrame.value;
    }

    public static void UpdateCurveTime(DataGroupAsset.DataItem dataItem, float newTime, float oldTime, float dis)
    {
        if (Mathf.Approximately(oldTime, 0.0f))
        {
            dataItem.curve = CreateDefaultCurve(newTime, dis);
        }
        else
        {
            Keyframe[] keyframes = dataItem.curve.keys;
            float timeScale = newTime / oldTime;
            
            Keyframe[] newKeyFrames = new Keyframe[keyframes.Length];

            for (int i = 0; i < keyframes.Length; i++)
            {
                Keyframe oneFrame = keyframes[i];

                oneFrame.time = oneFrame.time * timeScale;

                newKeyFrames[i] = oneFrame;
            }
            
            dataItem.curve.keys = newKeyFrames;
        }
    }
    
    public static void UpdateCurveValue(DataGroupAsset.DataItem dataItem, float newDis, float oldDis, float time)
    {
        if (Mathf.Approximately(oldDis, 0.0f))
        {
            dataItem.curve = CreateDefaultCurve(time, newDis);
        }
        else
        {
            Keyframe[] keyframes = dataItem.curve.keys;
            float valueScale = newDis / oldDis;
            
            Keyframe[] newKeyFrames = new Keyframe[keyframes.Length];
            
            for (int i = 0; i < keyframes.Length; i++)
            {
                Keyframe oneFrame = keyframes[i];

                oneFrame.value = oneFrame.value * valueScale;

                newKeyFrames[i] = oneFrame;
            }

            dataItem.curve.keys = newKeyFrames;
        }
            
        

        
    }

    #endregion
    
    #region SpringSwitch
    
    public static void RebuildSpringSwitch(DataGroupAsset dataGroupAsset)
    {
        dataGroupAsset.RemoveDataGroup("AnimSpringSwitch");
        
        List<DataGroupAsset.DataItem> dataList = GetDataList(dataGroupAsset, "AnimationList");
        if (dataList != null)
        {
            DataGroupAsset.DataGroup dataGroup = dataGroupAsset.AddDataGroup("AnimSpringSwitch");

            int len = dataList.Count;
            
            bool[] config = new bool[len];
            for (int i = 0; i < len; i++)
            {
                config[i] = false;
            }
            
            AnimSpringManagerConfig animSpringManagerConfig = new AnimSpringManagerConfig();
            animSpringManagerConfig.SetAnimSpringConfig(config);

            DataGroupAsset.DataItem dataItem = new DataGroupAsset.DataItem();
            dataItem.name = string.Empty;
            dataItem.type = DataTypeEnum.AnimSprintSwtich;
            dataItem.binaryData = animSpringManagerConfig.WriteByte();

            dataGroup.dataList.Add(dataItem);
        }
    }
    
    #endregion

    #region AnimCrossFade
    
    public static void RebuildAnimCrossFadeTime(DataGroupAsset dataGroupAsset)
    {
        dataGroupAsset.RemoveDataGroup("AnimCrossFadeTime");
        
        DataGroupAsset.DataGroup dataGroup = dataGroupAsset.GetDataGroup("AnimationList");

        List<DataGroupAsset.DataItem> animList = dataGroup.dataList;
        
        AnimSheetConfig config = new AnimSheetConfig();

        int count = animList.Count;

        config.m_crossFades = new float[count, count];

        for (int i = 0; i < count; i++)
        {
            for (int j = 0; j < count; j++)
            {
                float result = 0.0f;
                if (i != j)
                {
                    result = 0.2f;
                }

                config.m_crossFades[i, j] = result;
            }
        }

        DataGroupAsset.DataGroup crossDataGroup = dataGroupAsset.AddDataGroup("AnimCrossFadeTime");
        DataGroupAsset.DataItem dataItem = new DataGroupAsset.DataItem();
        dataItem.type = DataTypeEnum.AnimCrossTime;
        dataItem.binaryData = config.WriteByte();
        dataItem.name = string.Empty;
        
        crossDataGroup.dataList.Add(dataItem);
    }

    #endregion

    private static void ConvertCurve(DataGroupAsset dataAsset, string animType, string animName)
    {
        Debug.Log("lucky fuck you  " + animType + " " + animName);
        DataGroupAsset.DataGroup dataGroup = dataAsset.GetDataGroup(animType);
        
        DataGroupAsset.DataItem dis = dataAsset.GetDataItem(dataGroup.dataList, animName + "_Dis");
        DataGroupAsset.DataItem time = dataAsset.GetDataItem(dataGroup.dataList, animName + "_Time");
        DataGroupAsset.DataItem curve = dataAsset.GetDataItem(dataGroup.dataList, animName + "_Curve");

        float disValue = dis.floatVal;
        float timeValue = time.floatVal;
        AnimationCurve curveValue = curve.GetCurveVal();

        int len = curveValue.keys.Length;
        Keyframe[] curveKeyFrame = new Keyframe[len];
        
        for (int i = 0; i < len; i++)
        {
            Keyframe oneFrame = curveValue.keys[i];
            oneFrame.time = oneFrame.time * timeValue;
            oneFrame.value = oneFrame.value * disValue;
            curveKeyFrame[i] = oneFrame;
        }

        curveValue.keys = curveKeyFrame;

        dataAsset.RemoveDataItem(dataGroup.dataList, animName + "_Dis");
        dataAsset.RemoveDataItem(dataGroup.dataList, animName + "_Time");
    }

    private static void ConvertHeightCureve(DataGroupAsset dataAsset)
    {
        DataGroupAsset.DataGroup dataGroup = dataAsset.GetDataGroup("Hurt");
        
        DataGroupAsset.DataItem time = dataAsset.GetDataItem(dataGroup.dataList, "hurt_02_Time");
        DataGroupAsset.DataItem curve = dataAsset.GetDataItem(dataGroup.dataList, "hurt_02_HeightCurve");

        float timeValue = time.floatVal;
        AnimationCurve curveValue = curve.GetCurveVal();
        
        int len = curveValue.keys.Length;
        Keyframe[] curveKeyFrame = new Keyframe[len];
        
        for (int i = 0; i < len; i++)
        {
            Keyframe oneFrame = curveValue.keys[i];
            oneFrame.time = oneFrame.time * timeValue;
            curveKeyFrame[i] = oneFrame;
        }

        curveValue.keys = curveKeyFrame;
    }

    private static void SetDis(string typeName, string pre, DataGroupAsset dataAsset)
    {
        DataGroupAsset.DataGroup dataGroup = dataAsset.GetDataGroup(typeName);

        DataGroupAsset.DataItem rundataItem = dataAsset.GetDataItem(dataGroup.dataList, pre + "_Speed");
        float speed = rundataItem.floatVal;
                
        DataGroupAsset.DataItem stopTimeItem = dataAsset.GetDataItem(dataGroup.dataList, pre + "_stop_Time");
        float time = stopTimeItem.floatVal;
        
        DataGroupAsset.DataItem disdataItem = dataAsset.GetDataItem(dataGroup.dataList, pre + "_stop_Dis");
        float dis = speed * time * 0.5f;
        if(dis < disdataItem.floatVal)
            disdataItem.floatVal = dis;
        
        DataGroupAsset.DataItem stop02TimeItem = dataAsset.GetDataItem(dataGroup.dataList, pre + "_stop_02_Time");
        float time02 = stop02TimeItem.floatVal;
        
        DataGroupAsset.DataItem dis02dataItem = dataAsset.GetDataItem(dataGroup.dataList, pre + "_stop_02_Dis");
        float dis02 = speed * time02 * 0.5f;
        if(dis02 < dis02dataItem.floatVal)
            dis02dataItem.floatVal = dis02;
    }

    [MenuItem("Assets/小小英雄/更新CFG字段/新增单个蓄力CFG")]
    public static void AddSingleCastCFG()
    {
        Object obj = Selection.activeObject;
        if (obj is DataGroupAsset)
        {
            DataGroupAsset dataAsset = obj as DataGroupAsset;

            DataGroupAsset.DataGroup dataGroup = dataAsset.AddDataGroup("Cast");
            dataAsset.AddDataItem(dataGroup.dataList, "CastFrame", 21, "蓄力帧数");
            dataAsset.AddDataItem(dataGroup.dataList, "CastAbName", "scene_hero_bulletcast", "蓄力特效");

            EditorUtility.SetDirty(dataAsset);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
    }
    
    
    [MenuItem("Assets/小小英雄/更新CFG字段/新增目录CastCFG")]
    public static void AddCastCFG()
    {
        string guid = Selection.assetGUIDs[0];
        string path = AssetDatabase.GUIDToAssetPath(guid);
        string dir = Path.GetDirectoryName(path);

        string[] allFiles = Directory.GetFiles(dir);
        foreach (string file in allFiles)
        {
            if (file.EndsWith(".asset"))
            {
                DataGroupAsset dataAsset = AssetDatabase.LoadAssetAtPath<DataGroupAsset>(file);
                if(dataAsset == null || dataAsset.GetDataGroup("Cast") != null)
                {
                    continue;
                }
                DataGroupAsset.DataGroup dataGroup = dataAsset.AddDataGroup("Cast");
                dataAsset.AddDataItem(dataGroup.dataList, "CastFrame", 21, "蓄力帧数");
                dataAsset.AddDataItem(dataGroup.dataList, "CastAbName", "scene_hero_bulletcast", "蓄力特效");
                EditorUtility.SetDirty(dataAsset);
            }
        }
        
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }

    [MenuItem("Assets/小小英雄/更新CFG字段/新增目录BeatedCFG")]
    public static void AddBeatedCFG()
    {
        string guid = Selection.assetGUIDs[0];
        string path = AssetDatabase.GUIDToAssetPath(guid);
        string dir = Path.GetDirectoryName(path);

        string[] allFiles = Directory.GetFiles(dir);
        foreach (string file in allFiles)
        {
            if (file.EndsWith(".asset"))
            {
                DataGroupAsset dataAsset = AssetDatabase.LoadAssetAtPath<DataGroupAsset>(file);
                if(dataAsset == null || dataAsset.GetDataGroup("BeatedCfg") != null)
                {
                    continue;
                }
                DataGroupAsset.DataGroup dataGroup = dataAsset.AddDataGroup("BeatedCfg");
                dataAsset.AddDataItem(dataGroup.dataList, "BodyScaleChangePlan", 0, "受击变小方案");
                EditorUtility.SetDirty(dataAsset);
            }
        }
        
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }
    
    [MenuItem("Assets/小小英雄/更新CFG字段/新增单个胜利时相机CFG")]
    public static void AddSingleCameraCfg()
    {
        Object obj = Selection.activeObject;
        if (obj is DataGroupAsset)
        {
            DataGroupAsset dataAsset = obj as DataGroupAsset;

            DataGroupAsset.DataGroup dataGroup = dataAsset.AddDataGroup("VictoryCamera");
            dataGroup.dataList.Clear();
            dataAsset.AddDataItem(dataGroup.dataList, "CameraHomeAnimationName", "Default_Home", "摄像机动画主场名称");
            dataAsset.AddDataItem(dataGroup.dataList, "CameraAwayAnimationName", "Default_Away", "摄像机动画客场名称");
            dataAsset.AddDataItem(dataGroup.dataList, "CameraAnimationHomeTime", 8.0f, "摄像机动画主场时间");
            dataAsset.AddDataItem(dataGroup.dataList, "CameraAnimationAwayTime", 5.0f, "摄像机动画客场时间");
            
            EditorUtility.SetDirty(dataAsset);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
    }
    
    [MenuItem("Assets/小小英雄/更新CFG字段/新增目录下胜利时相机CFG")]
    public static void AddDirectoryCameraCfg()
    {
        string guid = Selection.assetGUIDs[0];
        string path = AssetDatabase.GUIDToAssetPath(guid);
        string dir = Path.GetDirectoryName(path);

        string[] allFiles = Directory.GetFiles(dir);
        foreach (string file in allFiles)
        {
            if (file.EndsWith(".asset"))
            {
                DataGroupAsset dataAsset = AssetDatabase.LoadAssetAtPath<DataGroupAsset>(file);
                DataGroupAsset.DataGroup dataGroup = dataAsset.AddDataGroup("VictoryCamera");
                dataGroup.dataList.Clear();
                dataAsset.AddDataItem(dataGroup.dataList, "CameraHomeAnimationName", "Default_Home", "摄像机动画主场名称");
                dataAsset.AddDataItem(dataGroup.dataList, "CameraAwayAnimationName", "Default_Away", "摄像机动画客场名称");
                dataAsset.AddDataItem(dataGroup.dataList, "CameraAnimationHomeTime", 8.0f, "摄像机动画主场时间");
                dataAsset.AddDataItem(dataGroup.dataList, "CameraAnimationAwayTime", 5.0f, "摄像机动画客场时间");
                EditorUtility.SetDirty(dataAsset);
            }
        }
        
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }
    
    private static AnimationCurve CreateDefaultCurve(float time, float dis)
    {
        AnimationCurve curve = new AnimationCurve();
        curve.AddKey(0, 0);

        float tmpTime = time;
        if (Mathf.Approximately(time, 0f))
        {
            tmpTime = 1.0f;
        }
        
        curve.AddKey(tmpTime, dis);
        return curve;
    }
#if !OUTSOURCE
    [MenuItem("Assets/GetConfigCount")]
    public static void GetData()
    {
        string guid = Selection.assetGUIDs[0];
        string path = AssetDatabase.GUIDToAssetPath(guid);
        ParticleSystemConfig config = AssetDatabase.LoadAssetAtPath<ParticleSystemConfig>(path);
        if (config != null)
        {
            SerializedObject obj = new SerializedObject(config);
            SerializedProperty sp = obj.FindProperty("m_count");
            int count = sp.intValue;
            Debug.Log("lucky config count " + sp.intValue);
        }
    }

    [MenuItem("luckywei/Check PS Count")]
    public static void CheckParticleSystem()
    {
        string filePath = "PSCount.txt";
        string[] paths = AssetDatabase.FindAssets("t:Prefab");
        int count = paths.Length;
        EditorUtility.DisplayCancelableProgressBar("Scan Prefab", string.Empty, 0f);
        
        if(File.Exists(filePath))
            File.Delete(filePath);

        using (FileStream fs = new FileStream(filePath, FileMode.Create))
        {
            using (StreamWriter sw = new StreamWriter(fs))
            {
                int i = 0;
                for (; i < count; i++)
                {
                    string file = AssetDatabase.GUIDToAssetPath(paths[i]);
                    if (EditorUtility.DisplayCancelableProgressBar("Scan Prefab", file, (float)i / (float)count))
                    {
                        break;
                    }

                    try
                    {
                        GameObject obj = AssetDatabase.LoadAssetAtPath<GameObject>(file);
                        GfxRoot_Unity[] gfxRoots = obj.GetComponentsInChildren<GfxRoot_Unity>(true);
                        foreach (GfxRoot_Unity gfxRootUnity in gfxRoots)
                        {
                            string configPath = gfxRootUnity.GetConfigPath();
                            if (!string.IsNullOrEmpty(configPath) && File.Exists(configPath))
                            {
                                ParticleSystemConfig psConfig = AssetDatabase.LoadAssetAtPath<ParticleSystemConfig>(configPath);
                                SerializedObject configSO = new SerializedObject(psConfig);
                                SerializedProperty countSP = configSO.FindProperty("m_count");
                                int dataCount = countSP.intValue;
                                int maxId = dataCount - 1;

                                ParticleSystemCompressData[] compressDatas = gfxRootUnity.GetComponentsInChildren<ParticleSystemCompressData>(true);

                                foreach (ParticleSystemCompressData compressData in compressDatas)
                                {
                                    SerializedObject compressDataSO = new SerializedObject(compressData);
                                    SerializedProperty idSP = compressDataSO.FindProperty("m_id");
                                    int id = idSP.intValue;
                                    if (id > maxId)
                                    {
                                        sw.WriteLine("path " + file);
                                        sw.WriteLine("gfxRootInfo " + gfxRootUnity.gameObject.name + " " + configPath);
                                        sw.WriteLine("count diff " + id + " " + maxId);
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        Debug.LogError(e);
                    }
                }
            }
        }
        
        EditorUtility.ClearProgressBar();
    }

    [MenuItem("Assets/替换TKDictionary")]
    public static void TestChangeFile()
    {
        string[] guids = Selection.assetGUIDs;
        foreach(string guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            if (Directory.Exists(path))
            {
                string[] allFiles = Directory.GetFiles(path, "*.cs", SearchOption.AllDirectories);
                foreach (string file in allFiles)
                {
                    ChangeOneFile(file);
                }
            }
            else if (File.Exists(path))
            {
                ChangeOneFile(path);
            }
        }
    }

    private static Encoding utf8Bom = new UTF8Encoding(true);

    private static void ChangeOneFile(string path)
    {
        string allLines = File.ReadAllText(path);
        string newAllLines = ReplaceText(allLines);
        File.WriteAllText(path, newAllLines, utf8Bom);
    }

    private static string ReplaceText(string data)
    {
        data = data.Replace("System.Collections.Generic.Dictionary<", "TKFrame.TKDictionary<");
        string p =@"\bDictionary\b";
        Regex regex = new Regex(p);
        data = regex.Replace(data, "TKDictionary");
        return data;
    }

    [MenuItem("luckywei/Test Replace")]
    private static void ReplaceText()
    {
        string[] lines =
        {
            "abcd Dictionary<int, b> = asdf",
            "abcd TKDictionary<int, b> = asdf",
            "public Dictionary<int, Dictionary<ddd, ada> heheDictionary = adasdf",
            "public Dictionary<int, int> hehe(Dictionary<int, string> b);",
            "public Dictionary<int, int> hehe(TKDictionary<int, string> b);",
            "public class aaaDicitonary<TKey, TValue> : Dictionary<TKey, TValue>"
        };
        
        string p =@"\bDictionary\b";
        Regex regex = new Regex(p);

        foreach (string line in lines)
        {
            string newLine = regex.Replace(line, "TKDictionary");
            Debug.Log(newLine);
        }
        
        Debug.Log("---------------------------------------");
        
        string input = "Dictionary<string, int> dict = new Dictionary<string, int>();";
        string pattern = @"Dictionary(?!\s*<\s*)\s*";
        string replacement = "TKDictionary";
        string output = Regex.Replace(input, pattern, replacement, RegexOptions.IgnoreCase);
        Debug.Log(output);
    }
    
    [Serializable]
    [ProtoContract]
    public sealed class BuffDataCollection1
    {
        [ProtoMap(DisableMap = true)]
        [ProtoMember(1)]
        public TKDictionary<int, BuffProtoResData> protoData = new TKDictionary<int, BuffProtoResData>();
        
        [ProtoMap(DisableMap = true)]
        [ProtoMember(2)]
        public TKDictionary<int, BuffLevelResData> levelData = new TKDictionary<int, BuffLevelResData>();

        [ProtoMap(DisableMap = true)]
        [ProtoMember(3)]
        public TKDictionary<int, BuffEffectGroupResData> groupData = new TKDictionary<int, BuffEffectGroupResData>();
        
        [ProtoMap(DisableMap = true)]
        [ProtoMember(4)]
        public TKDictionary<int, BuffProtoResData> protoDataTurbo = new TKDictionary<int, BuffProtoResData>();
        
        [ProtoMap(DisableMap = true)]
        [ProtoMember(5)]
        public TKDictionary<int, BuffLevelResData> levelDataTurbo = new TKDictionary<int, BuffLevelResData>();     
        
        [ProtoMap(DisableMap = true)]
        [ProtoMember(6)]
        public TKDictionary<int, BuffProtoResData> protoDataDual = new TKDictionary<int, BuffProtoResData>();
        
        [ProtoMap(DisableMap = true)]
        [ProtoMember(7)]
        public TKDictionary<int, BuffLevelResData> levelDataDual = new TKDictionary<int, BuffLevelResData>();
        
        [ProtoMap(DisableMap = true)]
        [ProtoMember(8)]
        public TKDictionary<int, BuffProtoResData> protoDataLimited = new TKDictionary<int, BuffProtoResData>();
        
        [ProtoMap(DisableMap = true)]
        [ProtoMember(9)]
        public TKDictionary<int, BuffLevelResData> levelDataLimited = new TKDictionary<int, BuffLevelResData>();
        
        [ProtoMap(DisableMap = true)]
        [ProtoMember(10)]
        public TKDictionary<int, TKDictionary<int, int>> combineData = new TKDictionary<int, TKDictionary<int, int>>(); // 触发Buff原型id：效果buff原型id：等级
    }
    
    

    [MenuItem("luckywei/test pb")]
    public static void ReadPB()
    {
        BuffDataCollection data;
        BuffDataCollection1 data1;
        string file = "Assets/Art_TFT_Raw/cfg/buff_data_cfg/buff_data_pb.bytes";
        using (FileStream fs = new FileStream(file, FileMode.Open))
        {
            data = PbNetAnalyser.Decode<BuffDataCollection>(fs);
            
            fs.Position = 0;
            
            data1 = PbNetAnalyser.Decode<BuffDataCollection1>(fs);

            
        }

        BuffDataCollection1 data2;
        using (MemoryStream ms = new MemoryStream())
        {
            BinaryFormatter bf = new BinaryFormatter();
            bf.Serialize(ms, data1);
            ms.Position = 0;
            data2 = bf.Deserialize(ms) as BuffDataCollection1;
        }
        
        
        string file1 = "Assets/Art_TFT_Raw/cfg/buff_data_cfg/buff_data_json.bytes";
        using (FileStream fs = new FileStream(file1, FileMode.Create))
        {
            string jsonStr = Newtonsoft.Json.JsonConvert.SerializeObject(data);
            using (BinaryWriter bw = new BinaryWriter(fs))
            {
                bw.Write(jsonStr);
            }
        }

        data = null;
        data1 = null;

        using (FileStream fs = new FileStream(file1, FileMode.Open))
        {
            using (BinaryReader br = new BinaryReader(fs))
            {
                string jsonStr = br.ReadString();
                data = Newtonsoft.Json.JsonConvert.DeserializeObject<BuffDataCollection>(jsonStr);
                
                data1 = Newtonsoft.Json.JsonConvert.DeserializeObject<BuffDataCollection1>(jsonStr);
            }
        }
    }
#endif
}
