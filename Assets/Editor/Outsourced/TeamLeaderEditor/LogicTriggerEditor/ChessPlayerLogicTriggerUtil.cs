using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

public static class ChessPlayerLogicTriggerUtil
{
    #region 错误颜色展示

    private static Stack<Color> m_originColorStack = new Stack<Color>();

    public static void BeginError(bool isError)
    {
        if (isError)
        {
            m_originColorStack.Push(GUI.color);
            GUI.color = Color.red;
        }
    }

    public static void EndError(bool isError)
    {
        if (isError)
        {
            GUI.color = m_originColorStack.Pop();
        }
    }

    #endregion

}
