using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyTool;
using TKPlugins;
using TKPlugins_Extend;
using UnityEditor;
using UnityEngine;

public class ChessPlayerLogicTriggerEditor : EditorWindowBaseEx
{
    [SerializeField] private string m_path = string.Empty;
    private ChessPlayerTriggerCfg m_cfg = new ChessPlayerTriggerCfg();

    //private int m_selected = 0;
    //private string[] m_selectNames = new string[0];

    //private UnityEditorInternal.ReorderableList m_conditionList;
    //private UnityEditorInternal.ReorderableList m_actionList;

    private ChessPlayerLogicTriggerListSubEditor m_listSubEditor = null;
    private ChessPlayerLogicTriggerContentEditor m_contentEditor = null;

    [MenuItem("Assets/小小英雄/打开逻辑触发器")]
    public static void Open()
    {
        var path = AssetDatabase.GetAssetPath(Selection.activeObject);
        if (path.StartsWith("Assets/Art_TFT_Raw/cfg/TriggerConfig/Logic/") && path.EndsWith(".txt"))
        {
            var window = GetWindow<ChessPlayerLogicTriggerEditor>("逻辑触发器编辑器");
            window.Init(path);
            window.Show();
        }
    }

    [MenuItem("Assets/小小英雄/转化成新版逻辑触发器（带变量的）")]
    public static void ConvertNewFormat()
    {
        var path = AssetDatabase.GetAssetPath(Selection.activeObject);
        if (path.StartsWith("Assets/Art_TFT_Raw/cfg/TriggerConfig/Logic/") && path.EndsWith(".txt"))
        {
            var text = File.ReadAllText(path);
            if (text.StartsWith("[{\"m_condition"))
            {
                var list = JsonConvert.DeserializeObject<List<ChessPlayerTriggerInfo>>(text);
                ChessPlayerTriggerCfg cfg = new ChessPlayerTriggerCfg();
                cfg.triggers = list;
                var jsonStr = JsonConvert.SerializeObject(cfg);
                File.WriteAllText(path, jsonStr);
            }
        }
    }

    //public static void Open(DataGroupAsset dataGroupAsset)
    //{
    //    var window = GetWindow<ChessPlayerLogicTriggerEditor>("逻辑触发器编辑器");
    //    window.Init(dataGroupAsset);
    //    window.Show();
    //}

    protected void Init(string path)
    {
        InitData(path);

        m_SubPanelList.Clear();
        var commPanel = CreateSubPanel<ChessPlayerLogicTriggerCommSubEditor>("控制面板", new Vector2(400, 50), E_SubPanelAnchor.Left, false);
        commPanel.SetController(this);
        var varPanel = CreateSubPanel<ChessPlayerLogicTriggerVarSubEditor>("变量列表", new Vector2(400, 300), E_SubPanelAnchor.Left, new Vector2(350, 200), new Vector2(350, 600));
        varPanel.SetController(this);
        m_listSubEditor = CreateSubPanel<ChessPlayerLogicTriggerListSubEditor>("触发器列表", new Vector2(350, 700), E_SubPanelAnchor.Center, new Vector2(350, 500), new Vector2(350, 1000));
        m_listSubEditor.SetController(this);
        m_contentEditor = CreateSubPanel<ChessPlayerLogicTriggerContentEditor>("触发器内容", new Vector2(350, 700), E_SubPanelAnchor.Right, new Vector2(350, 500), new Vector2(500, 1000));
        m_contentEditor.SetController(this);

        OnTriggerSelected(0);
        OnVarChanged();

        ReLayout();
    }

    protected void InitData(string path)
    {
        m_path = path;
        if (File.Exists(path))
        {
            var text = File.ReadAllText(path);
            m_cfg = ChessPlayerTriggerCfg.Parse(text);
        }

        if (m_cfg == null)
            m_cfg = new ChessPlayerTriggerCfg();
    }

    #region 接口

    public void AddVar(CSoTriggerVar varTemplate)
    {
        CSoTriggerVar var = new CSoTriggerVar(varTemplate);
        m_cfg.vars.Add(var);

        OnVarChanged();
    }

    public void AddVar(CSoTriggerVar.VarType type)
    {
        CSoTriggerVar var = new CSoTriggerVar();
        var.name = "var" + m_cfg.vars.Count;
        var.type = type;
        m_cfg.vars.Add(var);

        OnVarChanged();
    }

    public List<CSoTriggerVar> GetVars() 
    { 
        return m_cfg.vars; 
    }

    public void RemoveVar(int index)
    {
        if (index >= 0 && index < m_cfg.vars.Count)
            m_cfg.vars.RemoveAt(index);
        OnVarChanged();
    }

    public void OnVarChanged()
    {
        m_contentEditor.OnVarChanged(m_cfg.vars);
    }

    public void AddTrigger()
    {
        m_cfg.triggers.Add(new ChessPlayerTriggerInfo() { name = m_cfg.triggers.Count.ToString() });
    }

    public void RemoveTrigger(int index)
    {
        if (m_cfg.triggers.Count > index && index >= 0)
            m_cfg.triggers.RemoveAt(index);
    }

    public void OnTriggerNameChanged()
    {
        m_listSubEditor.RefershNameList();
    }

    public void OnTriggerSelected(int index)
    {
        if (m_cfg.triggers.Count > index && index >= 0)
        {
            var t = m_cfg.triggers[index];

            m_contentEditor.OnTriggerSelected(t);
        }
        else
        {
            m_contentEditor.OnTriggerSelected(null);
        }
    }

    public List<string> GetTriggerNameList()
    {
        List<string> l = new List<string>();
        for (int i = 0; i < m_cfg.triggers.Count; ++i)
        {
            if (string.IsNullOrEmpty(m_cfg.triggers[i].name))
                l.Add(i.ToString());
            else
                l.Add(m_cfg.triggers[i].name);
        }
        return l;
    }

    #endregion

    public void Save()
    {
        var jsonStr = JsonConvert.SerializeObject(m_cfg);
        File.WriteAllText(m_path, jsonStr);

        AssetDatabase.Refresh();

        ShowNotification(new GUIContent("保存成功! 路径: " + m_path));
    }

    protected override void OnPreDraw()
    {
        base.OnPreDraw();

        if (m_cfg == null && !string.IsNullOrEmpty(m_path))
        {
            Init(m_path);
        }
    }

    protected override void OnPostDraw()
    {
        base.OnPostDraw();


    }
}

