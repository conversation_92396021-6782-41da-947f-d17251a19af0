using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using UnityEditor;
using UnityEditor.Animations;
using UnityEngine;
using Object = UnityEngine.Object;

public class ModelFaceSeparate 
{
    [MenuItem("Assets/分离表情动画")]
    public static void SeparateFace()
    {
        new ModelFaceSeparate().Separate();
    }
    
    private string m_faceLayerName = "Face Layer";
    private AnimatorController m_animatorController;
    private string m_dir;
    private string m_modelName;
    private string m_avatarMaskPath;
    private AnimatorStateMachine m_stateMachine;
    private MethodInfo m_copyMethod = null;
    private object[] paramDatas = new object[5];

    public ModelFaceSeparate()
    {
        Type unsupportedType = typeof(Unsupported);
        m_copyMethod = unsupportedType.GetMethod("CopyStateMachineDataToPasteboard", BindingFlags.Static | BindingFlags.NonPublic);
    }

    private void CopyStateMachineDataToPasteboard(UnityEngine.Object[] stateMachineObjects, AnimatorStateMachine stateMachine, Vector3[] position, AnimatorController controller, int layerIndex)
    {
        paramDatas[0] = stateMachineObjects;
        paramDatas[1] = stateMachine;
        paramDatas[2] = position;
        paramDatas[3] = controller;
        paramDatas[4] = layerIndex;
        m_copyMethod.Invoke(null, paramDatas);
    }


    public void Separate()
    {
        string[] assetGUIDs = Selection.assetGUIDs;
        if (assetGUIDs.Length != 1)
        {
            EditorUtility.DisplayDialog("表情分离", "请选择一个AnimatorController", "确定");
            return;
        }

        string guid = assetGUIDs[0];
        string controllerPath = AssetDatabase.GUIDToAssetPath(guid);
        m_animatorController = AssetDatabase.LoadAssetAtPath<AnimatorController>(controllerPath);
        if (m_animatorController == null)
        {
            EditorUtility.DisplayDialog("表情分离", "请选择一个AnimatorController", "确定");
            return;
        }

        if (HasLayer(m_faceLayerName))
        {
            EditorUtility.DisplayDialog("表情分离", "AnimatorController已经包含Face Layer了，请直接修改AvatarMask", "确定");
            return;
        }
        
        AnimatorStateMachine srcStateMachine = m_animatorController.layers[0].stateMachine;

        // 设置参数
        SetParam(controllerPath);

        // 创建AvatarMask
        AvatarMask avatarMask = GetAvatarMask();
        if (avatarMask == null)
        {
            return;
        }

        // 创建StateMachine
        m_stateMachine = new AnimatorStateMachine();
        m_stateMachine.name = m_faceLayerName;
        
        // 创建Layer
        AnimatorControllerLayer faceLayer = CreateLayer(avatarMask);
        int layerIndex = m_animatorController.layers.Length;
        m_animatorController.AddLayer(faceLayer);

        // 加入到Controller中
        AssetDatabase.AddObjectToAsset(m_stateMachine, m_animatorController);
        m_stateMachine.hideFlags = srcStateMachine.hideFlags;

        // 拷贝StateMachine
        FastCopyAnimatorStateMachine(srcStateMachine);
        Unsupported.PasteToStateMachineFromPasteboard(m_stateMachine, m_animatorController, layerIndex, Vector3.zero);
        CopyAnimatorStateMachine(srcStateMachine, m_stateMachine);
        
        // Transition变瞬切
        ChangeAllTransitionImmediately(m_stateMachine);
        
        // 保存
        EditorUtility.SetDirty(m_animatorController);
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        
        EditorUtility.DisplayDialog("表情分离", "分离成功", "确定");
    }
    
    #region Change Transition

    private void ChangeAllTransitionImmediately(AnimatorStateMachine stateMachine)
    {
        foreach (ChildAnimatorState childAnimatorState in stateMachine.states)
        {
            AnimatorState animatorState = childAnimatorState.state;

            foreach (AnimatorStateTransition animatorStateTransition in animatorState.transitions)
            {
                ChangeTransitionImmediately(animatorStateTransition);
            }
        }

        foreach (ChildAnimatorStateMachine childAnimatorStateMachine in stateMachine.stateMachines)
        {
            ChangeAllTransitionImmediately(childAnimatorStateMachine.stateMachine);
        }
    }

    private void ChangeTransitionImmediately(AnimatorStateTransition transition)
    {
        transition.duration = 0;
        transition.offset = 0;
        transition.exitTime = 1;
    }
    
    #endregion
    
    #region Copy StateMachine

    private void FastCopyAnimatorStateMachine(AnimatorStateMachine stateMachine)
    {
        List<Object> m_stateMachineObjects = new List<Object>();
        List<Vector3> m_objectsPos = new List<Vector3>();

        foreach (ChildAnimatorState childSrcAnimatorState in stateMachine.states)
        {
            m_stateMachineObjects.Add(childSrcAnimatorState.state);
            m_objectsPos.Add(childSrcAnimatorState.position);

            foreach (AnimatorStateTransition stateTransition in childSrcAnimatorState.state.transitions)
            {
                m_stateMachineObjects.Add(stateTransition);
                m_objectsPos.Add(Vector3.zero);
            }
        }

        foreach (ChildAnimatorStateMachine childSrcAnimatorStateMachine in stateMachine.stateMachines)
        {
            m_stateMachineObjects.Add(childSrcAnimatorStateMachine.stateMachine);
            m_objectsPos.Add(childSrcAnimatorStateMachine.position);
            
            AnimatorTransition[] stateMachineTransitions = stateMachine.GetStateMachineTransitions(childSrcAnimatorStateMachine.stateMachine);
            foreach (AnimatorTransition srcAnimatorTransition in stateMachineTransitions)
            {
                m_stateMachineObjects.Add(srcAnimatorTransition);
                m_objectsPos.Add(Vector3.zero);
            }
        }
        
        CopyStateMachineDataToPasteboard(m_stateMachineObjects.ToArray(), stateMachine, m_objectsPos.ToArray(), m_animatorController, 0);
    }
    
    private void CopyAnimatorStateMachine(AnimatorStateMachine src, AnimatorStateMachine dst)
    {
        dst.entryPosition = src.entryPosition;
        dst.anyStatePosition = src.anyStatePosition;
        dst.exitPosition = src.exitPosition;
        dst.parentStateMachinePosition = src.parentStateMachinePosition;

        AnimatorState defalutState = null;
        foreach (ChildAnimatorState state in dst.states)
        {
            if (state.state.name == src.defaultState.name)
            {
                defalutState = state.state;
                break;
            }
        }

        if(defalutState != null)
            dst.defaultState = defalutState;
    }
    
    #endregion
    
    #region Create Layer

    private AnimatorControllerLayer CreateLayer(AvatarMask avatarMask)
    {
        AnimatorControllerLayer faceLayer = new AnimatorControllerLayer();
        faceLayer.name = m_faceLayerName;
        faceLayer.defaultWeight = 1.0f;
        faceLayer.stateMachine = m_stateMachine;
        faceLayer.avatarMask = avatarMask;
        return faceLayer;
    }
    
    #endregion
    
    #region Create Mask

    private AvatarMask GetAvatarMask()
    {
        if (File.Exists(m_avatarMaskPath))
        {
            return AssetDatabase.LoadAssetAtPath<AvatarMask>(m_avatarMaskPath);
        }
        
        string avatarPath = EditorUtility.OpenFilePanel("选择模型", "", "fbx");
        if (string.IsNullOrEmpty(avatarPath))
        {
            return null;
        }

        string copyAvatarPath = CopyFBX(avatarPath);
        return CreateAvatarMask(copyAvatarPath);
    }

    private AvatarMask CreateAvatarMask(string copyAvatarPath)
    {
        ModelImporter modelImporter = AssetImporter.GetAtPath(copyAvatarPath) as ModelImporter;

        AvatarMask avatarMask = new AvatarMask();

        int count = modelImporter.transformPaths.Length;
        avatarMask.transformCount = count;
        for (int i = 0; i < count; i++)
        {
            string transformPath = modelImporter.transformPaths[i];
            avatarMask.SetTransformPath(i, transformPath);

            bool isActive = false;
            int index = transformPath.LastIndexOf('/');
            if (index > 0)
            {
                string name = transformPath.Substring(index + 1);
                if (name.StartsWith("face_"))
                {
                    isActive = true;
                }
            }
            avatarMask.SetTransformActive(i, isActive);
        }

        AssetDatabase.CreateAsset(avatarMask, m_avatarMaskPath);
        AssetDatabase.DeleteAsset(copyAvatarPath);
        AssetDatabase.Refresh();
        return avatarMask;
    }
    
    private string CopyFBX(string avatarPath)
    {
        string modelName = Path.GetFileNameWithoutExtension(avatarPath);
        string newPath = m_dir + "/" + modelName + "_Temp.fbx";
        File.Copy(avatarPath, newPath);
        AssetDatabase.ImportAsset(newPath);
        return newPath;
    }
    
    #endregion
    
    #region Set Param

    private void SetParam(string controllerPath)
    {
        m_dir = Path.GetDirectoryName(controllerPath);
        string fileName = Path.GetFileNameWithoutExtension(controllerPath);
        int index = fileName.LastIndexOf('_');
        m_modelName = fileName.Substring(0, index);
        m_avatarMaskPath = m_dir + "/" + m_modelName + "_face.mask";
    }
    
    #endregion

    private bool HasLayer(string layerName)
    {
        for (int i = 0; i < m_animatorController.layers.Length; i++)
        {
            AnimatorControllerLayer controllerLayer = m_animatorController.layers[i];
            if (controllerLayer.name == layerName)
            {
                return true;
            }
        }
        return false;
    }
}
