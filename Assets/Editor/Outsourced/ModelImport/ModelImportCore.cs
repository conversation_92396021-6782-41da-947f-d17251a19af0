using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;
using UnityEngine.Rendering;
using TATools;
using System.Reflection;
using System.Text;
using static TKTrail;
using TKFrame.Asset;
using System.Security.Cryptography;

namespace ModelImport
{
    public enum ImportMode
    {
        Fast,       // 快速导入，只导入有变更的
        Full,       // 全部导入
        ForceFull,  // 全部导入，并且还导入贴图
        OnlyMatAndTexture, // 只导入贴图和材质球
        OnlyImportSelected, // 只导入选中的
    }

    public class ModelInfo
    {
        public string targetFile;
        public string avatarPath;
        public List<string> antiRefPaths;
        public Dictionary<string, Dictionary<string, Dictionary<string, Material[]>>> resMatDict;
    }

    public static class ModelImportCore
    {
        private static Type m_tInternalMeshUtil = null;
        private static MethodInfo m_GetPrimitiveCount = null;

        [MenuItem("Tools/PrefabGenTool/模型资源/CheckAnim", false, 100)]
        public static void CheckAnim()
        {
            var folder = AssetDatabase.GetAssetPath(Selection.activeObject);
            Dictionary<string, bool> animDict = new Dictionary<string, bool>();
            StringBuilder sb = new StringBuilder();
            if (Directory.Exists(folder))
            {
                var animGuids = AssetDatabase.FindAssets("t:AnimationClip", new string[] { folder });
                foreach (var guid in animGuids)
                {
                    var animPath = AssetDatabase.GUIDToAssetPath(guid);
                    var sorceAnimPath = animPath.Replace(".anim", ".fbx").Replace("Assets/Art_TFT_Raw/model_res/hero/", "E:\\Hero\\");
                    if (!File.Exists(sorceAnimPath))
                        continue;
                    var anim = AssetDatabase.LoadAssetAtPath<AnimationClip>(animPath);
                    EditorCurveBinding[] curveBindings = AnimationUtility.GetCurveBindings(anim);
                    bool hasMeshRendererEnableCurve = false;
                    foreach (EditorCurveBinding curveBinding in curveBindings)
                    {
                        if (curveBinding.type == typeof(SkinnedMeshRenderer))
                        {
                            hasMeshRendererEnableCurve = true;
                            break;
                        }
                    }
                    string animFolderPath = animPath.Substring(0, animPath.LastIndexOf("/"));
                    if (!animDict.TryGetValue(animFolderPath, out bool hasMREC))
                    {
                        animDict.Add(animFolderPath, hasMeshRendererEnableCurve);
                    }
                    else if (hasMREC != hasMeshRendererEnableCurve)
                    {
                        sb.AppendLine(animPath);
                    }
                }
            }
            TKFrame.Diagnostic.Log(sb.ToString());
        }

        public static int GetMeshTris(Mesh mesh)
        {           
            // InternalMeshUtil.GetPrimitiveCount(mesh)
            var ass = Assembly.GetAssembly(typeof(EditorApplication));
            if (m_tInternalMeshUtil == null)
                m_tInternalMeshUtil = ass.GetType("UnityEditor.InternalMeshUtil");
            if (m_GetPrimitiveCount == null)
                m_GetPrimitiveCount = m_tInternalMeshUtil.GetMethod("GetPrimitiveCount");
            return (int)m_GetPrimitiveCount.Invoke(null, new object[] { mesh });
        }

        private static void RenameAllAsset(string rootPath)
        {
            var files = ModelImportCore.GetFiles(rootPath);
            foreach (var file in files)
            {
                var fileName = Path.GetFileNameWithoutExtension(file);
                var fileEx = Path.GetExtension(file);
                if (fileEx == ".mesh")
                    continue;
                var arr = fileName.Split('-');
                if (arr.Length != 2)
                    continue;
                var srcPath = ModelImportCore.ConvertToUnityPath(file);
                var desPath = srcPath.Replace(fileName, arr[0]);
                AssetDatabase.MoveAsset(srcPath, desPath);
            }
        }

        private static Dictionary<string, string> GetAnimationDict(string rootPath)
        {
            Dictionary<string, string> dict = new Dictionary<string, string>();
            var files = GetFiles(rootPath);
            for (int i = 0; i < files.Count; ++i)
            {
                var file = files[i];
                if (file.EndsWith(".anim"))
                {
                    string key = Path.GetFileNameWithoutExtension(file);
                    if (key.Contains("@"))
                    {
                        key = key.Split('@')[1];
                    }
                    if (dict.ContainsKey(key))
                        continue;
                    dict.Add(key, file);
                }
            }
            return dict;
        }

        public static bool AnalyzerRootPath(string path, out string modelName, out string setName, out string heroType)
        {
            modelName = string.Empty;
            setName = string.Empty;
            heroType = string.Empty;
            var rootPath = path;
            if (string.IsNullOrEmpty(rootPath) /*|| !Directory.Exists(rootPath)*/)
            {
                TKFrame.Diagnostic.Log("[AnalyzerRootPath] rootPath: {0} not exist!", rootPath);
                return false;
            }
            var arr = rootPath.Replace("\\", "/").Split('/');
            if (arr.Length < 3)
            {
                TKFrame.Diagnostic.Log("[AnalyzerRootPath] rootPath: {0} arr.Length < 3!", rootPath);
                return false;
            }
            modelName = arr[arr.Length - 1];
            var setNameTag = arr[arr.Length - 2];
            if (setNameTag.StartsWith("set"))
            {
                setName = setNameTag;
                heroType = arr[arr.Length - 3].ToLower();
            }
            else if (setNameTag == "common") // 通用英雄目录
            {
                setName = setNameTag;
                heroType = arr[arr.Length - 3].ToLower();
            }
            else if (setNameTag.StartsWith("t_")) // 小小英雄系列子目录
            {
                heroType = arr[arr.Length - 3];
                modelName = arr[arr.Length - 2] + "/" + arr[arr.Length - 1];
            }
            else if (setNameTag == "LittleLegend")      // 小小英雄系列目录
            {
                heroType = arr[arr.Length - 2];
                modelName = arr[arr.Length - 1];
            }
            else if (setNameTag == "LittleLegend_low")      // 小小英雄系列目录
            {
                heroType = arr[arr.Length - 2];
                modelName = arr[arr.Length - 1];
            }
            else
            {
                heroType = setNameTag;
            }

            // 小马觉得小队长这个名字不好听 要换成英文的
            if (heroType == "XiaoDuiZhang" || heroType == "teamleader")
                heroType = "LittleLegend";

            return true;
        }

        public static void UpdateTargetPath(List<ModelResAsset> resAssetList, string setName, string heroType, string modelName)
        {
            string resPath = ModelImportSetting.ModelResPath;
            // 后面记得删
            if (resPath.Contains("teamleader")) resPath = resPath.Replace("teamleader", "LittleLegend");
            foreach (var resAsset in resAssetList)
            {
                var asset = resAsset;
                if (heroType == "LittleLegend_low")
                {
                    // 小小英雄低模要丢到低模的特殊目录里面
                    asset.TargetPath = TKAssetGroupUtil.NormalPathToLodPath(Application.dataPath + resPath + "/LittleLegend/" + modelName + asset.RelativePath, "low");
                }
                else
                {
                    if (string.IsNullOrEmpty(setName))
                    {
                        asset.TargetPath = Application.dataPath + resPath + "/" + heroType + "/" + modelName + asset.RelativePath;
                    }
                    else
                    {
                        asset.TargetPath = Application.dataPath + resPath + "/" + heroType + "/" + setName + "/" + modelName + asset.RelativePath;
                    }
                }
                asset.TargetPath = asset.TargetPath.Replace("\\", "/");
                asset.Diff();
            }
        }

        public static List<ModelResAsset> AnalyzerRawModelPath(ModelResMd5Mgr md5Mgr, string rawModelPath)
        {
            List<ModelResAsset> modelResList = new List<ModelResAsset>();
            var files = GetFiles(rawModelPath);
            foreach (var file in files)
            {
                ModelResAsset modelRes = new ModelResAsset(md5Mgr, file, rawModelPath);
                if (modelRes.AssetType != ModelAssetType.None)
                {
                    modelResList.Add(modelRes);
                }
            }
            return modelResList;
        }

        public static bool ImportAllAsset(List<ModelResAsset> resAssetList, ImportMode importMode, out HashSet<string> successAssets, bool isBatchMode)
        {
            Import_Error_Info = string.Empty;
            successAssets = new HashSet<string>();
            bool hasError = false;
            try
            {
                List<ModelResAsset> modelAssets = new List<ModelResAsset>();
                List<ModelResAsset> matAssets = new List<ModelResAsset>();

                List<ModelResAsset> modelPaths = new List<ModelResAsset>();
                List<ModelResAsset> animPaths = new List<ModelResAsset>();
                HashSet<string> assetPaths = new HashSet<string>();
                int i = 0;
                float count = resAssetList.Count;
                foreach (var resAsset in resAssetList)
                {
                    var modeResAsset = resAsset;
                    if (!modeResAsset.ImportEnable)
                        continue;

                    string targetPath = modeResAsset.TargetPath;
                    targetPath = targetPath.Substring(targetPath.IndexOf("/Assets/") + 1);
                    if (!assetPaths.Contains(targetPath))
                        assetPaths.Add(targetPath);

                    if (modeResAsset.AssetType == ModelAssetType.Model)
                    {
                        if (modeResAsset.resSetting.SingleBoneOptimize)
                            modelPaths.Add(modeResAsset);
                        modelAssets.Add(modeResAsset);
                        continue;
                    }
                    if (modeResAsset.AssetType == ModelAssetType.Material)
                    {
                        matAssets.Add(modeResAsset);
                        continue;
                    }
                    if (modeResAsset.AssetType == ModelAssetType.Animation)
                    {
                        if (modeResAsset.resSetting.AnimationVisibleOptimize)
                            animPaths.Add(modeResAsset);
                    }
                    if (!isBatchMode)
                        EditorUtility.DisplayProgressBar("导入中", modeResAsset.AssetName, i++ / count);
                    ModelImportCore.ImportAsset(modeResAsset, importMode, isBatchMode);
                    modeResAsset.Diff();
                }

                foreach (var matAsset in matAssets)
                {
                    if (!isBatchMode)
                        EditorUtility.DisplayProgressBar("导入中", matAsset.AssetName, i++ / count);
                    ModelImportCore.ImportAsset(matAsset, importMode, isBatchMode);
                    matAsset.Diff();
                }

                var modelInfo = ModelImportCore.PreProssModel(modelPaths);

                foreach (var modelAsset in modelAssets)
                {
                    if (!isBatchMode)
                        EditorUtility.DisplayProgressBar("导入中", modelAsset.AssetName, i++ / count);
                    ModelImportCore.ImportAsset(modelAsset, importMode, isBatchMode);
                    modelAsset.Diff();
                }

                // 修复各个资源引用关系
                //FixRef();

                // 后处理，处理一下模型和动画 
                // 模型优化单骨骼模型，从SMR优化为MR
                // 动画主要优化表情和道具，在Scale接近0的时候，直接隐藏Renderer
                ModelImportCore.PostProcess(modelPaths, animPaths, modelInfo);


                foreach (var item in modelInfo)
                {
                    foreach (var matDict in item.Value.resMatDict)
                    {
                        assetPaths.Add(matDict.Key);
                    }
                }

                successAssets = assetPaths;
            }
            catch (Exception ex)
            {
                Debug.LogError("有报错: " + ex.ToString());
                AddErrorInfo(ex.ToString());
                hasError = true;
            }
            finally
            {
                EditorUtility.ClearProgressBar();
                AssetDatabase.Refresh();
                AssetDatabase.SaveAssets();
            }

            return hasError || !string.IsNullOrEmpty(Import_Error_Info);
        }

        public static Dictionary<string, ModelInfo> PreProssModel(List<ModelResAsset> modelPaths)
        {
            AssetDatabase.Refresh();
            // 先找到这个模型现有的材质关系 记录下来
            Dictionary<string, ModelInfo> modelInfos = new Dictionary<string, ModelInfo>();
            if (modelPaths != null)
            {
                int processModelCount = 0;
                for (int i = 0; i < modelPaths.Count; ++i)
                {
                    var targetFile = modelPaths[i].GetTargetFile();
                    if (File.Exists(targetFile))
                    {
                        targetFile = targetFile.Substring(targetFile.IndexOf("/Assets/") + 1);
                        var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(targetFile);
                        if (prefab != null && ModelOptimizeTool.HasSingleBoneMesh(prefab))
                        {

                            EditorUtility.DisplayProgressBar(string.Format("预处理 {0}/{1}", i + 1, modelPaths.Count), Path.GetFileName(targetFile), (i + 1) / (float)modelPaths.Count);

                            var animator = prefab.GetComponent<Animator>();
                            if (animator == null || animator.avatar == null)
                                continue;
                            // 先找到所有引用这个模型的预制体，记录下来他们的材质和模型的关系
                            var avater = animator.avatar;
                            var antiRefPaths = ModelOptimizeTool.CollectAllRefModel(targetFile, avater, out Dictionary<string, Dictionary<string, Dictionary<string, Material[]>>> resMatDict);
                            if (antiRefPaths != null)
                            {
                                ModelInfo modelInfo = new ModelInfo();
                                modelInfo.targetFile = targetFile;
                                modelInfo.avatarPath = AssetDatabase.GetAssetPath(avater);
                                modelInfo.antiRefPaths = antiRefPaths;
                                modelInfo.resMatDict = resMatDict;
                                modelInfos.Add(targetFile, modelInfo);

                                ++processModelCount;
                            }
                            else
                            {

                            }
                        }
                    }
                }

                TKFrame.Diagnostic.Log("PreProcess model count: " + processModelCount);
            }
            EditorUtility.ClearProgressBar();
            return modelInfos;
        }

        public static void PostProcess(List<ModelResAsset> modelPaths, List<ModelResAsset> animPaths, Dictionary<string, ModelInfo> modelInfoDict)
        {
            AssetDatabase.Refresh();

            // 1. 模型优化单骨骼模型，从SMR优化为MR
            if (modelPaths != null && modelInfoDict != null)
            {
                int processModelCount = 0;
                for (int i = 0; i < modelPaths.Count; ++i)
                {
                    var targetFile = modelPaths[i].GetTargetFile();
                    if (File.Exists(targetFile))
                    {
                        targetFile = targetFile.Substring(targetFile.IndexOf("/Assets/") + 1);
                        var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(targetFile);
                        if (prefab != null && ModelOptimizeTool.HasSingleBoneMesh(prefab))
                        {
                            EditorUtility.DisplayProgressBar(string.Format("优化模型中 {0}/{1}", i + 1, modelPaths.Count), Path.GetFileName(targetFile), (i + 1) / (float)modelPaths.Count);

                            var animator = prefab.GetComponent<Animator>();
                            if (animator == null || animator.avatar == null)
                                continue;
                            // 先找到所有引用这个模型的预制体，记录下来他们的材质和模型的关系
                            var avatar = animator.avatar;
                            var avatatPath = AssetDatabase.GetAssetPath(avatar);
                            if (modelInfoDict.TryGetValue(targetFile, out ModelInfo modelInfo))
                            {
                                if (avatatPath == modelInfo.avatarPath)
                                {
                                    prefab = GameObject.Instantiate(prefab);
                                    // 将单骨骼模型从SMR转换为MR
                                    ModelOptimizeTool.OptimizeSingleBoneMesh(prefab, modelPaths[i].resSetting.SingleBoneOptimizePreskinned);
                                    // 保存
                                    PrefabUtility.SaveAsPrefabAsset(prefab, targetFile);
                                    GameObject.DestroyImmediate(prefab);

                                    // 重新将原来的SMR上的材质赋值到MR上
                                    ModelOptimizeTool.FixMaterialRef(avatar, modelInfo.resMatDict);

                                    // 修复一下mesh模型的相对位置
                                    ModelOptimizeTool.FixMeshRendererPos(modelInfo.antiRefPaths);

                                    ++processModelCount;
                                }
                            }
                            else
                            {
                                // 说明是首次导入
                                prefab = GameObject.Instantiate(prefab);
                                // 将单骨骼模型从SMR转换为MR
                                ModelOptimizeTool.OptimizeSingleBoneMesh(prefab, modelPaths[i].resSetting.SingleBoneOptimizePreskinned);
                                // 保存
                                PrefabUtility.SaveAsPrefabAsset(prefab, targetFile);
                                GameObject.DestroyImmediate(prefab);

                                ++processModelCount;
                            }
                        }
                    }
                }

                TKFrame.Diagnostic.Log("PostProcess model count: " + processModelCount);
            }
            EditorUtility.ClearProgressBar();

            // 2. 动画优化 模型如果Scale = 0的，节点直接隐藏掉
            if (animPaths != null)
            {
                int processAnimCount = 0;
                for (int i = 0; i < animPaths.Count; ++i)
                {
                    var targetFile = animPaths[i].GetTargetFile();
                    if (File.Exists(targetFile))
                    {
                        targetFile = targetFile.Substring(targetFile.IndexOf("/Assets/") + 1);
                        var spitArr = targetFile.Split("@");
                        if (spitArr.Length != 2)
                            continue;
                        var prefabPath = spitArr[0] + ".prefab";
                        if (!File.Exists(prefabPath)) // 三星的找不到，去找找1星的，有些小小英雄就只有一星的
                        {
                            prefabPath = prefabPath.Replace("3.prefab", "1.prefab").Replace("/Lv3/", "/Lv1/");
                        }
                        if (File.Exists(prefabPath))
                        {
                            var modelGo = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                            var animationClip = AssetDatabase.LoadAssetAtPath<AnimationClip>(targetFile);
                            if (modelGo != null && animationClip != null)
                            {
                                EditorUtility.DisplayProgressBar(string.Format("优化动作中 {0}/{1}", i + 1, animPaths.Count), Path.GetFileName(targetFile), (i + 1) / (float)animPaths.Count);

                                if (ModelOptimizeTool.OptimizeScale0Curve(modelGo, animationClip))
                                {
                                    // 节约磁盘空间
                                    ModelImportCore.ClearEditorCurves(animationClip);

                                    ++processAnimCount;
                                }
                            }
                        }
                    }
                }

                TKFrame.Diagnostic.Log("PostProcess anim count: " + processAnimCount);
            }
            EditorUtility.ClearProgressBar();
        }


        public static List<string> GetFiles(string path)
        {
            List<string> files = new List<string>();
            GetFiles(path, ref files);
            return files;
        }

        private static void GetFiles(string path, ref List<string> files)
        {
            files.AddRange(Directory.GetFiles(path));

            var directorys = Directory.GetDirectories(path);
            foreach (var directory in directorys)
            {
                GetFiles(directory, ref files);
            }
        }

        public static void ImportAsset(ModelResAsset asset, ImportMode importMode, bool isBatchMode)
        {
#if !OUTSOURCE
            ZGame.Editor.ModelPostProcessor.enable = false;
#endif
            switch (asset.AssetType)
            {
                case ModelAssetType.None:
                    break;
                case ModelAssetType.Model:
                    ImportModel(asset, importMode, isBatchMode);
                    break;
                case ModelAssetType.Animation:
                    ImportAnimation(asset, importMode);
                    break;
                case ModelAssetType.Material:
                    ImportMaterial(asset, importMode);
                    break;
                case ModelAssetType.Texture:
                    ImportTexture(asset, importMode);
                    break;
                default:
                    break;
            }
            asset.RefershMd5();
#if !OUTSOURCE
            ZGame.Editor.ModelPostProcessor.enable = true;
#endif
        }

        public static string ConvertToUnityPath(string absolutePath)
        {
            return absolutePath.Replace("\\", "/").Replace(Application.dataPath, "Assets").Replace("//", "/");
        }

        private static bool CheckModel(string assetName, Object[] modelAssets, out string errorStr)
        {
            List<int> meshTrisList = new List<int>()
            {
                0, 0, 0
            };
            List<bool> meshTrisOpenList = new List<bool>()
            {
                false, true, false
            };
            ModelImportLimitConfig.AssetType assetType = ModelImportLimitConfig.AssetType.英雄;
            if (assetName.StartsWith("t_"))
            {
                if (assetName.EndsWith("_h"))
                    assetType = ModelImportLimitConfig.AssetType.小小英雄_局外;
                else
                    assetType = ModelImportLimitConfig.AssetType.小小英雄_局内;
            }
            List<string> lodNameErrorList = new List<string>();
            for (int i = 0; i < modelAssets.Length; ++i)
            {
                var modelAsset = modelAssets[i];
                if (modelAsset is Mesh)
                {
                    string meshName = modelAsset.name;
                    int meshTris = GetMeshTris(modelAsset as Mesh);
                    if (meshName.Contains("lod_h"))
                    {
                        meshTrisList[(int)ModelImportLimitConfig.AssetLod.高模] += meshTris;
                    }
                    else if (meshName.Contains("lod_l"))
                    {
                        meshTrisOpenList[(int)ModelImportLimitConfig.AssetLod.低模] = true;
                        meshTrisList[(int)ModelImportLimitConfig.AssetLod.低模] += meshTris;
                    }
                    else if (meshName.Contains("lod_s"))
                    {
                        meshTrisOpenList[(int)ModelImportLimitConfig.AssetLod.Pad] = true;
                        meshTrisList[(int)ModelImportLimitConfig.AssetLod.Pad] += meshTris;
                    }
                    else
                    {
                        meshTrisList[(int)ModelImportLimitConfig.AssetLod.高模] += meshTris;
                        meshTrisList[(int)ModelImportLimitConfig.AssetLod.低模] += meshTris;
                        meshTrisList[(int)ModelImportLimitConfig.AssetLod.Pad] += meshTris;
                    }
                }
                else if (modelAsset is GameObject)
                {
                    if (assetType == ModelImportLimitConfig.AssetType.英雄)
                    {
                        var go = modelAsset as GameObject;
                        var rs = go.GetComponentsInChildren<Renderer>(true);
                        foreach (var r in rs)
                        {
                            if (r.name.Contains("lod_s") || r.name.Contains("lod_h") || r.name.Contains("lod_l"))
                                continue;
                            else
                                lodNameErrorList.Add(r.name);
                        }
                    }
                }
            }

            bool isTrisVaild = true;
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("模型名字： {0}, \n模型类型: {1}\n", assetName, assetType);
            for (int i = 0; i < meshTrisList.Count; ++i)
            {
                if (!meshTrisOpenList[i])
                    continue;

                var meshTris = meshTrisList[i];
                ModelImportLimitConfig.AssetLod lod = (ModelImportLimitConfig.AssetLod)i;

                int maxTris = ModelImportLimitConfig.GetMaxTris(assetName, assetType, lod);
                if (maxTris > 0)
                {
                    if (meshTris > maxTris)
                    {
                        isTrisVaild = false;
                        sb.AppendFormat("lod: {0} 面数: {1} 最高限制面数: {2}\n", lod, meshTris, maxTris);
                    }
                }
            }
            if (!isTrisVaild)
                sb.AppendLine("面数过多！无法导入！请让美术减面获得让TA加入白名单！");
            if (lodNameErrorList.Count != 0)
            {
                sb.AppendLine("LOD节点名字错误：" + string.Join(",", lodNameErrorList));
                sb.AppendLine("请让美术修改模型的节点名字！");
            }
            bool vaild = isTrisVaild && lodNameErrorList.Count == 0;
            if (!vaild)
                errorStr = sb.ToString();
            else
                errorStr = string.Empty;
            return vaild;
        }

        public static string Import_Error_Info = string.Empty;

        private static void AddErrorInfo(string error)
        {
            TKFrame.Diagnostic.Log("[AddErrorInfo] " + error);
            if (!string.IsNullOrEmpty(Import_Error_Info))
                Import_Error_Info += "\n";
            Import_Error_Info += error;
        }

        //public const string TempPath = "/temp/";
        public const string TempPreName = "temp_";
        public const string CfgDir = "Assets/Art_TFT_Raw/cfg/team_leader_cfg";
        private static List<string> m_willDeleteList = new List<string>();
        public static void ImportModel(ModelResAsset asset, ImportMode importMode, bool isBatchMode)
        {
            if (importMode == ImportMode.OnlyMatAndTexture)
                return;
            if (!asset.HasChanged && importMode == ImportMode.Fast)
                return;

            if (!Directory.Exists(asset.TargetPath))
                Directory.CreateDirectory(asset.TargetPath);

            var tempModelPath = asset.TargetPath + "/" + TempPreName + asset.AssetName + asset.AssetExtension;
            File.Copy(asset.RawPath + "/" + asset.AssetName + asset.AssetExtension, tempModelPath, true);
            var projectTempModelPath = ConvertToUnityPath(tempModelPath);
            AssetDatabase.ImportAsset(projectTempModelPath);
            ModelImportRule.ImportModel(projectTempModelPath, asset.resSetting, false, true);

            try
            {
                // 复制模型里面的资源
                string avatarPath = "";
                Dictionary<string, string> meshDict = new Dictionary<string, string>();
                var modelAssets = AssetDatabase.LoadAllAssetsAtPath(projectTempModelPath);
                var vaild = CheckModel(asset.AssetName, modelAssets, out string errorStr);
                if (!vaild)
                {
                    AssetDatabase.DeleteAsset(projectTempModelPath);
                    AssetDatabase.Refresh();
                    AssetDatabase.SaveAssets();

                    StringBuilder sb = new StringBuilder();
                    sb.Append(errorStr);

                    if (isBatchMode)
                    {
                        AddErrorInfo(sb.ToString());
                        return;
                    }
                    else
                    {
                        if (EditorUtility.DisplayDialog("错误", sb.ToString(), "OK"))
                        {
                            return;
                        }
                    }
                }

                for (int i = 0; i < modelAssets.Length; ++i)
                {
                    var modelAsset = modelAssets[i];
                    if (modelAsset is Mesh)
                    {
                        //MeshUtility.Optimize(modelAsset as Mesh);
                        var targetPath = CreateModelAssetMesh(asset.AssetName, asset.AssetExtension, asset.TargetPath, modelAsset);

                        meshDict.Add(modelAsset.name, targetPath);
                    }
                    else if (modelAsset is Avatar)
                    {
                        string targetPath = ConvertToUnityPath(asset.TargetPath + "/" + asset.AssetName + ".asset");
                        SafeSaveAsset(targetPath, modelAsset);

                        avatarPath = targetPath;
                    }
                }

                CreateModelPrefab(asset, projectTempModelPath, avatarPath, meshDict);

                // 设置AB 小小英雄走依赖打包 不在这里设置ab
                if (!asset.TargetPath.Contains("LittleLegend"))
                {
                    string targetPath = asset.TargetPath.Substring(0, asset.TargetPath.LastIndexOf('/'));
                    var path = ConvertToUnityPath(targetPath);
                    AssetImporter importer = AssetImporter.GetAtPath(path);
                    SetAssetBundleName(importer);
                    Debug.Log("SetAssetbundle: " + path);
                    AssetDatabase.WriteImportSettingsIfDirty(path);

                    ClearChildAssetbundle(path);
                }
            }
            catch(Exception ex)
            {
                Debug.LogError("导入模型失败:" + ex.ToString());
                AddErrorInfo("导入模型" + asset.AssetName + "失败, 异常详情: " + ex.ToString());
            }
            finally
            {
                AssetDatabase.DeleteAsset(projectTempModelPath);
                AssetDatabase.Refresh();
                AssetDatabase.SaveAssets();

                for (int i = 0; i < m_willDeleteList.Count; ++i)
                {
                    AssetDatabase.DeleteAsset(m_willDeleteList[i]);
                }
            }
        }

        public static void SafeSaveAsset(string assetPath, UnityEngine.Object asset, bool needInstance = true)
        {
            bool fileExist = File.Exists(assetPath);
            if (fileExist && assetPath.EndsWith(".mesh"))
            {
                var ins = needInstance ? Object.Instantiate(asset) : asset;
                ins.name += TempPreName;
                var tmpPath = assetPath.Replace(".mesh", TempPreName + ".mesh");
                AssetDatabase.CreateAsset(ins, tmpPath);
                File.Delete(assetPath);
                //AssetDatabase.Refresh();
                File.Copy(tmpPath, assetPath, true);
                //AssetDatabase.DeleteAsset(tmpPath);
                m_willDeleteList.Add(tmpPath);
                AssetDatabase.Refresh();
                Debug.Log("导入mesh：" + asset.name + " 完成");
            }
            else if (fileExist)
            {
                var savedAsset = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(assetPath);
                if (savedAsset != null)
                {
                    EditorUtility.CopySerialized(asset, savedAsset);
                    AssetDatabase.SaveAssets();
                }
                else
                {
                    var ins = needInstance ? Object.Instantiate(asset) : asset;
                    AssetDatabase.CreateAsset(ins, assetPath);
                }
            }
            else
            {
                var ins = needInstance ? Object.Instantiate(asset) : asset;
                AssetDatabase.CreateAsset(ins, assetPath);
            }

            var newAsset = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(assetPath);
            newAsset.name = "";
            newAsset.name = Path.GetFileNameWithoutExtension(assetPath);
        }

        private static void CreateModelPrefab(ModelResAsset asset, string projectTempModelPath, string avatarPath, Dictionary<string, string> meshDict)
        {
            bool isLittleLegend = false;
            List<string> oldModelBoneList = new List<string>();
            // 复制模型的prefab出来 要他的架子
            var modelTargetPath = ConvertToUnityPath(asset.TargetPath + "/" + asset.AssetName + ".prefab");
            if (modelTargetPath.Contains("LittleLegend"))
            {
                isLittleLegend = true;
            }
            //要记录原有模型的骨骼数据，因为有些是后期动态添加上去的;
            if (isLittleLegend)
            {
                GameObject oldModelGo = AssetDatabase.LoadAssetAtPath<GameObject>(modelTargetPath);
                if (oldModelGo != null)
                {
                    for (int i = 0, len = oldModelGo.transform.childCount; i < len; i++)
                    {
                        oldModelBoneList.Add(oldModelGo.transform.GetChild(i).name);
                    }
                }
            }
            var modelGo = AssetDatabase.LoadAssetAtPath<GameObject>(projectTempModelPath);
            GameObject tempObj = GameObject.Instantiate(modelGo);

            // 删除模型里面的相机组件
            var cameras = tempObj.GetComponentsInChildren<Camera>(true);
            foreach (var camera in cameras)
            {
                camera.enabled = false;
                GameObject.DestroyImmediate(camera);
            }

            var aminator = tempObj.GetComponent<Animator>();
            if (aminator != null)
            {
                aminator.avatar = AssetDatabase.LoadAssetAtPath<Avatar>(avatarPath);
            }

            var skinMeshs = tempObj.GetComponentsInChildren<SkinnedMeshRenderer>(true);
            foreach (var skinMesh in skinMeshs)
            {
                if (meshDict.TryGetValue(skinMesh.name, out string path))
                {
                    skinMesh.sharedMesh = AssetDatabase.LoadAssetAtPath<Mesh>(path);
                }

                // TODO 生成小小英雄时，检查Materials文件夹下所有材质使用的顶点通道，并据此设置NecessaryChannels
                if (isLittleLegend)
                {
#if !OUTSOURCE
                    skinMesh.necessaryChannels |= (1 << (int)VertexAttribute.Tangent);
#endif
                }

                skinMesh.skinnedMotionVectors = false;
            }

            var meshFilters = tempObj.GetComponentsInChildren<MeshFilter>(true);
            foreach (var mesh in meshFilters)
            {
                if (meshDict.TryGetValue(mesh.name, out string path))
                {
                    mesh.mesh = AssetDatabase.LoadAssetAtPath<Mesh>(path);
                }
            }

            RefershModelAnimator(tempObj, asset.TargetPath, asset.AssetName);
            RefershModelMaterial(tempObj, asset.TargetPath, asset.AssetName);
            if (isLittleLegend)
            {
                for (int i = 0, len = tempObj.transform.childCount; i < len; i++)
                {
                    string childName = tempObj.transform.GetChild(i).name;
                    oldModelBoneList.Remove(childName);
                }

                for (int i = 0, len = oldModelBoneList.Count; i < len; i++)
                {
                    GameObject boneGo = new GameObject(oldModelBoneList[i]);
                    boneGo.transform.localPosition = Vector3.zero;
                    boneGo.transform.localEulerAngles = Vector3.zero;
                    boneGo.transform.localScale = Vector3.one;
                    boneGo.transform.SetParent(tempObj.transform);
                }
            }

            PrefabUtility.SaveAsPrefabAsset(tempObj, modelTargetPath);
            GameObject.DestroyImmediate(tempObj);

            // 复制到真正的目录里面
            //File.Copy(modelTempPath, modelTargetPath, true);
            //AssetDatabase.ImportAsset(modelTargetPath);
            //AssetDatabase.Refresh();

            #region 环境光遮蔽bake
            // 局外高模才起效果
            if (asset.AssetName.EndsWith("_h"))
            {
                bool meshOcclusion = asset.resSetting != null && asset.resSetting.MeshOcclusion;
                var needBake = ModelImportBakerConfig.NeedBake(modelTargetPath, asset.AssetName);
                if (needBake || meshOcclusion)
                {
                    Debug.Log("[ModelImportBakerConfig]Bake Model " + modelTargetPath + " Start");
                    bool success = true;
                    try
                    {
                        MeshOcclusionBakeEditor.BacthBakeMeshs(new GameObject[1] { AssetDatabase.LoadAssetAtPath<GameObject>(modelTargetPath) });
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError(ex.ToString());
                        success = false;
                    }

                    if (!success)
                    {
                        EditorUtility.DisplayDialog("错误", "环境光遮蔽bake出现错误，请通知TA fengxzeng", "好的");
                    }

                    Debug.Log("[ModelImportBakerConfig]Bake Model " + modelTargetPath + " Finished success: " + success);
                }
            }
            #endregion


            #region 检测对依赖当前小小英雄的show进行挂点重置;
            if (isLittleLegend)
            {
                GameObject tmpModelGo = PrefabUtility.LoadPrefabContents(modelTargetPath);
                #region 因为部分小小英雄会加上一SpringManager,这个组件不能对骨骼进行优化,这里要将多余的骨骼给删掉，并将依赖于这个模型的show挂点进行重新取值赋值;
                //前提:已确认一个骨骼的名字在模型里是唯一的;
                List<Transform> childArrAtRootLayer1 = new List<Transform>();
                for (int i = 0, len = tmpModelGo.transform.childCount; i < len; i++)
                {
                    childArrAtRootLayer1.Add(tmpModelGo.transform.GetChild(i));
                }
                bool needRefreshShowChp = false;
                for (int i = childArrAtRootLayer1.Count - 1; i >= 0; i--)
                {
                    Transform tmpTran = childArrAtRootLayer1[i];
                    List<GameObject> sameNameGoList = new List<GameObject>();
#if !LOGIC_THREAD && !ENABLE_TYPE_TREE_IGNORE
                    EditorUtil.FindChildDeepList(tmpModelGo, tmpTran.name, sameNameGoList);
#endif
                    if (sameNameGoList.Count > 1)
                    {
                        for (int k = 0, klen = sameNameGoList.Count; k < klen; k++)
                        {
                            //如果是在跟节点下的就直接删掉;
                            if (sameNameGoList[k].transform.parent.Equals(tmpModelGo.transform))
                            {
                                GameObject.DestroyImmediate(sameNameGoList[k]);
                                needRefreshShowChp = true;
                            }
                        }
                    }
                }

                if (needRefreshShowChp)
                {
                    //保存修改;
                    PrefabUtility.SaveAsPrefabAsset(tmpModelGo, modelTargetPath);
                    //刷新引用收集，为了获取反向依赖的数据, 小小英雄不同的物体可能采用相同的模型，没有具体的命名对应规则;
                    ReferenceFinder.data.CollectAssets(true);
                    string modelGoGUI = AssetDatabase.AssetPathToGUID(modelTargetPath);
                    if (ReferenceFinder.data.antiDependenciesNonCached.TryGetValue(modelGoGUI, out HashSet<string> tmpSet) && tmpSet.Count > 0)
                    {
                        List<string> chpPathList = new List<string>();
                        foreach (var tmp in tmpSet)
                        {
                            //如果反向依赖没有了
                            CharacterHangPoint showChp = AssetDatabase.LoadAssetAtPath<CharacterHangPoint>(AssetDatabase.GUIDToAssetPath(tmp));
                            if (showChp != null)
                            {
                                chpPathList.Add(AssetDatabase.GetAssetPath(showChp.gameObject));
                            }
                        }
                        //过滤掉类似_show2依赖于_show3的情况;
                        List<string> dealChpPathList = new List<string>();
                        for (int i = 0, len = chpPathList.Count; i < len; i++)
                        {
                            bool isCanAdded = true;
                            string[] dependPathArr = AssetDatabase.GetDependencies(chpPathList[i]);
                            for (int k = 0, klen = chpPathList.Count; k < klen; k++)
                            {
                                if (chpPathList[k] != chpPathList[i] && dependPathArr.Contains(chpPathList[k]))
                                {
                                    isCanAdded = false;
                                }
                            }

                            if (isCanAdded)
                            {
                                dealChpPathList.Add(chpPathList[i]);
                            }
                        }

                        for (int i = 0, len = dealChpPathList.Count; i < len; i++)
                        {
                            CharacterHangPoint chp = AssetDatabase.LoadAssetAtPath<CharacterHangPoint>(dealChpPathList[i]);
                            if (chp != null)
                            {
#if !LOGIC_THREAD && !ENABLE_TYPE_TREE_IGNORE
                                CharacterHangPointEditor.AdditiveCreateBones(chp, true);
#endif
                            }
                        }
                    }
                }
                PrefabUtility.UnloadPrefabContents(tmpModelGo);

                #endregion              
            }
            #endregion
        }

        public static string CreateModelAssetMesh(string assetName, string assetExtension, string targetPath, Object modelAsset)
        {
            targetPath = ConvertToUnityPath(targetPath) + "/" + assetName + "-" + modelAsset.name + ".mesh";
            SafeSaveAsset(targetPath, modelAsset);
            return targetPath;
        }

        public static void RefershModelAnimator(GameObject template, string path, string modelName)
        {

        }

        public static void RefershModelMaterial(GameObject template, string path, string modelName)
        {
            // 避免引入standard资源
            var renderers = template.GetComponentsInChildren<Renderer>(true);
            foreach (var renderer in renderers)
            {
                var materials = new Material[renderer.sharedMaterials.Length];
                for (int i = 0; i < materials.Length; ++i)
                    materials[i] = AssetDatabase.LoadAssetAtPath<Material>("Assets/Art_TFT_Raw/model_res/Model_Default_Mat.mat");
                renderer.sharedMaterials = materials;

                EditorUtility.SetDirty(renderer);
            }
        }

        private static ModelImportTeamLeaderTool m_teamLeaderTool = new ModelImportTeamLeaderTool();
        public static void ImportAnimation(ModelResAsset asset, ImportMode importMode)
        {
            if (importMode == ImportMode.OnlyMatAndTexture)
                return;
            if (!asset.HasChanged && importMode == ImportMode.Fast)
                return;

            if (!Directory.Exists(asset.TargetPath))
                Directory.CreateDirectory(asset.TargetPath);

            //var tempPath = asset.TargetPath + TempPath;
            //if (!Directory.Exists(tempPath))
            //    Directory.CreateDirectory(tempPath);
            //AssetDatabase.ImportAsset(ConvertToUnityPath(tempPath), ImportAssetOptions.DontDownloadFromCacheServer);

            var tempModelPath = asset.TargetPath + "/" + TempPreName + asset.AssetName + asset.AssetExtension;
            File.Copy(asset.RawPath + "/" + asset.AssetName + asset.AssetExtension, tempModelPath, true);
            var projectTempModelPath = ConvertToUnityPath(tempModelPath);
            //FileUtil.CopyFileOrDirectory();
            AssetDatabase.Refresh();
            //AssetDatabase.ImportAsset(projectTempModelPath);
            // 相机曲线需要100%精准 不能压缩
            bool compress = ModelImportRule.ImportModel(projectTempModelPath, asset.resSetting, projectTempModelPath.Contains("enter_UI"), true);
            Dictionary<string, AnimationCurve> camCurves = new Dictionary<string, AnimationCurve>();
           /* if (!compress)
            {
                // 记录一下相机曲线 这个东西不能压缩
                var clip = AssetDatabase.LoadAssetAtPath<AnimationClip>(projectTempModelPath);
                EditorCurveBinding[] curveBindings = AnimationUtility.GetCurveBindings(clip);
                foreach (EditorCurveBinding curveBinding in curveBindings)
                {
                    string curveName = RuleToolUtil.GetCurveName(curveBinding);
                    if (curveName.StartsWith("Cam_loc") || curveName.StartsWith("Cam_Fov"))
                    {
                        camCurves.Add(curveBinding.path + curveBinding.propertyName, AnimationUtility.GetEditorCurve(clip, curveBinding));
                    }
                }
                ModelImportRule.ImportModel(projectTempModelPath, asset.resSetting, false, true);
            }*/

            try
            {
                // 复制模型里面的资源
                var modelAssets = AssetDatabase.LoadAllAssetsAtPath(projectTempModelPath);
                bool findAction = false;
                for (int i = 0; i < modelAssets.Length; ++i)
                {
                    var modelAsset = modelAssets[i];
                    if (modelAsset is AnimationClip
                        && asset.AssetName.EndsWith(modelAsset.name))
                    {
                        var targetPath = ConvertToUnityPath(asset.TargetPath + "/" + asset.AssetName + asset.AssetExtension).Replace(asset.AssetExtension, ".anim");
                        var clipIns = Object.Instantiate(modelAsset) as AnimationClip;
                        // 记录loop值
                        var oldIns = AssetDatabase.LoadAssetAtPath<AnimationClip>(targetPath);
                        if (oldIns != null)
                        {
                            var setting = AnimationUtility.GetAnimationClipSettings(clipIns);
                            setting.loopTime = oldIns.isLooping;
                            AnimationUtility.SetAnimationClipSettings(clipIns, setting);
                        }

                        //  把相机曲线给还原回去
                        if (camCurves.Count > 0)
                        {
                            EditorCurveBinding[] curveBindings = AnimationUtility.GetCurveBindings(clipIns);
                            foreach (EditorCurveBinding curveBinding in curveBindings)
                            {
                                string key = curveBinding.path + curveBinding.propertyName;
                                if (camCurves.TryGetValue(key, out var curve))
                                {
                                    AnimationUtility.SetEditorCurve(clipIns, curveBinding, curve);
                                }
                            }
                        }
                        
                        m_teamLeaderTool.Execute(asset, clipIns);

                        // 节约磁盘空间
                        ClearEditorCurves(clipIns);

                        SafeSaveAsset(targetPath, clipIns, false);

                        findAction = true;
                    }
                }

                if (!findAction)
                {
                    throw new Exception("导入失败，模型文件[" + asset.AssetName + "]中找不到任何动作！");
                }
            }
            catch(Exception ex)
            {
                Debug.LogError("导入动画失败：" + ex.ToString());
            }
            finally
            {
                // 清理临时目录
                AssetDatabase.DeleteAsset(projectTempModelPath);
            }

            //Directory.Delete(tempPath, true);
            //File.Delete(tempPath.Remove(tempPath.LastIndexOf("/")) + ".meta");
            //AssetDatabase.Refresh();
        }

        public static void ClearEditorCurves(AnimationClip anim)
        {
            var o = new SerializedObject(anim);
            var editorCurves = o.FindProperty("m_EditorCurves");
            if (editorCurves != null)
                editorCurves.ClearArray();

            var eulerEditorCurves = o.FindProperty("m_EulerEditorCurves");
            if (eulerEditorCurves != null)
                eulerEditorCurves.ClearArray();

            o.ApplyModifiedProperties();
        }

        private static bool IsRemoveScaleCurve(string anim, string propertyName)
        {
            if (propertyName.Contains("scale"))
            {
                if (anim.StartsWith("face_") || anim.StartsWith("eye_") || anim.StartsWith("mouth_") || anim.StartsWith("t_face") || anim.StartsWith("t_eye") || anim.StartsWith("t_mouth"))
                    return false;
                return true;
            }
            return false;
        }

        // 干掉scale曲线 以后这里可以加一个白名单
        public static int OptmizeAnimationScaleCurve(AnimationClip clip)
        {
            int scaleCount = 0;
            if (clip != null)
            {
                EditorCurveBinding[] curveBindings = AnimationUtility.GetCurveBindings(clip);
                AnimationClipCurveData[] curves = new AnimationClipCurveData[curveBindings.Length];
                for (int index = 0; index < curves.Length; ++index)
                {
                    curves[index] = new AnimationClipCurveData(curveBindings[index]);
                    string name = curveBindings[index].propertyName.ToLower();

                    int index1 = TeamLeaderCfgChange.GetLastIndex(curveBindings[index].path, '/', 1);
                    string lastAnim = curveBindings[index].path.Substring(index1 + 1);

                    if (IsRemoveScaleCurve(lastAnim, name))
                    {
                        AnimationUtility.SetEditorCurve(clip, curveBindings[index], null);
                        //curves[index].curve = null;
                        ++scaleCount;
                    }
                    else
                    {
                        curves[index].curve = AnimationUtility.GetEditorCurve(clip, curveBindings[index]);
                    }
                }
                foreach (AnimationClipCurveData curveDate in curves)
                {
                    if (curveDate.curve != null)
                        clip.SetCurve(curveDate.path, curveDate.type, curveDate.propertyName, curveDate.curve);
                }

                //去除scale曲线
                //foreach (EditorCurveBinding theCurveBinding in AnimationUtility.GetCurveBindings(clip))
                //{
                //    string name = theCurveBinding.propertyName.ToLower();
                //    if (name.Contains("scale"))
                //    {
                //        AnimationUtility.SetEditorCurve(clip, theCurveBinding, null);
                //        ++scaleCount;
                //    }
                //}
                Debug.LogFormat("关闭{0}的scale curve {1}", clip.name, scaleCount);
            }
            return scaleCount;
        }

        public static void ImportMaterial(ModelResAsset asset, ImportMode importMode)
        {
            if (importMode == ImportMode.ForceFull || importMode == ImportMode.OnlyMatAndTexture)
            {
                if (!Directory.Exists(asset.TargetPath))
                    Directory.CreateDirectory(asset.TargetPath);

                var srcMaterialPath = ConvertToUnityPath(asset.RawPath + "/" + asset.AssetName + asset.AssetExtension);
                var targetMaterialPath = ConvertToUnityPath(asset.TargetPath + "/" + asset.AssetName + asset.AssetExtension);

                Debug.Log("ImportMaterial");
                Debug.Log(srcMaterialPath);
                Debug.Log(targetMaterialPath);

                var srcMat = AssetDatabase.LoadAssetAtPath<Material>(srcMaterialPath);
                if (srcMat != null)
                {
                    bool newAsset = false;
                    var targetMat = AssetDatabase.LoadAssetAtPath<Material>(targetMaterialPath);
                    if (targetMat == null)
                    {
                        targetMat = new Material(srcMat.shader);
                        newAsset = true;
                    }
                    else
                    {
                        targetMat.shader = srcMat.shader;
                    }
                    targetMat.CopyPropertiesFromMaterial(srcMat);
                    var texNames = targetMat.GetTexturePropertyNames();
                    for (int i = 0; i < texNames.Length; ++i)
                    {
                        var texName = texNames[i];
                        if (!targetMat.HasProperty(texName))
                        {
                            targetMat.SetTexture(texName, null);
                            continue;
                        }
                        var tex = targetMat.GetTexture(texName);
                        if (tex != null)
                        {
                            var textureName = tex.name;
                            var srcTexPath = AssetDatabase.GetAssetPath(tex);
                            var srcTexPathEx = Path.GetExtension(srcTexPath);
                            var desTexPath = ConvertToUnityPath(asset.TargetPath + "/" + textureName + srcTexPathEx);
                            var desTex = AssetDatabase.LoadAssetAtPath<Texture>(desTexPath);
                            Debug.Log(string.Format("texName:{0} textureName:{1} desTexPath:{2}", texName, textureName, desTexPath));
                            if (desTex != null)
                                targetMat.SetTexture(texName, desTex);
                            else
                                Debug.LogError(string.Format("{0} is null des:{1}", texName, desTexPath));
                        }
                    }

                    if (newAsset)
                    {
                        targetMaterialPath = targetMaterialPath.Replace("//", "/");
                        AssetDatabase.CreateAsset(targetMat, targetMaterialPath);
                    }
                    else
                    {
                        EditorUtility.SetDirty(targetMat);
                        AssetDatabase.SaveAssets();
                    }
                }
                else
                {
                    Debug.LogError(srcMaterialPath + " no exist");
                }
            }
        }

        // 默认策略：贴图不存在才复制
        public static void ImportTexture(ModelResAsset asset, ImportMode importMode)
        {
            if (!Directory.Exists(asset.TargetPath))
                Directory.CreateDirectory(asset.TargetPath);

            var targetTexture = asset.TargetPath + "/" + asset.AssetName + asset.AssetExtension;
            if (!File.Exists(targetTexture) || importMode == ImportMode.ForceFull || importMode == ImportMode.OnlyMatAndTexture)
            {
                File.Copy(asset.RawPath + "/" + asset.AssetName + asset.AssetExtension, targetTexture, true);
            }
        }

        public static void SetAssetBundleName(AssetImporter importer)
        {
            if (importer != null)
            {
                string assetBundleName = importer.assetPath.Substring("Assets/".Length);
                int ext_index = assetBundleName.LastIndexOf('.');
                if (ext_index > 0)
                {
                    assetBundleName = assetBundleName.Substring(0, ext_index);
                }

                importer.assetBundleName = assetBundleName;
#if ENABLE_ASSET_BUNDLE_EXTEND
                importer.assetBundleVariant = "unity3d";
#endif
                importer.SaveAndReimport();
            }
        }

        public static void ClearChildAssetbundle(string subdir)
        {
            var info = new DirectoryInfo(subdir);
            if (info == null)
                // this is not a directory
                return;
            if (!info.Exists)
                return;
            var files = info.GetFileSystemInfos();
            for (int i = 0; i < files.Length; i++)
            {
                var path = files[i].FullName;
                path = path.Replace('\\', '/');
                path = "Assets" + path.Split(new[] { "Assets" }, StringSplitOptions.None)[1];
                //Debug.Log(path + " will be cleared");
                var importer = AssetImporter.GetAtPath(path);
                if (importer)
                    importer.assetBundleName = "";
            }
        }
    }
}
