using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;

public class ModelImportReportWindow : EditorWindow
{
    [SerializeField]
    public List<string> assetPaths = new List<string>();

    Vector2 scrollRect;

    public static void OpenWindow(List<string> assetPaths)
    {
        ModelImportReportWindow window = GetWindow(typeof(ModelImportReportWindow), false, "模型资源导入报告") as ModelImportReportWindow;
        assetPaths.Sort();
        window.assetPaths = assetPaths;
        window.Show(false);
    }

    private void OnGUI()
    {
        EditorGUILayout.LabelField("以下是影响到的资源文件，确认无误后可点击一键提交");

        scrollRect = EditorGUILayout.BeginScrollView(scrollRect);
        foreach (var assetPath in assetPaths)
        {
            EditorGUILayout.LabelField(" -> " + assetPath);
        }
        EditorGUILayout.EndScrollView();

        if (GUILayout.Button("一键提交SVN"))
        {
            SVNTools.SVNTool.SvnCommit(assetPaths, string.Empty);
        }
    }
}

