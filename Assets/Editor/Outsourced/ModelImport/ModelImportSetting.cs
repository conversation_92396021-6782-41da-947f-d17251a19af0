using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using UnityEditor;

namespace ModelImport
{
    public static class ModelImportSetting
    {
        public static string RawModelPath
        {
            get
            {
                return PlayerPrefs.GetString("RawModelPath");
            }
            set
            {
                PlayerPrefs.SetString("RawModelPath", value);
            }
        }

        public static string ModelResPath
        {
            get
            {
                return PlayerPrefs.GetString("ModelResPath");
            }
            set
            {
                PlayerPrefs.SetString("ModelResPath", value);
            }
        }

        // 小小英雄资源相对路径正则匹配表达式
        public static string LittleLegendModelPath;
        public static string LittleLegendMaterialPath;

        public static string LittleLegendModelNameLow;
        public static string LittleLegendModelNameHigh;

        public static string LittleLegendTextureNameLow;
        public static string LittleLegendTextureNameHigh;
        public static string LittleLegendTextureNameHighOld;

        // 英雄资源相对路径正则表达式
        public static string HeroModelPath;
        public static string HeroMaterialPath;

        public static string HeroModelName;
        public static string HeroTextureName;

        // 怪物资源相对路径正则表达式
        public static string MonsterModelPath;
        public static string MonsterMaterialPath;

        public static string MonsterModelName;
        public static string MonsterTextureName;

        // 召唤物资源相对路径正则表达式
        public static string SummonedModelPath;
        public static string SummonedMaterialPath;

        public static string SummonedModelName;
        public static string SummonedTextureName;
    }
}
