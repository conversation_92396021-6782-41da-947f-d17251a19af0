using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFramework;
using UnityEditor;
using UnityEngine;

public static class FbxOptimizeTool
{
    private static bool MeshEquals(string path, Renderer r)
    {
        var mr = r as MeshRenderer;
        if (mr != null)
        {
            var mf = mr.GetComponent<MeshFilter>();
            if (mf != null && mf.sharedMesh != null && AssetDatabase.GetAssetPath(mf.sharedMesh) == path)
            {
                return true;
            }
        }
        var smr = r as SkinnedMeshRenderer;
        if (smr != null)
        {
            if (smr.sharedMesh != null && AssetDatabase.GetAssetPath(smr.sharedMesh) == path)
            {
                return true;
            }
        }
        var psr = r as ParticleSystemRenderer;
        if (psr != null)
        {
            if (psr.mesh != null && AssetDatabase.GetAssetPath(psr.mesh) == path)
            {
                return true;
            }
        }

        return false;
    }

    private static bool MeshEquals(SerializedProperty mesh, string path)
    {
        if (mesh.objectReferenceValue != null && AssetDatabase.GetAssetPath(mesh.objectReferenceValue) == path)
        {
            return true;
        }
        return false;
    }

    private static bool IsLightOn(string path)
    {
        bool isLightOn = false;
        if (path.Contains("s4_tft_default_001")                 // 端游复刻地图
            || path.StartsWith("Assets/Art_TFT/scenes/LLReview")    // 小小英雄局外高清场景
            || path.StartsWith("Assets/Art_TFT/scenes/tft_lobby_01")                 // 大厅场景
            || (path.StartsWith("Assets/Art_TFT_Raw/model_res/LittleLegend") && path.Contains("/high/")))   // 小小英雄局外高模
        {
            isLightOn = true;
        }
        return isLightOn;
    }

    private enum OptimizeType
    {
        None,
        ClearDefaultMat,
        SetDefaultMat,
    }
    private static OptimizeType GetOptimizeType(string path, ref DateTime checkFBXDt)
    {
        // fbx中mesh数量大于1则不能优化
        int meshCout = 0;
        var allObjects = AssetDatabase.LoadAllAssetsAtPath(path);
        foreach (var item in allObjects)
        {
            var m = item as Mesh;
            if (m != null)
                ++meshCout;
        }
        if (meshCout > 1)
        {
            return OptimizeType.ClearDefaultMat;
        }

        //足球场
        if (path.Contains("Assets/Art_TFT/scenes/InGameScenes/s8_tft_worldcup"))
        {
            return OptimizeType.SetDefaultMat;
        }

        // 用烘焙的经典竞技场 不能裁剪
        if (path.Contains("Assets/Art_TFT/scenes/InGameScenes/s4_tft_default_001"))
        {
            return OptimizeType.ClearDefaultMat;
        }

        // 局内战斗特效强制开启优化
        if (path.Contains("Assets/Art/FX/Models/battle"))
        {
            return OptimizeType.SetDefaultMat;
        }

        // 非局内战斗特效 新提交的资源也可以优化
        var pathSvnInfo = TKSvnCore.GetInfo(path);
        if (pathSvnInfo != null && pathSvnInfo.submitTime > checkFBXDt)
        {
            return OptimizeType.SetDefaultMat;
        }
        else
        {
            return OptimizeType.ClearDefaultMat;
        }
    }

    [MenuItem("Tools/PrefabGenTool/自动根据引用生成所有FBX的默认材质")]
    public static void SetFBXDefaultMaterial()
    {
        ReferenceFinder.data.CollectAssets(true);

        var guids = AssetDatabase.FindAssets("t:Model");
        List<string> commitList = new List<string>();
        HashSet<string> useMaterials = new HashSet<string>();
        var checkFBXDt = new DateTime(2023, 6, 1);

        foreach (string guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            if (path.EndsWith(".fbx", StringComparison.OrdinalIgnoreCase))
            {

                var optimizeType = GetOptimizeType(path, ref checkFBXDt);
                if (optimizeType == OptimizeType.None)
                    continue;
                // 可能用实时光照的地方要强制开启keyword
                bool isLightOn = IsLightOn(path);
                if (ReferenceFinder.data.antiDependenciesNonCached.TryGetValue(guid, out HashSet<string> refs))
                {
                    // 1. 收集所有被引用的材质列表
                    HashSet<Material> materials = new HashSet<Material>();
                    foreach (var refGuid in refs)
                    {
                        var refPath = AssetDatabase.GUIDToAssetPath(refGuid);

                        if (refPath.EndsWith(".prefab", StringComparison.OrdinalIgnoreCase))
                        {
                            var go = AssetDatabase.LoadAssetAtPath<GameObject>(refPath);
                            if (go != null)
                            {
                                var renderers = go.GetComponentsInChildren<Renderer>(true);
                                foreach (var r in renderers)
                                {
                                    if (MeshEquals(path, r))
                                    {
                                        foreach (var item in r.sharedMaterials)
                                        {
                                            if (item != null && !materials.Contains(item))
                                                materials.Add(item);
                                        }
                                    }
                                }
                            }
                        }
#if !OUTSOURCE
                        else if (refPath.EndsWith(".asset", StringComparison.OrdinalIgnoreCase) && refPath.Contains("ParticleSystemConfig"))
                        {
                            var asset = AssetDatabase.LoadAssetAtPath<ParticleSystemConfig>(refPath);
                            if (asset == null)
                            {
                                TKFrame.Diagnostic.Error(refPath + " is null!!!");
                                continue;
                            }
                            var serializedObject = new SerializedObject(asset);
                            var rs = serializedObject.FindProperty("m_rendererList");
                            for (int i = 0; i < rs.arraySize; ++i)
                            {
                                var r = rs.GetArrayElementAtIndex(i);
                                bool find = MeshEquals(r.FindPropertyRelative("Mesh0"), path);
                                if (!find)
                                    find = MeshEquals(r.FindPropertyRelative("Mesh1"), path);
                                if (!find)
                                    find = MeshEquals(r.FindPropertyRelative("Mesh2"), path);
                                if (!find)
                                    find = MeshEquals(r.FindPropertyRelative("Mesh3"), path);

                                if (find)
                                {
                                    var mats = r.FindPropertyRelative("Materials");
                                    for (int k = 0; k < mats.arraySize; ++k)
                                    {
                                        var m = mats.GetArrayElementAtIndex(k);
                                        var mat = m.objectReferenceValue as Material;
                                        if (mat != null)
                                        {
                                            if (!materials.Contains(mat))
                                                materials.Add(mat);
                                        }
                                    }
                                }
                            }
                        }
#endif
                    }

                    if (materials.Count == 0)
                        continue;

                    // 2. 根据被引用的材质找相应的默认材质
                    HashSet<Material> defaultMaterials = new HashSet<Material>();
                    foreach (var mat in materials)
                    {
                        if (mat.shader == null)
                            continue;
                        var key = mat.shader.name;
                        key += string.Join("", mat.shaderKeywords);
                        string lightOnKeyWord = "LIGHTMAP_ON";
                        if (isLightOn && !key.Contains(lightOnKeyWord))
                            key += lightOnKeyWord;
                        var hashKey = Hash128.Compute(key).ToString();

                        var defaultMatPath = "Assets/Art/DefaultMaterials/replacement_" + hashKey + ".mat";
                        var defaultMat = AssetDatabase.LoadAssetAtPath<Material>(defaultMatPath);
                        if (defaultMat == null)
                        {
                            defaultMat = new Material(mat.shader);
                            defaultMat.shaderKeywords = mat.shaderKeywords;
                            AssetDatabase.CreateAsset(defaultMat, defaultMatPath);

                            commitList.Add(defaultMatPath);
                            commitList.Add(defaultMatPath + ".meta");
                        }
                        if (!defaultMaterials.Contains(defaultMat))
                            defaultMaterials.Add(defaultMat);

                        if (!useMaterials.Contains(defaultMatPath))
                            useMaterials.Add(defaultMatPath);
                    }

                    // 3. 将材质保存到FBX文件中
                    ModelImporter importer = AssetImporter.GetAtPath(path) as ModelImporter;
                    if (importer != null)
                    {
                        var serializedObject = new SerializedObject(importer);
                        var materialsProp = serializedObject.FindProperty("m_Materials");
                        bool needResave = false;
                        if (materialsProp.arraySize != defaultMaterials.Count || optimizeType == OptimizeType.ClearDefaultMat)
                        {
                            // 如果说材质数量和FBX中存储的不一致，就不要做这个优化了
                            // 如果之前就有优化的，记得还原回去
                            needResave = CleanAllRemap(importer);
                        }
                        else
                        {
                            var dict = importer.GetExternalObjectMap();

                            var mats = new HashSet<Material>(defaultMaterials);
                            foreach (var remap in dict)
                            {
                                var mat = remap.Value as Material;
                                if (mat != null && mats.Contains(mat))
                                {
                                    mats.Remove(mat);
                                }
                            }
                            if (mats.Count != 0)
                            {
                                //  清理已经不引用的
                                foreach (var remap in dict)
                                {
                                    var mat = remap.Value as Material;
                                    if (mat != null)
                                    {
                                        Debug.Log("remove external object_key: " + remap.Key.name + " asset: " + importer.assetPath);
                                        importer.RemoveRemap(remap.Key);
                                        needResave = true;
                                    }
                                }
                                if (AddMeshImporterRemap(materialsProp, defaultMaterials, importer))
                                    needResave = true;
                            }
                        }

                        if (needResave)
                        {
                            var ddd = importer.GetExternalObjectMap();
                            Debug.Log("resave: " + importer.assetPath + " ddd: " + ddd.Count);
                            importer.SaveAndReimport();
                            var ddd2 = importer.GetExternalObjectMap();
                            Debug.Log(" ddd2: " + ddd2.Count);
                            commitList.Add(importer.assetPath + ".meta");
                            //break;
                        }
                    }
                }
            }
        }

        // 自动清理用不到的default material
        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
        ReferenceFinder.data.CollectAssets(true);
        var materialGuids = AssetDatabase.FindAssets("t:Material", new string[] { "Assets/Art/DefaultMaterials" });
        foreach (var guid in materialGuids)
        {
            var assetPath = AssetDatabase.GUIDToAssetPath(guid);
            // 新增的不管
            if (useMaterials.Contains(assetPath))
                continue;

            // 没有引用的删掉
            if (!ReferenceFinder.data.antiDependenciesNonCached.TryGetValue(guid, out HashSet<string> refs) || refs.Count == 0)
            {
                File.Delete(assetPath);
                File.Delete(assetPath + ".meta");

                commitList.Add(assetPath);
                commitList.Add(assetPath + ".meta");
            }
        }

        // 自动提交这些fbx文件
        TKSvnCore.AutoCommitSync("--story=865809373 [xiaobai] commit fbx channel cut", commitList);
    }

    private static bool CleanAllRemap(ModelImporter importer)
    {
        bool needResave = false;
        var dict = importer.GetExternalObjectMap();
        foreach (var remap in dict)
        {
            var mat = remap.Value as Material;
            if (mat != null)
            {
                importer.RemoveRemap(remap.Key);
                needResave = true;
            }
        }
        return needResave;
    }

    private static bool AddMeshImporterRemap(SerializedProperty materialsProp, HashSet<Material> mats, ModelImporter importer)
    {
        bool needResave = false;
        int materialIdx = 0;
        foreach (var mat in mats)
        {
            if (materialIdx >= materialsProp.arraySize)
            {
                TKFrame.Diagnostic.Warn(importer.assetPath + " Material: " + mat.name + " ingore!");
                break;
            }

            var id = materialsProp.GetArrayElementAtIndex(materialIdx);
            var name = id.FindPropertyRelative("name").stringValue;

            importer.AddRemap(new AssetImporter.SourceAssetIdentifier(typeof(Material), name), mat);

            ++materialIdx;
            needResave = true;
        }
        return needResave;
    }

}

