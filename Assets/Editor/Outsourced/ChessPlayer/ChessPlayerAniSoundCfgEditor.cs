using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;
using ZGameChess;

[CustomEditor(typeof(ChessPlayerSoundCfg))]
public class ChessPlayerAniSoundCfgEditor : Editor
{
    private int aniSelectedIndex = 0;

    private string[] aniNames = new string[0];

    private List<ChessPlayerSoundData> clearList = new List<ChessPlayerSoundData>();

    public override void OnInspectorGUI()
    {
        EditorGUI.BeginChangeCheck();
        ChessPlayerSoundCfg m_target = target as ChessPlayerSoundCfg;

        RuntimeAnimatorController targetController = AssetDatabase.LoadAssetAtPath<RuntimeAnimatorController>(m_target.controllerPath);

        //显示动画列表
        RuntimeAnimatorController controller = EditorGUILayout.ObjectField("状态机", targetController,
            typeof(RuntimeAnimatorController), false) as RuntimeAnimatorController;
        m_target.controllerPath = AssetDatabase.GetAssetPath(controller);
        //把已经编辑的无效配置标记出来
        if (controller != targetController && m_target.clipData != null)
        {
            foreach (var clipdata in m_target.clipData)
            {
                if (controller != null)
                {
                    bool bFound = false;
                    foreach (var clip in controller.animationClips)
                    {
                        if (clipdata.clipName == clip.name)
                        {
                            bFound = true;
                            break;
                        }
                    }
                    if (!bFound) clipdata.bValid = false;
                }
                else
                {
                    clipdata.bValid = true;
                }
            }
        }
        targetController = controller;

        if (targetController == null && m_target.clipData.Count == 0) return;

        if (targetController != null)
        {
            aniNames = new string[targetController.animationClips.Length];
            for (int i = 0; i < aniNames.Length; i++)
            {
                aniNames[i] = targetController.animationClips[i].name;
            }
        }
        else
        {
            aniNames = new string[0];
        }

        UpdateAllWhiteAction();

        EditorGUI.BeginDisabledGroup(targetController == null);
        aniSelectedIndex = EditorGUILayout.Popup(aniSelectedIndex, aniNames);
        AnimationClip selectedClip = null;
        if (targetController != null)
            selectedClip = targetController.animationClips[aniSelectedIndex];
        EditorGUI.EndDisabledGroup();

        EditorGUI.BeginDisabledGroup(targetController == null);
        if (GUILayout.Button("Clear"))
        {
            if (EditorUtility.DisplayDialog("提示", "是否确认清理？", "确定", "取消"))
            {
                targetController = null;
                m_target.clipData.Clear();
            }
        }
        EditorGUI.EndDisabledGroup();

        if (GUILayout.Button("预览"))
        {
            ChessPlayerAniSoundCfgWindow.Display(m_target);
        }

        if (GUILayout.Button("Save"))
        {
            AssetDatabase.SaveAssets();
        }

        EditorGUI.BeginDisabledGroup(targetController == null);
        if (GUILayout.Button("Add"))
        {
            if (m_target.clipData == null)
                m_target.clipData = new List<ChessPlayerSoundData>();
            var aniData = new ChessPlayerSoundData();
            aniData.clipName = selectedClip.name;
            aniData.shortNameHash = GetShortHash(aniData.clipName);
            m_target.clipData.Add(aniData);
        }
        EditorGUI.EndDisabledGroup();

        if (GUILayout.Button("修复改名导致的声音丢失问题"))
        {
            foreach (var item in m_target.clipData)
            {
                item.shortNameHash = GetShortHash(item.clipName);
            }
            AssetDatabase.SaveAssets();
        }

        //编辑动画属性
        GUILayout.BeginVertical();
        InspectorAniCfg(m_target, selectedClip);
        GUILayout.EndVertical();

        if (EditorGUI.EndChangeCheck())
        {
            EditorUtility.SetDirty(m_target);
        }
    }

    private int GetShortHash(string name)
    {
        if (name.Contains("@"))
        {
            var arr = name.Split('@');
            return Animator.StringToHash(arr[1]);
        }
        else
            return Animator.StringToHash(name);
    }

    void InspectorAniCfg(ChessPlayerSoundCfg m_target, AnimationClip selectedClip)
    {
        RuntimeAnimatorController targetController = AssetDatabase.LoadAssetAtPath<RuntimeAnimatorController>(m_target.controllerPath);
        if (targetController == null) return;

        clearList.Clear();
        List<ChessPlayerSoundData> datas = GetData(m_target, selectedClip.name);
        for (int i = 0; i < datas.Count; i++)
        {
            var data = datas[i];
            EditorGUILayout.Space();
            EditorGUI.indentLevel = 1;
            GUILayout.Label("index : " + i);
            GUILayout.BeginVertical("box");
            data.triggerTime = EditorGUILayout.Slider(data.triggerTime, 0, selectedClip.length);
            data.triggerNormalizedTime = data.triggerTime / selectedClip.length;

            EditorGUILayout.Space();
            data.bankName = EditorGUILayout.TextField("Bank Name", data.bankName);
            data.soundName = EditorGUILayout.TextField("音效名字", data.soundName);
            int whiteActionMask = EditorGUILayout.MaskField("不清理音效的动作", data.whiteActionMask, aniNames);
            if (whiteActionMask != data.whiteActionMask)
            {
                data.whiteActionMask = whiteActionMask;

                UpdateWhiteAction(data);
            }
            data.loopTrigger = EditorGUILayout.Toggle("循环触发", data.loopTrigger);
            if (GUILayout.Button("Remove", GUILayout.Width(200)))
            {
                clearList.Add(data);
            }
            GUILayout.EndVertical();

            foreach (var d in clearList) m_target.clipData.Remove(d);
        }
    }

    private void UpdateAllWhiteAction()
    {
        Dictionary<int, int> hashIndexDict = new Dictionary<int, int>();
        for (int k = 0; k < aniNames.Length; ++k)
        {
            var hash = GetShortHash(aniNames[k]);
            if (!hashIndexDict.ContainsKey(hash))
                hashIndexDict.Add(hash, k);
            //else
            //    Debug.LogError("aniNames[" + k + "]: " + aniNames[k] + " already added key");
        }

        ChessPlayerSoundCfg m_target = target as ChessPlayerSoundCfg;
        for (int i = 0; i < m_target.clipData.Count; ++i)
        {
            var clipData = m_target.clipData[i];
            if (clipData.whiteActionHashList != null)
            {
                int mask = 0;
                for (int j = 0; j < clipData.whiteActionHashList.Count; ++j)
                {
                    var nameHash = clipData.whiteActionHashList[j];
                    if (hashIndexDict.TryGetValue(nameHash, out int index))
                    {
                        mask |= (1 << index);
                    }
                }
                clipData.whiteActionMask = mask;
            }
        }
    }

    private void UpdateWhiteAction(ChessPlayerSoundData data)
    {
        List<int> nameHashList = new List<int>();
        for (int j = 0; j < aniNames.Length; ++j)
        {
            if ((data.whiteActionMask & (1 << j)) != 0)
            {
                nameHashList.Add(GetShortHash(aniNames[j]));
            }
        }
        data.whiteActionHashList = nameHashList;
    }

    List<ChessPlayerSoundData> GetData(ChessPlayerSoundCfg cfg, string name)
    {
        var clipData = cfg.clipData.FindAll((d) => d.clipName == name);
        return clipData;
    }
}

