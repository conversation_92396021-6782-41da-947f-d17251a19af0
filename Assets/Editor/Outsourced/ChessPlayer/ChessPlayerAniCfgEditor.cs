using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System;
using ZGameChess;
using UnityEditor.Animations;

[CustomEditor(typeof(ChessPlayerEffectEventCfg))]
public class ChessPlayerAniCfgEditor : Editor
{
    private int aniSelectedIndex = 0;

    private string[] aniNames = new string[0];

    private List<ChessPlayerEffectEventData> clearList = new List<ChessPlayerEffectEventData>();

    private void OnEnable()
    {
        ChessPlayerEffectEventCfg m_target = target as ChessPlayerEffectEventCfg;
        List<ChessPlayerEffectEventData> datas = m_target.clipData;
        bool hasChanged = false;
        AnimatorController targetController = AssetDatabase.LoadAssetAtPath<AnimatorController>(m_target.controllerPath);
        for (int i = 0; i < datas.Count; i++)
        {
            var data = datas[i];
            if (!string.IsNullOrEmpty(data.effectName) && data.effect == null)
            {
                data.effect = FindEffect("Assets/Art_TFT_Raw/effects/business_effect/teamleader_effect", data.effectName);
                if (data.effect != null)
                {
                    hasChanged = true;
                }
                else
                {
                    Debug.LogError(data.effectName + " 资源不存在");
                }
            }

            // 检测clip名字是否有改变
            AnimatorState state = null;
            if (targetController != null && targetController.layers != null)
            {
                for (int j = 0; j < targetController.layers.Length; ++j)
                {
                    var layer = targetController.layers[j];
                    state = FindState(layer.stateMachine, data.clipName);
                    if (state != null)
                    {
                        if (state.nameHash != data.shortNameHash)
                        {
                            data.shortNameHash = state.nameHash;
                            hasChanged = true;
                        }
                    }
                }
            }

        }
        if (hasChanged)
        {
            EditorUtility.SetDirty(m_target);
            AssetDatabase.SaveAssets();
        }
    }

    private AnimatorState FindState(AnimatorStateMachine machine, string clipName)
    {
        for (int i = 0; i < machine.states.Length; ++i)
        {
            var s = machine.states[i];
            if (s.state.motion != null)
            {
                var animationClip = s.state.motion as AnimationClip;
                if (animationClip != null && animationClip.name == clipName)
                {
                    return s.state;
                }
            }
        }

        for (int i = 0; i < machine.stateMachines.Length; ++i)
        {
            var sm = machine.stateMachines[i];
            var s = FindState(sm.stateMachine, clipName);
            if (s != null)
                return s;
        }
        return null;
    }

    private GameObject FindEffect(string path, string effectName)
    {
        var allAssets = AssetDatabase.FindAssets("t:Prefab", new string[] { path });
        if (allAssets.Length > 0)
        {
            for (int i = 0; i < allAssets.Length; ++i)
            {
                var assetPath = AssetDatabase.GUIDToAssetPath(allAssets[i]);
                if (System.IO.Path.GetFileNameWithoutExtension(assetPath) == effectName)
                {
                    return AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
                }
            }
        }
        Debug.LogError(effectName + " 找不到 allAssets.Length = " + allAssets.Length);
        return null;
    }

    public override void OnInspectorGUI()
    {
        EditorGUI.BeginChangeCheck();
        ChessPlayerEffectEventCfg m_target = target as ChessPlayerEffectEventCfg;
        
        RuntimeAnimatorController targetController = AssetDatabase.LoadAssetAtPath<RuntimeAnimatorController>(m_target.controllerPath);
        
        //显示动画列表
        RuntimeAnimatorController controller = EditorGUILayout.ObjectField("状态机", targetController,
            typeof(RuntimeAnimatorController), false) as RuntimeAnimatorController;
        m_target.controllerPath = AssetDatabase.GetAssetPath(controller);
        //把已经编辑的无效配置标记出来
        if (controller != targetController && m_target.clipData != null)
        {
            foreach (var clipdata in m_target.clipData)
            {
                if (controller != null)
                {
                    bool bFound = false;
                    foreach (var clip in controller.animationClips)
                    {
                        if (clipdata.clipName == clip.name)
                        {
                            bFound = true;
                            break;
                        }
                    }
                    if (!bFound) clipdata.bValid = false;
                }
                else
                {
                    clipdata.bValid = true;
                }
            }
        }
        targetController = controller;

        if (targetController == null && m_target.clipData.Count == 0) return;

        if (targetController != null)
        {
            aniNames = new string[targetController.animationClips.Length];
            for (int i = 0; i < aniNames.Length; i++)
            {
                aniNames[i] = targetController.animationClips[i].name;
            }
        }
        else
        {
            aniNames = new string[0];
        }

        EditorGUI.BeginDisabledGroup(targetController == null);
        aniSelectedIndex = EditorGUILayout.Popup(aniSelectedIndex, aniNames);
        AnimationClip selectedClip = null;
        if(targetController != null)
            selectedClip = targetController.animationClips[aniSelectedIndex];
        EditorGUI.EndDisabledGroup();

        EditorGUI.BeginDisabledGroup(targetController == null);
        if (GUILayout.Button("Clear"))
        {
            if (EditorUtility.DisplayDialog("提示", "是否确认清理？", "确定", "取消"))
            {
                targetController = null;
                m_target.clipData.Clear();
            }
        }
        EditorGUI.EndDisabledGroup();

        if (GUILayout.Button("Preview"))
        {
            ChessPlayerAniCfgWindow.Display(m_target); 
        }

        if (GUILayout.Button("Save"))
        {
            AssetDatabase.SaveAssets();
        }

        EditorGUI.BeginDisabledGroup(targetController == null);
        if (GUILayout.Button("Add"))
        {
            if (m_target.clipData == null)
                m_target.clipData = new List<ChessPlayerEffectEventData>();
            var aniData = new ChessPlayerEffectEventData();
            aniData.clipName = selectedClip.name;
            if (aniData.clipName.Contains("@"))
                aniData.shortNameHash = Animator.StringToHash(aniData.clipName.Split('@')[1]);
            else
                aniData.shortNameHash = Animator.StringToHash(aniData.clipName);
            m_target.clipData.Add(aniData);
        }
        EditorGUI.EndDisabledGroup();

        //编辑动画属性
        GUILayout.BeginVertical();
        InspectorAniCfg(m_target, selectedClip);
        GUILayout.EndVertical();

        if (EditorGUI.EndChangeCheck())
        {
            EditorUtility.SetDirty(m_target);
        }
    }

    void InspectorAniCfg(ChessPlayerEffectEventCfg m_target, AnimationClip selectedClip)
    {
        RuntimeAnimatorController targetController = AssetDatabase.LoadAssetAtPath<RuntimeAnimatorController>(m_target.controllerPath);
        if (targetController == null) return;

        clearList.Clear();
        List<ChessPlayerEffectEventData> datas = GetData(m_target, selectedClip.name);
        for (int i = 0; i < datas.Count; i++)
        {
            var data = datas[i];
            EditorGUILayout.Space();
            EditorGUI.indentLevel = 1;
            GUILayout.Label("index : " + i);
            GUILayout.BeginVertical("box");
            data.effectTriggerTime = EditorGUILayout.Slider(data.effectTriggerTime, 0, selectedClip.length);
            data.effectNormalizedTime = data.effectTriggerTime / selectedClip.length;

            EditorGUILayout.Space();
            EditorGUI.BeginChangeCheck();
            data.effectName = EditorGUILayout.TextField("特效名称", data.effectName);
            if (EditorGUI.EndChangeCheck())
            {
                data.effect = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Art_TFT_Raw/effects/teamleader_effect/" + data.effectName + ".prefab");
                EditorUtility.SetDirty(m_target);
            }
            GUI.changed = false;
            EditorGUILayout.ObjectField("特效预制体", data.effect, typeof(GameObject), false);
            GUI.changed = true;

            data.sceneEffect = EditorGUILayout.Toggle("场景特效", data.sceneEffect);
            if (!data.sceneEffect)
                data.effectHangingPoint = (CharacterHangPoint.SupportHangPointType)EditorGUILayout.EnumPopup("光效挂点", data.effectHangingPoint);
            data.IsLoop = EditorGUILayout.Toggle(new GUIContent("是否动作循环时不重复触发"), data.IsLoop);
            data.DelayRemoveTime = EditorGUILayout.FloatField(new GUIContent("延迟销毁时间（s）"), data.DelayRemoveTime);
            if (!data.sceneEffect)
                data.bRotateByBone = EditorGUILayout.Toggle(new GUIContent("是否随骨骼旋转"), data.bRotateByBone);
            data.bCleanWhenTransition = EditorGUILayout.Toggle(new GUIContent("是否特效切动作删除"), data.bCleanWhenTransition);
            if (!data.sceneEffect)
                data.bScaleWithCharacter = EditorGUILayout.Toggle(new GUIContent("是否随角色缩放"), data.bScaleWithCharacter);
            data.stateCleanWhenTransition = EditorGUILayout.TextField(new GUIContent("特效切动作删除依赖的状态"), data.stateCleanWhenTransition);
            data.stateKeepWhenTransition = EditorGUILayout.TextField(new GUIContent("在这个状态下不删除特效"), data.stateKeepWhenTransition);
            data.alignActionTime = EditorGUILayout.Toggle(new GUIContent("严格对齐动作时间"), data.alignActionTime);
            if (GUILayout.Button("Remove", GUILayout.Width(200)))
            {
                clearList.Add(data);
            }
            GUILayout.EndVertical();

            foreach (var d in clearList) m_target.clipData.Remove(d);
        }
    }

    List<ChessPlayerEffectEventData> GetData(ChessPlayerEffectEventCfg cfg, string name)
    {
        var clipData = cfg.clipData.FindAll((d) => d.clipName == name);
        return clipData;
    }
}

