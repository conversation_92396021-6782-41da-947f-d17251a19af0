using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

namespace UnityEditor
{
    public class CustomPBRShaderGUI : OutGameShaderGUI
    {
        public static class Styles
        {
            public static string blendMode = "混合模式 blend Mode";
            public static readonly string[] blendNames = new string[4] {
                "不透明 Opaque",
                "镂空 Cutout",
                "透明 ",// Fade"
                "透明 Transparent (保留高光)"

            };
            public static GUIContent culltex = new GUIContent("剔除 Cull");
            public static readonly string[] cullNames = new string[3] {
                "双面显示",
                "正面",
                "背面"
            };
            public static GUIContent vrsText = new GUIContent("VRS 模式");
            public static readonly string[] vrsNames = new string[] {
                "VRS默认",
                "VRS1x1",
                "VRS1x2",
                "VRS2x1",
                "VRS2x2",
                "保留选项",
                "VRS4x2",
                "VRS4x4",
            };


            public static GUIContent albedoTex = new GUIContent("颜色(RGB) 透明(A) ");
            public static GUIContent alphaCutoffText = new GUIContent("Alpha Cutoff", "Threshold for alpha cutoff");

            public static GUIContent metallicMapText = new GUIContent("金属度(R) 光滑度(G) AO(B) ", "(R)Metallic (A)Smoothness (B)AmbientOcclusion ");
            public static GUIContent AOText = new GUIContent("AO强度", "Ambient  occlusion intensity");
            public static GUIContent smoothnessText = new GUIContent("光滑度", "Smoothness value");
            public static GUIContent smoothnessScaleText = new GUIContent("光滑度", "Smoothness scale factor");

            public static GUIContent normalMapText = new GUIContent("法线贴图", "Normal Map");
            public static GUIContent bumpBiasText = new GUIContent("Bias", "Normal Map Bias");
            public static GUIContent detailEnabledText = new GUIContent("细节贴图", "Enable Detail Map");
            public static GUIContent detailNormalMapText = new GUIContent("细节法线 Detail Normal", "Normal Map");
            // Hair
            public static GUIContent hairEnabledText = new GUIContent("开启头发", "Enable Hair Specular Map");
            public static GUIContent hairSpecularMapText = new GUIContent("头发高光贴图 Hair Specular", "Hair Specular Map");
            public static GUIContent hairSpecularColor1Text = new GUIContent("头发高光颜色1 Hair Specular Color1", "Hair Specular Color1");
            public static GUIContent hairSpecularColor2Text = new GUIContent("头发高光颜色2 Hair Specular Color2", "Hair Specular Color2");
            public static GUIContent hairOffsetText = new GUIContent("头发高光偏移 Hair Specular Offset", "Hair Specular Offset");
            public static GUIContent hairOffsetIntensityText = new GUIContent("头发高光偏移强度 Hair Specular Offset Intensity", "Hair Specular Offset Intensity");

            //public static string primaryMapsText = "Main Maps";

            //lightmap  --- fengxzeng
            //public static GUIContent lightmapTex = new GUIContent("光照贴图lighmap");
            //public static GUIContent LigtmapEnabledText = new GUIContent("开启光照贴图");
            //Vertex Wind  --- fengxzeng

            //plane reflection --- fengxzeng
            public static GUIContent PlaneReflectionEnabledText = new GUIContent("镜面反射");
            public static GUIContent PlaneReflectionIntensityText = new GUIContent("强度");

            //skin
            public static GUIContent skinText = new GUIContent("厚度(R) 3SMask(G)", "厚度图：做法不变; 3SMask：黑色部分没有3S效果");
            public static GUIContent bssrdfMapText = new GUIContent("散射颜色 3SLUT ");
            //public static GUIContent scatteringShadowIntensityText = new GUIContent("Scattering Shadow Scale");
            //public static GUIContent scatterLightingText = new GUIContent("Scatter Lighting");
            public static GUIContent powerText = new GUIContent("对比度");
            //public static GUIContent distortionText = new GUIContent("Distortion");
            public static GUIContent scaleText = new GUIContent("亮度");
            //【ID864553267】去掉法线模糊效果,因为在项目风格中效果不明显
            // public static GUIContent diffuseBumpSettingsText = new GUIContent("法线");
            // public static GUIContent diffuseNormalMapBlurBiasText = new GUIContent("法线模糊Bias");
            // public static GUIContent blurStrengthText = new GUIContent("法线模糊强度");

            public static GUIContent translucencyColorText = new GUIContent("透光颜色");
            public static GUIContent skinShadowColorText = new GUIContent("阴影染色");


        }
        MaterialProperty FLOW_Enabled = null;

        MaterialProperty FLOW_Pattern = null;

        MaterialProperty BOOM = null;
        MaterialEditor _materialEditor;

        public MaterialProperty cullmode = null;
        MaterialProperty vrsMode = null;
        public MaterialProperty alphaCutoff = null;

        MaterialProperty renderQueueOffset = null;

        MaterialProperty color = null;
        MaterialProperty prepassAlpha = null;
        MaterialProperty albedoMap = null;

        MaterialProperty emissionMap = null;
        MaterialProperty emissionColor = null;

        MaterialProperty metallicMap = null;
        MaterialProperty metallic = null;

        //MaterialProperty smoothness = null;
        MaterialProperty smoothnessScale = null;

        MaterialProperty bumpScale = null;
        MaterialProperty bumpMap = null;
        MaterialProperty bumpBias = null;

        MaterialProperty detailEnabled = null;
        MaterialProperty detailNormalMapScale = null;
        MaterialProperty detailNormalMap = null;


        // Diffuse / Specular Intensity 
        MaterialProperty diffuseLightingIntensity = null;
        MaterialProperty specularLightingIntensity = null;
        
        // Hair
        MaterialProperty hairEnabled = null;
        MaterialProperty hairSpecularColor1 = null;
        MaterialProperty hairSpecularColor2 = null;
        MaterialProperty hairSpecularMap = null;
        MaterialProperty hairSpecularMapScale = null;
        MaterialProperty hairOffset = null;
        MaterialProperty hairOffsetIntensity = null;
        
        
        
        //skin mask

        MaterialProperty skinEnable = null;
        MaterialProperty curvaturePower = null;
        MaterialProperty curvatureScale = null;
        MaterialProperty power = null;
        MaterialProperty scale = null;
        MaterialProperty skinShadowColor = null;
        MaterialProperty translucencyColor = null;
        MaterialProperty translucencyMap = null;
        MaterialProperty bssrdfMap = null;
        MaterialProperty diffuseNormalMapBlurBias = null;
        MaterialProperty blurStrength = null;



        MaterialProperty aoIntensity = null;


        MaterialProperty rimVector = null;
        MaterialProperty rimColor = null;
        MaterialProperty rimPower = null;
        MaterialProperty rimScale = null;
        MaterialProperty rimEffect = null;

        // reflection --fengxzeng
        //MaterialProperty lightmap = null;
        //MaterialProperty ligmapColor = null;
        //MaterialProperty lightmapEnabled = null;
        //Vertex Wind  --- fengxzeng
        //MaterialProperty windEnabled = null;

        MaterialProperty vertexWindEnabled = null;

        MaterialProperty WindFrequency = null;
        MaterialProperty WindAmplitude = null;
        MaterialProperty WindWavelength = null;
        MaterialProperty WindDirection = null;

        //dissolve
        MaterialProperty dissolveEnabled = null;

        // reflection --fengxzeng
        MaterialProperty planeReflectionEnabled = null;
        MaterialProperty PlaneReflectionIntensity = null;

        //Thin Film Interference	--horacezhao
        // MaterialProperty TFI_Enabled = null;

        //Opal --horacezhao
        MaterialProperty OPAL_Enabled = null;
        //Rim Transparency --horacezhao
        MaterialProperty RimTransparency_Enabled = null;
        //Stars Skin --horacezhao
        MaterialProperty Star_Enabled = null;
        MaterialProperty Sparkle_Enabled = null;
        MaterialProperty SparkleView_Enabled = null;
        MaterialProperty StarLine_Enabled = null;

        //Unique Shadow
        //MaterialProperty UniqueShadow_Enabled = null;

        public bool vertexWind = false;
        public static bool initialized = false;
        public static bool refresh = false;
        public void FindProperties(MaterialProperty[] props)
        {
            blendMode = FindProperty("_Mode", props);
            cullmode = FindProperty("_Cull", props);
            vrsMode = FindProperty("_ShadingRate", props, false);
            color = FindProperty("_Color", props, false);
            albedoMap = FindProperty("_MainTex", props, false);
            alphaCutoff = FindProperty("_Cutoff", props, false);

            emissionMap = FindProperty("_Emission", props,false);
            emissionColor = FindProperty("_Emissioncolor", props,false);
            metallicMap = FindProperty("_MetallicMap", props,false);
            metallic = FindProperty("_Metallic", props,false);

            //smoothness = FindProperty("_Glossiness", props);
            smoothnessScale = FindProperty("_GlossMapScale", props);

            aoIntensity = FindProperty("_AOIntensity", props);

            bumpScale = FindProperty("_BumpScale", props);
            bumpMap = FindProperty("_Bump", props);
            bumpBias = FindProperty("_NormalBias", props);

            detailEnabled = FindProperty("_DetailEnabled", props,false);
            detailNormalMapScale = FindProperty("_DetailBumpScale", props,false);
            detailNormalMap = FindProperty("_DetailBump", props,false);


            diffuseLightingIntensity = FindProperty("_DiffuseLightingIntensity", props, false);
            specularLightingIntensity = FindProperty("_SpecularLightingIntensity", props, false);
            
            

            renderQueueOffset = FindProperty("_RenderQueueOffset", props);


            if (isCharBase_PrePass)
            {
                prepassAlpha = FindProperty("_PrepassAlphaScale", props);
            }

            if (!isSceneBase)
            {
                skinEnable = FindProperty("_EnableSSS", props,false);
            }




            if (isCharBase)
            {
                // Hair
                hairEnabled = FindProperty("_HairEnabled", props);
                hairSpecularColor1 = FindProperty("_HairSpecularColor1", props);
                hairSpecularColor2 = FindProperty("_HairSpecularColor2", props);
                hairSpecularMap = FindProperty("_HairSpecularTex", props);
                hairOffset = FindProperty("_HairOffset", props);
                hairOffsetIntensity = FindProperty("_HairOffsetIntensity", props);
                
                rimVector = FindProperty("_RimVector", props);
                rimColor = FindProperty("_RimColor", props);
                rimPower = FindProperty("_RimPower", props);
                rimScale = FindProperty("_RimScale", props);
                rimEffect = FindProperty("_BeUsedFromEffect", props);

                //lightmapEnabled = FindProperty("_LightmapEnabled", props);
                //lightmap = FindProperty("_LightMap", props);
                //ligmapColor = FindProperty("_LightmapColor", props);

                //Vertex Wind  --- fengxzeng
                //windEnabled = FindProperty("_WindEnabled", props);

                vertexWindEnabled = FindProperty("_VertexWindEnabled", props);
                WindFrequency = FindProperty("_WindFrequency", props);
                WindAmplitude = FindProperty("_WindAmplitude", props);
                WindWavelength = FindProperty("_WindWavelength", props);
                WindDirection = FindProperty("_WindDirection", props);

                if (isSSSEnable)
                {
                    //skin
                    translucencyMap = FindProperty("_TranslucencyMap", props);
                    bssrdfMap = FindProperty("_BSSRDFTex", props);
                    //power = FindProperty("_Power", props);
                    scale = FindProperty("_Scale", props);
                    diffuseNormalMapBlurBias = FindProperty("_BlurBumpBias", props);
                    blurStrength = FindProperty("_BlurStrength", props);

                    //curvaturePower = FindProperty("_CurvaturePower", props);
                    curvatureScale = FindProperty("_CurvatureScale", props);
                    skinShadowColor = FindProperty("_SkinShadowColor", props);
                    translucencyColor = FindProperty("_TranslucencyColor", props);
                }

            }
        }




        public override void OnGUI(MaterialEditor materialEditor, MaterialProperty[] props)
        {


            _materialEditor = materialEditor;
            Material material = _materialEditor.target as Material;


            //isChar = GUILayout.Toggle(isChar, "isChar");
            //isScene = GUILayout.Toggle(isScene, "isScene");
            //isCharSkin = GUILayout.Toggle(isCharSkin, "isCharSkin");
            //isCharPrePass = GUILayout.Toggle(isCharPrePass, "isCharPrePass");


            isSSSEnable = false;
            isCharBase = false;
            isSceneBase = false;
            isCharBase_PrePass = false;
            if (material.shader.name == OG_CHAR_BASE || material.shader.name == OG_CHAR_BASE_PREPASS )
            {
                isCharBase = true;
                skinEnable = FindProperty("_EnableSSS", props);
                isSSSEnable = skinEnable.floatValue == 1 ? true : false;
                if (material.shader.name == OG_CHAR_BASE_PREPASS)
                {
                    isCharBase_PrePass = true;
                }
            }
            else if (material.shader.name == OG_SCENE_BASE)
            {
                isSceneBase = true;
                isSSSEnable = false;
                isCharBase = false;
                isCharBase_PrePass = false;

            }


            //初始化shader 类型识别 和 参数查找
            if (initialized == false)
            {
                InitGUI();
                //由 透明角色prepass  shader 切到其他shader 默认非透明
                if (lastShaderName == OG_CHAR_BASE_PREPASS && material.shader.name != OG_CHAR_BASE_PREPASS)
                {
                    //先注释掉这个功能  因为运行状态运行Shader GUI 会强行修改材质 【【1.1.0.4152 r259424】【局外】【小小英雄展示】vivo河灵眼睛亮度与原型图不一致】http://tapd.oa.com/cchessTest/bugtrace/bugs/view?bug_id=1120428482081164580
                    //blendMode = FindProperty("_Mode", props);
                    //blendMode.floatValue = (float)BlendMode.Opaque;
                    //SetupMaterialWithBlendMode(material, (BlendMode)blendMode.floatValue);
                    //Debug.Log("lastShaderName");

                }
                if (material.shader.name == OG_CHAR_BASE_PREPASS)
                {
                    //先注释掉这个功能  因为运行状态运行Shader GUI 会强行修改材质 【【1.1.0.4152 r259424】【局外】【小小英雄展示】vivo河灵眼睛亮度与原型图不一致】http://tapd.oa.com/cchessTest/bugtrace/bugs/view?bug_id=1120428482081164580
                    //blendMode = FindProperty("_Mode", props);
                    //blendMode.floatValue = (float)BlendMode.Fade;
                    //SetupMaterialWithBlendMode(material, (BlendMode)blendMode.floatValue);
                    //Debug.Log("charBasePrePassName");

                }
                lastShaderName = material.shader.name;


                initialized = true;
                FindProperties(props);

                //初始化默认关闭风抖动
                //material.DisableKeyword("_VERTEX_WIND");
                Shader.SetGlobalFloat("_VertexWindLerp", 0);
            }


            isPBRShader = isCharBase || isCharBase_PrePass || isSceneBase;
            isOutGameShader = true;
            isInGameShader = !isOutGameShader;

            ShaderPropertiesGUI(material, props);
        }


        public void ShaderPropertiesGUI(Material material, MaterialProperty[] props)
        {

            EditorStyles.boldLabel.fontStyle = FontStyle.Bold;


            //如果不是没帧找的话，如果你撤销了数据 gui 不会及时刷新，造成误会
            isSSSEnable = false;
            isCharBase = false;
            isSceneBase = false;

            if (material.shader.name == OG_CHAR_BASE || material.shader.name == OG_CHAR_BASE_PREPASS )
            {
                isCharBase = true;
                skinEnable = FindProperty("_EnableSSS", props);
                isSSSEnable = skinEnable.floatValue == 1 ? true : false;
                if (material.shader.name == OG_CHAR_BASE_PREPASS)
                {
                    isCharBase_PrePass = true;
                }

            }
            else if (material.shader.name == OG_SCENE_BASE)
            {
                isSceneBase = true;
            }


            FindProperties(props);
            //如果不是没帧找的话，如果你撤销了数据 gui 不会及时刷新，造成误会

            if (TopBar(material, props))
            {
                BlendModePopupAndCull(material, true);

                //切换shader 后因为GUI直接就没有了所有并没有关闭Keyword，  需要在此关闭
                material.DisableKeyword("_PLANE_REFLECTION");
                //material.DisableKeyword("_METALLICGLOSSMAP");
                material.DisableKeyword("_DETAIL_MULX2");
                //material.DisableKeyword("_VERTEX_WIND");
                material.DisableKeyword("_OPAL");
                // material.DisableKeyword("_RIMTRANSPARENCY");
                material.DisableKeyword("_SKIN_ENABLED");
                material.DisableKeyword("_OPAL");
                material.DisableKeyword("_STAR");
                material.DisableKeyword("_SPARKLE");
                material.DisableKeyword("_STARLINE");
                refresh = true;
                initialized = false;
                //Debug.Log("change");
                return;
            }


            TileBar("混合模式和剔除", 0, false);

            if (isCharBase_PrePass)
            {
                //GUI.enabled = false;
                //BlendModePopup(material,false);
                BlendModePopupAndCull(material);
                //GUI.enabled = true;


                ////混合模式和剔除
                //cullmode.floatValue = (float)EditorGUILayout.Popup(Styles.culltex, (int)cullmode.floatValue, Styles.cullNames);



                //更换透明角色 prepass 强行设置透明
                //blendMode = FindProperty("_Mode", props);
                //_materialEditor.RegisterPropertyChangeUndo("Blend Mode");


            }
            else
            {
                BlendModePopupAndCull(material);
            }


            //贴图  ScaleOffset
            TileBar("贴图设置", 5);
            
            SafeTextureScaleOffset(_materialEditor, albedoMap);
            // _materialEditor.TextureScaleOffsetProperty(albedoMap);
            GUILayout.Space(SPACE_LINE);

            //BaseColor 设置 
            AlbedoArea(material);



            // //mask 贴图设置
            // if (isCharSkin)
            // {
            //     //3s 皮肤设置
            //     //SkinSSSArea(material);
            // }
            // else
            // {
            //     //PBR base mask 设置
            //     MetallicArea(material);
            // }

            MetallicArea(material);


            //法线贴图
            GUILayout.Space(SPACE_LINE);
            _materialEditor.TexturePropertySingleLine(Styles.normalMapText, bumpMap, bumpMap.textureValue != null ? bumpScale : null);
            _materialEditor.ShaderProperty(bumpBias, Styles.bumpBiasText, INDENTATION);



            //自发光
            EditorGUI.BeginChangeCheck();
            _materialEditor.TexturePropertySingleLine(new GUIContent("自发光"), emissionMap, emissionColor);
            if (EditorGUI.EndChangeCheck() || refresh)
            {
                SetKeyword(material, "_EMISSION", material.GetTexture("_Emission"));

            }


            //细节法线
            GUILayout.Space(SPACE_LINE);
            //EditorStyles.label.fontStyle = FontStyle.Bold;
            EditorGUI.BeginChangeCheck();
            SafeShaderProperty(_materialEditor, detailEnabled, Styles.detailEnabledText);
            // _materialEditor.ShaderProperty(detailEnabled, Styles.detailEnabledText);
            //EditorStyles.label.fontStyle = FontStyle.Normal;
            if (detailEnabled!=null)
            {
                if (detailEnabled.floatValue != 0)
                {
                    _materialEditor.TexturePropertySingleLine(Styles.detailNormalMapText, detailNormalMap, detailNormalMapScale);
                    // _materialEditor.TextureScaleOffsetProperty(detailNormalMap);
                    SafeTextureScaleOffset(_materialEditor, detailNormalMap);
                    GUILayout.Space(SPACE_LINE);
                }
            }
            

            if (EditorGUI.EndChangeCheck() || refresh)
            {
                
                if (material.HasProperty("_DetailEnabled") )
                {
                    SetKeyword(material, "_DETAIL_MULX2", material.GetFloat("_DetailEnabled") > 0 ? true : false);
                }
                
                
            }
            
           
            // Hair
            if (isCharBase)
            {
                GUILayout.Space(SPACE_LINE);
                EditorGUI.BeginChangeCheck();
                // _materialEditor.ShaderProperty(hairEnabled, Styles.hairEnabledText,INDENTATION);
                SafeShaderProperty(_materialEditor,hairEnabled, Styles.hairEnabledText,INDENTATION);
                if (hairEnabled.floatValue != 0)
                {
                    _materialEditor.TexturePropertySingleLine(Styles.hairSpecularMapText, hairSpecularMap);
                    // _materialEditor.TextureScaleOffsetProperty(hairSpecularMap);
                    SafeTextureScaleOffset(_materialEditor, hairSpecularMap);
                
                    // _materialEditor.ShaderProperty(hairSpecularColor1, Styles.hairSpecularColor1Text, INDENTATION);
                    SafeShaderProperty(_materialEditor,hairSpecularColor1, Styles.hairSpecularColor1Text, INDENTATION);
                    // _materialEditor.ShaderProperty(hairSpecularColor2, Styles.hairSpecularColor2Text, INDENTATION);
                    SafeShaderProperty(_materialEditor,hairSpecularColor2, Styles.hairSpecularColor2Text, INDENTATION);
                    // _materialEditor.ShaderProperty(hairOffset, Styles.hairOffsetText, INDENTATION);
                    SafeShaderProperty(_materialEditor,hairOffset, Styles.hairOffsetText, INDENTATION);
                    SafeShaderProperty(_materialEditor,hairOffsetIntensity, Styles.hairOffsetIntensityText, INDENTATION);
                    GUILayout.Space(SPACE_LINE);
                }
                
                
            }
            

            SafeShaderProperty(_materialEditor,diffuseLightingIntensity, new GUIContent("漫反射强度"));
            SafeShaderProperty(_materialEditor,specularLightingIntensity, new GUIContent("高光强度"));
            
            
            GUILayout.Space(SPACE_LINE);
            TileBar("特殊效果");
            //3s 开关
            if (isCharBase)
            {
                EditorGUI.BeginChangeCheck();
                skinEnable = FindProperty("_EnableSSS", props);
                // _materialEditor.ShaderProperty(skinEnable, new GUIContent("次表面散射 SSS"));
                SafeShaderProperty(_materialEditor, skinEnable, new GUIContent("次表面散射 SSS"));

                //editor 脚本的数据不知道怎么做undo 撤销 暂时就写成 实时刷新好了
                //isCharSkin = skinEnable.floatValue == 0 ? false : true;
                if (EditorGUI.EndChangeCheck() || refresh)
                {
                    if (skinEnable.floatValue == 1 && CheckEffectsCost())
                    {
                        skinEnable.floatValue = 0;
                        EditorUtility.DisplayDialog("警告！", costAlertStr1 + " 3s效果" + costAlertStr2, "ok");
                        material.DisableKeyword("_EnableSSS");
                    }

                    isSSSEnable = skinEnable.floatValue == 0 ? false : true;
                    if (isSSSEnable)
                    {
                        MaterialProperty SSSLUT = FindProperty("_BSSRDFTex", props);
                        if (SSSLUT.textureValue == null)
                        {
                            Texture2D tex = AssetDatabase.LoadAssetAtPath<Texture2D>("Assets/Art_TFT/TinyRes/CommonTexture/custom/SkinRampBRDF.png");
                            if (tex != null)
                            {
                                SSSLUT.textureValue = tex;
                            }
                        }
                        if (refresh)
                        {
                            material.EnableKeyword("_SKIN_ENABLED");
                        }

                    }
                }
                if (isSSSEnable)
                {
                    //3s 皮肤设置
                    SkinSSSArea(material);
                }
            }

            if (isCharBase)
            {

                //Opal --horacezhao
                GUILayout.Space(SPACE_LINE);
                //EditorStyles.label.fontStyle = FontStyle.Bold;
                OPAL_Enabled = FindProperty("_OPAL_Enabled", props);
                EditorGUI.BeginChangeCheck();
                // _materialEditor.ShaderProperty(OPAL_Enabled, new GUIContent("油膜 Opal"));
                SafeShaderProperty(_materialEditor, OPAL_Enabled, new GUIContent("油膜 Opal"));
                if (EditorGUI.EndChangeCheck())
                {
                    if (OPAL_Enabled.floatValue == 1 && CheckEffectsCost())
                    {
                        OPAL_Enabled.floatValue = 0;
                        EditorUtility.DisplayDialog("警告！", costAlertStr1 + " 油膜效果" + costAlertStr2, "ok");
                        material.DisableKeyword("_OPAL");
                    }

                }
                //EditorStyles.label.fontStyle = FontStyle.Normal;
                if (OPAL_Enabled.floatValue != 0)
                {
                    _materialEditor.TexturePropertySingleLine(new GUIContent("油膜图 Map"), FindProperty("_OpalMap", props));
                    // _materialEditor.ShaderProperty(FindProperty("_OpalDepth", props), new GUIContent("深度 Depth"), INDENTATION);
                    SafeShaderProperty(_materialEditor, FindProperty("_OpalDepth", props), new GUIContent("深度 Depth"), INDENTATION);
                    // _materialEditor.ShaderProperty(FindProperty("_OpalFrequency", props), new GUIContent("频率 Frequency"), INDENTATION);
                    SafeShaderProperty(_materialEditor, FindProperty("_OpalFrequency", props), new GUIContent("频率 Frequency"), INDENTATION);
                    // _materialEditor.ShaderProperty(FindProperty("_IOR", props), new GUIContent("折射 IOR"), INDENTATION);
                    SafeShaderProperty(_materialEditor, FindProperty("_IOR", props), new GUIContent("折射 IOR"), INDENTATION);
                    // _materialEditor.ShaderProperty(FindProperty("_OpalStrength", props), new GUIContent("强度 Strength"), INDENTATION);
                    SafeShaderProperty(_materialEditor, FindProperty("_OpalStrength", props), new GUIContent("强度 Strength"), INDENTATION);
                    // _materialEditor.ShaderProperty(FindProperty("_Level", props), new GUIContent("Opal Level"), INDENTATION);
                    SafeShaderProperty(_materialEditor, FindProperty("_Level", props), new GUIContent("Opal Level"), INDENTATION);
                    // _materialEditor.ShaderProperty(FindProperty("_ShineType", props), new GUIContent("Shine Type"), INDENTATION);
                    SafeShaderProperty(_materialEditor, FindProperty("_ShineType", props), new GUIContent("Shine Type"), INDENTATION);
                    GUILayout.Space(SPACE_LINE);
                    if (refresh)
                    {
                        material.EnableKeyword("_OPAL");
                    }

                }

                bool temp = GUI.enabled;
                if ((BlendMode)blendMode.floatValue == BlendMode.Opaque)
                {
                    GUI.enabled = false;
                }
                //Rim Transparency --horacezhao
                GUILayout.Space(SPACE_LINE);
                //EditorStyles.label.fontStyle = FontStyle.Bold;
                RimTransparency_Enabled = FindProperty("_RimTransparency_Enabled", props);
                // _materialEditor.ShaderProperty(RimTransparency_Enabled, new GUIContent("边缘透明 Rim Transparency"));
                SafeShaderProperty(_materialEditor, RimTransparency_Enabled, new GUIContent("边缘透明 Rim Transparency"));
                //EditorStyles.label.fontStyle = FontStyle.Normal;
                if (RimTransparency_Enabled.floatValue != 0)
                {
                    // _materialEditor.ShaderProperty(FindProperty("_RimAlpha", props), new GUIContent("透明度 Rim Alpha"), INDENTATION);
                    SafeShaderProperty(_materialEditor, FindProperty("_RimAlpha", props), new GUIContent("透明度 Rim Alpha"), INDENTATION);
                    // _materialEditor.ShaderProperty(FindProperty("_RimAlphaRange", props), new GUIContent("范围 Rim Alpha Range"), INDENTATION);
                    SafeShaderProperty(_materialEditor, FindProperty("_RimAlphaRange", props), new GUIContent("范围 Rim Alpha Range"), INDENTATION);
                    GUILayout.Space(SPACE_LINE);
                    // if (refresh)
                    // {
                    //     material.EnableKeyword("_RIMTRANSPARENCY");
                    // }
                }
                GUI.enabled = temp;

                //Stars Skin --horacezhao
                GUILayout.Space(SPACE_LINE);
                //EditorStyles.label.fontStyle = FontStyle.Bold;
                Star_Enabled = FindProperty("_Star_Enabled", props);
                EditorGUI.BeginChangeCheck();
                // _materialEditor.ShaderProperty(Star_Enabled, new GUIContent("星辰 Star"));
                SafeShaderProperty(_materialEditor, Star_Enabled, new GUIContent("星辰 Star"));
                if (EditorGUI.EndChangeCheck())
                {
                    if (EditorGUI.EndChangeCheck())
                    {
                        if (Star_Enabled.floatValue == 1 && CheckEffectsCost())
                        {
                            Star_Enabled.floatValue = 0;
                            EditorUtility.DisplayDialog("警告！", costAlertStr1 + " 星辰效果" + costAlertStr2, "ok");
                            material.DisableKeyword("_STAR");
                        }

                    }
                }
                //EditorStyles.label.fontStyle = FontStyle.Normal;
                if (Star_Enabled.floatValue != 0)
                {
                    MaterialProperty mapColor = FindProperty("_starcol", props);
                    MaterialProperty spaceMode = FindProperty("_SpaceMode", props);
                    // _materialEditor.ShaderProperty(spaceMode, new GUIContent("混合模式"), INDENTATION);
                    SafeShaderProperty(_materialEditor, spaceMode, new GUIContent("混合模式"), INDENTATION);
                    if (spaceMode.floatValue == 0)
                    {
                        _materialEditor.TexturePropertySingleLine(new GUIContent("星辰图(浅层视差)"), FindProperty("_starmap", props), mapColor);
                        // _materialEditor.ShaderProperty(FindProperty("_ParallaxDepth", props), new GUIContent("浅层视差深度"), INDENTATION);
                        SafeShaderProperty(_materialEditor, FindProperty("_ParallaxDepth", props), new GUIContent("浅层视差深度"), INDENTATION);
                        // _materialEditor.ShaderProperty(FindProperty("_StarSpeed01", props), new GUIContent("UV 缩放 与 速度"), INDENTATION);
                        SafeShaderProperty(_materialEditor, FindProperty("_StarSpeed01", props), new GUIContent("UV 缩放 与 速度"), INDENTATION);
                        MaterialProperty mapColor02 = FindProperty("_starcol02", props);
                        _materialEditor.TexturePropertySingleLine(new GUIContent("星辰图(深层视差)"), FindProperty("_starmap02", props), mapColor02);
                        // _materialEditor.ShaderProperty(FindProperty("_ParallaxDepth02", props), new GUIContent("深层视差深度"), INDENTATION);
                        SafeShaderProperty(_materialEditor, FindProperty("_ParallaxDepth02", props), new GUIContent("深层视差深度"), INDENTATION);
                        // _materialEditor.ShaderProperty(FindProperty("_StarSpeed02", props), new GUIContent("UV 缩放 与 速度"), INDENTATION);
                        SafeShaderProperty(_materialEditor, FindProperty("_StarSpeed02", props), new GUIContent("UV 缩放 与 速度"), INDENTATION);
                        // _materialEditor.ShaderProperty(FindProperty("_ModelPosScale", props), new GUIContent("模型空间视差深度缩放，w表示整体缩放"), INDENTATION);
                        SafeShaderProperty(_materialEditor, FindProperty("_ModelPosScale", props), new GUIContent("模型空间视差深度缩放，w表示整体缩放"), INDENTATION);
                        // _materialEditor.ShaderProperty(FindProperty("_ModelPosOffset", props), new GUIContent("模型空间视差深度中心偏移"), INDENTATION);
                        SafeShaderProperty(_materialEditor, FindProperty("_ModelPosOffset", props), new GUIContent("模型空间视差深度中心偏移"), INDENTATION);
                    }
                    else
                    {
                        _materialEditor.TexturePropertySingleLine(new GUIContent("星辰图(屏幕空间)"), FindProperty("_starmap", props), mapColor);
                        // _materialEditor.ShaderProperty(FindProperty("_StarSpeed01", props), new GUIContent("UV 缩放 与 速度"), INDENTATION);
                        SafeShaderProperty(_materialEditor, FindProperty("_StarSpeed01", props), new GUIContent("UV 缩放 与 速度"), INDENTATION);
                    }
                    
                    if (refresh)
                    {
                        material.EnableKeyword("_STAR");
                    }
                }

                //Sparkle --lvanchen
                GUILayout.Space(SPACE_LINE);
                Sparkle_Enabled = FindProperty("_Sparkle_Enabled", props);
                EditorGUI.BeginChangeCheck();
                // _materialEditor.ShaderProperty(Sparkle_Enabled, new GUIContent("闪光 Sparkle"));
                SafeShaderProperty(_materialEditor, Sparkle_Enabled, new GUIContent("闪光 Sparkle"));
                if (EditorGUI.EndChangeCheck())
                {
                    if (EditorGUI.EndChangeCheck())
                    {
                        if (Sparkle_Enabled.floatValue == 1 && CheckEffectsCost())
                        {
                            Sparkle_Enabled.floatValue = 0;
                            EditorUtility.DisplayDialog("警告！", costAlertStr1 + " 闪光效果" + costAlertStr2, "ok");
                            material.DisableKeyword("_SPARKLE");
                        }

                    }
                }
                if (Sparkle_Enabled.floatValue != 0)
                {
                    MaterialProperty sparkleColor = FindProperty("_SparkleColor", props);
                    MaterialProperty sparkleReverse = FindProperty("_SparkleReverse", props);
                    // _materialEditor.ShaderProperty(sparkleReverse, new GUIContent("是否反向闪光图（0为默认，1为反向）"), INDENTATION);
                    SafeShaderProperty(_materialEditor, sparkleReverse, new GUIContent("是否反向闪光图（0为默认，1为反向）"), INDENTATION);
                    _materialEditor.TexturePropertySingleLine(new GUIContent("闪光图", "金属度图A通道作为Mask"), FindProperty("_SparkleTex", props), sparkleColor);
                    // _materialEditor.TextureScaleOffsetProperty(FindProperty("_SparkleTex", props));
                    SafeTextureScaleOffset(_materialEditor, FindProperty("_SparkleTex", props));
                    if (refresh)
                    {
                        material.EnableKeyword("_SPARKLE");
                    }
                }
                   //Sparkle --lvanchn
                GUILayout.Space(SPACE_LINE);
                SparkleView_Enabled = FindProperty("_SparkleView_Enabled", props);
                EditorGUI.BeginChangeCheck();
                // _materialEditor.ShaderProperty(SparkleView_Enabled, new GUIContent("闪光基于视角 Sparkle"));
                SafeShaderProperty(_materialEditor, SparkleView_Enabled, new GUIContent("闪光基于视角 Sparkle"));
                if (EditorGUI.EndChangeCheck())
                {
                    if (EditorGUI.EndChangeCheck())
                    {
                        if (SparkleView_Enabled.floatValue == 1 && CheckEffectsCost())
                        {
                            SparkleView_Enabled.floatValue = 0;
                            EditorUtility.DisplayDialog("警告！", costAlertStr1 + " 闪光效果基于视角" + costAlertStr2, "ok");
                            material.DisableKeyword("_SPARKLEVIEW");
                        }

                    }
                }
                if (SparkleView_Enabled.floatValue != 0)
                {
                    MaterialProperty sparkleColor = FindProperty("_SparkleColor01", props);
                    _materialEditor.TexturePropertySingleLine(new GUIContent("闪光图", "金属度图A通道作为Mask"), FindProperty("_SparkleViewTex01", props), sparkleColor);
                    // _materialEditor.TextureScaleOffsetProperty(FindProperty("_SparkleViewTex01", props));
                    SafeTextureScaleOffset(_materialEditor, FindProperty("_SparkleViewTex01", props));
                     MaterialProperty sparkleColor02 = FindProperty("_SparkleColor02", props);
                     _materialEditor.TexturePropertySingleLine(new GUIContent("闪光图", "金属度图A通道作为Mask"), FindProperty("_SparkleViewTex02", props), sparkleColor02);
                    // _materialEditor.TextureScaleOffsetProperty(FindProperty("_SparkleViewTex02", props));
                    SafeTextureScaleOffset(_materialEditor, FindProperty("_SparkleViewTex02", props));
                    
                     // _materialEditor.ShaderProperty(FindProperty("_SparklePow", props), new GUIContent("珠光范围"), INDENTATION);
                    SafeShaderProperty(_materialEditor, FindProperty("_SparklePow", props), new GUIContent("珠光范围"), INDENTATION);
                     // _materialEditor.ShaderProperty(FindProperty("_SparkleSpeed", props), new GUIContent("闪烁速度"), INDENTATION);
                    SafeShaderProperty(_materialEditor, FindProperty("_SparkleSpeed", props), new GUIContent("闪烁速度"), INDENTATION);
                    // _materialEditor.ShaderProperty(FindProperty("_Gem_Enabled", props), new GUIContent("钻石"), INDENTATION);
                    SafeShaderProperty(_materialEditor, FindProperty("_Gem_Enabled", props), new GUIContent("钻石"), INDENTATION);
                    // _materialEditor.ShaderProperty(FindProperty("_UseGemMask", props), new GUIContent("使用通用Mask"), INDENTATION);
                    SafeShaderProperty(_materialEditor, FindProperty("_UseGemMask", props), new GUIContent("使用通用Mask"), INDENTATION);
                    // _materialEditor.ShaderProperty(FindProperty("_GemMin", props), new GUIContent("钻石范围Min"), INDENTATION);
                    SafeShaderProperty(_materialEditor, FindProperty("_GemMin", props), new GUIContent("钻石范围Min"), INDENTATION);
                    // _materialEditor.ShaderProperty(FindProperty("_GemMax", props), new GUIContent("钻石范围Max"), INDENTATION);
                    SafeShaderProperty(_materialEditor, FindProperty("_GemMax", props), new GUIContent("钻石范围Max"), INDENTATION);
                    // _materialEditor.ShaderProperty(FindProperty("_GemVector", props), new GUIContent("钻石反射方向"), INDENTATION);
                    SafeShaderProperty(_materialEditor, FindProperty("_GemVector", props), new GUIContent("钻石反射方向"), INDENTATION);
                    // _materialEditor.ShaderProperty(FindProperty("_IfFlow", props), new GUIContent("流动"), INDENTATION);
                    SafeShaderProperty(_materialEditor, FindProperty("_IfFlow", props), new GUIContent("流动"), INDENTATION);
                    // _materialEditor.ShaderProperty(FindProperty("_FlowSpeed", props), new GUIContent("01XY流动;02XY流动"), INDENTATION);
                    SafeShaderProperty(_materialEditor, FindProperty("_FlowSpeed", props), new GUIContent("01XY流动;02XY流动"), INDENTATION);
                    if (refresh)
                    {
                        material.EnableKeyword("_SPARKLEVIEW");
                    }
                }

                //StarLine --lvanchen
                GUILayout.Space(SPACE_LINE);
                StarLine_Enabled = FindProperty("_StarLine_Enabled", props);
                EditorGUI.BeginChangeCheck();
                // _materialEditor.ShaderProperty(StarLine_Enabled, new GUIContent("星座线 StarLine"));
                SafeShaderProperty(_materialEditor, StarLine_Enabled, new GUIContent("星座线 StarLine"));
                if (EditorGUI.EndChangeCheck())
                {
                    if (EditorGUI.EndChangeCheck())
                    {
                        if (StarLine_Enabled.floatValue == 1 && CheckEffectsCost())
                        {
                            StarLine_Enabled.floatValue = 0;
                            EditorUtility.DisplayDialog("警告！", costAlertStr1 + " 星座线" + costAlertStr2, "ok");
                            material.DisableKeyword("_STARLINE");
                        
                        }

                    }
                }
                if (StarLine_Enabled.floatValue != 0)
                {
                    _materialEditor.TexturePropertySingleLine(new GUIContent("星座连接Mask", "R通道作为Mask"), FindProperty("_StarLineMask", props));
                    // _materialEditor.ShaderProperty(FindProperty("_StarLine", props), new GUIContent("星座连接缩放(XY)和速度(ZW)"), INDENTATION);
                    SafeShaderProperty(_materialEditor, FindProperty("_StarLine", props), new GUIContent("星座连接缩放(XY)和速度(ZW)"), INDENTATION);
                    if (refresh)
                    {
                        material.EnableKeyword("_STARLINE");
                    }
                }


            }





            // 软阴影
            //if (isChar && !isCharPrePass || isScene)
            //{
            //    UniqueShadow_Enabled = FindProperty("_UniqueShadow", props);
            //    _materialEditor.ShaderProperty(UniqueShadow_Enabled, new GUIContent("软阴影 UniqueShadow"));
            //}

            //边缘光 和 风抖动
            if (isCharBase)
            {
                var rimProEnable = FindProperty("_RimProEnable", props);//.floatValue == 1 ? true : false

                if (rimProEnable.floatValue == 1 && !isDebugMode)
                {
                    GUI.enabled = false;

                }
                RimLightArea(material, props);
                GUI.enabled = true;

                RimProLightArea(material, props);
                VertexWindArea(material, props);
            }
            else
            {

                material.DisableKeyword("_RIM");

            }

            if (isSceneBase)
            {
                //平面镜面反射
                PlanerReflectionArea(material, props);
            }
            else
            {

                material.DisableKeyword("_PLANE_REFLECTION");
            }


            //
            TileBar("特效设置");
            //if (isChar)
            //{
            //    _materialEditor.ShaderProperty(rimEffect, new GUIContent("作用于特效"));
            //}
            if (isSceneBase)
            {

                bool tempEnable = GUI.enabled;
                if (!isDebugMode)
                {
                    GUI.enabled = false;
                }
                //特效变黑功能
                // _materialEditor.ShaderProperty(FindProperty("_dark", props), new GUIContent("整体变黑"));
                SafeShaderProperty(_materialEditor, FindProperty("_dark", props), new GUIContent("整体变黑")); 
                GUI.enabled = tempEnable;
            }
            //【ID864230841】局外爆炸黑白闪	--horacezhao
            //EditorStyles.label.fontStyle = FontStyle.Bold;
            BOOM = FindProperty("_boom", props,false);
            SafeShaderProperty(_materialEditor, BOOM, new GUIContent("爆炸黑白闪")); // _materialEditor.ShaderProperty(BOOM, new GUIContent("爆炸黑白闪"));
            //EditorStyles.label.fontStyle = FontStyle.Normal;
            if (BOOM != null)
            {
                if (BOOM.floatValue != 0)
                {
                    MaterialProperty BloomDir = FindProperty("_BoomDir", props);
                    SafeShaderProperty(_materialEditor, BloomDir, new GUIContent("方向"),
                        INDENTATION);
                    // _materialEditor.ShaderProperty(FindProperty("_BoomDir", props), new GUIContent("方向"), INDENTATION);
                }

            }
            
            //Flow 流光效果  --lvanchen
            if (isCharBase || isCharBase_PrePass)
            {
                FLOW_Pattern = FindProperty("_Flow_Pattern_Enabled", props,false);
                SafeShaderProperty(_materialEditor, FLOW_Pattern, new GUIContent("流光（图案）")); // _materialEditor.ShaderProperty(FLOW_Pattern, new GUIContent("流光（图案）"));
                if (FLOW_Pattern != null)
                {
                    if (FLOW_Pattern.floatValue != 0)
                    {
                        _materialEditor.TexturePropertySingleLine(new GUIContent("流动图案"), FindProperty("_FlowPattern", props));
                        SafeTextureScaleOffset(_materialEditor, FindProperty("_FlowPattern", props));// _materialEditor.TextureScaleOffsetProperty(FindProperty("_FlowPattern", props));
                        SafeShaderProperty(_materialEditor, FindProperty("_Pattern01Color", props), new GUIContent("颜色01（图案R通道）"), INDENTATION);
                        //_materialEditor.ShaderProperty(FindProperty("_Pattern01Color", props), new GUIContent("颜色01（图案R通道）"), INDENTATION);
                        SafeShaderProperty(_materialEditor,FindProperty("_Pattern02Color", props),new GUIContent("颜色02（图案G通道）"), INDENTATION);
                        // _materialEditor.ShaderProperty(FindProperty("_Pattern02Color", props), new GUIContent("颜色02（图案G通道）"), INDENTATION);
                        _materialEditor.TexturePropertySingleLine(new GUIContent("流动形状"), FindProperty("_FlowMaskTex", props));
                        SafeShaderProperty(_materialEditor,FindProperty("_FlowMaskSpeed", props), new GUIContent("流动速度"), INDENTATION);
                        // _materialEditor.ShaderProperty(FindProperty("_FlowMaskSpeed", props), new GUIContent("流动速度"), INDENTATION);
                        
                        GUILayout.Label("流光遮罩(金属度贴图A通道，黑色区域表示没有流光)", EditorStyles.label);
                        if (refresh)
                        {
                            material.EnableKeyword("_FLOWADD");
                        }

                    }
                }
            }
 
            //顶点偏移溶解 

            if (isCharBase || isCharBase_PrePass)
            {
                GUILayout.BeginVertical();
                dissolveEnabled = FindProperty("_DissolveEnabled", props,false);
                SafeShaderProperty(_materialEditor,dissolveEnabled, new GUIContent("顶点偏移溶解特效"));
                // _materialEditor.ShaderProperty(dissolveEnabled, new GUIContent("顶点偏移溶解特效"));

                if (dissolveEnabled != null)
                {
                    if (dissolveEnabled.floatValue == 1f)
                    {
                        // _materialEditor.ShaderProperty(FindProperty("_DisAmount", props), new GUIContent("溶解进度 DissolveAmount"), INDENTATION);
                        SafeShaderProperty(_materialEditor,FindProperty("_DisAmount", props), new GUIContent("溶解进度 DissolveAmount"), INDENTATION);
                        
                        _materialEditor.TexturePropertySingleLine(new GUIContent("溶解方向贴图 DirectionTex"), FindProperty("_DirectionTex", props));
                        // _materialEditor.ShaderProperty(FindProperty("_DisColor", props), new GUIContent("溶解边缘颜色 DissolveColor"), INDENTATION);
                        SafeShaderProperty(_materialEditor,FindProperty("_DisColor", props), new GUIContent("溶解边缘颜色 DissolveColor"), INDENTATION);
                        
                        _materialEditor.TexturePropertySingleLine(new GUIContent("边缘噪点贴图 DissolveNoise"), FindProperty("_DissolveNoise", props));
                        // _materialEditor.ShaderProperty(FindProperty("_DissolveNoiseScale", props), new GUIContent("噪点贴图缩放 DissolveNoiseScale"), INDENTATION);
                        SafeShaderProperty(_materialEditor,FindProperty("_DissolveNoiseScale", props), new GUIContent("噪点贴图缩放 DissolveNoiseScale"), INDENTATION);

                        // _materialEditor.ShaderProperty(FindProperty("_HeightScale", props), new GUIContent("偏移高度 HeightScale"), INDENTATION);
                        SafeShaderProperty(_materialEditor,FindProperty("_HeightScale", props), new GUIContent("偏移高度 HeightScale"), INDENTATION);
                        // _materialEditor.ShaderProperty(FindProperty("_DisplacementWidth", props), new GUIContent("偏移范围 DisplacementWidth"), INDENTATION);
                        SafeShaderProperty(_materialEditor,FindProperty("_DisplacementWidth", props), new GUIContent("偏移范围 DisplacementWidth"), INDENTATION);
                        // _materialEditor.ShaderProperty(FindProperty("_DissolveCutoff", props), new GUIContent("裁切 DissolveCutoff"), INDENTATION);
                        SafeShaderProperty(_materialEditor,FindProperty("_DissolveCutoff", props), new GUIContent("裁切 DissolveCutoff"), INDENTATION);
                        // _materialEditor.ShaderProperty(FindProperty("_Smoothness", props), new GUIContent("裁切平滑 CutOffSmoothness"), INDENTATION);
                        SafeShaderProperty(_materialEditor,FindProperty("_Smoothness", props), new GUIContent("裁切平滑 CutOffSmoothness"), INDENTATION);

                        if (refresh)
                        {
                            material.EnableKeyword("_DISSOLVEEFFECT");
                        }
                    }
                }
                
            }

            TileBar("Advance Options");
            if (isDebugMode)
            {
                _materialEditor.EnableInstancingField();

            }

            //烘焙算面计算
            if (isSceneBase)
            {
                _materialEditor.DoubleSidedGIField();
            }
            //排序
            _materialEditor.RenderQueueField();
            refresh = false;

        }


        public static string costAlertStr1 = "无法开启 ";// !\n开启效果过多.请削减其他效果在开启.";
        public static string costAlertStr2 = "!\n开启效果过多.请削减其他效果在开启.\n 如必要请找 TA@fengxzeng(曾建锋) 或 @lvanchen(陈俊潮) 开启！";
        public static int MAX_COST_TEX = 5;

        public bool CheckEffectsCost()
        {
            bool isCost = false;
            // 限制能开启的效果最大数量 避免使用太多的贴图
            Dictionary<MaterialProperty, int> effectDics = new Dictionary<MaterialProperty, int>();
            effectDics[skinEnable] = 2;
            effectDics[OPAL_Enabled] = 2;
            //effectDics[RimTransparency_Enabled] = 3
            effectDics[Star_Enabled] = 2;
            effectDics[Sparkle_Enabled] = 3;
            int costCount = 0;
            foreach (var item in effectDics.Keys)
            {
                if (item.floatValue == 1)
                {
                    costCount += effectDics[item];
                }
            }

            if (costCount > MAX_COST_TEX)
            {
                isCost = true;
            }
            if (isExpertMode && isDebugMode && debugCheckIndex == (int)DebugCheckType.vertexTangent)
            {
                isCost = false;
            }
            return isCost;
        }


        void BlendModePopupAndCull(Material material)
        {
            BlendModePopupAndCull(material, false);
        }

        void BlendModePopupAndCull(Material material, bool up)
        {
            BlendModePopup(material, up);
            cullmode.floatValue = (float)EditorGUILayout.Popup(Styles.culltex, (int)cullmode.floatValue, Styles.cullNames);
            if(vrsMode!= null)
            {
                vrsMode.floatValue = (float)EditorGUILayout.Popup(Styles.vrsText, (int)vrsMode.floatValue, Styles.vrsNames);
            }
            //Require:UnityEngine.Rendering.VariableRateShadingMode
            //var enumValue = (UnityEngine.Rendering.VariableRateShadingMode)((int)vrsMode.floatValue);
            //enumValue = (UnityEngine.Rendering.VariableRateShadingMode)EditorGUILayout.EnumPopup(Styles.vrsText, enumValue);
            //vrsMode.floatValue = (float)enumValue;
        }

        void BlendModePopup(Material material, bool up)
        {


            Color col = GUI.color;

            if (blendMode.floatValue != 0)
            {
                GUI.color = Color.red;
            }
            EditorGUI.showMixedValue = blendMode.hasMixedValue;
            var mode = (BlendMode)blendMode.floatValue;

            EditorGUI.BeginChangeCheck();
            GUILayout.BeginHorizontal();
            mode = (BlendMode)EditorGUILayout.Popup(Styles.blendMode, (int)mode, Styles.blendNames);



            int tempInt = 0;
            int rq = 0;
            int curRQ = GetBlendModeRenderQueue(mode);

            if (isDebugMode)
            {
                renderQueueList = new Dictionary<string, int>();
                renderQueueList.Add("场景透明合并特效", 2999);
                if (renderQueueOffset.floatValue != 0 && (mode == BlendMode.Fade || mode == BlendMode.Cutout))
                {
                    GUI.color = Color.red;
                }


                string rqs = curRQ.ToString();
                if ((mode == BlendMode.Fade || mode == BlendMode.Transparent || mode == BlendMode.Cutout))
                {
                    int rqo = (int)renderQueueOffset.floatValue;
                    if (rqo != 0)
                    {
                        if (rqo > 0)
                        {
                            rqs += "+";
                        }
                        else
                        {
                            rqs += "-";
                        }
                        rqs += Math.Abs(rqo).ToString();

                    }
                    else
                    {

                        rqs = "";
                    }
                }

                GUILayout.Label("排序:" + rqs);//,GUILayout.Width(60));
                GUI.color = col;
                //string l = "排序:" + (material.renderQueue + renderQueueOffset.floatValue).ToString();


                if ((mode == BlendMode.Fade || mode == BlendMode.Transparent || mode == BlendMode.Cutout))
                {


                    //bool temp = GUI.enabled;
                    //if (mode != BlendMode.Fade)
                    //{
                    //    GUI.enabled = false;
                    //}

                    if ((mode == BlendMode.Fade || mode == BlendMode.Transparent  || mode == BlendMode.Cutout))
                    {
                        tempInt = curRQ + (int)renderQueueOffset.floatValue;
                        rq = EditorGUILayout.DelayedIntField(tempInt);
                    }
                    else
                    {
                        tempInt = curRQ;
                    }

                    //底
                    if (GUILayout.Button("-", GUILayout.Width(20)))
                    {
                        if (rq - 1 > -1)
                        {
                            rq -= 1;

                        }
                    }
                    //顶
                    if (GUILayout.Button("+", GUILayout.Width(20)))
                    {
                        if (rq + 1 < 5000)
                        {
                            rq += 1;

                        }
                    }
                    if (GUILayout.Button(EditorGUIUtility.IconContent("Preset.Context"), GUILayout.Width(30)))
                    {
                        GenericMenu toolsMenu = new GenericMenu();
                        toolsMenu.AddItem(new GUIContent("场景合并特效层级: 3000-1 "), false, SetRendeQueueOffset, -1);
                        toolsMenu.AddItem(new GUIContent("透明默认层级: 3000"), false, SetRendeQueueOffset, 0);
                        toolsMenu.AddSeparator("");
                        toolsMenu.DropDown(new Rect(Event.current.mousePosition.x, Event.current.mousePosition.y, 0, 0));
                    }
                    if (GUILayout.Button("R", GUILayout.Width(20)))
                    {
                        rq = curRQ;

                    }

                }



            }

            GUILayout.EndHorizontal();

            if (renderQueueOffset.floatValue != 0 && (mode == BlendMode.Fade || mode == BlendMode.Transparent || mode == BlendMode.Cutout))
            {

                EditorGUILayout.HelpBox("注意排序修改！", MessageType.Warning);
            }

            if (isCharBase_PrePass && isDebugMode)
            {
                _materialEditor.ShaderProperty(prepassAlpha, new GUIContent("背面透明度"));

            }
            GUI.color = col;

            if (EditorGUI.EndChangeCheck() || refresh)
            {
                //_materialEditor.RegisterPropertyChangeUndo("Blend Mode");
                //blendMode.floatValue = (float)mode;
                //SetupMaterialWithBlendMode(material, (BlendMode)material.GetFloat("_Mode"));

                _materialEditor.RegisterPropertyChangeUndo("Blend Mode");
                blendMode.floatValue = (float)mode;

                if ((mode == BlendMode.Fade || mode == BlendMode.Transparent  || mode == BlendMode.Cutout) && isDebugMode)
                {
                    if (rq < 5001 && rq > -1)
                    {

                        renderQueueOffset.floatValue = rq - curRQ;

                    }
                    SetupMaterialWithBlendMode(material, (BlendMode)mode, (int)renderQueueOffset.floatValue);

                }
                else
                {

                    SetupMaterialWithBlendMode(material, (BlendMode)mode, 0);

                }


            }

            EditorGUI.showMixedValue = false;
            EditorStyles.label.fontStyle = FontStyle.Normal;
        }

        public Dictionary<string, int> renderQueueList = new Dictionary<string, int>();

        void BlendModePopupDebug(Material material, bool up)
        {
            renderQueueList = new Dictionary<string, int>();
            renderQueueList.Add("场景透明合并特效", 2999);

            EditorStyles.label.fontStyle = FontStyle.Bold;
            EditorGUI.showMixedValue = blendMode.hasMixedValue;
            var mode = (BlendMode)blendMode.floatValue;

            GUILayout.BeginHorizontal();

            EditorGUI.BeginChangeCheck();
            mode = (BlendMode)EditorGUILayout.Popup(Styles.blendMode, (int)mode, Styles.blendNames);




            Color col = GUI.color;
            if (renderQueueOffset.floatValue != 0 && mode == BlendMode.Fade)
            {
                GUI.color = Color.red;
            }
            int curRQ = GetBlendModeRenderQueue(mode);

            string rqs = curRQ.ToString();
            if (mode == BlendMode.Fade)
            {
                int rqo = (int)renderQueueOffset.floatValue;
                if (rqo != 0)
                {
                    if (rqo > 0)
                    {
                        rqs += "+";
                    }
                    else
                    {
                        rqs += "-";
                    }
                    rqs += Math.Abs(rqo).ToString();

                }
                else
                {

                    rqs = "";
                }
            }

            GUILayout.Label("排序:" + rqs);//,GUILayout.Width(60));
            GUI.color = col;
            //string l = "排序:" + (material.renderQueue + renderQueueOffset.floatValue).ToString();
            int tempInt = 0;
            int rq = 0;

            if (mode == BlendMode.Fade)
            {


                //bool temp = GUI.enabled;
                //if (mode != BlendMode.Fade)
                //{
                //    GUI.enabled = false;
                //}

                if (mode == BlendMode.Fade)
                {
                    tempInt = curRQ + (int)renderQueueOffset.floatValue;
                    rq = EditorGUILayout.DelayedIntField(tempInt);
                }
                else
                {
                    tempInt = curRQ;
                }

                //底
                if (GUILayout.Button("-", GUILayout.Width(20)))
                {
                    if (rq - 1 > -1)
                    {
                        rq -= 1;

                    }
                }
                //顶
                if (GUILayout.Button("+", GUILayout.Width(20)))
                {
                    if (rq + 1 < 5000)
                    {
                        rq += 1;

                    }
                }
                if (GUILayout.Button(EditorGUIUtility.IconContent("Preset.Context"), GUILayout.Width(30)))
                {
                    GenericMenu toolsMenu = new GenericMenu();
                    toolsMenu.AddItem(new GUIContent("场景合并特效层级: 3000-1 "), false, SetRendeQueueOffset, -1);
                    toolsMenu.AddItem(new GUIContent("透明默认层级: 3000"), false, SetRendeQueueOffset, 0);
                    toolsMenu.AddSeparator("");
                    toolsMenu.DropDown(new Rect(Event.current.mousePosition.x, Event.current.mousePosition.y, 0, 0));
                }
                if (GUILayout.Button("R", GUILayout.Width(20)))
                {
                    rq = curRQ;

                }

            }

            if (EditorGUI.EndChangeCheck())
            {

                _materialEditor.RegisterPropertyChangeUndo("Blend Mode");
                blendMode.floatValue = (float)mode;

                if (mode == BlendMode.Fade)
                {
                    if (rq < 5001 && rq > -1)
                    {

                        renderQueueOffset.floatValue = rq - curRQ;

                    }
                    SetupMaterialWithBlendMode(material, (BlendMode)mode, (int)renderQueueOffset.floatValue);

                }
                else
                {

                    SetupMaterialWithBlendMode(material, (BlendMode)mode, 0);

                }

            }





            GUILayout.EndHorizontal();
            if (renderQueueOffset.floatValue != 0 && (mode == BlendMode.Fade || mode == BlendMode.Cutout))
            {

                EditorGUILayout.HelpBox("注意排序修改！", MessageType.Warning);
            }
            //GUI.enabled = temp;

            EditorGUI.showMixedValue = false;


            //EditorStyles.label.fontStyle = FontStyle.Bold;
            //var cull = cullmode.floatValue;
            //cullmode.floatValue = EditorGUILayout.Popup(Styles.culltex, (int)cull, Styles.cullNames);
            //EditorStyles.label.fontStyle = FontStyle.Normal;





        }


        public void SetRendeQueueOffset(object off)
        {

            renderQueueOffset.floatValue = (int)off;
        }


        void AlbedoArea(Material material)
        {
            _materialEditor.TexturePropertySingleLine(Styles.albedoTex, albedoMap, color);
            if ((BlendMode)material.GetFloat("_Mode") == BlendMode.Cutout)
            {
                INDENTATION = 2;
                _materialEditor.ShaderProperty(alphaCutoff, Styles.alphaCutoffText.text, INDENTATION);
            }
        }


        void MetallicArea(Material material)
        {
            bool hasGlossMap = metallicMap.textureValue != null;
            EditorGUI.BeginChangeCheck();

            _materialEditor.TexturePropertySingleLine(Styles.metallicMapText, metallicMap, hasGlossMap ? null : metallic);
            //_materialEditor.TexturePropertySingleLine(Styles.metallicMapText, metallicMap,  metallic);
            INDENTATION = 2;
            //_materialEditor.ShaderProperty(hasGlossMap ? smoothnessScale : smoothnessScale, hasGlossMap ? Styles.smoothnessScaleText : Styles.smoothnessText, INDENTATION);
            // _materialEditor.ShaderProperty(smoothnessScale, Styles.smoothnessScaleText, INDENTATION);
            SafeShaderProperty(_materialEditor, smoothnessScale, Styles.smoothnessScaleText, INDENTATION);
            if (hasGlossMap)
                _materialEditor.ShaderProperty(aoIntensity, Styles.AOText, INDENTATION);



            if (EditorGUI.EndChangeCheck() || refresh)
            {
                //_materialEditor.RegisterPropertyChangeUndo("_MetallicMap");
                SetKeyword(material, "_METALLICGLOSSMAP", material.GetTexture("_MetallicMap"));

            }

        }

        void SkinSSSArea(Material material)
        {
            GUILayout.BeginVertical();// GUI.skin.box);
            //bool hasGlossMap = metallicMap.textureValue != null;
            //EditorGUI.BeginChangeCheck();
            //_materialEditor.TexturePropertySingleLine(Styles.skinText, metallicMap);
            _materialEditor.TexturePropertySingleLine(Styles.skinText, translucencyMap, null);

            _materialEditor.TexturePropertySingleLine(Styles.bssrdfMapText, bssrdfMap);

            // _materialEditor.ShaderProperty(skinShadowColor, Styles.skinShadowColorText, INDENTATION);
            SafeShaderProperty(_materialEditor, skinShadowColor, Styles.skinShadowColorText, INDENTATION);
            INDENTATION = 2;
            //_materialEditor.ShaderProperty(hasGlossMap ? smoothnessScale : smoothness, hasGlossMap ? Styles.smoothnessScaleText : Styles.smoothnessText, INDENTATION);
            // _materialEditor.ShaderProperty(smoothnessScale,  Styles.smoothnessScaleText , INDENTATION);
            // _materialEditor.ShaderProperty(aoIntensity, Styles.AOText, INDENTATION);
            GUILayout.Space(SPACE_LINE);


            GUILayout.Label("   厚度图(背光透光部分)", EditorStyles.boldLabel);
            //_materialEditor.ShaderProperty(power, Styles.powerText, INDENTATION);
            // _materialEditor.ShaderProperty(scale, Styles.scaleText, INDENTATION);
            SafeShaderProperty(_materialEditor, scale, Styles.scaleText, INDENTATION);
            // _materialEditor.ShaderProperty(translucencyColor, Styles.translucencyColorText, INDENTATION);
            SafeShaderProperty(_materialEditor,translucencyColor, Styles.translucencyColorText, INDENTATION);
            GUILayout.Space(SPACE_LINE);
            GUILayout.Label("   曲率度(明暗交界线部分)", EditorStyles.boldLabel);
            //_materialEditor.ShaderProperty(curvaturePower, Styles.powerText, INDENTATION);
            // _materialEditor.ShaderProperty(curvatureScale, Styles.scaleText, INDENTATION);
            SafeShaderProperty(_materialEditor,curvatureScale, Styles.scaleText, INDENTATION);


            GUILayout.Space(SPACE_LINE);


            GUILayout.Space(SPACE_LINE);
            //GUILayout.Label(Styles.diffuseBumpSettingsText);
            // GUILayout.Label("   法线(模糊细节)", EditorStyles.boldLabel);
            // _materialEditor.ShaderProperty(diffuseNormalMapBlurBias, Styles.diffuseNormalMapBlurBiasText, INDENTATION);
            // _materialEditor.ShaderProperty(blurStrength, Styles.blurStrengthText, INDENTATION);
            //if(EditorGUI.EndChangeCheck()){

            //    _materialEditor.RegisterPropertyChangeUndo("3s");
            //}

            GUILayout.EndVertical();

        }


        bool RimLight = false;
        void RimLightArea(Material material, MaterialProperty[] props)
        {
            GUILayout.Space(SPACE_LINE);
            GUILayout.BeginVertical();// GUI.skin.box);
            //EditorStyles.label.fontStyle = FontStyle.Bold;
            RimLight = GUILayout.Toggle(RimLight, "边缘光RimLight", EditorStyles.foldout);
            //Rect rt = Rect.zero;
            //if (Event.current.type == EventType.Repaint)
            //{

            //    rt = GUILayoutUtility.GetLastRect();
            //    rt.x += 20;
            //}
            //EditorStyles.label.fontStyle = FontStyle.Normal;

            if (RimLight)
            {
                //GUILayout.Label("边缘光RimLight", EditorStyles.boldLabel);

                //EditorGUI.BeginChangeCheck();
                // _materialEditor.ShaderProperty(rimVector, new GUIContent("方向"), INDENTATION);
                SafeShaderProperty(_materialEditor,rimVector, new GUIContent("方向"), INDENTATION);
                //if (EditorGUI.EndChangeCheck())
                //{
                //_materialEditor.RegisterPropertyChangeUndo("rimVector Change");
                Vector3 v = new Vector3(rimVector.vectorValue.x, rimVector.vectorValue.y, rimVector.vectorValue.z).normalized;
                rimVector.vectorValue = new Vector4(v.x, v.y, v.z, 0);
                //}


                // _materialEditor.ShaderProperty(rimColor, new GUIContent("颜色"), INDENTATION);
                SafeShaderProperty(_materialEditor,rimColor, new GUIContent("颜色"), INDENTATION);
                // _materialEditor.ShaderProperty(rimPower, new GUIContent("范围"), INDENTATION);
                SafeShaderProperty(_materialEditor,rimPower, new GUIContent("范围"), INDENTATION);
                // _materialEditor.ShaderProperty(rimScale, new GUIContent("强度"), INDENTATION);
                SafeShaderProperty(_materialEditor,rimScale, new GUIContent("强度"), INDENTATION);

                bool rimEffectTemp = rimEffect.floatValue == 1;
                // _materialEditor.ShaderProperty(rimEffect, new GUIContent("作用于特效"), INDENTATION);
                SafeShaderProperty(_materialEditor,rimEffect, new GUIContent("作用于特效"), INDENTATION);


            }

            GUILayout.EndVertical();
            EditorGUI.indentLevel = 0;

        }
        bool sysChange = false;
        public static bool isSysChange = false;
        public static Material isSysChangeMat = null;

        static bool isSysRim = false;
        static float RIM_PRO = 0;
        static Vector4 RIM_DIR1 = Vector4.zero;
        static Color RIM_COL1 = Color.black;
        static float RIM_POW1 = 0;
        static float RIM_SAL1 = 0;

        static Vector4 RIM_DIR2 = Vector4.zero;
        static Color RIM_COL2 = Color.black;
        static float RIM_POW2 = 0;
        static float RIM_SAL2 = 0;

        static Vector4 RIM_MASK_CLAMP = new Vector4(0.6f, 0.8f, 1, 1);



        //检测顶点色用
        public bool isCheckedVertexColor = false;
        public bool isVertexColor = false;
        bool CheckVertexCoror()
        {
            bool isChecked = false;
            SkinnedMeshRenderer skm = Selection.activeGameObject.GetComponent<SkinnedMeshRenderer>();
            if (skm != null)
            {
                Mesh mh = new Mesh();
                skm.BakeMesh(mh);
                if (mh != null)
                {
                    if (mh.colors.Length > 0)
                    {
                        isChecked = true;
                    }
                }
            }
            //Debug.Log("检测下选中的模型是否有定点色！");
            return isChecked;
        }
   
        void RimProLightArea(Material material, MaterialProperty[] props)
        {

            // 没选中的情况下  提示下 同步不生效
            bool isSelect = false;
            Renderer selectRenderer = null;
            SkinnedMeshRenderer selectSkinRenderer = null;

            if (Selection.activeGameObject != null)
            {
                selectRenderer = Selection.activeGameObject.GetComponent<Renderer>();
                selectSkinRenderer = Selection.activeGameObject.GetComponent<SkinnedMeshRenderer>();
                if (selectRenderer != null)
                {
                    foreach (var item in selectRenderer.sharedMaterials)
                    {
                        if (item == material)
                        {
                            isSelect = true;
                        }
                    }
                }
            }



            GUILayout.Space(SPACE_LINE);
            GUILayout.BeginVertical();// GUI.skin.box);

            //EditorStyles.label.fontStyle = FontStyle.Bold;
            var rimProEnable = FindProperty("_RimProEnable", props);//.floatValue == 1 ? true : false
            var rimAddEnable = FindProperty("_RimAddEnable", props);
            EditorGUI.BeginChangeCheck();
            // _materialEditor.ShaderProperty(rimProEnable, new GUIContent("轮廓光 RIM_PRO"));
            SafeShaderProperty(_materialEditor,rimProEnable, new GUIContent("轮廓光 RIM_PRO"));
            GUI.color = Color.red;


            //打开的时候就检测一次
            if (EditorGUI.EndChangeCheck() && rimProEnable.floatValue == 1 && isSelect)
            {
                isCheckedVertexColor = true;
                isVertexColor = CheckVertexCoror();

            }
            var RimNoSH = FindProperty("_RimNoSH", props);

            //选中就检测一次
            if (!isCheckedVertexColor && rimProEnable.floatValue == 1 && isSelect)
            {
                isCheckedVertexColor = true;
                isVertexColor = CheckVertexCoror();
                RimNoSH.floatValue = isVertexColor ? 1 : 0;

            }
            if (!isVertexColor && rimProEnable.floatValue == 1)
            {
                string label = "模型未烘焙顶点色！ 请烘焙顶点色在使用轮轮廓光！@fengxzeng";
                EditorGUILayout.HelpBox(label, MessageType.Error);
                EditorGUILayout.HelpBox(label, MessageType.Error);
                EditorGUILayout.HelpBox(label, MessageType.Error);
                EditorGUILayout.HelpBox(label, MessageType.Error);
                EditorGUILayout.HelpBox(label, MessageType.Error);
                EditorGUILayout.HelpBox(label, MessageType.Error);
                EditorGUILayout.HelpBox(label, MessageType.Error);

            }
         
            //GUILayout.Toggle(isVertexColor, new GUIContent("isVertexColor"));
            //GUILayout.Toggle(isCheckedVertexColor, new GUIContent("isCheckedVertexColor"));





            if (rimProEnable.floatValue == 1f)
            {
                // _materialEditor.ShaderProperty(rimAddEnable, new GUIContent("风格化边缘光 RimAdd"));
                SafeShaderProperty(_materialEditor,rimAddEnable, new GUIContent("风格化边缘光 RimAdd"));
                // _materialEditor.ShaderProperty(RimNoSH, new GUIContent("模型烘焙过顶点色"));
                SafeShaderProperty(_materialEditor,RimNoSH, new GUIContent("模型烘焙过顶点色"));
                GUILayout.Label("仅限Set6资源使用！");
                GUILayout.Label("警告！ 非set6  禁止合并到分支！ 分支功能会混乱！");
                GUILayout.Label("警告！ 非set6  禁止合并到分支！ 分支功能会混乱！");
            }


            GUI.color = Color.white;
            return;
            GUILayout.BeginHorizontal();
            var rimDir = FindProperty("_RimDir1", props);
            var rimCol = FindProperty("_RimCol1", props);
            var rimPower = FindProperty("_RimPower1", props);
            var rimScale = FindProperty("_RimScale1", props);

            var rimDir2 = FindProperty("_RimDir2", props);
            var rimCol2 = FindProperty("_RimCol2", props);
            var rimPower2 = FindProperty("_RimPower2", props);
            var rimScale2 = FindProperty("_RimScale2", props);

            var rimMaskClamp = FindProperty("_RimMaskClamp", props);


            Color col = GUI.color;
            if (rimProEnable.floatValue == 1f)
            {
                if (!isSelect)
                {
                    GUI.enabled = false;
                }
                GUILayout.Label("      ", GUILayout.Width(30));
                EditorGUI.BeginChangeCheck();
                if (isSysChange && !sysChange)
                {
                    //GUI.enabled = false;
                }
                sysChange = GUILayout.Toggle(sysChange, new GUIContent("同步 轮廓光 参数"), GUI.skin.button, GUILayout.Width(200));
                GUI.enabled = true;

                if (EditorGUI.EndChangeCheck())
                {
                    isSysChange = sysChange;
                }
                if (GUILayout.Button("copy"))
                {
                    RIM_PRO = rimProEnable.floatValue;
                    RIM_DIR1 = rimDir.vectorValue;
                    RIM_COL1 = rimCol.colorValue;
                    RIM_POW1 = rimPower.floatValue;
                    RIM_SAL1 = rimScale.floatValue;

                    RIM_DIR2 = rimDir2.vectorValue;
                    RIM_COL2 = rimCol2.colorValue;
                    RIM_POW2 = rimPower2.floatValue;
                    RIM_SAL2 = rimScale2.floatValue;

                    RIM_MASK_CLAMP = rimMaskClamp.vectorValue;

                }
                if (GUILayout.Button("Paste"))
                {
                    rimProEnable.floatValue = RIM_PRO;
                    rimDir.vectorValue = RIM_DIR1;
                    rimCol.colorValue = RIM_COL1;
                    rimPower.floatValue = RIM_POW1;
                    rimScale.floatValue = RIM_SAL1;

                    rimDir2.vectorValue = RIM_DIR2;
                    rimCol2.colorValue = RIM_COL2;
                    rimPower2.floatValue = RIM_POW2;
                    rimScale2.floatValue = RIM_SAL2;

                    rimMaskClamp.vectorValue = RIM_MASK_CLAMP;

                }
                EditorGUI.BeginChangeCheck();
                _DebugRimPro = GUILayout.Toggle(_DebugRimPro, "遮蔽预览", GUI.skin.button);
                if (EditorGUI.EndChangeCheck())
                {
                    Shader.SetGlobalFloat("_DebugRimPro", _DebugRimPro ? 1 : 0);
                    //if (!_DebugRimPro)
                    //{
                    //    isDebugMode = false;
                    //}
                }

                if (GUILayout.Button("仅显示轮廓光"))
                {

                    isDebugPBRMode = true;
                    isDebugMode = !isDebugMode;
                    PBRDebugMode = 1;
                    debugCheckIndex = (int)DebugCheckType.RimLight;
                    debugCheckInput = (int)DebugCheckType.RimLight;
                    if (isDebugPBRMode)
                    {
                        Shader.SetGlobalFloat("_DebugCheckIndex", debugCheckIndex);
                        Shader.EnableKeyword("_DEBUG_CHANNEL_CHECK");
                    }
                    else
                    {
                        Shader.SetGlobalFloat("_DebugCheckIndex", -2);
                        Shader.DisableKeyword("_DEBUG_CHANNEL_CHECK");
                    }
                }
                GUI.enabled = true;

            }




            if (sysChange)
            {
                // 没选中的情况下  提示下 同步不生效

                if (isSelect)
                {
                    var mts = selectRenderer.sharedMaterials;
                    foreach (var item in mts)
                    {
                        if (item != null)
                        {
                            if (item.shader != null)
                            {

                                if (item != material && item.shader.name == material.shader.name)
                                {
                                    item.SetColor(rimCol.name, rimCol.colorValue);
                                    item.SetFloat(rimPower.name, rimPower.floatValue);
                                    item.SetFloat(rimScale.name, rimScale.floatValue);
                                    item.SetVector(rimDir.name, rimDir.vectorValue);
                                    //item.SetFloat(rimProEnable.name, rimProEnable.floatValue); 

                                    item.SetColor(rimCol2.name, rimCol2.colorValue);
                                    item.SetFloat(rimPower2.name, rimPower2.floatValue);
                                    item.SetFloat(rimScale2.name, rimScale2.floatValue);
                                    item.SetVector(rimDir2.name, rimDir2.vectorValue);

                                    item.SetVector(rimMaskClamp.name, rimMaskClamp.vectorValue);

                                }

                                if (isDebugMode) // 临时加入debug 模式 后续删除
                                {
                                    //if (!material.IsKeywordEnabled("_DETAIL_MULX2"))
                                    //{
                                    //    item.DisableKeyword("_DETAIL_MULX2");
                                    //}
                                    //else
                                    //{
                                    //    item.EnableKeyword("_DETAIL_MULX2");

                                    //}
                                }

                            }
                        }

                    }


                }

            }

            GUILayout.EndHorizontal();



            var RimProLight = rimProEnable.floatValue == 1 ? true : false;
            //if (EditorGUI.EndChangeCheck()) {

            //    SetKeyword(material, "_RIM_PRO", RimProLight);
            //    rimProEnable.floatValue = RimProLight ? 1.0f : 0f;
            //}
            if (RimProLight)
            {
                if (sysChange)
                {
                    GUI.color = Color.red;

                }
                else
                {
                    if (isSysChange)
                    {
                        //GUI.enabled = false;
                        EditorGUILayout.HelpBox("参数同步修改中！", MessageType.Warning);
                    }

                }

                EditorGUI.BeginChangeCheck();
                float maxm = rimMaskClamp.vectorValue.y, minm = rimMaskClamp.vectorValue.x;
                if (isDebugMode)
                {

                    minm = EditorGUILayout.Slider(new GUIContent("暗部范围"), rimMaskClamp.vectorValue.x, 0f, 0.965f);
                    if ((maxm < minm + 0.036f) || (maxm == minm))
                    {
                        if (minm < 0.965f)
                        {
                            maxm = minm + 0.035f;
                        }
                    }
                    maxm = EditorGUILayout.Slider(new GUIContent("亮部范围"), maxm, 0.035f, 1);
                    if ((maxm < minm + 0.036f) || (maxm == minm))
                    {
                        if (maxm > 0.035f)
                        {
                            minm = maxm - 0.035f;
                        }
                    }
                    //_materialEditor.ShaderProperty(rimMaskClamp, new GUIContent("遮罩衰减 x(Min)，y(Max)"), INDENTATION);
                    //rimMaskClamp.vectorValue= new Vector4(Mathf.Clamp01(rimMaskClamp.vectorValue.x), Mathf.Clamp01(rimMaskClamp.vectorValue.y),rimMaskClamp.vectorValue.y, rimMaskClamp.vectorValue.z);
                }

                float aom = EditorGUILayout.Slider(new GUIContent("AO 遮蔽"), rimMaskClamp.vectorValue.z, 0.01f, 1);
                if (EditorGUI.EndChangeCheck())
                {
                    rimMaskClamp.vectorValue = new Vector4(minm, maxm, aom, rimMaskClamp.vectorValue.w);
                }


                _materialEditor.ShaderProperty(rimDir, new GUIContent("方向1"), INDENTATION);
                Vector3 v = new Vector3(rimDir.vectorValue.x, rimDir.vectorValue.y, rimDir.vectorValue.z).normalized;
                rimDir.vectorValue = new Vector4(v.x, v.y, v.z, 0);
                _materialEditor.ShaderProperty(rimCol, new GUIContent("颜色1"), INDENTATION);

                if (isDebugMode)
                {
                    //_materialEditor.ShaderProperty(rimPower, new GUIContent("范围1"), INDENTATION);
                    // _materialEditor.ShaderProperty(rimScale, new GUIContent("强度"), INDENTATION);
                }

                _materialEditor.ShaderProperty(rimDir2, new GUIContent("方向2"), INDENTATION);
                Vector3 v2 = new Vector3(rimDir2.vectorValue.x, rimDir2.vectorValue.y, rimDir2.vectorValue.z).normalized;
                rimDir2.vectorValue = new Vector4(v2.x, v2.y, v2.z, 0);
                _materialEditor.ShaderProperty(rimCol2, new GUIContent("颜色2"), INDENTATION);
                if (isDebugMode)
                {
                    //_materialEditor.ShaderProperty(rimScale2, new GUIContent("强度"), INDENTATION);
                    //_materialEditor.ShaderProperty(rimPower2, new GUIContent("范围2"), INDENTATION);
                }
                GUI.color = col;
                GUI.enabled = true;

            }





            GUILayout.EndVertical();
            EditorGUI.indentLevel = 0;
        }

        //vertex wind  fengxzeng
        //bool VertexWind = false;
        void VertexWindArea(Material material, MaterialProperty[] props)
        {
            GUILayout.BeginVertical();// GUI.skin.box);

            vertexWindEnabled = FindProperty("_VertexWindEnabled", props);
            // _materialEditor.ShaderProperty(vertexWindEnabled, new GUIContent("主界面风吹抖动 VertexWind"));
            SafeShaderProperty(_materialEditor, vertexWindEnabled, new GUIContent("主界面风吹抖动 VertexWind"));
            //VertexWind = GUILayout.Toggle(VertexWind, "风吹抖动 VertexWind", EditorStyles.foldout);
            if (vertexWindEnabled.floatValue == 1.0f)
            {
                GUILayout.BeginHorizontal();
                GUILayout.Label(" ", EditorStyles.boldLabel);

                string windstr = "关闭预览 风抖动 ";
                if (!vertexWind)
                {
                    windstr = "预览风抖动";
                }
                if (GUILayout.Button(windstr, GUILayout.Width(100)))
                {
                    vertexWind = !vertexWind;
                }
                GUILayout.EndHorizontal();

                if (!Application.isPlaying)
                {
                    if (!vertexWind)
                    {
                        //material.DisableKeyword("_VERTEX_WIND");
                        Shader.SetGlobalFloat("_VertexWindLerp", 0);
                    }
                    else
                    {
                        //material.EnableKeyword("_VERTEX_WIND");
                        Shader.SetGlobalFloat("_VertexWindLerp", 1);
                        if (!Application.isPlaying)
                        {
                            Shader.SetGlobalFloat("_VertexWindLerp", 1);
                        }
                    }
                }
                // _materialEditor.ShaderProperty(WindFrequency, new GUIContent(" 频率  Frequency"), INDENTATION);
                SafeShaderProperty(_materialEditor, WindFrequency, new GUIContent(" 频率  Frequency"), INDENTATION);
                // _materialEditor.ShaderProperty(WindAmplitude, new GUIContent(" 振幅  Amplitude"), INDENTATION);
                SafeShaderProperty(_materialEditor, WindAmplitude, new GUIContent(" 振幅  Amplitude"), INDENTATION);
                // _materialEditor.ShaderProperty(WindWavelength, new GUIContent(" 波长  Wavelength"), INDENTATION);
                SafeShaderProperty(_materialEditor, WindWavelength, new GUIContent(" 波长  Wavelength"), INDENTATION);
                
                // _materialEditor.ShaderProperty(WindDirection, new GUIContent(" 方向振幅偏移  Direction Amplitude Offset"), INDENTATION);
                SafeShaderProperty(_materialEditor, WindDirection, new GUIContent(" 方向振幅偏移  Direction Amplitude Offset"), INDENTATION);

            }
            GUILayout.EndVertical();


        }
        //Planer reflection  fengxzeng
        void PlanerReflectionArea(Material material, MaterialProperty[] props)
        {
            planeReflectionEnabled = FindProperty("_PlaneReflectionEnabled", props);
            PlaneReflectionIntensity = FindProperty("_PlaneReflectionIntensity", props);

            // _materialEditor.ShaderProperty(planeReflectionEnabled, Styles.PlaneReflectionEnabledText);
            SafeShaderProperty(_materialEditor, planeReflectionEnabled, Styles.PlaneReflectionEnabledText);
            if (planeReflectionEnabled.floatValue == 0)
            {
                material.DisableKeyword("_PLANE_REFLECTION");
            }
            else
            {
                if (isDebugMode)
                {
                    // _materialEditor.ShaderProperty(PlaneReflectionIntensity, Styles.PlaneReflectionIntensityText, INDENTATION);
                    SafeShaderProperty(_materialEditor, PlaneReflectionIntensity, Styles.PlaneReflectionIntensityText, INDENTATION);

                }
                material.EnableKeyword("_PLANE_REFLECTION");
            }

        }




        static void SetKeyword(Material material, string keyword, bool n)
        {
            if (n)
                material.EnableKeyword(keyword);
            else
                material.DisableKeyword(keyword);
        }

    }
}