using GfxFramework;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame.Asset;
using UnityEditor;
using UnityEngine;
using UnityEngine.Playables;
using ZGame;
// 提示信息
// 模型面数
// 模型内存
// 贴图数量
// 贴图内存
// 动画数量
// 动画内存

// 错误信息
// 面数超标 OK
// 单模型面数超65535 OK
// 贴图内存超标 OK
// 相机近裁面 OK
// 相机远裁面 OK
// 引用不该引用的资源 
// 存在相同的多维子材质 OK
// 使用了默认材质球 OK
// 使用了默认shader OK
// shader错误 OK
// 开启了ShapeModule, 但是贴图没有可读 OK
// 粒子引用的模型存在多个subMesh OK
// 存在同级节点的subEmitter OK
// PlayableDirector存在冗余引用 OK
// 丢失了引用 OK
// DDS图
// 最大粒子数超过100

public class ArtExportError
{
    public string subNodePath;
    public string errorInfo;
}

public class ArtExportAsset
{
    public const string DDS_IMAGE = ".dds";

    public string mainPath;
    public List<string> dependencyPaths;

    public ArtExportAsset(string path)
    {
        mainPath = path;

        CollectDependencies();
    }

    public void CollectDependencies()
    {
        dependencyPaths = new List<string>(AssetDatabase.GetDependencies(mainPath));

        //TKAssetGroupManager.CheckFillTKAssetGroupCfgDependency(mainPath, dependencyPaths);
        
        AddLogicCfg(mainPath);

        AddTinyDependencyPaths(mainPath);

        AddEffectDependencyPaths(mainPath);

        for (int i = dependencyPaths.Count - 1; i >= 0; --i)
            if (dependencyPaths[i].EndsWith(".cs") || dependencyPaths[i].EndsWith(".shader") || dependencyPaths[i] == mainPath)
                dependencyPaths.RemoveAt(i);

        //dependencyPaths.Sort(SortPath);
    }

    private void AddLogicCfg(string path)
    {        
        // 逻辑层配置
        if (path.EndsWith(".asset"))
        {
            var logicCfgPath = path.Replace(".asset", "_logic.json");
            if (File.Exists(logicCfgPath))
            {
                dependencyPaths.Add(logicCfgPath);
            }
        }
    }

    private void AddTinyDependencyPaths(string path)
    {
        // 小小英雄模型配置
        if (path.StartsWith("Assets/Art_TFT_Raw/little_legend_res/model/"))
        {
            var tinyShowName = Path.GetFileNameWithoutExtension(path);
            var tinyCfgPath = "Assets/Art_TFT_Raw/little_legend_res/model_cfg/" + tinyShowName + ".asset";
            if (File.Exists(tinyCfgPath))
            {
                var tinyCfgDepPaths = AssetDatabase.GetDependencies(tinyCfgPath);
                dependencyPaths.AddRange(tinyCfgDepPaths);
            }

            // 小小英雄cfg
            try
            {
                DataBaseManager.Instance.Initialize();
                var items = DataBaseManager.Instance.GetItems();
                foreach (var item in items)
                {
                    if (item.Value.sPreviewResource == tinyShowName)
                    {
                        var sTeamLeaderCfgPath = "Assets/Art_TFT_Raw/cfg/team_leader_cfg/" + item.Value.sTeamLeaderCfg + ".asset";
                        if (File.Exists(sTeamLeaderCfgPath))
                        {
                            dependencyPaths.Add(sTeamLeaderCfgPath);

                            AddLogicCfg(sTeamLeaderCfgPath);
                        }
                    }
                }
            }
            catch(Exception ex)
            {
                TKFrame.Diagnostic.Error("[ArtExportAsset.AddTinyDependencyPaths] " + path + "\n" + ex.ToString());
            }
        }
    }

    private void AddEffectDependencyPaths(string path)
    {
        if (path.StartsWith("Assets/Art_TFT_Raw/effects/") || path.StartsWith("Assets/Art_TFT_Raw/effects_low/"))
        {
            var effect = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            if (effect != null)
            {
                var gfxs = effect.GetComponentsInChildren<GfxFramework.GfxRoot_Unity>(true);
                if (gfxs != null)
                {
                    foreach (var gfx in gfxs)
                    {
                        var assetName = gfx.assetName;
                        if (!string.IsNullOrEmpty(assetName))
                        {
                            var assetPath = "Assets/ParticleSystemConfig/" + assetName + ".asset";
                            Debug.Log($"{path} has GfxRoot_Unity {assetPath}");
                            dependencyPaths.Add(assetPath);
                            var dep = AssetDatabase.GetDependencies(assetPath);
                            dependencyPaths.AddRange(dep);
                            string depStr = string.Join(",", dep);
                            Debug.Log($"{path} has GfxRoot_Unity {depStr}");
                        }
                    }
                }
            }
        }
    }

    //public int SortPath(string l, string r)
    //{
    //    if (l.EndsWith(".prefab") && r.EndsWith(".prefab"))
    //        return r.CompareTo(l);
    //    else if (l.EndsWith(".prefab"))
    //        return -1;
    //    else if (r.EndsWith(".prefab"))
    //        return 1;
    //    return r.CompareTo(l);
    //}
}

