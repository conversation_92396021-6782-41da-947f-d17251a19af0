using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;

[CustomEditor(typeof(ArtExportAssetWhiteList))]
public class ArtExportAssetWhiteListInspector : Editor
{
    private UnityEditorInternal.ReorderableList m_paramList;
    private ArtExportAssetWhiteList.ArtLimitOverride m_selected;

    private void OnEnable()
    {
        var t = target as ArtExportAssetWhiteList;
        m_paramList = new UnityEditorInternal.ReorderableList(t.list, typeof(string), true, true, false, true);
        m_paramList.elementHeight = 20f;
        m_paramList.drawElementCallback = delegate (Rect rect, int index, bool selected, bool focused)
        {
            var item = t.list[index];

            rect.y += 1;
            rect.height -= 1;

            var pathRect = rect;
            pathRect.width = pathRect.width - 350;
            EditorGUI.LabelField(pathRect, item.assetPath);

            var dataPreNameRect = rect;
            dataPreNameRect.x = pathRect.x + pathRect.width + 10;
            dataPreNameRect.width = 330;
            EditorGUI.LabelField(dataPreNameRect, "说明:" + item.desc);
        };

        m_paramList.onRemoveCallback = delegate (UnityEditorInternal.ReorderableList list)
        {
            t.list.RemoveAt(list.index);
        };

        m_paramList.drawHeaderCallback = (Rect rect) =>
        {
            GUI.Label(rect, "导出白名单");
        };

        m_paramList.onSelectCallback = delegate (UnityEditorInternal.ReorderableList list)
        {
            m_selected = t.list[list.index];
        };
    }

    public override void OnInspectorGUI()
    {
        //base.OnInspectorGUI();

        m_paramList.DoLayoutList();


        DrawSelected();

        if (GUILayout.Button("保存"))
        {
            EditorUtility.SetDirty(target);
           TKFrame.Diagnostic.SaveAssets();

            ArtExportAssetWhiteList.ms_overrideDict = null;
        }

        var t = target as ArtExportAssetWhiteList;
        DoDragAndDrop(t);
    }

    private void DrawSelected()
    {
        if (m_selected != null)
        {
            var t = target as ArtExportAssetWhiteList;
            if (t.list.IndexOf(m_selected) == -1)
            {
                m_selected = null;
                return;
            }    

            EditorGUILayout.BeginVertical("box");

            EditorGUILayout.LabelField("当前选中：");

            m_selected.assetPath = EditorGUILayout.TextField("路径:", m_selected.assetPath);
            m_selected.desc = EditorGUILayout.TextField("说明:", m_selected.desc);

            EditorGUILayout.BeginVertical("box");

            if (GUILayout.Button("添加重载"))
            {
                m_selected.overrides.Add(new ArtExportAssetWhiteList.ArtLimitOverrideItem() { type = ArtExportAssetWhiteList.ArtOverrideType.MaxTriangles });
            }

            for (int i = m_selected.overrides.Count - 1; i >= 0; --i)
            {
                var o = m_selected.overrides[i];

                EditorGUILayout.BeginHorizontal();

                o.lod = EditorGUILayout.IntField("lod:", o.lod);
                o.type = (ArtExportAssetWhiteList.ArtOverrideType)EditorGUILayout.EnumPopup(o.type);
                o.value = EditorGUILayout.IntField(o.value);

                if (GUILayout.Button("移除"))
                {
                    m_selected.overrides.RemoveAt(i);
                }

                EditorGUILayout.EndHorizontal();
            }

            EditorGUILayout.EndVertical();

            EditorGUILayout.EndVertical();
        }
    }

    private void DoDragAndDrop(ArtExportAssetWhiteList t)
    {
        GUI.enabled = false;
        EditorGUILayout.TextArea("拖动到这里自动添加路径", GUILayout.Height(80));
        GUI.enabled = true;
        var dragRect = GUILayoutUtility.GetLastRect();
        switch (Event.current.type)
        {
            case EventType.DragUpdated:
            case EventType.DragPerform:
                {
                    if (dragRect.Contains(Event.current.mousePosition))
                    {
                        DragAndDrop.visualMode = DragAndDropVisualMode.Copy;
                        if (Event.current.type == EventType.DragPerform)
                        {
                            DragAndDrop.AcceptDrag();
                            for (int i = 0, len = DragAndDrop.paths.Length; i < len; i++)
                            {
                                var path = DragAndDrop.paths[i];

                                t.list.Add(new ArtExportAssetWhiteList.ArtLimitOverride() { assetPath = path });
                            }
                        }
                    }
                }
                break;
        }
    }
}

