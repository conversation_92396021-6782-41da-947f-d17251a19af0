using GfxFramework;
using PBRTools;
using Spine.Unity;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using UnityEditor;
using UnityEngine;
using UnityEngine.Playables;
using ZGameChess;


public enum GfxAssetType
{
    
}

public class ArtAssetDisplayData
{
    public List<GfxDebugInfo> gfxRefAssets = new List<GfxDebugInfo>();
    
}

public class ArtExportPrefabAsset
{
    public string mainPath;

    public string totalInfo;

    public string briefWarningInfo;//带颜色的简要警告信息

    public string briefNormalInfo;//无警告时候的正常简要信息

    public string briefInfoForTooltips;//用于Tooltips显示的简要信息

    public List<ArtExportError> errors = new List<ArtExportError>();
    
    public List<ArtExportError> warnings = new List<ArtExportError>(); 

    public GfxProfilerStaticData staticData = new GfxProfilerStaticData();
    
    // public ArtAssetType assetType;
    //
    // public ArtAssetType whiteAssetType;

    public List<KeyValuePair<int, LodStat>> lodStats;

    // 粒子系统贴图尺寸与粒子大小比例的阈值
    public static int ParticleTextureToSizeRatioThreshold = 128 * 128;

    // 模型贴图尺寸与模型体积比例的阈值
    public static int MeshTextureToVolumeRatioThreshold = 128 * 128;
    
    public bool isAnyWarning = false;

    public ArtExportPrefabAsset(string path)
    {
        mainPath = path;
    }

    public void AnalyzePrefab()
    {
        if (mainPath.EndsWith(".prefab"))
        {
            errors.Clear();
            
            warnings.Clear();

            var root = AssetDatabase.LoadAssetAtPath<GameObject>(mainPath).transform;
            staticData = new GfxProfilerStaticData();
            staticData.Analyze(root);

            GenTotalInfo(root);
            
        }
    }

    public void GenBriefWarningInfo(bool rebuild = false)
    {
        if (mainPath.EndsWith(".prefab"))
        {
            if (!staticData.isInited() || rebuild)
            {
                var root = AssetDatabase.LoadAssetAtPath<GameObject>(mainPath).transform;
                staticData = new GfxProfilerStaticData();
                staticData.Analyze(root);
            }
            GenWarnInfo();
        }
    }
    
    private void GenWarnInfo()
    {
        // if(rebuild){
        //     lodStats = null;
        //     assetType = null;
        //     whiteAssetType = null;
        // }
        
        warnings.Clear();
        
        lodStats = GetLodStat().ToList();
        lodStats.Sort(SortLodStats);
    
    
        var assetType = ArtExportAssetDefine.GetInstance().GetAssetType(mainPath);
        
        var whiteAssetType = ArtExportAssetDefine.GetInstance().GetWhiteListAssetType(mainPath);
        
        List<string> infos = new List<string>();
        StringBuilder briefStr = new StringBuilder();
        StringBuilder briefForNormal = new StringBuilder();
        isAnyWarning = false;
        foreach (var lodStat in lodStats)
        {
            bool anyWarningInThisLod = false;
            int lodLevel = lodStat.Key;
            ArtAssetLimit limit;
            if (assetType.limits.Count > lodLevel)
            {
                limit = assetType.limits[lodLevel];
            }
            else
            {
                limit = assetType.limits[assetType.limits.Count - 1];
            }
    
            int maxTriangles = limit.MaxTriangles;
            int maxClipMemory = limit.MaxClipMemory;
            int maxTextureMemory = limit.MaxTextureMemory;
            int maxTextureSize = limit.MaxTextureSize;
    
            //新的白名单实现
            if (whiteAssetType != null)
            {
                foreach (var cfg in whiteAssetType.overrides)
                {
                    if (cfg.lod != lodLevel)
                        continue;
                    switch (cfg.type)
                    {
                        case ArtExportAssetWhiteList.ArtOverrideType.MaxClipMemory:
                            maxClipMemory = cfg.value;
                            break;
                        case ArtExportAssetWhiteList.ArtOverrideType.MaxTextureSize:
                            maxTextureSize = cfg.value;
                            break;
                        case ArtExportAssetWhiteList.ArtOverrideType.MaxTextureMemory:
                            maxTextureMemory = cfg.value;
                            break;
                        case ArtExportAssetWhiteList.ArtOverrideType.MaxTriangles:
                            maxTriangles = cfg.value;
                            break;
                        default:
                            break;
                    }
                }
            }
            
            infos.Add(string.Format("lod: {0} 资源总内存: {1} 模型内存: {2} 动画内存: {3}/{4} 贴图内存: {5}/{6} 贴图数量: {7} 模型顶点: {8} 模型面数: {9}/{10}", lodLevel,
                EditorUtility.FormatBytes(lodStat.Value.meshMem + lodStat.Value.clipMem + lodStat.Value.textureMem),
                EditorUtility.FormatBytes(lodStat.Value.meshMem),
                EditorUtility.FormatBytes(lodStat.Value.clipMem), EditorUtility.FormatBytes(maxClipMemory),
                EditorUtility.FormatBytes(lodStat.Value.textureMem), EditorUtility.FormatBytes(maxTextureMemory),
                lodStat.Value.textureNum, // 添加贴图数量
                lodStat.Value.vertexs, lodStat.Value.triangles, maxTriangles
            ));
    
            
            briefForNormal.Append(string.Format("<color=#000000>|</color>lod:{0} total:{1}", lodLevel,
                EditorUtility.FormatBytes(lodStat.Value.meshMem + lodStat.Value.clipMem + lodStat.Value.textureMem)));


            if (maxClipMemory < lodStat.Value.clipMem
                || maxTextureMemory < lodStat.Value.textureMem
                || maxTextureMemory < lodStat.Value.textureMem
                || maxTriangles < lodStat.Value.triangles)
            {
                briefStr.Append(string.Format("<color=#000000>|</color>lod:{0} total:{1}", lodLevel,
                    EditorUtility.FormatBytes(lodStat.Value.meshMem + lodStat.Value.clipMem + lodStat.Value.textureMem)));
                isAnyWarning = true;
            }
    
            // 检测性能
            if (maxClipMemory < lodStat.Value.clipMem)
            {
                briefStr.Append($" clip:<color=#FF0000>{EditorUtility.FormatBytes(lodStat.Value.clipMem)}</color>/{EditorUtility.FormatBytes(maxClipMemory)}");
                warnings.Add(new ArtExportError() { subNodePath = string.Empty, errorInfo = "lod: " + lodLevel + " 动画内存超标！ 上限: " + EditorUtility.FormatBytes(maxClipMemory) });
            }
            if (maxTextureMemory < lodStat.Value.textureMem)
            {
                briefStr.Append($" tex:<color=#FF0000>{EditorUtility.FormatBytes(lodStat.Value.textureMem)}</color>/{EditorUtility.FormatBytes(maxTextureMemory)}");
                warnings.Add(new ArtExportError() { subNodePath = string.Empty, errorInfo = "lod: " + lodLevel + " 贴图内存超标！ 上限: " + EditorUtility.FormatBytes(maxTextureMemory) });
            }
            if (maxTriangles < lodStat.Value.triangles)
            {
                briefStr.Append($" tri:<color=#FF0000>{EditorUtility.FormatBytes(lodStat.Value.triangles)}</color>/{EditorUtility.FormatBytes(maxTriangles)}");
                warnings.Add(new ArtExportError() { subNodePath = string.Empty, errorInfo = "lod: " + lodLevel + " 模型面数超标！ 上限: " + maxTriangles });
            }
        }
    
        briefWarningInfo = briefStr.ToString();
        briefInfoForTooltips = string.Join("\n", infos);
        briefNormalInfo += briefForNormal.ToString();
    }


    public ArtAssetDisplayData GetSubAssetGfxData(GameObject go)
    {
        ArtAssetDisplayData ret = new ArtAssetDisplayData();
#if UNITY_EDITOR
        
        var root = go.transform;
            var gfxRoot = root.GetComponent<GfxRoot>();
            if (gfxRoot != null)
            {
                gfxRoot.UnpackParticleData();
            }

            var components = root.GetComponents<Component>();

            foreach (var item in components)
            {
                if (item == null
                    || item.GetType().FullName == "PBRTools.CustomLightingTool") // 这里面有很多editor的图 直接忽略不测
                {
                    continue;
                }

                if (item is SkinnedMeshRenderer skinnedMeshRenderer)
                {
                    if (staticData.skinnedMeshRenderer2GfxRefAsset.TryGetValue(skinnedMeshRenderer,
                            out GfxSkinnedMeshRenderer val))
                    {
                        ret.gfxRefAssets.Add(val);
                    }
                }
                else if (item is ParticleSystem particleSystem)
                {
                    //粒子
                    if (staticData.particleSystem2GfxRefAsset.TryGetValue(particleSystem,
                            out GfxParticleSystemStaticData val))
                    {
                        ret.gfxRefAssets.Add(val);
                    }
                }

                var serializedObject = new SerializedObject(item);
                var iter = serializedObject.GetIterator();
                while (iter.NextVisible(true))
                {
                    if (iter.propertyType == SerializedPropertyType.ObjectReference)
                    {
                        if (iter.objectReferenceValue != null)
                        {
                            var path = AssetDatabase.GetAssetPath(iter.objectReferenceValue);
                            //网格
                            var mesh = iter.objectReferenceValue as Mesh;
                            if (mesh != null)
                            {
                                if (staticData.mesh2GfxRefAsset.TryGetValue(mesh,
                                        out GfxRefMeshAsset val))
                                {
                                    ret.gfxRefAssets.Add(val);
                                }
                                continue;
                            }

                            //材质
                            var material = iter.objectReferenceValue as Material;
                            if (material != null)
                            {
                                if (staticData.material2GfxRefAsset.TryGetValue(material,
                                        out GfxRefMaterialAsset val))
                                {
                                    ret.gfxRefAssets.Add(val);
                                }

                                //纹理
                                var textureIds = material.GetTexturePropertyNameIDs();
                                foreach (var textureId in textureIds)
                                {
                                    var t = material.GetTexture(textureId);
                                    if (t != null)
                                    {
                                        if (staticData.texture2GfxRefAsset.TryGetValue(t,
                                                out GfxRefTextureAsset valT))
                                        {
                                            ret.gfxRefAssets.Add(valT);
                                        }
                                    }
                                }
                                continue;
                            }

                            var texture = iter.objectReferenceValue as Texture;
                            if (texture != null)
                            {
                                if (staticData.texture2GfxRefAsset.TryGetValue(texture,
                                        out GfxRefTextureAsset valT))
                                {
                                    ret.gfxRefAssets.Add(valT);
                                }
                                continue;
                            }

                            var sprite = iter.objectReferenceValue as Sprite;
                            if (sprite != null)
                            {
                                var t = sprite.texture;
                                if (t!=null)
                                {
                                    if (t != null)
                                    {
                                        if (staticData.texture2GfxRefAsset.TryGetValue(t,
                                                out GfxRefTextureAsset valT))
                                        {
                                            ret.gfxRefAssets.Add(valT);
                                        }
                                    }
                                }
                                continue;
                            }

                            var ac = iter.objectReferenceValue as RuntimeAnimatorController;
                            if (ac != null)
                            {
                                foreach (var subClip in ac.animationClips)
                                {
                                    path = AssetDatabase.GetAssetPath(subClip);
                                    if (subClip != null)
                                    {
                                        if (staticData.clip2GfxRefAsset.TryGetValue(subClip,
                                                out GfxRefClipAsset val))
                                        {
                                            ret.gfxRefAssets.Add(val);
                                        }
                                    }
                                }
                                continue;
                            }

                            var timeline = iter.objectReferenceValue as PlayableAsset;
                            if (timeline != null)
                            {
                                var assetPath = AssetDatabase.GetAssetPath(timeline);
                                if (!string.IsNullOrEmpty(assetPath))
                                {
                                    var denpendencies = AssetDatabase.GetDependencies(assetPath);
                                    foreach (var d in denpendencies)
                                    {
                                        var dclip = AssetDatabase.LoadAssetAtPath<AnimationClip>(d);
                                        if (dclip != null)
                                        {
                                            if (staticData.clip2GfxRefAsset.TryGetValue(dclip,
                                                    out GfxRefClipAsset val))
                                            {
                                                ret.gfxRefAssets.Add(val);
                                            }
                                        }
                                    }
                                }

                                continue;
                            }

                            var clip = iter.objectReferenceValue as AnimationClip;
                            if (clip != null)
                            {
                                if (staticData.clip2GfxRefAsset.TryGetValue(clip,
                                        out GfxRefClipAsset val))
                                {
                                    ret.gfxRefAssets.Add(val);
                                }
                                continue;
                            }
                        }
                    }
                }
            }
#endif
        return ret;
    }

    //private bool CheckModelTri(string assetName, string[] modelAssets, out string errorStr)
    //{
    //    List<int> meshTrisList = new List<int>()
    //        {
    //            0, 0, 0
    //        };
    //    List<bool> meshTrisOpenList = new List<bool>()
    //        {
    //            false, true, false
    //        };
    //    ModelImportLimitConfig.AssetType assetType = ModelImportLimitConfig.AssetType.英雄;
    //    if (assetName.StartsWith("t_"))
    //    {
    //        if (assetName.EndsWith("_h"))
    //            assetType = ModelImportLimitConfig.AssetType.小小英雄_局外;
    //        else
    //            assetType = ModelImportLimitConfig.AssetType.小小英雄_局内;
    //    }
    //    List<string> lodNameErrorList = new List<string>();
    //    for (int i = 0; i < modelAssets.Length; ++i)
    //    {
    //        var modelAsset = modelAssets[i];
    //        if (modelAsset is Mesh)
    //        {
    //            string meshName = modelAsset.name;
    //            int meshTris = GetMeshTris(modelAsset as Mesh);
    //            if (meshName.Contains("lod_h"))
    //            {
    //                meshTrisList[(int)ModelImportLimitConfig.AssetLod.高模] += meshTris;
    //            }
    //            else if (meshName.Contains("lod_l"))
    //            {
    //                meshTrisOpenList[(int)ModelImportLimitConfig.AssetLod.低模] = true;
    //                meshTrisList[(int)ModelImportLimitConfig.AssetLod.低模] += meshTris;
    //            }
    //            else if (meshName.Contains("lod_s"))
    //            {
    //                meshTrisOpenList[(int)ModelImportLimitConfig.AssetLod.Pad] = true;
    //                meshTrisList[(int)ModelImportLimitConfig.AssetLod.Pad] += meshTris;
    //            }
    //            else
    //            {
    //                meshTrisList[(int)ModelImportLimitConfig.AssetLod.高模] += meshTris;
    //                meshTrisList[(int)ModelImportLimitConfig.AssetLod.低模] += meshTris;
    //                meshTrisList[(int)ModelImportLimitConfig.AssetLod.Pad] += meshTris;
    //            }
    //        }
    //        else if (modelAsset is GameObject)
    //        {
    //            if (assetType == ModelImportLimitConfig.AssetType.英雄)
    //            {
    //                var go = modelAsset as GameObject;
    //                var rs = go.GetComponentsInChildren<Renderer>(true);
    //                foreach (var r in rs)
    //                {
    //                    if (r.name.Contains("lod_s") || r.name.Contains("lod_h") || r.name.Contains("lod_l"))
    //                        continue;
    //                    else
    //                        lodNameErrorList.Add(r.name);
    //                }
    //            }
    //        }
    //    }

    //    bool isTrisVaild = true;
    //    StringBuilder sb = new StringBuilder();
    //    sb.AppendFormat("模型名字： {0}, \n模型类型: {1}\n", assetName, assetType);
    //    for (int i = 0; i < meshTrisList.Count; ++i)
    //    {
    //        if (!meshTrisOpenList[i])
    //            continue;

    //        var meshTris = meshTrisList[i];
    //        ModelImportLimitConfig.AssetLod lod = (ModelImportLimitConfig.AssetLod)i;

    //        int maxTris = ModelImportLimitConfig.GetMaxTris(assetName, assetType, lod);
    //        if (maxTris > 0)
    //        {
    //            if (meshTris > maxTris)
    //            {
    //                isTrisVaild = false;
    //                sb.AppendFormat("lod: {0} 面数: {1} 最高限制面数: {2}\n", lod, meshTris, maxTris);
    //            }
    //        }
    //    }
    //    if (!isTrisVaild)
    //        sb.AppendLine("面数过多！无法导入！请让美术减面获得让TA加入白名单！");
    //    if (lodNameErrorList.Count != 0)
    //    {
    //        sb.AppendLine("LOD节点名字错误：" + string.Join(",", lodNameErrorList));
    //        sb.AppendLine("请让美术修改模型的节点名字！");
    //    }
    //    bool vaild = isTrisVaild && lodNameErrorList.Count == 0;
    //    if (!vaild)
    //        errorStr = sb.ToString();
    //    else
    //        errorStr = string.Empty;
    //    return vaild;
    //}

    public class LodStat
    {
        public long meshMem;
        public long clipMem;
        public long textureMem;
        public int vertexs;
        public int triangles;
        public int textureNum; // 贴图数量
    }

    private Dictionary<int, LodStat> GetLodStat()
    {
        LodStat baseStat = new LodStat();
        Dictionary<int, LodStat> lodStats = new Dictionary<int, LodStat>();
        
        // 记录每个LOD级别已经计数的贴图，避免重复计算
        Dictionary<int, HashSet<string>> countedTexturesPerLod = new Dictionary<int, HashSet<string>>();
   

        countedTexturesPerLod[(int)GfxRefAssetLod.None] = new HashSet<string>();
        countedTexturesPerLod[(int)GfxRefAssetLod.Lod0] = new HashSet<string>();
        countedTexturesPerLod[(int)GfxRefAssetLod.Lod1] = new HashSet<string>();
        countedTexturesPerLod[(int)GfxRefAssetLod.Lod2] = new HashSet<string>();
        countedTexturesPerLod[(int)GfxRefAssetLod.Lod3] = new HashSet<string>();
        countedTexturesPerLod[(int)GfxRefAssetLod.Lod4] = new HashSet<string>();

        foreach (var mesh in staticData.meshs)
        {
            if (mesh.Value.lod == 0)
            {
                baseStat.meshMem += mesh.Value.size;
                baseStat.vertexs += mesh.Value.vertexs;
                baseStat.triangles += mesh.Value.triangles;
            }
            if ((mesh.Value.lod & (int)GfxRefAssetLod.Lod0) != 0)
            {
                if (!lodStats.TryGetValue(0, out LodStat stat))
                {
                    stat = new LodStat();
                    lodStats.Add(0, stat);
                }
                stat.meshMem += mesh.Value.size;
                stat.vertexs += mesh.Value.vertexs;
                stat.triangles += mesh.Value.triangles;
            }
            if ((mesh.Value.lod & (int)GfxRefAssetLod.Lod1) != 0)
            {
                if (!lodStats.TryGetValue(1, out LodStat stat))
                {
                    stat = new LodStat();
                    lodStats.Add(1, stat);
                }
                stat.meshMem += mesh.Value.size;
                stat.vertexs += mesh.Value.vertexs;
                stat.triangles += mesh.Value.triangles;
            }
            if ((mesh.Value.lod & (int)GfxRefAssetLod.Lod2) != 0)
            {
                if (!lodStats.TryGetValue(2, out LodStat stat))
                {
                    stat = new LodStat();
                    lodStats.Add(2, stat);
                }
                stat.meshMem += mesh.Value.size;
                stat.vertexs += mesh.Value.vertexs;
                stat.triangles += mesh.Value.triangles;
            }
            if ((mesh.Value.lod & (int)GfxRefAssetLod.Lod3) != 0)
            {
                if (!lodStats.TryGetValue(3, out LodStat stat))
                {
                    stat = new LodStat();
                    lodStats.Add(3, stat);
                }
                stat.meshMem += mesh.Value.size;
                stat.vertexs += mesh.Value.vertexs;
                stat.triangles += mesh.Value.triangles;
            }
            if ((mesh.Value.lod & (int)GfxRefAssetLod.Lod4) != 0)
            {
                if (!lodStats.TryGetValue(4, out LodStat stat))
                {
                    stat = new LodStat();
                    lodStats.Add(4, stat);
                }
                stat.meshMem += mesh.Value.size;
                stat.vertexs += mesh.Value.vertexs;
                stat.triangles += mesh.Value.triangles;
            }
        }

        foreach (var clip in staticData.clips)
        {
            if (clip.Value.lod == 0)
            {
                baseStat.clipMem += clip.Value.size;
            }
            if ((clip.Value.lod & (int)GfxRefAssetLod.Lod0) != 0)
            {
                if (!lodStats.TryGetValue(0, out LodStat stat))
                {
                    stat = new LodStat();
                    lodStats.Add(0, stat);
                }
                stat.clipMem += clip.Value.size;
            }
            if ((clip.Value.lod & (int)GfxRefAssetLod.Lod1) != 0)
            {
                if (!lodStats.TryGetValue(1, out LodStat stat))
                {
                    stat = new LodStat();
                    lodStats.Add(1, stat);
                }
                stat.clipMem += clip.Value.size;
            }
            if ((clip.Value.lod & (int)GfxRefAssetLod.Lod2) == 1)
            {
                if (!lodStats.TryGetValue(2, out LodStat stat))
                {
                    stat = new LodStat();
                    lodStats.Add(2, stat);
                }
                stat.clipMem += clip.Value.size;
            }
            if ((clip.Value.lod & (int)GfxRefAssetLod.Lod3) != 0)
            {
                if (!lodStats.TryGetValue(3, out LodStat stat))
                {
                    stat = new LodStat();
                    lodStats.Add(3, stat);
                }
                stat.clipMem += clip.Value.size;
            }
            if ((clip.Value.lod & (int)GfxRefAssetLod.Lod4) != 0)
            {
                if (!lodStats.TryGetValue(4, out LodStat stat))
                {
                    stat = new LodStat();
                    lodStats.Add(4, stat);
                }
                stat.clipMem += clip.Value.size;
            }
        }

        foreach (var texture in staticData.textures)
        {
            if (texture.Value.lod == 0)
            {
                baseStat.textureMem += texture.Value.size;
                // 只计算一次基础贴图
                if (!countedTexturesPerLod[(int)GfxRefAssetLod.None].Contains(texture.Key))
                {
                    baseStat.textureNum++;
                    countedTexturesPerLod[(int)GfxRefAssetLod.None].Add(texture.Key);
                }
            }
            if ((texture.Value.lod & (int)GfxRefAssetLod.Lod0) != 0)
            {
                if (!lodStats.TryGetValue(0, out LodStat stat))
                {
                    stat = new LodStat();
                    lodStats.Add(0, stat);
                }
                stat.textureMem += texture.Value.size;
                
                // 对每个LOD级别，只计算不重复的贴图
                if (!countedTexturesPerLod[(int)GfxRefAssetLod.Lod0].Contains(texture.Key))
                {
                    stat.textureNum++;
                    countedTexturesPerLod[(int)GfxRefAssetLod.Lod0].Add(texture.Key);
                }
            }
            if ((texture.Value.lod & (int)GfxRefAssetLod.Lod1) != 0)
            {
                if (!lodStats.TryGetValue(1, out LodStat stat))
                {
                    stat = new LodStat();
                    lodStats.Add(1, stat);
                }
                stat.textureMem += texture.Value.size;
                
                if (!countedTexturesPerLod[(int)GfxRefAssetLod.Lod1].Contains(texture.Key))
                {
                    stat.textureNum++;
                    countedTexturesPerLod[(int)GfxRefAssetLod.Lod1].Add(texture.Key);
                }
            }
            if ((texture.Value.lod & (int)GfxRefAssetLod.Lod2) != 0)
            {
                if (!lodStats.TryGetValue(2, out LodStat stat))
                {
                    stat = new LodStat();
                    lodStats.Add(2, stat);
                }
                stat.textureMem += texture.Value.size;
                
                if (!countedTexturesPerLod[(int)GfxRefAssetLod.Lod2].Contains(texture.Key))
                {
                    stat.textureNum++;
                    countedTexturesPerLod[(int)GfxRefAssetLod.Lod2].Add(texture.Key);
                }
            }
            if ((texture.Value.lod & (int)GfxRefAssetLod.Lod3) != 0)
            {
                if (!lodStats.TryGetValue(3, out LodStat stat))
                {
                    stat = new LodStat();
                    lodStats.Add(3, stat);
                }
                stat.textureMem += texture.Value.size;
                
                if (!countedTexturesPerLod[(int)GfxRefAssetLod.Lod3].Contains(texture.Key))
                {
                    stat.textureNum++;
                    countedTexturesPerLod[(int)GfxRefAssetLod.Lod3].Add(texture.Key);
                }
            }
            if ((texture.Value.lod & (int)GfxRefAssetLod.Lod4) != 0)
            {
                if (!lodStats.TryGetValue(4, out LodStat stat))
                {
                    stat = new LodStat();
                    lodStats.Add(4, stat);
                }
                stat.textureMem += texture.Value.size;
                
                if (!countedTexturesPerLod[(int)GfxRefAssetLod.Lod4].Contains(texture.Key))
                {
                    stat.textureNum++;
                    countedTexturesPerLod[(int)GfxRefAssetLod.Lod4].Add(texture.Key);
                }
            }
        }

        if (lodStats.Count == 0) // 无lod的情况 
        {
            lodStats.Add(4, baseStat);      // 默认资源为高配资源
        }
        else
        {
            foreach (var item in lodStats)
            {
                item.Value.meshMem += baseStat.meshMem;
                item.Value.clipMem += baseStat.clipMem;
                item.Value.textureMem += baseStat.textureMem;
                item.Value.vertexs += baseStat.vertexs;
                item.Value.triangles += baseStat.triangles;
                item.Value.textureNum += baseStat.textureNum; // 添加基础贴图数量
            }
        }

        return lodStats;
    }

    static int SortLodStats(KeyValuePair<int, LodStat> l, KeyValuePair<int, LodStat> r)
    {
        return l.Key.CompareTo(r.Key);
    }

    public void GenTotalInfo(Transform root)
    {
        GenWarnInfo();
        // var lodStats = GetLodStat().ToList();
        // lodStats.Sort(SortLodStats);
        //
        // var assetType = ArtExportAssetDefine.GetInstance().GetAssetType(mainPath);
        // var whiteAssetType = ArtExportAssetDefine.GetInstance().GetWhiteListAssetType(mainPath);
        // List<string> infos = new List<string>();
        // foreach (var lodStat in lodStats)
        // {
        //     int lodLevel = lodStat.Key;
        //     ArtAssetLimit limit;
        //     if (assetType.limits.Count > lodLevel)
        //     {
        //         limit = assetType.limits[lodLevel];
        //     }
        //     else
        //     {
        //         limit = assetType.limits[assetType.limits.Count - 1];
        //     }
        //
        //     int maxTriangles = limit.MaxTriangles;
        //     int maxClipMemory = limit.MaxClipMemory;
        //     int maxTextureMemory = limit.MaxTextureMemory;
        //     int maxTextureSize = limit.MaxTextureSize;
        //
        //     // 使用新的白名单
        //     // var overrideDict = ArtExportAssetWhiteList.GetOverrideDict();
        //     // if (overrideDict.TryGetValue(mainPath, out ArtExportAssetWhiteList.ArtLimitOverride overrideCfg))
        //     // {
        //     //     foreach (var cfg in overrideCfg.overrides)
        //     //     {
        //     //         if (cfg.lod != lodLevel)
        //     //             continue;
        //     //         switch (cfg.type)
        //     //         {
        //     //             case ArtExportAssetWhiteList.ArtOverrideType.MaxClipMemory:
        //     //                 maxClipMemory = cfg.value;
        //     //                 break;
        //     //             case ArtExportAssetWhiteList.ArtOverrideType.MaxTextureSize:
        //     //                 maxTextureSize = cfg.value;
        //     //                 break;
        //     //             case ArtExportAssetWhiteList.ArtOverrideType.MaxTextureMemory:
        //     //                 maxTextureMemory = cfg.value;
        //     //                 break;
        //     //             case ArtExportAssetWhiteList.ArtOverrideType.MaxTriangles:
        //     //                 maxTriangles = cfg.value;
        //     //                 break;
        //     //             default:
        //     //                 break;
        //     //         }
        //     //     }
        //     // }
        //
        //     //新的白名单实现
        //     if (whiteAssetType != null)
        //     {
        //         foreach (var cfg in whiteAssetType.overrides)
        //         {
        //             if (cfg.lod != lodLevel)
        //                 continue;
        //             switch (cfg.type)
        //             {
        //                 case ArtExportAssetWhiteList.ArtOverrideType.MaxClipMemory:
        //                     maxClipMemory = cfg.value;
        //                     break;
        //                 case ArtExportAssetWhiteList.ArtOverrideType.MaxTextureSize:
        //                     maxTextureSize = cfg.value;
        //                     break;
        //                 case ArtExportAssetWhiteList.ArtOverrideType.MaxTextureMemory:
        //                     maxTextureMemory = cfg.value;
        //                     break;
        //                 case ArtExportAssetWhiteList.ArtOverrideType.MaxTriangles:
        //                     maxTriangles = cfg.value;
        //                     break;
        //                 default:
        //                     break;
        //             }
        //         }
        //     }
        //
        //     infos.Add(string.Format("lod: {0} 资源总内存: {1} 模型内存: {2} 动画内存: {3}/{4} 贴图内存: {5}/{6} 贴图数量: {7} 模型顶点: {8} 模型面数: {9}/{10}", lodLevel,
        //         EditorUtility.FormatBytes(lodStat.Value.meshMem + lodStat.Value.clipMem + lodStat.Value.textureMem),
        //         EditorUtility.FormatBytes(lodStat.Value.meshMem),
        //         EditorUtility.FormatBytes(lodStat.Value.clipMem), EditorUtility.FormatBytes(maxClipMemory),
        //         EditorUtility.FormatBytes(lodStat.Value.textureMem), EditorUtility.FormatBytes(maxTextureMemory),
        //         lodStat.Value.textureNum, // 添加贴图数量
        //         lodStat.Value.vertexs, lodStat.Value.triangles, maxTriangles
        //         ));
        //
        //     // 检测性能
        //     if (maxClipMemory < lodStat.Value.clipMem)
        //     {
        //         errors.Add(new ArtExportError() { subNodePath = string.Empty, errorInfo = "lod: " + lodLevel + " 动画内存超标！ 上限: " + EditorUtility.FormatBytes(maxClipMemory) });
        //     }
        //     if (maxTextureMemory < lodStat.Value.textureMem)
        //     {
        //         errors.Add(new ArtExportError() { subNodePath = string.Empty, errorInfo = "lod: " + lodLevel + " 贴图内存超标！ 上限: " + EditorUtility.FormatBytes(maxTextureMemory) });
        //     }
        //     if (maxTriangles < lodStat.Value.triangles)
        //     {
        //         errors.Add(new ArtExportError() { subNodePath = string.Empty, errorInfo = "lod: " + lodLevel + " 模型面数超标！ 上限: " + maxTriangles });
        //     }

            //var mTextureSize = maxTextureSize * maxTextureSize;
            //foreach (var texture in staticData.textures)
            //{
            //    if ((texture.Value.width * texture.Value.height) > mTextureSize)
            //    {
            //        var nodes = texture.Value.nodes.ToList();
            //        errors.Add(new ArtExportError() { subNodePath = nodes.Count > 0 ? nodes[0] : string.Empty, errorInfo = "lod: " + lodLevel + " 贴图" + texture.Key + "尺寸超标！ 上限: " + maxTextureSize + "x" + maxTextureSize });
            //    }
            //}

            // foreach (var mesh in staticData.meshs)
            // {
            //     if (mesh.Key == "Library/unity default resources")
            //     {
            //         var nodes = mesh.Value.nodes.ToList();
            //         errors.Add(new ArtExportError() { subNodePath = nodes.Count > 0 ? nodes[0] : string.Empty, errorInfo = "lod: " + lodLevel + " 引用了unity内置模型！" });
            //     }
            // }
        // }

        CheckError(root);
        
        totalInfo = string.Join("\n", briefInfoForTooltips);
    }

    private void CheckCameraError(Transform transform)
    {
        var uiAdapter = transform.GetComponentsInChildren<TinySceneCameraUIAdapter>();
        if (uiAdapter.Length > 1)
        {
            errors.Add(new ArtExportError() { subNodePath = "", errorInfo = "存在多个TinySceneCameraUIAdapter组件，请删除多余的，只保留场景相机上面的" });
        }

        Dictionary<float, Camera> cameraDepthDict = new Dictionary<float, Camera>();
        var cameras = transform.GetComponentsInChildren<Camera>(true);
        foreach (var camera in cameras)
        {
            if (!camera.enabled)
                continue;

            if (camera.name == "_Shadow Camera_Directional Light")
                continue;

            if (cameraDepthDict.TryGetValue(camera.depth, out var oldCam))
            {
                errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(camera.transform, transform), errorInfo = string.Format("相机深度：{0} 与相机： {1}冲突，请修改其中一个相机的深度", camera.depth, GfxProfilerStaticData.GetNodePath(oldCam.transform, transform)) });
            }

            var tkpps = camera.GetComponent<TKPostProcessingStack>();
            if (tkpps == null)
            {
                errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(camera.transform, transform), errorInfo = "相机未挂载TKPostProcessingStack后处理组件，请添加" });
            }

            var tkppsh = camera.GetComponent<TKPostProcessingStackHelper>();
            if (tkppsh == null)
            {
                errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(camera.transform, transform), errorInfo = "相机未挂载TKPostProcessingStackHelper后处理组件，请添加" });
            }

            if (!camera.orthographic)
            {
                if (camera.nearClipPlane < 0.3f)
                {
                    errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(camera.transform, transform), errorInfo = "相机近裁面过小，当前为 " + camera.nearClipPlane + " 应该大于0.3" });
                }
                if (camera.farClipPlane > 1000f)
                {
                    errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(camera.transform, transform), errorInfo = "相机远裁面过大，当前为 " + camera.farClipPlane + " 不希望超过1000" });
                }
            }
        }
    }

    private void CheckRendererError(Transform transform)
    {
        var rs = transform.GetComponentsInChildren<Renderer>(true);
        foreach (var r in rs)
        {
            bool isPsr = r is ParticleSystemRenderer;
            bool isSpriteRenderer = r is SpriteRenderer;

            HashSet<Material> mats = new HashSet<Material>();
            foreach (var mat in r.sharedMaterials)
            {
                if (mat == null)
                    continue;

                // sprite可以不用管
                if (isSpriteRenderer && mat.name == "Sprites-Default")
                {
                    continue;
                }

                if (!isPsr && mats.Contains(mat) && mat.name != "Model_Default_Mat")
                {
                    errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(r.transform, transform), errorInfo = "存在相同材质，材质名: " + mat.name + " 建议合并相同材质的subMesh" });
                    continue;
                }
                mats.Add(mat);

                var matPath = AssetDatabase.GetAssetPath(mat);
                if (matPath == "Resources/unity_builtin_extra")
                {
                    errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(r.transform, transform), errorInfo = "存在Unity内置默认材质，材质名: " + mat.name });
                }
                else
                {
                    var shader = mat.shader;
                    if (shader == null || shader.name == "Hidden/InternalErrorShader")
                    {
                        errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(r.transform, transform), errorInfo = "材质引用了错误shader，材质名: " + mat.name });
                    }
                    else
                    {
                        var shaderPath = AssetDatabase.GetAssetPath(shader);
                        if (shaderPath == "Resources/unity_builtin_extra")
                        {
                            errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(r.transform, transform), errorInfo = "材质引用了内置shader，材质名: " + mat.name + " shader名: " + shader.name });
                        }
                    }
                }
            }
        }
    }

    public void CheckParticleSystemError(Transform transform)
    {
        var pss = transform.GetComponentsInChildren<ParticleSystem>(true);
        foreach (var ps in pss)
        {
            if(ps.emission.enabled)
            {
                // 添加检查：如果粒子发射数量为0，则报警
                if (ps.emission.rateOverTime.constant == 0 && ps.emission.rateOverDistance.constant == 0 && ps.emission.burstCount == 0)
                {
                    errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(ps.transform, transform), errorInfo = $"{ps.name} 的粒子发射器请删除" });
                }
                // 添加检查：粒子贴图尺寸与粒子大小比例
                else if (ps.emission.rateOverTime.constant > 1 || ps.emission.rateOverDistance.constant > 1 || ps.emission.burstCount > 1)
                {
                    // 获取渲染器
                    var renderer = ps.GetComponent<ParticleSystemRenderer>();
                    if (renderer != null && renderer.sharedMaterial != null)
                    {
                        // 获取材质中的主贴图
                        var mainTexture = renderer.sharedMaterial.mainTexture;
                        if (mainTexture != null)
                        {
                            int textureArea = mainTexture.width * mainTexture.height;
                            if (textureArea > ParticleTextureToSizeRatioThreshold)
                            {
                                // 获取粒子的最大尺寸
                                float startSize = ps.main.startSize.mode == ParticleSystemCurveMode.Constant ? 
                                    ps.main.startSize.constant : 
                                    Mathf.Max(ps.main.startSize.constantMin, ps.main.startSize.constantMax);


                                
                                // 检查是否有SizeOverLifetime模块
                                float sizeOverLifetimeMax = 1.0f;
                                // Debug.LogWarning($"接下来读取{ps.transform.name}的SizeOverLifetime模块的enabled属性");
           
                                // TODO:部份粒子访问sizeOverLifetime.enabled属性会导致crash，如t_kayleswordgold_kill这个预制中的jianguang04.具体原因未查出。先屏蔽此功能
                                // if ( ps.sizeOverLifetime.enabled)
                                // {
                                //     if (ps.sizeOverLifetime.size.mode == ParticleSystemCurveMode.Constant)
                                //     {
                                //         sizeOverLifetimeMax = ps.sizeOverLifetime.size.constant;
                                //     }
                                //     else if (ps.sizeOverLifetime.size.mode == ParticleSystemCurveMode.TwoConstants)
                                //     {
                                //         sizeOverLifetimeMax = Mathf.Max(ps.sizeOverLifetime.size.constantMin, ps.sizeOverLifetime.size.constantMax);
                                //     }
                                //     else if (ps.sizeOverLifetime.size.mode == ParticleSystemCurveMode.Curve)
                                //     {
                                //         // 如果是曲线，取曲线最大值
                                //         AnimationCurve curve = ps.sizeOverLifetime.size.curve;
                                //         if (curve == null)
                                //         {
                                //             errors.Add(new ArtExportError() { 
                                //                 subNodePath = GfxProfilerStaticData.GetNodePath(ps.transform, transform), 
                                //                 errorInfo = $"{ps.name} sizeOverLifetime.size.mode 设置了 Curve 模式，但是Curve是空，请检查" 
                                //             });
                                //         }
                                //         float maxVal = 0;
                                //         for (float t = 0; t <= 1.0f; t += 0.1f)
                                //         {
                                //             maxVal = Mathf.Max(maxVal, curve.Evaluate(t));
                                //         }
                                //         sizeOverLifetimeMax = maxVal;
                                //     }
                                //     else if (ps.sizeOverLifetime.size.mode == ParticleSystemCurveMode.TwoCurves)
                                //     {
                                //         // 如果是两条曲线，取两条曲线的最大值
                                //         AnimationCurve curveMin = ps.sizeOverLifetime.size.curveMin;
                                //         AnimationCurve curveMax = ps.sizeOverLifetime.size.curveMax;
                                //         if (curveMin == null)
                                //         {
                                //             errors.Add(new ArtExportError() { 
                                //                 subNodePath = GfxProfilerStaticData.GetNodePath(ps.transform, transform), 
                                //                 errorInfo = $"{ps.name} sizeOverLifetime.size.mode 设置了 Two Curves 模式，但是curveMin是空，请检查" 
                                //             });
                                //             sizeOverLifetimeMax = 0;
                                //         }
                                //         else if (curveMax == null)
                                //         {
                                //             errors.Add(new ArtExportError() { 
                                //                 subNodePath = GfxProfilerStaticData.GetNodePath(ps.transform, transform), 
                                //                 errorInfo = $"{ps.name} sizeOverLifetime.size.mode 设置了 Two Curves 模式，但是curveMax是空，请检查" 
                                //             });
                                //             sizeOverLifetimeMax = 0;
                                //         }
                                //         else
                                //         {
                                //             float maxVal = 0;
                                //             for (float t = 0; t <= 1.0f; t += 0.1f)
                                //             {
                                //                 maxVal = Mathf.Max(maxVal, curveMin.Evaluate(t), curveMax.Evaluate(t));
                                //             }
                                //             sizeOverLifetimeMax = maxVal;
                                //         }
                                //         
                                //     }
                                // }
                                
                                // 计算比例
                                float particleMaxSize = startSize * sizeOverLifetimeMax;
                                if (particleMaxSize > 0)
                                {
                                    float ratio = textureArea / particleMaxSize;
                                    // 屏蔽，待TA培训外包人员后再加回
                                    // if (ratio > ParticleTextureToSizeRatioThreshold)
                                    // {
                                    //     errors.Add(new ArtExportError() { 
                                    //         subNodePath = GfxProfilerStaticData.GetNodePath(ps.transform, transform), 
                                    //         errorInfo = $"{ps.name} 粒子使用的贴图过大，请缩小贴图尺寸。贴图尺寸：{mainTexture.width}x{mainTexture.height}，粒子尺寸：{particleMaxSize}" 
                                    //     });
                                    // }
                                }
                            }
                        }
                    }
                }
            }
            

            if (ps.main.maxParticles > 100 && ps.GetComponent<GfxLodParticleSystem_Unity>() == null)
            {
                // 最大粒子数超过100
                errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(ps.transform, transform), errorInfo = "最大粒子数超过100，并且未挂载GfxLodParticleSystem_Unity组件限制！" });
            }

            if (ps.shape.enabled && ps.shape.texture != null)
            {
                var texturePath = AssetDatabase.GetAssetPath(ps.shape.texture);
                var importer = AssetImporter.GetAtPath(texturePath) as TextureImporter;
                if (importer != null)
                {
                    if (!importer.isReadable)
                    {
                        errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(ps.transform, transform), errorInfo = "开启了ShapeModule，但是引用的贴图：" + texturePath + " 不可读写! " });
                    }
                }
            }

            if (ps.emission.enabled)
            {
                if (ps.emission.rateOverDistance.constant > 0 && ps.main.ringBufferMode == ParticleSystemRingBufferMode.Disabled)
                {
                    errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(ps.transform, transform), errorInfo = "使用了 Rate over Distance并且ringBufferMode被关闭了, 这个东西Unity有BUG，会崩溃，请不要使用！ " });
                }
            }

            var r = ps.GetComponent<ParticleSystemRenderer>();
            if (r != null && r.renderMode == ParticleSystemRenderMode.Mesh)
            {
                var meshs = new Mesh[r.meshCount];
                r.GetMeshes(meshs);
                int numTris = 0;
                foreach (var mesh in meshs)
                {
                    if (mesh != null && mesh.subMeshCount != 1)
                    {
                        numTris += (mesh.triangles.Length / 3);
                        errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(ps.transform, transform), errorInfo = mesh.name + "存在多个subMesh" });
                    }
                }
                if (numTris * ps.main.maxParticles > 10000)
                {
                    errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(ps.transform, transform), errorInfo = "粒子特效使用Mesh方式发射，且mesh面数峰值高于10000面，请降低最大粒子数或者减面" });
                }
            }

            if (ps.subEmitters.enabled)
            {
                for (int x = 0; x < ps.subEmitters.subEmittersCount; ++x)
                {
                    var subPs = ps.subEmitters.GetSubEmitterSystem(x);
                    if (subPs != null)
                    {
                        if (subPs.transform.parent == ps.transform.parent)
                        {
                            errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(ps.transform, transform), errorInfo = "存在subEmitter: " + subPs.name + "是同级节点!" });
                        }
                    }
                }
            }

            if (ps.customData.enabled)
            {
                var rObject = new SerializedObject(r);
                var useCustomVertexStreams = rObject.FindProperty("m_UseCustomVertexStreams");
                if (useCustomVertexStreams != null && useCustomVertexStreams.boolValue && r.sharedMaterial != null)
                {
                    if (!CheckCustomDataVaild(r, out string error))
                    {
                        errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(ps.transform, transform), errorInfo = error });
                    }
                }
            }

            if (ps.trails.enabled)
            {
                if (ps.trails.mode == ParticleSystemTrailMode.Ribbon)
                {
                    // 会导致一部分机型崩溃
                    errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(ps.transform, transform), errorInfo = "不允许使用Ribbon类型的拖尾" });
                }
            }
        }
    }

    public void CheckLineRenderer(Transform transform)
    {
        var lrs = transform.GetComponentsInChildren<LineRenderer>(true);
        foreach (var lr in lrs)
        {
            if (!lr.useWorldSpace)
            {
                errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(lr.transform, transform), errorInfo = "LineRenderer未勾选Use World Space! " });
            }
        }
    }

    public void CheckTrailRenderer(Transform transform)
    {
        var lrs = transform.GetComponentsInChildren<TrailRenderer>(true);
        foreach (var lr in lrs)
        {
            if (lr.autodestruct)
            {
                errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(lr.transform, transform), errorInfo = "TrailRenderer勾选autodestruct,会在播放一次后自动销毁，请取消勾选! " });
            }
        }
    }

    public void CheckSceneCamera(Transform transform)
    {
        if (transform == null)
            return;

        var cameras = transform.GetComponentsInChildren<Camera>();
        Dictionary<float, string> cameraDepthDict = new Dictionary<float, string>();
        foreach (var item in cameras)
        {
            if (item.enabled)
            {
                try
                {
                    string cameraPath = ZGame.GameUtil.GetHierarchyPath(item.transform);
                    if (item.GetComponent<TKPostProcessingStack>() == null)
                    {
                        errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(item.transform, transform), errorInfo = "相机未添加: TKPostProcessingStack! " });
                    }
                    if (item.GetComponent<TKPostProcessingStackHelper>() == null)
                    {
                        errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(item.transform, transform), errorInfo = "相机未添加: TKPostProcessingStackHelper! " });
                    }

                    if (cameraDepthDict.TryGetValue(item.depth, out string existCameraPath))
                    {
                        errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(item.transform, transform), errorInfo = $"相机和{existCameraPath}depth参数相同!手机上渲染可能会出错!" });
                    }
                    else
                    {
                        cameraDepthDict.Add(item.depth, cameraPath);
                    }

                    if (item.depth >= 2)
                    {
                        errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(item.transform, transform), errorInfo = "相机depth:{item.depth}大于2!这个相机的渲染效果会在部分界面产生问题！ " });
                    }
                }
                catch (Exception ex)
                {
                    TKFrame.Diagnostic.Error(ex);
                }
            }
        }
    }

    private static PropertyInfo ms_supportsMeshInstancingProperty;
    private static MethodInfo ms_CheckVertexStreamsMatchShaderMethod;

    private static MethodInfo ms_GetTextureBindingIndexMethod;

    private static readonly int[] ms_vertexStreamTexCoordChannels = { 0, 0, 0, 0, 2, 2, 2, 2, 1, 1, 3, 1, 1, 2, 3, 1, 3, 1, 3, 3, 1, 1, 1, 1, 2, 3, 4, 1, 2, 3, 4, 1, 2, 3, 4, 1, 2, 3, 4, 1, 2, 3, 1, 2, 3 };

    public static bool Has_SupportsMeshInstancing(ParticleSystemRenderer r)
    {
        if (ms_supportsMeshInstancingProperty == null)
        {
            ms_supportsMeshInstancingProperty = typeof(ParticleSystemRenderer).GetProperty("supportsMeshInstancing", BindingFlags.NonPublic | BindingFlags.Instance);
        }

        return (r.renderMode == ParticleSystemRenderMode.Mesh) && (bool)ms_supportsMeshInstancingProperty.GetValue(r);
    }

    public static bool Call_CheckVertexStreamsMatchShader(bool hasTangent, bool hasColor, int texCoordChannelCount, Material material, ref bool tangentError, ref bool colorError, ref bool uvError)
    {
        // 先走一遍这个函数，保证shader初始化了，这样才能用CheckVertexStreamsMatchShader
        if (ms_GetTextureBindingIndexMethod == null)
        {
            ms_GetTextureBindingIndexMethod = typeof(ShaderUtil).GetMethod("GetTextureBindingIndex", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Static);
        }
        ms_GetTextureBindingIndexMethod.Invoke(null, new object[] { material.shader, Shader.PropertyToID("_MainTex") });

        if (ms_CheckVertexStreamsMatchShaderMethod == null)
        {
            ms_CheckVertexStreamsMatchShaderMethod = typeof(ParticleSystem).GetMethod("CheckVertexStreamsMatchShader", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Static);
        }

        var args = new object[]
        {
                            hasTangent, hasColor, texCoordChannelCount, material,
                            null, null, null //ref
        };

        bool anyErrors = (bool)ms_CheckVertexStreamsMatchShaderMethod.Invoke(null, args);
        tangentError = (bool)args[4];
        colorError = (bool)args[5];
        uvError = (bool)args[6];

        return anyErrors;
    }

    private static bool CheckCustomDataVaild(ParticleSystemRenderer r, out string error)
    {
        Material material = r.sharedMaterial;
        if (material == null)
        {
            error = string.Empty;
            return true;
        }

        var m_HasTangent = false;
        var m_HasColor = false;
        int m_TexCoordChannelIndex = 0;
        int m_NumTexCoords = 0;
        var m_VertexStreams = new List<ParticleSystemVertexStream>();
        r.GetActiveVertexStreams(m_VertexStreams);
        bool m_HasGPUInstancing = Has_SupportsMeshInstancing(r);
        foreach (var vs in m_VertexStreams)
        {
            int numChannels = ms_vertexStreamTexCoordChannels[(int)vs];
            if (m_HasGPUInstancing && ((int)vs >= 8 || vs == ParticleSystemVertexStream.Color))
            {
                if (vs == ParticleSystemVertexStream.Color)
                {
                    m_HasColor = true;
                }
            }
            else if (numChannels != 0)
            {
                m_TexCoordChannelIndex += numChannels;
                if (m_TexCoordChannelIndex >= 4)
                {
                    m_TexCoordChannelIndex -= 4;
                    m_NumTexCoords++;
                }
            }
            else
            {
                if (vs == ParticleSystemVertexStream.Tangent)
                    m_HasTangent = true;
                if (vs == ParticleSystemVertexStream.Color)
                    m_HasColor = true;
            }
        }
        int totalChannelCount = m_NumTexCoords * 4 + m_TexCoordChannelIndex;
        bool tangentError = false, colorError = false, uvError = false;
        bool anyErrors = Call_CheckVertexStreamsMatchShader(m_HasTangent, m_HasColor, totalChannelCount, material, ref tangentError, ref colorError, ref uvError);
        if (anyErrors)
        {
            StringBuilder sb = new StringBuilder();
            //var channel = ShaderUtil.GetShaderChannelMask(material.shader);
            sb.AppendFormat("材质与顶点流不匹配：");
            if (tangentError)
                sb.Append("切线 ");
            if (colorError)
                sb.Append("颜色 ");
            if (uvError)
                sb.Append("纹理坐标 ");
            error = sb.ToString();
            return false;
        }

        error = string.Empty;
        return true;
    }

    private void CheckPlayableDirector(Transform transform)
    {
        var pss = transform.GetComponentsInChildren<PlayableDirector>(true);
        foreach (var playable in pss)
        {
            if (CheckPlayableDirector(playable))
            {
                errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(playable.transform, transform), errorInfo = "存在冗余引用，会导致资源加载错误，请删除组件并重新添加!" });
            }
        }
    }

    public static bool CheckPlayableDirector(PlayableDirector playable)
    {
        Dictionary<UnityEngine.Object, UnityEngine.Object> bindings = new Dictionary<UnityEngine.Object, UnityEngine.Object>();
        if (playable.playableAsset != null)
        {
            foreach (var pb in playable.playableAsset.outputs)
            {
                var key = pb.sourceObject;
                var value = playable.GetGenericBinding(key);
                if (!bindings.ContainsKey(key) && key != null)
                {
                    bindings.Add(key, value);
                }
            }
        }

        bool hasChanged = false;
        var dirSO = new SerializedObject(playable);
        var sceneBindings = dirSO.FindProperty("m_SceneBindings");
        for (var i = sceneBindings.arraySize - 1; i >= 0; i--)
        {
            var binding = sceneBindings.GetArrayElementAtIndex(i);
            var key = binding.FindPropertyRelative("key");
            if (key.objectReferenceValue == null || !bindings.ContainsKey(key.objectReferenceValue))
            {
                hasChanged = true;
            }
        }

        return hasChanged;
    }

    private static List<Type> blacklist = new List<Type>()
    {
        typeof(EffectMaterialScript), typeof(PBRTools.CustomLightingTool)
    };

    private void CheckNameError(string assetPath)
    {
        if (Path.GetFileNameWithoutExtension(assetPath).Contains(" "))
            errors.Add(new ArtExportError() { subNodePath = null, errorInfo = "资源名字中包含空格!" });

        var importer = AssetImporter.GetAtPath(assetPath);
        if (importer != null)
        {
            if (!string.IsNullOrEmpty(importer.assetBundleName) && importer.assetBundleVariant != "unity3d")
            {
                errors.Add(new ArtExportError() { subNodePath = null, errorInfo = "资源AssetBundle名字设置不对，请清理!" });
            }
        }
    }

    private void CheckMissing(Transform transform, string assetPath)
    {
        if (string.IsNullOrEmpty(assetPath))
            return;

        var components = transform.GetComponentsInChildren<Component>(true);
        Transform curTransform = null;
        for (int i = 0; i < components.Length; ++i)
        {
            var component = components[i];
            if (component == null)
            {
                errors.Add(new ArtExportError()
                {
                    subNodePath = curTransform != null ? GfxProfilerStaticData.GetNodePath(curTransform.transform, transform) : string.Empty,
                    errorInfo = (curTransform != null ? curTransform.name : string.Empty) + "存在丢失的组件!"
                });
                continue;
            }
            if (component is Transform)
            {
                curTransform = component as Transform;
            }

            if (blacklist.Contains(component.GetType()))
                continue;

            var serializedObject = new SerializedObject(component);
            var iter = serializedObject.GetIterator();
            while (iter.NextVisible(true))
            {
                if (iter.propertyType == SerializedPropertyType.ObjectReference)
                {
                    if (iter.objectReferenceValue == null && iter.objectReferenceInstanceIDValue != 0)
                    {
                        if (iter.name == "m_Mesh" && component is ParticleSystemRenderer)
                        {
                            var psr = component as ParticleSystemRenderer;
                            if (psr.renderMode != ParticleSystemRenderMode.Mesh)
                                continue;       // 忽略掉不是mesh渲染的情况
                        }

                        errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(component.transform, transform), errorInfo = "组件:" + component.GetType() + " 属性:" + iter.displayName + "引用的资源丢失!" });
                    }
                }
                if ((iter.propertyType == SerializedPropertyType.ObjectReference ||
                              iter.propertyType == SerializedPropertyType.ExposedReference) &&
                             iter.type != "PPtr<MonoScript>" &&
                             iter.type != "PPtr<EditorExtension>")
                {
                    if (iter.objectReferenceValue != null)
                    {
                        string path = AssetDatabase.GetAssetPath(iter.objectReferenceValue);
                        if (path.EndsWith(".prefab") && assetPath != path)
                        {
                            bool result = true;
                            if (iter.objectReferenceValue is Transform)
                            {
                                Transform t = iter.objectReferenceValue as Transform;
                                if (t.parent == null)
                                    result = false;
                            }
                            else if (iter.objectReferenceValue is GameObject)
                            {
                                GameObject gameObject = iter.objectReferenceValue as GameObject;
                                if (gameObject.transform.parent == null)
                                    result = false;
                            }
#if ACGGAME_CLIENT && !LOGIC_THREAD && !OUTSOURCE
                            else if (iter.objectReferenceValue is Lucifer.ActCore.CharacterConfig)
                            {
                                Lucifer.ActCore.CharacterConfig c = iter.objectReferenceValue as Lucifer.ActCore.CharacterConfig;
                                if (c.transform.parent == null)
                                    result = false;
                            }
#endif
                            else if (iter.objectReferenceValue is Component)
                            {
                                var behaviour = iter.objectReferenceValue as Component;
                                if (behaviour.transform.parent == null)
                                    result = false;
                            }
                            if (result)
                            {
                                errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(component.transform, transform), errorInfo = "组件:" + component.GetType() + " 属性:" + iter.displayName + "错误的引用到了外部prefab内部资源!" });
                            }
                        }
                    }
                }
            }
        }
    }

    private void CheckModelTriLimit()
    {
        foreach (var item in staticData.meshs)
        {
            if (item.Value.triangles >= 65535)
            {
                foreach (var node in item.Value.nodes)
                {
                    errors.Add(new ArtExportError() { subNodePath = node, errorInfo = "引用的模型面数超过65535! 路径：" + item.Key });
                }
            }
        }
    }

    private void CheckTextureError()
    {
        foreach (var item in staticData.textures)
        {
            if (item.Key.ToLower().EndsWith(ArtExportAsset.DDS_IMAGE))
            {
                foreach (var node in item.Value.nodes)
                {
                    errors.Add(new ArtExportError() { subNodePath = node, errorInfo = "引用了dds图! 路径：" + item.Key });
                }
            }
        }
    }

    // 如果是商业化资源，则检测是否有引用到战斗内的材质、贴图
    private void CheckResHasBattleRef()
    {
        // 如果是战斗用的资源 则忽略掉这个检测
        if (mainPath.Contains("/set/") || mainPath.Contains("/battle/") 
            || mainPath.Contains("/model_res/hero/") || mainPath.Contains("/Art_TFT_Raw/hero/") 
            || mainPath.Contains("/model_res/Monster/") || mainPath.Contains("/model_res/summoned/"))
            return;

        foreach (var item in staticData.textures)
        {
            if (item.Key.Contains("/battle/"))
            {
                foreach (var node in item.Value.nodes)
                {
                    errors.Add(new ArtExportError() { subNodePath = node, errorInfo = "引用了局内贴图! 路径：" + item.Key });
                }
            }
        }

        foreach (var item in staticData.meshs)
        {
            if (item.Key.Contains("/battle/"))
            {
                foreach (var node in item.Value.nodes)
                {
                    errors.Add(new ArtExportError() { subNodePath = node, errorInfo = "引用了局内模型! 路径：" + item.Key });
                }
            }
        }

        foreach (var item in staticData.clips)
        {
            if (item.Key.Contains("/battle/"))
            {
                foreach (var node in item.Value.nodes)
                {
                    errors.Add(new ArtExportError() { subNodePath = node, errorInfo = "引用了局内动作! 路径：" + item.Key });
                }
            }
        }

        foreach (var item in staticData.materials)
        {
            if (item.Key.Contains("/battle/"))
            {
                foreach (var node in item.Value.nodes)
                {
                    errors.Add(new ArtExportError() { subNodePath = node, errorInfo = "引用了局内材质! 路径：" + item.Key });
                }
            }
        }
    }

    private GfxRootLodView m_lodView = new GfxRootLodView();
    public void CheckGfxLod(Transform transform)
    {
        var gfxRoot = transform.GetComponent<GfxRoot>();
        if (gfxRoot != null)
        {
            m_lodView.UpdateLods(gfxRoot);
            var lodCount = m_lodView.GetLodCount();
            if (lodCount > 8)       // 过于简单的特效也可以忽略掉lod设置
            {
                int psCount = gfxRoot.GetComponentsInChildren<ParticleSystem>(true).Length;
                float standendLodPercent = 0.6f;
                //if (mainPath.Contains("/battle/"))
                //    standendLodPercent = 0.4f;
                standendLodPercent = Mathf.Max((lodCount - psCount) / (float)lodCount, standendLodPercent);
                var lodLodPercent = m_lodView.GetPercent(GfxLod.Low);
                if (lodLodPercent > standendLodPercent)
                {
                    errors.Add(new ArtExportError() { subNodePath = "", errorInfo = string.Format("没有为这个特效设置合理的LOD，LOD=Low的节点应低于{0:P2}，当前是: {1:P2}", standendLodPercent, lodLodPercent) });
                }
            }
        }
    }

    private int GetSpitCount(string str, char spitChar)
    {
        int count = 0;
        foreach (var c in str)
        {
            if (c == spitChar)
                count++;
        }
        return count;
    }

    public void CheckTinyOrginModel(Transform transform)
    {
        var characterHangPoint = transform.GetComponentInChildren<CharacterHangPoint>();
        if (characterHangPoint != null)
        {
            errors.Add(new ArtExportError() { subNodePath = "", errorInfo = $"小小英雄原始模型存在CharacterHangPoint组件，请删除！" });
        }

        var animtor = transform.GetComponent<Animator>();
        if (animtor == null)
        {
            errors.Add(new ArtExportError() { subNodePath = "", errorInfo = $"小小英雄原始模型找不到Animator组件！" });
        }
        else
        {
            if (animtor.avatar == null)
            {
                errors.Add(new ArtExportError() { subNodePath = "", errorInfo = $"小小英雄原始模型的Avatar为空！" });
            }
            if (animtor.runtimeAnimatorController != null)
            {
                errors.Add(new ArtExportError() { subNodePath = "", errorInfo = $"小小英雄原始模型的AnimatorController不为空！请删除" });
            }
        }

        var renderers = transform.GetComponentsInChildren<Renderer>();
        foreach (var renderer in renderers)
        {
            foreach (var m in renderer.sharedMaterials)
            {
                if (m.name != "Model_Default_Mat")
                {
                    errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(renderer.transform, transform), errorInfo = $"小小英雄原始模型的材质: {m.name}必须是: Model_Default_Mat" });
                }
            }
        }
    }

    public void CheckTinyModel(Transform transform, bool checkHangPoint = true)
    {
        if (!transform.localRotation.eulerAngles.Equals(Vector3.zero))
        {
            errors.Add(new ArtExportError() { subNodePath = "", errorInfo = $"小小英雄旋转角度: {transform.localRotation.eulerAngles}未归零！" });
        }

        var meshes = transform.GetComponentsInChildren<MeshFilter>(true);
        foreach (var item in meshes)
        {
            if (item.sharedMesh != null)
            {
                var meshPath = AssetDatabase.GetAssetPath(item.sharedMesh);
                if (!meshPath.EndsWith(".mesh", StringComparison.OrdinalIgnoreCase))
                {
                    errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(item.transform, transform), errorInfo = $"小小英雄用到的模型[{meshPath}]不是通过模型导入工具导入进来的！" });
                }
            }
        }

        var animator = transform.GetComponentInChildren<Animator>();
        if (animator == null)
        {
            errors.Add(new ArtExportError() { subNodePath = "", errorInfo = $"小小英雄缺少Animator组件！" });
        }
        else if (animator.runtimeAnimatorController == null)
        {
            errors.Add(new ArtExportError() { subNodePath = "", errorInfo = $"小小英雄的Animator组件没有挂在状态机控制器！" });
        }
        else if (animator.avatar == null)
        {
            errors.Add(new ArtExportError() { subNodePath = "", errorInfo = $"小小英雄的Animator组件没有挂Avater！" });
        }
        //else if (transform.name.EndsWith("_h_show") && GetSpitCount(transform.name, '_') == 4)      // 如果是局外高模，并且不是换皮小小英雄 才去检查这玩意 (TA要求不检查这玩意)
        //{
        //    CheckIdleRootLocOffset(animator, transform);
        //}

        if (checkHangPoint)
        {
            var characterHangPoint = transform.GetComponentInChildren<CharacterHangPoint>();
            if (characterHangPoint == null)
            {
                errors.Add(new ArtExportError() { subNodePath = "", errorInfo = $"小小英雄缺少CharacterHangPoint组件！" });
            }
            else
            {
                foreach (var pointData in characterHangPoint.pointPosData)
                {
                    if (pointData.bindTrans == null)
                    {
                        errors.Add(new ArtExportError() { subNodePath = "", errorInfo = $"小小英雄CharacterHangPoint组件中，有挂点: {pointData.supportHangPointType}为空！请重置挂点！" });
                    }
                }
            }
        }
    }

    private static List<string> idleNameArr = new List<string>()
        {
            "idle",
            "idle_UI",
            "UI_Normal_idle",
            "idle_Trunk",
            "idle_UI_Trunk",
            "UI_Normal_idle_Trunk",
        };

    private void CheckIdleRootLocOffset(Animator animator, Transform transform)
    {
        AnimationClip[] clipArr = animator.runtimeAnimatorController.animationClips;
        AnimationClip idleClip = null;
        
        for (int i = 0; i < clipArr.Length; i++)
        {
            AnimationClip clip = clipArr[i];
            if (clip != null)
            {
                var arr = clip.name.Split("@");
                var name = clip.name;
                if (arr.Length > 1) 
                {
                    name = arr[1];
                }
                if (idleNameArr.Contains(name))
                {
                    idleClip = clip;
                }
            }
        }
        if (idleClip == null)
        {
            errors.Add(new ArtExportError() { subNodePath = "", errorInfo = $"小小英雄动画状态机找不到: {idleNameArr[0]}、{idleNameArr[1]}或者{idleNameArr[2]}状态！" });
            return;
        }

        var body = transform.Find("t_body");
        if (body == null)
        {
            errors.Add(new ArtExportError() { subNodePath = "", errorInfo = $"小小英雄找不到: t_body节点！" });
            return;
        }
        var skinMeshRenderer = body.GetComponent<SkinnedMeshRenderer>();
        if (skinMeshRenderer == null)
        {
            errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(body, transform), errorInfo = $"小小英雄的t_body节点没有SkinnedMeshRenderer！" });
            return;
        }
        if (skinMeshRenderer.sharedMesh == null)
        {
            errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(body, transform), errorInfo = $"小小英雄的t_body节点的SkinnedMeshRenderer组件没有Mesh！" });
            return;
        }

        var avatar = animator.avatar;
        if (avatar != null)
        {
            var meshObject = new SerializedObject(skinMeshRenderer.sharedMesh);
            var boneNameHashProperty = meshObject.FindProperty("m_RootBoneNameHash");
            int boneNameHash = boneNameHashProperty.intValue;
            meshObject.Dispose();

            Dictionary<int, string> tosDict = GetTOSDict(avatar);
            if (tosDict.TryGetValue(boneNameHash, out string boneName))
            {
                EditorCurveBinding[] bindings = AnimationUtility.GetCurveBindings(idleClip);
                foreach (EditorCurveBinding binding in bindings)
                {
                    if (binding.path.EndsWith(boneName) && (binding.propertyName == "m_LocalPosition.x"))
                    {
                        AnimationCurve curve = AnimationUtility.GetEditorCurve(idleClip, binding);
                        var pos1 = curve.Evaluate(0.2f);
                        var pos2 = curve.Evaluate(0.5f);
                        var pos3 = curve.Evaluate(0.7f);
                        var pos4 = curve.Evaluate(0.9f);
                        if (Mathf.Abs(pos1) > 0.1f && Mathf.Abs(pos2) > 0.1f && Mathf.Abs(pos3) > 0.1f && Mathf.Abs(pos4) > 0.1f)
                        {
                            errors.Add(new ArtExportError() { subNodePath = "", errorInfo = $"小小英雄的{idleClip.name}的{boneName}骨骼位置x轴：{pos1}与0偏离过大！请归零！" });
                        }
                    }
                }
            }
        }
    }

    private static Dictionary<int, string> GetTOSDict(Avatar avatar)
    {
        var avaterObject = new SerializedObject(avatar);
        var TOSProperty = avaterObject.FindProperty("m_TOS");
        // <key: 骨骼hash, value: 骨骼名字>
        Dictionary<int, string> tosDict = new Dictionary<int, string>();
        for (int i = 0; i < TOSProperty.arraySize; ++i)
        {
            var element = TOSProperty.GetArrayElementAtIndex(i);
            var key = element.FindPropertyRelative("first");
            var value = element.FindPropertyRelative("second");
            tosDict.Add(key.intValue, value.stringValue);
        }
        avaterObject.Dispose();
        return tosDict;
    }
    public static void CheckTinyError2(int id, string path, string sceneName, StringBuilder sb)
    {
        ArtExportPrefabAsset asset = new ArtExportPrefabAsset(path);
        var root = AssetDatabase.LoadAssetAtPath<GameObject>(path).transform;
        asset.CheckTinyModel(root, false);
        if (asset.errors.Count > 0)
        {
            sb.AppendLine(string.Format("ID: {0} 资源: {1} 场景名字: {2}",id , path, sceneName));
            for (int j = 0; j < asset.errors.Count; j++)
            {
                var error = asset.errors[j];
                sb.AppendLine($"【错误{j}】 节点：{error.subNodePath} 错误信息: {error.errorInfo}");
            }
        }
    }
    public static void CheckCameraError2(int id, string path, StringBuilder sb)
    {
        ArtExportPrefabAsset asset = new ArtExportPrefabAsset(path);
        var root = AssetDatabase.LoadAssetAtPath<GameObject>(path).transform;
        asset.CheckCameraError(root);
        if (asset.errors.Count > 0)
        {
            sb.AppendLine(string.Format("ID: {0} 资源: {1}", id, path));
            for (int j = 0; j < asset.errors.Count; j++)
            {
                var error = asset.errors[j];
                sb.AppendLine($"【错误{j}】 节点：{error.subNodePath} 错误信息: {error.errorInfo}");
            }
        }
    }
    [MenuItem("Tools/PrefabGenTool/CheckAllTiny")]
    public static void CheckAllTiny()
    {
        StringBuilder sb = new StringBuilder();
        ZGame.DataBaseManager.Instance.Initialize();
        HashSet<string> checkedScene = new HashSet<string>();
        var cfgs = ZGame.DataBaseManager.Instance.GetItems();
        int errorCount = 0;

        var tinyIdArr = File.ReadAllLines(Application.dataPath + "/scan_tiny_id.txt");
        HashSet<int> tinyIds = new HashSet<int>();
        foreach (var tinyIdStr in tinyIdArr)
        {
            if (int.TryParse(tinyIdStr, out int tinyId))
            {
                if (!tinyIds.Contains(tinyId))
                    tinyIds.Add(tinyId);
            }
        }

        foreach (var cfg in cfgs)
        {
            if (ZGameChess.ChessItemModel.IsTinyItem(cfg.Key) && tinyIds.Contains(cfg.Key))
            {
                string abName = ZGameChess.ChessPlayerUnit.ConvertAssetbundleName(cfg.Value.sPreviewResource);
                string assetName = cfg.Value.sPreviewResource;
                List<string> assets = new List<string>();
                //assets.AddRange(AssetDatabase.GetAssetPathsFromAssetBundleAndAssetName(abName + ".unity3d", assetName));
                assetName = cfg.Value.sPreviewResource.Replace("_show", "_h_show");
                abName = ZGameChess.ChessPlayerUnit.ConvertAssetbundleName(assetName);
                assets.AddRange(AssetDatabase.GetAssetPathsFromAssetBundleAndAssetName(abName + ".unity3d", assetName));
                //assetName = cfg.Value.sPreviewResource.Replace("_show", "_lobby_show");
                //abName = ZGameChess.ChessPlayerUnit.ConvertAssetbundleName(assetName);
                //assets.AddRange(AssetDatabase.GetAssetPathsFromAssetBundleAndAssetName(abName + ".unity3d", assetName));
                foreach (var path in assets)
                {
                    try
                    {
                        var tinyCfg = ZGame.DataBaseManager.Instance.SearchTinyHero(cfg.Key);
                        if (tinyCfg != null && !string.IsNullOrEmpty(tinyCfg.sHighQualityRes))
                        {
                            var length = sb.Length;
                            CheckTinyError2(cfg.Key, path, tinyCfg.sHighQualityRes, sb);

                            var scenePath = ConstVar.TinyScenePath + "/" + tinyCfg.sHighQualityRes + ".prefab";
                            CheckCameraError2(cfg.Key, scenePath, sb);

                            // 有些入场动画可能也有相机 也要检查一下
                            var ltCfgPath = LittleLegendCfg.PATH + assetName + ".asset";
                            var ltCfg = AssetDatabase.LoadAssetAtPath<LittleLegendCfg>(ltCfgPath);
                            if (ltCfg != null && ltCfg.actionEventCfg != null)
                            {
                                foreach (var clipdata in ltCfg.actionEventCfg.clipData)
                                {
                                    if (clipdata.clipName.ToLower().Contains("enter"))
                                    {
                                        var effect = clipdata.effect;
                                        if (effect != null)
                                        {
                                            var effectPath = AssetDatabase.GetAssetPath(effect);
                                            CheckCameraError2(cfg.Key, effectPath, sb);
                                        }
                                    }
                                }
                            }

                            checkedScene.Add(tinyCfg.sHighQualityRes);

                            if (sb.Length != length)
                            {
                                sb.AppendLine();
                                ++errorCount;
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        Diagnostic.Error("id: {0} path: {1} exception: {2}", cfg.Key, path, e);
                    }
                }
            }
        }
        //var guids = AssetDatabase.FindAssets("t:Prefab", new string[] { ConstVar.TinyModelPath });
        //for (int i = 0; i < guids.Length; ++i)
        //{
        //    var guid = guids[i];
        //    var path = AssetDatabase.GUIDToAssetPath(guid);
        //    try
        //    {
        //        ArtExportPrefabAsset asset = new ArtExportPrefabAsset(path);
        //        var root = AssetDatabase.LoadAssetAtPath<GameObject>(path).transform;
        //        asset.CheckTinyModel(root);
        //        if (asset.errors.Count > 0)
        //        {
        //            sb.AppendLine("资源: " + path);
        //            for (int j = 0; j < asset.errors.Count; j++)
        //            {
        //                var error = asset.errors[j];
        //                sb.AppendLine($"【错误{j}】 节点：{error.subNodePath} 错误信息: {error.errorInfo}");
        //            }
        //        }
        //    }
        //    catch (Exception e)
        //    {
        //        Diagnostic.Error("guid: {0} path: {1} exception: {2}", guid, path, e);
        //    }
        //}

        sb.AppendFormat("错误小小英雄数量: {0}", errorCount);

        WeChatTool.SendFile("78bd12f1-7215-4c7b-9924-5752be4ba63e", "小小英雄错误信息扫描.txt", sb.ToString());
    }

    public void CheckLittleLegendScene(Transform transform)
    {
        if (CommonUtil.FindTransform(transform, "CharRoot") == null)
        {
            errors.Add(new ArtExportError() { subNodePath = "", errorInfo = "缺少小小英雄出生点 （CharRoot）" });
        }

        if (CommonUtil.FindTransform(transform, "RTCenter") == null)
        {
            errors.Add(new ArtExportError() { subNodePath = "", errorInfo = "缺少小小英雄Idle位置 （RTCenter）" });
        }

        var sceneTool = transform.GetComponent<SceneTool>();
        if (sceneTool == null)
        {
            errors.Add(new ArtExportError() { subNodePath = "", errorInfo = string.Format("导出的节点: {0} 中未检测到SceneTool组件", transform.name) });
        }

        if (sceneTool != null)
        {
            if (transform.GetComponentsInChildren<Light>().Length > 1 && sceneTool.m_lightLod.enable == false)
            {
                errors.Add(new ArtExportError() { subNodePath = "", errorInfo = "光照数量大于1，并且未开启lod" });
            }
        }

        var sceneTimeline = transform.GetComponentInChildren<TinySceneTimeline>();
        if (sceneTimeline != null)
        {
            foreach (var timeline in sceneTimeline.m_timelines)
            {
                if (timeline.timeline == null)
                {
                    errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(sceneTimeline.transform, transform), errorInfo = "TinySceneTimeline组件存在空Timeline，请挂载或删除，若场景存在Timeline但是不挂载好的话，连续打开这个场景显示会出问题" });
                }
            }
        }

        if (ScanTools.ScanSceneTool.ScanScene(transform.gameObject, out string errorContent))
        {
            errors.Add(new ArtExportError() { subNodePath = "", errorInfo = errorContent });
        }
    }

    public void CheckBattleMap(Transform transform)
    {
        var sceneTool = transform.GetComponent<SceneTool>();
        if (sceneTool == null)
        {
            errors.Add(new ArtExportError() { subNodePath = "", errorInfo = string.Format("导出的节点: {0} 中未检测到SceneTool组件", transform.name) });
        }

        if (transform.GetComponentsInChildren<Light>().Length > 1 && sceneTool.m_lightLod.enable == false)
        {
            errors.Add(new ArtExportError() { subNodePath = "", errorInfo = "光照数量大于1，并且未开启lod" });
        }

        if (ScanTools.ScanSceneTool.ScanScene(transform.gameObject, out string errorContent))
        {
            errors.Add(new ArtExportError() { subNodePath = "", errorInfo = errorContent });
        }

        var renderers = transform.GetComponentsInChildren<Renderer>();
        foreach (var r in renderers)
        {
            if (r is MeshRenderer && r.GetComponent<MeshFilter>() != null)
                continue;
            if (r.gameObject.layer == GameObjectLayer.MapObjs)
            {
                errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(r.transform, transform), errorInfo = "节点的Layer设置成MapObjs，但是节点没有MeshFilter组件！" });
            }
        }
    }

    public void CheckSpine(Transform transform)
    {
        var renderers = transform.GetComponentsInChildren<SkeletonRenderer>();
        foreach (var r in renderers)
        {
            if (r.useClipping)
            {
                errors.Add(new ArtExportError() { subNodePath = GfxProfilerStaticData.GetNodePath(r.transform, transform), errorInfo = "Spine节点开启了useClipping，会耗费大量性能，请关闭！" });
            }
        }
    }

    //private static bool CheckModel(string assetName)
    //{
    //    List<int> meshTrisList = new List<int>()
    //        {
    //            0, 0, 0
    //        };
    //    List<bool> meshTrisOpenList = new List<bool>()
    //        {
    //            false, true, false
    //        };
    //    ModelImportLimitConfig.AssetType assetType = ModelImportLimitConfig.AssetType.英雄;
    //    if (assetName.StartsWith("t_"))
    //    {
    //        if (assetName.EndsWith("_h"))
    //            assetType = ModelImportLimitConfig.AssetType.小小英雄_局外;
    //        else
    //            assetType = ModelImportLimitConfig.AssetType.小小英雄_局内;
    //    }
    //    List<string> lodNameErrorList = new List<string>();
    //    for (int i = 0; i < modelAssets.Length; ++i)
    //    {
    //        var modelAsset = modelAssets[i];
    //        if (modelAsset is Mesh)
    //        {
    //            string meshName = modelAsset.name;
    //            int meshTris = GetMeshTris(modelAsset as Mesh);
    //            if (meshName.Contains("lod_h"))
    //            {
    //                meshTrisList[(int)ModelImportLimitConfig.AssetLod.高模] += meshTris;
    //            }
    //            else if (meshName.Contains("lod_l"))
    //            {
    //                meshTrisOpenList[(int)ModelImportLimitConfig.AssetLod.低模] = true;
    //                meshTrisList[(int)ModelImportLimitConfig.AssetLod.低模] += meshTris;
    //            }
    //            else if (meshName.Contains("lod_s"))
    //            {
    //                meshTrisOpenList[(int)ModelImportLimitConfig.AssetLod.Pad] = true;
    //                meshTrisList[(int)ModelImportLimitConfig.AssetLod.Pad] += meshTris;
    //            }
    //            else
    //            {
    //                meshTrisList[(int)ModelImportLimitConfig.AssetLod.高模] += meshTris;
    //                meshTrisList[(int)ModelImportLimitConfig.AssetLod.低模] += meshTris;
    //                meshTrisList[(int)ModelImportLimitConfig.AssetLod.Pad] += meshTris;
    //            }
    //        }
    //        else if (modelAsset is GameObject)
    //        {
    //            if (assetType == ModelImportLimitConfig.AssetType.英雄)
    //            {
    //                var go = modelAsset as GameObject;
    //                var rs = go.GetComponentsInChildren<Renderer>(true);
    //                foreach (var r in rs)
    //                {
    //                    if (r.name.Contains("lod_s") || r.name.Contains("lod_h") || r.name.Contains("lod_l"))
    //                        continue;
    //                    else
    //                        lodNameErrorList.Add(r.name);
    //                }
    //            }
    //        }
    //    }

    //    bool isTrisVaild = true;
    //    StringBuilder sb = new StringBuilder();
    //    sb.AppendFormat("模型名字： {0}, \n模型类型: {1}\n", assetName, assetType);
    //    for (int i = 0; i < meshTrisList.Count; ++i)
    //    {
    //        if (!meshTrisOpenList[i])
    //            continue;

    //        var meshTris = meshTrisList[i];
    //        ModelImportLimitConfig.AssetLod lod = (ModelImportLimitConfig.AssetLod)i;

    //        int maxTris = ModelImportLimitConfig.GetMaxTris(assetName, assetType, lod);
    //        if (maxTris > 0)
    //        {
    //            if (meshTris > maxTris)
    //            {
    //                isTrisVaild = false;
    //                sb.AppendFormat("lod: {0} 面数: {1} 最高限制面数: {2}\n", lod, meshTris, maxTris);
    //            }
    //        }
    //    }
    //    if (!isTrisVaild)
    //        sb.AppendLine("面数过多！无法导入！请让美术减面获得让TA加入白名单！");
    //    if (lodNameErrorList.Count != 0)
    //    {
    //        sb.AppendLine("LOD节点名字错误：" + string.Join(",", lodNameErrorList));
    //        sb.AppendLine("请让美术修改模型的节点名字！");
    //    }
    //    bool vaild = isTrisVaild && lodNameErrorList.Count == 0;
    //    if (!vaild)
    //        errorStr = sb.ToString();
    //    else
    //        errorStr = string.Empty;
    //    return vaild;
    //}

    private void CheckDependencies()
    {
        var asset = new ArtExportAsset(mainPath);
        {
            if (!ArtExportCore.IsAssetPathBacklist(mainPath) && ArtExportCore.ContainsNonAscii(mainPath, out var ch, out var index))
            {
                errors.Add(new ArtExportError() { subNodePath = "", errorInfo = string.Format("文件: {0} 存在非ASCII码字符 [{1}] char. 位置: {2} 无法提交！", mainPath, ch, index) });
            }
        }

        foreach (var item in asset.dependencyPaths)
        {
            if (!ArtExportCore.IsAssetPathBacklist(item) && ArtExportCore.ContainsNonAscii(item, out var ch, out var index))
            {
                errors.Add(new ArtExportError() { subNodePath = "", errorInfo =string.Format("引用的文件: {0} 存在非ASCII码字符 [{1}] char. 位置: {2} 无法提交！", item, ch, index) });
            }
        }
    }

    public void CheckError(Transform transform)
    {
        CheckUseUnityDefaultResource();
        
        CheckDependencies();

        CheckModelTriLimit();

        CheckCameraError(transform);

        CheckRendererError(transform);

        CheckParticleSystemError(transform);

        CheckLineRenderer(transform);

        CheckTrailRenderer(transform);

        CheckPlayableDirector(transform);

        CheckMissing(transform, mainPath);

        // 在 CheckGfxLod 之前调用 CheckGfxFrameworkError
        // 屏蔽，待TA培训外包人员后再加回
        //CheckGfxFrameworkError(transform);

        CheckGfxLod(transform);

        CheckSceneCamera(transform);

        CheckSpine(transform);

        CheckNameError(mainPath);

        CheckTextureError();

        CheckResHasBattleRef();
        
        // 屏蔽，待TA培训外包人员后再加回
        //CheckMeshRenderError(transform);

        if (mainPath.StartsWith(ConstVar.TinyScenePath))
        {
            CheckLittleLegendScene(transform);
        }
        else if (mainPath.StartsWith(ConstVar.MAP_DIR))
        {
            CheckBattleMap(transform);
        }
        else if (mainPath.StartsWith(ConstVar.TinyModelPath))
        {
            CheckTinyModel(transform);
        }
        else if (mainPath.StartsWith(ConstVar.ModelResPath + "/LittleLegend"))
        {
            if (mainPath.EndsWith("_1.prefab") || mainPath.EndsWith("_1_h.prefab") || mainPath.EndsWith("_3.prefab") || mainPath.EndsWith("_3_h.prefab"))
                CheckTinyOrginModel(transform);
        }
    }

    private void CheckUseUnityDefaultResource()
    {
        foreach (var lodStat in lodStats)
        {
            int lodLevel = lodStat.Key;
            foreach (var mesh in staticData.meshs)
            {
                if (mesh.Key == "Library/unity default resources"  && (mesh.Value.lod & lodLevel) > 0)
                {
                    var nodes = mesh.Value.nodes.ToList();
                    errors.Add(new ArtExportError() { subNodePath = nodes.Count > 0 ? nodes[0] : string.Empty, errorInfo = "lod: " + lodLevel + " 引用了unity内置模型！" });
                }
            }
        }


    }

    /// <summary>
    /// 检查模型渲染器中贴图尺寸与模型体积的比例是否合理
    /// </summary>
    /// <param name="transform">要检查的根变换</param>
    /// <remarks>
    /// 此函数检查所有MeshRenderer组件，判断其使用的贴图尺寸是否与模型体积匹配。
    /// 检查流程：
    /// 1. 获取每个MeshRenderer引用的所有材质中最大的贴图
    /// 2. 如果贴图面积大于128x128，计算模型的AABB包围盒体积
    /// 3. 确保包围盒每个轴的尺寸至少为1，避免体积过小导致比例异常
    /// 4. 计算贴图面积与模型体积的比例
    /// 5. 如果比例超过MeshTextureToVolumeRatioThreshold阈值，添加错误警告
    /// 
    /// 此检查有助于发现贴图资源浪费，优化内存占用和渲染性能
    /// </remarks>
    public void CheckMeshRenderError(Transform transform)
    {
        var meshRenderers = transform.GetComponentsInChildren<MeshRenderer>(true);
        foreach (var renderer in meshRenderers)
        {
            // 获取MeshFilter
            var meshFilter = renderer.GetComponent<MeshFilter>();
            if (meshFilter == null || meshFilter.sharedMesh == null)
                continue;
                
            // 检查材质
            var materials = renderer.sharedMaterials;
            if (materials == null || materials.Length == 0)
                continue;
                
            // 查找最大贴图
            Texture largestTexture = null;
            int largestTextureArea = 0;
            
            foreach (var material in materials)
            {
                if (material == null)
                    continue;
                    
                var mainTexture = material.mainTexture;
                if (mainTexture != null)
                {
                    int textureArea = mainTexture.width * mainTexture.height;
                    if (textureArea > largestTextureArea)
                    {
                        largestTextureArea = textureArea;
                        largestTexture = mainTexture;
                    }
                }
            }
            
            // 如果找到贴图，并且贴图足够大，继续检测
            if (largestTexture != null && largestTextureArea > 128 * 128)
            {
                // 计算模型AABB体积
                var mesh = meshFilter.sharedMesh;
                var bounds = mesh.bounds;
                
                // 确保AABB至少为1x1x1
                float sizeX = Mathf.Max(1.0f, bounds.size.x);
                float sizeY = Mathf.Max(1.0f, bounds.size.y);
                float sizeZ = Mathf.Max(1.0f, bounds.size.z);
                
                float volume = sizeX * sizeY * sizeZ;
                
                // 计算贴图尺寸与体积的比例
                float ratio = largestTextureArea / volume;
                
                if (ratio > MeshTextureToVolumeRatioThreshold)
                {
                    errors.Add(new ArtExportError() { 
                        subNodePath = GfxProfilerStaticData.GetNodePath(renderer.transform, transform), 
                        errorInfo = $"{renderer.name} 该模型使用的贴图过大，请缩小贴图尺寸。贴图尺寸：{largestTexture.width}x{largestTexture.height}，模型体积：{volume}" 
                    });
                }
            }
        }
    }

    /// <summary>
    /// 检查粒子系统是否应该使用 Gfx_Framework 替代
    /// </summary>
    /// <param name="transform">要检查的根变换</param>
    /// <remarks>
    /// 此函数检查所有粒子系统组件，判断是否有以下情况：
    /// - Max Particles 设置为 1
    /// - 未启用 Shape 模块
    /// 
    /// 这种情况下通常表示粒子系统被用作简单的效果展示，应该改用 Gfx_Framework 制作，
    /// 以提高性能和可维护性。使用 Gfx_Framework 可以更好地控制资源和优化渲染。
    /// </remarks>
    public void CheckGfxFrameworkError(Transform transform)
    {
        var particleSystems = transform.GetComponentsInChildren<ParticleSystem>(true);
        foreach (var ps in particleSystems)
        {
            // 检查 Max Particles 是否为 1 且 Shape 模块未启用
            if (ps.main.maxParticles == 1 && !ps.shape.enabled)
            {
                errors.Add(new ArtExportError() { 
                    subNodePath = GfxProfilerStaticData.GetNodePath(ps.transform, transform), 
                    errorInfo = $"{ps.name} 节点请改用Gfx_Framework制作，不要用粒子发射器。" 
                });
            }
        }
    }
}

