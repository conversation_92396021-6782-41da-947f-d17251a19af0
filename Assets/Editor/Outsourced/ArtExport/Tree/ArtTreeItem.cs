using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEditor.Experimental;
using UnityEngine;

public interface IArtTreeItem
{
    float GetHeight();
    float OnGUI(int depth, Rect r);
}

public class ArtTreeItem : IArtTreeItem
{
    public string path;
    public string name;
    public ArtExportTool exportTool;
    public ArtTreeView treeView;

    public List<ArtTreeItem> children = new List<ArtTreeItem>();

    private bool expandedState = true;
    private bool selected = false;

    //缓存资源内存占用数据
    public long cacheAssetMemory;

    private int warnRedValue = 0;

    public ArtTreeItem(string path, string name, ArtExportTool exportTool, ArtTreeView treeView)
    {
        this.path = path;
        this.name = name;
        this.exportTool = exportTool;
        this.treeView = treeView;
        warnRedValue = EditorPrefs.GetInt(ArtExportSettingPanel.ASSETTOOL_WARN_MEM_VALUE);
    }

    public override string ToString()
    {
        return path;
    }

    public void UnSelect()
    {
        selected = false;
    }

    public void Select()
    {
        selected = true;
    }

    public float GetHeight()
    {
        float height = exportTool.ExpandAssetHeight(path);
        if (expandedState)
        {
            for (int i = 0; i < children.Count; ++i)
            {
                height += children[i].GetHeight();
            }
        }
        return height;
    }

    public float OnGUI(int depth, Rect r)
    {
        float height = 0;
        var rItem = r;
        cacheAssetMemory = 0;
        rItem.height = ArtGlobalDefine.SingLineHeight;
        var s = ArtGlobalDefine.GetStyle();
        if (Event.current.type == EventType.Repaint)
        {
            s.Draw(rItem, false, false, selected, false);
        }
        warnRedValue = EditorPrefs.GetInt(ArtExportSettingPanel.ASSETTOOL_WARN_MEM_VALUE);
        if (Event.current.clickCount >= 1 && rItem.Contains(Event.current.mousePosition))
        {
            exportTool.OnClick(path, string.Empty, Event.current.clickCount != 1, Event.current.clickCount != 1);
            treeView.Select(this);
            // Event.current.Use();
        }

        var foldoutRect = rItem;
        foldoutRect.x = ArtGlobalDefine.GetIndent(depth);
        foldoutRect.width = ArtGlobalDefine.FoldoutWidth;
        if (children.Count > 0)
        {
            expandedState = GUI.Toggle(foldoutRect, expandedState, GUIContent.none, ArtGlobalDefine.foldout);
        }

        Rect iconRect = foldoutRect;
        iconRect.x += foldoutRect.width;
        iconRect.width = ArtGlobalDefine.SingLineHeight;

        GUI.DrawTexture(iconRect, ArtGlobalDefine.GetIcon(path));

        Rect nameRect = iconRect;
        nameRect.x += iconRect.width;
        nameRect.width = r.width - nameRect.x;

        GUI.Label(nameRect, name);
        foreach (var mainAsset in exportTool.SelectedAssets)
        {
            if (mainAsset.dependencyPaths.Contains(path))
            {
                if (exportTool.PrefabInfos.TryGetValue(mainAsset.mainPath, out ArtExportPrefabAsset prefabInfo) && prefabInfo.staticData != null)
                {
                    string assetPath = path;
                    long assetMemory = 0;
                    string formatInfo = "";
                    bool isUseACL = false;
                    bool showACL = false;
                    
                    UnityEngine.Object assetObject = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(assetPath);
                    
                    if (assetObject != null)
                    {
                        if (assetPath.EndsWith(".FBX"))
                        {
                            // 查找mesh资源内存
                            string key = name.Replace(".FBX","");
                            foreach (var meshAsset in prefabInfo.staticData.meshs)
                            {
                                if (meshAsset.Key.IndexOf(key) >= 0)
                                {
                                    assetMemory += meshAsset.Value.size;
                                }
                            }
                        }
                        else if (assetObject is Material material)
                        {
                            // 查找材质资源内存
                            if (prefabInfo.staticData.materials.TryGetValue(assetPath, out var materialAsset))
                            {
                                assetMemory = materialAsset.size;
                                formatInfo = "Shader: " + materialAsset.shaderName;
                            }
                        }
                        else if (assetObject is Texture texture)
                        {
                            // 查找纹理资源内存
                            if (prefabInfo.staticData.textures.TryGetValue(assetPath, out var textureAsset))
                            {
                                assetMemory = textureAsset.size;
                                formatInfo = "压缩格式：" + textureAsset.format.ToString();
                            }
                        }
                        else if (assetObject is Sprite sprite)
                        {
                            // 查找精灵纹理资源内存
                            if (sprite.texture != null)
                            {
                                string texturePath = AssetDatabase.GetAssetPath(sprite.texture);
                                if (prefabInfo.staticData.textures.TryGetValue(texturePath, out var textureAsset))
                                {
                                    assetMemory = textureAsset.size;
                                    formatInfo = "压缩格式：" + textureAsset.format.ToString();
                                }
                            }
                        }
                        else if (assetObject is AnimationClip clip)
                        {
                            // 查找动画资源内存
                            if (prefabInfo.staticData.clips.TryGetValue(assetPath, out var clipAsset))
                            {
                                assetMemory = clipAsset.size;
                                
                                // 检查clip是否使用ACL压缩
                                SerializedObject clipObject = new SerializedObject(clip);
                                isUseACL = !clipObject.FindProperty("m_UseHighQualityCurve").boolValue;
                                formatInfo = "ACL压缩：" + (isUseACL ? "是" : "否");
                                showACL = true;
                            }
                        }
                        else if (assetObject is RuntimeAnimatorController animatorController)
                        {
                            // 查找动画控制器资源内存
                            foreach (var animClip in animatorController.animationClips)
                            {
                                string clipPath = AssetDatabase.GetAssetPath(animClip);
                                if (prefabInfo.staticData.clips.TryGetValue(clipPath, out var clipAsset))
                                {
                                    assetMemory += clipAsset.size;
                                }
                            }
                        }
                        else if (assetObject is UnityEngine.Playables.PlayableAsset playableAsset)
                        {
                            // 查找可播放资源内存
                            var dependencies = AssetDatabase.GetDependencies(assetPath);
                            foreach (var dependency in dependencies)
                            {
                                var dependencyClip = AssetDatabase.LoadAssetAtPath<AnimationClip>(dependency);
                                if (dependencyClip != null)
                                {
                                    if (prefabInfo.staticData.clips.TryGetValue(dependency, out var clipAsset))
                                    {
                                        assetMemory += clipAsset.size;
                                    }
                                }
                            }
                        }
                    }
                    
                    // 显示资源内存信息
                    if (assetMemory > 0)
                    {
                        string memoryInfo = EditorUtility.FormatBytes(assetMemory);
                        
                        GUIStyle style = GUI.skin.label;
                        float memoryWidth = style.CalcSize(new GUIContent(memoryInfo)).x;
                        
                        // 创建用于显示内存信息的矩形（右对齐）
                        Rect memoryRect = nameRect;
                        memoryRect.x = nameRect.x + nameRect.width - memoryWidth - 10; // 10是右边距
                        memoryRect.width = memoryWidth;
                        
                        // 如果有格式信息，显示格式信息
                        if (!string.IsNullOrEmpty(formatInfo))
                        {
                            float formatWidth = style.CalcSize(new GUIContent(formatInfo)).x;
                            
                            // 创建用于显示格式信息的矩形
                            Rect formatRect = nameRect;
                            formatRect.x = nameRect.x + nameRect.width * 0.6f;
                            formatRect.width = formatWidth;
                            
                            // 确保格式信息不会与内存信息重叠
                            if (formatRect.x + formatWidth > memoryRect.x - 20)
                            {
                                formatRect.x = memoryRect.x - formatWidth - 20; // 与内存信息保持20像素距离
                            }
                            
                            // 如果是ACL压缩信息，使用颜色标识
                            if (showACL)
                            {
                                GUIStyle aclStyle = new GUIStyle(GUI.skin.label);
                                aclStyle.normal.textColor = isUseACL ? Color.green : Color.red;
                                GUI.Label(formatRect, formatInfo, aclStyle);
                            }
                            else
                            {
                                GUI.Label(formatRect, formatInfo);
                            }
                        }

                        if (assetMemory > warnRedValue)
                        {
                            GUIStyle aclStyle = new GUIStyle(GUI.skin.label);
                            aclStyle.normal.textColor = isUseACL ? Color.green : Color.red;
                            // 显示内存信息
                            GUI.Label(memoryRect, memoryInfo,aclStyle);
                        }
                        else
                        {
                            // 显示内存信息
                            GUI.Label(memoryRect, memoryInfo);
                        }
                        
                    }

                    cacheAssetMemory = assetMemory;
                    break;
                }
            }
        }
        

        
        height += rItem.height;

        var svnInfo = exportTool.GetSvnInfo(path);
        if (!string.IsNullOrEmpty(svnInfo))
        {
            var svnInfoRect = r;
            svnInfoRect.x += nameRect.x;
            svnInfoRect.y += height;
            EditorGUI.LabelField(svnInfoRect, svnInfo);
            height += svnInfoRect.height;
        }

        if (name.ToLower().EndsWith(ArtExportAsset.DDS_IMAGE))
        {
            rItem = r;
            rItem.y += height;
            rItem.height = ArtGlobalDefine.GetLineHeight(1);

            s = ArtGlobalDefine.GetStyle();
            if (Event.current.type == EventType.Repaint)
            {
                s.Draw(rItem, false, false, selected, false);
            }

            rItem.x += nameRect.x;

            var rIcon = rItem;
            rIcon.width = 20;

            EditorGUI.LabelField(rIcon, EditorGUIUtility.TrTextContentWithIcon("", "", MessageType.Error));

            rItem.x += 20;
            EditorGUI.LabelField(rItem, "发现DDS图，请删除！");
            height += rItem.height;
        }

        var prefabRect = r;
        prefabRect.y += height;
        height += DrawPrefabInfo(prefabRect, path, nameRect.x);

        if (cacheAssetMemory > 0 && treeView != null)
        {
            treeView.AddSortLeafChild(this);
        }

        if (treeView != null && treeView.isSortDisplayMode)
        {
            var nextRect = rItem;
            nextRect.y += height;
            treeView.DrawNext(0,nextRect);
        }
        else
        {
            if (expandedState && children.Count > 0)
            {
                var nextRect = rItem;
                nextRect.y += height;
                int nextDepth = depth + 1;
                for (int i = 0; i < children.Count; ++i)
                {
                    var childHeight = children[i].OnGUI(nextDepth, nextRect);
                    height += childHeight;
                    nextRect.y += childHeight;
                }
            }
        }
 

        return height;
    }

    private float DrawPrefabInfo(Rect r, string path, float indent)
    {
        float height = 0;
        if (exportTool.PrefabInfos.TryGetValue(path, out ArtExportPrefabAsset prefabInfo))
        {
            int lineCount = ArtGlobalDefine.GetLineCount(prefabInfo.totalInfo);

            Rect rItem = r;
            rItem.height = ArtGlobalDefine.GetLineHeight(lineCount);

            var s = ArtGlobalDefine.GetStyle();
            if (Event.current.type == EventType.Repaint)
            {
                s.Draw(rItem, false, false, selected, false);
            }

            if (Event.current.clickCount >= 1 && rItem.Contains(Event.current.mousePosition))
            {
                exportTool.OnClick(path, string.Empty, Event.current.clickCount != 1, Event.current.clickCount != 1);
                treeView.Select(this);
            }

            rItem.x += indent;
            EditorGUI.LabelField(rItem, prefabInfo.totalInfo);

            height = rItem.height;
            
            for (int i = 0; i < prefabInfo.warnings.Count; ++i)
            {
                var warning = prefabInfo.warnings[i];
                lineCount = ArtGlobalDefine.GetLineCount(warning.errorInfo);

                rItem = r;
                rItem.y += height;
                rItem.height = ArtGlobalDefine.GetLineHeight(lineCount);

                s = ArtGlobalDefine.GetStyle();
                if (Event.current.type == EventType.Repaint)
                {
                    s.Draw(rItem, false, false, selected, false);
                }

                rItem.x += indent;

                var rIcon = rItem;
                rIcon.width = 20;

                EditorGUI.LabelField(rIcon, EditorGUIUtility.TrTextContentWithIcon("", "", MessageType.Warning));

                rItem.x += 20;
                EditorGUI.LabelField(rItem, warning.errorInfo);

                if (Event.current.clickCount >= 1 && rItem.Contains(Event.current.mousePosition))
                {
                    exportTool.OnClick(path, warning.subNodePath, true, Event.current.clickCount != 1);
                }

                height += rItem.height;
            }

            for (int i = 0; i < prefabInfo.errors.Count; ++i)
            {
                var error = prefabInfo.errors[i];
                lineCount = ArtGlobalDefine.GetLineCount(error.errorInfo);

                rItem = r;
                rItem.y += height;
                rItem.height = ArtGlobalDefine.GetLineHeight(lineCount);

                s = ArtGlobalDefine.GetStyle();
                if (Event.current.type == EventType.Repaint)
                {
                    s.Draw(rItem, false, false, selected, false);
                }

                rItem.x += indent;

                var rIcon = rItem;
                rIcon.width = 20;

                EditorGUI.LabelField(rIcon, EditorGUIUtility.TrTextContentWithIcon("", "", MessageType.Error));

                rItem.x += 20;
                EditorGUI.LabelField(rItem, error.errorInfo);

                if (Event.current.clickCount >= 1 && rItem.Contains(Event.current.mousePosition))
                {
                    exportTool.OnClick(path, error.subNodePath, true, Event.current.clickCount != 1);
                }

                height += rItem.height;
            }
        }
        return height;
    }
}

