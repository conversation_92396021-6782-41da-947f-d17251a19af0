using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;


namespace NXEditorGUITable
{

	/// <summary>
	/// This class represents a column that will draw Property Cells from the given property name, 
	/// relative to the collection element's serialized property.
	/// </summary>
	public class SelectFromPropertyNameColumn : SelectorColumn
	{
		public string propertyName;
		public SelectFromPropertyNameColumn (string propertyName, string title, params TableColumnOption[] options) : base (title, options)
		{
			this.propertyName = propertyName;
		}

		public override TableCell GetCell (TableItemData data)
		{
            var val = data.GetCell(propertyName);
            if (val == null)
                return new LabelCell("null");
            else
                return new LabelCell(val.ToString());
		}
	
	}

}