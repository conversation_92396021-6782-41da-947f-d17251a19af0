using System;
using System.Collections.Generic;
using System.IO;
using GameFramework.FMath;
using SDFCollider;
using TKFrame;
using UnityEditor;
using UnityEngine;

namespace SDFTool
{
    public class SDFColliderTool
    {
        public static string GenColliderName(string name)
        {
            return  name.ToLower() + "_collider";
        }
        
        public static SDFColliderConfig Create(string colliderName, float realMapSize, ColliderBaseClass collider)
        {
            SDFColliderConfig colliderConfig = new SDFColliderConfig();
            colliderConfig.colliderName = colliderName;
            colliderConfig.m_points = new Fix64[SDFPathCore.MAP_SIZE, SDFPathCore.MAP_SIZE];

            collider.Calculate();
            HashSet<int> colliderData = collider.GetHashSet();
            Gen(ref colliderConfig.m_points, colliderData, realMapSize);

            Save(colliderConfig, collider);
            ToMapPNG(colliderConfig);

            return colliderConfig;
        }

        private static void Gen(ref Fix64[,] points, HashSet<int> collider, float realMapSize)
        {
            GenColliderOneZeroMap(ref points, collider);
            SDFCoreTool.EudlideanDistanceTransfromMap(ref points, realMapSize);
        }
        
        private static void GenColliderOneZeroMap(ref Fix64[,] points, HashSet<int> colliderSet)
        {
            for (int x = 0; x < SDFPathCore.MAP_SIZE; ++x)
            {
                for (int y = 0; y < SDFPathCore.MAP_SIZE; ++y)
                {
                    int hashCode = JPSPath.HashKey(x, y);
                    if (colliderSet.Contains(hashCode))
                    {
                        points[x, y] = Fix64.zero;
                    }
                    else
                    {
                        points[x, y] = Fix64.one;
                    }
                }
            }
        }
        
        private static void ToMapPNG(SDFColliderConfig sdfColliderConfig)
        {
            Texture2D texture = new Texture2D(SDFPathCore.MAP_SIZE, SDFPathCore.MAP_SIZE, TextureFormat.ARGB32, false);

            Fix64 maxR = Fix64.zero;
            Fix64 minR = Fix64.maxValue;

            for (int x = 0; x < SDFPathCore.MAP_SIZE; ++x)
            {
                for (int y = 0; y < SDFPathCore.MAP_SIZE; ++y)
                {
                    var r = sdfColliderConfig.m_points[x, y];
                    maxR = Fix64.Max(maxR, r);
                    minR = Fix64.Min(minR, r);
                }
            }
            for (int x = 0; x < SDFPathCore.MAP_SIZE; ++x)
            {
                for (int y = 0; y < SDFPathCore.MAP_SIZE; ++y)
                {
                    var r = sdfColliderConfig.m_points[x, y];
                    
                    if (r >= Fix64.zero)
                    {
                        float rf = (r / maxR).ToSingle();
                        texture.SetPixel(x, y, new Color(0, rf, 0, 0.2f));
                    }
                    else
                    {
                        float rf = (r / minR).ToSingle();
                        texture.SetPixel(x, y, new Color(rf, 0, 0, 0.2f));
                    }
                }
            }
            

            var path = SDFCoreTool.GetPathName(sdfColliderConfig.colliderName).Replace(".bytes", ".png");
            if(File.Exists(path))
                File.Delete(path);
            File.WriteAllBytes(path, texture.EncodeToPNG());

            AssetDatabase.Refresh();

            var importer = TextureImporter.GetAtPath(path) as TextureImporter;
            if (importer != null)
            {
                importer.textureType = TextureImporterType.Sprite;
                importer.alphaIsTransparency = true;
                importer.mipmapEnabled = false;
                importer.SaveAndReimport();
            }
            
        }

        private static void Save(SDFColliderConfig sdfColliderConfig, ColliderBaseClass colliderConfig)
        {
            if (!string.IsNullOrEmpty(sdfColliderConfig.colliderName))
            {
                using (MemoryStream ms = new MemoryStream())
                {
                    using (BinaryWriter writer = new BinaryWriter(ms))
                    {
                        writer.Write(sdfColliderConfig.colliderName);
                        colliderConfig.WriteConfig(writer);
                        writer.Write(SDFPathCore.MAP_SIZE);
                        
                        for (int x = 0; x < SDFPathCore.MAP_SIZE; ++x)
                        {
                            for (int y = 0; y < SDFPathCore.MAP_SIZE; ++y)
                            {
                                var point = sdfColliderConfig.m_points[x, y];
                                writer.Write(point.rawValue);
                            }
                        }
                    }
                    
                    byte[] data = ms.ToArray();
                    //MemoryStream outFile = GZipTools.Compress(data);// 压缩源字符串

                    string path = SDFCoreTool.GetPathName(sdfColliderConfig.colliderName);
                    if(File.Exists(path))
                        File.Delete(path);
                    File.WriteAllBytes(path, data);
                }
            }
        }
        
        public static SDFColliderConfig LoadCollider(string colliderName)
        {
            string prefix = Environment.CurrentDirectory;
            string path = prefix + "/Assets/" + SDFPathCore.DirPath + "/" + colliderName + ".bytes";
            if (File.Exists(path))
            {
                SDFColliderConfig colliderConfig = new SDFColliderConfig();
                using (Stream stream = FileManager.Instance.GetReadStream(path))
                {
                    colliderConfig.Decode(stream);
                }
                return colliderConfig;
            }

            return null;
        }

        public static void WriteColliderConfig(string fileName, ColliderWrapper colliderWrapper)
        {
            if (File.Exists(fileName))
                File.Delete(fileName);
            
            using (FileStream fs = new FileStream(fileName, FileMode.Create))
            {
                using (BinaryWriter bw = new BinaryWriter(fs))
                {
                    colliderWrapper.Write(bw);
                }
            }
        }

        public static ColliderWrapper ReadColliderConfig(string fileName)
        {
            if (File.Exists(fileName))
            {
                using (FileStream fs = new FileStream(fileName, FileMode.Open))
                {
                    using (BinaryReader br = new BinaryReader(fs))
                    {
                        ColliderWrapper colliderWrapper = new ColliderWrapper();
                        colliderWrapper.Read(br);

                        return colliderWrapper;
                    }
                }
            }

            return null;
        }
    }
}
