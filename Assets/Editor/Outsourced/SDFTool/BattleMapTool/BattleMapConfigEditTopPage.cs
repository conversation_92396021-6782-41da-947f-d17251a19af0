using System;
using System.Collections.Generic;
using System.Reflection;
using Newtonsoft.Json;
using UnityEditor;
using UnityEditor.Animations;
using UnityEngine;
using ZGameChess;

/// <summary>
/// 棋盘配置编辑器——顶部页面
/// </summary>
public class BattleMapConfigEditTopPage : BattleMapConfigEditBasePage
{
    public BattleMapConfigEditTopPage(BattleMapConfigEditor parentEditor,
     BattleMapConfig target,
     string title,
     bool initOpen,
     bool isTopBar)
    : base(parentEditor, target, title, initOpen, isTopBar) { }

    private List<string> m_errorMsg;
    private string m_errorText;

    public override void OnShow()
    {
        base.OnShow();

        GUILayout.BeginVertical(m_boxStyle, GUILayout.ExpandWidth(true));

        GUILayout.BeginHorizontal(m_boxStyle, GUILayout.ExpandWidth(true), GUILayout.Height(50));
        
        GUILayout.BeginVertical(m_boxStyle, GUILayout.Width(40), GUILayout.Height(40));
        BeginColor(Color.cyan);
        if (GUILayout.Button("wiki", GUILayout.Height(40), GUILayout.Width(40)))
        {
            Application.OpenURL("https://iwiki.woa.com/pages/viewpage.action?pageId=1692823828");
        }
        EndColor();
        GUILayout.EndVertical();

        GUILayout.BeginVertical(m_boxStyle, GUILayout.Width(500), GUILayout.ExpandHeight(true));
        BeginColor(Color.green);
        GUI.enabled = !EditorApplication.isPlaying;
        if (GUILayout.Button("保存到文件", GUILayout.Height(40), GUILayout.Width(200)))
        {
            EditorUtility.SetDirty(m_target);
            ShowNotification("保存成功");
        }
        GUI.enabled = true;
        EndColor();
        GUILayout.EndVertical();

        GUILayout.BeginVertical(m_boxStyle, GUILayout.ExpandWidth(true), GUILayout.ExpandHeight(true));
        BeginColor(Color.yellow);
        if (GUILayout.Button("检查配置", GUILayout.Height(40), GUILayout.Width(200)))
        {
            string result = "检查成功 ： 无异常";
            m_errorText = "";
            m_errorMsg = CheckCfg();
            if (m_errorMsg.Count > 0)
            {
                result = "有异常 !!! 详见报错面板";
                for (int i = 0; i < m_errorMsg.Count; i++)
                {
                    m_errorText += "错误 " + (i + 1) + ":\t" + m_errorMsg[i];
                    if (i < m_errorMsg.Count - 1)
                    {
                        m_errorText += "\n";
                    }
                }
            }
            
            EditorUtility.DisplayDialog("提示", result, "确定");
        }
        EndColor();
        GUILayout.EndVertical();

        GUILayout.EndHorizontal();

        if (!string.IsNullOrEmpty(m_errorText))
        {
            GUILayout.BeginHorizontal(m_boxStyle, GUILayout.ExpandWidth(true));
            BeginColor(Color.red);
            {
                GUILayout.TextField(m_errorText);
            }
            EndColor();
            GUILayout.EndHorizontal();
        }

        GUILayout.EndVertical();
    }

    private List<string> CheckCfg()
    {
        List<string> result = new List<string>();
        //事件触发动画重复
        var eventAnimCfgs = m_target.m_eventAnimationList;
        for (int i = 0; i < eventAnimCfgs.Count; i++)
        {
            for (int j = i + 1; j < eventAnimCfgs.Count; j++)
            {
                if (EditorUtil.IfEquals(eventAnimCfgs[i], eventAnimCfgs[j]))
                {
                    result.Add(string.Format("事件触发动画重复 ： 事件 {0} 和 事件 {1}", i, j));
                }
            }
        }

        //动画错误排查
        List<string> errorList = CheckAnimInfo();
        if (errorList.Count > 0)
        {
            result.AddRange(CheckAnimInfo());
        }

        //AnimationEvent检查
        string animationEventResult = CheckAnimationEvent();
        if (!string.IsNullOrEmpty(animationEventResult))
        {
            result.Add(CheckAnimationEvent());
        }

        return result;
    }

    private List<string> CheckAnimInfo()
    {
        List<string> result = new List<string>();

        try
        {
            //区域触发动画
            var triggerAnimCfgs = m_target.m_triggerAnimationList;
            for (int i = 0; i < triggerAnimCfgs.Count; i++)
            {
                var cfg = triggerAnimCfgs[i];
                if (cfg != null && cfg.m_animator != null && cfg.m_enterAnimationName.Filled())
                {
                    string checkResult = CheckAnimInfo(cfg, cfg.m_enterAnimationName);
                    if (!string.IsNullOrEmpty(checkResult))
                    {
                        result.Add("区域触发动画 索引：" + i + checkResult);
                    }
                }
            }

            //事件触发动画
            var eventAnimCfgs = m_target.m_eventAnimationList;
            for (int i = 0; i < eventAnimCfgs.Count; i++)
            {
                var cfg = eventAnimCfgs[i];
                if (cfg != null && cfg.m_animator != null && cfg.m_enterAnimationName.Filled())
                {
                    string animName = BattleMapTriggerConfigInspector.GetEventTriggerDesc(cfg, true);
                    string checkResult = CheckAnimInfo(cfg, animName);
                    if (!string.IsNullOrEmpty(checkResult))
                    {
                        result.Add("事件触发动画 索引：" + i + checkResult);
                    }
                }
            }

            //逻辑触发动画
            var logicAnimCfgs = m_target.m_eventLogicList;
            for (int i = 0; i < logicAnimCfgs.Count; i++)
            {
                var logicAnimCfg = logicAnimCfgs[i];
                if (logicAnimCfg != null && logicAnimCfg.triggerAnimCfgList != null)
                {
                    foreach (var cfg in logicAnimCfg.triggerAnimCfgList)
                    {
                        if (cfg != null && cfg.m_animator != null && cfg.m_enterAnimationName.Filled())
                        {
                            string checkResult = CheckAnimInfo(cfg, cfg.m_enterAnimationName);
                            if (!string.IsNullOrEmpty(checkResult))
                            {
                                result.Add("逻辑触发动画 索引：" + i + checkResult);
                            }
                        }
                    }
                }
            }
        }
        catch (Exception e)
        {
            result.Add("程序错误！" + e.ToString());
        }

        return result;
    }

    private string CheckAnimInfo(BattleMapTriggerAminationConfig cfg, string enterAnimationName)
    {
        AnimatorController animatorController = cfg.m_animator.runtimeAnimatorController as AnimatorController;
        AnimatorStateMachine stateMachine = TeamLeaderAnimatorUtil.GetStateMachine(animatorController, 0);
        ChildAnimatorState[] states = stateMachine.states;
        int enterAnimHash = Animator.StringToHash(enterAnimationName);
        AnimatorState enterAnimState = null;
        foreach (var state in states)
        {
            if (state.state.nameHash == enterAnimHash)
            {
                enterAnimState = state.state;
                break;
            }
        }
        
        if (enterAnimState == null)
        {
            return " 找不到动画 !! animator: " + cfg.m_animator.name + "  animName: " + enterAnimationName;
        }
        else
        {
            return GenerateFinalAnimInfo(cfg, enterAnimationName, enterAnimState);
        }
    }

    /// <summary>
    /// 生成动画终点数据
    /// </summary>
    private string GenerateFinalAnimInfo(BattleMapTriggerAminationConfig cfg,
        string enterAnimationName,
        AnimatorState enterAnimState)
    {
        string result = "";
        AnimatorState scanState = enterAnimState;

        int safeLoop = 100;
        while (scanState != null)
        {
            safeLoop--;

            if (safeLoop < 0)
            {
                scanState = enterAnimState;
                result += "循环动画 !! animator: " + cfg.m_animator.name + "  animName: " + enterAnimationName;
                break;
            }

            if (scanState.transitions.Length != 1)
            {
                break;
            }

            AnimatorStateTransition transition = scanState.transitions[0];
            if (transition.conditions != null && transition.conditions.Length > 0)
            {
                //有锁条件，那么就不继续了
                break;
            }

            scanState = transition.destinationState;
        }

        if (scanState == null)
        {
            result += " 生成动画终点数据错误 !! animator: " + cfg.m_animator.name + "  animName: " + enterAnimationName;
        }
        else
        {
            cfg.m_finalAnimName = scanState.name;
        }

        return result;
    }

    private string CheckAnimationEvent()
    {
        string result = "";

        //检查是否有AnimationEvent
        if (m_target != null)
        {
            var animators = m_target.gameObject.GetComponentsInChildren<Animator>(true);

            foreach (var animator in animators)
            {
                var clips = AnimationUtility.GetAnimationClips(animator.gameObject);
                foreach (var clip in clips)
                {
                    var events = AnimationUtility.GetAnimationEvents(clip);
                    if (events != null && events.Length > 0)
                    {
                        result += " 动画有 " + events.Length + " 个Event !! 请确认是否需要！！ animator: " + animator.name + "  animName: " + clip.name;
                    }
                }
            }
        }

        return result;
    }
}