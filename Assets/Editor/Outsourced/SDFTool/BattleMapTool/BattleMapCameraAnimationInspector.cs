using GfxFramework;
using Lucifer.ActCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEditor.Animations;
using UnityEngine;

namespace ZGameChess
{
    [CustomEditor(typeof(BattleMapCameraAnimation))]
    public class BattleMapCameraAnimationInspector : Editor
    {
        public class SetInfo
        {
            public string title;
            public bool foldout = false;

            public List<string> boolPropertys = new List<string>();
            public List<string> intPropertys = new List<string>();
            public List<string> triggerPropertys = new List<string>();

            public SetInfo(string title, string[] bools, string[] ints, string[] triggers)
            {
                this.title = title;
                if (bools != null)
                    boolPropertys.AddRange(bools);
                if (ints != null)
                    intPropertys.AddRange(ints);
                if (triggers != null)
                    triggerPropertys.AddRange(triggers);
            }
        }

        private List<SetInfo> artCamSetList = new List<SetInfo>()
        {
            new SetInfo("回合设置", null, new string[] { ArtCameraBattleMap.TURN_COUNT }, new string[] { ArtCameraBattleMap.TURN_START_TRIGGER }),
            new SetInfo("胜利设置", new string[] { ArtCameraBattleMap.IS_HOME_FIELD }, null, new string[] { ArtCameraBattleMap.USER_OUT_TRIGGER }),
            new SetInfo("轮抽设置", null, new string[] { ArtCameraBattleMap.ROUND_SELECT_POS }, new string[] { ArtCameraBattleMap.ROUND_SELECT_START_TRIGGER }),
        };

        private void ShowV1Controller(BattleMapCameraAnimation bmca)
        {
            if (bmca.AnimationList.Count > 0 && Application.isPlaying)
            {

                GfxEditorUtility.ContentsHeader("V1版本镜头控制器");
                GfxEditorUtility.BeginContents();
                GfxEditorUtility.BeginGroup();

                if (!bmca.CameraAnimationInited)
                {
                    var animationObj = GameObject.Find("chess_scene/game_dynamic_battle/BattleRoot/CamAnimation");
                    if (animationObj != null)
                    {
                        var animation = animationObj.GetComponent<Animation>();
                        if (animation != null)
                            bmca.InitCameraAnimation(animation);
                    }
                }

                var camAnim = EditorGUILayout.ObjectField("相机动画组件", bmca.CameraAnimation, typeof(Animation), true) as Animation;
                if (camAnim != null)
                {
                    if (!bmca.CameraAnimationInited || camAnim != bmca.CameraAnimation)
                    {
                        bmca.InitCameraAnimation(camAnim);
                    }
                }

                if (GUILayout.Button("开局动画"))
                {
                    ACG.Core.Core.Setup();
                    bmca.OnTurnStart(1);
                }
                if (GUILayout.Button("主场胜利"))
                {
                    ACG.Core.Core.Setup();
                    bmca.OnFirstRankFoucs(true, true);
                }
                if (GUILayout.Button("客场胜利"))
                {
                    ACG.Core.Core.Setup();
                    bmca.OnFirstRankFoucs(false, true);
                }

                GfxEditorUtility.EndGroup();
                GfxEditorUtility.EndContents();
            }
        }

        private void ShowV2Controller(BattleMapCameraAnimation bmca)
        {
            if (bmca.ArtCameraEnable && Application.isPlaying)
            {
                GfxEditorUtility.ContentsHeader("V2版本镜头控制器");
                GfxEditorUtility.BeginContents();
                GfxEditorUtility.BeginGroup();

                var artCam = bmca.ArtCamera;
                // 回合设置
                for (int i = 0; i < artCamSetList.Count; ++i)
                {
                    var s = artCamSetList[i];

                    s.foldout = GfxEditorUtility.ContentsHeader(s.title, s.foldout);
                    if (s.foldout)
                    {
                        GfxEditorUtility.BeginContents();
                        GfxEditorUtility.BeginGroup();

                        for (int j = 0; j < s.boolPropertys.Count; ++j)
                        {
                            var p = s.boolPropertys[j];
                            bool oldValue = artCam.GetBoolParam(p);
                            bool newValue = EditorGUILayout.Toggle(p, oldValue);
                            if (newValue != oldValue)
                            {
                                artCam.SetBoolParam(p, newValue);
                            }
                        }

                        for (int j = 0; j < s.intPropertys.Count; ++j)
                        {
                            var p = s.intPropertys[j];
                            int oldValue = artCam.GetIntParam(p);
                            int newValue = EditorGUILayout.IntField(p, oldValue);
                            if (newValue != oldValue)
                            {
                                artCam.SetIntParam(p, newValue);
                            }
                        }

                        for (int j = 0; j < s.triggerPropertys.Count; ++j)
                        {
                            var p = s.triggerPropertys[j];
                            if (GUILayout.Button(p))
                            {
                                ACG.Core.Core.Setup();
                                artCam.SetTrigger(p);
                            }
                        }
                        GfxEditorUtility.EndGroup();
                        GfxEditorUtility.EndContents();
                    }
                }

                GfxEditorUtility.EndGroup();
                GfxEditorUtility.EndContents();

                ShowMayaPos();
            }
        }

        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();

            var bmca = target as BattleMapCameraAnimation;

            if (bmca.ArtCameraEnable)
            {
                if (GUILayout.Button("转成老版本镜头"))
                {
                    ConvertOldMethod();
                }
            }

            ShowV1Controller(bmca);

            ShowV2Controller(bmca);


        }

        private void ShowMayaPos()
        {
            GfxEditorUtility.ContentsHeader("3DMax中相机位置:");
            GUILayout.BeginVertical("box");
            var bmca = target as BattleMapCameraAnimation;
            var aa = bmca.ArtCamera;
            if (aa == null || aa.OprCamera == null)
            {
                GUILayout.Label("要像查看3dmax的效果，请进入游戏中");
            }
            else
            {
                var camPos = aa.LocPosTransNode;
                if (camPos != null)
                {
                    EditorGUILayout.Vector3Field("相机位置: (米)", camPos.parent.InverseTransformPoint(aa.OprCamera.transform.position));
                    EditorGUILayout.Vector3Field("相机位置: (厘米)", camPos.parent.InverseTransformPoint(aa.OprCamera.transform.position) * 100);
                    EditorGUILayout.Vector3Field("相机旋转: ", camPos.parent.InverseTransformDirection(aa.OprCamera.transform.rotation.eulerAngles) + new Vector3(0, -180f, 0));
                    EditorGUILayout.FloatField("相机FOV:", aa.RecoveryFov(aa.OprCamera.fieldOfView));
                }
                else
                {
                    GUILayout.Label("找不到Cam_loc");
                }
            }
            GUILayout.EndVertical();
        }

        public void ConvertOldMethod()
        {
            var bmca = target as BattleMapCameraAnimation;
            var artCamera = bmca.ArtCamera;
            if (artCamera == null)
            {
                EditorUtility.DisplayDialog("提示", "无法转换，找不到新版本镜头组件", "ok");
                return;
            }

            var animator = artCamera.GetComponent<Animator>();
            if (animator == null)
            {
                EditorUtility.DisplayDialog("提示", "无法转换，找不到新版本镜头animator", "ok");
                return;
            }
            UnityEditor.Animations.AnimatorController ac = animator.runtimeAnimatorController as UnityEditor.Animations.AnimatorController;
            if (ac != null)
            {
                for (int j = 0; j < ac.layers.Length; j++)
                {
                    AnimatorControllerLayer layer = ac.layers[j];
                    for (int k = 0; k < layer.stateMachine.states.Length; k++)
                    {
                        var state = layer.stateMachine.states[k];

                        var animationClip = state.state.motion as AnimationClip;
                        if (animationClip != null)
                        {
                            if (state.state.name == "入场动画")
                            {
                                CreateClip(bmca, 0, 1, animationClip);
                            }
                            if (state.state.name == "主场胜利")
                            {
                                CreateClip(bmca, 2, 0, animationClip);
                            }
                            if (state.state.name == "客场胜利")
                            {
                                CreateClip(bmca, 2, 1, animationClip);
                            }
                        }
                    }
                }
            }
        }

        private class FrameInfo
        {
            public bool hasPos = false;
            public bool hasRot = false;
            public Vector3 pos;
            public Quaternion rot;

            public void SetValue(string propertyName, float val)
            {
                if (propertyName == "m_LocalPosition.x")
                {
                    hasPos = true;
                    pos = new Vector3(val, pos.y, pos.z);
                }
                if (propertyName == "m_LocalPosition.y")
                {
                    hasPos = true;
                    pos = new Vector3(pos.x, val, pos.z);
                }
                if (propertyName == "m_LocalPosition.z")
                {
                    hasPos = true;
                    pos = new Vector3(pos.x, pos.y, val);
                }

                if (propertyName == "m_LocalRotation.x")
                {
                    hasRot = true;
                    rot = new Quaternion(val, rot.y, rot.z, rot.w);
                }
                if (propertyName == "m_LocalRotation.y")
                {
                    hasRot = true;
                    rot = new Quaternion(rot.x, val, rot.z, rot.w);
                }
                if (propertyName == "m_LocalRotation.z")
                {
                    hasRot = true;
                    rot = new Quaternion(rot.x, rot.y, val, rot.w);
                }
                if (propertyName == "m_LocalRotation.w")
                {
                    hasRot = true;
                    rot = new Quaternion(rot.x, rot.y, rot.z, val);
                }
            }
        }

        private void CreateClip(BattleMapCameraAnimation bmca, int type, int param, AnimationClip animationClip)
        {
            AnimationClip clip = new AnimationClip();
            clip.name = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name + "-" + type + "-" + param;
            clip.legacy = true;
            var setting = AnimationUtility.GetAnimationClipSettings(animationClip);
            AnimationUtility.SetAnimationClipSettings(clip, setting);

            var curveBindings = AnimationUtility.GetCurveBindings(animationClip);

            Vector3 camPos = new Vector3(-0.74f, 25.17f, -25.8f);
            Quaternion rot = Quaternion.Euler(45.68f, 0, 0);

            Dictionary<float, FrameInfo> frameDict = new Dictionary<float, FrameInfo>();

            foreach (var binding in curveBindings)
            {
                // 位置 要和相机原始位置做个偏移，因为他现在的位置是改父节点
                if (binding.path == "Cam_loc")
                {
                    switch (binding.propertyName)
                    {
                        case "m_LocalPosition.x":
                        case "m_LocalPosition.y":
                        case "m_LocalPosition.z":
                        case "m_LocalRotation.x":
                        case "m_LocalRotation.y":
                        case "m_LocalRotation.z":
                        case "m_LocalRotation.w":
                            {
                                var curve = AnimationUtility.GetEditorCurve(animationClip, binding);
                                if (curve.keys.Length != 0)
                                {
                                    for (int i = 0; i < curve.keys.Length; ++i)
                                    {
                                        var key = curve.keys[i];

                                        if (!frameDict.TryGetValue(key.time, out FrameInfo info))
                                        {
                                            info = new FrameInfo();
                                            frameDict.Add(key.time, info);
                                        }

                                        info.SetValue(binding.propertyName, key.value);
                                    }
                                }
                            }
                            break;
                        default:
                            break;
                    }
                }
                else if (binding.path == "Cam_Fov")
                {
                    if (binding.propertyName == "m_LocalScale.x")
                    {
                        var curve = AnimationUtility.GetEditorCurve(animationClip, binding);
                        AnimationCurve newCurve = new AnimationCurve();
                        for (int i = 0; i < curve.keys.Length; ++i)
                        {
                            var key = curve.keys[i];
                            float hFOV_R = 2.0f * Mathf.Atan(36.0f / (2.0f * 35));
                            float vFOV_R = 2.0f * Mathf.Atan(Mathf.Tan(hFOV_R / 2.0f) / (16f / 9f));
                            float vFOV_D = vFOV_R * Mathf.Rad2Deg;
                            key.value = vFOV_D;
                            newCurve.AddKey(key);
                        }

                        clip.SetCurve("CamParent/Main Camera", typeof(Camera), "field of view", newCurve);
                    }
                }
            }

            AnimationCurve xCurve = new AnimationCurve();
            AnimationCurve yCurve = new AnimationCurve();
            AnimationCurve zCurve = new AnimationCurve();

            AnimationCurve rxCurve = new AnimationCurve();
            AnimationCurve ryCurve = new AnimationCurve();
            AnimationCurve rzCurve = new AnimationCurve();
            AnimationCurve rwCurve = new AnimationCurve();

            AnimationCurve orginRxCurve = AnimationUtility.GetEditorCurve(animationClip, EditorCurveBinding.FloatCurve("Cam_loc", typeof(Transform), "m_LocalRotation.x"));
            AnimationCurve orginRyCurve = AnimationUtility.GetEditorCurve(animationClip, EditorCurveBinding.FloatCurve("Cam_loc", typeof(Transform), "m_LocalRotation.y"));
            AnimationCurve orginRzCurve = AnimationUtility.GetEditorCurve(animationClip, EditorCurveBinding.FloatCurve("Cam_loc", typeof(Transform), "m_LocalRotation.z"));
            AnimationCurve orginRwCurve = AnimationUtility.GetEditorCurve(animationClip, EditorCurveBinding.FloatCurve("Cam_loc", typeof(Transform), "m_LocalRotation.w"));

            foreach (var frame in frameDict)
            {
                var b = frame.Value;
                //Vector3 v = b.rot.eulerAngles;
                //v -= rot.eulerAngles;
                //var a = Quaternion.Euler(v);
                var brot = b.rot;
                if (!b.hasRot)
                {
                    brot = new Quaternion(orginRxCurve.Evaluate(frame.Key), orginRyCurve.Evaluate(frame.Key), orginRzCurve.Evaluate(frame.Key), orginRwCurve.Evaluate(frame.Key));
                }

                //Debug.Log("a: " + a + " euler: " + a.eulerAngles + "\na2: " + a2 + " euler: " + a2.eulerAngles);

                b.rot = brot * Quaternion.Inverse(rot);

                if (b.hasPos)
                {
                    b.pos += b.rot * -camPos;

                    xCurve.AddKey(frame.Key, b.pos.x);
                    yCurve.AddKey(frame.Key, b.pos.y);
                    zCurve.AddKey(frame.Key, b.pos.z);
                }
                if (b.hasRot)
                {
                    rxCurve.AddKey(frame.Key, b.rot.x);
                    ryCurve.AddKey(frame.Key, b.rot.y);
                    rzCurve.AddKey(frame.Key, b.rot.z);
                    rwCurve.AddKey(frame.Key, b.rot.w);
                }
            }

            clip.SetCurve("", typeof(Transform), "m_LocalPosition.x", xCurve);
            clip.SetCurve("", typeof(Transform), "m_LocalPosition.y", yCurve);
            clip.SetCurve("", typeof(Transform), "m_LocalPosition.z", zCurve);

            clip.SetCurve("", typeof(Transform), "m_LocalRotation.x", rxCurve);
            clip.SetCurve("", typeof(Transform), "m_LocalRotation.y", ryCurve);
            clip.SetCurve("", typeof(Transform), "m_LocalRotation.z", rzCurve);
            clip.SetCurve("", typeof(Transform), "m_LocalRotation.w", rwCurve);

            // 保存AnimationClip
            AssetDatabase.CreateAsset(clip, "Assets/Art_TFT_Raw/scenes/camera_animation/" + clip.name + ".anim");

            BattleMapCameraAnimation.ClipInfo clipInfo = null;
            for (int ii = 0; ii < bmca.AnimationList.Count; ++ii)
            {
                var a = bmca.AnimationList[ii];
                if (a.type == type && a.param == param)
                {
                    clipInfo = a;
                    break;
                }
            }
            if (clipInfo == null)
            {
                clipInfo = new BattleMapCameraAnimation.ClipInfo();
                clipInfo.type = type;
                clipInfo.param = param;
                bmca.AnimationList.Add(clipInfo);
            }

            clipInfo.clip = clip; 
            if (type == 0)
            {
                clipInfo.isSpecial = false;
                clipInfo.courtType = BattleMapCameraAnimation.CameraAnimationHomeCourtType.MoveToHomeCourt;
            }
            else
            {
                clipInfo.isSpecial = true;
                clipInfo.courtType = BattleMapCameraAnimation.CameraAnimationHomeCourtType.AwayFromHomeCourt;
            }

            EditorUtility.SetDirty(bmca);

            AssetDatabase.SaveAssets();
        }
    }
}
