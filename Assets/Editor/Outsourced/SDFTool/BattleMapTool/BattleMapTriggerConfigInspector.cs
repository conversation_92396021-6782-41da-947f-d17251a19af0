using System;
using System.Collections.Generic;
using SDFTool;
using TKFrame;
using UnityEditor;
using UnityEngine;
using ZGameChess;
using E_CT = ZGameChess.BattleMapTriggerAminationConfig.E_CheckType;
using E_MODT = ZGameChess.BattleMapTriggerAminationConfig.E_MapModifyType;
using E_MTT = ZGameChess.BattleMapTriggerAminationConfig.E_MapTriggerType;
using E_MM = ZGameChess.BattleMapTriggerAminationConfig.E_MatchMode;
using E_ET = ZGameChess.BattleMapTriggerAminationConfig.E_ExtraType;
using ED = ZGameChess.BattleMapTriggerAminationConfig.ExtraData;
using static ZGameChess.BattleMapTriggerAminationConfig;

public class BattleMapTriggerConfigInspector
{
    private enum E_CheckType
    {
        回合对局数,
        对局胜利,
        对局失败,
        对局连胜,
        对局连败,
        羁绊达成,
        达成指定排名,
        英雄达成星级,
        击败玩家出局,
        玩家自己出局,
        比赛开始,
        切换观战,
        客场跳入,
        客场吃鸡,
        小小英雄观战,
        玩家等级,
        到达时间,
        选秀开始,
        战斗阶段,
    }

    private const string defaultParams = "{0}:{1}:{2}";
    private const string specialParams = "{0}:{1}:{2}|{3}";

    private static TKDictionary<E_CheckType, string> triggerToNameDic;
    private static TKDictionary<string, E_CheckType> nameToTriggerDic;
    private static TKDictionary<string, ED> extraMaps = new TKDictionary<string, ED>();
    private static GUIStyle m_boxStyle = new GUIStyle(GUI.skin.box);

    private static void InitTriggerDic()
    {
        if (triggerToNameDic == null)
        {
            triggerToNameDic = new TKDictionary<E_CheckType, string>
            {
                { E_CheckType.回合对局数, E_CT.TURN_COUNT },
                { E_CheckType.对局胜利, E_CT.TURN_WIN },
                { E_CheckType.对局失败, E_CT.TURN_LOSE },
                { E_CheckType.对局连胜, E_CT.TURN_WIN_COUNT },
                { E_CheckType.对局连败, E_CT.TURN_LOSE_COUNT },
                { E_CheckType.羁绊达成, E_CT.FETTER_REACH },
                { E_CheckType.达成指定排名, E_CT.RANK_REACH },
                { E_CheckType.英雄达成星级, E_CT.HERO_STAR_MATCH },
                { E_CheckType.击败玩家出局, E_CT.USER_OUT },
                { E_CheckType.玩家自己出局, E_CT.PLAYER_OUT },
                { E_CheckType.比赛开始, E_CT.GAME_START },
                { E_CheckType.切换观战, E_CT.CHANGE_OBSERVE },
                { E_CheckType.客场跳入, E_CT.JUMP_TO_MAP },
                { E_CheckType.客场吃鸡, E_CT.VISITFIELD_RANK_FIRST },
                { E_CheckType.小小英雄观战, E_CT.TINY_HERO_VISIT },
                { E_CheckType.玩家等级, E_CT.PLAYER_LEVEL },
                { E_CheckType.到达时间, E_CT.REACH_TIME },
                { E_CheckType.选秀开始, E_CT.ROUND_SELECT_START },
                { E_CheckType.战斗阶段, E_CT.BATTLE_STAGE },
            };
        }

        if (nameToTriggerDic == null)
        {
            nameToTriggerDic = new TKDictionary<string, E_CheckType>
            {
                { E_CT.TURN_COUNT, E_CheckType.回合对局数 },
                { E_CT.TURN_WIN, E_CheckType.对局胜利 },
                { E_CT.TURN_LOSE, E_CheckType.对局失败 },
                { E_CT.TURN_WIN_COUNT, E_CheckType.对局连胜 },
                { E_CT.TURN_LOSE_COUNT, E_CheckType.对局连败 },
                { E_CT.FETTER_REACH, E_CheckType.羁绊达成 },
                { E_CT.RANK_REACH, E_CheckType.达成指定排名 },
                { E_CT.HERO_STAR_MATCH, E_CheckType.英雄达成星级 },
                { E_CT.USER_OUT, E_CheckType.击败玩家出局 },
                { E_CT.PLAYER_OUT, E_CheckType.玩家自己出局 },
                { E_CT.GAME_START, E_CheckType.比赛开始 },
                { E_CT.CHANGE_OBSERVE, E_CheckType.切换观战 },
                { E_CT.JUMP_TO_MAP, E_CheckType.客场跳入 },
                { E_CT.VISITFIELD_RANK_FIRST, E_CheckType.客场吃鸡 },
                { E_CT.TINY_HERO_VISIT, E_CheckType.小小英雄观战 },
                { E_CT.PLAYER_LEVEL, E_CheckType.玩家等级 },
                { E_CT.REACH_TIME, E_CheckType.到达时间 },
                { E_CT.ROUND_SELECT_START, E_CheckType.选秀开始 },
                { E_CT.BATTLE_STAGE, E_CheckType.战斗阶段 },
            };
        }
    }

    private static E_CheckType GetSelectType(string msg)
    {
        if (msg.Filled())
        {
            if (nameToTriggerDic.TryGetValue(msg, out E_CheckType type))
            {
                return type;
            }
        }

        return E_CheckType.回合对局数;
    }

    private static string GetTriggerName(E_CheckType type)
    {
        if (triggerToNameDic.TryGetValue(type, out string triggerName))
        {
            return triggerName;
        }

        return "";
    }

    public static void Fill(BattleMapTriggerAminationConfig cfg,int m_enumIndex,string[] m_enumDescArr)
    {
        bool hasAnimator = true;
        InitTriggerDic();

        E_CheckType selectType = E_CheckType.回合对局数;
        string[] baseCfgs = null;
        string otherCfg = "";
        if (cfg.m_enterAnimationName.Filled())
        {
            var strs = cfg.m_enterAnimationName.Split('|');
            baseCfgs = strs[0].Split(':');
            if (strs.Length > 1)
            {
                otherCfg = strs[1];
            }
        }
        else
        {
            baseCfgs = new string[1]
            {
                GetTriggerName(selectType)
            };
        }

        if (baseCfgs.Length > 0)
        {
            selectType = GetSelectType(baseCfgs[0]);
        }
        
        cfg.m_animator = (Animator)EditorGUILayout.ObjectField("动画控制器: ", cfg.m_animator, typeof(Animator), true);

        string animName = "";
        if (baseCfgs.Length > 2 && hasAnimator)
        {
            animName = baseCfgs[2];
        }

        BattleMapConfigEditBasePage.RefreshAnimationClipsEnum(cfg.m_animator,
            animName,
            ref m_enumIndex,
            ref m_enumDescArr);

        if (m_enumDescArr != null)
        {
            EditorGUI.BeginChangeCheck();
            m_enumIndex = EditorGUILayout.Popup("动画选择：", m_enumIndex, m_enumDescArr, GUILayout.Width(350.0f));
            if (EditorGUI.EndChangeCheck())
            {
                animName = m_enumDescArr[m_enumIndex];
            }
        }

        cfg.m_needCache = EditorGUILayout.Toggle("是否缓存：", cfg.m_needCache);

        selectType = (E_CheckType)EditorGUILayout.EnumPopup("触发类型：", selectType);
        string triggerName = GetTriggerName(selectType);

        int checkParam = 0;
        if (baseCfgs.Length > 1)
        {
            checkParam = int.Parse(baseCfgs[1]);
            checkParam = FillCheckParam(selectType, checkParam);
        }

        if(selectType == E_CheckType.客场吃鸡)
        {
            cfg.AniVisitFirstGO = (GameObject)EditorGUILayout.ObjectField("转移的动画节点: ", cfg.AniVisitFirstGO, typeof(GameObject), true);
        }
        else if (selectType == E_CheckType.对局连败)
        {
            cfg.IsMutex = EditorGUILayout.Toggle("屏蔽失败动画: ", cfg.IsMutex);
        }
        else if (selectType == E_CheckType.对局连胜)
        {
            cfg.IsMutex = EditorGUILayout.Toggle("屏蔽胜利动画: ", cfg.IsMutex);
        }
        else if( selectType == E_CheckType.客场跳入)
        {
            cfg.m_useWhiteList = EditorGUILayout.Toggle("仅指定小小英雄可触发: ", cfg.m_useWhiteList);
            if (cfg.m_useWhiteList)
            {
                if (cfg.m_modelConfigWhiteList != null)
                {
                    for (int i = 0; i < cfg.m_modelConfigWhiteList.Count; ++i)
                    {
                        if (DrawModelNameWhiteList(i, cfg.m_modelConfigWhiteList))
                        {
                            cfg.m_modelConfigWhiteList.RemoveAt(i);
                            break;
                        }
                    }
                }

                if (GUILayout.Button("添加小小英雄", GUILayout.Width(180)))
                {
                    if (cfg.m_modelConfigWhiteList == null)
                    {
                        cfg.m_modelConfigWhiteList = new List<BattleMapTriggerModelOverrideConfig>();
                    }

                    var overrideConfig = new BattleMapTriggerModelOverrideConfig();
                    overrideConfig.overrideEnterAnimName = cfg.m_enterAnimationName;
                    overrideConfig.overrideEnterTriggerType = cfg.m_enterTriggerType;
                    overrideConfig.overrideExitAnimName = cfg.m_exitAnimationName;
                    overrideConfig.overrideExitTriggerType = cfg.m_exitTriggerType;
                    overrideConfig.overrideBlendTime = cfg.m_blendTime;
                    overrideConfig.overridePlaySound = cfg.m_playSound;
                    overrideConfig.overrideBankName = cfg.m_bankName;
                    overrideConfig.overrideSoundName = cfg.m_soundName;
                    overrideConfig.overrideSoundObj = cfg.m_soundObj;
                    overrideConfig.overrideSoundInfos = cfg.m_soundInfos;
                    overrideConfig.overrideSetSwitch = cfg.m_setSwitch;
                    overrideConfig.overrideIsSetState = cfg.m_isSetState;
                    overrideConfig.overrideIsSetRTPC = cfg.m_isSetRTPC;
                    overrideConfig.overrideSwitchObj = cfg.m_switchObj;
                    overrideConfig.overrideSwitchGroup = cfg.m_switchGroup;
                    overrideConfig.overrideSwitchKey = cfg.m_switchKey;
                    overrideConfig.overrideNeedCache = cfg.m_needCache;
                    cfg.m_modelConfigWhiteList.Add(overrideConfig);
                }
            }
        }
        else if (selectType == E_CheckType.战斗阶段)
        {
            BattleMapManagerTool.BeginShowHelp("从Project里拖入，需要替换的利息球prefab，左侧");
            cfg.SwitchInterestTemplate = (GameObject)EditorGUILayout.ObjectField("切换利息球-左: ", cfg.SwitchInterestTemplate, typeof(GameObject), false);
            BattleMapManagerTool.EndShowHelp();

            BattleMapManagerTool.BeginShowHelp("从Project里拖入，需要替换的利息球prefab，右侧");
            cfg.SwitchInterestTemplate2 = (GameObject)EditorGUILayout.ObjectField("切换利息球-右: ", cfg.SwitchInterestTemplate2, typeof(GameObject), false);
            BattleMapManagerTool.EndShowHelp();
        }

        cfg.m_mapModifyType = (E_MODT)EditorGUILayout.EnumPopup("场景修改状态", cfg.m_mapModifyType);

        GUI.enabled = false;
        EditorGUILayout.TextField("触发后播放动画：", animName);
        GUI.enabled = true;

        if (FillOtherParams(cfg, selectType, otherCfg, triggerName, checkParam, animName))
        {
            //特殊配置
            int count = 0;
            otherCfg = "";
            foreach (var item in extraMaps.Values)
            {
                count++;

                otherCfg += item.Encode();
                if (count < extraMaps.Count)
                {
                    otherCfg += ",";
                }
            }

            cfg.m_enterAnimationName = string.Format(specialParams, triggerName, checkParam, animName, otherCfg);
        }
        else
        {
            //默认配置
            cfg.m_enterAnimationName = string.Format(defaultParams, triggerName, checkParam, animName);
        }

        cfg.IsFlyEffect = EditorGUILayout.Toggle("是否飞行特效：", cfg.IsFlyEffect);

        if (cfg.IsFlyEffect)
        {
            cfg.FlyEffectTargetType = (BattleMapTriggerAminationConfig.E_MAP_FLY_EFFECT_TARGET_TYPE)EditorGUILayout.EnumPopup("飞行特效-目标类型", cfg.FlyEffectTargetType);
            cfg.FlyEffectAnimator = (Animator)EditorGUILayout.ObjectField("命中-动画控制器: ", cfg.FlyEffectAnimator, typeof(Animator), true);

            BattleMapConfigEditBasePage.RefreshAnimationClipsEnum(cfg.FlyEffectAnimator,
                cfg.FlyEffectAnimName,
                ref m_enumIndex,
                ref m_enumDescArr);

            if (m_enumDescArr != null)
            {
                EditorGUI.BeginChangeCheck();
                m_enumIndex = EditorGUILayout.Popup("命中-动画选择：", m_enumIndex, m_enumDescArr, GUILayout.Width(350.0f));
                if (EditorGUI.EndChangeCheck())
                {
                    cfg.FlyEffectAnimName = m_enumDescArr[m_enumIndex];
                }
            }
            cfg.FlySpeed = (float)EditorGUILayout.FloatField("飞行速度", cfg.FlySpeed);
        }
    }

    public static string GetEventTriggerDesc(BattleMapTriggerAminationConfig cfg, bool getAnimName = false)
    {
        if (string.IsNullOrEmpty(cfg.m_enterAnimationName))
        {
            return "";
        }

        InitTriggerDic();

        cfg.Init();

        var animData = cfg.GetEnterAnimData();

        if (animData == null)
        {
            return null;
        }

        if (getAnimName)
        {
            return animData.animName;
        }

        var selectType = GetSelectType(animData.type);
        string triggerName = Enum.GetName(typeof(E_CheckType), selectType);
        string triggerParam = animData.checkValue.ToString();
        string detailStr = null;

        var extraMap = animData.extraMap;
        if (extraMap != null)
        {
            switch (selectType)
            {
                case E_CheckType.羁绊达成:
                    if (extraMap.TryGetValue(E_ET.FETTER_COUNT, out var fetterData))
                    {
                        detailStr = "羁绊数量 " + (int)fetterData.floatParam;
                    }
                    break;
                case E_CheckType.英雄达成星级:
                    if (extraMap.TryGetValue(E_ET.HERO_STAR_COUNT, out var starData))
                    {
                        detailStr = "英雄星级 " + (int)starData.floatParam;
                    }
                    break;
                case E_CheckType.到达时间:
                    if (extraMap.TryGetValue(E_ET.SERVER_TIME, out var reachTimeData)
                        && !string.IsNullOrEmpty(reachTimeData.stringParam)
                        )
                    {
                        detailStr = "时间 " + reachTimeData.stringParam;
                    }
                    break;
                case E_CheckType.对局胜利:
                case E_CheckType.对局连胜:
                    if (extraMap.TryGetValue(E_ET.SERVER_TIME_START, out var serverTimeStart)
                        && extraMap.TryGetValue(E_ET.SERVER_TIME_END, out var serverTimeEnd)
                        && !string.IsNullOrEmpty(serverTimeStart.stringParam)
                        && !string.IsNullOrEmpty(serverTimeEnd.stringParam)
                       )
                    {
                        detailStr = "时间 " + serverTimeStart.stringParam +"-"+ serverTimeEnd.stringParam;
                    }
                    break;
            }
        }

        if (string.IsNullOrEmpty(triggerName)
        || string.IsNullOrEmpty(triggerParam)
        )
        {
            return null;
        }

        return triggerName + " " + triggerParam + (string.IsNullOrEmpty(detailStr) ? "" : ("  " + detailStr));
    }

    private static bool DrawModelNameWhiteList(int index, List<BattleMapTriggerModelOverrideConfig> whiteList)
    {
        EditorGUILayout.BeginHorizontal();
        GUILayout.Label(string.Format("第{0}个小小英雄:", index + 1));
        if (GUILayout.Button("删除小小英雄", GUILayout.Width(180)))
        {
            return true;
        }
        EditorGUILayout.EndHorizontal();
        whiteList[index].modelName = EditorGUILayout.TextField(string.Format("No.{0}模型名称:", index + 1), whiteList[index].modelName);
        whiteList[index].nameMatchMode = (E_MM)EditorGUILayout.EnumPopup(string.Format("No.{0}模型名称匹配模式:", index + 1), whiteList[index].nameMatchMode);
        whiteList[index].overrideEnterAnimName = EditorGUILayout.TextField(string.Format("No.{0}重载进入动画:", index + 1), whiteList[index].overrideEnterAnimName);
        whiteList[index].overrideEnterTriggerType = (E_MTT)EditorGUILayout.EnumPopup(string.Format("No.{0}进入动画触发类型:", index + 1), whiteList[index].overrideEnterTriggerType);
        whiteList[index].overrideExitAnimName = EditorGUILayout.TextField(string.Format("No.{0}重载离开动画:", index + 1), whiteList[index].overrideExitAnimName);
        whiteList[index].overrideExitTriggerType = (E_MTT)EditorGUILayout.EnumPopup(string.Format("No.{0}离开动画触发类型:", index + 1), whiteList[index].overrideExitTriggerType);
        whiteList[index].overrideBlendTime = EditorGUILayout.FloatField(string.Format("No.{0}动画过渡混合时间(单位:s):", index + 1), whiteList[index].overrideBlendTime);
        whiteList[index].overridePlaySound = EditorGUILayout.Toggle(string.Format("No.{0}播放音效:", index + 1), whiteList[index].overridePlaySound);
        if (whiteList[index].overridePlaySound)
        {
            whiteList[index].overrideSoundObj = (GameObject)EditorGUILayout.ObjectField(string.Format("No.{0}切换物体:", index + 1), whiteList[index].overrideSoundObj, typeof(GameObject), true);
            whiteList[index].overrideBankName = EditorGUILayout.TextField(string.Format("No.{0}BankName:", index + 1), whiteList[index].overrideBankName);
            whiteList[index].overrideSoundName = EditorGUILayout.TextField(string.Format("No.{0}SoundName:", index + 1), whiteList[index].overrideSoundName);
            int soundRemoveId = -1;
            List<SoundInfo> soundInfos = whiteList[index].overrideSoundInfos;
            if (soundInfos != null && soundInfos.Count > 0)
            {
                for (int i = 0, n = soundInfos.Count; i < n; ++i)
                {
                    string bankName = soundInfos[i].soundBank;
                    string eventName = soundInfos[i].soundEvent;
                    GameObject obj = soundInfos[i].soundObj;
                    if (DrawSoundInfoArea(i, ref bankName, ref eventName, ref obj))
                    {
                        soundRemoveId = i;
                    }
                    else
                    {
                        soundInfos[i].soundBank = bankName;
                        soundInfos[i].soundEvent = eventName;
                        soundInfos[i].soundObj = obj;
                    }
                }
            }

            if (soundRemoveId != -1)
            {
                soundInfos.RemoveAt(soundRemoveId);
            }

            if (GUILayout.Button("添加"))
            {
                soundInfos.Add(new SoundInfo());
            }
        }
        whiteList[index].overrideSetSwitch = EditorGUILayout.Toggle(string.Format("No.{0}切换音效:", index + 1), whiteList[index].overrideSetSwitch);
        if (whiteList[index].overrideSetSwitch)
        {
            whiteList[index].overrideIsSetState = EditorGUILayout.Toggle(string.Format("No.{0}是否State:", index + 1), whiteList[index].overrideIsSetState);
            whiteList[index].overrideIsSetRTPC = EditorGUILayout.Toggle(string.Format("No.{0}是否State:", index + 1), whiteList[index].overrideIsSetRTPC);
            whiteList[index].overrideSwitchObj = (GameObject)EditorGUILayout.ObjectField(string.Format("No.{0}切换物体:", index + 1), whiteList[index].overrideSwitchObj, typeof(GameObject), true);
            whiteList[index].overrideSwitchGroup = EditorGUILayout.TextField(string.Format("No.{0}SwitchGroup:", index + 1), whiteList[index].overrideSwitchGroup);
            whiteList[index].overrideSwitchKey = EditorGUILayout.TextField(string.Format("No.{0}SwitchKey:", index + 1), whiteList[index].overrideSwitchKey);
        }

        return false;
    }

    private static int FillCheckParam(E_CheckType selectType, int checkParam)
    {
        switch (selectType)
        {
            case E_CheckType.回合对局数:
                BattleMapManagerTool.BeginShowHelp("填 0 代表每回合都触发");
                checkParam = EditorGUILayout.IntField("游戏进行到X回合：", checkParam);
                BattleMapManagerTool.EndShowHelp();
                break;
            case E_CheckType.对局连胜:
                checkParam = EditorGUILayout.IntField("对局连胜次数：", checkParam);
                break;
            case E_CheckType.对局连败:
                checkParam = EditorGUILayout.IntField("对局连败次数：", checkParam);
                break;
            case E_CheckType.羁绊达成:
                checkParam = EditorGUILayout.IntField("达成羁绊ID：", checkParam);
                break;
            case E_CheckType.达成指定排名:
                checkParam = EditorGUILayout.IntField("玩家进入第X名内：", checkParam);
                break;
            case E_CheckType.英雄达成星级:
                checkParam = EditorGUILayout.IntField("英雄ID：", checkParam);
                break;
            case E_CheckType.小小英雄观战:
                checkParam = EditorGUILayout.IntField("触发CD（秒）：", checkParam);
                break;
            case E_CheckType.玩家等级:
                checkParam = EditorGUILayout.IntField("等级值：", checkParam);
                break;
            case E_CheckType.选秀开始:
                checkParam = EditorGUILayout.IntField("第X次选秀：", checkParam);
                break;
            case E_CheckType.战斗阶段:
                checkParam = EditorGUILayout.IntField("战斗阶段：", checkParam);
                break;
        }

        return checkParam;
    }

    private static bool FillOtherParams(BattleMapTriggerAminationConfig cfg, E_CheckType selectType,
        string otherCfg, string triggerName, int checkParam, string animName)
    {
        extraMaps = ED.Decode(otherCfg, extraMaps);
        switch (selectType)
        {
            case E_CheckType.比赛开始:
                {
                    #region 贴地特殊参数
                    ED groundData;
                    extraMaps.TryGetValue(E_ET.GROUND_TYPE, out groundData);

                    groundData.type = E_ET.GROUND_TYPE;
                    groundData.boolParam = EditorGUILayout.Toggle("是否贴地处理：", groundData.boolParam);
                    if (groundData.boolParam)
                    {
                        groundData.floatParam = EditorGUILayout.FloatField("贴地持续时间：", groundData.floatParam);
                        extraMaps[E_ET.GROUND_TYPE] = groundData;
                    }
                    else
                    {
                        extraMaps.Remove(E_ET.GROUND_TYPE);
                    }                   
                    #endregion

                    #region 挂点特殊参数
                    ED hangPointData;
                    extraMaps.TryGetValue(E_ET.HANG_POINT_TYPE, out hangPointData);

                    hangPointData.type = E_ET.HANG_POINT_TYPE;
                    BattleMapManagerTool.BeginShowHelp("把小小英雄挂到某个物体下，跟随物体移动，例如骑龙");
                    hangPointData.boolParam = EditorGUILayout.Toggle("是否切换小小英雄挂点：", hangPointData.boolParam);
                    BattleMapManagerTool.EndShowHelp();
                    if (hangPointData.boolParam)
                    {
                        hangPointData.stringParam = EditorGUILayout.TextField("挂点名字：", hangPointData.stringParam);
                        hangPointData.floatParam = EditorGUILayout.FloatField("挂点持续时间：", hangPointData.floatParam);
                        extraMaps[E_ET.HANG_POINT_TYPE] = hangPointData;
                    }
                    else
                    {
                        extraMaps.Remove(E_ET.HANG_POINT_TYPE);
                    }                   
                    #endregion
                }               
                return true;
            case E_CheckType.羁绊达成:
                {
                    ED fetterData;
                    extraMaps.TryGetValue(E_ET.FETTER_COUNT, out fetterData);

                    fetterData.type = E_ET.FETTER_COUNT;

                    int value = (int)fetterData.floatParam;
                    value = EditorGUILayout.IntField("达成羁绊数量：", value);
                    fetterData.floatParam = value;

                    bool boolValue = fetterData.boolParam;
                    fetterData.boolParam = EditorGUILayout.Toggle("不会重复触发 ", boolValue);

                    extraMaps[E_ET.FETTER_COUNT] = fetterData;

                    SetCDTime();
                }
                
                return true;
            case E_CheckType.英雄达成星级:
                {
                    ED starData;
                    extraMaps.TryGetValue(E_ET.HERO_STAR_COUNT, out starData);

                    starData.type = E_ET.HERO_STAR_COUNT;

                    int value = (int)starData.floatParam;
                    value = EditorGUILayout.IntField("英雄星级：", value);
                    starData.floatParam = value;

                    bool boolValue = starData.boolParam;
                    starData.boolParam = EditorGUILayout.Toggle("不会重复触发 ", boolValue);

                    extraMaps[E_ET.HERO_STAR_COUNT] = starData;

                    SetCDTime();
                }
                
                return true;
            case E_CheckType.达成指定排名:
                {
                    cfg.m_needCache = true;

                    ED stayStateData;
                    extraMaps.TryGetValue(E_ET.STAY_STATE, out stayStateData);
                    stayStateData.type = E_ET.STAY_STATE;
                    stayStateData.boolParam = EditorGUILayout.Toggle("保持达成后状态：", stayStateData.boolParam);
                    extraMaps[E_ET.STAY_STATE] = stayStateData;

                    #region 挂点特殊参数
                    ED hangPointData;
                    extraMaps.TryGetValue(E_ET.HANG_POINT_TYPE, out hangPointData);

                    hangPointData.type = E_ET.HANG_POINT_TYPE;
                    BattleMapManagerTool.BeginShowHelp("把小小英雄挂到某个物体下，跟随物体移动，例如骑龙");
                    hangPointData.boolParam = EditorGUILayout.Toggle("是否切换小小英雄挂点：", hangPointData.boolParam);
                    BattleMapManagerTool.EndShowHelp();
                    if (hangPointData.boolParam)
                    {
                        hangPointData.stringParam = EditorGUILayout.TextField("挂点名字：", hangPointData.stringParam);
                        hangPointData.floatParam = EditorGUILayout.FloatField("挂点持续时间：", hangPointData.floatParam);
                        extraMaps[E_ET.HANG_POINT_TYPE] = hangPointData;
                    }
                    else
                    {
                        extraMaps.Remove(E_ET.HANG_POINT_TYPE);
                    }
                    #endregion
                }
                return true;
            case E_CheckType.玩家等级:
                {
                    ED compareTypeData;
                    extraMaps.TryGetValue(E_ET.COMPARE_TYPE, out compareTypeData);
                    compareTypeData.type = E_ET.COMPARE_TYPE;

                    compareTypeData.intParam = (int)(E_CompareType)EditorGUILayout.EnumPopup("比较方式", (E_CompareType)compareTypeData.intParam);
                    extraMaps[E_ET.COMPARE_TYPE] = compareTypeData;
                }
                return true;
            case E_CheckType.到达时间:
            {
                ED reachTimeData;
                extraMaps.TryGetValue(E_ET.SERVER_TIME, out reachTimeData);
                reachTimeData.type = E_ET.SERVER_TIME;

                BattleMapManagerTool.BeginShowHelp("格式：XX:XX:XX，例如 00:00:00 ，英文冒号，注意，最大只能是 23:59:59，不能填 24:00:00\n\n" +
                                                   "特殊机制：前面加state，例如 state00:00:00 ，代表需要保持状态用的动效，比如18点-24点保持黑夜");
                reachTimeData.stringParam = EditorGUILayout.TextField("服务器时间", reachTimeData.stringParam);
                BattleMapManagerTool.EndShowHelp();

                extraMaps[E_ET.SERVER_TIME] = reachTimeData;
            }
                return true;
            case E_CheckType.对局胜利:
            case E_CheckType.对局连胜:
            {
                //带时间判断的
                extraMaps.TryGetValue(E_ET.SERVER_TIME_START, out var serverTimeStart);
                serverTimeStart.type = E_ET.SERVER_TIME_START;

                extraMaps.TryGetValue(E_ET.SERVER_TIME_END, out var serverTimeEnd);
                serverTimeEnd.type = E_ET.SERVER_TIME_END;

                BattleMapManagerTool.BeginShowHelp("开始时间 格式：XX:XX:XX，例如 00:00:00 ，英文冒号，注意，最大只能是 23:59:59，不能填 24:00:00");
                serverTimeStart.stringParam = EditorGUILayout.TextField("开始时间", serverTimeStart.stringParam);
                BattleMapManagerTool.EndShowHelp();

                BattleMapManagerTool.BeginShowHelp("结束时间 格式：XX:XX:XX，例如 00:00:00 ，英文冒号，注意，最大只能是 23:59:59，不能填 24:00:00");
                serverTimeEnd.stringParam = EditorGUILayout.TextField("结束时间", serverTimeEnd.stringParam);
                BattleMapManagerTool.EndShowHelp();

                extraMaps[E_ET.SERVER_TIME_START] = serverTimeStart;
                extraMaps[E_ET.SERVER_TIME_END] = serverTimeEnd;
            }
                return true;
        }

        return false;
    }

    private static void SetCDTime()
    {
        ED cdTime;
        extraMaps.TryGetValue(E_ET.CD_TIME, out cdTime);
        cdTime.type = E_ET.CD_TIME;
        int cdValue = (int) cdTime.floatParam;
        cdValue = EditorGUILayout.IntField("触发CD：", cdValue);
        cdTime.floatParam = cdValue;
        extraMaps[E_ET.CD_TIME] = cdTime;
    }

    protected static bool DrawSoundInfoArea(int index, ref string bankName, ref string eventName, ref GameObject obj)
    {
        EditorGUILayout.BeginHorizontal(m_boxStyle);
        EditorGUILayout.BeginVertical(m_boxStyle);

        EditorGUI.BeginChangeCheck();

        bankName = EditorGUILayout.TextField("Bank Name(" + index + "): ", bankName);
        eventName = EditorGUILayout.TextField("Event Name(" + index + "): ", eventName);
        obj = (GameObject)EditorGUILayout.ObjectField("Event Obj(" + index + "): ", obj, typeof(GameObject), true);

        EditorGUILayout.EndVertical();

        if (GUILayout.Button("删除", GUILayout.Width(50), GUILayout.ExpandWidth(true)))
        {
            EditorGUILayout.EndHorizontal();
            return true;
        }
        EditorGUILayout.EndHorizontal();

        return false;
    }
}