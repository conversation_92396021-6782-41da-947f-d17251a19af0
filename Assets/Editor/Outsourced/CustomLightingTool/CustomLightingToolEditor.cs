//using System;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;
using System.Collections.Generic;
using UnityEngine.SceneManagement;
using System.IO;
using System.Linq;
using System.Reflection;
#if UNITY_EDITOR
#endif

#if !ENABLE_TYPE_TREE_IGNORE
namespace PBRTools
{
    [CustomEditor(typeof(CustomLightingTool))]
    public class CustomLightingToolEditor : Editor
    {

        //[MenuItem("GameObject/Light/512")]
        //static void InvokeCreateCustomLightx()
        //{
        //    Selection.activeGameObject.GetComponent<Light>().shadowCustomResolution = 2048;
        //}

        //[MenuItem("GameObject/Light/128")]
        //static void InvokeCreateCustomLighstx()
        //{
        //    Selection.activeGameObject.GetComponent<Light>().shadowCustomResolution = 512;
        //}


        [MenuItem("GameObject/Light/自定义PBR光照")]
        static void InvokeCreateCustomLight()
        {
            CreateCustomLight();
        }


        [MenuItem("GameObject/创建 自定义PBR光照", false, 0)]
        static void InvokeCreateCustomLight1()
        {
            CreateCustomLight();
        }
        [MenuItem("PBR工具/创建 自定义PBR光照")]
        static void InvokeCreateCustomLight2()
        {
            CreateCustomLight();
        }


        [MenuItem("GameObject/添加 到自定义PBR光照", false, 0)]
        static void InvokeAddtoCustomLightTool()
        {
            AddtoCustomLightTool();
        }
        [MenuItem("GameObject/移除 自定义PBR光照", false, 0)]
        static void InvokeRemoveFromCustomLightTool()
        {
            RemoveFromCustomLightTool();
        }

        public static void MarkCubemapReadable(Cubemap cube, bool readable)
        {
            Assembly assembly = typeof(UnityEditor.AssetDatabase).Assembly;
            var type = assembly.GetType("UnityEditor.TextureUtil");
            if (type == null)
            {
                Debug.LogError("Can't Get <UnityEditor.TextureUtil>");
                return;
            }
            var method = type.GetMethod("MarkCubemapReadable", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Static);
            if (method == null)
            {
                Debug.Log("Can't Get <UnityEditor.TextureUtil.MarkCubemapReadable>");
                return;
            }
            method.Invoke(null, new object[] { cube, readable });
        }

        //[MenuItem("PBR工具/创建 GetToolPath")]
        //static void InvokeGetToolPath()
        //{
        //    GetToolPath();
        //}
        public static void AddtoCustomLightTool()
        {


            CustomLightingTool cl = GameObject.FindObjectOfType<CustomLightingTool>();
            List<GameObject> objs = new List<GameObject>();
            if (cl != null)
            {
                foreach (var item in Selection.gameObjects)
                {
                    if (item != cl.gameObject)
                    {

                        bool isExist = false;
                        foreach (var l in cl.CustomLightObjs)
                        {
                            if (l == item)
                            {
                                isExist = true;
                            }
                        }
                        if (!isExist)
                        {
                            objs.Add(item as GameObject);
                        }
                    }
                }

                if (objs.Count > 0)
                {
                    Undo.RegisterCompleteObjectUndo(cl, "CustomLightObjs add select " + cl.CustomLightObjs.Length.ToString());
                    //Debug.Log("CustomLightObjs add " + ligthSetting.CustomLightObjs.Length.ToString()); 
                    GameObject[] tempObjs = new GameObject[cl.CustomLightObjs.Length + objs.Count];
                    for (int i = 0; i < tempObjs.Length; i++)
                    {
                        if (i > cl.CustomLightObjs.Length - 1)
                        {
                            tempObjs[i] = objs[Mathf.Max(0, i - cl.CustomLightObjs.Length)];
                        }
                        else
                        {
                            tempObjs[i] = cl.CustomLightObjs[i];
                        }
                    }
                    cl.CustomLightObjs = tempObjs;
                }
            }
            else
            {
                EditorUtility.DisplayDialog("CustomLightTools", "请先创建 自定义光照！", "好的!");

            }

        }

        public static void RemoveFromCustomLightTool()
        {
            CustomLightingTool cl = GameObject.FindObjectOfType<CustomLightingTool>();
            List<GameObject> objs = new List<GameObject>();
            objs = cl.CustomLightObjs.ToList<GameObject>();
            foreach (var item in Selection.gameObjects)
            {
                foreach (var l in cl.CustomLightObjs)
                {
                    if (l == item)
                    {
                        objs.Remove(item);
                        if (item != null)
                        {
                            Renderer[] rds = item.GetComponentsInChildren<Renderer>();
                            foreach (var itemerd in rds)
                            {
                                itemerd.probeAnchor = null;
                                itemerd.lightProbeUsage = LightProbeUsage.BlendProbes;
                                //SphericalHarmonicsL2 SH1=new SphericalHarmonicsL2();
                                //for (int i = 0; i < 9; i++)
                                //{
                                //    SH1[0, i] = RenderSettings.ambientProbe[0, i];
                                //    SH1[1, i] = RenderSettings.ambientProbe[1, i];
                                //    SH1[2, i] = RenderSettings.ambientProbe[2, i];
                                //}
                                //SphericalHarmonicsL2[] shs = null;
                                //shs = new SphericalHarmonicsL2[1] { SH1 };
                                //MaterialPropertyBlock te = new MaterialPropertyBlock();
                                //itemerd.GetPropertyBlock(te);
                                //te.CopySHCoefficientArraysFrom(shs);
                                //itemerd.SetPropertyBlock(te);
                            }
                        }
                    }
                }
            }
            cl.CustomLightObjs = objs.ToArray();


            Undo.RegisterCompleteObjectUndo(cl, "CustomLightObjs deletxx");
            //delete object 



        }


        public static void CreateCustomLight()
        {
            GetToolPath();
            Scene scene = UnityEngine.SceneManagement.SceneManager.GetActiveScene();
            string filename = "";

            CustomLightingTool cl = GameObject.FindObjectOfType<CustomLightingTool>();
            if (cl != null)
            {
                EditorUtility.DisplayDialog("CustomLightTools", "只能存在一个自定义光照", "好的!");
                return;

            }
            else
            {
                if (!File.Exists(scene.path))
                {
                    EditorUtility.DisplayDialog("CustomLightTools", "请先保存下场景在创建 自定义光照！", "好的!");
                    return;
                }
                else
                {
                    filename = scene.path;
                    filename = Path.GetDirectoryName(filename).Replace("\\", "/") + "/" + scene.name;
                    filename = Application.dataPath.Replace("/Assets", "") + "/" + filename + "/";

                    if (Directory.Exists(filename))
                    {

                    }
                    else
                    {
                        Directory.CreateDirectory(filename);

                    }
                    string newfilename = filename + "CustomLighting.prefab";
                    bool next = true;
                    int count = 0;
                    while (next)
                    {
                        if (File.Exists(newfilename))
                        {
                            newfilename = filename + "CustomLighting" + count.ToString() + ".prefab";
                            count += 1;
                        }
                        else
                        {
                            filename = newfilename;
                            next = false;
                        }
                        if (count > 10)
                        {
                            next = false;
                            EditorUtility.DisplayDialog("CustomLightTools", "请清理下 自定义光照prefab 数量太多了", "好的!");

                        }

                    }
                    filename = "Assets/" + filename.Replace(Application.dataPath + "/", "");
                    //filename =filename.Replace(Application.dataPath + "/", "");
                    //Debug.Log(filename);
                    //UnityEngine.Object clt = AssetDatabase.LoadAssetAtPath("Assets/Scripts/CustomLightingTool/CustomLighting.prefab", typeof(UnityEngine.Object));
                    //Debug.Log(clt);
                    GameObject go = (GameObject)PrefabUtility.InstantiatePrefab(CUSTOM_TOOL_PREFBA);
                    //Debug.Log(go);

                    //go.transform.localPosition = new Vector3(0, -1000, 0);

                    if (go != null)
                    {
                        //Debug.Log(clt);
                        PrefabUtility.SaveAsPrefabAssetAndConnect(go, filename, InteractionMode.UserAction);
                        CustomLightingTool cltt = go.GetComponent<CustomLightingTool>();
                        cltt.CustomReflectionTexture = null;
                        cltt.CustomReflectionProbe.customBakedTexture = null;
                        cltt.CustomReflectionProbe.bakedTexture = null;
                    }
                    else
                    {

                        EditorUtility.DisplayDialog("CustomLightTools", "灯光不存在请更新下 ", "好的!");

                    }
                    //PrefabUtility.pre

                }
            }



        }




        public static string TOOL_PATH = "Assets/CustomLightingTool/";
        public static UnityEngine.Object CUSTOM_TOOL_PREFBA = null;
        public static UnityEngine.Object HDRI_SKYBOX_MESH = null;
        public static void GetToolPath()
        {
            //string[] sceneGuids = AssetDatabase.FindAssets("t:Script", new string[] { "Assets" });
            //string toolPath = "";
            //for (int i = 0; i < sceneGuids.Length; i++)
            //{
            //    string fname = AssetDatabase.GUIDToAssetPath(sceneGuids[i]);
            //    if (Path.GetFileNameWithoutExtension(fname) == "CustomLightingToolEditor")
            //    {
            //        toolPath = Path.GetDirectoryName(fname);
            //        TOOL_PATH = toolPath.Replace("\\", "/");// +"/../";
            //        TOOL_PATH = TOOL_PATH.Substring(0, TOOL_PATH.Length - 6);
            //    }
            //}

            CUSTOM_TOOL_PREFBA = AssetDatabase.LoadAssetAtPath(TOOL_PATH + "CustomLighting.prefab", typeof(UnityEngine.Object));
            HDRI_SKYBOX_MESH = AssetDatabase.LoadAssetAtPath(TOOL_PATH + "Res/skybox.fbx", typeof(UnityEngine.Object));


            if (CUSTOM_TOOL_PREFBA == null)
            {
                Debug.LogError("CustomLighting.prefab 找不到！ | " + TOOL_PATH + "CustomLighting.prefab");

            }
        }


        public static string PasteResourceFilename = "";


        public static string VERSION = "1.4";
        public static string VERSION_TIME = "2022-04_6";
        public static string VERSION_CUREENT_LOG = "1.4 |1.修复cube数量太多问题 2.添加lookdev 参考球 3.添加RGBM支持";
        public static string VERSION_LAST_LOG = "1.0 |1.： 更新了Bake和reflectionprobe 存储问题| \n 1.2： 修复了多数bug  \n 1.3： 1.添加模板功能。 2.禁用prefab模式下编辑。 3.循环cube引用 报警";


        public static string CUBEMAPNAME = "ReflectionProbe-Custom";

        private CustomLightingTool ligthSetting { get { return target as CustomLightingTool; } }
        private GameObject targetObj = null;
        static bool LightprobeTogle = true;
        static bool playerListTogle = true;

        static bool ShowRimLightGizom = false;
        static bool ShowRimLightGizom1 = false;
        static bool ShowRimLightGizom2 = false;
        static bool RimProDebug = false;
        static bool _DebugRimPro = false;


        static bool RIM_PRO = false;
        static bool RIM_PRO1 = false;
        static bool RIM_PRO2 = false;
        static Vector3 RIM_DIR1 = Vector3.zero;
        static Vector3 RIM_DIR2 = Vector3.zero;
        static Color RIM_COL1 = Color.black;
        static Color RIM_COL2 = Color.black;
        static Vector4 RIM_MASK_CLAMP = new Vector4(0.6f, 0.8f, 1, 1);
        static Vector4 RIM_SHAPE = new Vector4(0.6f, 0.8f, 1, 1);
        static bool RIM_Add = false;


        static int ControlRimProDirIndex = 0;

        static bool ShowAmbientLightGizom = false;
        static bool ShowRefLightGizom = false;
        static bool ShowElseLightGizom = true;
        static public float showDirLightGizomRate = 1.64f;
        int selectionAmbientLitIndex = 0;
        int selectionRefLitIndex = 0;



        public static bool falseColor = false;
        public static float falseColorExposure = 0;

        //HDRI lightProbe sphere
        static RenderTexture HDRI_RT;
        static RenderTexture HDRI_RT1;
        static RenderTexture HDRI_RT2;
        // static RenderTexture HDRISH_RT;

        static bool HDIRShow=true;


        ColorPickerHDRConfig cpc = new ColorPickerHDRConfig(0, 1000, -50, 50);


        //public static string ReflectionLightRootName = "RelectionLights_BuYaoQiYiYangDeMingZI";
        public static string ReflectionLightRootName = "CustomLightingTool 镜面反射编辑的临时文件 请忽略！";

        public float minExposure = -15;
        public float maxExposure = 15;

        void OnEnable()
        {
            HDRI_RT=new RenderTexture(1024,512,16,RenderTextureFormat.ARGBHalf);
            HDRI_RT1=new RenderTexture(256,256,16,RenderTextureFormat.ARGBHalf);
            HDRI_RT2=new RenderTexture(256,256,16,RenderTextureFormat.ARGBHalf);

     
            GetToolPath();

            if (PlayerSettings.colorSpace == UnityEngine.ColorSpace.Gamma)
            {
                minExposure = -5;
                maxExposure = 5;
            }

            RefreshRefProbeVisualTex();

        }

        //void OnDisable()
        //{
        //    EditorApplication.update -= inspectorUpdate;
        //}

        //// 修改属性后 刷新 reflection 预览图
        public void RefreshRefProbeVisualTex()
        {
            //Debug.Log("RefreshRefProbeVisualTex up");
            if (ligthSetting.CubemapToEquirectangularMipmapMat)
            {
                RenderTexture texRealtime = ligthSetting.CustomReflectionProbe.realtimeTexture;
                RenderTexture temprt = RenderTexture.active;
                RenderTexture.active = ligthSetting.ReflectionTexVisual;
                ligthSetting.CubemapToEquirectangularMipmapMat.SetFloat("_LodNum", ligthSetting.ReflectionTexVisualMip);
                ligthSetting.CubemapToEquirectangularMipmapMat.DisableKeyword("_FLIP_Y_FOR_EXR");
                ligthSetting.CubemapToEquirectangularMipmapMat.EnableKeyword("_GET_MIPMAP");

                if (ligthSetting.onEditorRef && texRealtime != null)
                {
                    Graphics.Blit(texRealtime, ligthSetting.ReflectionTexVisual, ligthSetting.CubemapToEquirectangularMipmapMat);
                }
                else
                {
                    Graphics.Blit(ligthSetting.CustomReflectionProbe.bakedTexture, ligthSetting.ReflectionTexVisual, ligthSetting.CubemapToEquirectangularMipmapMat);
                }
                RenderTexture.active = temprt;

                //repainter
                EditorUtility.SetDirty(this);
                Repaint();
            }
        }


        public void UpdateReflectionLightObject()
        {

            if (ligthSetting.lightParent == null)
            {
                GameObject go = GameObject.Find(ReflectionLightRootName);
                DestroyImmediate(go);

                ligthSetting.lightParent = new GameObject(ReflectionLightRootName).transform;
                // ligthSetting.lightParent.gameObject.hideFlags = HideFlags.NotEditable;
                ligthSetting.lightParent.gameObject.hideFlags = HideFlags.HideAndDontSave;
                ligthSetting.lightParent.position = ligthSetting.CustomReflectionProbe.transform.position;
            }



            foreach (var item in ligthSetting.reflectionLights)
            {
                if (item.goParent == null)
                {
                    item.CreateLight(ligthSetting.lightParent, item.tex);
                }
            }

            GameObject gs = GameObject.Instantiate(HDRI_SKYBOX_MESH, ligthSetting.lightParent) as GameObject;
            gs.hideFlags = HideFlags.HideAndDontSave;
            gs.gameObject.hideFlags = HideFlags.HideAndDontSave;
            gs.transform.localScale = new Vector3(35, 35, 35);
            gs.transform.localPosition = Vector3.zero;
            gs.transform.localRotation = Quaternion.identity;
            gs.layer = 31;
            //ligthSetting.SkyboxBase = gs.transform;

            MeshRenderer mr = gs.GetComponent<MeshRenderer>();
            mr.sharedMaterial = ligthSetting.SkyboxMate;

        }

        public string probeModeTipStr = "";

        Rect scale = new Rect(0, 0, 100, 500);



        public void SaveMe()
        {

            if (ligthSetting.tabLightMode == 1)
            {

                if (!ligthSetting.onEditorRef)
                {
                    EditorUtility.DisplayDialog("CustomLightTools", "保存失败！ 请在镜面反射编辑模式 刷新下试试", "好的!");

                    //UpdateReflectionLightObject()

                }
                else
                {

                    BakeRealtimeProbe();
                    if (ligthSetting.onEditorRef && ligthSetting.RefreshRealtimeSH && ligthSetting.refreshRefProbe)
                    {
                        UpdateRealtimeSH();
                    }
                }

            }
            //else { 
            //}
            ligthSetting.SetRefTextrue();
            ligthSetting.onEditorRef = false;
            ligthSetting.CustomReflectionProbe.mode = ReflectionProbeMode.Custom;
            ligthSetting.CustomReflectionProbe.customBakedTexture = ligthSetting.CustomReflectionTexture;

            if (ligthSetting.lightParent != null)
            {
                DestroyImmediate(ligthSetting.lightParent.gameObject);

            }

            PrefabUtility.ApplyPrefabInstance(ligthSetting.gameObject, InteractionMode.AutomatedAction);
            RefreshRefProbeVisualTex();
            ligthSetting.refreshRefProbe = true;
            ligthSetting.onEditorRef = false;
            Repaint();

        }


        //public override void OnInspectorGUI()
        //{
        //    base.OnInspectorGUI();


        // 模板功能
        public static string SD_CUSTOMLIGHT_LIST = "CustomLight_tempList";
        public static string SD_SPLIT = "|";



        //struct TempLit
        //{
        //    public string name;
        //    public string sceneName ;
        //    public string prefabName ;
        //    public string cubeName;
        //    public Object obj ;
        //    public Texture2D tex;
        //    public Cubemap cp ;
        //    public Object scene ;

        //    public string ToSaveStr()
        //    {
        //        string str = "";

        //        str = name + SD_SPLIT + prefabName + SD_SPLIT + sceneName + SD_SPLIT + cubeName;

        //        return str;
        //    }
        //}


        public class TempLit
        {
            public string name = "";
            public string sceneName = "";
            public string prefabName = "";
            public string cubeName = "";
            public Object obj = null;
            public Texture2D tex = null;
            public Cubemap cp = null;
            public Object scene = null;

            public string ToSaveStr()
            {
                string str = "";

                str = name + SD_SPLIT + prefabName + SD_SPLIT + sceneName + SD_SPLIT + cubeName;

                return str;
            }

        }
        public static Dictionary<string, TempLit> tempList = new Dictionary<string, TempLit>();
        public static void saveTempList()
        {

            string tempListStr = "";
            foreach (var item in tempList.Keys)
            {
                tempListStr += item + "|" + tempList[item].ToSaveStr() + "\"";
            }
            EditorPrefs.SetString(SD_CUSTOMLIGHT_LIST, tempListStr);
        }


        public static void LoadTempList()
        {

            string tempListStr = EditorPrefs.GetString(SD_CUSTOMLIGHT_LIST);

            if (tempListStr != "")
            {
                string[] ts = tempListStr.Split('"');
                tempList = new Dictionary<string, TempLit>();
                foreach (var item in ts)
                {
                    string[] ns = item.Split('|');
                    string st = "";
                    foreach (var x in ns)
                    {
                        st += "|||" + x;
                    }
                    if (ns.Length == 5)
                    {

                        if (File.Exists(ns[2]) && File.Exists(ns[3]))//&& File.Exists(ns[4]))
                        {
                            TempLit tl = new TempLit();
                            tl.name = ns[1];
                            tl.prefabName = ns[2];
                            tl.sceneName = ns[3];
                            tl.cubeName = ns[4];
                            tempList[tl.prefabName] = tl;
                            tl.obj = AssetDatabase.LoadAssetAtPath<Object>(tl.prefabName);
                            tl.cp = AssetDatabase.LoadAssetAtPath<Cubemap>(tl.cubeName);
                            tl.tex = AssetPreview.GetAssetPreview(tl.cp);
                            tl.scene = AssetDatabase.LoadAssetAtPath<Object>(tl.sceneName);
                        }
                        else
                        {
                            //Debug.Log( ns[1] );
                            //Debug.Log( File.Exists(ns[1]) );
                            //Debug.Log( File.Exists(ns[2]) );
                            //Debug.Log( File.Exists(ns[3]) );
                        }
                        //Debug.Log("list:" + ns[0] + "|" + ns[1]);
                    }
                    else
                    {
                        //Debug.Log(ns.Length);

                    }
                }
            }


        }

        public static void AddTempList(string name, string pname, string sname, string cname)
        {
            LoadTempList();
            TempLit tl = new TempLit();
            tl.name = name;
            tl.prefabName = pname;
            tl.sceneName = sname;
            tl.cubeName = cname;
            tempList[tl.prefabName] = tl;
            tl.obj = AssetDatabase.LoadAssetAtPath<Object>(tl.prefabName);
            tl.cp = AssetDatabase.LoadAssetAtPath<Cubemap>(tl.cubeName);
            tl.tex = AssetPreview.GetAssetPreview(tl.cp);
            tl.scene = AssetDatabase.LoadAssetAtPath<Object>(tl.sceneName);
            saveTempList();
        }
        // 模板功能

        public static bool TempMode = false;
        public bool cubemapError = false;

        public override void OnInspectorGUI()
        {
            //Get inspect Rect
            EditorGUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace();
            EditorGUILayout.EndHorizontal();


            if (Event.current.type == EventType.Repaint && ligthSetting.ReflectionTexVisual != null)
            {

                scale = GUILayoutUtility.GetLastRect();
                float h = scale.width / (ligthSetting.ReflectionTexVisual.width * 1f / ligthSetting.ReflectionTexVisual.height);
                h = Mathf.Min(256, h);
                scale.height = h;
            }






            //缺少资源文件
            if (TOOL_PATH == "" && CUSTOM_TOOL_PREFBA == null)
            {
                EditorGUILayout.HelpBox("找不到资源！", MessageType.Warning);
                return;
            }


            //禁止 选中 prefab文件时候的编辑
            if (ligthSetting.SkyboxMate == null)
            {
                GUI.color = Color.red;
                int fontSize = GUI.skin.label.fontSize;
                GUI.skin.label.fontSize = 60;
                GUILayout.Label("只能编辑场景中的光照!");
                GUI.skin.label.fontSize = fontSize;
                //EditorGUILayout.HelpBox("只能拿编辑场景中的光照！", MessageType.Warning);
                return;
            }


            //提示Debug模式
            if (ligthSetting.DebugMode)
            {
                GUI.color = Color.red;
                int fontSize = GUI.skin.label.fontSize;
                GUI.skin.label.fontSize = 60;
                GUILayout.Label("危险操作! 危险操作！");
                GUILayout.Label("后果严重！后果严重！");
                GUILayout.Label("Debug 模式警告! ");
                GUI.skin.label.fontSize = fontSize;
                //EditorGUILayout.HelpBox("只能拿编辑场景中的光照！", MessageType.Warning);
            }



            GUILayout.BeginVertical(GUI.skin.box);
            //使用了循环cube 错误提示！
            //string tempstr = "";
            //if (cubemapError)
            //{
            //    GUI.color = Color.red;
            //    //int fontSize = GUI.skin.label.fontSize;
            //    //GUI.skin.label.fontSize = 20;
            //    //GUILayout.BeginHorizontal();
            //    //GUILayout.Label("错误Cubmap！");//,GUILayout.MaxWidth(300));
            //    //GUILayout.Label("请更改！",GUILayout.MaxWidth(300));
            //    //GUILayout.EndHorizontal();
            //    //EditorGUILayout.HelpBox("错误Cubmap!不能使用其他场景的cubemap", MessageType.Error);
            //    //GUILayout.Label("请更改！", GUILayout.MaxWidth(450));
            //    //GUI.skin.label.fontSize = 20;
            //    //GUILayout.Label("不能使用其他场景的cubemap", GUILayout.MaxWidth(350)) ;

            //    //GUILayout.Button("错误Cubmap!不能使用其他场景的cubemap");
            //    tempstr = "错误Cubmap!不能使用其他场景的cubemap";
            //    GUILayout.Label(tempstr);

            //    //GUI.skin.label.fontSize = fontSize;
            //    GUI.color = Color.white;

            //}
            //else { 
            //    tempstr = "错误Cubmap!不能使用其他场景的cubemap";
            //    GUILayout.Label(tempstr);

            //}
            GUILayout.Label("版本号" + VERSION + "   更新时间：" + VERSION_TIME);

            GUILayout.EndVertical();







            // GUILayout.Label("版本更新内容："+VERSION_LAST_LOG);

            if (ligthSetting.DebugMode)
            {
                GUI.color = Color.white;
            }
            GUILayout.BeginHorizontal(GUI.skin.box);



            GUIContent content = new GUIContent();
            //content.image = EditorGUIUtility.IconContent("SaveActive").image;

            if (TempMode)
            {
                GUI.enabled = false;
            }


            if (ligthSetting.tabLightMode == 1)
            {
                if (!ligthSetting.onEditorRef)
                {
                    GUI.enabled = false;
                    content.text = "开启编辑模式 在保存！";
                    GUILayout.Button(content, GUI.skin.button, GUILayout.Width(150), GUILayout.Height(30));
                    GUI.enabled = true;
                }
                else
                {

                    content.text = "保存并且 Bake Cubemap";
                    if (GUILayout.Button(content, GUI.skin.button, GUILayout.Width(150), GUILayout.Height(30)))
                    {

                        SaveMe();
                    }
                }

            }
            else
            {

                if (GUILayout.Button("保存一下 预制体", GUI.skin.button, GUILayout.Width(150), GUILayout.Height(30)))
                {

                    SaveMe();
                }
            }


            content.image = EditorGUIUtility.IconContent("_Help").image;
            content.text = "帮助";
            if (GUILayout.Button(content, GUI.skin.button, GUILayout.Height(30)))
            {
                Application.OpenURL("https://iwiki.woa.com/pages/viewpage.action?pageId=965109441");
            }
            if (!TempMode)
            {
                GUI.enabled = true;

            }
            if (ligthSetting.DebugMode)
            {

                content.image = EditorGUIUtility.IconContent("redLight").image;
            }
            else
            {
                content.image = null;//EditorGUIUtility.IconContent("orangeLight").image;
            }
            content.text = "debug";

            ligthSetting.DebugMode = GUILayout.Toggle(ligthSetting.DebugMode, content, GUI.skin.button, GUILayout.Height(30));
            if (ligthSetting.DebugMode)
            {
                ligthSetting.ExpertMode = GUILayout.Toggle(ligthSetting.ExpertMode, "高级模式", GUI.skin.button, GUILayout.Height(30));

            }





            // 收藏按钮
            content.image = EditorGUIUtility.IconContent("Favorite Icon").image;
            content.text = "收藏";
            PasteResourceFilename = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(ligthSetting.gameObject);
            bool isFav = false;
            foreach (var item in tempList.Keys)
            {
                if (item == PasteResourceFilename)
                {
                    isFav = true;
                }
            }
            if (isFav)
            {
                GUI.enabled = false;
                content.text = "已收藏";

            }




            if (GUILayout.Button(content, GUILayout.Height(30)))
            {

                //}
                //if (GUILayout.Button("设置此为模板", GUI.skin.button, GUILayout.Height(30)))
                //{
                Scene sc = SceneManager.GetActiveScene();//.name;

                if (sc != null)
                {
                    if (File.Exists(sc.path))
                    {
                        //Debug.Log(AssetDatabase.GetAssetPath(ligthSetting.CustomReflectionTexture));
                        AddTempList(sc.name, PasteResourceFilename, sc.path, AssetDatabase.GetAssetPath(ligthSetting.CustomReflectionTexture));
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("CustomLightTools", "请先存下场景！", "好的!");
                        Debug.Log(sc.path);
                    }
                }
            }

            GUI.enabled = true;
            if (TempMode)
            {
                GUI.color = Color.red;
            }


            content.image = EditorGUIUtility.IconContent("FolderFavorite Icon").image;
            content.text = "载入模板";

            EditorGUI.BeginChangeCheck();
            TempMode = GUILayout.Toggle(TempMode, content, GUI.skin.button, GUILayout.Height(30));
            if (EditorGUI.EndChangeCheck())
            {
                LoadTempList();
            }


            GUILayout.EndHorizontal();

            bool isDeletTemp = false;
            string isDeletTempKey = "";
            //光照模板管理
            if (TempMode)
            {

                EditorGUILayout.BeginVertical(GUI.skin.box);
                //EditorPrefs.SetString()
                GUI.color = Color.white;


                if (tempList.Keys.Count == 0)
                {

                    if (GUILayout.Button("设置此为模板", GUI.skin.button, GUILayout.Height(30)))
                    {
                        PasteResourceFilename = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(ligthSetting.gameObject);
                        Scene sc = SceneManager.GetActiveScene();//.name;

                        if (sc != null)
                        {
                            if (File.Exists(sc.path))
                            {
                                //Debug.Log(AssetDatabase.GetAssetPath(ligthSetting.CustomReflectionTexture));
                                AddTempList(sc.name, PasteResourceFilename, sc.path, AssetDatabase.GetAssetPath(ligthSetting.CustomReflectionTexture));
                            }
                            else
                            {
                                EditorUtility.DisplayDialog("CustomLightTools", "请先存下场景！", "好的!");
                                Debug.Log(sc.path);
                            }
                        }
                    }

                    EditorGUILayout.HelpBox("请先添加模板！", MessageType.Warning);

                }

                //GameObject.find
                int i = 0;
                foreach (var item in tempList.Keys)
                {

                    TempLit tl = tempList[item];
                    PasteResourceFilename = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(ligthSetting.gameObject);

                    GUILayout.Label(tempList[item].sceneName);
                    GUILayout.BeginHorizontal(GUI.skin.box);

                    //GUILayout.BeginVertical();
                    if (GUILayout.Button(tl.tex, GUI.skin.box, GUILayout.Height(60), GUILayout.Width(60)))
                    {
                        EditorGUIUtility.PingObject(tl.cp);
                    }
                    if (tl.prefabName == PasteResourceFilename)
                    {
                        GUI.enabled = false;
                    }
                    //GUILayout.EndVertical();



                    GUILayout.BeginVertical();
                    GUILayout.Label(tl.name, (GUIStyle)"AssetLabel");
                    GUI.enabled = false;
                    EditorGUILayout.ObjectField(tl.obj, typeof(Object));
                    //GUILayout.Label(tempList[item].prefabName);

                    //EditorGUILayout.ObjectField("cubemap：",tl.cp,typeof(Cubemap));
                    EditorGUILayout.ObjectField(tl.scene, typeof(Object));

                    //GUILayout.Label(tempList[item].sceneName);
                    GUI.enabled = true;
                    if (tl.prefabName == PasteResourceFilename)
                    {
                        GUI.enabled = false;
                    }
                    GUILayout.EndVertical();



                    if (GUILayout.Button("载入参数", GUILayout.Height(60)))
                    {
                        //Debug.Log("ssss");
                        //Debug.Log(EditorPrefs.GetString(SD_CUSTOMLIGHT_LIST));
                        GameObject go = (GameObject)PrefabUtility.InstantiatePrefab(tl.obj);
                        CustomLightingTool ctlp = go.GetComponent<CustomLightingTool>();
                        if (ctlp != null)
                        {
                            ligthSetting.CopyLight(ctlp);
                            //复制参数功能
                        }
                        TempMode = false;

                        //刷新参数生成
                        ligthSetting.onEditorRef = true;
                        ShowRefLightGizom = true;
                        ligthSetting.CustomReflectionProbe.mode = ReflectionProbeMode.Realtime;
                        ligthSetting.CustomReflectionProbe.RenderProbe();
                        ligthSetting.refreshRefProbe = true;
                        ShowRefLightGizom = true;
                        UpdateReflectionLightObject();

                        if (ligthSetting.RefreshRealtimeSH)
                        {
                            UpdateRealtimeSH();
                            //Debug.Log("UpdateRealtimeSH Bake");

                        }




                        if (ligthSetting.lightParent != null)
                        {
                            DestroyImmediate(ligthSetting.lightParent.gameObject);

                        }
                        UpdateReflectionLightObject();
                        RefreshRefProbeVisualTex();

                        DestroyImmediate(go);

                    }
                    if (tl.prefabName == PasteResourceFilename)
                    {
                        GUI.enabled = true;
                    }
                    if (GUILayout.Button("X", GUILayout.Height(60)))
                    {
                        isDeletTemp = true;
                        isDeletTempKey = item;
                    }
                    GUILayout.EndHorizontal();

                    GUILayout.Space(10);
                    i++;
                    GUI.enabled = true;

                }
                //if (GUILayout.Button("assss"))
                //{
                //    Debug.Log("ssss");
                //    Debug.Log(EditorPrefs.GetString(SD_CUSTOMLIGHT_LIST));

                //}
                //if (GUILayout.Button("xxxxx"))
                //{
                //    Debug.Log(tempList.Keys.Count);

                //}


                EditorGUILayout.EndVertical();


                if (isDeletTemp)
                {
                    tempList.Remove(isDeletTempKey);
                    saveTempList();
                }
                return;
            }








            targetObj = ligthSetting.gameObject;

            playerListTogle = true;
            GUI.skin.button.fontSize = 17;
            EditorGUILayout.BeginHorizontal(EditorStyles.helpBox);
            GUILayout.Label("自定义光照物件列表", GUI.skin.button);
            EditorGUILayout.EndHorizontal();
            GUI.skin.button.fontSize = 0;

            if (playerListTogle == true)
            {
                EditorGUILayout.HelpBox("添加对象到列表接受自定义光照!", MessageType.Info);

                EditorGUILayout.BeginVertical();
                int delIndex = -1;
                bool delState = false;
                int litCount = 0;
                foreach (var item in ligthSetting.CustomLightObjs)
                {
                    EditorGUILayout.BeginHorizontal();
                    EditorGUI.BeginChangeCheck();
                    GameObject go = (GameObject)EditorGUILayout.ObjectField(item, typeof(GameObject));
                    if (EditorGUI.EndChangeCheck())
                    {
                        Undo.RegisterCompleteObjectUndo(ligthSetting, "CustomLightObjs change" + litCount.ToString());
                        ligthSetting.CustomLightObjs[litCount] = go;
                    }

                    if (GUILayout.Button("x", GUILayout.Width(30)))
                    {
                        delIndex = litCount;
                        delState = true;
                        Undo.RegisterCompleteObjectUndo(ligthSetting, "CustomLightObjs delet" + litCount.ToString());

                    }
                    litCount += 1;
                    EditorGUILayout.EndHorizontal();

                }
                EditorGUILayout.BeginHorizontal();
                float h = 30;
                if (GUILayout.Button("+", GUILayout.Height(h)))
                {
                    Undo.RegisterCompleteObjectUndo(ligthSetting, "CustomLightObjs add " + ligthSetting.CustomLightObjs.Length.ToString());
                    GameObject[] tempObjs = new GameObject[ligthSetting.CustomLightObjs.Length + 1];
                    int tempI = 0;
                    foreach (var item in ligthSetting.CustomLightObjs)
                    {

                        tempObjs[tempI] = ligthSetting.CustomLightObjs[tempI];
                        tempI += 1;
                    }
                    ligthSetting.CustomLightObjs = tempObjs;

                }

                if (GUILayout.Button("添加所选择物件", GUILayout.Height(h)))
                {
                    if (Selection.objects.Length != 0)
                    {
                        List<GameObject> objs = new List<GameObject>();
                        foreach (var item in Selection.objects)
                        {
                            if (item != ligthSetting.gameObject)
                            {
                                if (item.GetType() == typeof(GameObject))
                                {
                                    bool isExist = false;
                                    foreach (var l in ligthSetting.CustomLightObjs)
                                    {
                                        if (l == item)
                                        {
                                            isExist = true;
                                        }
                                    }
                                    if (!isExist)
                                    {
                                        objs.Add(item as GameObject);
                                    }
                                }
                            }
                        }
                        if (objs.Count > 0)
                        {
                            Undo.RegisterCompleteObjectUndo(ligthSetting, "CustomLightObjs add select " + ligthSetting.CustomLightObjs.Length.ToString());
                            GameObject[] tempObjs = new GameObject[ligthSetting.CustomLightObjs.Length + objs.Count];
                            for (int i = 0; i < tempObjs.Length; i++)
                            {
                                if (i > ligthSetting.CustomLightObjs.Length - 1)
                                {
                                    tempObjs[i] = objs[Mathf.Max(0, i - ligthSetting.CustomLightObjs.Length)];
                                }
                                else
                                {
                                    tempObjs[i] = ligthSetting.CustomLightObjs[i];
                                }
                            }
                            ligthSetting.CustomLightObjs = tempObjs;
                        }
                    }
                }

                if (GUILayout.Button("重置选择光照", GUILayout.Height(h)))
                {
                    if (Selection.objects.Length != 0)
                    {
                        foreach (var item in Selection.objects)
                        {
                            if (item != ligthSetting.gameObject)
                            {
                                if (item.GetType() == typeof(GameObject))
                                {
                                    bool isExist = false;
                                    foreach (var l in ligthSetting.CustomLightObjs)
                                    {
                                        if (l == item)
                                        {
                                            isExist = true;
                                        }
                                    }
                                    if (!isExist)
                                    {
                                        //set delete obj to normal light 
                                        foreach (var go in ((GameObject)item).GetComponentsInChildren<Renderer>())
                                        {
                                            go.probeAnchor = null;
                                            go.lightProbeUsage = LightProbeUsage.BlendProbes;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                EditorGUILayout.EndHorizontal();

                //delete object
                if (delState)
                {
                    if (delIndex > -1 && delIndex < ligthSetting.CustomLightObjs.Length + 1)
                    {
                        GameObject[] tempObjs = new GameObject[ligthSetting.CustomLightObjs.Length - 1];
                        int tempI = 0;
                        //set delete obj to normal light 
                        if (ligthSetting.CustomLightObjs[delIndex] != null)
                        {
                            foreach (var item in ligthSetting.CustomLightObjs[delIndex].GetComponentsInChildren<Renderer>())
                            {
                                item.probeAnchor = null;
                                item.lightProbeUsage = LightProbeUsage.BlendProbes;
                            }
                        }
                        for (int i = 0; i < tempObjs.Length; i++)
                        {
                            if (i != delIndex)
                            {
                                tempObjs[i] = ligthSetting.CustomLightObjs[tempI];
                            }
                            else
                            {
                                //skip next  delete obj
                                tempI += 1;
                                tempObjs[i] = ligthSetting.CustomLightObjs[tempI];
                            }
                            tempI += 1;
                        }

                        ligthSetting.CustomLightObjs = tempObjs;
                    }
                    delState = false;
                }


                EditorGUILayout.EndVertical();

            }

#if UNITY_2019_1_OR_NEWER
            //EditorGUILayout.EndFoldoutHeaderGroup();
#endif



            //if (true)
            //{
            //    if (GUILayout.Button("xx"))
            //    {
            //        Debug.Log(UnityEditor.AssetDatabase.IsNativeAsset(target));
            //    }
            //    //return;

            //}

            if (EditorApplication.isPlaying)
            {
                EditorGUILayout.HelpBox("运行时不能编辑！", MessageType.Warning);

                return;
            }

            EditorGUILayout.Space();
            EditorGUILayout.Space();



            GUI.skin.button.fontSize = 17;
            EditorGUILayout.BeginHorizontal(EditorStyles.helpBox);
            GUILayout.Label("间接光设置", GUI.skin.button);
            EditorGUILayout.EndHorizontal();

            // GUILayout.Button(HDRI_RT,GUI.skin.label,GUILayout.Height(128));
           EditorStyles.toolbarButton.fixedHeight=150;

            ligthSetting.tabLightMode = GUILayout.Toolbar(ligthSetting.tabLightMode, new Texture[] { (Texture)HDRI_RT1,(Texture)HDRI_RT2  }, GUI.skin.button);
           EditorStyles.toolbarButton.fixedHeight=20;


            GUI.skin.button.fontSize = 0;

            GUIContent[] contents = new GUIContent[2];
            contents[0] = new GUIContent();
            contents[1] = new GUIContent();
            // contents[0].image = EditorGUIUtility.FindTexture("LightProbeGroup Gizmo");
            //contents[0].image = HDRI_RT;
            
            contents[0].text = "漫反射";

            // contents[1].image = EditorGUIUtility.FindTexture("ReflectionProbe Gizmo");
            contents[1].text = "镜面反射";

            //EditorStyles.toolbarButton.fixedHeight = 45;
            float tempSize = EditorStyles.toolbarButton.fixedHeight;
            int tempfSize = EditorStyles.toolbarButton.fontSize;
            EditorStyles.toolbarButton.fixedHeight = 25;
            EditorStyles.toolbarButton.fontSize = 20;

            //ligthSetting.tabLightMode = GUILayout.Toolbar(ligthSetting.tabLightMode, new Texture2D[] { EditorGUIUtility.FindTexture("LightProbeGroup Gizmo"), EditorGUIUtility.FindTexture("ReflectionProbe Gizmo") }, EditorStyles.toolbarButton);
            ligthSetting.tabLightMode = GUILayout.Toolbar(ligthSetting.tabLightMode, contents, EditorStyles.toolbarButton);
            EditorStyles.toolbarButton.fixedHeight = tempSize;
            EditorStyles.toolbarButton.fontSize = tempfSize;
            //ligthSetting.tabLightMode = GUILayout.Toolbar(ligthSetting.tabLightMode, new string[] { "漫反射", "镜面反射" }, EditorStyles.toolbarButton);
            //EditorStyles.toolbarButton.fontSize = 0;
            //EditorStyles.toolbarButton.fixedHeight = 20;



            EditorGUILayout.BeginVertical();

            if (ligthSetting.tabLightMode == 0)
            {
                ShowRefLightGizom = false;

                //GUILayout.Box(texs.lightProbeIcon, GUILayout.Width(30), GUILayout.Height(30));
#if UNITY_2019_1_OR_NEWER
                // LightprobeTogle = EditorGUILayout.BeginFoldoutHeaderGroup(LightprobeTogle, "自定义 漫反射 (环境光) Diffuse Light");

#else
                // GUILayout.Label("自定义 漫反射 (环境光) Diffuse Light");
#endif
                EditorGUILayout.BeginHorizontal(EditorStyles.helpBox);// GUI.skin.box);
                LightprobeTogle = GUILayout.Toggle(LightprobeTogle, "", GUILayout.Width(35));
                GUILayout.Label("自定义 漫反射 (环境光) Diffuse Light", EditorStyles.boldLabel);


                //GUILayout.Label(" ", EditorStyles.boldLabel);
                if (ligthSetting.tab == (int)CustomLightingTool.ESHMode.CustomProbe)
                {
                    ShowAmbientLightGizom = GUILayout.Toggle(ShowAmbientLightGizom, "显示灯光方向", GUI.skin.button);
                    if (ShowAmbientLightGizom)
                    {
                        ShowRefLightGizom = false;
                    }

                }
                else
                {
                    ShowAmbientLightGizom = false;
                }


                EditorGUILayout.EndHorizontal();
                if (ligthSetting.LightProbeMode != CustomLightingTool.ESHMode.LightProbe && ligthSetting.ExpertMode)
                {
                    GUILayout.BeginHorizontal();

                    EditorGUI.BeginChangeCheck();
                    float ligthSettingInstensity = EditorGUILayout.Slider("强度Intensity: ", ligthSetting.Instensity, 0, 3);
                    if (GUILayout.Button("0", GUILayout.Width(40)))
                    {
                        ligthSettingInstensity = 0;
                    }
                    if (GUILayout.Button("1", GUILayout.Width(40)))
                    {
                        ligthSettingInstensity = 1;

                    }
                    if (EditorGUI.EndChangeCheck())
                    {
                        Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting.lightprobe Instensity");
                        //Debug.Log("ligthSetting.lightprobe Instensity");

                        ligthSetting.Instensity = ligthSettingInstensity;
                    }
                    GUILayout.EndHorizontal();

                }


                //LightprobeTogle = GUILayout.Toggle(LightprobeTogle, "ON", GUI.skin.button);

                if (LightprobeTogle)
                {


                    EditorGUILayout.BeginVertical(GUI.skin.box);


                    if (ligthSetting.ExpertMode)
                    {
                        EditorStyles.toolbarButton.fontSize = 14;
                        EditorStyles.toolbarButton.fixedHeight = 23;
                        ligthSetting.tab = GUILayout.Toolbar(ligthSetting.tab, new string[] { "自定义编辑", "Cubemap生成", "使用环境光", "灯光探针", }, (GUIStyle)"toolbarbutton");
                        EditorStyles.toolbarButton.fixedHeight = 20;
                        EditorStyles.toolbarButton.fontSize = 0;
                    }
                    else
                    {
                        GUILayout.BeginHorizontal();
                        GUI.enabled = false;
                        GUILayout.Button("自定义编辑");
                        GUI.enabled = true;
                        GUILayout.Button("Cubemap生成");
                        GUI.enabled = false;
                        GUILayout.Button("使用环境光");
                        GUILayout.Button("灯光探针");
                        GUI.enabled = true;
                        GUILayout.EndHorizontal();
                    }



                    switch (ligthSetting.tab)
                    {
                        case (int)CustomLightingTool.ESHMode.CustomProbe:
                            probeModeTipStr = "使用自定义Diffuse光照";
                            ligthSetting.LightProbeMode = CustomLightingTool.ESHMode.CustomProbe;
                            break;
                        case (int)CustomLightingTool.ESHMode.AmbientProbe:
                            probeModeTipStr = "使用场景默认Skybo Diffuse光照";
                            ligthSetting.LightProbeMode = CustomLightingTool.ESHMode.AmbientProbe;
                            break;
                        case (int)CustomLightingTool.ESHMode.LightProbe:
                            probeModeTipStr = "使用场景烘焙的 Diffuse光照";
                            ligthSetting.LightProbeMode = CustomLightingTool.ESHMode.LightProbe;
                            break;
                        case (int)CustomLightingTool.ESHMode.CubeMapSH:
                            probeModeTipStr = "使用自定义烘焙Cubemap Diffuse光照";
                            ligthSetting.LightProbeMode = CustomLightingTool.ESHMode.CubeMapSH;
                            break;
                        default:
                            break;
                    }
                    EditorGUILayout.HelpBox(probeModeTipStr, MessageType.Info); // 解锁下给后续设置使用 ———— neozfzheng
                    GUI.color = Color.red;
                    EditorGUILayout.HelpBox("暂时禁用 Difuse 调节功能！ 有问题请联系 TA @fengxzeng", MessageType.Info);
                    GUI.color = Color.white;



                    if (ligthSetting.LightProbeMode == CustomLightingTool.ESHMode.CubeMapSH && ligthSetting.ExpertMode)
                    {

                        GUILayout.BeginVertical();
                        //if (ligthSetting.ExpertMode)
                        //{
                        EditorGUI.BeginChangeCheck();
                        bool UseRealtimeSH = GUILayout.Toggle(ligthSetting.UseRealtimeSH, "根据镜面反射实时生成漫反射");
                        if (EditorGUI.EndChangeCheck())
                        {
                            Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting UseRealtimeSH UseRealtimeSH ");
                            ligthSetting.UseRealtimeSH = UseRealtimeSH;
                        }

                        // }

                        if (ligthSetting.UseRealtimeSH)
                        {
                            GUI.enabled = false;
                        }

                        ligthSetting.SHCubemap = EditorGUILayout.ObjectField("Cubemap", ligthSetting.SHCubemap, typeof(Cubemap)) as Cubemap;

                        if (ligthSetting.DebugMode)
                        {
                            ligthSetting.CustomReflectionCubemap = EditorGUILayout.ObjectField("Cubemap", ligthSetting.CustomReflectionCubemap, typeof(Cubemap)) as Cubemap;

                        }

                        GUILayout.BeginHorizontal();

                        if (GUILayout.Button("获取镜面反射Cubemap", GUILayout.Height(37), GUILayout.Width(200)))
                        {
                            ligthSetting.SHCubemap = ligthSetting.CustomReflectionProbe.customBakedTexture as Cubemap;
                        }
                        if (ligthSetting.DebugMode)
                        {
                            if (GUILayout.Button("获取Realtim Cubemap", GUILayout.Height(37), GUILayout.Width(200)))
                            {
                                ligthSetting.CustomReflectionRT = ligthSetting.CustomReflectionProbe.realtimeTexture;
                            }
                        }

                        if (GUILayout.Button("从Cubmap生成漫反射参数", GUILayout.Height(37)))
                        {
                            if (ligthSetting.SHCubemap != null)
                            {
                                SHEncoding sh = new SHEncoding();
                                AssetUtil.setReadable(ligthSetting.SHCubemap, true);
                                SHUtil.BakeSH(ref sh, ligthSetting.SHCubemap, 0, false);
                                sh.setBuffer();

                                ligthSetting.SHV2 = new Vector4[9];

                                for (int i = 0; i < 9; i++)
                                {
                                    ligthSetting.SH2[0, i] = sh.cBuffer[i].x;
                                    ligthSetting.SH2[1, i] = sh.cBuffer[i].y;
                                    ligthSetting.SH2[2, i] = sh.cBuffer[i].z;

                                    // for unity 2018 way
                                    ligthSetting.SHV2[i] = sh.cBuffer[i];

                                }
                            }
                            else
                            {
                                EditorUtility.DisplayDialog("CustomLightTools", "请先设置好Cubemap在生成漫反射！", "好的!");
                                //Debug.Log("CustomReflectionTexture==null");
                            }
                        }
                        if (ligthSetting.DebugMode)
                        {
                            if (GUILayout.Button("从Cubmap生成漫反射参数", GUILayout.Height(37)))
                            {
                                if (ligthSetting.CustomReflectionCubemap != null)
                                {
                                    GetSHFromeCubemap(ligthSetting.CustomReflectionCubemap);
                                }
                                else
                                {
                                    EditorUtility.DisplayDialog("CustomLightTools", "请先设置好Cubemap在生成漫反射！", "好的!");
                                    //Debug.Log("CustomReflectionTexture==null");
                                }
                            }

                        }

                        GUILayout.EndHorizontal();
                        GUI.enabled = true;

                        GUILayout.EndVertical();


                    }

                    if (ligthSetting.LightProbeMode == CustomLightingTool.ESHMode.CustomProbe)
                    {
                        GUIContent gc = new GUIContent("环境色 Color:");
                        EditorGUI.BeginChangeCheck();
                        Color ligthSettingAmbientColor = EditorGUILayout.ColorField(gc, ligthSetting.AmbientColor, true, true, true, cpc);
                        if (EditorGUI.EndChangeCheck())
                        {
                            Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting AmbientColor");
                            //Debug.Log("ligthSetting AmbientColor" );
                            ligthSetting.AmbientColor = ligthSettingAmbientColor;
                        }// GUILayout.Label("index: "+ ligthSetting.CurrentDirLightIndex.ToString());
                        bool startDel = false;
                        CustomLightingTool.DiffuseLight delLight = null;
                        int count = 0;
                        //ligthSetting.CurrentDirLightIndex = -1;

                        //ShowElseLightGizom = GUILayout.Toggle(ShowElseLightGizom, "显示灯光方向", GUI.skin.button);


                        if (ligthSetting.AmbientLights != null)
                        {
                            foreach (var item in ligthSetting.AmbientLights)
                            {
                                GUILayout.BeginHorizontal();
                                if (item.isEdit)
                                {
                                    GUI.color = Color.green;
                                }
                                GUILayout.BeginVertical(GUI.skin.box);
                                GUI.color = Color.white;

                                //if (GUILayout.Button(texs.directionLight,GUILayout.Width(30), GUILayout.Height(30)))
                                //{

                                //}
                                //EditorGUILayout.ToggleGroupScope
                                //EditorGUI.indentLevel = 1;
                                GUILayout.BeginHorizontal();

                                EditorGUI.BeginChangeCheck();
                                bool itemEnabled = GUILayout.Toggle(item.enabled, "", GUILayout.Width(30));
                                if (EditorGUI.EndChangeCheck())
                                {
                                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting Directionlight on " + count.ToString());
                                    item.enabled = itemEnabled;
                                }

                                EditorStyles.label.fontStyle = FontStyle.Bold;
                                EditorGUILayout.LabelField("灯光 Light " + count.ToString());
                                EditorStyles.label.fontStyle = FontStyle.Normal;

                                //if (GUILayout.Button("删除" ))
                                //{
                                //    startDel = true;
                                //    delLight = item;
                                //    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting item delete " + count.ToString());
                                //    //Debug.Log("ligthSetting item delete " + count.ToString());
                                //}

                                GUILayout.EndHorizontal();

                                GUILayout.BeginHorizontal();
                                //EditorGUI.BeginChangeCheck();
                                //Vector3 itemdirection = EditorGUILayout.Vector3Field("方向 Direction: ", item.direction);
                                //if (EditorGUI.EndChangeCheck())
                                //{
                                //    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting direction " + count.ToString());
                                //    //Debug.Log("ligthSetting direction " + count.ToString());
                                //    item.direction = itemdirection;
                                //}

                                GUI.enabled = false;
                                EditorGUILayout.Vector3Field("方向 Direction: ", item.direction);
                                GUI.enabled = true;
                                EditorGUI.BeginChangeCheck();

                                item.isEdit = GUILayout.Toggle(item.isEdit, "旋转灯光", "Button", GUILayout.Width(60));
                                //bool itemisEdit = GUILayout.Toggle(item.isEdit, "旋转灯光", "Button",GUILayout.Width(60));
                                if (EditorGUI.EndChangeCheck())
                                {
                                    //    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting isEdit  " + count.ToString());
                                    //    Debug.Log("ligthSetting isEdit " + count.ToString());
                                    //    item.isEdit = itemisEdit;
                                    if (item.isEdit)
                                    {
                                        // CurrentDirLight = item;
                                        ligthSetting.CurrentDirLightIndex = count;
                                        selectionAmbientLitIndex = 0;
                                        Selection.activeObject = targetObj;
                                        //Debug.Log("isEdit: " + count.ToString() + "ligthSetting.CurrentDirLightIndex: "+ ligthSetting.CurrentDirLightIndex.ToString());
                                        // InitSenceGUI();
                                        for (int i = 0; i < ligthSetting.AmbientLights.Count; i++)
                                        {
                                            if (ligthSetting.CurrentDirLightIndex != i)
                                            {
                                                ligthSetting.AmbientLights[i].isEdit = false;
                                            }
                                        }

                                    }
                                    else
                                    {
                                        //if (ligthSetting.CurrentDirLightIndex == count)
                                        //{
                                        //    ligthSetting.CurrentDirLightIndex = -1;
                                        //    CurrentDirLight = null;

                                        //}
                                        //ClearSenceGUI();
                                        //Debug.Log("ligthSetting.CurrentDirLightIndex = -1;");
                                        ligthSetting.CurrentDirLightIndex = -1;
                                        ///Debug.Log("de isEdit: " + count.ToString());

                                    }
                                }
                                GUILayout.EndHorizontal();



                                //COlor
                                GUILayout.BeginHorizontal();

                                if (item.useColorTemperature)
                                {

                                    //var rect = EditorGUILayout.GetControlRect();
                                    //rect = GUILayoutUtility.GetLastRect();
                                    //var controlID = GUIUtility.GetControlID(FocusType.Passive, rect);
                                    //var line = new Rect(rect);
                                    //EditorGUI.Slider(rect, new GUIContent("Temperature (K)"),item.ColorTemperature, 1000, 20000);
                                    //if (Event.current.GetTypeForControl(controlID) != EventType.Repaint)
                                    //    return;

                                    //rect = EditorGUI.PrefixLabel(rect, controlID, new GUIContent(" "));
                                    //rect.xMax -= 55f;

                                    //line.width = 1f;
                                    //line.x = rect.xMin;

                                    //for (int x = 0; x < rect.width - 1; x++, line.x++)
                                    //{
                                    //    var temperature = Mathf.Lerp(1000, 20000, x / rect.width);
                                    //    EditorGUI.DrawRect(line, LightTemperature.ColorFromTemperature(temperature));
                                    //}

                                    //new GUIStyle("ColorPickerBox").Draw(rect, GUIContent.none, controlID);

                                    //line.width = 2f;
                                    //line.yMin--;
                                    //line.yMin++;
                                    //line.x = rect.xMin + Mathf.Lerp(0f, rect.width - 1f, (item.ColorTemperature- 1000) / (20000 - 1000));

                                    //EditorGUI.DrawRect(line, new Color32(56, 56, 56, 255));



                                    bool eb = GUI.enabled;
                                    EditorGUI.BeginChangeCheck();
                                    float colorTemp = EditorGUILayout.Slider("色温: ", item.ColorTemperature, 1000, 20000);
                                    //EditorGUI.Slider(rect, property, 1000, maxTemp, new GUIContent("Temperature (K)"));
                                    if (EditorGUI.EndChangeCheck())
                                    {
                                        Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting  amb item  color ColorTemperature " + count.ToString());
                                        //Debug.Log("ligthSetting color " + count.ToString());
                                        item.ColorTemperature = colorTemp;
                                        ligthSetting.refreshRefProbe = true;
                                    }
                                    GUI.enabled = false;
                                    EditorGUILayout.ColorField(Mathf.CorrelatedColorTemperatureToRGB(item.ColorTemperature), GUILayout.Width(100));
                                    GUI.enabled = eb;
                                }
                                else
                                {
                                    GUIContent gccc = new GUIContent("颜色 Color:");

                                    EditorGUI.BeginChangeCheck();
                                    Color itemcolor1 = EditorGUILayout.ColorField(gccc, item.color, true, true, true, cpc);
                                    if (EditorGUI.EndChangeCheck())
                                    {
                                        Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting  amb item  color " + count.ToString());
                                        //Debug.Log("ligthSetting color " + count.ToString());
                                        item.color = itemcolor1;
                                        ligthSetting.refreshRefProbe = true;
                                    }


                                }


                                if (item.useColorTemperature)
                                {
                                    item.useColorTemperatureList = GUILayout.Toggle(item.useColorTemperatureList, "List", "Button", GUILayout.Width(40));
                                }

                                if (ligthSetting.ExpertMode)
                                {
                                    EditorGUI.BeginChangeCheck();
                                    bool useColorTemperature = GUILayout.Toggle(item.useColorTemperature, "色温", "Button", GUILayout.Width(50));
                                    if (EditorGUI.EndChangeCheck())
                                    {
                                        Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting  amb item  color  useColorTemperature" + count.ToString());
                                        //Debug.Log("ligthSetting color " + count.ToString());
                                        item.useColorTemperature = useColorTemperature;
                                        ligthSetting.refreshRefProbe = true;
                                    }
                                }



                                GUILayout.EndHorizontal();

                                if (item.useColorTemperatureList)
                                {
                                    GUILayout.BeginHorizontal();
                                    EditorGUI.BeginChangeCheck();

                                    if (GUILayout.Button("火柴光"))
                                    {
                                        item.ColorTemperature = 1700;
                                    }
                                    if (GUILayout.Button("蜡烛"))
                                    {
                                        item.ColorTemperature = 1850;
                                    }

                                    if (GUILayout.Button("白炽灯"))
                                    {
                                        item.ColorTemperature = 2800;
                                    }
                                    if (GUILayout.Button("日光灯"))
                                    {
                                        item.ColorTemperature = 3000;
                                    }
                                    GUILayout.EndHorizontal();

                                    GUILayout.BeginHorizontal();
                                    if (GUILayout.Button("CP”灯"))
                                    {
                                        item.ColorTemperature = 3350;
                                    }
                                    if (GUILayout.Button("照相泛光灯"))
                                    {
                                        item.ColorTemperature = 3400;
                                    }

                                    if (GUILayout.Button("月光、浅黄光日光灯"))
                                    {
                                        item.ColorTemperature = 4100;
                                    }
                                    if (GUILayout.Button("日光"))
                                    {
                                        item.ColorTemperature = 5000;
                                    }
                                    GUILayout.EndHorizontal();


                                    GUILayout.BeginHorizontal();
                                    if (GUILayout.Button("平均日光、电子闪光（因厂商而异）"))
                                    {
                                        item.ColorTemperature = 5500;
                                    }
                                    if (GUILayout.Button("有效太阳温度"))
                                    {
                                        item.ColorTemperature = 5770;
                                    }

                                    if (GUILayout.Button("氙弧灯"))
                                    {
                                        item.ColorTemperature = 6420;
                                    }
                                    if (GUILayout.Button("最常见的白光日光灯色温"))
                                    {
                                        item.ColorTemperature = 6500;
                                    }
                                    GUILayout.EndHorizontal();

                                    GUILayout.BeginHorizontal();
                                    if (GUILayout.Button("电视屏幕（模拟）"))
                                    {
                                        item.ColorTemperature = 9300;
                                    }
                                    if (GUILayout.Button("x"))
                                    {
                                        item.ColorTemperature = 5770;
                                    }

                                    if (GUILayout.Button("x"))
                                    {
                                        item.ColorTemperature = 6420;
                                    }
                                    if (GUILayout.Button("x"))
                                    {
                                        item.ColorTemperature = 6500;
                                    }
                                    if (EditorGUI.EndChangeCheck())
                                    {

                                        item.useColorTemperatureList = false;
                                    }
                                    GUILayout.EndHorizontal();
                                }



                                //GUIContent gcc = new GUIContent("颜色 Color:");

                                //EditorGUI.BeginChangeCheck();
                                //Color itemcolor = EditorGUILayout.ColorField(gcc, item.color, true, true, true, cpc);
                                //if (EditorGUI.EndChangeCheck())
                                //{
                                //    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting color " + count.ToString());
                                //    //Debug.Log("ligthSetting color " + count.ToString());
                                //    item.color = itemcolor;
                                //}


                                EditorGUI.BeginChangeCheck();
                                float intensity = EditorGUILayout.Slider("强度Intensity: ", item.intensity, 0, 10);
                                if (EditorGUI.EndChangeCheck())
                                {
                                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting item intensity " + count.ToString());
                                    item.intensity = intensity;
                                }
                                //item.intensity = EditorGUILayout.FloatField("Intensity: ", item.intensity);
                                //EditorGUI.indentLevel = -1;
                                GUILayout.EndVertical();
                                //if (GUILayout.Button(texs.directionLightRemove,GUILayout.Height(100)))
                                if (GUILayout.Button("删除", GUILayout.Height(80)))
                                {
                                    startDel = true;
                                    delLight = item;
                                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting item delete " + count.ToString());

                                }

                                GUILayout.EndHorizontal();
                                if ((item.intensity * item.color).maxColorComponent > 1.999f)
                                {
                                    EditorGUILayout.HelpBox("小心灯光强度太强会穿透的!", MessageType.Warning);

                                }
                                count += 1;

                            }

                        }

                        //GUI.color = Color.grey;
                        //GUILayout.BeginVertical(GUI.skin.box, GUILayout.Height(40));

                        if (GUILayout.Button("添加灯光", GUILayout.Height(37)))
                        {
                            Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting item add " + ligthSetting.AmbientLights.Count.ToString());
                            CustomLightingTool.DiffuseLight dl = new CustomLightingTool.DiffuseLight();
                            ligthSetting.AmbientLights.Add(dl);
                            //Vector3 v = new Vector3(UnityEngine.Random.Range(-1, 1f), UnityEngine.Random.Range(-1, 1f), UnityEngine.Random.Range(-1, 1f));
                            //dl.direction =dl.rotation * v.normalized;
                            //dl.rotation=Quaternion.Euler(dl.x)

                        }

                        // GUILayout.EndVertical();


                        //GUI.color = Color.white;
                        if (ligthSetting.AmbientLights != null)
                        {
                            if (ligthSetting.AmbientLights.Count > 3)
                            {
                                EditorGUILayout.HelpBox("灯光最不宜过多!3个左右就差不多了.太多了也作用不大", MessageType.Warning);

                            }
                            if (startDel)
                            {
                                ligthSetting.AmbientLights.Remove(delLight);

                                foreach (var item in ligthSetting.AmbientLights)
                                {
                                    item.isEdit = false;
                                }
                                ligthSetting.CurrentDirLightIndex = -1;
                                //Debug.Log("delete");
                            }
                        }
                    }

                    EditorGUILayout.EndVertical();
                }
                else
                {
                    ShowAmbientLightGizom = false;
                }

#if UNITY_2019_1_OR_NEWER
                // EditorGUILayout.EndFoldoutHeaderGroup();
#endif



            }




            //EditorGUILayout.Space();

            if (ligthSetting.tabLightMode == 1)
            {
                ShowAmbientLightGizom = false;


                bool useCustomRefProbe = ligthSetting.UseCustomRefProbe;
                EditorGUI.BeginChangeCheck();
#if UNITY_2019_1_OR_NEWER
                //ReflectionTogle = EditorGUILayout.BeginFoldoutHeaderGroup(ReflectionTogle, "自定义镜面反射 Custom Reflection ");

#else


#endif
                EditorGUILayout.BeginHorizontal(EditorStyles.helpBox);// GUI.skin.box);
                useCustomRefProbe = GUILayout.Toggle(useCustomRefProbe, " ", GUILayout.Width(35));
                GUILayout.Label("自定义镜面反射 Custom Reflection ", EditorStyles.boldLabel);
                if (ligthSetting.UseCustomRefProbe)
                {
                    ShowRefLightGizom = GUILayout.Toggle(ShowRefLightGizom, "显示灯光方向", GUI.skin.button);
                    if (ShowRefLightGizom)
                    {
                        ShowAmbientLightGizom = false;
                    }

                }
                else
                {
                    ShowRefLightGizom = false;
                }
                EditorGUILayout.EndHorizontal();
                if (ligthSetting.CustomReflectionProbe != null)
                {
                    GUILayout.BeginHorizontal();
                    EditorGUI.BeginChangeCheck();
                    float rfIntensity = EditorGUILayout.Slider("强度Intensity: ", ligthSetting.CustomRefProbeIntensity, 0, 3);
                    if (GUILayout.Button("0"))
                    {
                        rfIntensity = 0;
                    }
                    if (GUILayout.Button("1"))
                    {
                        rfIntensity = 1;

                    }
                    if (EditorGUI.EndChangeCheck())
                    {
                        Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting CustomReflectionProbe intensity");
                        ligthSetting.CustomRefProbeIntensity = rfIntensity;
                        ligthSetting.CustomReflectionProbe.intensity = ligthSetting.CustomRefProbeIntensity;
                    }

                    GUILayout.EndHorizontal();



                }
                if (EditorGUI.EndChangeCheck())
                {
                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting useCustomRefProbe change");
                    ligthSetting.UseCustomRefProbe = useCustomRefProbe;
                }



                if (ligthSetting.UseCustomRefProbe)
                {

                    //EditorGUI.indentLevel = 1;

                    if (ligthSetting.UseCustomRefProbe)
                    {

                        if (ligthSetting.ExpertMode)
                        {
                            ligthSetting.isBakeEXR = GUILayout.Toggle(ligthSetting.isBakeEXR, "EXR", GUI.skin.button);
                            EditorGUI.BeginChangeCheck();
                            ReflectionProbe rp = (ReflectionProbe)EditorGUILayout.ObjectField("自定义反射探针 Custom Reflection Probe", ligthSetting.CustomReflectionProbe, typeof(ReflectionProbe));
                            if (EditorGUI.EndChangeCheck())
                            {
                                Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting CustomReflectionProbe change");
                                ligthSetting.CustomReflectionProbe = rp;
                            }
                            EditorGUI.BeginChangeCheck();
                            Texture crt = (Texture)EditorGUILayout.ObjectField("自定义Cubemap", ligthSetting.CustomReflectionTexture, typeof(Texture));
                            if (EditorGUI.EndChangeCheck())
                            {
                                Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting CustomReflectionTexture change");
                                ligthSetting.CustomReflectionTexture = crt;
                            }
                        }


                    }




                    EditorGUILayout.BeginHorizontal();



                    if (ligthSetting.ExpertMode)
                    {
                        if (!ligthSetting.onEditorRef)
                        {
                            GUI.enabled = false;
                        }

                        if (GUILayout.Button("Bake", GUI.skin.button, GUILayout.Width(50), GUILayout.Height(30)))
                        {


                            BakeRealtimeProbe();
                            ligthSetting.SetRefTextrue();
                            ligthSetting.onEditorRef = false;
                            ligthSetting.CustomReflectionProbe.mode = ReflectionProbeMode.Custom;
                            ligthSetting.CustomReflectionProbe.customBakedTexture = ligthSetting.CustomReflectionTexture;


                            if (ligthSetting.onEditorRef && ligthSetting.RefreshRealtimeSH && ligthSetting.refreshRefProbe)
                            {
                                UpdateRealtimeSH();

                            }


                            if (ligthSetting.lightParent != null)
                            {
                                DestroyImmediate(ligthSetting.lightParent.gameObject);

                            }

                            PrefabUtility.ApplyPrefabInstance(ligthSetting.gameObject, InteractionMode.AutomatedAction);
                            RefreshRefProbeVisualTex();
                            Repaint();
                        }
                        GUI.enabled = true;

                    }




                    EditorGUI.BeginChangeCheck();
                    bool onEditorRef = GUILayout.Toggle(ligthSetting.onEditorRef, "编辑", GUI.skin.button, GUILayout.Height(35));
                    if (EditorGUI.EndChangeCheck())
                    {
                        if (!onEditorRef)
                        {
                            if (EditorUtility.DisplayDialog("CustomLightTools", "是否需要 bake 镜面反射 Cubemap", "要!", "不要!"))
                            {
                                //BakeRealtimeProbe();

                                SaveMe();
                            }
                            ligthSetting.onEditorRef = onEditorRef;
                            ligthSetting.CustomReflectionProbe.mode = ReflectionProbeMode.Custom;
                            ligthSetting.CustomReflectionProbe.customBakedTexture = ligthSetting.CustomReflectionTexture;
                            foreach (var item in ligthSetting.reflectionLights)
                            {
                                item.isEdit = false;
                            }
                            ShowRefLightGizom = false;
                            ligthSetting.refreshRefProbe = true;
                            if (ligthSetting.lightParent != null)
                            {
                                DestroyImmediate(ligthSetting.lightParent.gameObject);

                            }

                        }
                        else
                        {



                            ligthSetting.onEditorRef = onEditorRef;
                            bool next = true;

                            if (!QualitySettings.realtimeReflectionProbes)
                            {

                                next = false;
                                //EditorUtility.DisplayDialog("CustomLighttools", ""
                                QualitySettings.realtimeReflectionProbes = true;

                            }

                            if (!QualitySettings.realtimeReflectionProbes)
                            {
                                if (EditorUtility.DisplayDialog("CustomLightTools", "自定义编辑光照 必须要打开 RealtimeReflection 在 Quality 设置里面！ 请手动打开！", "ok"))
                                {


                                }

                            }


                            if (next)
                            {
                                ShowRefLightGizom = true;
                                ligthSetting.CustomReflectionProbe.mode = ReflectionProbeMode.Realtime;
                                ligthSetting.CustomReflectionProbe.RenderProbe();
                                ligthSetting.refreshRefProbe = true;



                                ShowRefLightGizom = true;

                                UpdateReflectionLightObject();

                                if (ligthSetting.RefreshRealtimeSH)
                                {
                                    UpdateRealtimeSH();
                                    //Debug.Log("UpdateRealtimeSH Bake");

                                }
                            }

                        }
                    }
                    if (ligthSetting.ExpertMode)
                    {
                        if (GUILayout.Button("选中Cubemap", GUI.skin.button, GUILayout.Width(90), GUILayout.Height(30)))
                        {
                            Selection.activeObject = ligthSetting.CustomReflectionProbe.bakedTexture;

                        }
                    }

                    if (!ligthSetting.onEditorRef)
                    {
                        GUI.enabled = false;
                    }
                    if (GUILayout.Button("刷新", GUI.skin.button, GUILayout.Width(40), GUILayout.Height(30)))
                    {
                        if (ligthSetting.lightParent != null)
                        {
                            DestroyImmediate(ligthSetting.lightParent.gameObject);

                        }
                        UpdateReflectionLightObject();
                        RefreshRefProbeVisualTex();
                        ligthSetting.RefreshRealtimeSH=true;
                    }
                    GUI.enabled = true;


                    if (ligthSetting.DebugMode)
                    {
                        //string filename = AssetDatabase.GetAssetPath(ligthSetting.CustomReflectionProbe.bakedTexture);




                    }
                    EditorGUILayout.EndHorizontal();
                    if (ligthSetting.DebugMode)
                    {
                        //EditorGUILayout.ObjectField("RT:",ligthSetting.CustomReflectionRT , typeof(RenderTexture));
                        //EditorGUILayout.ObjectField("Cubemap:",ligthSetting.CustomReflectionCubemap, typeof(Cubemap));
                        //EditorGUILayout.ObjectField("Texture2D:", ligthSetting.DebugTex1, typeof(Texture2D));
                        //EditorGUILayout.ObjectField("realtime:", ligthSetting.RealtimeCubemap, typeof(Cubemap));

                        //if (GUILayout.Button("test", GUI.skin.button, GUILayout.Width(45), GUILayout.Height(30)))
                        //{
                        //    ligthSetting.RealtimeCubemap = GetRealtimeCubemap();
                        //}
                        //if (GUILayout.Button("test1", GUI.skin.button, GUILayout.Width(45), GUILayout.Height(30)))
                        //{
                        //    ligthSetting.RealtimeCubemap = GetRealtimeCubemapMip(8);
                        //}

                    }





                    //EditorGUI.indentLevel = 0;
                    if (ligthSetting.ReflectionTexVisual != null)
                    {

                        //GUILayout.Label(scale.ToString());

                        int mipmapCount = 1;


                        if (ligthSetting.CustomReflectionProbe.bakedTexture != null)
                        {
#if UNITY_2019_1_OR_NEWER
                            if (ligthSetting.CustomReflectionProbe.realtimeTexture != null)
                            {
                                mipmapCount = ligthSetting.CustomReflectionProbe.realtimeTexture.mipmapCount - 1;
                            }
#else
                            if (ligthSetting.CustomReflectionProbe.bakedTexture != null)
                            {
                                mipmapCount = (int)Mathf.Log(ligthSetting.CustomReflectionProbe.bakedTexture.width, 2);

                            }
#endif
                        }



                        GUILayout.BeginVertical();
                        //GUILayout.Button(ligthSetting.ReflectionTexVisual, GUI.skin.box, GUILayout.Height(scale.height));
                        GUILayout.Space(scale.height);
                        //GUILayout.Button(" " ,GUI.skin.box, GUILayout.Height(scale.height));
                        Rect rt1 = new Rect(0, 0, 100, 50);
                        if (Event.current.type == EventType.Repaint && ligthSetting.ReflectionTexVisual != null)
                        {

                            rt1 = GUILayoutUtility.GetLastRect();
                            float b = (scale.height / ligthSetting.ReflectionTexVisual.height) * ligthSetting.ReflectionTexVisual.width - 20;
                            b = Mathf.Max(b, 50);
                            float a = (scale.width - b) / 2;
                            rt1.width = b;
                            rt1.height = scale.height;
                            rt1.x = Mathf.Abs(a) + 10;
                        }


                        EditorGUI.DrawPreviewTexture(rt1, ligthSetting.ReflectionTexVisual);

                        //EditorGUI.DrawRect(rt1, Color.red);

                        // 鼠标拖动灯光的控制方式先注释掉 有bug
                        if (false)
                        //if (ligthSetting.ExpertMode)
                        {
                            Rect rt = new Rect(0, 0, 100, 50);
                            Vector2 pos = Vector2.zero;
                            if (Event.current.type == EventType.Repaint && ligthSetting.ReflectionTexVisual != null)
                            {

                                rt = GUILayoutUtility.GetLastRect();
                                if (rt.Contains(Event.current.mousePosition))
                                {
                                    pos.y = (Event.current.mousePosition.x - rt.x) / rt.width * 360 + 90;// 2*Mathf.PI;
                                    pos.x = (Event.current.mousePosition.y - rt.y) / rt.height * 180 + 90;// 2 * Mathf.PI;
                                }

                            }

                            if (Event.current.type == EventType.Used && ligthSetting.ReflectionTexVisual != null)
                            {
                                //Debug.Log(Event.current.button);
                                if (ligthSetting.CurrentRefLightIndex > -1 && Event.current.button == 0)
                                {
                                    ligthSetting.canMoveRefLight = true;
                                    //Debug.Log("xx");

                                }
                            }

                            if (Event.current.type == EventType.Repaint && ligthSetting.canMoveRefLight)
                            {

                                ligthSetting.reflectionLights[ligthSetting.CurrentRefLightIndex].rotation = Quaternion.Euler(pos.x, pos.y, 0);
                                ligthSetting.reflectionLights[ligthSetting.CurrentRefLightIndex].direction = ligthSetting.reflectionLights[0].rotation * Vector3.forward;
                                ligthSetting.refreshRefProbe = true;
                                ligthSetting.canMoveRefLight = false;
                            }
                            //EditorGUI.DrawRect(rt, Color.green);

                            int countRef = 0;
                            foreach (var item in ligthSetting.reflectionLights)
                            {
                                Vector3 rot = item.rotation.eulerAngles;

                                Vector2 lpos = Vector2.zero;
                                //lpos.x = rt.width*((rot.y/360)-90);
                                //lpos.y =rt.height* (((180 - rot.x)% 360 + 90 )/ 180) ;

                                lpos.x = (rot.y + 90) % 360;
                                lpos.y = ((360 - rot.x) + 90) % 360;
                                lpos.x = (lpos.x / 360.0f) * rt.width;
                                lpos.y = (lpos.y / 180.0f) * rt.height;

                                Vector2 size = new Vector2(100, 100) * item.size * (new Vector2(item.scale.x, item.scale.z));
                                size.x = Mathf.Min(size.x, 100);
                                size.y = Mathf.Min(size.y, 100);
                                item.clickRect = new Rect(lpos.x - size.x / 2 + rt.x, lpos.y - size.y / 2 + rt.y, size.x, size.y);
                                //EditorGUILayout.Vector3Field("rot", rot);
                                //EditorGUILayout.Vector3Field("lpos", lpos);
                                //EditorGUILayout.RectField("RT", RT);

                                if (item.isEdit)
                                {
                                    EditorGUI.DrawRect(item.clickRect, Color.red);
                                }

                                if (Event.current.button == 0 && item.clickRect.Contains(Event.current.mousePosition))
                                {
                                    foreach (var itemt in ligthSetting.reflectionLights)
                                    {
                                        itemt.isEdit = false;
                                    }
                                    item.isEdit = true;
                                    ligthSetting.CurrentRefLightIndex = countRef;
                                }

                                countRef += 1;

                            }



                        }




                        if (ligthSetting.CustomReflectionProbe != null && ligthSetting.ExpertMode)
                        {
                            if (ligthSetting.CustomReflectionProbe.realtimeTexture != null)
                            {
                                EditorGUILayout.BeginHorizontal();
                                for (int i = 0; i < mipmapCount + 1; i++)
                                {
                                    if (GUILayout.Button(i.ToString()))
                                    {
                                        ligthSetting.ReflectionTexVisualMip = i;
                                        ligthSetting.refreshRefProbe = true;

                                    }
                                }

                                EditorGUILayout.EndHorizontal();


                                EditorGUI.BeginChangeCheck();
                                ligthSetting.ReflectionTexVisualMip = EditorGUILayout.Slider("粗糙度: ", ligthSetting.ReflectionTexVisualMip, 0, mipmapCount);
                                if (EditorGUI.EndChangeCheck())
                                {
                                    ligthSetting.refreshRefProbe = true;

                                }

                            }
                            
                            EditorGUI.BeginChangeCheck();
                            GUILayout.BeginHorizontal();
                            falseColor = GUILayout.Toggle(falseColor, "FalseColor",GUI.skin.button);
                            falseColorExposure = EditorGUILayout.Slider("ViusalExposure", falseColorExposure, -20, 20);
                            if (GUILayout.Button("0"))
                            {
                                falseColorExposure=0;
                            }
                            GUILayout.EndHorizontal();
                            if (EditorGUI.EndChangeCheck())
                            {
                                RefreshRefProbeVisualTex();
                                if (falseColor)
                                {
                                    Shader.SetGlobalFloat("_FalseColor", 1);
                                    Shader.SetGlobalFloat("_falseColorExposure", falseColorExposure);

                                }
                                else
                                {
                                    Shader.SetGlobalFloat("_FalseColor", 0);
                                    Shader.SetGlobalFloat("_falseColorExposure", 0);
                                }
                            }


                      

                        }











                        GUILayout.EndVertical();


                    }



                    //reflection lights editor
                    bool startDel = false;
                    CustomLightingTool.ReflectionLight delLight = null;
                    int count = 0;


                    float titelHeight = 30f;



                    if (!ligthSetting.onEditorRef)
                    {
                        GUI.enabled = false;
                    }



                    if (ligthSetting.CustomReflectionProbe != null)
                    {

                        GUILayout.BeginHorizontal();
                        GUILayout.Label("分辨率 resolution");
                        if (GUILayout.Button(ligthSetting.CustomReflectionProbe.resolution.ToString(), EditorStyles.popup))
                        {

                            GenericMenu toolsMenu = new GenericMenu();
                            toolsMenu.AddItem(new GUIContent("16 差"), false, OnChangeReflectionProbeResolution, 16);
                            toolsMenu.AddItem(new GUIContent("32 一般"), false, OnChangeReflectionProbeResolution, 32);
                            toolsMenu.AddItem(new GUIContent("64 普通"), false, OnChangeReflectionProbeResolution, 64);
                            toolsMenu.AddItem(new GUIContent("128 高"), false, OnChangeReflectionProbeResolution, 128);
                            toolsMenu.AddItem(new GUIContent("256 极高"), false, OnChangeReflectionProbeResolution, 256);
                            if (ligthSetting.ExpertMode)
                            {
                                toolsMenu.AddItem(new GUIContent("512 超级高消耗"), false, OnChangeReflectionProbeResolution, 512);
                                toolsMenu.AddItem(new GUIContent("1024 太高了仅限测试用"), false, OnChangeReflectionProbeResolution, 1024);
                                toolsMenu.AddItem(new GUIContent("2048 太高了仅限测试用"), false, OnChangeReflectionProbeResolution, 2048);
                            }
                            toolsMenu.DropDown(new Rect(Event.current.mousePosition.x, Event.current.mousePosition.y, 0, 0));
                        }
                        GUILayout.EndHorizontal();





                    }






                    if (ligthSetting.onEditorRef)
                    {


                        if (ligthSetting.reflectionLights != null)
                        {
                            GUILayout.BeginVertical(GUI.skin.box);

                            GUILayout.BeginHorizontal();

                            //EditorGUI.BeginChangeCheck();
                            //bool enablet = GUILayout.Toggle(ligthSetting.skyboxValue.enable, "", GUILayout.Height(titelHeight), GUILayout.Width(30));
                            //if (EditorGUI.EndChangeCheck())
                            //{
                            //    Undo.RegisterCompleteObjectUndo(ligthSetting.SkyboxBase.gameObject, "ligthSetting  ligthSetting.SkyboxBase.gameObject item Directionlight on " + count.ToString());
                            //    ligthSetting.skyboxValue.enable = enablet;
                            //    ligthSetting.SkyboxBase.gameObject.SetActive(enablet);
                            //}

                            if (GUILayout.Button("背景环境Cubemap: ", GUILayout.Height(25)))
                            {
                                //Undo.RegisterCompleteObjectUndo(ligthSetting.SkyboxBase.gameObject, "ligthSetting  ligthSetting.SkyboxBase.gameObject item Directionlight on " + count.ToString());
                                //ligthSetting.skyboxValue.enable = !ligthSetting.skyboxValue.enable;
                                ligthSetting.skyboxValue.enable = true;
                            }

                            GUILayout.EndHorizontal();



                            if (ligthSetting.ExpertMode)
                            {

                                GUILayout.BeginHorizontal();

                                EditorGUI.BeginChangeCheck();
                                bool maxValueOn = GUILayout.Toggle(ligthSetting.skyboxValue.maxValueOn, "限制亮度");
                                if (EditorGUI.EndChangeCheck())
                                {
                                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting CustomReflectionProbe maxValueOn");
                                    ligthSetting.skyboxValue.maxValueOn = maxValueOn;

                                    ligthSetting.refreshRefProbe = true;
                                }
                                if (!maxValueOn)
                                {
                                    GUI.enabled = false;
                                }
                                EditorGUI.BeginChangeCheck();
                                float maxValue = EditorGUILayout.FloatField("最大亮度 Max Value: ", ligthSetting.skyboxValue.maxValue);
                                if (EditorGUI.EndChangeCheck())
                                {
                                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting CustomReflectionProbex  maxValue");
                                    ligthSetting.skyboxValue.maxValue = maxValue;
                                    ligthSetting.refreshRefProbe = true;
                                }

                                if (GUILayout.Button("RGBM"))
                                {
                                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting CustomReflectionProbex  maxValue");
                                    ligthSetting.skyboxValue.maxValue = 5;
                                    ligthSetting.refreshRefProbe = true;
                                }
                                if (GUILayout.Button("dLDR"))
                                {
                                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting CustomReflectionProbex  maxValue");
                                    ligthSetting.refreshRefProbe = true;
                                    ligthSetting.skyboxValue.maxValue = 2;
                                }
                                GUILayout.EndHorizontal();
                                if (ligthSetting.onEditorRef)
                                {
                                    GUI.enabled = true;

                                }



                            }






                            GUILayout.BeginVertical();
                            int tabx = 0;
                            if (ligthSetting.skyboxValue.enable)
                            {



                                if (!ligthSetting.skyboxValue.panoromicEnabled)
                                {
                                    tabx = 1;
                                }
                                if (ligthSetting.skyboxValue.cubemap != null)
                                {
                                    if (ligthSetting.skyboxValue.cubemap.name.Contains(CUBEMAPNAME) && tabx == 0)
                                    {

                                        cubemapError = true;
                                    }
                                    else
                                    {
                                        cubemapError = false;

                                    }
                                }
                                EditorGUI.BeginChangeCheck();
                                if (cubemapError)
                                {
                                    GUI.color = Color.red;

                                }
                                tabx = GUILayout.Toolbar(tabx, new string[] { "天空盒子Skybox", "图片" }, (GUIStyle)"toolbarbutton");
                                if (EditorGUI.EndChangeCheck())
                                {
                                    if (tabx == 0)
                                    {
                                        ligthSetting.skyboxValue.panoromicEnabled = true;

                                    }
                                    else
                                    {
                                        ligthSetting.skyboxValue.panoromicEnabled = false;

                                    }
                                    ligthSetting.refreshRefProbe = true;

                                }

                                if (tabx == 0)
                                {



                                    EditorGUI.BeginChangeCheck();
                                    string cmtempStr = "Cubmap: ";
                                    if (cubemapError)
                                    {
                                        //int fontSize= GUI.skin.label.fontSize ;
                                        //GUI.skin.label.fontSize = 20;

                                        //GUILayout.BeginHorizontal();
                                        //GUILayout.Label("错误Cubmap！");//,GUILayout.MaxWidth(200));
                                        //GUILayout.Label("请更改！",GUILayout.MaxWidth(300));
                                        //GUILayout.EndHorizontal();

                                        //GUI.skin.label.fontSize = 20;
                                        //GUILayout.Label("不能使用其他场景的cubemap",GUILayout.MaxWidth(450));
                                        //GUI.skin.label.fontSize = 30;
                                        //GUILayout.Label("请更改！");
                                        //GUI.skin.label.fontSize = fontSize;
                                    }
                                    Cubemap cubemap = EditorGUILayout.ObjectField("Cubmap: ", ligthSetting.skyboxValue.cubemap, typeof(Cubemap)) as Cubemap;
                                    if (EditorGUI.EndChangeCheck())
                                    {
                                        Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting  skyboxValue cubemap a  " + count.ToString());
                                        ligthSetting.skyboxValue.cubemap = cubemap;
                                        ligthSetting.refreshRefProbe = true;
                                    }


                                    if (cubemapError)
                                    {
                                        EditorGUILayout.HelpBox("不能使用其他场景的 cubemap 作为基础!!!! 请更改其他 cubemap", MessageType.Error);

                                    }


                                }
                                else
                                {
                                    EditorGUI.BeginChangeCheck();
                                    Texture2D PanoromicHDR = EditorGUILayout.ObjectField("Texture2D: ", ligthSetting.skyboxValue.PanoromicHDR, typeof(Texture2D)) as Texture2D;
                                    if (EditorGUI.EndChangeCheck())
                                    {
                                        Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting  skyboxValue PanoromicHDR a  " + count.ToString());
                                        ligthSetting.skyboxValue.PanoromicHDR = PanoromicHDR;
                                        ligthSetting.refreshRefProbe = true;
                                    }

                                }

                                if (cubemapError)
                                {
                                    GUI.color = Color.white;
                                }




                                EditorGUI.BeginChangeCheck();
                                GUIContent gcc = new GUIContent("颜色 Color:");
                                EditorGUI.BeginChangeCheck();
                                Color sbcolor = EditorGUILayout.ColorField(gcc, ligthSetting.skyboxValue.color, true, true, true, cpc);
                                if (EditorGUI.EndChangeCheck())
                                {
                                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting  skyboxValue skyboxValue  color " + count.ToString());
                                    ligthSetting.refreshRefProbe = true;
                                    ligthSetting.skyboxValue.color = sbcolor;
                                }


                                GUILayout.BeginHorizontal();
                                EditorGUI.BeginChangeCheck();
                                float exposure = ligthSetting.skyboxValue.exposure;
                                exposure = EditorGUILayout.Slider("曝光: ", exposure, minExposure, maxExposure);
                                if (GUILayout.Button("-"))
                                {
                                    exposure -= 1;
                                }
                                if (GUILayout.Button("0"))
                                {
                                    exposure = 0;
                                }
                                if (GUILayout.Button("+"))
                                {
                                    exposure += 1;

                                }
                                exposure = Mathf.Clamp(exposure, minExposure, maxExposure);

                                if (EditorGUI.EndChangeCheck())
                                {
                                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting  skyboxValue expesure a  " + count.ToString());
                                    ligthSetting.skyboxValue.exposure = exposure;
                                    ligthSetting.refreshRefProbe = true;
                                }

                                GUILayout.EndHorizontal();

                                EditorGUI.BeginChangeCheck();
                                float rotation = EditorGUILayout.Slider("旋转: ", ligthSetting.skyboxValue.rotation, 0, 360);
                                if (EditorGUI.EndChangeCheck())
                                {
                                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting  skyboxValue rotation a  " + count.ToString());
                                    ligthSetting.skyboxValue.rotation = rotation;
                                    ligthSetting.refreshRefProbe = true;
                                }


                            }


                            ligthSetting.SkyboxMate.SetColor("_Tint", ligthSetting.skyboxValue.color);
                            ligthSetting.SkyboxMate.SetFloat("_Exposure", ligthSetting.skyboxValue.exposure);
                            ligthSetting.SkyboxMate.SetFloat("_Rotation", ligthSetting.skyboxValue.rotation);
                            ligthSetting.SkyboxMate.SetTexture("_Tex", ligthSetting.skyboxValue.cubemap);
                            ligthSetting.SkyboxMate.SetTexture("_PanoromicTex", ligthSetting.skyboxValue.PanoromicHDR);
                            ligthSetting.SkyboxMate.SetFloat("_Exposure", ligthSetting.skyboxValue.exposure);
                            Shader.SetGlobalFloat("_RefLightMaxValue", ligthSetting.skyboxValue.maxValue);
                            if (ligthSetting.skyboxValue.maxValueOn)
                            {
                                Shader.EnableKeyword("_REF_LIGHT_MAX_VALUE");
                            }
                            else
                            {
                                Shader.DisableKeyword("_REF_LIGHT_MAX_VALUE");

                            }

                            if (tabx == 1)
                            {
                                ligthSetting.SkyboxMate.EnableKeyword("_PANOROMIC");

                            }
                            else
                            {
                                ligthSetting.SkyboxMate.DisableKeyword("_PANOROMIC");

                            }




                            GUILayout.EndVertical();
                            GUILayout.EndVertical();

















                            foreach (var item in ligthSetting.reflectionLights)
                            {




                                Color lightColor = item.color;
                                lightColor.a = 1;

                                if (!item.enabled)
                                {
                                    GUI.color = Color.grey;
                                }

                                GUILayout.BeginHorizontal(GUI.skin.box);
                                GUI.color = Color.white;

                                EditorGUI.BeginChangeCheck();

                                //string enabledStr = "开";

                                //if (item.enabled)
                                //{
                                //    enabledStr = "关";
                                //}
                                bool itemEnabled = GUILayout.Toggle(item.enabled, "", GUILayout.Height(titelHeight), GUILayout.Width(30));
                                if (EditorGUI.EndChangeCheck())
                                {
                                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting  ref item Directionlight on " + count.ToString());
                                    item.enabled = itemEnabled;
                                    ligthSetting.refreshRefProbe = true;

                                    // RefreshRefProbeVisualTex();
                                }

                                if (!item.enabled)
                                {
                                    GUI.color = Color.grey;
                                    lightColor *= Color.grey;
                                }
                                GUILayout.Button(item.tex, GUI.skin.box, GUILayout.Height(titelHeight), GUILayout.Width(30));
                                GUI.color = Color.white;

                                GUI.color = lightColor;


                                if (GUILayout.Button("灯光 Light " + count.ToString(), GUILayout.Height(titelHeight)))
                                {
                                    item.isEdit = !item.isEdit;

                                    ligthSetting.CurrentRefLightIndex = count;
                                    for (int i = 0; i < ligthSetting.reflectionLights.Count; i++)
                                    {
                                        if (ligthSetting.CurrentRefLightIndex != i)
                                        {
                                            ligthSetting.reflectionLights[i].isEdit = false;
                                        }
                                    }
                                }

                                GUI.color = Color.white;

                                if (item.isEdit && ligthSetting.ExpertMode)
                                {
                                    //EditorGUILayout.BeginHorizontal();
                                    //GUILayout.FlexibleSpace();
                                    //EditorGUILayout.EndHorizontal();
                                    //Rect scalexx = new Rect(0, 0, 0, 0);
                                    //if (Event.current.type == EventType.Repaint)
                                    //{

                                    //    scalexx = GUILayoutUtility.GetLastRect();
                                    //    float h = scalexx.width / (ligthSetting.ReflectionTexVisual.width * 1f / ligthSetting.ReflectionTexVisual.height);
                                    //    h = Mathf.Min(256, h);
                                    //    scalexx.height = h;
                                    //    scalexx.y -= scalexx.height + 3;
                                    //}
                                    //GUI.DrawTexture(scalexx, ligthSetting.ReflectionTexVisual);

                                }


                                //if (GUILayout.Button(texs.directionLightRemove,GUILayout.Height(100)))

                                if (GUILayout.Button("▲", GUILayout.Height(titelHeight), GUILayout.Width(20)))
                                {
                                    //item.index = count;
                                    if (count != 0)
                                    {
                                        ligthSetting.RefChangeIndex(count, count - 1);

                                    }

                                }
                                if (GUILayout.Button("▼", GUILayout.Height(titelHeight), GUILayout.Width(20)))
                                {
                                    //item.index = count;
                                    if (count != ligthSetting.reflectionLights.Count - 1)
                                    {
                                        ligthSetting.RefChangeIndex(count, count + 1);

                                    }
                                }
                                item.index = count;


                                if (GUILayout.Button("×", GUILayout.Height(titelHeight), GUILayout.Width(30)))
                                {
                                    startDel = true;
                                    delLight = item;


                                    //Debug.Log("ligthSetting item delete " + count.ToString());
                                    ligthSetting.refreshRefProbe = true;

                                }


                                GUILayout.EndHorizontal();
                                if (!item.isEdit)
                                {


                                    count += 1;
                                    continue;

                                }
                                //GUI.color=
                                GUILayout.BeginHorizontal();

                                if (item.isEdit)
                                {
                                    GUI.color = Color.green;
                                }
                                GUILayout.BeginVertical(GUI.skin.box);
                                GUI.color = Color.white;


                                EditorGUI.indentLevel = 1;




                                EditorGUI.BeginChangeCheck();
                                Texture2D texxxx = (Texture2D)EditorGUILayout.ObjectField("tex:", item.tex, typeof(Texture2D));
                                if (EditorGUI.EndChangeCheck())
                                {

                                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting  ref item  tex " + count.ToString());
                                    item.tex = texxxx;
                                    ligthSetting.refreshRefProbe = true;
                                }




                                GUILayout.BeginHorizontal();





                                GUI.enabled = false;
                                EditorGUILayout.Vector3Field("方向 Direction: ", item.direction);
                                GUI.enabled = true;
                                EditorGUI.BeginChangeCheck();

                                item.isEdit = GUILayout.Toggle(item.isEdit, "旋转灯光", "Button", GUILayout.Width(60));
                                //bool itemisEdit = GUILayout.Toggle(item.isEdit, "旋转灯光", "Button",GUILayout.Width(60));
                                if (EditorGUI.EndChangeCheck())
                                {
                                    //    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting isEdit  " + count.ToString());
                                    //    Debug.Log("ligthSetting isEdit " + count.ToString());
                                    //    item.isEdit = itemisEdit;
                                    if (item.isEdit)
                                    {
                                        // CurrentDirLight = item;
                                        ligthSetting.CurrentRefLightIndex = count;
                                        selectionRefLitIndex = 0;
                                        Selection.activeObject = targetObj;
                                        //Debug.Log("isEdit: " + count.ToString() + "ligthSetting.CurrentDirLightIndex: "+ ligthSetting.CurrentDirLightIndex.ToString());
                                        // InitSenceGUI();
                                        for (int i = 0; i < ligthSetting.reflectionLights.Count; i++)
                                        {
                                            if (ligthSetting.CurrentRefLightIndex != i)
                                            {
                                                ligthSetting.reflectionLights[i].isEdit = false;
                                            }
                                        }

                                    }
                                    else
                                    {
                                        //if (ligthSetting.CurrentDirLightIndex == count)
                                        //{
                                        //    ligthSetting.CurrentDirLightIndex = -1;
                                        //    CurrentDirLight = null;

                                        //}
                                        //ClearSenceGUI();
                                        //Debug.Log("ligthSetting.CurrentDirLightIndex = -1;");
                                        ligthSetting.CurrentDirLightIndex = -1;
                                        ///Debug.Log("de isEdit: " + count.ToString());

                                    }
                                }
                                GUILayout.EndHorizontal();



                                EditorGUI.BeginChangeCheck();
                                float aa = EditorGUILayout.Slider("透明度: ", item.color.a, 0.0f, 1f);
                                if (EditorGUI.EndChangeCheck())
                                {
                                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting  ref item a  " + count.ToString());
                                    //Debug.Log("ligthSetting item intensity " + count.ToString());
                                    item.color.a = aa;
                                    ligthSetting.refreshRefProbe = true;
                                }


                                //COlor
                                GUILayout.BeginHorizontal();

                                if (item.useColorTemperature)
                                {
                                    bool eb = GUI.enabled;
                                    EditorGUI.BeginChangeCheck();
                                    float colorTemp = EditorGUILayout.Slider("色温: ", item.ColorTemperature, 1000, 20000);
                                    if (EditorGUI.EndChangeCheck())
                                    {
                                        Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting  ref item  color ColorTemperature " + count.ToString());
                                        //Debug.Log("ligthSetting color " + count.ToString());
                                        item.ColorTemperature = colorTemp;
                                        ligthSetting.refreshRefProbe = true;
                                    }
                                    GUI.enabled = false;
                                    Color itemcolor = EditorGUILayout.ColorField(Mathf.CorrelatedColorTemperatureToRGB(item.ColorTemperature), GUILayout.Width(100));
                                    GUI.enabled = eb;
                                }
                                else
                                {
                                    GUIContent gcc = new GUIContent("颜色 Color:");

                                    EditorGUI.BeginChangeCheck();
                                    Color itemcolor = EditorGUILayout.ColorField(gcc, item.color, true, true, true, cpc);
                                    if (EditorGUI.EndChangeCheck())
                                    {
                                        Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting  ref item  color " + count.ToString());
                                        //Debug.Log("ligthSetting color " + count.ToString());
                                        item.color = itemcolor;
                                        ligthSetting.refreshRefProbe = true;
                                    }


                                }


                                if (ligthSetting.ExpertMode)
                                {
                                    EditorGUI.BeginChangeCheck();
                                    bool useColorTemperature = GUILayout.Toggle(item.useColorTemperature, "色温", "Button", GUILayout.Width(50));
                                    if (EditorGUI.EndChangeCheck())
                                    {
                                        Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting  ref item  color  useColorTemperature" + count.ToString());
                                        //Debug.Log("ligthSetting color " + count.ToString());
                                        item.useColorTemperature = useColorTemperature;
                                        ligthSetting.refreshRefProbe = true;
                                    }
                                }


                                GUILayout.EndHorizontal();




                                GUILayout.BeginHorizontal();
                                EditorGUI.BeginChangeCheck();
                                float exposure = item.Exposure;
                                exposure = EditorGUILayout.Slider("曝光: ", exposure, minExposure, maxExposure);
                                if (GUILayout.Button("-"))
                                {
                                    exposure -= 1;
                                }
                                if (GUILayout.Button("0"))
                                {
                                    exposure = 0;
                                }
                                if (GUILayout.Button("+"))
                                {
                                    exposure += 1;

                                }
                                exposure = Mathf.Clamp(exposure, minExposure, maxExposure);

                                if (EditorGUI.EndChangeCheck())
                                {
                                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting  item expesure a  " + count.ToString());
                                    item.Exposure = exposure;
                                    ligthSetting.refreshRefProbe = true;
                                }

                                GUILayout.EndHorizontal();




                                //EditorGUI.BeginChangeCheck();
                                //float intensity = EditorGUILayout.Slider("曝光 Exposure: ", , -15, 15);
                                //if (EditorGUI.EndChangeCheck())
                                //{
                                //    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting  ref item intensity " + count.ToString());
                                //    //Debug.Log("ligthSetting item intensity " + count.ToString());
                                //    item.Exposure = intensity;
                                //    ligthSetting.refreshRefProbe = true;
                                //}


                                EditorGUI.BeginChangeCheck();
                                float size = EditorGUILayout.Slider("大小Size: ", item.size, 0, 5);
                                if (EditorGUI.EndChangeCheck())
                                {
                                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting ref item size " + count.ToString());
                                    //Debug.Log("ligthSetting item intensity " + count.ToString());
                                    item.size = size;
                                    ligthSetting.refreshRefProbe = true;
                                }

                                EditorGUI.BeginChangeCheck();
                                float scalex = EditorGUILayout.Slider("宽: ", item.scale.x, 0.1f, 5);
                                if (EditorGUI.EndChangeCheck())
                                {
                                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting  ref item scalex " + count.ToString());
                                    //Debug.Log("ligthSetting item intensity " + count.ToString());
                                    item.scale.x = scalex;
                                    ligthSetting.refreshRefProbe = true;
                                }
                                EditorGUI.BeginChangeCheck();
                                float scalez = EditorGUILayout.Slider("高: ", item.scale.z, 0.1f, 5);
                                if (EditorGUI.EndChangeCheck())
                                {
                                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting  ref item scalez " + count.ToString());
                                    //Debug.Log("ligthSetting item intensity " + count.ToString());
                                    item.scale.z = scalez;
                                    ligthSetting.refreshRefProbe = true;
                                }
                                EditorGUI.BeginChangeCheck();
                                bool fo = false;
                                if (item.falloff == 0)
                                {
                                    fo = true;
                                }
                                bool falloff = GUILayout.Toggle(fo, "衰减", GUI.skin.button);//, GUILayout.Width(30));
                                if (EditorGUI.EndChangeCheck())
                                {
                                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting  ref item falloff on " + count.ToString());
                                    if (falloff)
                                    {
                                        item.falloff = 0;
                                    }
                                    else
                                    {
                                        item.falloff = 1;

                                    }
                                    ligthSetting.refreshRefProbe = true;
                                    // RefreshRefProbeVisualTex();
                                }
                                if (fo)
                                {
                                    EditorGUI.BeginChangeCheck();
                                    float falloffPower = EditorGUILayout.Slider("衰减强度: ", item.falloffPower, 0.01f, 10);
                                    if (EditorGUI.EndChangeCheck())
                                    {
                                        Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting  ref item falloffPower " + count.ToString());
                                        //Debug.Log("ligthSetting item intensity " + count.ToString());
                                        item.falloffPower = falloffPower;
                                        ligthSetting.refreshRefProbe = true;
                                    }

                                }





                                GUILayout.EndVertical();




                                GUILayout.EndHorizontal();
                                //if ((item.intensity * item.color).maxColorComponent > 1.999f)
                                //{
                                //    EditorGUILayout.HelpBox("小心灯光强度太强会穿透的!", MessageType.Warning);

                                //}
                                count += 1;
                                item.UpdateSetting();

                            }
                        }




                        //GUI.color = Color.grey;
                        GUILayout.BeginVertical(GUI.skin.box, GUILayout.Height(40));

                        if (GUILayout.Button("添加灯光", GUILayout.Height(37)))
                        //if (GUILayout.Button(texs.directionLightAdd))
                        {
                            if (ligthSetting.lightParent != null)
                            {
                                Undo.RegisterCompleteObjectUndo(ligthSetting.lightParent.gameObject, "lightParent  ref item add " + count.ToString());
                                Undo.RegisterCompleteObjectUndo(ligthSetting, "lightParent  ref item add " + count.ToString());
                                ligthSetting.reflectionLights.Add(ligthSetting.CreateRefLight());
                            }
                            else
                            {
                                EditorUtility.DisplayDialog("CustomLightTools", "请刷新下！", "好的!");

                            }


                        }
                        GUILayout.EndVertical();
                        GUI.enabled = true;

                        if (ligthSetting.reflectionLights != null)
                        {
                            if (startDel)
                            {

                                foreach (var item in ligthSetting.reflectionLights)
                                {
                                    item.isEdit = false;
                                }

                                Undo.RegisterCompleteObjectUndo(ligthSetting.lightParent.gameObject, "lightParent  ref item add " + count.ToString());
                                Undo.RegisterCompleteObjectUndo(ligthSetting, "lightParent  ref item add " + count.ToString());
                                ligthSetting.CurrentDirLightIndex = -1;
                                // Debug.Log("delete");
                                //CurrentDirLight = null;
                                //DestroyImmediate(delLight.root);
                                if (delLight.goParent != null)
                                {
                                    DestroyImmediate(delLight.mat);
                                    DestroyImmediate(delLight.go);
                                    DestroyImmediate(delLight.goParent);
                                }

                                ligthSetting.reflectionLights.Remove(delLight);
                            }
                        }


                    }






                    if (ligthSetting.DebugMode)
                    {

                        if (GUILayout.Button("Update Reflection Probe", GUILayout.Height(37)))
                        {
                            ligthSetting.CustomReflectionProbe.RenderProbe();
                            //ligthSetting.CustomReflectionProbe.RenderProbe((RenderTexture)ligthSetting.CustomReflectionProbe.bakedTexture);
                            Debug.Log("CustomReflectionProbe");
                            //AssetDatabase.SaveAssets();
                        }

                    }



                    //if (ligthSetting.onEditorRef&&ligthSetting.RefreshRealtimeSH && ligthSetting.refreshRefProbe)
                    //{
                    //    UpdateRealtimeSH();
                    //    Debug.Log("UpdateRealtimeSH");

                    //}
                }

#if UNITY_2019_1_OR_NEWER
                // EditorGUILayout.EndFoldoutHeaderGroup();
#endif

                //if (EditorGUI.EndChangeCheck())
                //{
                //    Undo.RegisterCompleteObjectUndo(ligthSetting, "reflectionProbe1");
                //    Debug.Log("tag reflectionProbe1");

                //}



            }


            EditorGUILayout.EndVertical();

            if (ligthSetting.DebugMode)
            {
                if (GUILayout.Button("delete OnSceneSaved", GUILayout.Height(37)))
                {
                    ligthSetting.DeleteOnsave();

                }

            }

            ligthSetting.UpdateLightingInternal();


            //ligthSetting.onEditorRef = ShowRefLightGizom;

            if (ligthSetting.refreshRefProbe)// && Event.current.type == EventType.Repaint)
            {
                ligthSetting.CustomReflectionProbe.RenderProbe();
                RefreshRefProbeVisualTex();
                if (ligthSetting.UseRealtimeSH)
                {
                    ligthSetting.RefreshRealtimeSH = true;
                }


                if (ligthSetting.onEditorRef && ligthSetting.RefreshRealtimeSH && ligthSetting.refreshRefProbe)
                {
                    UpdateRealtimeSH();
                    //Debug.Log("UpdateRealtimeSH");

                }

                ligthSetting.refreshRefProbe = false;

                //Debug.Log("editor");
            }


            if (ligthSetting.onEditorRef)
            {
                if (ligthSetting.lightParent == null)
                {
                    UpdateReflectionLightObject();
                }

            }



            GUILayout.Space(50);
            // 轮廓光设置
            GUI.enabled = true;
            GUI.color = Color.green;
            GUI.skin.button.fontSize = 17;
            EditorGUILayout.BeginHorizontal(EditorStyles.helpBox);
            GUILayout.Label("轮廓光设置", GUI.skin.button);
            EditorGUILayout.EndHorizontal();
            GUI.skin.button.fontSize = 0;
            GUI.color = Color.white;

            CustomLightingTool.RimProVlaue rv = ligthSetting.rimProValue;


            EditorGUI.BeginChangeCheck();
            EditorGUILayout.BeginHorizontal(EditorStyles.helpBox);// GUI.skin.box);
            bool rimProEnable = GUILayout.Toggle(ligthSetting.rimProEnable, "", GUILayout.Width(35));
            GUILayout.Label("开启轮廓光", EditorStyles.boldLabel);
            RimProDebug = GUILayout.Toggle(RimProDebug, "Debug", GUI.skin.button);
            if (RimProDebug)
            {
                EditorGUI.BeginChangeCheck();
                _DebugRimPro = GUILayout.Toggle(_DebugRimPro, "遮蔽预览", GUI.skin.button);
                if (EditorGUI.EndChangeCheck())
                {
                    Shader.SetGlobalFloat("_DebugRimPro", _DebugRimPro ? 1 : 0);
                    //if (!_DebugRimPro)
                    //{
                    //    isDebugMode = false;
                    //}
                    Repaint();
                }
            }



            if (GUILayout.Button("copy"))
            {
                Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting.rimProEnable  rimProEnable  copy");
                RIM_PRO = rimProEnable;
                RIM_PRO1 = rv.Rim1;
                RIM_PRO2 = rv.Rim2;
                RIM_DIR1 = rv.RimDir1;
                RIM_COL1 = rv.RimColor1;
                RIM_DIR2 = rv.RimDir2;
                RIM_COL2 = rv.RimColor2;
                RIM_SHAPE = rv.RimShape;
                RIM_Add = rv.RimAdd;
                RIM_MASK_CLAMP = rv.RimMaskClamp;
            }
            if (GUILayout.Button("Paste"))
            {
                Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting.rimProEnable  rimProEnable  Paste");
                rimProEnable = RIM_PRO;
                rv.Rim1 = RIM_PRO1;
                rv.Rim2 = RIM_PRO2;
                rv.RimDir1 = RIM_DIR1;
                rv.RimColor1 = RIM_COL1;
                rv.RimDir2 = RIM_DIR2;
                rv.RimColor2 = RIM_COL2;
                rv.RimShape = RIM_SHAPE;
                rv.RimAdd= RIM_Add;
                rv.RimMaskClamp = RIM_MASK_CLAMP;
            }

            if (GUILayout.Button("Rest"))
            {

                Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting.rimProEnable  rimProEnable  Rest");
                rv.Rest();
            }

            EditorGUILayout.EndHorizontal();
            //bool rimProEnable =GUILayout.Toggle(ligthSetting.rimProEnable, new GUIContent("rimProEnable"));

            if (EditorGUI.EndChangeCheck())
            {
                Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting.rimProEnable  rimProEnable");
                ligthSetting.rimProEnable = rimProEnable; ;

            }



            if (rimProEnable)
            {






                if (RimProDebug)
                {

                    //GUILayout.Label("轮廓光");
                    //GUILayout.BeginHorizontal();
                    ////OutGameShaderGUI.ShaderButton((int)OutGameShaderGUI.DebugCheckType.RimLight);
                    //EditorGUI.BeginChangeCheck();
                    //OutGameShaderGUI._DebugRimPro = GUILayout.Toggle(OutGameShaderGUI._DebugRimPro, "遮蔽", GUI.skin.button, GUILayout.Width(50));
                    //OutGameShaderGUI._DebugRimProShow = GUILayout.Toggle(OutGameShaderGUI._DebugRimProShow, "显示", GUI.skin.button, GUILayout.Width(50));
                    //if (EditorGUI.EndChangeCheck())
                    //{
                    //    Shader.SetGlobalFloat("_DebugRimPro", OutGameShaderGUI._DebugRimPro ? 1 : 0);
                    //    if (OutGameShaderGUI._DebugRimPro)
                    //    {
                    //        CommonShaderGUI.isDebugMode = true;
                    //    }
                    //    Shader.SetGlobalFloat("_DebugRimProShow", OutGameShaderGUI._DebugRimProShow ? 1 : 0);
                    //}
                    //GUILayout.EndHorizontal();


                    ////if (GUILayout.Button("仅显示轮廓光"))
                    ////{

                    ////    CommonShaderGUI.isDebugMode = !CommonShaderGUI.isDebugMode;
                    ////    CommonShaderGUI.isDebugPBRMode = CommonShaderGUI.isDebugMode;

                    ////    OutGameShaderGUI.PBRDebugMode = 1;
                    ////    OutGameShaderGUI.debugCheckInput = (int)OutGameShaderGUI.DebugCheckType.RimLight;
                    ////    if (CommonShaderGUI.isDebugPBRMode)
                    ////    {
                    ////        Shader.SetGlobalFloat("_DebugCheckIndex", (int)OutGameShaderGUI.debugCheckIndex);
                    ////        Shader.EnableKeyword("_DEBUG_CHANNEL_CHECK");
                    ////    }
                    ////    else
                    ////    {
                    ////        Shader.SetGlobalFloat("_DebugCheckIndex", -2);
                    ////        Shader.DisableKeyword("_DEBUG_CHANNEL_CHECK");
                    ////    }
                    ////}

                    //if (OutGameShaderGUI.debugCheckInput == (int)OutGameShaderGUI.DebugCheckType.RimLight || OutGameShaderGUI.debugCheckInput == (int)OutGameShaderGUI.DebugCheckType.RimMask || OutGameShaderGUI.debugCheckInput == (int)OutGameShaderGUI.DebugCheckType.RimNormal)
                    //{

                    //    GUILayout.BeginHorizontal();
                    //    //OutGameShaderGUI.ShaderButton((int)OutGameShaderGUI.DebugCheckType.RimNormal);
                    //    //OutGameShaderGUI.ShaderButton((int)OutGameShaderGUI.DebugCheckType.RimMask);
                    //    if (GUILayout.Button("遮蔽1"))
                    //    {

                    //        //CommonShaderGUI.isDebugPBRMode = !CommonShaderGUI.isDebugPBRMode;
                    //        //CommonShaderGUI.isDebugMode = !CommonShaderGUI.isDebugMode;
                    //        //OutGameShaderGUI.PBRDebugMode = 1;
                    //        OutGameShaderGUI.debugCheckInput = (int)OutGameShaderGUI.DebugCheckType.RimNormal;
                    //        if (CommonShaderGUI.isDebugPBRMode)
                    //        {
                    //            Shader.SetGlobalFloat("_DebugCheckIndex", (int)OutGameShaderGUI.debugCheckIndex);
                    //            Shader.EnableKeyword("_DEBUG_CHANNEL_CHECK");
                    //        }
                    //        //else
                    //        //{
                    //        //    Shader.SetGlobalFloat("_DebugCheckIndex", -2);
                    //        //    Shader.DisableKeyword("_DEBUG_CHANNEL_CHECK");
                    //        //}
                    //    }
                    //    if (GUILayout.Button("遮蔽2"))
                    //    {

                    //        //CommonShaderGUI.isDebugPBRMode = !CommonShaderGUI.isDebugPBRMode;
                    //        //CommonShaderGUI.isDebugMode = !CommonShaderGUI.isDebugMode;
                    //        OutGameShaderGUI.PBRDebugMode = 1;
                    //        OutGameShaderGUI.debugCheckInput = (int)OutGameShaderGUI.DebugCheckType.RimMask;
                    //        if (CommonShaderGUI.isDebugPBRMode)
                    //        {
                    //            Shader.SetGlobalFloat("_DebugCheckIndex", (int)OutGameShaderGUI.debugCheckIndex);
                    //            //Shader.EnableKeyword("_DEBUG_CHANNEL_CHECK");
                    //        }
                    //        //else
                    //        //{
                    //        //    Shader.SetGlobalFloat("_DebugCheckIndex", -2);
                    //        //    //Shader.DisableKeyword("_DEBUG_CHANNEL_CHECK");
                    //        //}
                    //    }
                    //    GUILayout.EndHorizontal();
                    //}



                }



                float maxm = rv.RimMaskClamp.y, minm = rv.RimMaskClamp.x;
                if (!RimProDebug)
                {
                    GUI.enabled = false;
                }
                EditorGUI.BeginChangeCheck();

                minm = EditorGUILayout.Slider(new GUIContent("暗部范围"), rv.RimMaskClamp.x, 0f, 0.965f);
                if ((maxm < minm + 0.036f) || (maxm == minm))
                {
                    if (minm < 0.965f)
                    {
                        maxm = minm + 0.035f;
                    }
                }
                maxm = EditorGUILayout.Slider(new GUIContent("亮部范围"), maxm, 0.035f, 1);
                if ((maxm < minm + 0.036f) || (maxm == minm))
                {
                    if (maxm > 0.035f)
                    {
                        minm = maxm - 0.035f;
                    }
                }
                GUI.enabled = true;

                float aom = EditorGUILayout.Slider(new GUIContent("AO 遮蔽"), rv.RimMaskClamp.z, 0.00f, 1);
                if (EditorGUI.EndChangeCheck())
                {
                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting.rimProEnable  RimMaskClamp");
                    rv.RimMaskClamp = new Vector4(minm, maxm, aom, rv.RimMaskClamp.w);
                }

                //EditorGUI.BeginChangeCheck();
                //Vector4 RimMaskClamp = EditorGUILayout.Vector4Field("RimMaskClamp", rv.RimMaskClamp);
                //if (EditorGUI.EndChangeCheck())
                //{
                //    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting.rimProEnable  RimMaskClamp");
                //    rv.RimMaskClamp = RimMaskClamp;

                //}


                GUI.enabled = true;

                rv.Rim1 = GUILayout.Toggle(rv.Rim1, new GUIContent("轮廓光1"));

                if (!rv.Rim1)
                {
                    GUI.enabled = false;
                }

                GUILayout.BeginHorizontal();
                EditorGUI.BeginChangeCheck();
                Vector3 RimDir1 = EditorGUILayout.Vector3Field("RimDir1", rv.RimDir1);
                ShowRimLightGizom1 = GUILayout.Toggle(ShowRimLightGizom1, new GUIContent("旋转"), GUI.skin.button);
                if (EditorGUI.EndChangeCheck())
                {
                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting.rimProEnable  x2131s1241241ssss");
                    if (ShowRimLightGizom1)
                    {
                        ShowRimLightGizom2 = false;
                        Selection.activeGameObject = ligthSetting.gameObject;
                    }
                    rv.RimDir1 = RimDir1;
                }
                GUILayout.EndHorizontal();
                EditorGUI.BeginChangeCheck();
                Color RimColor1 = EditorGUILayout.ColorField(new GUIContent("RimColor1"), rv.RimColor1, true, true, true);
                if (EditorGUI.EndChangeCheck())
                {
                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting.rimProEnable RimColor1  settingg111s");
                    rv.RimColor1 = RimColor1;
                }


                GUI.enabled = true;

                rv.Rim2 = GUILayout.Toggle(rv.Rim2, new GUIContent("轮廓光2"));

                if (!rv.Rim2)
                {
                    GUI.enabled = false;
                }
                GUILayout.BeginHorizontal();
                EditorGUI.BeginChangeCheck();
                Vector3 RimDir2 = EditorGUILayout.Vector3Field("RimDir2", rv.RimDir2);
                ShowRimLightGizom2 = GUILayout.Toggle(ShowRimLightGizom2, new GUIContent("旋转"), GUI.skin.button);
                if (EditorGUI.EndChangeCheck())
                {
                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting.rimProEnable  xs4121241241ssss");
                    if (ShowRimLightGizom2)
                    {
                        ShowRimLightGizom1 = false;
                        Selection.activeGameObject = ligthSetting.gameObject;

                    }
                    rv.RimDir2 = RimDir2;
                }
                GUILayout.EndHorizontal();


                EditorGUI.BeginChangeCheck();
                Color RimColor2 = EditorGUILayout.ColorField(new GUIContent("RimColor2"), rv.RimColor2, true, true, true);
                if (EditorGUI.EndChangeCheck())
                {
                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting.rimProEnable RimColor2  settingg111s");
                    rv.RimColor2 = RimColor2;

                }
                GUI.enabled = true;
                
                EditorGUI.BeginChangeCheck();
                rv.RimAdd = GUILayout.Toggle(rv.RimAdd, new GUIContent("风格化边缘光"));
                if (!rv.RimAdd)
                {
                    GUI.enabled = false;
                }
                
                float wide = EditorGUILayout.Slider(new GUIContent("边缘光宽度"), rv.RimShape.x, 0.0f, 1f);

                float smooth = rv.RimShape.y, smoothrange = rv.RimShape.z;
                smooth = EditorGUILayout.Slider(new GUIContent("平滑过渡"), rv.RimShape.y, 0.035f, 1f);
                if ((smooth < smoothrange + 0.036f) || (smooth == smoothrange))
                {
                    if (smoothrange < 0.965f)
                    {
                        smooth = smoothrange + 0.035f;
                    }
                }
                smoothrange = EditorGUILayout.Slider(new GUIContent("平滑过渡范围"), rv.RimShape.z,  0f, 0.965f);
                if ((smooth < smoothrange + 0.036f) || (smooth == smoothrange))
                {
                    if (smooth > 0.035f)
                    {
                        smoothrange = smooth - 0.035f;
                    }
                }
                if (EditorGUI.EndChangeCheck())
                {
                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting.rimProEnable  RimShape");
                    rv.RimShape = new Vector4(wide, smooth, smoothrange, rv.RimShape.w);
                }
                GUI.enabled = true;

            }
        }






        public bool canMoveLightUI = false;
        public Camera cam;
        public string camName="CustomLightingTool 镜面反射编辑的临时文件 请忽略！  CustomlightingTools camera for debug dont save!!! ...12154asd4a6s46fas";
       public GameObject   DebugHDRICam;
       GameObject ball1;
        GameObject ball2;
        void OnSceneGUI()
        {
            // GUI.color=Color.red;
            if(cam==null){
                DebugHDRICam= GameObject.Find(camName);
                DestroyImmediate(ball1);
                DestroyImmediate(ball2);
                DestroyImmediate(DebugHDRICam);
                if(DebugHDRICam==null){
                    DebugHDRICam=new GameObject(camName);
                    DebugHDRICam.transform.localPosition=new Vector3(299,-2999,-999);
                    // gc.hideFlags=HideFlags.HideInHierarchy;
                    DebugHDRICam.hideFlags=HideFlags.HideAndDontSave;
                    cam= DebugHDRICam.AddComponent<Camera>();
                    cam.cullingMask= 1 << 31;
                    cam.nearClipPlane=0.6f;
                    cam.farClipPlane=4f;
                    cam.orthographic =true;
                    cam.orthographicSize =1.2f;
                    cam.backgroundColor= Color.clear; 
                    cam.clearFlags=CameraClearFlags.SolidColor;
                    ball1=(GameObject)GameObject.Instantiate(ligthSetting.sphereGO1);
                    ball2=(GameObject)GameObject.Instantiate(ligthSetting.sphereGO2);
                    ball1.hideFlags=HideFlags.HideAndDontSave;
                    ball2.hideFlags=HideFlags.HideAndDontSave;
                    ball1.layer=31;
                    ball2.layer=31;
                    ball1.transform.parent=DebugHDRICam.transform;
                    ball2.transform.parent=DebugHDRICam.transform;
                    ball2.transform.localPosition=new Vector3(-1.2f,0,2);
                    ball1.transform.localPosition=new Vector3(1.2f,0,2);
                    List<GameObject> tmpLsGoOpts =new List<GameObject>();//= new List<GameObject>(ligthSetting.CustomLightObjs);
                    for (int i = 0; i < ligthSetting.CustomLightObjs.Length; i++)
                    {
                        if (ligthSetting.CustomLightObjs[i]!=null)
                        {
                            tmpLsGoOpts.Add(ligthSetting.CustomLightObjs[i]);
                        }
                    }

                    if(!tmpLsGoOpts.Contains(ball1)){
                        tmpLsGoOpts.Add(ball1);
                    }
                    if(!tmpLsGoOpts.Contains(ball2)){
                        tmpLsGoOpts.Add(ball2);
                    }
               
               

                    ligthSetting.UpdateLighting(tmpLsGoOpts.ToArray());

                }else{
                        cam=DebugHDRICam.GetComponent<Camera>();
                }

            }

            if(cam!=null){
                ball1.SetActive(true);
                ball2.SetActive(true);
                cam.transform.localRotation=Camera.current.transform.localRotation;
                cam.targetTexture=HDRI_RT;
                // cam.
                cam.Render();
                ball1.SetActive(false);
                ball2.SetActive(false);

            }
            RenderTexture trt= RenderTexture.active;
            Graphics.SetRenderTarget(HDRI_RT2);
            Graphics.Blit(HDRI_RT,HDRI_RT2,new Vector2(0.5f,1),new Vector2(0.5f,0));
            Graphics.SetRenderTarget(HDRI_RT1);
            Graphics.Blit(HDRI_RT,HDRI_RT1,new Vector2(0.5f,1),new Vector2(0,0));
            RenderTexture.active=trt;
           
           
            Handles.BeginGUI();
            if (HDIRShow)
            {
                // GUI.DrawTexture(new Rect (0,0,256,128),HDRI_RT,ScaleMode.StretchToFill,true);
                GUI.DrawTexture(new Rect (0,0,128,128),HDRI_RT1,ScaleMode.StretchToFill,true);
                GUI.DrawTexture(new Rect (128,0,128,128),HDRI_RT2,ScaleMode.StretchToFill,true);
            }
 
            if (ligthSetting.DebugMode)
            {
                GUI.color=Color.grey;
                GUI.Label(new Rect (45,128,256,128),"漫反射");
                GUI.Label(new Rect (170,128,256,128),"镜面反射");
                GUI.color=Color.white;
                // GUI.Label(new Rect (0,128,256,128),Screen.width.ToString()+"|"+Screen.height.ToString());
                // GUI.Label(new Rect (Screen.width/2,Screen.height/2,256,128),Screen.width.ToString()+"|"+Screen.height.ToString());
                // GUI.DrawTexture(new Rect (0,Screen.height-200,256,128),HDRI_RT,ScaleMode.StretchToFill,true);
                // GUI.DrawTexture(new Rect (Screen.width/2,Screen.height/2,256,128),Texture2D.blackTexture,ScaleMode.StretchToFill);
            }
            
            Handles.EndGUI();

            targetObj = ligthSetting.gameObject;
            if (Selection.activeGameObject != targetObj)
            {
                foreach (var item in ligthSetting.AmbientLights)
                {
                    item.isEdit = false;

                }
                ligthSetting.CurrentDirLightIndex = -1;

                //Debug.Log("ligthSetting.CurrentDirLightIndex: = -1");
                canMoveLightUI = false;
                return;
            }

            if (ShowAmbientLightGizom && targetObj != null)
            {
                //InitSenceGUI();
                Vector3 pos = targetObj.transform.position;

                //pos = Camera.current.ScreenToWorldPoint(new Vector3( Math.Min(Screen.width, Screen.width/2 ), Math.Min( Screen.height,200),100));
                ligthSetting.lightHandlePos = new Vector2(Mathf.Min(Screen.width, ligthSetting.lightHandlePos.x), Mathf.Min(Screen.height, ligthSetting.lightHandlePos.y));
                ligthSetting.lightHandlePos = new Vector2(Mathf.Max(0, ligthSetting.lightHandlePos.x), Mathf.Max(0, ligthSetting.lightHandlePos.y));

                pos = Camera.current.ScreenToWorldPoint(new Vector3(ligthSetting.lightHandlePos.x, ligthSetting.lightHandlePos.y, 100));
                //Debug.Log(pos);


                Handles.CubeHandleCap(0, pos, Quaternion.identity, 1.5f, EventType.Repaint);



                if (selectionAmbientLitIndex == 0)
                {
                    int count = 0;
                    foreach (var item in ligthSetting.AmbientLights)
                    {

                        Vector3 arowPos = pos - item.direction * HandleUtility.GetHandleSize(pos) * showDirLightGizomRate;
                        //arowPos *= 1.5f;
                        //Handles.Label(arowPos, "light",GUI.skin.box);
                        Handles.BeginGUI();
                        Color col = item.color * item.intensity;
                        col.a = 1;
                        if (!item.isEdit)
                        {
                            col.a = 0.40f;
                        }
                        GUI.color = col;
                        Vector2 pot = Camera.current.WorldToScreenPoint(arowPos - item.direction * 2);
                        Rect rtt = new Rect(pot.x, Screen.height - pot.y, 60, 20);
                        if (GUI.Button(rtt, "Light_" + count.ToString()))
                        {
                            item.isEdit = true;
                            // CurrentDirLight = item;
                            ligthSetting.CurrentDirLightIndex = count;
                            selectionAmbientLitIndex = 0;
                            Selection.activeObject = targetObj;
                            //Debug.Log("isEdit: " + count.ToString() + "ligthSetting.CurrentDirLightIndex: "+ ligthSetting.CurrentDirLightIndex.ToString());
                            // InitSenceGUI();
                            for (int i = 0; i < ligthSetting.AmbientLights.Count; i++)
                            {
                                if (ligthSetting.CurrentDirLightIndex != i)
                                {
                                    ligthSetting.AmbientLights[i].isEdit = false;
                                }
                            }

                        }


                        count += 1;
                    }


                    Rect rt = new Rect(pos.x, Screen.height - pos.y, 60, 20);
                    GUI.color = Color.white;
                    rt.x = ligthSetting.lightHandlePos.x - 25;
                    rt.y = Screen.height - ligthSetting.lightHandlePos.y + 70;
                    rt.width = 50;
                    GUI.color = Color.red;

                    if (GUI.Button(rt, "退出"))
                    {
                        ShowAmbientLightGizom = false;
                    }
                    GUI.color = new Color(1, 1, 1, 0.3f);
                    rt.x -= 150;
                    rt.y -= 30;
                    rt.width = 100;
                    rt.height = 60;
                    //GUI.Box(rt,"x");

                    if (Event.current.control)
                    {
                        canMoveLightUI = GUI.Toggle(rt, canMoveLightUI, "Move", GUI.skin.button);

                    }
                    //if (GUI.Button(rt, "Move"))
                    //{
                    //    Debug.Log("1xxx pressed!");
                    //    // = false;
                    //    //canmove

                    //}
                    GUI.color = Color.white;

                    //Debug.Log(Event.current.type);
                    //if (Event.current.type == EventType.MouseUp)
                    //{
                    //    //canMoveLightUI = false;
                    //    Debug.Log("MouseUp");

                    //}

                    //if (rt.Contains(Event.current.mousePosition))
                    //{
                    //    if (Event.current.type == EventType.MouseDown)
                    //    {
                    //        //canMoveLightUI = true;
                    //        Debug.Log("MouseDown");
                    //    }

                    //}

                    if (canMoveLightUI || Event.current.control)
                    {
                        //Debug.Log(lightHandlePos);
                        ligthSetting.lightHandlePos.x += Event.current.delta.x;
                        ligthSetting.lightHandlePos.y -= Event.current.delta.y;
                        ligthSetting.lightHandlePos.y = Mathf.Max(ligthSetting.lightHandlePos.y, 50);
                        ligthSetting.lightHandlePos.x = Mathf.Max(ligthSetting.lightHandlePos.x, 50);
                    }

                    if (Event.current.type == EventType.MouseLeaveWindow || Event.current.type == EventType.MouseUp || Event.current.type == EventType.MouseDown || Event.current.type == EventType.KeyDown)
                    {
                        canMoveLightUI = false;
                        //Debug.Log("canMoveLightUI");

                    }

                    Handles.EndGUI();





                    count = 0;
                    foreach (var item in ligthSetting.AmbientLights)
                    {
                        if (count == ligthSetting.CurrentDirLightIndex)
                        {
                            //EditorGUI.BeginChangeCheck();
                            CustomLightingTool.DiffuseLight CurrentDirLight = ligthSetting.AmbientLights[ligthSetting.CurrentDirLightIndex];
                            //if (CurrentDirLight.rotation.x == 0 && CurrentDirLight.rotation.y == 0 && CurrentDirLight.rotation.z == 0 && CurrentDirLight.rotation.w == 0)
                            //{
                            //    CurrentDirLight.rotation = Quaternion.identity;
                            //}
                            Quaternion rotation = Handles.RotationHandle(CurrentDirLight.rotation, pos);
                            Vector3 direction = CurrentDirLight.rotation * Vector3.forward;
                            if (Event.current.type == EventType.Used)
                            {
                                Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting item rotate1 " + count.ToString());
                                //Debug.Log("ligthSetting item rotate1 " + count.ToString());
                                CurrentDirLight.rotation = rotation;
                                CurrentDirLight.direction = direction;

                            }


                            Color col = CurrentDirLight.color * CurrentDirLight.intensity;
                            col.a = 1;
                            Handles.color = col;
                            Handles.ConeHandleCap(0, pos - CurrentDirLight.direction * HandleUtility.GetHandleSize(pos) * showDirLightGizomRate, CurrentDirLight.rotation, HandleUtility.GetHandleSize(pos) * 0.8f, EventType.Repaint);
                            //Tools.current = Tool.None;


                        }
                        else if (ShowElseLightGizom)
                        {
                            Color col = item.color * item.intensity;
                            col.a = 0.35f;
                            Handles.color = col;
                            Handles.ConeHandleCap(0, pos - item.direction * HandleUtility.GetHandleSize(pos) * showDirLightGizomRate, item.rotation, HandleUtility.GetHandleSize(pos) * 0.6f, EventType.Repaint);

                        }
                        count += 1;
                    }

                }
                else
                {
                    //foreach (var item in ligthSetting.reflectionLights)
                    //{
                    //    if (item == CurrentRefLight)
                    //    {
                    //        //EditorGUI.BeginChangeCheck();

                    //        CurrentRefLight.rotation = Handles.RotationHandle(CurrentRefLight.rotation, pos);
                    //        CurrentRefLight.direction = CurrentRefLight.rotation * Vector3.forward;

                    //        //CurrentRefLight.root.transform.rotation = CurrentRefLight.rotation;
                    //        Color col = CurrentRefLight.color * CurrentRefLight.intensity;
                    //        col.a = 1;
                    //        Handles.color = col;
                    //        Handles.ConeHandleCap(0, pos - CurrentRefLight.direction, CurrentRefLight.rotation, HandleUtility.GetHandleSize(pos), EventType.Repaint);
                    //        //if (EditorGUI.EndChangeCheck())
                    //        //{
                    //        //    Undo.RegisterCompleteObjectUndo(ligthSetting, "rotate");

                    //        //}
                    //    }
                    //    //else
                    //    //{
                    //    //    Color col = item.color * item.intensity;
                    //    //    col.a = 0.63f;
                    //    //    //Handles.color = col;
                    //    //   // Handles.ConeHandleCap(0, pos - item.direction, item.rotation, HandleUtility.GetHandleSize(pos) * 0.6f, EventType.Repaint);

                    //    //}
                    //}
                }





                ligthSetting.UpdateLightingInternal();
            }
            else
            {
                //ClearSenceGUI();
            }


            if (ShowRefLightGizom && ligthSetting.onEditorRef && targetObj != null)
            {
                Vector3 pos = targetObj.transform.position;

                ligthSetting.lightHandlePos = new Vector2(Mathf.Min(Screen.width, ligthSetting.lightHandlePos.x), Mathf.Min(Screen.height, ligthSetting.lightHandlePos.y));
                pos = Camera.current.ScreenToWorldPoint(new Vector3(ligthSetting.lightHandlePos.x, ligthSetting.lightHandlePos.y, 100));
                Handles.CubeHandleCap(0, pos, Quaternion.identity, 1.5f, EventType.Repaint);

                if (selectionRefLitIndex == 0)
                {
                    int count = 0;
                    foreach (var item in ligthSetting.reflectionLights)
                    {

                        Vector3 arowPos = pos - item.direction * HandleUtility.GetHandleSize(pos) * showDirLightGizomRate;
                        //arowPos *= 1.5f;
                        //Handles.Label(arowPos, "light",GUI.skin.box);
                        Handles.BeginGUI();
                        Color col = item.color * item.Exposure;
                        col.a = 1;
                        if (!item.isEdit)
                        {
                            col.a = 0.40f;
                        }
                        GUI.color = col;
                        Vector2 pot = Camera.current.WorldToScreenPoint(arowPos - item.direction * 2);
                        Rect rtt = new Rect(pot.x, Screen.height - pot.y, 60, 20);
                        if (GUI.Button(rtt, "Light_" + count.ToString()))
                        {
                            item.isEdit = true;
                            // CurrentDirLight = item;
                            ligthSetting.CurrentRefLightIndex = count;
                            selectionAmbientLitIndex = 0;
                            Selection.activeObject = targetObj;
                            //Debug.Log("isEdit: " + count.ToString() + "ligthSetting.CurrentDirLightIndex: "+ ligthSetting.CurrentDirLightIndex.ToString());
                            // InitSenceGUI();
                            for (int i = 0; i < ligthSetting.reflectionLights.Count; i++)
                            {
                                if (ligthSetting.CurrentRefLightIndex != i)
                                {
                                    ligthSetting.reflectionLights[i].isEdit = false;
                                }
                            }
                        }

                        count += 1;
                    }


                    Rect rt = new Rect(pos.x, Screen.height - pos.y, 60, 20);
                    GUI.color = Color.white;
                    rt.x = ligthSetting.lightHandlePos.x - 25;
                    rt.y = Screen.height - ligthSetting.lightHandlePos.y + 70;
                    rt.width = 50;
                    GUI.color = Color.red;

                    if (GUI.Button(rt, "退出"))
                    {
                        ShowRefLightGizom = false;
                    }
                    GUI.color = new Color(1, 1, 1, 0.3f);
                    rt.x -= 150;
                    rt.y -= 30;
                    rt.width = 100;
                    rt.height = 60;
                    //GUI.Box(rt,"x");

                    if (Event.current.control)
                    {
                        canMoveLightUI = GUI.Toggle(rt, canMoveLightUI, "Move", GUI.skin.button);

                    }

                    GUI.color = Color.white;

                    if (canMoveLightUI || Event.current.control)
                    {
                        //Debug.Log(lightHandlePos);
                        ligthSetting.lightHandlePos.x += Event.current.delta.x;
                        ligthSetting.lightHandlePos.y -= Event.current.delta.y;
                        ligthSetting.lightHandlePos.y = Mathf.Max(ligthSetting.lightHandlePos.y, 50);
                        ligthSetting.lightHandlePos.x = Mathf.Max(ligthSetting.lightHandlePos.x, 50);
                    }

                    if (Event.current.type == EventType.MouseLeaveWindow || Event.current.type == EventType.MouseUp || Event.current.type == EventType.MouseDown || Event.current.type == EventType.KeyDown)
                    {
                        canMoveLightUI = false;
                        //Debug.Log("canMoveLightUI");

                    }

                    Handles.EndGUI();



                    count = 0;
                    foreach (var item in ligthSetting.reflectionLights)
                    {
                        if (count == ligthSetting.CurrentRefLightIndex)
                        {
                            CustomLightingTool.ReflectionLight CurrentDirLight = ligthSetting.reflectionLights[ligthSetting.CurrentRefLightIndex];
                            Quaternion rotation = Handles.RotationHandle(CurrentDirLight.rotation, pos);
                            Vector3 direction = CurrentDirLight.rotation * Vector3.forward;
                            if (Event.current.type == EventType.Used)
                            {
                                Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting item rotate1 " + count.ToString());
                                //Debug.Log("ligthSetting item rotate1 " + count.ToString());
                                CurrentDirLight.rotation = rotation;
                                CurrentDirLight.direction = direction;
                                ligthSetting.refreshRefProbe = true;

                            }


                            Color col = CurrentDirLight.color * CurrentDirLight.Exposure;
                            col.a = 1;
                            Handles.color = col;
                            Handles.ConeHandleCap(0, pos - CurrentDirLight.direction * HandleUtility.GetHandleSize(pos) * showDirLightGizomRate, Quaternion.LookRotation(item.direction, Vector3.up), HandleUtility.GetHandleSize(pos) * 0.8f, EventType.Repaint);
                            //Tools.current = Tool.None;


                        }
                        else if (ShowElseLightGizom)
                        {
                            Color col = item.color * item.Exposure;
                            col.a = 0.35f;
                            Handles.color = col;
                            Handles.ConeHandleCap(10, pos - item.direction * HandleUtility.GetHandleSize(pos) * showDirLightGizomRate, Quaternion.LookRotation(item.direction, Vector3.up), HandleUtility.GetHandleSize(pos) * 0.6f, EventType.Repaint);

                        }
                        count += 1;
                        item.UpdateSetting();
                    }

                }
                else
                {

                }
            }






            CustomLightingTool.RimProVlaue rv = ligthSetting.rimProValue;
            if (ligthSetting.rimProEnable && (ShowRimLightGizom1 || ShowRimLightGizom2))
            {
                Vector3 dir = rv.RimDir1;
                if (ShowRimLightGizom2)
                {
                    dir = rv.RimDir2;
                }
                if (ShowRimLightGizom1)
                {
                    dir = rv.RimDir1;
                }


                Vector3 pos = SceneView.lastActiveSceneView.camera.ScreenToWorldPoint(new Vector3(Screen.width / 1.3f, Screen.height / 3.5f, SceneView.lastActiveSceneView.camera.nearClipPlane + 10));
                Vector3 value1 = new Vector3(-dir.x, -dir.y, dir.z);
                Vector3 worldDir = SceneView.lastActiveSceneView.camera.transform.rotation * value1;
                Quaternion rot = Quaternion.FromToRotation(Vector3.forward, worldDir);
                rot = Handles.RotationHandle(rot, pos);
                Vector3 newlocalDir = Quaternion.Inverse(SceneView.lastActiveSceneView.camera.transform.rotation) * rot * Vector3.forward;

                if (Event.current.type == EventType.Used)
                {
                    Undo.RegisterCompleteObjectUndo(ligthSetting, "ligthSetting RimProVlaue rotate1111 ");
                    dir = new Vector4(-newlocalDir.x, -newlocalDir.y, newlocalDir.z);
                }
                if (ShowRimLightGizom2)
                {
                    Handles.color = rv.RimColor2;
                    Handles.BeginGUI();
                    GUI.Button(new Rect(Screen.width / 2, Screen.height / 2, 120, 40), "Rim_pro 方向1", GUI.skin.label);
                    Handles.EndGUI();

                }
                if (ShowRimLightGizom1)
                {
                    Handles.color = rv.RimColor1;
                    Handles.BeginGUI();
                    GUI.Button(new Rect(Screen.width / 2, Screen.height / 2, 120, 40), "Rim_pro 方向2", GUI.skin.label);
                    Handles.EndGUI();

                }
                Vector3 capPos = pos + (rot * Vector3.back).normalized * 2;
                Handles.ConeHandleCap(0, capPos, rot, HandleUtility.GetHandleSize(capPos), EventType.Repaint);


                if (ShowRimLightGizom2)
                {
                    rv.RimDir2 = dir;
                }
                if (ShowRimLightGizom1)
                {
                    rv.RimDir1 = dir;
                }

                if (Event.current.type == EventType.Used)
                {
                    ligthSetting.UpdateLightingInternal();
                }

            }
        }



        public void GetSHFromeCubemap(Cubemap cp)
        {
            if (cp != null)
            {
                SHEncoding sh = new SHEncoding();
                AssetUtil.setReadable(cp, true);
                SHUtil.BakeSH(ref sh, cp, 0, false);
                sh.setBuffer();

                for (int i = 0; i < 9; i++)
                {
                    ligthSetting.SH2[0, i] = sh.cBuffer[i].x;
                    ligthSetting.SH2[1, i] = sh.cBuffer[i].y;
                    ligthSetting.SH2[2, i] = sh.cBuffer[i].z;
                    ligthSetting.SHV2[i] = sh.cBuffer[i];

                }
                //Debug.Log("GetSHFromeCubemap");
                //Debug.Log(sh.cBuffer[0]);

            }
        }


        //储存Bake Realtime-probe 直接copy到磁盘。在刷新mipmap 重新导入
        public void BakeRealtimeProbe()
        {

            if (ligthSetting.CustomReflectionProbe != null)
            {
                ligthSetting.CustomReflectionProbe.cullingMask = 1 << 31;
            }


            //#if UNITY_2019_1_OR_NEWER
            //            isBakeEXR = true;
            //#else

            //            isBakeEXR = true;

            //#endif

            if (ligthSetting.ForceBakeFormat)
            {
                ligthSetting.isBakeEXR = ligthSetting.isBakeEXR;
            }


            if (ligthSetting.isBakeEXR)
            {
                ligthSetting.CustomReflectionTexture = GetRealtimeCubemapEXR();


            }
            else
            {

                ligthSetting.CustomReflectionTexture = GetRealtimeCubemapAsset();
                if (ligthSetting.CustomReflectionTexture == null)
                {
                    EditorUtility.DisplayDialog("CustomLightTools", "保存失败！ 请在镜面反射编辑模式 刷新下试试", "好的!");
                    return;
                }


            }
            ligthSetting.CustomReflectionProbe.bakedTexture = ligthSetting.CustomReflectionTexture;
            ligthSetting.CustomReflectionProbe.customBakedTexture = ligthSetting.CustomReflectionTexture;

        }


        public Cubemap GetRealtimeCubemapAsset()
        {
            if (ligthSetting.CustomReflectionProbe.realtimeTexture != null && ligthSetting.onEditorRef)
            {

                RenderTexture lastrt = RenderTexture.active;
                int width = ligthSetting.CustomReflectionProbe.realtimeTexture.width;
                Cubemap newCubemap = new Cubemap(width, TextureFormat.RGBAHalf, true);
                newCubemap.filterMode = FilterMode.Trilinear;
                int mipmapCount = 1;
#if UNITY_2019_1_OR_NEWER
                mipmapCount = ligthSetting.CustomReflectionProbe.realtimeTexture.mipmapCount;
#else
                mipmapCount = (int)Mathf.Log(ligthSetting.CustomReflectionProbe.realtimeTexture.width, 2) + 1;
#endif

                //Debug.Log(mipmapCount);
                //Debug.Log((int)Mathf.Log(ligthSetting.CustomReflectionProbe.realtimeTexture.width, 2));

                for (int i = 0; i < mipmapCount; i++)
                {
                    int widthmip = (int)Mathf.Pow(2, mipmapCount - i - 1);
                    //Debug.Log(i);
                    // Debug.Log(widthmip);

                    RenderTexture mipRT = new RenderTexture(widthmip * 6, widthmip, 0, RenderTextureFormat.ARGBHalf);
                    mipRT.wrapMode = TextureWrapMode.Repeat;
                    mipRT.filterMode = FilterMode.Trilinear;
                    ligthSetting.CubemapToEXR.SetFloat("_LodNum", i);
                    ligthSetting.CubemapToEXR.DisableKeyword("_FLIP_Y_FOR_EXR");
                    ligthSetting.CubemapToEXR.EnableKeyword("_GET_MIPMAP");
                    RenderTexture.active = mipRT;
                    Graphics.Blit(ligthSetting.CustomReflectionProbe.realtimeTexture, mipRT, ligthSetting.CubemapToEXR);
                    if (i == 1)
                    {
                        ligthSetting.CustomReflectionRT = mipRT;
                    }
                    Texture2D mipTex = new Texture2D(widthmip * 6, widthmip, TextureFormat.RGBAHalf, false);
                    mipTex.wrapMode = TextureWrapMode.Repeat;
                    mipTex.filterMode = FilterMode.Trilinear;
                    mipTex.ReadPixels(new Rect(0, 0, widthmip * 6, widthmip), 0, 0, false);
                    mipTex.Apply();
                    // Debug.Log(newCubemap.GetPixels((CubemapFace)1, i).Length);
                    for (int ic = 0; ic < 6; ic++)
                    {
                        Color[] cols = mipTex.GetPixels(ic * widthmip, 0, widthmip, widthmip);
                        newCubemap.SetPixels(cols, (CubemapFace)ic, i);
                    }
                    //PanoramicToCubemapRuntimeConverter.ConvertPanoramaTexture(newCubemap, i, mipTex, widthmip);
                    RenderTexture.active = lastrt;
                    mipRT.Release();
                    DestroyImmediate(mipTex);
                }

                RenderTexture.active = lastrt;
                newCubemap.Apply(false);



                //存到哪里
                string filename = "";
                string filenameNoExtent = "";
                Scene scene = UnityEngine.SceneManagement.SceneManager.GetActiveScene();
                if (!File.Exists(scene.path))
                {
                    EditorUtility.DisplayDialog("CustomLightTools", "请先存下场景在bake！", "好的!");
                    return null;
                }
                else
                {

                    bool createNew = false;

                    //没有存储或者丢失的情况 创建一个新的
                    if (ligthSetting.CustomReflectionTexture != null)
                    {
                        if (!AssetDatabase.GetAssetPath(ligthSetting.CustomReflectionTexture).Contains("."))
                        {
                            createNew = true;
                        }
                        else
                        {
                            filename = Application.dataPath.Replace("/Assets", "") + "/" + AssetDatabase.GetAssetPath(ligthSetting.CustomReflectionTexture);
                            filename = filename.Replace(".exr", ".asset");

                        }
                    }
                    else
                    {
                        createNew = true;
                    }


                    //Debug.LogError(createNew);
                    //Debug.LogError("xxx:"+filename);

                    //创建新的时候 检测是否冲突文件
                    if (createNew)
                    {
                        filename = scene.path;
                        filename = Path.GetDirectoryName(filename).Replace("\\", "/") + "/" + scene.name;
                        filename = Application.dataPath.Replace("/Assets", "") + "/" + filename + "/";
                        //Debug.Log(filename);
                        if (!Directory.Exists(filename))
                        {
                            Directory.CreateDirectory(filename);
                        }
                        filenameNoExtent = filename + CUBEMAPNAME + ".asset";

                        //filename = filename + CUBEMAPNAME + ".asset";
                        int cho = 0;
                        if (File.Exists(filenameNoExtent))
                        {
                            // "覆盖!", "新建"  "取消"  0,  1, 2
                            cho = EditorUtility.DisplayDialogComplex("CustomLightTools", "是否覆盖已存在Cubemap!    (不知道什么情况，可以找@fengxzeng)" + filenameNoExtent, "覆盖!", "取消", "新建");
                            //Debug.Log("Exists:" + filenameNoExtent);
                        }
                        else
                        {
                            //Debug.Log("! Exists:" + filenameNoExtent);
                            filename = filenameNoExtent;

                        }

                        //新建
                        if (cho == 2)
                        {
                            //Debug.LogError("create new :" + filenameNoExtent);

                            bool next = true;
                            int count = 0;
                            while (next)
                            {
                                if (File.Exists(filenameNoExtent))
                                {
                                    filenameNoExtent = filename + CUBEMAPNAME + count.ToString() + ".asset";
                                    //Debug.Log("filename Exists:  " + newfilename);
                                    count += 1;
                                }
                                else
                                {
                                    filename = filenameNoExtent;
                                    //Debug.Log("filename not Exists:  " + newfilename);

                                    next = false;
                                }
                                if (count > 15)
                                {

                                    EditorUtility.DisplayDialog("CustomLightTools", "请联系TA(@fengxzeng)相关同事清理下ReflectionProbe 数量太多了1", "好的!");
                                    return null;
                                }

                                if (count > 5)
                                {
                                    next = false;
                                    EditorUtility.DisplayDialog("CustomLightTools", "请联系TA(@fengxzeng)相关同事清理下ReflectionProbe 数量太多了", "好的!");
                                    //return null;
                                }
                            }
                        }
                        else if (cho == 1)
                        {
                            return null;
                        }
                        else if (cho == 0)
                        {
                            filename = filenameNoExtent;

                        }
                    }
                    //Debug.LogError("xxx new :" + filename);

                }


                filename = "Assets/" + filename.Replace(Application.dataPath + "/", "");
                Log("save: " + filename);
                //Debug.LogError("save: " + filename);
                MarkCubemapReadable(newCubemap, false);
                AssetDatabase.CreateAsset(newCubemap, filename);
                AssetDatabase.Refresh();
                return newCubemap;

            }
            return null;

        }


        public Cubemap GetRealtimeCubemapEXR()
        {
            if (ligthSetting.CustomReflectionProbe.realtimeTexture != null && ligthSetting.onEditorRef)
            {
                RenderTexture lastrt = RenderTexture.active;
                int width = ligthSetting.CustomReflectionProbe.realtimeTexture.width;
                Texture2D newCubemapExr = new Texture2D(width * 6, width, TextureFormat.RGBAHalf, true);
                //int widthmip = (int)Mathf.Pow(2, ligthSetting.CustomReflectionProbe.realtimeTexture.mipmapCount - i - 1);

                int widthmip = ligthSetting.CustomReflectionProbe.realtimeTexture.width;
                RenderTexture mipRT = new RenderTexture(widthmip * 6, widthmip, 0, RenderTextureFormat.ARGBHalf);
                mipRT.wrapMode = TextureWrapMode.Repeat;
                mipRT.filterMode = FilterMode.Trilinear;
                ligthSetting.CubemapToEXR.SetFloat("_LodNum", 0);
                ligthSetting.CubemapToEXR.EnableKeyword("_FLIP_Y_FOR_EXR");
                ligthSetting.CubemapToEXR.EnableKeyword("_GET_MIPMAP");
                RenderTexture.active = mipRT;
                Graphics.Blit(ligthSetting.CustomReflectionProbe.realtimeTexture, mipRT, ligthSetting.CubemapToEXR);

                newCubemapExr.ReadPixels(new Rect(0, 0, widthmip * 6, widthmip), 0, 0, false);
                newCubemapExr.Apply(false);
                RenderTexture.active = lastrt;
                mipRT.Release();




                //存到哪里
                string filename = "";
                string filenameNoExtent = "";
                Scene scene = UnityEngine.SceneManagement.SceneManager.GetActiveScene();
                if (!File.Exists(scene.path))
                {
                    EditorUtility.DisplayDialog("CustomLightTools", "请先存下场景在bake！", "好的!");
                    return null;
                }
                else
                {

                    bool createNew = false;

                    //没有存储或者丢失的情况 创建一个新的
                    if (ligthSetting.CustomReflectionTexture != null)
                    {
                        if (!AssetDatabase.GetAssetPath(ligthSetting.CustomReflectionTexture).Contains("."))
                        {
                            createNew = true;
                        }
                        else
                        {
                            filename = Application.dataPath.Replace("/Assets", "") + "/" + AssetDatabase.GetAssetPath(ligthSetting.CustomReflectionTexture);
                            filename = filename.Replace(".asset", ".exr");

                        }
                    }
                    else
                    {
                        createNew = true;
                    }


                    //Debug.LogError(createNew);
                    //Debug.LogError("xxx:"+filename);

                    //创建新的时候 检测是否冲突文件
                    if (createNew)
                    {
                        filename = scene.path;
                        filename = Path.GetDirectoryName(filename).Replace("\\", "/") + "/" + scene.name;
                        filename = Application.dataPath.Replace("/Assets", "") + "/" + filename + "/";
                        //Debug.Log(filename);
                        if (!Directory.Exists(filename))
                        {
                            Directory.CreateDirectory(filename);
                        }
                        filenameNoExtent = filename + CUBEMAPNAME + ".exr";

                        //filename = filename + CUBEMAPNAME + ".asset";
                        int cho = 0;
                        if (File.Exists(filenameNoExtent))
                        {
                            // "覆盖!", "新建"  "取消"  0,  1, 2
                            cho = EditorUtility.DisplayDialogComplex("CustomLightTools", "是否覆盖已存在Cubemap!    (不知道什么情况，可以找@fengxzeng)" + filenameNoExtent, "覆盖!", "取消", "新建");
                            //Debug.Log("Exists:" + filenameNoExtent);
                        }
                        else
                        {
                            //Debug.Log("! Exists:" + filenameNoExtent);
                            filename = filenameNoExtent;

                        }

                        //新建
                        if (cho == 2)
                        {
                            //Debug.LogError("create new :" + filenameNoExtent);

                            bool next = true;
                            int count = 0;
                            while (next)
                            {
                                if (File.Exists(filenameNoExtent))
                                {
                                    filenameNoExtent = filename + CUBEMAPNAME + count.ToString() + ".exr";
                                    //Debug.Log("filename Exists:  " + newfilename);
                                    count += 1;
                                }
                                else
                                {
                                    filename = filenameNoExtent;
                                    //Debug.Log("filename not Exists:  " + newfilename);

                                    next = false;
                                }
                                if (count > 15)
                                {

                                    EditorUtility.DisplayDialog("CustomLightTools", "请联系TA(@fengxzeng)相关同事清理下ReflectionProbe 数量太多了1", "好的!");
                                    return null;
                                }

                                if (count > 5)
                                {
                                    next = false;
                                    EditorUtility.DisplayDialog("CustomLightTools", "请联系TA(@fengxzeng)相关同事清理下ReflectionProbe 数量太多了", "好的!");
                                    //return null;
                                }
                            }
                        }
                        else if (cho == 1)
                        {
                            return null;
                        }
                        else if (cho == 0)
                        {
                            filename = filenameNoExtent;

                        }
                    }
                    //Debug.LogError("xxx new :" + filename);

                }




                Log("save: " + filename);
                byte[] byts = newCubemapExr.EncodeToEXR();
                System.IO.File.WriteAllBytes(filename, byts);
                AssetDatabase.Refresh();

                string cps = "Assets" + filename.Replace(Application.dataPath, "");

                //Debug.Log("filename: " + filename);
                //Debug.Log("cps: " + cps);
                //Debug.Log("Application.dataPath: " + Application.dataPath);
                TextureImporterSettings importerset = new TextureImporterSettings();
                TextureImporter importer = (TextureImporter)TextureImporter.GetAtPath(cps);

                importerset.wrapMode = TextureWrapMode.Clamp;
                importerset.textureType = TextureImporterType.Default;
                importerset.mipmapEnabled = false;
                //importer.maxTextureSize = 2048;
                importerset.textureShape = TextureImporterShape.TextureCube;
                importerset.generateCubemap = TextureImporterGenerateCubemap.AutoCubemap;
                importerset.cubemapConvolution = TextureImporterCubemapConvolution.Specular;
                importerset.filterMode = FilterMode.Trilinear;
                importerset.seamlessCubemap = true;
                importerset.npotScale = TextureImporterNPOTScale.ToNearest;
                importerset.mipmapEnabled = true;
                importerset.sRGBTexture = true;

                //Debug.Log(importerset);
                //Debug.Log(importer);
                importer.SetTextureSettings(importerset);
                EditorUtility.SetDirty(importer);
                importer.SaveAndReimport();

                Cubemap CP = AssetDatabase.LoadAssetAtPath<Cubemap>(cps.Replace("/", "\\"));
                //Debug.Log("Cubemapfn : " + cps);
                //Debug.Log("Cubemap :  " + CP.name);

                return CP;

            }
            return null;

        }



        public Cubemap GetRealtimeCubemapMip(int mipNum)
        {
            if (ligthSetting.CustomReflectionProbe.realtimeTexture != null && ligthSetting.onEditorRef)
            {
                RenderTexture lastrt = RenderTexture.active;
                int width = ligthSetting.CustomReflectionProbe.realtimeTexture.width;

                Cubemap newCubemapx = new Cubemap(width, TextureFormat.RGBAHalf, true);

                int widthmip = (int)Mathf.Pow(2, newCubemapx.mipmapCount - mipNum - 1);
                DestroyImmediate(newCubemapx);
                Cubemap newCubemap = new Cubemap(widthmip, TextureFormat.RGBAHalf, true);
                RenderTexture mipRT = new RenderTexture(widthmip * 6, widthmip, 0, RenderTextureFormat.ARGBHalf);
                mipRT.wrapMode = TextureWrapMode.Repeat;
                mipRT.filterMode = FilterMode.Trilinear;
                ligthSetting.CubemapToEXR.SetFloat("_LodNum", mipNum);
                ligthSetting.CubemapToEXR.DisableKeyword("_FLIP_Y_FOR_EXR");
                ligthSetting.CubemapToEXR.EnableKeyword("_GET_MIPMAP");
                RenderTexture.active = mipRT;
                Graphics.Blit(ligthSetting.CustomReflectionProbe.realtimeTexture, mipRT, ligthSetting.CubemapToEXR);

                Texture2D mipTex = new Texture2D(widthmip * 6, widthmip, TextureFormat.RGBAHalf, false);
                mipTex.wrapMode = TextureWrapMode.Repeat;
                mipTex.filterMode = FilterMode.Trilinear;
                mipTex.ReadPixels(new Rect(0, 0, widthmip * 6, widthmip), 0, 0, false);
                mipTex.Apply();
                for (int ic = 0; ic < 6; ic++)
                {
                    Color[] cols = mipTex.GetPixels(ic * widthmip, 0, widthmip, widthmip);
                    newCubemap.SetPixels(cols, (CubemapFace)ic);
                }

                //PanoramicToCubemapRuntimeConverter.ConvertPanoramaTexture(newCubemap, i, mipTex, widthmip);
                RenderTexture.active = lastrt;
                mipRT.Release();
                DestroyImmediate(mipTex);

                newCubemap.Apply(false);
                //string filename = AssetDatabase.GetAssetPath(ligthSetting.CustomReflectionProbe.bakedTexture);
                //AssetDatabase.CreateAsset(newCubemap, filename.Replace(".exr", "cbx.asset"));
                return newCubemap;

            }
            return null;
        }




        public void UpdateRealtimeSH()
        {
            ligthSetting.SHRealtimeCubemap = GetRealtimeCubemapMip(ligthSetting.UpdateRealtimeSHMipmap);
            if (ligthSetting.SHRealtimeCubemap != null)
            {
                GetSHFromeCubemap(ligthSetting.SHRealtimeCubemap);
            }
            // Debug.Log("REaltime sh");
        }

        public void Log(string test)
        {

            if (ligthSetting.DebugMode)
            {
                Debug.Log(test);
            }
        }


        public void OnChangeReflectionProbeResolution(object res)
        {

            if (ligthSetting.CustomReflectionProbe != null)
            {
                ligthSetting.CustomReflectionProbe.resolution = (int)res;
                ligthSetting.refreshRefProbe = true;
            }
        }


    }
}
#endif