using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;

namespace PBRTools
{
    public class PBRScriptImporter : AssetPostprocessor
    {

        [UnityEditor.Callbacks.DidReloadScripts()]
        private static void OnScriptReload()
        {
            SceneToolListener.OnScriptReload();
        }
    }

    public static class SceneToolListener
    {
        private static SceneTool ms_sceneTool = null;

        static SceneToolListener()
        {
            EditorApplication.playModeStateChanged += playModeStateChanged;
            EditorApplication.update += Update;
        }

        public static void OnScriptReload()
        {
            if (ms_sceneTool == null)
            {
                ms_sceneTool = GameObject.FindObjectOfType<SceneTool>();
            }

            if (ms_sceneTool != null && ms_sceneTool.enabled && !ms_sceneTool.IsApply)
            {
                ms_sceneTool.ApplyAll();
            }
        }

        private static void Update()
        {
            if (!EditorApplication.isPlaying)
            {
                if (ms_sceneTool == null)
                {
                    ms_sceneTool = GameObject.FindObjectOfType<SceneTool>();
                }

                if (ms_sceneTool != null && ms_sceneTool.enabled && !ms_sceneTool.IsApply)
                {
                    ms_sceneTool.ApplyAll();
                }
            }
        }

        private static void playModeStateChanged(PlayModeStateChange obj)
        {
            var sceneTool = GameObject.FindObjectOfType<SceneTool>();
            if (sceneTool != null)
            {
                sceneTool.ApplyAll();
            }
        }
    }
}
