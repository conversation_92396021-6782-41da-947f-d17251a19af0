using NXEditorGUITable;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;

namespace ModelSeparate
{
    public class NXModelInfo : TableItemData
    {
        public string name;
        public GameObject go;
        public bool select;
        public Dictionary<UnityEngine.Object, NXAssetLinker> linkerDict = new Dictionary<UnityEngine.Object, NXAssetLinker>();
        public List<string> assetbundlePaths = new List<string>();

        public bool needUpdate
        {
            get
            {
                foreach (var linker in linkerDict)
                {
                    if (linker.Value.needUpdate)
                    {
                        return true;
                    }
                }
                return false;
            }
        }

        public override object GetCell(string name)
        {
            switch (name)
            {
                case "name":
                    return name;
                case "needupdate":
                    {
                        return needUpdate ? "需要更新!" : "";
                    }
                case "updatelist":
                    {
                        StringBuilder stringBuilder = new StringBuilder();
                        foreach (var linker in linkerDict)
                        {
                            if (linker.Value.needUpdate)
                            {
                                stringBuilder.Append(linker.Value.assetName).Append(" ");
                            }
                        }
                        return stringBuilder.ToString();
                    }
                default:
                    break;
            }
            return null;
        }

        public void RevertAsset()
        {

        }

        public void ApplyAssetbundlePaths()
        {
            HashSet<string> assetbundleFolders = new HashSet<string>();
            for (int i = 0; i < assetbundlePaths.Count; ++i)
            {
                if (!assetbundleFolders.Contains(assetbundlePaths[i]))
                    assetbundleFolders.Add(assetbundlePaths[i]);
            }
            foreach (var path in assetbundleFolders)
            {
                if (HasAssetbundleInParent(path))
                    continue;

                AssetImporter importer = AssetImporter.GetAtPath(path);
                SetAssetBundleName(importer);
                Debug.Log("SetAssetbundle: " + path);
                AssetDatabase.WriteImportSettingsIfDirty(path);

                ClearChildAssetbundle(path);
            }
        }

        public void ClearChildAssetbundle(string subdir)
        {
            var info = new DirectoryInfo(subdir);
            if (info == null)
                // this is not a directory
                return;
            if (!info.Exists)
                return;
            var files = info.GetFileSystemInfos();
            for (int i = 0; i < files.Length; i++)
            {
                var path = files[i].FullName;
                path = path.Replace('\\', '/');
                path = "Assets" + path.Split(new[] { "Assets" }, StringSplitOptions.None)[1];
                //Debug.Log(path + " will be cleared");
                var importer = AssetImporter.GetAtPath(path);
                if (importer)
                    importer.assetBundleName = "";
            }
        }

        public bool HasAssetbundleInParent(string path)
        {
            var parentPath = Directory.GetParent(path);
            for (int i = 0; i < 10; ++i)
            {
                if (parentPath == null)
                    break;
                var parentPathName = parentPath.FullName;
                parentPathName = parentPathName.Replace('\\', '/');
                if (!parentPathName.Contains("/Assets/"))
                    break;
                parentPathName = "Assets" + parentPathName.Split(new[] { "Assets" }, StringSplitOptions.None)[1];
                //Debug.Log(parentPathName + " will be cleared");
                AssetImporter importer1 = AssetImporter.GetAtPath(parentPathName);
                if (importer1 && importer1.assetBundleName != string.Empty)
                {
                    return true;
                }
                parentPath = parentPath.Parent;
            }
            return false;
        }

        public static void SetAssetBundleName(AssetImporter importer)
        {
            if (importer != null)
            {
                string assetBundleName = importer.assetPath.Substring("Assets/".Length);
                int ext_index = assetBundleName.LastIndexOf('.');
                if (ext_index > 0)
                {
                    assetBundleName = assetBundleName.Substring(0, ext_index);
                }

                importer.assetBundleName = assetBundleName;
                importer.SaveAndReimport();
            }
        }
    }
}
