#if ACGGAME_CLIENT
namespace UniqueInfo.Editor.ChessPlayerConfig_Gen
{
    public class HashUniqueInfo_Config
    {
        // Equal Method
        // UnityEngine.AnimationCurve Class Func
        public static void UnityEngine_AnimationCurve_Hash(UnityEngine.AnimationCurve data, ref int hashCode)
        {
            TransferClassField_Hash.Hash(data, ref hashCode);
        }

        // UnityEngine.AnimationCurve Ref Func
        public static void UnityEngine_AnimationCurve_RefHash(UnityEngine.AnimationCurve data, ref int hashCode)
        {
            if(data == null)
                return;
                
            int index = WriteUniqueInfo_ConfigPool.instance.UnityEngine_AnimationCurve_GetIndex(data);
            TransferBaseField_Hash.Hash(index, ref hashCode);
        }

        // string Class Func
        public static void System_String_Hash(string data, ref int hashCode)
        {
            TransferClassField_Hash.Hash(data, ref hashCode);
        }

        // string Ref Func
        public static void System_String_RefHash(string data, ref int hashCode)
        {
            if(data == null)
                return;
                
            int index = WriteUniqueInfo_ConfigPool.instance.System_String_GetIndex(data);
            TransferBaseField_Hash.Hash(index, ref hashCode);
        }

        // System.Collections.Generic.KeyValuePair<string, int> Class Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_Hash(System.Collections.Generic.KeyValuePair<string, int> data, ref int hashCode)
        {
            // Field
            HashUniqueInfo_Config.System_String_RefHash(data.Key, ref hashCode);
            TransferBaseField_Hash.Hash(data.Value, ref hashCode);
        }

        // System.Collections.Generic.KeyValuePair<string, int> Ref Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_RefHash(System.Collections.Generic.KeyValuePair<string, int> data, ref int hashCode)
        {
            HashUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_Hash(data, ref hashCode);
        }

        // System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>> Class Func
        public static void System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_Hash(System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>> dataCollection, ref int hashCode)
        {
            int len = dataCollection.Count;
            TransferBaseField_Hash.Hash(len, ref hashCode);
            foreach(System.Collections.Generic.KeyValuePair<string, int> data in dataCollection)
            {
                HashUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_RefHash(data, ref hashCode);
            }
        }

        // System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>> Ref Func
        public static void System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_RefHash(System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>> data, ref int hashCode)
        {
            if(data == null)
                return;
                
            int index = WriteUniqueInfo_ConfigPool.instance.System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_GetIndex(data);
            TransferBaseField_Hash.Hash(index, ref hashCode);
        }

        // ChessPlayerAnimConfig Class Func
        public static void ChessPlayerAnimConfig_Hash(ChessPlayerAnimConfig data, ref int hashCode)
        {
            // Field
            HashUniqueInfo_Config.System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_RefHash(data.animList, ref hashCode);
            TransferBaseField_Hash.Hash(data.totalWeight, ref hashCode);
        }

        // ChessPlayerAnimConfig Ref Func
        public static void ChessPlayerAnimConfig_RefHash(ChessPlayerAnimConfig data, ref int hashCode)
        {
            if(data == null)
                return;
        
            int index = WriteUniqueInfo_ConfigPool.instance.ChessPlayerAnimConfig_GetIndex(data);
            TransferBaseField_Hash.Hash(index, ref hashCode);
        }

        // System.Collections.Generic.KeyValuePair<string, ChessPlayerAnimConfig> Class Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_String_ChessPlayerAnimConfig_End_Hash(System.Collections.Generic.KeyValuePair<string, ChessPlayerAnimConfig> data, ref int hashCode)
        {
            // Field
            HashUniqueInfo_Config.System_String_RefHash(data.Key, ref hashCode);
            HashUniqueInfo_Config.ChessPlayerAnimConfig_RefHash(data.Value, ref hashCode);
        }

        // System.Collections.Generic.KeyValuePair<string, ChessPlayerAnimConfig> Ref Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_String_ChessPlayerAnimConfig_End_RefHash(System.Collections.Generic.KeyValuePair<string, ChessPlayerAnimConfig> data, ref int hashCode)
        {
            HashUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_String_ChessPlayerAnimConfig_End_Hash(data, ref hashCode);
        }

        // TKFrame.TKDictionary<string, ChessPlayerAnimConfig> Class Func
        public static void TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_Hash(TKFrame.TKDictionary<string, ChessPlayerAnimConfig> dataCollection, ref int hashCode)
        {
            int len = dataCollection.Count;
            TransferBaseField_Hash.Hash(len, ref hashCode);
            foreach(System.Collections.Generic.KeyValuePair<string, ChessPlayerAnimConfig> data in dataCollection)
            {
                HashUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_String_ChessPlayerAnimConfig_End_RefHash(data, ref hashCode);
            }
        }

        // TKFrame.TKDictionary<string, ChessPlayerAnimConfig> Ref Func
        public static void TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_RefHash(TKFrame.TKDictionary<string, ChessPlayerAnimConfig> data, ref int hashCode)
        {
            if(data == null)
                return;
                
            int index = WriteUniqueInfo_ConfigPool.instance.TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_GetIndex(data);
            TransferBaseField_Hash.Hash(index, ref hashCode);
        }

        // float[,] Class Func
        public static void System_Single_2_Array_Hash(float[,] data, ref int hashCode)
        {
            int len = data.Length;
            TransferBaseField_Hash.Hash(len, ref hashCode);
        
            int rank = data.Rank;
            TransferBaseField_Hash.Hash(rank, ref hashCode);
        
            int l0 = data.GetLength(0);
            TransferBaseField_Hash.Hash(l0, ref hashCode);
            int l1 = data.GetLength(1);
            TransferBaseField_Hash.Hash(l1, ref hashCode);
        
            for (int i = 0; i < len; i++)
            {
                int index = i;
                int d1 = index % l1; index /= l1;
                int d0 = index % l0; index /= l0;
                TransferBaseField_Hash.Hash(data[d0,d1], ref hashCode);
            }
        }

        // float[,] Ref Func
        public static void System_Single_2_Array_RefHash(float[,] data, ref int hashCode)
        {
            if(data == null)
                return;
                
            int index = WriteUniqueInfo_ConfigPool.instance.System_Single_2_Array_GetIndex(data);
            TransferBaseField_Hash.Hash(index, ref hashCode);
        }

        // AnimSheetConfig Class Func
        public static void AnimSheetConfig_Hash(AnimSheetConfig data, ref int hashCode)
        {
            // Field
            HashUniqueInfo_Config.System_Single_2_Array_RefHash(data.m_crossFades, ref hashCode);
        }

        // AnimSheetConfig Ref Func
        public static void AnimSheetConfig_RefHash(AnimSheetConfig data, ref int hashCode)
        {
            if(data == null)
                return;
        
            int index = WriteUniqueInfo_ConfigPool.instance.AnimSheetConfig_GetIndex(data);
            TransferBaseField_Hash.Hash(index, ref hashCode);
        }

        // bool[] Class Func
        public static void System_Boolean_1_Array_Hash(bool[] data, ref int hashCode)
        {
            int len = data.Length;
            TransferBaseField_Hash.Hash(len, ref hashCode);
        
            int rank = data.Rank;
            TransferBaseField_Hash.Hash(rank, ref hashCode);
        
            int l0 = data.GetLength(0);
            TransferBaseField_Hash.Hash(l0, ref hashCode);
        
            for (int i = 0; i < len; i++)
            {
                int index = i;
                int d0 = index % l0; index /= l0;
                TransferBaseField_Hash.Hash(data[d0], ref hashCode);
            }
        }

        // bool[] Ref Func
        public static void System_Boolean_1_Array_RefHash(bool[] data, ref int hashCode)
        {
            if(data == null)
                return;
                
            int index = WriteUniqueInfo_ConfigPool.instance.System_Boolean_1_Array_GetIndex(data);
            TransferBaseField_Hash.Hash(index, ref hashCode);
        }

        // AnimSpringManagerConfig Class Func
        public static void AnimSpringManagerConfig_Hash(AnimSpringManagerConfig data, ref int hashCode)
        {
            // Field
            HashUniqueInfo_Config.System_Boolean_1_Array_RefHash(data.m_animSpringConfig, ref hashCode);
        }

        // AnimSpringManagerConfig Ref Func
        public static void AnimSpringManagerConfig_RefHash(AnimSpringManagerConfig data, ref int hashCode)
        {
            if(data == null)
                return;
        
            int index = WriteUniqueInfo_ConfigPool.instance.AnimSpringManagerConfig_GetIndex(data);
            TransferBaseField_Hash.Hash(index, ref hashCode);
        }

        // System.Collections.Generic.KeyValuePair<int, int> Class Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_System_Int32_End_Hash(System.Collections.Generic.KeyValuePair<int, int> data, ref int hashCode)
        {
            // Field
            TransferBaseField_Hash.Hash(data.Key, ref hashCode);
            TransferBaseField_Hash.Hash(data.Value, ref hashCode);
        }

        // System.Collections.Generic.KeyValuePair<int, int> Ref Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_System_Int32_End_RefHash(System.Collections.Generic.KeyValuePair<int, int> data, ref int hashCode)
        {
            HashUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_System_Int32_End_Hash(data, ref hashCode);
        }

        // TKFrame.TKDictionary<int, int> Class Func
        public static void TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_Hash(TKFrame.TKDictionary<int, int> dataCollection, ref int hashCode)
        {
            int len = dataCollection.Count;
            TransferBaseField_Hash.Hash(len, ref hashCode);
            foreach(System.Collections.Generic.KeyValuePair<int, int> data in dataCollection)
            {
                HashUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_System_Int32_End_RefHash(data, ref hashCode);
            }
        }

        // TKFrame.TKDictionary<int, int> Ref Func
        public static void TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_RefHash(TKFrame.TKDictionary<int, int> data, ref int hashCode)
        {
            if(data == null)
                return;
                
            int index = WriteUniqueInfo_ConfigPool.instance.TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_GetIndex(data);
            TransferBaseField_Hash.Hash(index, ref hashCode);
        }

        // System.Collections.Generic.HashSet<string> Class Func
        public static void System_Collections_Generic_HashSet_1_Begin_System_String_End_Hash(System.Collections.Generic.HashSet<string> dataCollection, ref int hashCode)
        {
            int len = dataCollection.Count;
            TransferBaseField_Hash.Hash(len, ref hashCode);
            foreach(string data in dataCollection)
            {
                HashUniqueInfo_Config.System_String_RefHash(data, ref hashCode);
            }
        }

        // System.Collections.Generic.HashSet<string> Ref Func
        public static void System_Collections_Generic_HashSet_1_Begin_System_String_End_RefHash(System.Collections.Generic.HashSet<string> data, ref int hashCode)
        {
            if(data == null)
                return;
                
            int index = WriteUniqueInfo_ConfigPool.instance.System_Collections_Generic_HashSet_1_Begin_System_String_End_GetIndex(data);
            TransferBaseField_Hash.Hash(index, ref hashCode);
        }

        // ChessPlayerConfig Class Func
        public static void ChessPlayerConfig_Hash(ChessPlayerConfig data, ref int hashCode)
        {
            // Field
            TransferBaseField_Hash.Hash(data.rotateSpeed, ref hashCode);
            TransferBaseField_Hash.Hash(data.walkSpeed, ref hashCode);
            TransferBaseField_Hash.Hash(data.walkStartDis, ref hashCode);
            TransferBaseField_Hash.Hash(data.walkStartTime, ref hashCode);
            HashUniqueInfo_Config.UnityEngine_AnimationCurve_RefHash(data.walkStartCurve, ref hashCode);
            TransferBaseField_Hash.Hash(data.walkStopDis, ref hashCode);
            TransferBaseField_Hash.Hash(data.walkStopTime, ref hashCode);
            HashUniqueInfo_Config.UnityEngine_AnimationCurve_RefHash(data.walkStopCurve, ref hashCode);
            TransferBaseField_Hash.Hash(data.walk02StopDis, ref hashCode);
            TransferBaseField_Hash.Hash(data.walk02StopTime, ref hashCode);
            HashUniqueInfo_Config.UnityEngine_AnimationCurve_RefHash(data.walk02StopCurve, ref hashCode);
            TransferBaseField_Hash.Hash(data.runSpeed, ref hashCode);
            TransferBaseField_Hash.Hash(data.runStartDis, ref hashCode);
            TransferBaseField_Hash.Hash(data.runStartTime, ref hashCode);
            HashUniqueInfo_Config.UnityEngine_AnimationCurve_RefHash(data.runStartCurve, ref hashCode);
            TransferBaseField_Hash.Hash(data.runStopDis, ref hashCode);
            TransferBaseField_Hash.Hash(data.runStopTime, ref hashCode);
            HashUniqueInfo_Config.UnityEngine_AnimationCurve_RefHash(data.runStopCurve, ref hashCode);
            TransferBaseField_Hash.Hash(data.run02StopDis, ref hashCode);
            TransferBaseField_Hash.Hash(data.run02StopTime, ref hashCode);
            HashUniqueInfo_Config.UnityEngine_AnimationCurve_RefHash(data.run02StopCurve, ref hashCode);
            TransferBaseField_Hash.Hash(data.rushDis, ref hashCode);
            TransferBaseField_Hash.Hash(data.rushTime, ref hashCode);
            HashUniqueInfo_Config.UnityEngine_AnimationCurve_RefHash(data.rushCurve, ref hashCode);
            TransferBaseField_Hash.Hash(data.hurtTime1, ref hashCode);
            TransferBaseField_Hash.Hash(data.hurtDis1, ref hashCode);
            HashUniqueInfo_Config.UnityEngine_AnimationCurve_RefHash(data.hurtCurve1, ref hashCode);
            TransferBaseField_Hash.Hash(data.hurtTime2, ref hashCode);
            TransferBaseField_Hash.Hash(data.hurtDis2, ref hashCode);
            HashUniqueInfo_Config.UnityEngine_AnimationCurve_RefHash(data.hurtCurve2, ref hashCode);
            TransferBaseField_Hash.Hash(data.deathHurtTime, ref hashCode);
            TransferBaseField_Hash.Hash(data.deathHurtDis, ref hashCode);
            HashUniqueInfo_Config.UnityEngine_AnimationCurve_RefHash(data.deathHurtCurve, ref hashCode);
            HashUniqueInfo_Config.UnityEngine_AnimationCurve_RefHash(data.hurtHeightCurve2, ref hashCode);
            HashUniqueInfo_Config.UnityEngine_AnimationCurve_RefHash(data.jumpIn, ref hashCode);
            HashUniqueInfo_Config.UnityEngine_AnimationCurve_RefHash(data.jumpOut, ref hashCode);
            TransferBaseField_Hash.Hash(data.jump_in_min_height, ref hashCode);
            TransferBaseField_Hash.Hash(data.jump_in_max_height, ref hashCode);
            TransferBaseField_Hash.Hash(data.jump_in_touch_ground_frame, ref hashCode);
            TransferBaseField_Hash.Hash(data.jump_out_min_height, ref hashCode);
            TransferBaseField_Hash.Hash(data.jump_out_max_height, ref hashCode);
            TransferBaseField_Hash.Hash(data.jump_in_start_frame, ref hashCode);
            TransferBaseField_Hash.Hash(data.jump_out_touch_ground_frame, ref hashCode);
            HashUniqueInfo_Config.TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_RefHash(data.m_animList, ref hashCode);
            HashUniqueInfo_Config.System_String_RefHash(data.m_victoryAnimationHomeName, ref hashCode);
            HashUniqueInfo_Config.System_String_RefHash(data.m_victoryAnimationAwayName, ref hashCode);
            TransferBaseField_Hash.Hash(data.m_victoryAnimationHomeTime, ref hashCode);
            TransferBaseField_Hash.Hash(data.m_victoryAnimationAwayTime, ref hashCode);
            HashUniqueInfo_Config.AnimSheetConfig_RefHash(data.m_animSheetConfig, ref hashCode);
            HashUniqueInfo_Config.AnimSpringManagerConfig_RefHash(data.m_animSpringManagerConfig, ref hashCode);
            HashUniqueInfo_Config.TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_RefHash(data.m_animIds, ref hashCode);
            TransferBaseField_Hash.Hash(data.BodyScaleChangePlan, ref hashCode);
            HashUniqueInfo_Config.System_Collections_Generic_HashSet_1_Begin_System_String_End_RefHash(data.m_clickRootMotionNames, ref hashCode);
            TransferBaseField_Hash.Hash(data.m_maxRoundSelectMaxScale, ref hashCode);
        }

        // ChessPlayerConfig Ref Func
        public static void ChessPlayerConfig_RefHash(ChessPlayerConfig data, ref int hashCode)
        {
            if(data == null)
                return;
        
            int index = WriteUniqueInfo_ConfigPool.instance.ChessPlayerConfig_GetIndex(data);
            TransferBaseField_Hash.Hash(index, ref hashCode);
        }

        // System.Collections.Generic.KeyValuePair<int, ChessPlayerConfig> Class Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_ChessPlayerConfig_End_Hash(System.Collections.Generic.KeyValuePair<int, ChessPlayerConfig> data, ref int hashCode)
        {
            // Field
            TransferBaseField_Hash.Hash(data.Key, ref hashCode);
            HashUniqueInfo_Config.ChessPlayerConfig_RefHash(data.Value, ref hashCode);
        }

        // System.Collections.Generic.KeyValuePair<int, ChessPlayerConfig> Ref Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_ChessPlayerConfig_End_RefHash(System.Collections.Generic.KeyValuePair<int, ChessPlayerConfig> data, ref int hashCode)
        {
            HashUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_ChessPlayerConfig_End_Hash(data, ref hashCode);
        }

        // UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> Class Func
        public static void UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Hash(UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> dataCollection, ref int hashCode)
        {
            int len = dataCollection.Count;
            TransferBaseField_Hash.Hash(len, ref hashCode);
            foreach(System.Collections.Generic.KeyValuePair<int, ChessPlayerConfig> data in dataCollection)
            {
                HashUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_ChessPlayerConfig_End_RefHash(data, ref hashCode);
            }
        }

        // UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> Ref Func
        public static void UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_RefHash(UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> data, ref int hashCode)
        {
            if(data == null)
                return;
                
            int index = WriteUniqueInfo_ConfigPool.instance.UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_GetIndex(data);
            TransferBaseField_Hash.Hash(index, ref hashCode);
        }

    }
}
#endif
