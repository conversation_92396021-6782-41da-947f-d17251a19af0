#if ACGGAME_CLIENT
namespace UniqueInfo.Editor.ChessPlayerConfig_Gen
{
    public class EqualUniqueInfo_Config
    {
        // Equal Method
        // UnityEngine.AnimationCurve Class Func
        public static void UnityEngine_AnimationCurve_Equal(UnityEngine.AnimationCurve x, UnityEngine.AnimationCurve y, ref bool result)
        {     
            TransferClassField_Equal.Equal(x, y, ref result);
        }

        // UnityEngine.AnimationCurve Ref Func
        public static void UnityEngine_AnimationCurve_RefEqual(UnityEngine.AnimationCurve x, UnityEngine.AnimationCurve y, ref bool result)
        {
            if(x == null || y == null)
            {
                TransferBaseField_Equal.Equal(x, y , ref result);
                return;
            }
                
            int index1 = WriteUniqueInfo_ConfigPool.instance.UnityEngine_AnimationCurve_GetIndex(x);
            int index2 = WriteUniqueInfo_ConfigPool.instance.UnityEngine_AnimationCurve_GetIndex(y);
            TransferBaseField_Equal.Equal(index1, index2, ref result);
        }

        // string Class Func
        public static void System_String_Equal(string x, string y, ref bool result)
        {     
            TransferClassField_Equal.Equal(x, y, ref result);
        }

        // string Ref Func
        public static void System_String_RefEqual(string x, string y, ref bool result)
        {
            if(x == null || y == null)
            {
                TransferBaseField_Equal.Equal(x, y , ref result);
                return;
            }
                
            int index1 = WriteUniqueInfo_ConfigPool.instance.System_String_GetIndex(x);
            int index2 = WriteUniqueInfo_ConfigPool.instance.System_String_GetIndex(y);
            TransferBaseField_Equal.Equal(index1, index2, ref result);
        }

        // System.Collections.Generic.KeyValuePair<string, int> Class Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_Equal(System.Collections.Generic.KeyValuePair<string, int> x, System.Collections.Generic.KeyValuePair<string, int> y, ref bool result)
        {     
            if (!result)
                return;
            // Field
            EqualUniqueInfo_Config.System_String_RefEqual(x.Key, y.Key, ref result);
            TransferBaseField_Equal.Equal(x.Value, y.Value, ref result);
        }

        // System.Collections.Generic.KeyValuePair<string, int> Ref Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_RefEqual(System.Collections.Generic.KeyValuePair<string, int> x, System.Collections.Generic.KeyValuePair<string, int> y, ref bool result)
        {
            EqualUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_Equal(x, y, ref result);
        }

        // System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>> Class Func
        public static void System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_Equal(System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>> xCollection, System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>> yCollection, ref bool result)
        {
            if (!result)
                return;
        
            TransferBaseField_Equal.Equal(xCollection.Count, yCollection.Count, ref result);
            if (!result)
                return;
        
            int len = xCollection.Count;
            var xEnumerator = xCollection.GetEnumerator();
            var yEnumerator = yCollection.GetEnumerator();
            
            for (int i = 0; i < len; i++)
            {
                xEnumerator.MoveNext();
                yEnumerator.MoveNext();
        
                System.Collections.Generic.KeyValuePair<string, int> x = xEnumerator.Current;
                System.Collections.Generic.KeyValuePair<string, int> y = yEnumerator.Current;
        
                EqualUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_RefEqual(x, y, ref result);
        
                if (!result)
                    return;
            }
        }

        // System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>> Ref Func
        public static void System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_RefEqual(System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>> x, System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>> y, ref bool result)
        {
            if(x == null || y == null)
            {
                TransferBaseField_Equal.Equal(x, y , ref result);
                return;
            }
                
            int index1 = WriteUniqueInfo_ConfigPool.instance.System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_GetIndex(x);
            int index2 = WriteUniqueInfo_ConfigPool.instance.System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_GetIndex(y);
            TransferBaseField_Equal.Equal(index1, index2, ref result);
        }

        // ChessPlayerAnimConfig Class Func
        public static void ChessPlayerAnimConfig_Equal(ChessPlayerAnimConfig x, ChessPlayerAnimConfig y, ref bool result)
        {     
            if (!result)
                return;
            // Field
            EqualUniqueInfo_Config.System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_RefEqual(x.animList, y.animList, ref result);
            TransferBaseField_Equal.Equal(x.totalWeight, y.totalWeight, ref result);
        }

        // ChessPlayerAnimConfig Ref Func
        public static void ChessPlayerAnimConfig_RefEqual(ChessPlayerAnimConfig x, ChessPlayerAnimConfig y, ref bool result)
        {
            if(x == null || y == null)
            {
                TransferBaseField_Equal.Equal(x, y , ref result);
                return;
            }
        
            TransferBaseField_Equal.TypeEqual(x, y , ref result);
            if(!result)
                return;
                
            int index1 = WriteUniqueInfo_ConfigPool.instance.ChessPlayerAnimConfig_GetIndex(x);
            int index2 = WriteUniqueInfo_ConfigPool.instance.ChessPlayerAnimConfig_GetIndex(y);
            TransferBaseField_Equal.Equal(index1, index2, ref result);
        }

        // System.Collections.Generic.KeyValuePair<string, ChessPlayerAnimConfig> Class Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_String_ChessPlayerAnimConfig_End_Equal(System.Collections.Generic.KeyValuePair<string, ChessPlayerAnimConfig> x, System.Collections.Generic.KeyValuePair<string, ChessPlayerAnimConfig> y, ref bool result)
        {     
            if (!result)
                return;
            // Field
            EqualUniqueInfo_Config.System_String_RefEqual(x.Key, y.Key, ref result);
            EqualUniqueInfo_Config.ChessPlayerAnimConfig_RefEqual(x.Value, y.Value, ref result);
        }

        // System.Collections.Generic.KeyValuePair<string, ChessPlayerAnimConfig> Ref Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_String_ChessPlayerAnimConfig_End_RefEqual(System.Collections.Generic.KeyValuePair<string, ChessPlayerAnimConfig> x, System.Collections.Generic.KeyValuePair<string, ChessPlayerAnimConfig> y, ref bool result)
        {
            EqualUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_String_ChessPlayerAnimConfig_End_Equal(x, y, ref result);
        }

        // TKFrame.TKDictionary<string, ChessPlayerAnimConfig> Class Func
        public static void TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_Equal(TKFrame.TKDictionary<string, ChessPlayerAnimConfig> xCollection, TKFrame.TKDictionary<string, ChessPlayerAnimConfig> yCollection, ref bool result)
        {
            if (!result)
                return;
        
            TransferBaseField_Equal.Equal(xCollection.Count, yCollection.Count, ref result);
            if (!result)
                return;
        
            int len = xCollection.Count;
            var xEnumerator = xCollection.GetEnumerator();
            var yEnumerator = yCollection.GetEnumerator();
            
            for (int i = 0; i < len; i++)
            {
                xEnumerator.MoveNext();
                yEnumerator.MoveNext();
        
                System.Collections.Generic.KeyValuePair<string, ChessPlayerAnimConfig> x = xEnumerator.Current;
                System.Collections.Generic.KeyValuePair<string, ChessPlayerAnimConfig> y = yEnumerator.Current;
        
                EqualUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_String_ChessPlayerAnimConfig_End_RefEqual(x, y, ref result);
        
                if (!result)
                    return;
            }
        }

        // TKFrame.TKDictionary<string, ChessPlayerAnimConfig> Ref Func
        public static void TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_RefEqual(TKFrame.TKDictionary<string, ChessPlayerAnimConfig> x, TKFrame.TKDictionary<string, ChessPlayerAnimConfig> y, ref bool result)
        {
            if(x == null || y == null)
            {
                TransferBaseField_Equal.Equal(x, y , ref result);
                return;
            }
                
            int index1 = WriteUniqueInfo_ConfigPool.instance.TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_GetIndex(x);
            int index2 = WriteUniqueInfo_ConfigPool.instance.TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_GetIndex(y);
            TransferBaseField_Equal.Equal(index1, index2, ref result);
        }

        // float[,] Class Func
        public static void System_Single_2_Array_Equal(float[,]x, float[,] y, ref bool result)
        {
            if (!result)
                return;
        
            TransferBaseField_Equal.Equal(x.Length, y.Length, ref result);
            if (!result)
                return;
        
            int l0 = x.GetLength(0);
            TransferBaseField_Equal.Equal(x.GetLength(0), y.GetLength(0), ref result);
            if (!result)
                return;
            int l1 = x.GetLength(1);
            TransferBaseField_Equal.Equal(x.GetLength(1), y.GetLength(1), ref result);
            if (!result)
                return;
        
            for (int i = 0; i < x.Length; i++)
            {
                int index = i;
                int d1 = index % l1; index /= l1;
                int d0 = index % l0; index /= l0;
                TransferBaseField_Equal.Equal(x[d0,d1], y[d0,d1], ref result);
                if (!result)
                    return;
            }
        }

        // float[,] Ref Func
        public static void System_Single_2_Array_RefEqual(float[,] x, float[,] y, ref bool result)
        {
            if(x == null || y == null)
            {
                TransferBaseField_Equal.Equal(x, y , ref result);
                return;
            }
                
            int index1 = WriteUniqueInfo_ConfigPool.instance.System_Single_2_Array_GetIndex(x);
            int index2 = WriteUniqueInfo_ConfigPool.instance.System_Single_2_Array_GetIndex(y);
            TransferBaseField_Equal.Equal(index1, index2, ref result);
        }

        // AnimSheetConfig Class Func
        public static void AnimSheetConfig_Equal(AnimSheetConfig x, AnimSheetConfig y, ref bool result)
        {     
            if (!result)
                return;
            // Field
            EqualUniqueInfo_Config.System_Single_2_Array_RefEqual(x.m_crossFades, y.m_crossFades, ref result);
        }

        // AnimSheetConfig Ref Func
        public static void AnimSheetConfig_RefEqual(AnimSheetConfig x, AnimSheetConfig y, ref bool result)
        {
            if(x == null || y == null)
            {
                TransferBaseField_Equal.Equal(x, y , ref result);
                return;
            }
        
            TransferBaseField_Equal.TypeEqual(x, y , ref result);
            if(!result)
                return;
                
            int index1 = WriteUniqueInfo_ConfigPool.instance.AnimSheetConfig_GetIndex(x);
            int index2 = WriteUniqueInfo_ConfigPool.instance.AnimSheetConfig_GetIndex(y);
            TransferBaseField_Equal.Equal(index1, index2, ref result);
        }

        // bool[] Class Func
        public static void System_Boolean_1_Array_Equal(bool[]x, bool[] y, ref bool result)
        {
            if (!result)
                return;
        
            TransferBaseField_Equal.Equal(x.Length, y.Length, ref result);
            if (!result)
                return;
        
            int l0 = x.GetLength(0);
            TransferBaseField_Equal.Equal(x.GetLength(0), y.GetLength(0), ref result);
            if (!result)
                return;
        
            for (int i = 0; i < x.Length; i++)
            {
                int index = i;
                int d0 = index % l0; index /= l0;
                TransferBaseField_Equal.Equal(x[d0], y[d0], ref result);
                if (!result)
                    return;
            }
        }

        // bool[] Ref Func
        public static void System_Boolean_1_Array_RefEqual(bool[] x, bool[] y, ref bool result)
        {
            if(x == null || y == null)
            {
                TransferBaseField_Equal.Equal(x, y , ref result);
                return;
            }
                
            int index1 = WriteUniqueInfo_ConfigPool.instance.System_Boolean_1_Array_GetIndex(x);
            int index2 = WriteUniqueInfo_ConfigPool.instance.System_Boolean_1_Array_GetIndex(y);
            TransferBaseField_Equal.Equal(index1, index2, ref result);
        }

        // AnimSpringManagerConfig Class Func
        public static void AnimSpringManagerConfig_Equal(AnimSpringManagerConfig x, AnimSpringManagerConfig y, ref bool result)
        {     
            if (!result)
                return;
            // Field
            EqualUniqueInfo_Config.System_Boolean_1_Array_RefEqual(x.m_animSpringConfig, y.m_animSpringConfig, ref result);
        }

        // AnimSpringManagerConfig Ref Func
        public static void AnimSpringManagerConfig_RefEqual(AnimSpringManagerConfig x, AnimSpringManagerConfig y, ref bool result)
        {
            if(x == null || y == null)
            {
                TransferBaseField_Equal.Equal(x, y , ref result);
                return;
            }
        
            TransferBaseField_Equal.TypeEqual(x, y , ref result);
            if(!result)
                return;
                
            int index1 = WriteUniqueInfo_ConfigPool.instance.AnimSpringManagerConfig_GetIndex(x);
            int index2 = WriteUniqueInfo_ConfigPool.instance.AnimSpringManagerConfig_GetIndex(y);
            TransferBaseField_Equal.Equal(index1, index2, ref result);
        }

        // System.Collections.Generic.KeyValuePair<int, int> Class Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_System_Int32_End_Equal(System.Collections.Generic.KeyValuePair<int, int> x, System.Collections.Generic.KeyValuePair<int, int> y, ref bool result)
        {     
            if (!result)
                return;
            // Field
            TransferBaseField_Equal.Equal(x.Key, y.Key, ref result);
            TransferBaseField_Equal.Equal(x.Value, y.Value, ref result);
        }

        // System.Collections.Generic.KeyValuePair<int, int> Ref Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_System_Int32_End_RefEqual(System.Collections.Generic.KeyValuePair<int, int> x, System.Collections.Generic.KeyValuePair<int, int> y, ref bool result)
        {
            EqualUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_System_Int32_End_Equal(x, y, ref result);
        }

        // TKFrame.TKDictionary<int, int> Class Func
        public static void TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_Equal(TKFrame.TKDictionary<int, int> xCollection, TKFrame.TKDictionary<int, int> yCollection, ref bool result)
        {
            if (!result)
                return;
        
            TransferBaseField_Equal.Equal(xCollection.Count, yCollection.Count, ref result);
            if (!result)
                return;
        
            int len = xCollection.Count;
            var xEnumerator = xCollection.GetEnumerator();
            var yEnumerator = yCollection.GetEnumerator();
            
            for (int i = 0; i < len; i++)
            {
                xEnumerator.MoveNext();
                yEnumerator.MoveNext();
        
                System.Collections.Generic.KeyValuePair<int, int> x = xEnumerator.Current;
                System.Collections.Generic.KeyValuePair<int, int> y = yEnumerator.Current;
        
                EqualUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_System_Int32_End_RefEqual(x, y, ref result);
        
                if (!result)
                    return;
            }
        }

        // TKFrame.TKDictionary<int, int> Ref Func
        public static void TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_RefEqual(TKFrame.TKDictionary<int, int> x, TKFrame.TKDictionary<int, int> y, ref bool result)
        {
            if(x == null || y == null)
            {
                TransferBaseField_Equal.Equal(x, y , ref result);
                return;
            }
                
            int index1 = WriteUniqueInfo_ConfigPool.instance.TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_GetIndex(x);
            int index2 = WriteUniqueInfo_ConfigPool.instance.TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_GetIndex(y);
            TransferBaseField_Equal.Equal(index1, index2, ref result);
        }

        // System.Collections.Generic.HashSet<string> Class Func
        public static void System_Collections_Generic_HashSet_1_Begin_System_String_End_Equal(System.Collections.Generic.HashSet<string> xCollection, System.Collections.Generic.HashSet<string> yCollection, ref bool result)
        {
            if (!result)
                return;
        
            TransferBaseField_Equal.Equal(xCollection.Count, yCollection.Count, ref result);
            if (!result)
                return;
        
            int len = xCollection.Count;
            var xEnumerator = xCollection.GetEnumerator();
            var yEnumerator = yCollection.GetEnumerator();
            
            for (int i = 0; i < len; i++)
            {
                xEnumerator.MoveNext();
                yEnumerator.MoveNext();
        
                string x = xEnumerator.Current;
                string y = yEnumerator.Current;
        
                EqualUniqueInfo_Config.System_String_RefEqual(x, y, ref result);
        
                if (!result)
                    return;
            }
        }

        // System.Collections.Generic.HashSet<string> Ref Func
        public static void System_Collections_Generic_HashSet_1_Begin_System_String_End_RefEqual(System.Collections.Generic.HashSet<string> x, System.Collections.Generic.HashSet<string> y, ref bool result)
        {
            if(x == null || y == null)
            {
                TransferBaseField_Equal.Equal(x, y , ref result);
                return;
            }
                
            int index1 = WriteUniqueInfo_ConfigPool.instance.System_Collections_Generic_HashSet_1_Begin_System_String_End_GetIndex(x);
            int index2 = WriteUniqueInfo_ConfigPool.instance.System_Collections_Generic_HashSet_1_Begin_System_String_End_GetIndex(y);
            TransferBaseField_Equal.Equal(index1, index2, ref result);
        }

        // ChessPlayerConfig Class Func
        public static void ChessPlayerConfig_Equal(ChessPlayerConfig x, ChessPlayerConfig y, ref bool result)
        {     
            if (!result)
                return;
            // Field
            TransferBaseField_Equal.Equal(x.rotateSpeed, y.rotateSpeed, ref result);
            TransferBaseField_Equal.Equal(x.walkSpeed, y.walkSpeed, ref result);
            TransferBaseField_Equal.Equal(x.walkStartDis, y.walkStartDis, ref result);
            TransferBaseField_Equal.Equal(x.walkStartTime, y.walkStartTime, ref result);
            EqualUniqueInfo_Config.UnityEngine_AnimationCurve_RefEqual(x.walkStartCurve, y.walkStartCurve, ref result);
            TransferBaseField_Equal.Equal(x.walkStopDis, y.walkStopDis, ref result);
            TransferBaseField_Equal.Equal(x.walkStopTime, y.walkStopTime, ref result);
            EqualUniqueInfo_Config.UnityEngine_AnimationCurve_RefEqual(x.walkStopCurve, y.walkStopCurve, ref result);
            TransferBaseField_Equal.Equal(x.walk02StopDis, y.walk02StopDis, ref result);
            TransferBaseField_Equal.Equal(x.walk02StopTime, y.walk02StopTime, ref result);
            EqualUniqueInfo_Config.UnityEngine_AnimationCurve_RefEqual(x.walk02StopCurve, y.walk02StopCurve, ref result);
            TransferBaseField_Equal.Equal(x.runSpeed, y.runSpeed, ref result);
            TransferBaseField_Equal.Equal(x.runStartDis, y.runStartDis, ref result);
            TransferBaseField_Equal.Equal(x.runStartTime, y.runStartTime, ref result);
            EqualUniqueInfo_Config.UnityEngine_AnimationCurve_RefEqual(x.runStartCurve, y.runStartCurve, ref result);
            TransferBaseField_Equal.Equal(x.runStopDis, y.runStopDis, ref result);
            TransferBaseField_Equal.Equal(x.runStopTime, y.runStopTime, ref result);
            EqualUniqueInfo_Config.UnityEngine_AnimationCurve_RefEqual(x.runStopCurve, y.runStopCurve, ref result);
            TransferBaseField_Equal.Equal(x.run02StopDis, y.run02StopDis, ref result);
            TransferBaseField_Equal.Equal(x.run02StopTime, y.run02StopTime, ref result);
            EqualUniqueInfo_Config.UnityEngine_AnimationCurve_RefEqual(x.run02StopCurve, y.run02StopCurve, ref result);
            TransferBaseField_Equal.Equal(x.rushDis, y.rushDis, ref result);
            TransferBaseField_Equal.Equal(x.rushTime, y.rushTime, ref result);
            EqualUniqueInfo_Config.UnityEngine_AnimationCurve_RefEqual(x.rushCurve, y.rushCurve, ref result);
            TransferBaseField_Equal.Equal(x.hurtTime1, y.hurtTime1, ref result);
            TransferBaseField_Equal.Equal(x.hurtDis1, y.hurtDis1, ref result);
            EqualUniqueInfo_Config.UnityEngine_AnimationCurve_RefEqual(x.hurtCurve1, y.hurtCurve1, ref result);
            TransferBaseField_Equal.Equal(x.hurtTime2, y.hurtTime2, ref result);
            TransferBaseField_Equal.Equal(x.hurtDis2, y.hurtDis2, ref result);
            EqualUniqueInfo_Config.UnityEngine_AnimationCurve_RefEqual(x.hurtCurve2, y.hurtCurve2, ref result);
            TransferBaseField_Equal.Equal(x.deathHurtTime, y.deathHurtTime, ref result);
            TransferBaseField_Equal.Equal(x.deathHurtDis, y.deathHurtDis, ref result);
            EqualUniqueInfo_Config.UnityEngine_AnimationCurve_RefEqual(x.deathHurtCurve, y.deathHurtCurve, ref result);
            EqualUniqueInfo_Config.UnityEngine_AnimationCurve_RefEqual(x.hurtHeightCurve2, y.hurtHeightCurve2, ref result);
            EqualUniqueInfo_Config.UnityEngine_AnimationCurve_RefEqual(x.jumpIn, y.jumpIn, ref result);
            EqualUniqueInfo_Config.UnityEngine_AnimationCurve_RefEqual(x.jumpOut, y.jumpOut, ref result);
            TransferBaseField_Equal.Equal(x.jump_in_min_height, y.jump_in_min_height, ref result);
            TransferBaseField_Equal.Equal(x.jump_in_max_height, y.jump_in_max_height, ref result);
            TransferBaseField_Equal.Equal(x.jump_in_touch_ground_frame, y.jump_in_touch_ground_frame, ref result);
            TransferBaseField_Equal.Equal(x.jump_out_min_height, y.jump_out_min_height, ref result);
            TransferBaseField_Equal.Equal(x.jump_out_max_height, y.jump_out_max_height, ref result);
            TransferBaseField_Equal.Equal(x.jump_in_start_frame, y.jump_in_start_frame, ref result);
            TransferBaseField_Equal.Equal(x.jump_out_touch_ground_frame, y.jump_out_touch_ground_frame, ref result);
            EqualUniqueInfo_Config.TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_RefEqual(x.m_animList, y.m_animList, ref result);
            EqualUniqueInfo_Config.System_String_RefEqual(x.m_victoryAnimationHomeName, y.m_victoryAnimationHomeName, ref result);
            EqualUniqueInfo_Config.System_String_RefEqual(x.m_victoryAnimationAwayName, y.m_victoryAnimationAwayName, ref result);
            TransferBaseField_Equal.Equal(x.m_victoryAnimationHomeTime, y.m_victoryAnimationHomeTime, ref result);
            TransferBaseField_Equal.Equal(x.m_victoryAnimationAwayTime, y.m_victoryAnimationAwayTime, ref result);
            EqualUniqueInfo_Config.AnimSheetConfig_RefEqual(x.m_animSheetConfig, y.m_animSheetConfig, ref result);
            EqualUniqueInfo_Config.AnimSpringManagerConfig_RefEqual(x.m_animSpringManagerConfig, y.m_animSpringManagerConfig, ref result);
            EqualUniqueInfo_Config.TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_RefEqual(x.m_animIds, y.m_animIds, ref result);
            TransferBaseField_Equal.Equal(x.BodyScaleChangePlan, y.BodyScaleChangePlan, ref result);
            EqualUniqueInfo_Config.System_Collections_Generic_HashSet_1_Begin_System_String_End_RefEqual(x.m_clickRootMotionNames, y.m_clickRootMotionNames, ref result);
            TransferBaseField_Equal.Equal(x.m_maxRoundSelectMaxScale, y.m_maxRoundSelectMaxScale, ref result);
        }

        // ChessPlayerConfig Ref Func
        public static void ChessPlayerConfig_RefEqual(ChessPlayerConfig x, ChessPlayerConfig y, ref bool result)
        {
            if(x == null || y == null)
            {
                TransferBaseField_Equal.Equal(x, y , ref result);
                return;
            }
        
            TransferBaseField_Equal.TypeEqual(x, y , ref result);
            if(!result)
                return;
                
            int index1 = WriteUniqueInfo_ConfigPool.instance.ChessPlayerConfig_GetIndex(x);
            int index2 = WriteUniqueInfo_ConfigPool.instance.ChessPlayerConfig_GetIndex(y);
            TransferBaseField_Equal.Equal(index1, index2, ref result);
        }

        // System.Collections.Generic.KeyValuePair<int, ChessPlayerConfig> Class Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_ChessPlayerConfig_End_Equal(System.Collections.Generic.KeyValuePair<int, ChessPlayerConfig> x, System.Collections.Generic.KeyValuePair<int, ChessPlayerConfig> y, ref bool result)
        {     
            if (!result)
                return;
            // Field
            TransferBaseField_Equal.Equal(x.Key, y.Key, ref result);
            EqualUniqueInfo_Config.ChessPlayerConfig_RefEqual(x.Value, y.Value, ref result);
        }

        // System.Collections.Generic.KeyValuePair<int, ChessPlayerConfig> Ref Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_ChessPlayerConfig_End_RefEqual(System.Collections.Generic.KeyValuePair<int, ChessPlayerConfig> x, System.Collections.Generic.KeyValuePair<int, ChessPlayerConfig> y, ref bool result)
        {
            EqualUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_ChessPlayerConfig_End_Equal(x, y, ref result);
        }

        // UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> Class Func
        public static void UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Equal(UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> xCollection, UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> yCollection, ref bool result)
        {
            if (!result)
                return;
        
            TransferBaseField_Equal.Equal(xCollection.Count, yCollection.Count, ref result);
            if (!result)
                return;
        
            int len = xCollection.Count;
            var xEnumerator = xCollection.GetEnumerator();
            var yEnumerator = yCollection.GetEnumerator();
            
            for (int i = 0; i < len; i++)
            {
                xEnumerator.MoveNext();
                yEnumerator.MoveNext();
        
                System.Collections.Generic.KeyValuePair<int, ChessPlayerConfig> x = xEnumerator.Current;
                System.Collections.Generic.KeyValuePair<int, ChessPlayerConfig> y = yEnumerator.Current;
        
                EqualUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_ChessPlayerConfig_End_RefEqual(x, y, ref result);
        
                if (!result)
                    return;
            }
        }

        // UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> Ref Func
        public static void UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_RefEqual(UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> x, UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> y, ref bool result)
        {
            if(x == null || y == null)
            {
                TransferBaseField_Equal.Equal(x, y , ref result);
                return;
            }
                
            int index1 = WriteUniqueInfo_ConfigPool.instance.UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_GetIndex(x);
            int index2 = WriteUniqueInfo_ConfigPool.instance.UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_GetIndex(y);
            TransferBaseField_Equal.Equal(index1, index2, ref result);
        }

    }
}
#endif
