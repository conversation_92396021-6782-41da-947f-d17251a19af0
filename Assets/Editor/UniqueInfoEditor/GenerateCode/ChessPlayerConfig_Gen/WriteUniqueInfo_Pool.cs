#if ACGGAME_CLIENT
using System.Collections.Generic;
using System.IO;
using TKFrame;

namespace UniqueInfo.Editor.ChessPlayerConfig_Gen
{
    public class WriteUniqueInfo_NormalPool
    {
        private static object m_locker = new object();
        private static WriteUniqueInfo_NormalPool m_instance = null;

        public static WriteUniqueInfo_NormalPool instance
        {
            get
            {
                if (m_instance == null)
                {
                    lock (m_locker)
                    {
                        if (m_instance == null)
                        {
                            m_instance = new WriteUniqueInfo_NormalPool();
                        }
                    }
                }

                return m_instance;
            }
        }

        // Class Pool

        #region UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> Data
        
        private List<UniqueInfo.ConfigHashMap<int, ChessPlayerConfig>> m_UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Data = new List<UniqueInfo.ConfigHashMap<int, ChessPlayerConfig>>();
        
        public void Collect_UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Data(UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> data)
        {
            CollectUniqueInfo_Config.UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_RefCollect(ref data);
            m_UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Data.Add(data);
        }
        
        private void Write_UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Data(BinaryWriter bw)
        {
            TransferBaseField_Write.Write(m_UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Data.Count, bw);
            foreach (var data in m_UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Data)
            {
                WriteUniqueInfo_Config.UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_RefWrite(data, bw);
            }
        }
                
        #endregion

        
        public void GeneratePoolData(BinaryWriter bw)
        {
            // Generate Pool
            Write_UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Data(bw);
        }

        public static void Clear()
        {
            m_instance = null;
        }
    }
}
#endif
