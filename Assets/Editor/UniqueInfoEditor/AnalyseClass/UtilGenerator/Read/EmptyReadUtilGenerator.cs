using System.IO;

namespace UniqueInfo.Editor
{
    public class EmptyReadUtilGenerator : BaseUtilGenerator
    {
        protected override string GetContentTemplateFile()
        {
            return GetTemplateDir() + "Read/EmptyReadUtilContentTemplate.txt";
        }
        
        protected override string GetAssetContentTemplateFile()
        {
            return GetTemplateDir() + "Read/EmptyReadUtilContentTemplate.txt";
        }
        
        protected override string GetConfigContentTemplateFile()
        {
            return GetTemplateDir() + "Read/EmptyReadUtilContentTemplate.txt";
        }

        protected override string GetTemplateFile()
        {
            return GetTemplateDir() + "Read/ReadUtilTemplate.txt";
        }

        protected override string GetGeneratePath()
        {
            return CodeDir + "/UniqueInfoReadUtil.cs";
        }

        protected override void Init()
        {
            base.Init();
            string[] codeDirFiles = Directory.GetFiles(CodeDir, "*");
            foreach (string file in codeDirFiles)
            {
                File.Delete(file);
            }
        }
    }
}

