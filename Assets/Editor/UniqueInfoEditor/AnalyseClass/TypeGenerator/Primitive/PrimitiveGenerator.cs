using System.IO;

namespace UniqueInfo.Editor
{
    public class PrimitiveGenerator : BaseGenerator
    {
        public override TypeGenerator GetTypeGenerator()
        {
            return TypeGenerator.Primitive;
        }
        
        #region Class

        public override void WriteClassCall(TextWriter sw)
        {
            
        }

        protected override void WriteClassFunctionImpl(TextWriter sw, SplitContent splitContent, StringTextWriter sb)
        {
            
        }

        #endregion
        
        #region Ref
        
        public override void WriteRefCall(TextWriter sw, string fieldName)
        {
            sw.WriteLine(m_config.GetRefCallFormat(m_transferType), fieldName);
        }
        
        protected override void WriteRefFunctionImpl(TextWriter sw, SplitContent splitContent, StringTextWriter sb)
        {
            
        }

        #endregion
        
        #region Pool
        
        protected override void WriteClassPoolFunctionImpl(TextWriter sw, SplitContent splitContent, StringTextWriter sb)
        {
        }
        
        #endregion
        
        #region Pool Call
        
        protected override void WritePoolCallImpl(TextWriter sw)
        {
        }

        #endregion
        
        #region Data

        protected override void WriteDataPoolFunctionImpl(TextWriter sw, SplitContent splitContent)
        {
        }
        
        #endregion
        
        #region Data Call

        protected override void WriteDataPoolCallImpl(TextWriter sw)
        {
        }
        
        #endregion
        
        public static BaseGenerator Begin()
        {
            return MicroIClearPool<PrimitiveGenerator>.Get();
        }

        public override void End()
        {
            MicroIClearPool<PrimitiveGenerator>.Release(this);
        }
    }
}