using System.IO;

namespace UniqueInfo.Editor
{
    public class GenericClassGenerator : ClassGenerator
    {
        public override TypeGenerator GetTypeGenerator()
        {
            return TypeGenerator.GenericClass;
        }

        #region Class

        protected override void WriteClassFunctionImpl(TextWriter sw, SplitContent splitContent, StringTextWriter sb)
        {
            if (m_typeNode.argTypeNodes != null)
            {
                foreach (TypeNode argTypeNode in m_typeNode.argTypeNodes)
                {
                    BaseGenerator generator = GeneratorManager.ins.Begin(argTypeNode, m_transferType);
                    generator.WriteClassFunction(sw);
                    generator.WriteRefFunction(sw);
                    GeneratorManager.ins.End(generator);
                }
            }
            base.WriteClassFunctionImpl(sw, splitContent, sb);
        }
        
        #endregion

        #region Pool
        
        protected override void WriteClassPoolFunctionImpl(TextWriter sw, SplitContent splitContent, StringTextWriter sb)
        {
            WriteArgsPoolFunction(sw);
            base.WriteClassPoolFunctionImpl(sw, splitContent, sb);
        }

        private void WriteArgsPoolFunction(TextWriter sw)
        {
            if (m_typeNode.argTypeNodes != null)
            {
                foreach (TypeNode argTypeNode in m_typeNode.argTypeNodes)
                {
                    BaseGenerator generator = GeneratorManager.ins.Begin(argTypeNode, m_transferType);
                    generator.WriteClassPoolFunction(sw);
                    GeneratorManager.ins.End(generator);
                }
            }
        }

        protected override void WritePoolCallImpl(TextWriter sw)
        {
            WriteArgsPoolCallImpl(sw);
            base.WritePoolCallImpl(sw);
        }

        private void WriteArgsPoolCallImpl(TextWriter sw)
        {
            if (m_typeNode.argTypeNodes != null)
            {
                foreach (TypeNode argTypeNode in m_typeNode.argTypeNodes)
                {
                    BaseGenerator generator = GeneratorManager.ins.Begin(argTypeNode, m_transferType);
                    generator.WritePoolCall(sw);
                    GeneratorManager.ins.End(generator);
                }
            }
        }
        
        #endregion

        public static BaseGenerator Begin()
        {
            return MicroIClearPool<GenericClassGenerator>.Get();
        }

        public override void End()
        {
            MicroIClearPool<GenericClassGenerator>.Release(this);
        }
    }
}