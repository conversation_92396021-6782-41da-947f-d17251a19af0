using System.Collections.Generic;
using System.IO;

namespace UniqueInfo.Editor
{
    public class KeyValuePairGenerator : GenericStructGenerator
    {
        public override TypeGenerator GetTypeGenerator()
        {
            return TypeGenerator.KeyValuePair;
        }
        
        #region Class
        
        protected override void WriteClassFunctionImpl(TextWriter sw, SplitContent splitContent, StringTextWriter sb)
        {
            BaseGenerator keyGenerator = GeneratorManager.ins.Begin(m_typeNode.argTypeNodes[0], m_transferType);
            keyGenerator.WriteClassFunction(sw);
            keyGenerator.WriteRefFunction(sw);
            GeneratorManager.ins.End(keyGenerator);
            
            BaseGenerator valueGenerator = GeneratorManager.ins.Begin(m_typeNode.argTypeNodes[1], m_transferType);
            valueGenerator.WriteClassFunction(sw);
            valueGenerator.WriteRefFunction(sw);
            GeneratorManager.ins.End(valueGenerator);
            
            WriteOneClass(sw, splitContent, sb);
        }
        
        protected override void WriteOneClass(TextWriter sw, SplitContent splitContent, StringTextWriter sb)
        {
            WriteClassField(sb);
            ReplaceContent(splitContent, m_contentMark, sb);
            WriteClass(sw, splitContent);
        }

        private void WriteClassField(TextWriter sw)
        {
            sw.WriteLine("// Field");

            string keyFieldName = ".Key";
            string valueFieldName = ".Value";
            if (m_transferType.transferType == TransferType.Collect || m_transferType.transferType == TransferType.Read)
            {
                keyFieldName = "Key";
                valueFieldName = "Value";
            }

            TypeNode keyTypeNode = m_typeNode.argTypeNodes[0];
            BaseGenerator keyGenerator = GeneratorManager.ins.Begin(keyTypeNode, m_transferType);
            if (m_transferType.transferType == TransferType.Read)
            {
                keyGenerator.WriteTypeInit(sw, "dataKey");
            }
            keyGenerator.WriteRefCall(sw, keyFieldName);
            GeneratorManager.ins.End(keyGenerator);

            TypeNode valueTypeNode = m_typeNode.argTypeNodes[1];
            BaseGenerator valueGenerator = GeneratorManager.ins.Begin(valueTypeNode, m_transferType);
            if (m_transferType.transferType == TransferType.Read)
            {
                valueGenerator.WriteTypeInit(sw, "dataValue");
            }
            valueGenerator.WriteRefCall(sw, valueFieldName);
            GeneratorManager.ins.End(valueGenerator);
        }
        
        private void WriteClass(TextWriter sw, SplitContent splitContent)
        {
            WriteFuncType(sw, "Class Func");
            TypeGeneratorUtil.ReplaceData(sw, splitContent, m_typeNode);
            sw.WriteLine();
        }
        
        #endregion
        
        #region Pool

        protected override void WriteClassPoolFunctionImpl(TextWriter sw, SplitContent splitContent, StringTextWriter sb)
        {
            BaseGenerator keyGenerator = GeneratorManager.ins.Begin(m_typeNode.argTypeNodes[0], m_transferType);
            keyGenerator.WriteClassPoolFunction(sw);
            GeneratorManager.ins.End(keyGenerator);
            
            BaseGenerator valueGenerator = GeneratorManager.ins.Begin(m_typeNode.argTypeNodes[1], m_transferType);
            valueGenerator.WriteClassPoolFunction(sw);
            GeneratorManager.ins.End(valueGenerator);
        }
        
        #endregion
        
        #region Pool Call
        
        protected override void WritePoolCallImpl(TextWriter sw)
        {
            BaseGenerator keyGenerator = GeneratorManager.ins.Begin(m_typeNode.argTypeNodes[0], m_transferType);
            keyGenerator.WritePoolCall(sw);
            GeneratorManager.ins.End(keyGenerator);
            
            BaseGenerator valueGenerator = GeneratorManager.ins.Begin(m_typeNode.argTypeNodes[1], m_transferType);
            valueGenerator.WritePoolCall(sw);
            GeneratorManager.ins.End(valueGenerator);
        }

        #endregion
        
        public static BaseGenerator Begin()
        {
            return MicroIClearPool<KeyValuePairGenerator>.Get();
        }

        public override void End()
        {
            MicroIClearPool<KeyValuePairGenerator>.Release(this);
        }
    }
}


