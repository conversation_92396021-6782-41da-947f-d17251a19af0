using System.Collections.Generic;

namespace UniqueInfo.Editor
{
    public class TwoIntKey
    {
        public int typeId;
        public int indexId;

        public static TwoIntKey_Compare Comparer = new TwoIntKey_Compare();

        public TwoIntKey()
        {
            
        }

        public TwoIntKey(TwoIntKey other)
        {
            typeId = other.typeId;
            indexId = other.indexId;
        }
    }

    public class TwoIntKey_Compare : IEqualityComparer<TwoIntKey>
    {
        public bool Equals(TwoIntKey x, TwoIntKey y)
        {
            if (x.typeId == y.typeId && x.indexId == y.indexId)
                return true;
            return false;
        }

        public int GetHashCode(TwoIntKey obj)
        {
            return obj.typeId * obj.indexId;
        }
    }
}


