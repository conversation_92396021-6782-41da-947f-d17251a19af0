#region {1}

private List<int> m_{0}_Index_List = new List<int>();
private Dictionary<{1}, int> m_{0}_Ref_Index_Dict = new new Dictionary<{1}, int>();
private Dictionary<TwoIntKey, int> m_{1}_Data_Index_Dict = new Dictionary<TwoIntKey, int>(TwoIntKey.Comparer);
private TwoIntKey m_tmpKey = new TwoIntKey();

private List<{1}> m_{0}_List = new List<{1}>();
private Dictionary<{1}, int> m_{0}_Data_Dict = new Dictionary<{1}, int>(new {0}_Compare());
private Dictionary<{1}, int> m_{0}_Ref_Dict = new Dictionary<{1}, int>();

public int {0}_GetIndex({1} data)
{
    int result = -1;
    if (m_{0}_Ref_Index_Dict.TryGetValue(data, out result))
    {
        return result;
    }

    {0}_To_Key(data, m_tmpKey);

    if (!m_{1}_Data_Index_Dict.TryGetValue(m_tmpKey, out result))
    {
        result = m_{0}_Index_List.Count;
        TwoIntKey newKey = new TwoIntKey(m_tmpKey);
        m_{1}_Data_Index_Dict.Add(newKey, result);
        m_{0}_Index_List.Add(m_tmpKey.typeId);
        m_{0}_Index_List.Add(m_tmpKey.indexId);
    }

    m_{0}_Ref_Index_Dict.Add(data, result);

    return result;
}

private void {0}_To_Key({1} data, TwoIntKey key)
{
    /*-----child1-----*/

    key.typeId = -1;
    key.indexId = {0}_Get_Internal_Index(data);
}

private int {0}_Get_Internal_Index({1} data)
{
    int result = -1;
    if (m_{0}_Ref_Dict.TryGetValue(data, out result))
    {
        return result;
    }
    
    if (!m_{0}_Data_Dict.TryGetValue(data, out result))
    {
        result = m_{0}_List.Count;
        m_{0}_Data_Dict.Add(data, result);
        m_{0}_List.Add(data);
    }

    m_{0}_Ref_Dict.Add(data, result);

    return result;
}

public {1} {0}_GetData(int index)
{
    int typeId = m_{0}_Index_List[index];
    int indexId = m_{0}_Index_List[index + 1];

    switch (typeId)
    {
        /*-----child2-----*/
        default:
            break;
    }

    return m_{0}_List[indexId];
}

private void {0}_Write_Index_Lists(BinaryWriter bw)
{
    TransferBaseField_Write.Write(m_{0}_Index_List.Count, bw);
    foreach (int index in m_{0}_Index_List)
    {
        TransferBaseField_Write.Write(index, bw);
    }
}

private void {0}_Write_Lists(BinaryWriter bw)
{
    TransferBaseField_Write.Write(m_{0}_List.Count, bw);
    foreach (var data in m_{0}_List)
    {
        /*-----content-----*/
    }
}

private class {0}_Compare : IEqualityComparer<{1}>
{
    public bool Equals({1} x, {1} y)
    {
        bool result = true;
        EqualUniqueInfo.{0}_Equal(x, y, ref result);
        return result;
    }
    
    public int GetHashCode({1} data)
    {
        int hashCode = 0;
        HashUniqueInfo.{0}_Hash(data, ref hashCode);
        return hashCode;
    }
}

#endregion