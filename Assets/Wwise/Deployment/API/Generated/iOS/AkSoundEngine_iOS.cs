#if UNITY_IOS && ! UNITY_EDITOR
//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 3.0.12
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------


public partial class AkSoundEngine {
  public static uint AK_SOUNDBANK_VERSION { get { return AkSoundEnginePINVOKE.CSharp_AK_SOUNDBANK_VERSION_get(); } 
  }

  public static ushort AK_INT { get { return AkSoundEnginePINVOKE.CSharp_AK_INT_get(); } 
  }

  public static ushort AK_FLOAT { get { return AkSoundEnginePINVOKE.CSharp_AK_FLOAT_get(); } 
  }

  public static byte AK_INTERLEAVED { get { return AkSoundEnginePINVOKE.CSharp_AK_INTERLEAVED_get(); } 
  }

  public static byte AK_NONINTERLEAVED { get { return AkSoundEnginePINVOKE.CSharp_AK_NONINTERLEAVED_get(); } 
  }

  public static uint AK_LE_NATIVE_BITSPERSAMPLE { get { return AkSoundEnginePINVOKE.CSharp_AK_LE_NATIVE_BITSPERSAMPLE_get(); } 
  }

  public static uint AK_LE_NATIVE_SAMPLETYPE { get { return AkSoundEnginePINVOKE.CSharp_AK_LE_NATIVE_SAMPLETYPE_get(); } 
  }

  public static uint AK_LE_NATIVE_INTERLEAVE { get { return AkSoundEnginePINVOKE.CSharp_AK_LE_NATIVE_INTERLEAVE_get(); } 
  }

  public static uint DynamicSequenceOpen(UnityEngine.GameObject in_gameObjectID, uint in_uFlags, AkCallbackManager.EventCallback in_pfnCallback, object in_pCookie, AkDynamicSequenceType in_eDynamicSequenceType) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

	in_pCookie = AkCallbackManager.EventCallbackPackage.Create(in_pfnCallback, in_pCookie, ref in_uFlags);
    {
		uint ret = AkSoundEnginePINVOKE.CSharp_DynamicSequenceOpen__SWIG_0(in_gameObjectID_id, in_uFlags, in_uFlags != 0 ? (global::System.IntPtr)1 : global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero, (int)in_eDynamicSequenceType);
		AkCallbackManager.SetLastAddedPlayingID(ret);
		return ret;
	}
  }

  public static uint DynamicSequenceOpen(UnityEngine.GameObject in_gameObjectID, uint in_uFlags, AkCallbackManager.EventCallback in_pfnCallback, object in_pCookie) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

	in_pCookie = AkCallbackManager.EventCallbackPackage.Create(in_pfnCallback, in_pCookie, ref in_uFlags);
    {
		uint ret = AkSoundEnginePINVOKE.CSharp_DynamicSequenceOpen__SWIG_1(in_gameObjectID_id, in_uFlags, in_uFlags != 0 ? (global::System.IntPtr)1 : global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero);
		AkCallbackManager.SetLastAddedPlayingID(ret);
		return ret;
	}
  }

  public static uint DynamicSequenceOpen(UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    {
		uint ret = AkSoundEnginePINVOKE.CSharp_DynamicSequenceOpen__SWIG_2(in_gameObjectID_id);
		AkCallbackManager.SetLastAddedPlayingID(ret);
		return ret;
	}
  }

  public static AKRESULT DynamicSequenceClose(uint in_playingID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_DynamicSequenceClose(in_playingID); }

  public static AKRESULT DynamicSequencePlay(uint in_playingID, int in_uTransitionDuration, AkCurveInterpolation in_eFadeCurve) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_DynamicSequencePlay__SWIG_0(in_playingID, in_uTransitionDuration, (int)in_eFadeCurve); }

  public static AKRESULT DynamicSequencePlay(uint in_playingID, int in_uTransitionDuration) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_DynamicSequencePlay__SWIG_1(in_playingID, in_uTransitionDuration); }

  public static AKRESULT DynamicSequencePlay(uint in_playingID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_DynamicSequencePlay__SWIG_2(in_playingID); }

  public static AKRESULT DynamicSequencePause(uint in_playingID, int in_uTransitionDuration, AkCurveInterpolation in_eFadeCurve) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_DynamicSequencePause__SWIG_0(in_playingID, in_uTransitionDuration, (int)in_eFadeCurve); }

  public static AKRESULT DynamicSequencePause(uint in_playingID, int in_uTransitionDuration) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_DynamicSequencePause__SWIG_1(in_playingID, in_uTransitionDuration); }

  public static AKRESULT DynamicSequencePause(uint in_playingID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_DynamicSequencePause__SWIG_2(in_playingID); }

  public static AKRESULT DynamicSequenceResume(uint in_playingID, int in_uTransitionDuration, AkCurveInterpolation in_eFadeCurve) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_DynamicSequenceResume__SWIG_0(in_playingID, in_uTransitionDuration, (int)in_eFadeCurve); }

  public static AKRESULT DynamicSequenceResume(uint in_playingID, int in_uTransitionDuration) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_DynamicSequenceResume__SWIG_1(in_playingID, in_uTransitionDuration); }

  public static AKRESULT DynamicSequenceResume(uint in_playingID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_DynamicSequenceResume__SWIG_2(in_playingID); }

  public static AKRESULT DynamicSequenceStop(uint in_playingID, int in_uTransitionDuration, AkCurveInterpolation in_eFadeCurve) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_DynamicSequenceStop__SWIG_0(in_playingID, in_uTransitionDuration, (int)in_eFadeCurve); }

  public static AKRESULT DynamicSequenceStop(uint in_playingID, int in_uTransitionDuration) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_DynamicSequenceStop__SWIG_1(in_playingID, in_uTransitionDuration); }

  public static AKRESULT DynamicSequenceStop(uint in_playingID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_DynamicSequenceStop__SWIG_2(in_playingID); }

  public static AKRESULT DynamicSequenceBreak(uint in_playingID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_DynamicSequenceBreak(in_playingID); }

  public static AKRESULT DynamicSequenceGetPauseTimes(uint in_playingID, out uint out_uTime, out uint out_uDuration) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_DynamicSequenceGetPauseTimes(in_playingID, out out_uTime, out out_uDuration); }

  public static AkPlaylist DynamicSequenceLockPlaylist(uint in_playingID) {
    global::System.IntPtr cPtr = AkSoundEnginePINVOKE.CSharp_DynamicSequenceLockPlaylist(in_playingID);
    AkPlaylist ret = (cPtr == global::System.IntPtr.Zero) ? null : new AkPlaylist(cPtr, false);
    return ret;
  }

  public static AKRESULT DynamicSequenceUnlockPlaylist(uint in_playingID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_DynamicSequenceUnlockPlaylist(in_playingID); }

  public static bool IsInitialized() { return AkSoundEnginePINVOKE.CSharp_IsInitialized(); }

  public static AKRESULT GetAudioSettings(AkAudioSettings out_audioSettings) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetAudioSettings(AkAudioSettings.getCPtr(out_audioSettings)); }

  public static AkChannelConfig GetSpeakerConfiguration(ulong in_idOutput) {
    AkChannelConfig ret = new AkChannelConfig(AkSoundEnginePINVOKE.CSharp_GetSpeakerConfiguration__SWIG_0(in_idOutput), true);
    return ret;
  }

  public static AkChannelConfig GetSpeakerConfiguration() {
    AkChannelConfig ret = new AkChannelConfig(AkSoundEnginePINVOKE.CSharp_GetSpeakerConfiguration__SWIG_1(), true);
    return ret;
  }

  public static AKRESULT GetPanningRule(out int out_ePanningRule, ulong in_idOutput) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetPanningRule__SWIG_0(out out_ePanningRule, in_idOutput); }

  public static AKRESULT GetPanningRule(out int out_ePanningRule) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetPanningRule__SWIG_1(out out_ePanningRule); }

  public static AKRESULT SetPanningRule(AkPanningRule in_ePanningRule, ulong in_idOutput) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetPanningRule__SWIG_0((int)in_ePanningRule, in_idOutput); }

  public static AKRESULT SetPanningRule(AkPanningRule in_ePanningRule) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetPanningRule__SWIG_1((int)in_ePanningRule); }

  public static AKRESULT GetSpeakerAngles(float[] io_pfSpeakerAngles, ref uint io_uNumAngles, out float out_fHeightAngle, ulong in_idOutput) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetSpeakerAngles__SWIG_0(io_pfSpeakerAngles, ref io_uNumAngles, out out_fHeightAngle, in_idOutput); }

  public static AKRESULT GetSpeakerAngles(float[] io_pfSpeakerAngles, ref uint io_uNumAngles, out float out_fHeightAngle) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetSpeakerAngles__SWIG_1(io_pfSpeakerAngles, ref io_uNumAngles, out out_fHeightAngle); }

  public static AKRESULT SetSpeakerAngles(float[] in_pfSpeakerAngles, uint in_uNumAngles, float in_fHeightAngle, ulong in_idOutput) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetSpeakerAngles__SWIG_0(in_pfSpeakerAngles, in_uNumAngles, in_fHeightAngle, in_idOutput); }

  public static AKRESULT SetSpeakerAngles(float[] in_pfSpeakerAngles, uint in_uNumAngles, float in_fHeightAngle) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetSpeakerAngles__SWIG_1(in_pfSpeakerAngles, in_uNumAngles, in_fHeightAngle); }

  public static AKRESULT SetVolumeThreshold(float in_fVolumeThresholdDB) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetVolumeThreshold(in_fVolumeThresholdDB); }

  public static AKRESULT SetMaxNumVoicesLimit(ushort in_maxNumberVoices) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetMaxNumVoicesLimit(in_maxNumberVoices); }

  public static AKRESULT RenderAudio(bool in_bAllowSyncRender) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_RenderAudio__SWIG_0(in_bAllowSyncRender); }

  public static AKRESULT RenderAudio() { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_RenderAudio__SWIG_1(); }

  public static AKRESULT RegisterPluginDLL(string in_DllName, string in_DllPath) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_RegisterPluginDLL__SWIG_0(in_DllName, in_DllPath); }

  public static AKRESULT RegisterPluginDLL(string in_DllName) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_RegisterPluginDLL__SWIG_1(in_DllName); }

  public static uint GetIDFromString(string in_pszString) { return AkSoundEnginePINVOKE.CSharp_GetIDFromString__SWIG_0(in_pszString); }

  public static uint PostEvent(uint in_eventID, UnityEngine.GameObject in_gameObjectID, uint in_uFlags, AkCallbackManager.EventCallback in_pfnCallback, object in_pCookie, uint in_cExternals, AkExternalSourceInfoArray in_pExternalSources, uint in_PlayingID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

	in_pCookie = AkCallbackManager.EventCallbackPackage.Create(in_pfnCallback, in_pCookie, ref in_uFlags);
    {
		uint ret = AkSoundEnginePINVOKE.CSharp_PostEvent__SWIG_0(in_eventID, in_gameObjectID_id, in_uFlags, in_uFlags != 0 ? (global::System.IntPtr)1 : global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero, in_cExternals, in_pExternalSources.GetBuffer(), in_PlayingID);
		AkCallbackManager.SetLastAddedPlayingID(ret);
		return ret;
	}
  }

  public static uint PostEvent(uint in_eventID, UnityEngine.GameObject in_gameObjectID, uint in_uFlags, AkCallbackManager.EventCallback in_pfnCallback, object in_pCookie, uint in_cExternals, AkExternalSourceInfoArray in_pExternalSources) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

	in_pCookie = AkCallbackManager.EventCallbackPackage.Create(in_pfnCallback, in_pCookie, ref in_uFlags);
    {
		uint ret = AkSoundEnginePINVOKE.CSharp_PostEvent__SWIG_1(in_eventID, in_gameObjectID_id, in_uFlags, in_uFlags != 0 ? (global::System.IntPtr)1 : global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero, in_cExternals, in_pExternalSources.GetBuffer());
		AkCallbackManager.SetLastAddedPlayingID(ret);
		return ret;
	}
  }

  public static uint PostEvent(uint in_eventID, UnityEngine.GameObject in_gameObjectID, uint in_uFlags, AkCallbackManager.EventCallback in_pfnCallback, object in_pCookie) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

	in_pCookie = AkCallbackManager.EventCallbackPackage.Create(in_pfnCallback, in_pCookie, ref in_uFlags);
    {
		uint ret = AkSoundEnginePINVOKE.CSharp_PostEvent__SWIG_2(in_eventID, in_gameObjectID_id, in_uFlags, in_uFlags != 0 ? (global::System.IntPtr)1 : global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero);
		AkCallbackManager.SetLastAddedPlayingID(ret);
		return ret;
	}
  }

  public static uint PostEvent(uint in_eventID, UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    {
		uint ret = AkSoundEnginePINVOKE.CSharp_PostEvent__SWIG_3(in_eventID, in_gameObjectID_id);
		AkCallbackManager.SetLastAddedPlayingID(ret);
		return ret;
	}
  }

  public static uint PostEvent(string in_pszEventName, UnityEngine.GameObject in_gameObjectID, uint in_uFlags, AkCallbackManager.EventCallback in_pfnCallback, object in_pCookie, uint in_cExternals, AkExternalSourceInfoArray in_pExternalSources, uint in_PlayingID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

	in_pCookie = AkCallbackManager.EventCallbackPackage.Create(in_pfnCallback, in_pCookie, ref in_uFlags);
    {
		uint ret = AkSoundEnginePINVOKE.CSharp_PostEvent__SWIG_4(in_pszEventName, in_gameObjectID_id, in_uFlags, in_uFlags != 0 ? (global::System.IntPtr)1 : global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero, in_cExternals, in_pExternalSources.GetBuffer(), in_PlayingID);
		AkCallbackManager.SetLastAddedPlayingID(ret);
		return ret;
	}
  }

  public static uint PostEvent(string in_pszEventName, UnityEngine.GameObject in_gameObjectID, uint in_uFlags, AkCallbackManager.EventCallback in_pfnCallback, object in_pCookie, uint in_cExternals, AkExternalSourceInfoArray in_pExternalSources) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

	in_pCookie = AkCallbackManager.EventCallbackPackage.Create(in_pfnCallback, in_pCookie, ref in_uFlags);
    {
		uint ret = AkSoundEnginePINVOKE.CSharp_PostEvent__SWIG_5(in_pszEventName, in_gameObjectID_id, in_uFlags, in_uFlags != 0 ? (global::System.IntPtr)1 : global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero, in_cExternals, in_pExternalSources.GetBuffer());
		AkCallbackManager.SetLastAddedPlayingID(ret);
		return ret;
	}
  }

  public static uint PostEvent(string in_pszEventName, UnityEngine.GameObject in_gameObjectID, uint in_uFlags, AkCallbackManager.EventCallback in_pfnCallback, object in_pCookie) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

	in_pCookie = AkCallbackManager.EventCallbackPackage.Create(in_pfnCallback, in_pCookie, ref in_uFlags);
    {
		uint ret = AkSoundEnginePINVOKE.CSharp_PostEvent__SWIG_6(in_pszEventName, in_gameObjectID_id, in_uFlags, in_uFlags != 0 ? (global::System.IntPtr)1 : global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero);
		AkCallbackManager.SetLastAddedPlayingID(ret);
		return ret;
	}
  }

  public static uint PostEvent(string in_pszEventName, UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    {
		uint ret = AkSoundEnginePINVOKE.CSharp_PostEvent__SWIG_7(in_pszEventName, in_gameObjectID_id);
		AkCallbackManager.SetLastAddedPlayingID(ret);
		return ret;
	}
  }

  public static AKRESULT ExecuteActionOnEvent(uint in_eventID, AkActionOnEventType in_ActionType, UnityEngine.GameObject in_gameObjectID, int in_uTransitionDuration, AkCurveInterpolation in_eFadeCurve, uint in_PlayingID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ExecuteActionOnEvent__SWIG_0(in_eventID, (int)in_ActionType, in_gameObjectID_id, in_uTransitionDuration, (int)in_eFadeCurve, in_PlayingID); }
  }

  public static AKRESULT ExecuteActionOnEvent(uint in_eventID, AkActionOnEventType in_ActionType, UnityEngine.GameObject in_gameObjectID, int in_uTransitionDuration, AkCurveInterpolation in_eFadeCurve) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ExecuteActionOnEvent__SWIG_1(in_eventID, (int)in_ActionType, in_gameObjectID_id, in_uTransitionDuration, (int)in_eFadeCurve); }
  }

  public static AKRESULT ExecuteActionOnEvent(uint in_eventID, AkActionOnEventType in_ActionType, UnityEngine.GameObject in_gameObjectID, int in_uTransitionDuration) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ExecuteActionOnEvent__SWIG_2(in_eventID, (int)in_ActionType, in_gameObjectID_id, in_uTransitionDuration); }
  }

  public static AKRESULT ExecuteActionOnEvent(uint in_eventID, AkActionOnEventType in_ActionType, UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ExecuteActionOnEvent__SWIG_3(in_eventID, (int)in_ActionType, in_gameObjectID_id); }
  }

  public static AKRESULT ExecuteActionOnEvent(uint in_eventID, AkActionOnEventType in_ActionType) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ExecuteActionOnEvent__SWIG_4(in_eventID, (int)in_ActionType); }

  public static AKRESULT ExecuteActionOnEvent(string in_pszEventName, AkActionOnEventType in_ActionType, UnityEngine.GameObject in_gameObjectID, int in_uTransitionDuration, AkCurveInterpolation in_eFadeCurve, uint in_PlayingID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ExecuteActionOnEvent__SWIG_5(in_pszEventName, (int)in_ActionType, in_gameObjectID_id, in_uTransitionDuration, (int)in_eFadeCurve, in_PlayingID); }
  }

  public static AKRESULT ExecuteActionOnEvent(string in_pszEventName, AkActionOnEventType in_ActionType, UnityEngine.GameObject in_gameObjectID, int in_uTransitionDuration, AkCurveInterpolation in_eFadeCurve) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ExecuteActionOnEvent__SWIG_6(in_pszEventName, (int)in_ActionType, in_gameObjectID_id, in_uTransitionDuration, (int)in_eFadeCurve); }
  }

  public static AKRESULT ExecuteActionOnEvent(string in_pszEventName, AkActionOnEventType in_ActionType, UnityEngine.GameObject in_gameObjectID, int in_uTransitionDuration) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ExecuteActionOnEvent__SWIG_7(in_pszEventName, (int)in_ActionType, in_gameObjectID_id, in_uTransitionDuration); }
  }

  public static AKRESULT ExecuteActionOnEvent(string in_pszEventName, AkActionOnEventType in_ActionType, UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ExecuteActionOnEvent__SWIG_8(in_pszEventName, (int)in_ActionType, in_gameObjectID_id); }
  }

  public static AKRESULT ExecuteActionOnEvent(string in_pszEventName, AkActionOnEventType in_ActionType) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ExecuteActionOnEvent__SWIG_9(in_pszEventName, (int)in_ActionType); }

  public static AKRESULT PostMIDIOnEvent(uint in_eventID, UnityEngine.GameObject in_gameObjectID, AkMIDIPostArray in_pPosts, ushort in_uNumPosts) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PostMIDIOnEvent(in_eventID, in_gameObjectID_id, in_pPosts.GetBuffer(), in_uNumPosts); }
  }

  public static AKRESULT StopMIDIOnEvent(uint in_eventID, UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_StopMIDIOnEvent__SWIG_0(in_eventID, in_gameObjectID_id); }
  }

  public static AKRESULT StopMIDIOnEvent(uint in_eventID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_StopMIDIOnEvent__SWIG_1(in_eventID); }

  public static AKRESULT StopMIDIOnEvent() { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_StopMIDIOnEvent__SWIG_2(); }

  public static AKRESULT PinEventInStreamCache(uint in_eventID, sbyte in_uActivePriority, sbyte in_uInactivePriority) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PinEventInStreamCache__SWIG_0(in_eventID, in_uActivePriority, in_uInactivePriority); }

  public static AKRESULT PinEventInStreamCache(string in_pszEventName, sbyte in_uActivePriority, sbyte in_uInactivePriority) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PinEventInStreamCache__SWIG_1(in_pszEventName, in_uActivePriority, in_uInactivePriority); }

  public static AKRESULT UnpinEventInStreamCache(uint in_eventID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_UnpinEventInStreamCache__SWIG_0(in_eventID); }

  public static AKRESULT UnpinEventInStreamCache(string in_pszEventName) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_UnpinEventInStreamCache__SWIG_1(in_pszEventName); }

  public static AKRESULT GetBufferStatusForPinnedEvent(uint in_eventID, out float out_fPercentBuffered, out int out_bCachePinnedMemoryFull) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetBufferStatusForPinnedEvent__SWIG_0(in_eventID, out out_fPercentBuffered, out out_bCachePinnedMemoryFull); }

  public static AKRESULT GetBufferStatusForPinnedEvent(string in_pszEventName, out float out_fPercentBuffered, out int out_bCachePinnedMemoryFull) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetBufferStatusForPinnedEvent__SWIG_1(in_pszEventName, out out_fPercentBuffered, out out_bCachePinnedMemoryFull); }

  public static AKRESULT SeekOnEvent(uint in_eventID, UnityEngine.GameObject in_gameObjectID, int in_iPosition, bool in_bSeekToNearestMarker, uint in_PlayingID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SeekOnEvent__SWIG_0(in_eventID, in_gameObjectID_id, in_iPosition, in_bSeekToNearestMarker, in_PlayingID); }
  }

  public static AKRESULT SeekOnEvent(uint in_eventID, UnityEngine.GameObject in_gameObjectID, int in_iPosition, bool in_bSeekToNearestMarker) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SeekOnEvent__SWIG_1(in_eventID, in_gameObjectID_id, in_iPosition, in_bSeekToNearestMarker); }
  }

  public static AKRESULT SeekOnEvent(uint in_eventID, UnityEngine.GameObject in_gameObjectID, int in_iPosition) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SeekOnEvent__SWIG_2(in_eventID, in_gameObjectID_id, in_iPosition); }
  }

  public static AKRESULT SeekOnEvent(string in_pszEventName, UnityEngine.GameObject in_gameObjectID, int in_iPosition, bool in_bSeekToNearestMarker, uint in_PlayingID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SeekOnEvent__SWIG_3(in_pszEventName, in_gameObjectID_id, in_iPosition, in_bSeekToNearestMarker, in_PlayingID); }
  }

  public static AKRESULT SeekOnEvent(string in_pszEventName, UnityEngine.GameObject in_gameObjectID, int in_iPosition, bool in_bSeekToNearestMarker) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SeekOnEvent__SWIG_4(in_pszEventName, in_gameObjectID_id, in_iPosition, in_bSeekToNearestMarker); }
  }

  public static AKRESULT SeekOnEvent(string in_pszEventName, UnityEngine.GameObject in_gameObjectID, int in_iPosition) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SeekOnEvent__SWIG_5(in_pszEventName, in_gameObjectID_id, in_iPosition); }
  }

  public static AKRESULT SeekOnEvent(uint in_eventID, UnityEngine.GameObject in_gameObjectID, float in_fPercent, bool in_bSeekToNearestMarker, uint in_PlayingID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SeekOnEvent__SWIG_9(in_eventID, in_gameObjectID_id, in_fPercent, in_bSeekToNearestMarker, in_PlayingID); }
  }

  public static AKRESULT SeekOnEvent(uint in_eventID, UnityEngine.GameObject in_gameObjectID, float in_fPercent, bool in_bSeekToNearestMarker) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SeekOnEvent__SWIG_10(in_eventID, in_gameObjectID_id, in_fPercent, in_bSeekToNearestMarker); }
  }

  public static AKRESULT SeekOnEvent(uint in_eventID, UnityEngine.GameObject in_gameObjectID, float in_fPercent) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SeekOnEvent__SWIG_11(in_eventID, in_gameObjectID_id, in_fPercent); }
  }

  public static AKRESULT SeekOnEvent(string in_pszEventName, UnityEngine.GameObject in_gameObjectID, float in_fPercent, bool in_bSeekToNearestMarker, uint in_PlayingID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SeekOnEvent__SWIG_12(in_pszEventName, in_gameObjectID_id, in_fPercent, in_bSeekToNearestMarker, in_PlayingID); }
  }

  public static AKRESULT SeekOnEvent(string in_pszEventName, UnityEngine.GameObject in_gameObjectID, float in_fPercent, bool in_bSeekToNearestMarker) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SeekOnEvent__SWIG_13(in_pszEventName, in_gameObjectID_id, in_fPercent, in_bSeekToNearestMarker); }
  }

  public static AKRESULT SeekOnEvent(string in_pszEventName, UnityEngine.GameObject in_gameObjectID, float in_fPercent) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SeekOnEvent__SWIG_14(in_pszEventName, in_gameObjectID_id, in_fPercent); }
  }

  public static void CancelEventCallbackCookie(object in_pCookie) {
		AkCallbackManager.RemoveEventCallbackCookie(in_pCookie);
	}

  public static void CancelEventCallbackGameObject(UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { AkSoundEnginePINVOKE.CSharp_CancelEventCallbackGameObject(in_gameObjectID_id); }
  }

  public static void CancelEventCallback(uint in_playingID) {
		AkCallbackManager.RemoveEventCallback(in_playingID);
	}

  public static AKRESULT GetSourcePlayPosition(uint in_PlayingID, out int out_puPosition, bool in_bExtrapolate) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetSourcePlayPosition__SWIG_0(in_PlayingID, out out_puPosition, in_bExtrapolate); }

  public static AKRESULT GetSourcePlayPosition(uint in_PlayingID, out int out_puPosition) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetSourcePlayPosition__SWIG_1(in_PlayingID, out out_puPosition); }

  public static AKRESULT GetSourceStreamBuffering(uint in_PlayingID, out int out_buffering, out int out_bIsBuffering) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetSourceStreamBuffering(in_PlayingID, out out_buffering, out out_bIsBuffering); }

  public static void StopAll(UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { AkSoundEnginePINVOKE.CSharp_StopAll__SWIG_0(in_gameObjectID_id); }
  }

  public static void StopAll() { AkSoundEnginePINVOKE.CSharp_StopAll__SWIG_1(); }

  public static void StopPlayingID(uint in_playingID, int in_uTransitionDuration, AkCurveInterpolation in_eFadeCurve) { AkSoundEnginePINVOKE.CSharp_StopPlayingID__SWIG_0(in_playingID, in_uTransitionDuration, (int)in_eFadeCurve); }

  public static void StopPlayingID(uint in_playingID, int in_uTransitionDuration) { AkSoundEnginePINVOKE.CSharp_StopPlayingID__SWIG_1(in_playingID, in_uTransitionDuration); }

  public static void StopPlayingID(uint in_playingID) { AkSoundEnginePINVOKE.CSharp_StopPlayingID__SWIG_2(in_playingID); }

  public static void ExecuteActionOnPlayingID(AkActionOnEventType in_ActionType, uint in_playingID, int in_uTransitionDuration, AkCurveInterpolation in_eFadeCurve) { AkSoundEnginePINVOKE.CSharp_ExecuteActionOnPlayingID__SWIG_0((int)in_ActionType, in_playingID, in_uTransitionDuration, (int)in_eFadeCurve); }

  public static void ExecuteActionOnPlayingID(AkActionOnEventType in_ActionType, uint in_playingID, int in_uTransitionDuration) { AkSoundEnginePINVOKE.CSharp_ExecuteActionOnPlayingID__SWIG_1((int)in_ActionType, in_playingID, in_uTransitionDuration); }

  public static void ExecuteActionOnPlayingID(AkActionOnEventType in_ActionType, uint in_playingID) { AkSoundEnginePINVOKE.CSharp_ExecuteActionOnPlayingID__SWIG_2((int)in_ActionType, in_playingID); }

  public static void SetRandomSeed(uint in_uSeed) { AkSoundEnginePINVOKE.CSharp_SetRandomSeed(in_uSeed); }

  public static void MuteBackgroundMusic(bool in_bMute) { AkSoundEnginePINVOKE.CSharp_MuteBackgroundMusic(in_bMute); }

  public static bool GetBackgroundMusicMute() { return AkSoundEnginePINVOKE.CSharp_GetBackgroundMusicMute(); }

  public static AKRESULT SendPluginCustomGameData(uint in_busID, UnityEngine.GameObject in_busObjectID, AkPluginType in_eType, uint in_uCompanyID, uint in_uPluginID, global::System.IntPtr in_pData, uint in_uSizeInBytes) {

	var in_busObjectID_id = AkSoundEngine.GetAkGameObjectID(in_busObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_busObjectID, in_busObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SendPluginCustomGameData(in_busID, in_busObjectID_id, (int)in_eType, in_uCompanyID, in_uPluginID, in_pData, in_uSizeInBytes); }
  }

  public static AKRESULT UnregisterAllGameObj() { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_UnregisterAllGameObj(); }

  public static AKRESULT SetMultiplePositions(UnityEngine.GameObject in_GameObjectID, AkPositionArray in_pPositions, ushort in_NumPositions, AkMultiPositionType in_eMultiPositionType) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetMultiplePositions__SWIG_0(AkSoundEngine.GetAkGameObjectID(in_GameObjectID), in_pPositions.m_Buffer, in_NumPositions, (int)in_eMultiPositionType); }

  public static AKRESULT SetMultiplePositions(UnityEngine.GameObject in_GameObjectID, AkPositionArray in_pPositions, ushort in_NumPositions) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetMultiplePositions__SWIG_1(AkSoundEngine.GetAkGameObjectID(in_GameObjectID), in_pPositions.m_Buffer, in_NumPositions); }

  public static AKRESULT SetMultiplePositions(UnityEngine.GameObject in_GameObjectID, AkChannelEmitterArray in_pPositions, ushort in_NumPositions, AkMultiPositionType in_eMultiPositionType) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetMultiplePositions__SWIG_2(AkSoundEngine.GetAkGameObjectID(in_GameObjectID), in_pPositions.m_Buffer, in_NumPositions, (int)in_eMultiPositionType); }

  public static AKRESULT SetMultiplePositions(UnityEngine.GameObject in_GameObjectID, AkChannelEmitterArray in_pPositions, ushort in_NumPositions) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetMultiplePositions__SWIG_3(AkSoundEngine.GetAkGameObjectID(in_GameObjectID), in_pPositions.m_Buffer, in_NumPositions); }

  public static AKRESULT SetScalingFactor(UnityEngine.GameObject in_GameObjectID, float in_fAttenuationScalingFactor) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetScalingFactor(AkSoundEngine.GetAkGameObjectID(in_GameObjectID), in_fAttenuationScalingFactor); }

  public static AKRESULT ClearBanks() { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ClearBanks(); }

  public static AKRESULT SetBankLoadIOSettings(float in_fThroughput, sbyte in_priority) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetBankLoadIOSettings(in_fThroughput, in_priority); }

  public static AKRESULT LoadBank(string in_pszString, int in_memPoolId, out uint out_bankID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_LoadBank__SWIG_0(in_pszString, in_memPoolId, out out_bankID); }

  public static AKRESULT LoadBank(uint in_bankID, int in_memPoolId) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_LoadBank__SWIG_1(in_bankID, in_memPoolId); }

  public static AKRESULT LoadBank(global::System.IntPtr in_pInMemoryBankPtr, uint in_uInMemoryBankSize, out uint out_bankID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_LoadBank__SWIG_2(in_pInMemoryBankPtr, in_uInMemoryBankSize, out out_bankID); }

  public static AKRESULT LoadBank(global::System.IntPtr in_pInMemoryBankPtr, uint in_uInMemoryBankSize, int in_uPoolForBankMedia, out uint out_bankID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_LoadBank__SWIG_3(in_pInMemoryBankPtr, in_uInMemoryBankSize, in_uPoolForBankMedia, out out_bankID); }

  public static AKRESULT LoadBank(string in_pszString, AkCallbackManager.BankCallback in_pfnBankCallback, object in_pCookie, int in_memPoolId, out uint out_bankID) {
		in_pCookie = new AkCallbackManager.BankCallbackPackage(in_pfnBankCallback, in_pCookie);
    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_LoadBank__SWIG_4(in_pszString, global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero, in_memPoolId, out out_bankID); }
  }

  public static AKRESULT LoadBank(uint in_bankID, AkCallbackManager.BankCallback in_pfnBankCallback, object in_pCookie, int in_memPoolId) {
		in_pCookie = new AkCallbackManager.BankCallbackPackage(in_pfnBankCallback, in_pCookie);
    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_LoadBank__SWIG_5(in_bankID, global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero, in_memPoolId); }
  }

  public static AKRESULT LoadBank(global::System.IntPtr in_pInMemoryBankPtr, uint in_uInMemoryBankSize, AkCallbackManager.BankCallback in_pfnBankCallback, object in_pCookie, out uint out_bankID) {
		in_pCookie = new AkCallbackManager.BankCallbackPackage(in_pfnBankCallback, in_pCookie);
    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_LoadBank__SWIG_6(in_pInMemoryBankPtr, in_uInMemoryBankSize, global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero, out out_bankID); }
  }

  public static AKRESULT LoadBank(global::System.IntPtr in_pInMemoryBankPtr, uint in_uInMemoryBankSize, AkCallbackManager.BankCallback in_pfnBankCallback, object in_pCookie, int in_uPoolForBankMedia, out uint out_bankID) {
		in_pCookie = new AkCallbackManager.BankCallbackPackage(in_pfnBankCallback, in_pCookie);
    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_LoadBank__SWIG_7(in_pInMemoryBankPtr, in_uInMemoryBankSize, global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero, in_uPoolForBankMedia, out out_bankID); }
  }

  public static AKRESULT UnloadBank(string in_pszString, global::System.IntPtr in_pInMemoryBankPtr, out int out_pMemPoolId) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_UnloadBank__SWIG_0(in_pszString, in_pInMemoryBankPtr, out out_pMemPoolId); }

  public static AKRESULT UnloadBank(string in_pszString, global::System.IntPtr in_pInMemoryBankPtr) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_UnloadBank__SWIG_1(in_pszString, in_pInMemoryBankPtr); }

  public static AKRESULT UnloadBank(uint in_bankID, global::System.IntPtr in_pInMemoryBankPtr, out int out_pMemPoolId) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_UnloadBank__SWIG_4(in_bankID, in_pInMemoryBankPtr, out out_pMemPoolId); }

  public static AKRESULT UnloadBank(uint in_bankID, global::System.IntPtr in_pInMemoryBankPtr) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_UnloadBank__SWIG_5(in_bankID, in_pInMemoryBankPtr); }

  public static AKRESULT UnloadBank(string in_pszString, global::System.IntPtr in_pInMemoryBankPtr, AkCallbackManager.BankCallback in_pfnBankCallback, object in_pCookie) {
		in_pCookie = new AkCallbackManager.BankCallbackPackage(in_pfnBankCallback, in_pCookie);
    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_UnloadBank__SWIG_6(in_pszString, in_pInMemoryBankPtr, global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero); }
  }

  public static AKRESULT UnloadBank(uint in_bankID, global::System.IntPtr in_pInMemoryBankPtr, AkCallbackManager.BankCallback in_pfnBankCallback, object in_pCookie) {
		in_pCookie = new AkCallbackManager.BankCallbackPackage(in_pfnBankCallback, in_pCookie);
    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_UnloadBank__SWIG_8(in_bankID, in_pInMemoryBankPtr, global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero); }
  }

  public static void CancelBankCallbackCookie(object in_pCookie) {
		AkCallbackManager.RemoveBankCallback(in_pCookie);
	}

  public static AKRESULT PrepareBank(AkPreparationType in_PreparationType, string in_pszString, AkBankContent in_uFlags) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PrepareBank__SWIG_0((int)in_PreparationType, in_pszString, (int)in_uFlags); }

  public static AKRESULT PrepareBank(AkPreparationType in_PreparationType, string in_pszString) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PrepareBank__SWIG_1((int)in_PreparationType, in_pszString); }

  public static AKRESULT PrepareBank(AkPreparationType in_PreparationType, uint in_bankID, AkBankContent in_uFlags) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PrepareBank__SWIG_4((int)in_PreparationType, in_bankID, (int)in_uFlags); }

  public static AKRESULT PrepareBank(AkPreparationType in_PreparationType, uint in_bankID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PrepareBank__SWIG_5((int)in_PreparationType, in_bankID); }

  public static AKRESULT PrepareBank(AkPreparationType in_PreparationType, string in_pszString, AkCallbackManager.BankCallback in_pfnBankCallback, object in_pCookie, AkBankContent in_uFlags) {
		in_pCookie = new AkCallbackManager.BankCallbackPackage(in_pfnBankCallback, in_pCookie);
    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PrepareBank__SWIG_6((int)in_PreparationType, in_pszString, global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero, (int)in_uFlags); }
  }

  public static AKRESULT PrepareBank(AkPreparationType in_PreparationType, string in_pszString, AkCallbackManager.BankCallback in_pfnBankCallback, object in_pCookie) {
		in_pCookie = new AkCallbackManager.BankCallbackPackage(in_pfnBankCallback, in_pCookie);
    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PrepareBank__SWIG_7((int)in_PreparationType, in_pszString, global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero); }
  }

  public static AKRESULT PrepareBank(AkPreparationType in_PreparationType, uint in_bankID, AkCallbackManager.BankCallback in_pfnBankCallback, object in_pCookie, AkBankContent in_uFlags) {
		in_pCookie = new AkCallbackManager.BankCallbackPackage(in_pfnBankCallback, in_pCookie);
    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PrepareBank__SWIG_10((int)in_PreparationType, in_bankID, global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero, (int)in_uFlags); }
  }

  public static AKRESULT PrepareBank(AkPreparationType in_PreparationType, uint in_bankID, AkCallbackManager.BankCallback in_pfnBankCallback, object in_pCookie) {
		in_pCookie = new AkCallbackManager.BankCallbackPackage(in_pfnBankCallback, in_pCookie);
    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PrepareBank__SWIG_11((int)in_PreparationType, in_bankID, global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero); }
  }

  public static AKRESULT ClearPreparedEvents() { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ClearPreparedEvents(); }

  public static AKRESULT PrepareEvent(AkPreparationType in_PreparationType, string [] in_ppszString, uint in_uNumEvent) {
			
		//Find the required size
		int size = 0;
		foreach(string s in in_ppszString)
			size += s.Length + 1;
				
		int sizeofChar = 2;	//Unicode
		global::System.IntPtr pMem = global::System.Runtime.InteropServices.Marshal.AllocHGlobal(size * sizeofChar + 2);
		
		//Write the length of array
		global::System.Runtime.InteropServices.Marshal.WriteInt16(pMem, (short)in_ppszString.Length);
		global::System.IntPtr pCurrent = (global::System.IntPtr)(pMem.ToInt64() + sizeofChar);
		
		//Copy the strings one after the other.
		foreach(string s in in_ppszString)
		{
			global::System.Runtime.InteropServices.Marshal.Copy(s.ToCharArray(), 0, pCurrent, s.Length);
			pCurrent = (global::System.IntPtr)(pCurrent.ToInt64() + sizeofChar * s.Length);
			global::System.Runtime.InteropServices.Marshal.WriteInt16(pCurrent, 0);	//Null-terminated string
			pCurrent = (global::System.IntPtr)(pCurrent.ToInt64() + sizeofChar);
		}		
		
    try { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PrepareEvent__SWIG_0((int)in_PreparationType, pMem, in_uNumEvent); } finally {
	global::System.Runtime.InteropServices.Marshal.FreeHGlobal(pMem);
    }
  }

  public static AKRESULT PrepareEvent(AkPreparationType in_PreparationType, uint[] in_pEventID, uint in_uNumEvent) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PrepareEvent__SWIG_1((int)in_PreparationType, in_pEventID, in_uNumEvent); }

  public static AKRESULT PrepareEvent(AkPreparationType in_PreparationType, string [] in_ppszString, uint in_uNumEvent, AkCallbackManager.BankCallback in_pfnBankCallback, object in_pCookie) {
			
		//Find the required size
		int size = 0;
		foreach(string s in in_ppszString)
			size += s.Length + 1;
				
		int sizeofChar = 2;	//Unicode
		global::System.IntPtr pMem = global::System.Runtime.InteropServices.Marshal.AllocHGlobal(size * sizeofChar + 2);
		
		//Write the length of array
		global::System.Runtime.InteropServices.Marshal.WriteInt16(pMem, (short)in_ppszString.Length);
		global::System.IntPtr pCurrent = (global::System.IntPtr)(pMem.ToInt64() + sizeofChar);
		
		//Copy the strings one after the other.
		foreach(string s in in_ppszString)
		{
			global::System.Runtime.InteropServices.Marshal.Copy(s.ToCharArray(), 0, pCurrent, s.Length);
			pCurrent = (global::System.IntPtr)(pCurrent.ToInt64() + sizeofChar * s.Length);
			global::System.Runtime.InteropServices.Marshal.WriteInt16(pCurrent, 0);	//Null-terminated string
			pCurrent = (global::System.IntPtr)(pCurrent.ToInt64() + sizeofChar);
		}		
		
		in_pCookie = new AkCallbackManager.BankCallbackPackage(in_pfnBankCallback, in_pCookie);
    try { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PrepareEvent__SWIG_2((int)in_PreparationType, pMem, in_uNumEvent, global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero); } finally {
	global::System.Runtime.InteropServices.Marshal.FreeHGlobal(pMem);
    }
  }

  public static AKRESULT PrepareEvent(AkPreparationType in_PreparationType, uint[] in_pEventID, uint in_uNumEvent, AkCallbackManager.BankCallback in_pfnBankCallback, object in_pCookie) {
		in_pCookie = new AkCallbackManager.BankCallbackPackage(in_pfnBankCallback, in_pCookie);
    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PrepareEvent__SWIG_3((int)in_PreparationType, in_pEventID, in_uNumEvent, global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero); }
  }

  public static AKRESULT SetMedia(AkSourceSettingsArray in_pSourceSettings, uint in_uNumSourceSettings) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetMedia(in_pSourceSettings.GetBuffer(), in_uNumSourceSettings); }

  public static AKRESULT UnsetMedia(AkSourceSettingsArray in_pSourceSettings, uint in_uNumSourceSettings) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_UnsetMedia(in_pSourceSettings.GetBuffer(), in_uNumSourceSettings); }

  public static AKRESULT PrepareGameSyncs(AkPreparationType in_PreparationType, AkGroupType in_eGameSyncType, string in_pszGroupName, string [] in_ppszGameSyncName, uint in_uNumGameSyncs) {
			
		//Find the required size
		int size = 0;
		foreach(string s in in_ppszGameSyncName)
			size += s.Length + 1;
				
		int sizeofChar = 2;	//Unicode
		global::System.IntPtr pMem = global::System.Runtime.InteropServices.Marshal.AllocHGlobal(size * sizeofChar + 2);
		
		//Write the length of array
		global::System.Runtime.InteropServices.Marshal.WriteInt16(pMem, (short)in_ppszGameSyncName.Length);
		global::System.IntPtr pCurrent = (global::System.IntPtr)(pMem.ToInt64() + sizeofChar);
		
		//Copy the strings one after the other.
		foreach(string s in in_ppszGameSyncName)
		{
			global::System.Runtime.InteropServices.Marshal.Copy(s.ToCharArray(), 0, pCurrent, s.Length);
			pCurrent = (global::System.IntPtr)(pCurrent.ToInt64() + sizeofChar * s.Length);
			global::System.Runtime.InteropServices.Marshal.WriteInt16(pCurrent, 0);	//Null-terminated string
			pCurrent = (global::System.IntPtr)(pCurrent.ToInt64() + sizeofChar);
		}		
		
    try { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PrepareGameSyncs__SWIG_0((int)in_PreparationType, (int)in_eGameSyncType, in_pszGroupName, pMem, in_uNumGameSyncs); } finally {
	global::System.Runtime.InteropServices.Marshal.FreeHGlobal(pMem);
    }
  }

  public static AKRESULT PrepareGameSyncs(AkPreparationType in_PreparationType, AkGroupType in_eGameSyncType, uint in_GroupID, uint[] in_paGameSyncID, uint in_uNumGameSyncs) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PrepareGameSyncs__SWIG_1((int)in_PreparationType, (int)in_eGameSyncType, in_GroupID, in_paGameSyncID, in_uNumGameSyncs); }

  public static AKRESULT PrepareGameSyncs(AkPreparationType in_PreparationType, AkGroupType in_eGameSyncType, string in_pszGroupName, string [] in_ppszGameSyncName, uint in_uNumGameSyncs, AkCallbackManager.BankCallback in_pfnBankCallback, object in_pCookie) {
			
		//Find the required size
		int size = 0;
		foreach(string s in in_ppszGameSyncName)
			size += s.Length + 1;
				
		int sizeofChar = 2;	//Unicode
		global::System.IntPtr pMem = global::System.Runtime.InteropServices.Marshal.AllocHGlobal(size * sizeofChar + 2);
		
		//Write the length of array
		global::System.Runtime.InteropServices.Marshal.WriteInt16(pMem, (short)in_ppszGameSyncName.Length);
		global::System.IntPtr pCurrent = (global::System.IntPtr)(pMem.ToInt64() + sizeofChar);
		
		//Copy the strings one after the other.
		foreach(string s in in_ppszGameSyncName)
		{
			global::System.Runtime.InteropServices.Marshal.Copy(s.ToCharArray(), 0, pCurrent, s.Length);
			pCurrent = (global::System.IntPtr)(pCurrent.ToInt64() + sizeofChar * s.Length);
			global::System.Runtime.InteropServices.Marshal.WriteInt16(pCurrent, 0);	//Null-terminated string
			pCurrent = (global::System.IntPtr)(pCurrent.ToInt64() + sizeofChar);
		}		
		
		in_pCookie = new AkCallbackManager.BankCallbackPackage(in_pfnBankCallback, in_pCookie);
    try { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PrepareGameSyncs__SWIG_2((int)in_PreparationType, (int)in_eGameSyncType, in_pszGroupName, pMem, in_uNumGameSyncs, global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero); } finally {
	global::System.Runtime.InteropServices.Marshal.FreeHGlobal(pMem);
    }
  }

  public static AKRESULT PrepareGameSyncs(AkPreparationType in_PreparationType, AkGroupType in_eGameSyncType, uint in_GroupID, uint[] in_paGameSyncID, uint in_uNumGameSyncs, AkCallbackManager.BankCallback in_pfnBankCallback, object in_pCookie) {
		in_pCookie = new AkCallbackManager.BankCallbackPackage(in_pfnBankCallback, in_pCookie);
    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PrepareGameSyncs__SWIG_3((int)in_PreparationType, (int)in_eGameSyncType, in_GroupID, in_paGameSyncID, in_uNumGameSyncs, global::System.IntPtr.Zero, in_pCookie != null ? (global::System.IntPtr)in_pCookie.GetHashCode() : global::System.IntPtr.Zero); }
  }

  public static AKRESULT AddListener(UnityEngine.GameObject in_emitterGameObj, UnityEngine.GameObject in_listenerGameObj) {

	var in_emitterGameObj_id = AkSoundEngine.GetAkGameObjectID(in_emitterGameObj);
	AkSoundEngine.PreGameObjectAPICall(in_emitterGameObj, in_emitterGameObj_id);


	var in_listenerGameObj_id = AkSoundEngine.GetAkGameObjectID(in_listenerGameObj);
	AkSoundEngine.PreGameObjectAPICall(in_listenerGameObj, in_listenerGameObj_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_AddListener(in_emitterGameObj_id, in_listenerGameObj_id); }
  }

  public static AKRESULT RemoveListener(UnityEngine.GameObject in_emitterGameObj, UnityEngine.GameObject in_listenerGameObj) {

	var in_emitterGameObj_id = AkSoundEngine.GetAkGameObjectID(in_emitterGameObj);
	AkSoundEngine.PreGameObjectAPICall(in_emitterGameObj, in_emitterGameObj_id);


	var in_listenerGameObj_id = AkSoundEngine.GetAkGameObjectID(in_listenerGameObj);
	AkSoundEngine.PreGameObjectAPICall(in_listenerGameObj, in_listenerGameObj_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_RemoveListener(in_emitterGameObj_id, in_listenerGameObj_id); }
  }

  public static AKRESULT AddDefaultListener(UnityEngine.GameObject in_listenerGameObj) {

	var in_listenerGameObj_id = AkSoundEngine.GetAkGameObjectID(in_listenerGameObj);
	AkSoundEngine.PreGameObjectAPICall(in_listenerGameObj, in_listenerGameObj_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_AddDefaultListener(in_listenerGameObj_id); }
  }

  public static AKRESULT RemoveDefaultListener(UnityEngine.GameObject in_listenerGameObj) {

	var in_listenerGameObj_id = AkSoundEngine.GetAkGameObjectID(in_listenerGameObj);
	AkSoundEngine.PreGameObjectAPICall(in_listenerGameObj, in_listenerGameObj_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_RemoveDefaultListener(in_listenerGameObj_id); }
  }

  public static AKRESULT ResetListenersToDefault(UnityEngine.GameObject in_emitterGameObj) {

	var in_emitterGameObj_id = AkSoundEngine.GetAkGameObjectID(in_emitterGameObj);
	AkSoundEngine.PreGameObjectAPICall(in_emitterGameObj, in_emitterGameObj_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ResetListenersToDefault(in_emitterGameObj_id); }
  }

  public static AKRESULT SetListenerSpatialization(UnityEngine.GameObject in_uListenerID, bool in_bSpatialized, AkChannelConfig in_channelConfig, float[] in_pVolumeOffsets) {

	var in_uListenerID_id = AkSoundEngine.GetAkGameObjectID(in_uListenerID);
	AkSoundEngine.PreGameObjectAPICall(in_uListenerID, in_uListenerID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetListenerSpatialization__SWIG_0(in_uListenerID_id, in_bSpatialized, AkChannelConfig.getCPtr(in_channelConfig), in_pVolumeOffsets); }
  }

  public static AKRESULT SetListenerSpatialization(UnityEngine.GameObject in_uListenerID, bool in_bSpatialized, AkChannelConfig in_channelConfig) {

	var in_uListenerID_id = AkSoundEngine.GetAkGameObjectID(in_uListenerID);
	AkSoundEngine.PreGameObjectAPICall(in_uListenerID, in_uListenerID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetListenerSpatialization__SWIG_1(in_uListenerID_id, in_bSpatialized, AkChannelConfig.getCPtr(in_channelConfig)); }
  }

  public static AKRESULT SetRTPCValue(uint in_rtpcID, float in_value, UnityEngine.GameObject in_gameObjectID, int in_uValueChangeDuration, AkCurveInterpolation in_eFadeCurve, bool in_bBypassInternalValueInterpolation) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetRTPCValue__SWIG_0(in_rtpcID, in_value, in_gameObjectID_id, in_uValueChangeDuration, (int)in_eFadeCurve, in_bBypassInternalValueInterpolation); }
  }

  public static AKRESULT SetRTPCValue(uint in_rtpcID, float in_value, UnityEngine.GameObject in_gameObjectID, int in_uValueChangeDuration, AkCurveInterpolation in_eFadeCurve) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetRTPCValue__SWIG_1(in_rtpcID, in_value, in_gameObjectID_id, in_uValueChangeDuration, (int)in_eFadeCurve); }
  }

  public static AKRESULT SetRTPCValue(uint in_rtpcID, float in_value, UnityEngine.GameObject in_gameObjectID, int in_uValueChangeDuration) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetRTPCValue__SWIG_2(in_rtpcID, in_value, in_gameObjectID_id, in_uValueChangeDuration); }
  }

  public static AKRESULT SetRTPCValue(uint in_rtpcID, float in_value, UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetRTPCValue__SWIG_3(in_rtpcID, in_value, in_gameObjectID_id); }
  }

  public static AKRESULT SetRTPCValue(uint in_rtpcID, float in_value) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetRTPCValue__SWIG_4(in_rtpcID, in_value); }

  public static AKRESULT SetRTPCValue(string in_pszRtpcName, float in_value, UnityEngine.GameObject in_gameObjectID, int in_uValueChangeDuration, AkCurveInterpolation in_eFadeCurve, bool in_bBypassInternalValueInterpolation) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetRTPCValue__SWIG_5(in_pszRtpcName, in_value, in_gameObjectID_id, in_uValueChangeDuration, (int)in_eFadeCurve, in_bBypassInternalValueInterpolation); }
  }

  public static AKRESULT SetRTPCValue(string in_pszRtpcName, float in_value, UnityEngine.GameObject in_gameObjectID, int in_uValueChangeDuration, AkCurveInterpolation in_eFadeCurve) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetRTPCValue__SWIG_6(in_pszRtpcName, in_value, in_gameObjectID_id, in_uValueChangeDuration, (int)in_eFadeCurve); }
  }

  public static AKRESULT SetRTPCValue(string in_pszRtpcName, float in_value, UnityEngine.GameObject in_gameObjectID, int in_uValueChangeDuration) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetRTPCValue__SWIG_7(in_pszRtpcName, in_value, in_gameObjectID_id, in_uValueChangeDuration); }
  }

  public static AKRESULT SetRTPCValue(string in_pszRtpcName, float in_value, UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetRTPCValue__SWIG_8(in_pszRtpcName, in_value, in_gameObjectID_id); }
  }

  public static AKRESULT SetRTPCValue(string in_pszRtpcName, float in_value) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetRTPCValue__SWIG_9(in_pszRtpcName, in_value); }

  public static AKRESULT SetRTPCValueByPlayingID(uint in_rtpcID, float in_value, uint in_playingID, int in_uValueChangeDuration, AkCurveInterpolation in_eFadeCurve, bool in_bBypassInternalValueInterpolation) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetRTPCValueByPlayingID__SWIG_0(in_rtpcID, in_value, in_playingID, in_uValueChangeDuration, (int)in_eFadeCurve, in_bBypassInternalValueInterpolation); }

  public static AKRESULT SetRTPCValueByPlayingID(uint in_rtpcID, float in_value, uint in_playingID, int in_uValueChangeDuration, AkCurveInterpolation in_eFadeCurve) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetRTPCValueByPlayingID__SWIG_1(in_rtpcID, in_value, in_playingID, in_uValueChangeDuration, (int)in_eFadeCurve); }

  public static AKRESULT SetRTPCValueByPlayingID(uint in_rtpcID, float in_value, uint in_playingID, int in_uValueChangeDuration) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetRTPCValueByPlayingID__SWIG_2(in_rtpcID, in_value, in_playingID, in_uValueChangeDuration); }

  public static AKRESULT SetRTPCValueByPlayingID(uint in_rtpcID, float in_value, uint in_playingID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetRTPCValueByPlayingID__SWIG_3(in_rtpcID, in_value, in_playingID); }

  public static AKRESULT SetRTPCValueByPlayingID(string in_pszRtpcName, float in_value, uint in_playingID, int in_uValueChangeDuration, AkCurveInterpolation in_eFadeCurve, bool in_bBypassInternalValueInterpolation) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetRTPCValueByPlayingID__SWIG_4(in_pszRtpcName, in_value, in_playingID, in_uValueChangeDuration, (int)in_eFadeCurve, in_bBypassInternalValueInterpolation); }

  public static AKRESULT SetRTPCValueByPlayingID(string in_pszRtpcName, float in_value, uint in_playingID, int in_uValueChangeDuration, AkCurveInterpolation in_eFadeCurve) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetRTPCValueByPlayingID__SWIG_5(in_pszRtpcName, in_value, in_playingID, in_uValueChangeDuration, (int)in_eFadeCurve); }

  public static AKRESULT SetRTPCValueByPlayingID(string in_pszRtpcName, float in_value, uint in_playingID, int in_uValueChangeDuration) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetRTPCValueByPlayingID__SWIG_6(in_pszRtpcName, in_value, in_playingID, in_uValueChangeDuration); }

  public static AKRESULT SetRTPCValueByPlayingID(string in_pszRtpcName, float in_value, uint in_playingID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetRTPCValueByPlayingID__SWIG_7(in_pszRtpcName, in_value, in_playingID); }

  public static AKRESULT ResetRTPCValue(uint in_rtpcID, UnityEngine.GameObject in_gameObjectID, int in_uValueChangeDuration, AkCurveInterpolation in_eFadeCurve, bool in_bBypassInternalValueInterpolation) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ResetRTPCValue__SWIG_0(in_rtpcID, in_gameObjectID_id, in_uValueChangeDuration, (int)in_eFadeCurve, in_bBypassInternalValueInterpolation); }
  }

  public static AKRESULT ResetRTPCValue(uint in_rtpcID, UnityEngine.GameObject in_gameObjectID, int in_uValueChangeDuration, AkCurveInterpolation in_eFadeCurve) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ResetRTPCValue__SWIG_1(in_rtpcID, in_gameObjectID_id, in_uValueChangeDuration, (int)in_eFadeCurve); }
  }

  public static AKRESULT ResetRTPCValue(uint in_rtpcID, UnityEngine.GameObject in_gameObjectID, int in_uValueChangeDuration) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ResetRTPCValue__SWIG_2(in_rtpcID, in_gameObjectID_id, in_uValueChangeDuration); }
  }

  public static AKRESULT ResetRTPCValue(uint in_rtpcID, UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ResetRTPCValue__SWIG_3(in_rtpcID, in_gameObjectID_id); }
  }

  public static AKRESULT ResetRTPCValue(uint in_rtpcID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ResetRTPCValue__SWIG_4(in_rtpcID); }

  public static AKRESULT ResetRTPCValue(string in_pszRtpcName, UnityEngine.GameObject in_gameObjectID, int in_uValueChangeDuration, AkCurveInterpolation in_eFadeCurve, bool in_bBypassInternalValueInterpolation) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ResetRTPCValue__SWIG_5(in_pszRtpcName, in_gameObjectID_id, in_uValueChangeDuration, (int)in_eFadeCurve, in_bBypassInternalValueInterpolation); }
  }

  public static AKRESULT ResetRTPCValue(string in_pszRtpcName, UnityEngine.GameObject in_gameObjectID, int in_uValueChangeDuration, AkCurveInterpolation in_eFadeCurve) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ResetRTPCValue__SWIG_6(in_pszRtpcName, in_gameObjectID_id, in_uValueChangeDuration, (int)in_eFadeCurve); }
  }

  public static AKRESULT ResetRTPCValue(string in_pszRtpcName, UnityEngine.GameObject in_gameObjectID, int in_uValueChangeDuration) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ResetRTPCValue__SWIG_7(in_pszRtpcName, in_gameObjectID_id, in_uValueChangeDuration); }
  }

  public static AKRESULT ResetRTPCValue(string in_pszRtpcName, UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ResetRTPCValue__SWIG_8(in_pszRtpcName, in_gameObjectID_id); }
  }

  public static AKRESULT ResetRTPCValue(string in_pszRtpcName) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ResetRTPCValue__SWIG_9(in_pszRtpcName); }

  public static AKRESULT SetSwitch(uint in_switchGroup, uint in_switchState, UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetSwitch__SWIG_0(in_switchGroup, in_switchState, in_gameObjectID_id); }
  }

  public static AKRESULT SetSwitch(string in_pszSwitchGroup, string in_pszSwitchState, UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetSwitch__SWIG_1(in_pszSwitchGroup, in_pszSwitchState, in_gameObjectID_id); }
  }

  public static AKRESULT PostTrigger(uint in_triggerID, UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PostTrigger__SWIG_0(in_triggerID, in_gameObjectID_id); }
  }

  public static AKRESULT PostTrigger(string in_pszTrigger, UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PostTrigger__SWIG_1(in_pszTrigger, in_gameObjectID_id); }
  }

  public static AKRESULT SetState(uint in_stateGroup, uint in_state) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetState__SWIG_0(in_stateGroup, in_state); }

  public static AKRESULT SetState(string in_pszStateGroup, string in_pszState) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetState__SWIG_1(in_pszStateGroup, in_pszState); }

  public static AKRESULT SetGameObjectAuxSendValues(UnityEngine.GameObject in_gameObjectID, AkAuxSendArray in_aAuxSendValues, uint in_uNumSendValues) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetGameObjectAuxSendValues(in_gameObjectID_id, in_aAuxSendValues.GetBuffer(), in_uNumSendValues); }
  }

  public static AKRESULT SetGameObjectOutputBusVolume(UnityEngine.GameObject in_emitterObjID, UnityEngine.GameObject in_listenerObjID, float in_fControlValue) {

	var in_emitterObjID_id = AkSoundEngine.GetAkGameObjectID(in_emitterObjID);
	AkSoundEngine.PreGameObjectAPICall(in_emitterObjID, in_emitterObjID_id);


	var in_listenerObjID_id = AkSoundEngine.GetAkGameObjectID(in_listenerObjID);
	AkSoundEngine.PreGameObjectAPICall(in_listenerObjID, in_listenerObjID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetGameObjectOutputBusVolume(in_emitterObjID_id, in_listenerObjID_id, in_fControlValue); }
  }

  public static AKRESULT SetActorMixerEffect(uint in_audioNodeID, uint in_uFXIndex, uint in_shareSetID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetActorMixerEffect(in_audioNodeID, in_uFXIndex, in_shareSetID); }

  public static AKRESULT SetBusEffect(uint in_audioNodeID, uint in_uFXIndex, uint in_shareSetID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetBusEffect__SWIG_0(in_audioNodeID, in_uFXIndex, in_shareSetID); }

  public static AKRESULT SetBusEffect(string in_pszBusName, uint in_uFXIndex, uint in_shareSetID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetBusEffect__SWIG_1(in_pszBusName, in_uFXIndex, in_shareSetID); }

  public static AKRESULT SetMixer(uint in_audioNodeID, uint in_shareSetID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetMixer__SWIG_0(in_audioNodeID, in_shareSetID); }

  public static AKRESULT SetMixer(string in_pszBusName, uint in_shareSetID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetMixer__SWIG_1(in_pszBusName, in_shareSetID); }

  public static AKRESULT SetBusConfig(uint in_audioNodeID, AkChannelConfig in_channelConfig) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetBusConfig__SWIG_0(in_audioNodeID, AkChannelConfig.getCPtr(in_channelConfig)); }

  public static AKRESULT SetBusConfig(string in_pszBusName, AkChannelConfig in_channelConfig) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetBusConfig__SWIG_1(in_pszBusName, AkChannelConfig.getCPtr(in_channelConfig)); }

  public static AKRESULT SetObjectObstructionAndOcclusion(UnityEngine.GameObject in_EmitterID, UnityEngine.GameObject in_ListenerID, float in_fObstructionLevel, float in_fOcclusionLevel) {

	var in_EmitterID_id = AkSoundEngine.GetAkGameObjectID(in_EmitterID);
	AkSoundEngine.PreGameObjectAPICall(in_EmitterID, in_EmitterID_id);


	var in_ListenerID_id = AkSoundEngine.GetAkGameObjectID(in_ListenerID);
	AkSoundEngine.PreGameObjectAPICall(in_ListenerID, in_ListenerID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetObjectObstructionAndOcclusion(in_EmitterID_id, in_ListenerID_id, in_fObstructionLevel, in_fOcclusionLevel); }
  }

  public static AKRESULT SetMultipleObstructionAndOcclusion(UnityEngine.GameObject in_EmitterID, UnityEngine.GameObject in_uListenerID, AkObstructionOcclusionValuesArray in_fObstructionOcclusionValues, uint in_uNumOcclusionObstruction) {

	var in_EmitterID_id = AkSoundEngine.GetAkGameObjectID(in_EmitterID);
	AkSoundEngine.PreGameObjectAPICall(in_EmitterID, in_EmitterID_id);


	var in_uListenerID_id = AkSoundEngine.GetAkGameObjectID(in_uListenerID);
	AkSoundEngine.PreGameObjectAPICall(in_uListenerID, in_uListenerID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetMultipleObstructionAndOcclusion(in_EmitterID_id, in_uListenerID_id, in_fObstructionOcclusionValues.GetBuffer(), in_uNumOcclusionObstruction); }
  }

  public static AKRESULT StartOutputCapture(string in_CaptureFileName) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_StartOutputCapture(in_CaptureFileName); }

  public static AKRESULT StopOutputCapture() { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_StopOutputCapture(); }

  public static AKRESULT AddOutputCaptureMarker(string in_MarkerText) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_AddOutputCaptureMarker(in_MarkerText); }

  public static AKRESULT StartProfilerCapture(string in_CaptureFileName) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_StartProfilerCapture(in_CaptureFileName); }

  public static AKRESULT StopProfilerCapture() { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_StopProfilerCapture(); }

  public static AKRESULT RemoveOutput(ulong in_idOutput) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_RemoveOutput(in_idOutput); }

  public static AKRESULT ReplaceOutput(AkOutputSettings in_Settings, ulong in_outputDeviceId, out ulong out_pOutputDeviceId) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ReplaceOutput__SWIG_0(AkOutputSettings.getCPtr(in_Settings), in_outputDeviceId, out out_pOutputDeviceId); }

  public static AKRESULT ReplaceOutput(AkOutputSettings in_Settings, ulong in_outputDeviceId) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_ReplaceOutput__SWIG_1(AkOutputSettings.getCPtr(in_Settings), in_outputDeviceId); }

  public static ulong GetOutputID(uint in_idShareset, uint in_idDevice) { return AkSoundEnginePINVOKE.CSharp_GetOutputID__SWIG_0(in_idShareset, in_idDevice); }

  public static ulong GetOutputID(string in_szShareSet, uint in_idDevice) { return AkSoundEnginePINVOKE.CSharp_GetOutputID__SWIG_1(in_szShareSet, in_idDevice); }

  public static AKRESULT SetBusDevice(uint in_idBus, uint in_idNewDevice) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetBusDevice__SWIG_0(in_idBus, in_idNewDevice); }

  public static AKRESULT SetBusDevice(string in_BusName, string in_DeviceName) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetBusDevice__SWIG_1(in_BusName, in_DeviceName); }

  public static AKRESULT SetOutputVolume(ulong in_idOutput, float in_fVolume) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetOutputVolume(in_idOutput, in_fVolume); }

  public static AKRESULT GetDeviceSpatialAudioSupport(uint in_idDevice) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetDeviceSpatialAudioSupport(in_idDevice); }

  public static AKRESULT Suspend(bool in_bRenderAnyway) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_Suspend__SWIG_0(in_bRenderAnyway); }

  public static AKRESULT Suspend() { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_Suspend__SWIG_1(); }

  public static AKRESULT WakeupFromSuspend() { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_WakeupFromSuspend(); }

  public static uint GetBufferTick() { return AkSoundEnginePINVOKE.CSharp_GetBufferTick(); }

  public static byte AK_INVALID_MIDI_CHANNEL { get { return AkSoundEnginePINVOKE.CSharp_AK_INVALID_MIDI_CHANNEL_get(); } 
  }

  public static byte AK_INVALID_MIDI_NOTE { get { return AkSoundEnginePINVOKE.CSharp_AK_INVALID_MIDI_NOTE_get(); } 
  }

  public static AKRESULT GetPlayingSegmentInfo(uint in_PlayingID, AkSegmentInfo out_segmentInfo, bool in_bExtrapolate) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetPlayingSegmentInfo__SWIG_0(in_PlayingID, AkSegmentInfo.getCPtr(out_segmentInfo), in_bExtrapolate); }

  public static AKRESULT GetPlayingSegmentInfo(uint in_PlayingID, AkSegmentInfo out_segmentInfo) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetPlayingSegmentInfo__SWIG_1(in_PlayingID, AkSegmentInfo.getCPtr(out_segmentInfo)); }

  public static AKRESULT PostCode(AkMonitorErrorCode in_eError, AkMonitorErrorLevel in_eErrorLevel, uint in_playingID, UnityEngine.GameObject in_gameObjID, uint in_audioNodeID, bool in_bIsBus) {

	var in_gameObjID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjID, in_gameObjID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PostCode__SWIG_0((int)in_eError, (int)in_eErrorLevel, in_playingID, in_gameObjID_id, in_audioNodeID, in_bIsBus); }
  }

  public static AKRESULT PostCode(AkMonitorErrorCode in_eError, AkMonitorErrorLevel in_eErrorLevel, uint in_playingID, UnityEngine.GameObject in_gameObjID, uint in_audioNodeID) {

	var in_gameObjID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjID, in_gameObjID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PostCode__SWIG_1((int)in_eError, (int)in_eErrorLevel, in_playingID, in_gameObjID_id, in_audioNodeID); }
  }

  public static AKRESULT PostCode(AkMonitorErrorCode in_eError, AkMonitorErrorLevel in_eErrorLevel, uint in_playingID, UnityEngine.GameObject in_gameObjID) {

	var in_gameObjID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjID, in_gameObjID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PostCode__SWIG_2((int)in_eError, (int)in_eErrorLevel, in_playingID, in_gameObjID_id); }
  }

  public static AKRESULT PostCode(AkMonitorErrorCode in_eError, AkMonitorErrorLevel in_eErrorLevel, uint in_playingID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PostCode__SWIG_3((int)in_eError, (int)in_eErrorLevel, in_playingID); }

  public static AKRESULT PostCode(AkMonitorErrorCode in_eError, AkMonitorErrorLevel in_eErrorLevel) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PostCode__SWIG_4((int)in_eError, (int)in_eErrorLevel); }

  public static AKRESULT PostString(string in_pszError, AkMonitorErrorLevel in_eErrorLevel, uint in_playingID, UnityEngine.GameObject in_gameObjID, uint in_audioNodeID, bool in_bIsBus) {

	var in_gameObjID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjID, in_gameObjID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PostString__SWIG_0(in_pszError, (int)in_eErrorLevel, in_playingID, in_gameObjID_id, in_audioNodeID, in_bIsBus); }
  }

  public static AKRESULT PostString(string in_pszError, AkMonitorErrorLevel in_eErrorLevel, uint in_playingID, UnityEngine.GameObject in_gameObjID, uint in_audioNodeID) {

	var in_gameObjID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjID, in_gameObjID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PostString__SWIG_1(in_pszError, (int)in_eErrorLevel, in_playingID, in_gameObjID_id, in_audioNodeID); }
  }

  public static AKRESULT PostString(string in_pszError, AkMonitorErrorLevel in_eErrorLevel, uint in_playingID, UnityEngine.GameObject in_gameObjID) {

	var in_gameObjID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjID, in_gameObjID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PostString__SWIG_2(in_pszError, (int)in_eErrorLevel, in_playingID, in_gameObjID_id); }
  }

  public static AKRESULT PostString(string in_pszError, AkMonitorErrorLevel in_eErrorLevel, uint in_playingID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PostString__SWIG_3(in_pszError, (int)in_eErrorLevel, in_playingID); }

  public static AKRESULT PostString(string in_pszError, AkMonitorErrorLevel in_eErrorLevel) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_PostString__SWIG_4(in_pszError, (int)in_eErrorLevel); }

  public static int GetTimeStamp() { return AkSoundEnginePINVOKE.CSharp_GetTimeStamp(); }

  public static uint GetNumNonZeroBits(uint in_uWord) { return AkSoundEnginePINVOKE.CSharp_GetNumNonZeroBits(in_uWord); }

  public static void AkGetDefaultHighPriorityThreadProperties(AkThreadProperties out_threadProperties) { AkSoundEnginePINVOKE.CSharp_AkGetDefaultHighPriorityThreadProperties(AkThreadProperties.getCPtr(out_threadProperties)); }

  public static uint ResolveDialogueEvent(uint in_eventID, uint[] in_aArgumentValues, uint in_uNumArguments, uint in_idSequence) { return AkSoundEnginePINVOKE.CSharp_ResolveDialogueEvent__SWIG_0(in_eventID, in_aArgumentValues, in_uNumArguments, in_idSequence); }

  public static uint ResolveDialogueEvent(uint in_eventID, uint[] in_aArgumentValues, uint in_uNumArguments) { return AkSoundEnginePINVOKE.CSharp_ResolveDialogueEvent__SWIG_1(in_eventID, in_aArgumentValues, in_uNumArguments); }

  public static AKRESULT GetDialogueEventCustomPropertyValue(uint in_eventID, uint in_uPropID, out int out_iValue) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetDialogueEventCustomPropertyValue__SWIG_0(in_eventID, in_uPropID, out out_iValue); }

  public static AKRESULT GetDialogueEventCustomPropertyValue(uint in_eventID, uint in_uPropID, out float out_fValue) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetDialogueEventCustomPropertyValue__SWIG_1(in_eventID, in_uPropID, out out_fValue); }

  public static AKRESULT GetPosition(UnityEngine.GameObject in_GameObjectID, AkTransform out_rPosition) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetPosition(AkSoundEngine.GetAkGameObjectID(in_GameObjectID), AkTransform.getCPtr(out_rPosition)); }

  public static AKRESULT GetListenerPosition(UnityEngine.GameObject in_uIndex, AkTransform out_rPosition) {

	var in_uIndex_id = AkSoundEngine.GetAkGameObjectID(in_uIndex);
	AkSoundEngine.PreGameObjectAPICall(in_uIndex, in_uIndex_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetListenerPosition(in_uIndex_id, AkTransform.getCPtr(out_rPosition)); }
  }

  public static AKRESULT GetRTPCValue(uint in_rtpcID, UnityEngine.GameObject in_gameObjectID, uint in_playingID, out float out_rValue, ref int io_rValueType) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetRTPCValue__SWIG_0(in_rtpcID, in_gameObjectID_id, in_playingID, out out_rValue, ref io_rValueType); }
  }

  public static AKRESULT GetRTPCValue(string in_pszRtpcName, UnityEngine.GameObject in_gameObjectID, uint in_playingID, out float out_rValue, ref int io_rValueType) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetRTPCValue__SWIG_1(in_pszRtpcName, in_gameObjectID_id, in_playingID, out out_rValue, ref io_rValueType); }
  }

  public static AKRESULT GetSwitch(uint in_switchGroup, UnityEngine.GameObject in_gameObjectID, out uint out_rSwitchState) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetSwitch__SWIG_0(in_switchGroup, in_gameObjectID_id, out out_rSwitchState); }
  }

  public static AKRESULT GetSwitch(string in_pstrSwitchGroupName, UnityEngine.GameObject in_GameObj, out uint out_rSwitchState) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetSwitch__SWIG_1(in_pstrSwitchGroupName, AkSoundEngine.GetAkGameObjectID(in_GameObj), out out_rSwitchState); }

  public static AKRESULT GetState(uint in_stateGroup, out uint out_rState) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetState__SWIG_0(in_stateGroup, out out_rState); }

  public static AKRESULT GetState(string in_pstrStateGroupName, out uint out_rState) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetState__SWIG_1(in_pstrStateGroupName, out out_rState); }

  public static AKRESULT GetGameObjectAuxSendValues(UnityEngine.GameObject in_gameObjectID, AkAuxSendArray out_paAuxSendValues, ref uint io_ruNumSendValues) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetGameObjectAuxSendValues(in_gameObjectID_id, out_paAuxSendValues.GetBuffer(), ref io_ruNumSendValues); }
  }

  public static AKRESULT GetGameObjectDryLevelValue(UnityEngine.GameObject in_EmitterID, UnityEngine.GameObject in_ListenerID, out float out_rfControlValue) {

	var in_EmitterID_id = AkSoundEngine.GetAkGameObjectID(in_EmitterID);
	AkSoundEngine.PreGameObjectAPICall(in_EmitterID, in_EmitterID_id);


	var in_ListenerID_id = AkSoundEngine.GetAkGameObjectID(in_ListenerID);
	AkSoundEngine.PreGameObjectAPICall(in_ListenerID, in_ListenerID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetGameObjectDryLevelValue(in_EmitterID_id, in_ListenerID_id, out out_rfControlValue); }
  }

  public static AKRESULT GetObjectObstructionAndOcclusion(UnityEngine.GameObject in_EmitterID, UnityEngine.GameObject in_ListenerID, out float out_rfObstructionLevel, out float out_rfOcclusionLevel) {

	var in_EmitterID_id = AkSoundEngine.GetAkGameObjectID(in_EmitterID);
	AkSoundEngine.PreGameObjectAPICall(in_EmitterID, in_EmitterID_id);


	var in_ListenerID_id = AkSoundEngine.GetAkGameObjectID(in_ListenerID);
	AkSoundEngine.PreGameObjectAPICall(in_ListenerID, in_ListenerID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetObjectObstructionAndOcclusion(in_EmitterID_id, in_ListenerID_id, out out_rfObstructionLevel, out out_rfOcclusionLevel); }
  }

  public static AKRESULT QueryAudioObjectIDs(uint in_eventID, ref uint io_ruNumItems, AkObjectInfoArray out_aObjectInfos) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_QueryAudioObjectIDs__SWIG_0(in_eventID, ref io_ruNumItems, out_aObjectInfos.GetBuffer()); }

  public static AKRESULT QueryAudioObjectIDs(string in_pszEventName, ref uint io_ruNumItems, AkObjectInfoArray out_aObjectInfos) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_QueryAudioObjectIDs__SWIG_1(in_pszEventName, ref io_ruNumItems, out_aObjectInfos.GetBuffer()); }

  public static AKRESULT GetPositioningInfo(uint in_ObjectID, AkPositioningInfo out_rPositioningInfo) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetPositioningInfo(in_ObjectID, AkPositioningInfo.getCPtr(out_rPositioningInfo)); }

  public static bool GetIsGameObjectActive(UnityEngine.GameObject in_GameObjId) {

	var in_GameObjId_id = AkSoundEngine.GetAkGameObjectID(in_GameObjId);
	AkSoundEngine.PreGameObjectAPICall(in_GameObjId, in_GameObjId_id);

    { return AkSoundEnginePINVOKE.CSharp_GetIsGameObjectActive(in_GameObjId_id); }
  }

  public static float GetMaxRadius(UnityEngine.GameObject in_GameObjId) {

	var in_GameObjId_id = AkSoundEngine.GetAkGameObjectID(in_GameObjId);
	AkSoundEngine.PreGameObjectAPICall(in_GameObjId, in_GameObjId_id);

    { return AkSoundEnginePINVOKE.CSharp_GetMaxRadius(in_GameObjId_id); }
  }

  public static uint GetEventIDFromPlayingID(uint in_playingID) { return AkSoundEnginePINVOKE.CSharp_GetEventIDFromPlayingID(in_playingID); }

  public static ulong GetGameObjectFromPlayingID(uint in_playingID) { return AkSoundEnginePINVOKE.CSharp_GetGameObjectFromPlayingID(in_playingID); }

  public static AKRESULT GetPlayingIDsFromGameObject(UnityEngine.GameObject in_GameObjId, ref uint io_ruNumIDs, uint[] out_aPlayingIDs) {

	var in_GameObjId_id = AkSoundEngine.GetAkGameObjectID(in_GameObjId);
	AkSoundEngine.PreGameObjectAPICall(in_GameObjId, in_GameObjId_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetPlayingIDsFromGameObject(in_GameObjId_id, ref io_ruNumIDs, out_aPlayingIDs); }
  }

  public static AKRESULT GetCustomPropertyValue(uint in_ObjectID, uint in_uPropID, out int out_iValue) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetCustomPropertyValue__SWIG_0(in_ObjectID, in_uPropID, out out_iValue); }

  public static AKRESULT GetCustomPropertyValue(uint in_ObjectID, uint in_uPropID, out float out_fValue) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetCustomPropertyValue__SWIG_1(in_ObjectID, in_uPropID, out out_fValue); }

  public static void AK_SPEAKER_SETUP_FIX_LEFT_TO_CENTER(ref uint io_uChannelMask) { AkSoundEnginePINVOKE.CSharp_AK_SPEAKER_SETUP_FIX_LEFT_TO_CENTER(ref io_uChannelMask); }

  public static void AK_SPEAKER_SETUP_FIX_REAR_TO_SIDE(ref uint io_uChannelMask) { AkSoundEnginePINVOKE.CSharp_AK_SPEAKER_SETUP_FIX_REAR_TO_SIDE(ref io_uChannelMask); }

  public static void AK_SPEAKER_SETUP_CONVERT_TO_SUPPORTED(ref uint io_uChannelMask) { AkSoundEnginePINVOKE.CSharp_AK_SPEAKER_SETUP_CONVERT_TO_SUPPORTED(ref io_uChannelMask); }

  public static byte ChannelMaskToNumChannels(uint in_uChannelMask) { return AkSoundEnginePINVOKE.CSharp_ChannelMaskToNumChannels(in_uChannelMask); }

  public static uint ChannelMaskFromNumChannels(uint in_uNumChannels) { return AkSoundEnginePINVOKE.CSharp_ChannelMaskFromNumChannels(in_uNumChannels); }

  public static byte ChannelBitToIndex(uint in_uChannelBit, uint in_uChannelMask) { return AkSoundEnginePINVOKE.CSharp_ChannelBitToIndex(in_uChannelBit, in_uChannelMask); }

  public static bool HasSurroundChannels(uint in_uChannelMask) { return AkSoundEnginePINVOKE.CSharp_HasSurroundChannels(in_uChannelMask); }

  public static bool HasStrictlyOnePairOfSurroundChannels(uint in_uChannelMask) { return AkSoundEnginePINVOKE.CSharp_HasStrictlyOnePairOfSurroundChannels(in_uChannelMask); }

  public static bool HasSideAndRearChannels(uint in_uChannelMask) { return AkSoundEnginePINVOKE.CSharp_HasSideAndRearChannels(in_uChannelMask); }

  public static bool HasHeightChannels(uint in_uChannelMask) { return AkSoundEnginePINVOKE.CSharp_HasHeightChannels(in_uChannelMask); }

  public static uint BackToSideChannels(uint in_uChannelMask) { return AkSoundEnginePINVOKE.CSharp_BackToSideChannels(in_uChannelMask); }

  public static uint StdChannelIndexToDisplayIndex(AkChannelOrdering in_eOrdering, uint in_uChannelMask, uint in_uChannelIdx) { return AkSoundEnginePINVOKE.CSharp_StdChannelIndexToDisplayIndex((int)in_eOrdering, in_uChannelMask, in_uChannelIdx); }

  public static float kDefaultMaxPathLength { get { return AkSoundEnginePINVOKE.CSharp_kDefaultMaxPathLength_get(); } 
  }

  public static uint kDefaultDiffractionMaxEdges { get { return AkSoundEnginePINVOKE.CSharp_kDefaultDiffractionMaxEdges_get(); } 
  }

  public static uint kDefaultDiffractionMaxPaths { get { return AkSoundEnginePINVOKE.CSharp_kDefaultDiffractionMaxPaths_get(); } 
  }

  public static float kMaxDiffraction { get { return AkSoundEnginePINVOKE.CSharp_kMaxDiffraction_get(); } 
  }

  public static uint kListenerDiffractionMaxEdges { get { return AkSoundEnginePINVOKE.CSharp_kListenerDiffractionMaxEdges_get(); } 
  }

  public static uint kListenerDiffractionMaxPaths { get { return AkSoundEnginePINVOKE.CSharp_kListenerDiffractionMaxPaths_get(); } 
  }

  public static uint kPortalToPortalDiffractionMaxPaths { get { return AkSoundEnginePINVOKE.CSharp_kPortalToPortalDiffractionMaxPaths_get(); } 
  }

  public static int g_SpatialAudioPoolId { set { AkSoundEnginePINVOKE.CSharp_g_SpatialAudioPoolId_set(value); }  get { return AkSoundEnginePINVOKE.CSharp_g_SpatialAudioPoolId_get(); } 
  }

  public static int GetPoolID() { return AkSoundEnginePINVOKE.CSharp_GetPoolID(); }

  public static AKRESULT RegisterEmitter(UnityEngine.GameObject in_gameObjectID, AkEmitterSettings in_settings) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_RegisterEmitter(in_gameObjectID_id, AkEmitterSettings.getCPtr(in_settings)); }
  }

  public static AKRESULT UnregisterEmitter(UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_UnregisterEmitter(in_gameObjectID_id); }
  }

  public static AKRESULT SetEmitterAuxSendValues(UnityEngine.GameObject in_gameObjectID, AkAuxSendArray in_pAuxSends, uint in_uNumAux) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetEmitterAuxSendValues(in_gameObjectID_id, in_pAuxSends.GetBuffer(), in_uNumAux); }
  }

  public static AKRESULT SetImageSource(uint in_srcID, AkImageSourceSettings in_info, uint in_AuxBusID, ulong in_roomID, UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetImageSource__SWIG_0(in_srcID, AkImageSourceSettings.getCPtr(in_info), in_AuxBusID, in_roomID, in_gameObjectID_id); }
  }

  public static AKRESULT SetImageSource(uint in_srcID, AkImageSourceSettings in_info, uint in_AuxBusID, ulong in_roomID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetImageSource__SWIG_1(in_srcID, AkImageSourceSettings.getCPtr(in_info), in_AuxBusID, in_roomID); }

  public static AKRESULT RemoveImageSource(uint in_srcID, uint in_AuxBusID, UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_RemoveImageSource__SWIG_0(in_srcID, in_AuxBusID, in_gameObjectID_id); }
  }

  public static AKRESULT RemoveImageSource(uint in_srcID, uint in_AuxBusID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_RemoveImageSource__SWIG_1(in_srcID, in_AuxBusID); }

  public static AKRESULT RemoveGeometry(ulong in_SetID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_RemoveGeometry(in_SetID); }

  public static AKRESULT QueryReflectionPaths(UnityEngine.GameObject in_gameObjectID, AkVector out_listenerPos, AkVector out_emitterPos, AkReflectionPathInfoArray out_aPaths, out uint io_uArraySize) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_QueryReflectionPaths(in_gameObjectID_id, AkVector.getCPtr(out_listenerPos), AkVector.getCPtr(out_emitterPos), out_aPaths.GetBuffer(), out io_uArraySize); }
  }

  public static AKRESULT RemoveRoom(ulong in_RoomID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_RemoveRoom(in_RoomID); }

  public static AKRESULT RemovePortal(ulong in_PortalID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_RemovePortal(in_PortalID); }

  public static AKRESULT SetGameObjectInRoom(UnityEngine.GameObject in_gameObjectID, ulong in_CurrentRoomID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetGameObjectInRoom(in_gameObjectID_id, in_CurrentRoomID); }
  }

  public static AKRESULT SetEmitterObstructionAndOcclusion(UnityEngine.GameObject in_gameObjectID, float in_fObstruction, float in_fOcclusion) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetEmitterObstructionAndOcclusion(in_gameObjectID_id, in_fObstruction, in_fOcclusion); }
  }

  public static AKRESULT SetPortalObstructionAndOcclusion(ulong in_PortalID, float in_fObstruction, float in_fOcclusion) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetPortalObstructionAndOcclusion(in_PortalID, in_fObstruction, in_fOcclusion); }

  public static AKRESULT QueryWetDiffraction(ulong in_portal, out float out_wetDiffraction) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_QueryWetDiffraction(in_portal, out out_wetDiffraction); }

  public static void SetErrorLogger(AkLogger.ErrorLoggerInteropDelegate logger) { AkSoundEnginePINVOKE.CSharp_SetErrorLogger__SWIG_0(logger); }

  public static void SetErrorLogger() { AkSoundEnginePINVOKE.CSharp_SetErrorLogger__SWIG_1(); }

  public static void SetAudioInputCallbacks(AkAudioInputManager.AudioSamplesInteropDelegate getAudioSamples, AkAudioInputManager.AudioFormatInteropDelegate getAudioFormat) { AkSoundEnginePINVOKE.CSharp_SetAudioInputCallbacks(getAudioSamples, getAudioFormat); }

  public static AKRESULT Init(AkInitializationSettings settings) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_Init(AkInitializationSettings.getCPtr(settings)); }

  public static AKRESULT InitSpatialAudio(AkSpatialAudioInitSettings settings) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_InitSpatialAudio(AkSpatialAudioInitSettings.getCPtr(settings)); }

  public static AKRESULT InitCommunication(AkCommunicationSettings settings) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_InitCommunication(AkCommunicationSettings.getCPtr(settings)); }

  public static void Term() { AkSoundEnginePINVOKE.CSharp_Term(); }

  public static AKRESULT RegisterGameObjInternal(UnityEngine.GameObject in_GameObj) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_RegisterGameObjInternal(AkSoundEngine.GetAkGameObjectID(in_GameObj)); }

  public static AKRESULT UnregisterGameObjInternal(UnityEngine.GameObject in_GameObj) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_UnregisterGameObjInternal(AkSoundEngine.GetAkGameObjectID(in_GameObj)); }

  public static AKRESULT RegisterGameObjInternal_WithName(UnityEngine.GameObject in_GameObj, string in_pszObjName) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_RegisterGameObjInternal_WithName(AkSoundEngine.GetAkGameObjectID(in_GameObj), in_pszObjName); }

  public static AKRESULT SetBasePath(string in_pszBasePath) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetBasePath(in_pszBasePath); }

  public static AKRESULT SetCurrentLanguage(string in_pszAudioSrcPath) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetCurrentLanguage(in_pszAudioSrcPath); }

  public static AKRESULT LoadFilePackage(string in_pszFilePackageName, out uint out_uPackageID, int in_memPoolID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_LoadFilePackage(in_pszFilePackageName, out out_uPackageID, in_memPoolID); }

  public static AKRESULT AddBasePath(string in_pszBasePath) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_AddBasePath(in_pszBasePath); }

  public static AKRESULT SetGameName(string in_GameName) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetGameName(in_GameName); }

  public static AKRESULT SetDecodedBankPath(string in_DecodedPath) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetDecodedBankPath(in_DecodedPath); }

  public static AKRESULT LoadAndDecodeBank(string in_pszString, bool in_bSaveDecodedBank, out uint out_bankID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_LoadAndDecodeBank(in_pszString, in_bSaveDecodedBank, out out_bankID); }

  public static AKRESULT LoadAndDecodeBankFromMemory(global::System.IntPtr in_BankData, uint in_BankDataSize, bool in_bSaveDecodedBank, string in_DecodedBankName, bool in_bIsLanguageSpecific, out uint out_bankID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_LoadAndDecodeBankFromMemory(in_BankData, in_BankDataSize, in_bSaveDecodedBank, in_DecodedBankName, in_bIsLanguageSpecific, out out_bankID); }

  public static string GetCurrentLanguage() { return AkSoundEngine.StringFromIntPtrOSString(AkSoundEnginePINVOKE.CSharp_GetCurrentLanguage()); }

  public static AKRESULT UnloadFilePackage(uint in_uPackageID) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_UnloadFilePackage(in_uPackageID); }

  public static AKRESULT UnloadAllFilePackages() { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_UnloadAllFilePackages(); }

  public static AKRESULT SetObjectPosition(UnityEngine.GameObject in_GameObjectID, float PosX, float PosY, float PosZ, float FrontX, float FrontY, float FrontZ, float TopX, float TopY, float TopZ) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetObjectPosition(AkSoundEngine.GetAkGameObjectID(in_GameObjectID), PosX, PosY, PosZ, FrontX, FrontY, FrontZ, TopX, TopY, TopZ); }

  public static AKRESULT GetSourceMultiplePlayPositions(uint in_PlayingID, uint[] out_audioNodeID, uint[] out_mediaID, int[] out_msTime, ref uint io_pcPositions, bool in_bExtrapolate) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_GetSourceMultiplePlayPositions(in_PlayingID, out_audioNodeID, out_mediaID, out_msTime, ref io_pcPositions, in_bExtrapolate); }

  public static AKRESULT SetListeners(UnityEngine.GameObject in_emitterGameObj, ulong[] in_pListenerGameObjs, uint in_uNumListeners) {

	var in_emitterGameObj_id = AkSoundEngine.GetAkGameObjectID(in_emitterGameObj);
	AkSoundEngine.PreGameObjectAPICall(in_emitterGameObj, in_emitterGameObj_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetListeners(in_emitterGameObj_id, in_pListenerGameObjs, in_uNumListeners); }
  }

  public static AKRESULT SetDefaultListeners(ulong[] in_pListenerObjs, uint in_uNumListeners) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetDefaultListeners(in_pListenerObjs, in_uNumListeners); }

  public static AKRESULT AddOutput(AkOutputSettings in_Settings, out ulong out_pDeviceID, ulong[] in_pListenerIDs, uint in_uNumListeners) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_AddOutput(AkOutputSettings.getCPtr(in_Settings), out out_pDeviceID, in_pListenerIDs, in_uNumListeners); }

  public static void GetDefaultStreamSettings(AkStreamMgrSettings out_settings) { AkSoundEnginePINVOKE.CSharp_GetDefaultStreamSettings(AkStreamMgrSettings.getCPtr(out_settings)); }

  public static void GetDefaultDeviceSettings(AkDeviceSettings out_settings) { AkSoundEnginePINVOKE.CSharp_GetDefaultDeviceSettings(AkDeviceSettings.getCPtr(out_settings)); }

  public static void GetDefaultMusicSettings(AkMusicSettings out_settings) { AkSoundEnginePINVOKE.CSharp_GetDefaultMusicSettings(AkMusicSettings.getCPtr(out_settings)); }

  public static void GetDefaultInitSettings(AkInitSettings out_settings) { AkSoundEnginePINVOKE.CSharp_GetDefaultInitSettings(AkInitSettings.getCPtr(out_settings)); }

  public static void GetDefaultPlatformInitSettings(AkPlatformInitSettings out_settings) { AkSoundEnginePINVOKE.CSharp_GetDefaultPlatformInitSettings(AkPlatformInitSettings.getCPtr(out_settings)); }

  public static uint GetMajorMinorVersion() { return AkSoundEnginePINVOKE.CSharp_GetMajorMinorVersion(); }

  public static uint GetSubminorBuildVersion() { return AkSoundEnginePINVOKE.CSharp_GetSubminorBuildVersion(); }

  public static AKRESULT QueryIndirectPaths(UnityEngine.GameObject in_gameObjectID, AkPathParams arg1, AkReflectionPathInfoArray paths, uint numPaths) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_QueryIndirectPaths(in_gameObjectID_id, AkPathParams.getCPtr(arg1), paths.GetBuffer(), numPaths); }
  }

  public static AKRESULT QueryDiffractionPaths(UnityEngine.GameObject in_gameObjectID, AkPathParams arg1, AkDiffractionPathInfoArray paths, uint numPaths) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_QueryDiffractionPaths(in_gameObjectID_id, AkPathParams.getCPtr(arg1), paths.GetBuffer(), numPaths); }
  }

  public static AKRESULT SetRoomPortal(ulong in_PortalID, AkTransform Transform, AkVector Extent, bool bEnabled, ulong FrontRoom, ulong BackRoom) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetRoomPortal(in_PortalID, AkTransform.getCPtr(Transform), AkVector.getCPtr(Extent), bEnabled, FrontRoom, BackRoom); }

  public static AKRESULT SetRoom(ulong in_RoomID, AkRoomParams in_roomParams, string in_pName) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetRoom(in_RoomID, AkRoomParams.getCPtr(in_roomParams), in_pName); }

  public static AKRESULT RegisterSpatialAudioListener(UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_RegisterSpatialAudioListener(in_gameObjectID_id); }
  }

  public static AKRESULT UnregisterSpatialAudioListener(UnityEngine.GameObject in_gameObjectID) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_UnregisterSpatialAudioListener(in_gameObjectID_id); }
  }

  public static AKRESULT SetGeometry(ulong in_GeomSetID, AkTriangleArray Triangles, uint NumTriangles, AkVertexArray Vertices, uint NumVertices, AkAcousticSurfaceArray Surfaces, uint NumSurfaces, ulong RoomID, bool EnableDiffraction, bool EnableDiffractionOnBoundaryEdges) { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_SetGeometry(in_GeomSetID, Triangles.GetBuffer(), NumTriangles, Vertices.GetBuffer(), NumVertices, Surfaces.GetBuffer(), NumSurfaces, RoomID, EnableDiffraction, EnableDiffractionOnBoundaryEdges); }

  public const int AK_SIMD_ALIGNMENT = 16;
  public const int AK_BUFFER_ALIGNMENT = 16;
  public const int AK_MAX_PATH = 260;
  public const int AK_BANK_PLATFORM_DATA_ALIGNMENT = (16);
  public const uint AK_INVALID_PLUGINID = unchecked((uint)(-1));
  public const ulong AK_INVALID_GAME_OBJECT = unchecked((ulong)(-1));
  public const uint AK_INVALID_UNIQUE_ID = 0;
  public const uint AK_INVALID_RTPC_ID = AK_INVALID_UNIQUE_ID;
  public const uint AK_INVALID_LISTENER_INDEX = unchecked((uint)(-1));
  public const uint AK_INVALID_PLAYING_ID = AK_INVALID_UNIQUE_ID;
  public const uint AK_DEFAULT_SWITCH_STATE = 0;
  public const int AK_INVALID_POOL_ID = -1;
  public const int AK_DEFAULT_POOL_ID = -1;
  public const uint AK_INVALID_AUX_ID = AK_INVALID_UNIQUE_ID;
  public const uint AK_INVALID_FILE_ID = unchecked((uint)(-1));
  public const uint AK_INVALID_DEVICE_ID = unchecked((uint)(-1));
  public const uint AK_INVALID_BANK_ID = AK_INVALID_UNIQUE_ID;
  public const uint AK_FALLBACK_ARGUMENTVALUE_ID = 0;
  public const uint AK_INVALID_CHANNELMASK = 0;
  public const uint AK_INVALID_OUTPUT_DEVICE_ID = AK_INVALID_UNIQUE_ID;
  public const uint AK_MIXER_FX_SLOT = unchecked((uint)(-1));
  public const ulong AK_DEFAULT_LISTENER_OBJ = 0;
  public const uint AK_DEFAULT_PRIORITY = 50;
  public const uint AK_MIN_PRIORITY = 0;
  public const uint AK_MAX_PRIORITY = 100;
  public const uint AK_DEFAULT_BANK_IO_PRIORITY = AK_DEFAULT_PRIORITY;
  public const double AK_DEFAULT_BANK_THROUGHPUT = 1*1024*1024/1000.0;
  public const uint AKCOMPANYID_AUDIOKINETIC = 0;
  public const uint AK_LISTENERS_MASK_ALL = 0xFFFFFFFF;
  public const int NULL = 0;
  public const int AKCURVEINTERPOLATION_NUM_STORAGE_BIT = 5;
  public const int AK_MAX_LANGUAGE_NAME_SIZE = (32);
  public const int AKCOMPANYID_PLUGINDEV_MIN = (64);
  public const int AKCOMPANYID_PLUGINDEV_MAX = (255);
  public const int AKCOMPANYID_AUDIOKINETIC_EXTERNAL = (1);
  public const int AKCOMPANYID_MCDSP = (256);
  public const int AKCOMPANYID_WAVEARTS = (257);
  public const int AKCOMPANYID_PHONETICARTS = (258);
  public const int AKCOMPANYID_IZOTOPE = (259);
  public const int AKCOMPANYID_CRANKCASEAUDIO = (261);
  public const int AKCOMPANYID_IOSONO = (262);
  public const int AKCOMPANYID_AUROTECHNOLOGIES = (263);
  public const int AKCOMPANYID_DOLBY = (264);
  public const int AKCOMPANYID_TWOBIGEARS = (265);
  public const int AKCOMPANYID_OCULUS = (266);
  public const int AKCOMPANYID_BLUERIPPLESOUND = (267);
  public const int AKCOMPANYID_ENZIEN = (268);
  public const int AKCOMPANYID_KROTOS = (269);
  public const int AKCOMPANYID_NURULIZE = (270);
  public const int AKCOMPANYID_SUPERPOWERED = (271);
  public const int AKCOMPANYID_GOOGLE = (272);
  public const int AKCOMPANYID_VISISONICS = (277);
  public const int AKCODECID_BANK = (0);
  public const int AKCODECID_PCM = (1);
  public const int AKCODECID_ADPCM = (2);
  public const int AKCODECID_XMA = (3);
  public const int AKCODECID_VORBIS = (4);
  public const int AKCODECID_WIIADPCM = (5);
  public const int AKCODECID_PCMEX = (7);
  public const int AKCODECID_EXTERNAL_SOURCE = (8);
  public const int AKCODECID_XWMA = (9);
  public const int AKCODECID_AAC = (10);
  public const int AKCODECID_FILE_PACKAGE = (11);
  public const int AKCODECID_ATRAC9 = (12);
  public const int AKCODECID_VAG = (13);
  public const int AKCODECID_PROFILERCAPTURE = (14);
  public const int AKCODECID_ANALYSISFILE = (15);
  public const int AKCODECID_MIDI = (16);
  public const int AKCODECID_OPUSNX = (17);
  public const int AKCODECID_CAF = (18);
  public const int AKCODECID_AKOPUS = (19);
  public const int AKPLUGINID_METER = (129);
  public const int AKPLUGINID_RECORDER = (132);
  public const int AKEXTENSIONID_SPATIALAUDIO = (800);
  public const int AKEXTENSIONID_INTERACTIVEMUSIC = (801);
  public const int AKEXTENSIONID_EVENTMGRTHREAD = (900);
  public const int AK_WAVE_FORMAT_VAG = 0xFFFB;
  public const int AK_WAVE_FORMAT_AT9 = 0xFFFC;
  public const int AK_WAVE_FORMAT_VORBIS = 0xFFFF;
  public const int AK_WAVE_FORMAT_AAC = 0xAAC0;
  public const int AK_WAVE_FORMAT_OPUSNX = 0x3039;
  public const int AK_WAVE_FORMAT_OPUS = 0x3040;
  public const int WAVE_FORMAT_XMA2 = 0x166;
  public const int AK_PANNER_NUM_STORAGE_BITS = 3;
  public const int AK_POSSOURCE_NUM_STORAGE_BITS = 3;
  public const int AK_SPAT_NUM_STORAGE_BITS = 3;
  public const int AK_MAX_BITS_METERING_FLAGS = (5);
  public const int AK_OS_STRUCT_ALIGN = 4;
  public const int AK_64B_OS_STRUCT_ALIGN = 8;
  public const bool AK_ASYNC_OPEN_DEFAULT = (false);
  public const int AK_COMM_DEFAULT_DISCOVERY_PORT = 24024;
  public const int AK_MIDI_EVENT_TYPE_INVALID = 0x00;
  public const int AK_MIDI_EVENT_TYPE_NOTE_OFF = 0x80;
  public const int AK_MIDI_EVENT_TYPE_NOTE_ON = 0x90;
  public const int AK_MIDI_EVENT_TYPE_NOTE_AFTERTOUCH = 0xa0;
  public const int AK_MIDI_EVENT_TYPE_CONTROLLER = 0xb0;
  public const int AK_MIDI_EVENT_TYPE_PROGRAM_CHANGE = 0xc0;
  public const int AK_MIDI_EVENT_TYPE_CHANNEL_AFTERTOUCH = 0xd0;
  public const int AK_MIDI_EVENT_TYPE_PITCH_BEND = 0xe0;
  public const int AK_MIDI_EVENT_TYPE_SYSEX = 0xf0;
  public const int AK_MIDI_EVENT_TYPE_ESCAPE = 0xf7;
  public const int AK_MIDI_EVENT_TYPE_META = 0xff;
  public const int AK_MIDI_CC_BANK_SELECT_COARSE = 0;
  public const int AK_MIDI_CC_MOD_WHEEL_COARSE = 1;
  public const int AK_MIDI_CC_BREATH_CTRL_COARSE = 2;
  public const int AK_MIDI_CC_CTRL_3_COARSE = 3;
  public const int AK_MIDI_CC_FOOT_PEDAL_COARSE = 4;
  public const int AK_MIDI_CC_PORTAMENTO_COARSE = 5;
  public const int AK_MIDI_CC_DATA_ENTRY_COARSE = 6;
  public const int AK_MIDI_CC_VOLUME_COARSE = 7;
  public const int AK_MIDI_CC_BALANCE_COARSE = 8;
  public const int AK_MIDI_CC_CTRL_9_COARSE = 9;
  public const int AK_MIDI_CC_PAN_POSITION_COARSE = 10;
  public const int AK_MIDI_CC_EXPRESSION_COARSE = 11;
  public const int AK_MIDI_CC_EFFECT_CTRL_1_COARSE = 12;
  public const int AK_MIDI_CC_EFFECT_CTRL_2_COARSE = 13;
  public const int AK_MIDI_CC_CTRL_14_COARSE = 14;
  public const int AK_MIDI_CC_CTRL_15_COARSE = 15;
  public const int AK_MIDI_CC_GEN_SLIDER_1 = 16;
  public const int AK_MIDI_CC_GEN_SLIDER_2 = 17;
  public const int AK_MIDI_CC_GEN_SLIDER_3 = 18;
  public const int AK_MIDI_CC_GEN_SLIDER_4 = 19;
  public const int AK_MIDI_CC_CTRL_20_COARSE = 20;
  public const int AK_MIDI_CC_CTRL_21_COARSE = 21;
  public const int AK_MIDI_CC_CTRL_22_COARSE = 22;
  public const int AK_MIDI_CC_CTRL_23_COARSE = 23;
  public const int AK_MIDI_CC_CTRL_24_COARSE = 24;
  public const int AK_MIDI_CC_CTRL_25_COARSE = 25;
  public const int AK_MIDI_CC_CTRL_26_COARSE = 26;
  public const int AK_MIDI_CC_CTRL_27_COARSE = 27;
  public const int AK_MIDI_CC_CTRL_28_COARSE = 28;
  public const int AK_MIDI_CC_CTRL_29_COARSE = 29;
  public const int AK_MIDI_CC_CTRL_30_COARSE = 30;
  public const int AK_MIDI_CC_CTRL_31_COARSE = 31;
  public const int AK_MIDI_CC_BANK_SELECT_FINE = 32;
  public const int AK_MIDI_CC_MOD_WHEEL_FINE = 33;
  public const int AK_MIDI_CC_BREATH_CTRL_FINE = 34;
  public const int AK_MIDI_CC_CTRL_3_FINE = 35;
  public const int AK_MIDI_CC_FOOT_PEDAL_FINE = 36;
  public const int AK_MIDI_CC_PORTAMENTO_FINE = 37;
  public const int AK_MIDI_CC_DATA_ENTRY_FINE = 38;
  public const int AK_MIDI_CC_VOLUME_FINE = 39;
  public const int AK_MIDI_CC_BALANCE_FINE = 40;
  public const int AK_MIDI_CC_CTRL_9_FINE = 41;
  public const int AK_MIDI_CC_PAN_POSITION_FINE = 42;
  public const int AK_MIDI_CC_EXPRESSION_FINE = 43;
  public const int AK_MIDI_CC_EFFECT_CTRL_1_FINE = 44;
  public const int AK_MIDI_CC_EFFECT_CTRL_2_FINE = 45;
  public const int AK_MIDI_CC_CTRL_14_FINE = 46;
  public const int AK_MIDI_CC_CTRL_15_FINE = 47;
  public const int AK_MIDI_CC_CTRL_20_FINE = 52;
  public const int AK_MIDI_CC_CTRL_21_FINE = 53;
  public const int AK_MIDI_CC_CTRL_22_FINE = 54;
  public const int AK_MIDI_CC_CTRL_23_FINE = 55;
  public const int AK_MIDI_CC_CTRL_24_FINE = 56;
  public const int AK_MIDI_CC_CTRL_25_FINE = 57;
  public const int AK_MIDI_CC_CTRL_26_FINE = 58;
  public const int AK_MIDI_CC_CTRL_27_FINE = 59;
  public const int AK_MIDI_CC_CTRL_28_FINE = 60;
  public const int AK_MIDI_CC_CTRL_29_FINE = 61;
  public const int AK_MIDI_CC_CTRL_30_FINE = 62;
  public const int AK_MIDI_CC_CTRL_31_FINE = 63;
  public const int AK_MIDI_CC_HOLD_PEDAL = 64;
  public const int AK_MIDI_CC_PORTAMENTO_ON_OFF = 65;
  public const int AK_MIDI_CC_SUSTENUTO_PEDAL = 66;
  public const int AK_MIDI_CC_SOFT_PEDAL = 67;
  public const int AK_MIDI_CC_LEGATO_PEDAL = 68;
  public const int AK_MIDI_CC_HOLD_PEDAL_2 = 69;
  public const int AK_MIDI_CC_SOUND_VARIATION = 70;
  public const int AK_MIDI_CC_SOUND_TIMBRE = 71;
  public const int AK_MIDI_CC_SOUND_RELEASE_TIME = 72;
  public const int AK_MIDI_CC_SOUND_ATTACK_TIME = 73;
  public const int AK_MIDI_CC_SOUND_BRIGHTNESS = 74;
  public const int AK_MIDI_CC_SOUND_CTRL_6 = 75;
  public const int AK_MIDI_CC_SOUND_CTRL_7 = 76;
  public const int AK_MIDI_CC_SOUND_CTRL_8 = 77;
  public const int AK_MIDI_CC_SOUND_CTRL_9 = 78;
  public const int AK_MIDI_CC_SOUND_CTRL_10 = 79;
  public const int AK_MIDI_CC_GENERAL_BUTTON_1 = 80;
  public const int AK_MIDI_CC_GENERAL_BUTTON_2 = 81;
  public const int AK_MIDI_CC_GENERAL_BUTTON_3 = 82;
  public const int AK_MIDI_CC_GENERAL_BUTTON_4 = 83;
  public const int AK_MIDI_CC_REVERB_LEVEL = 91;
  public const int AK_MIDI_CC_TREMOLO_LEVEL = 92;
  public const int AK_MIDI_CC_CHORUS_LEVEL = 93;
  public const int AK_MIDI_CC_CELESTE_LEVEL = 94;
  public const int AK_MIDI_CC_PHASER_LEVEL = 95;
  public const int AK_MIDI_CC_DATA_BUTTON_P1 = 96;
  public const int AK_MIDI_CC_DATA_BUTTON_M1 = 97;
  public const int AK_MIDI_CC_NON_REGISTER_COARSE = 98;
  public const int AK_MIDI_CC_NON_REGISTER_FINE = 99;
  public const int AK_MIDI_CC_ALL_SOUND_OFF = 120;
  public const int AK_MIDI_CC_ALL_CONTROLLERS_OFF = 121;
  public const int AK_MIDI_CC_LOCAL_KEYBOARD = 122;
  public const int AK_MIDI_CC_ALL_NOTES_OFF = 123;
  public const int AK_MIDI_CC_OMNI_MODE_OFF = 124;
  public const int AK_MIDI_CC_OMNI_MODE_ON = 125;
  public const int AK_MIDI_CC_OMNI_MONOPHONIC_ON = 126;
  public const int AK_MIDI_CC_OMNI_POLYPHONIC_ON = 127;
  public const int AK_SPEAKER_FRONT_LEFT = 0x1;
  public const int AK_SPEAKER_FRONT_RIGHT = 0x2;
  public const int AK_SPEAKER_FRONT_CENTER = 0x4;
  public const int AK_SPEAKER_LOW_FREQUENCY = 0x8;
  public const int AK_SPEAKER_BACK_LEFT = 0x10;
  public const int AK_SPEAKER_BACK_RIGHT = 0x20;
  public const int AK_SPEAKER_BACK_CENTER = 0x100;
  public const int AK_SPEAKER_SIDE_LEFT = 0x200;
  public const int AK_SPEAKER_SIDE_RIGHT = 0x400;
  public const int AK_SPEAKER_TOP = 0x800;
  public const int AK_SPEAKER_HEIGHT_FRONT_LEFT = 0x1000;
  public const int AK_SPEAKER_HEIGHT_FRONT_CENTER = 0x2000;
  public const int AK_SPEAKER_HEIGHT_FRONT_RIGHT = 0x4000;
  public const int AK_SPEAKER_HEIGHT_BACK_LEFT = 0x8000;
  public const int AK_SPEAKER_HEIGHT_BACK_CENTER = 0x10000;
  public const int AK_SPEAKER_HEIGHT_BACK_RIGHT = 0x20000;
  public const int AK_SPEAKER_SETUP_MONO = 0x4;
  public const int AK_SPEAKER_SETUP_0POINT1 = 0x8;
  public const int AK_SPEAKER_SETUP_1POINT1 = (0x4|0x8);
  public const int AK_SPEAKER_SETUP_STEREO = (0x1|0x2);
  public const int AK_SPEAKER_SETUP_2POINT1 = ((0x1|0x2)|0x8);
  public const int AK_SPEAKER_SETUP_3STEREO = ((0x1|0x2)|0x4);
  public const int AK_SPEAKER_SETUP_3POINT1 = (((0x1|0x2)|0x4)|0x8);
  public const int AK_SPEAKER_SETUP_4 = ((0x1|0x2)|0x200|0x400);
  public const int AK_SPEAKER_SETUP_4POINT1 = (((0x1|0x2)|0x200|0x400)|0x8);
  public const int AK_SPEAKER_SETUP_5 = (((0x1|0x2)|0x200|0x400)|0x4);
  public const int AK_SPEAKER_SETUP_5POINT1 = ((((0x1|0x2)|0x200|0x400)|0x4)|0x8);
  public const int AK_SPEAKER_SETUP_6 = (((0x1|0x2)|0x200|0x400)|0x10|0x20);
  public const int AK_SPEAKER_SETUP_6POINT1 = ((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x8);
  public const int AK_SPEAKER_SETUP_7 = ((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x4);
  public const int AK_SPEAKER_SETUP_7POINT1 = (((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x4)|0x8);
  public const int AK_SPEAKER_SETUP_SURROUND = ((0x1|0x2)|0x100);
  public const int AK_SPEAKER_SETUP_DPL2 = (((0x1|0x2)|0x200|0x400));
  public const int AK_SPEAKER_SETUP_HEIGHT_4 = (0x1000|0x4000|0x8000|0x20000);
  public const int AK_SPEAKER_SETUP_HEIGHT_5 = ((0x1000|0x4000|0x8000|0x20000)|0x2000);
  public const int AK_SPEAKER_SETUP_HEIGHT_ALL = (((0x1000|0x4000|0x8000|0x20000)|0x2000)|0x10000);
  public const int AK_SPEAKER_SETUP_AURO_222 = (((0x1|0x2)|0x200|0x400)|0x1000|0x4000);
  public const int AK_SPEAKER_SETUP_AURO_8 = ((((0x1|0x2)|0x200|0x400)|0x1000|0x4000)|0x8000|0x20000);
  public const int AK_SPEAKER_SETUP_AURO_9 = (((((0x1|0x2)|0x200|0x400)|0x1000|0x4000)|0x8000|0x20000)|0x4);
  public const int AK_SPEAKER_SETUP_AURO_9POINT1 = ((((((0x1|0x2)|0x200|0x400)|0x1000|0x4000)|0x8000|0x20000)|0x4)|0x8);
  public const int AK_SPEAKER_SETUP_AURO_10 = ((((((0x1|0x2)|0x200|0x400)|0x1000|0x4000)|0x8000|0x20000)|0x4)|0x800);
  public const int AK_SPEAKER_SETUP_AURO_10POINT1 = (((((((0x1|0x2)|0x200|0x400)|0x1000|0x4000)|0x8000|0x20000)|0x4)|0x800)|0x8);
  public const int AK_SPEAKER_SETUP_AURO_11 = (((((((0x1|0x2)|0x200|0x400)|0x1000|0x4000)|0x8000|0x20000)|0x4)|0x800)|0x2000);
  public const int AK_SPEAKER_SETUP_AURO_11POINT1 = ((((((((0x1|0x2)|0x200|0x400)|0x1000|0x4000)|0x8000|0x20000)|0x4)|0x800)|0x2000)|0x8);
  public const int AK_SPEAKER_SETUP_AURO_11_740 = (((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x4)|(0x1000|0x4000|0x8000|0x20000));
  public const int AK_SPEAKER_SETUP_AURO_11POINT1_740 = ((((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x4)|(0x1000|0x4000|0x8000|0x20000))|0x8);
  public const int AK_SPEAKER_SETUP_AURO_13_751 = (((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x4)|((0x1000|0x4000|0x8000|0x20000)|0x2000)|0x800);
  public const int AK_SPEAKER_SETUP_AURO_13POINT1_751 = ((((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x4)|((0x1000|0x4000|0x8000|0x20000)|0x2000)|0x800)|0x8);
  public const int AK_SPEAKER_SETUP_DOLBY_5_0_2 = ((((0x1|0x2)|0x200|0x400)|0x4)|0x1000|0x4000);
  public const int AK_SPEAKER_SETUP_DOLBY_5_1_2 = (((((0x1|0x2)|0x200|0x400)|0x4)|0x1000|0x4000)|0x8);
  public const int AK_SPEAKER_SETUP_DOLBY_6_0_2 = ((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x1000|0x4000);
  public const int AK_SPEAKER_SETUP_DOLBY_6_1_2 = (((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x1000|0x4000)|0x8);
  public const int AK_SPEAKER_SETUP_DOLBY_6_0_4 = (((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x1000|0x4000)|0x8000|0x20000);
  public const int AK_SPEAKER_SETUP_DOLBY_6_1_4 = ((((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x1000|0x4000)|0x8000|0x20000)|0x8);
  public const int AK_SPEAKER_SETUP_DOLBY_7_0_2 = (((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x4)|0x1000|0x4000);
  public const int AK_SPEAKER_SETUP_DOLBY_7_1_2 = ((((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x4)|0x1000|0x4000)|0x8);
  public const int AK_SPEAKER_SETUP_DOLBY_7_0_4 = ((((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x4)|0x1000|0x4000)|0x8000|0x20000);
  public const int AK_SPEAKER_SETUP_DOLBY_7_1_4 = (((((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x4)|0x1000|0x4000)|0x8000|0x20000)|0x8);
  public const int AK_SPEAKER_SETUP_ALL_SPEAKERS = ((((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x4)|0x8)|0x100|(((0x1000|0x4000|0x8000|0x20000)|0x2000)|0x10000)|0x800);
  public const int AK_IDX_SETUP_FRONT_LEFT = (0);
  public const int AK_IDX_SETUP_FRONT_RIGHT = (1);
  public const int AK_IDX_SETUP_CENTER = (2);
  public const int AK_IDX_SETUP_NOCENTER_BACK_LEFT = (2);
  public const int AK_IDX_SETUP_NOCENTER_BACK_RIGHT = (3);
  public const int AK_IDX_SETUP_NOCENTER_SIDE_LEFT = (4);
  public const int AK_IDX_SETUP_NOCENTER_SIDE_RIGHT = (5);
  public const int AK_IDX_SETUP_WITHCENTER_BACK_LEFT = (3);
  public const int AK_IDX_SETUP_WITHCENTER_BACK_RIGHT = (4);
  public const int AK_IDX_SETUP_WITHCENTER_SIDE_LEFT = (5);
  public const int AK_IDX_SETUP_WITHCENTER_SIDE_RIGHT = (6);
  public const int AK_IDX_SETUP_0_LFE = (0);
  public const int AK_IDX_SETUP_1_CENTER = (0);
  public const int AK_IDX_SETUP_1_LFE = (1);
  public const int AK_IDX_SETUP_2_LEFT = (0);
  public const int AK_IDX_SETUP_2_RIGHT = (1);
  public const int AK_IDX_SETUP_2_LFE = (2);
  public const int AK_IDX_SETUP_3_LEFT = (0);
  public const int AK_IDX_SETUP_3_RIGHT = (1);
  public const int AK_IDX_SETUP_3_CENTER = (2);
  public const int AK_IDX_SETUP_3_LFE = (3);
  public const int AK_IDX_SETUP_4_FRONTLEFT = (0);
  public const int AK_IDX_SETUP_4_FRONTRIGHT = (1);
  public const int AK_IDX_SETUP_4_REARLEFT = (2);
  public const int AK_IDX_SETUP_4_REARRIGHT = (3);
  public const int AK_IDX_SETUP_4_LFE = (4);
  public const int AK_IDX_SETUP_5_FRONTLEFT = (0);
  public const int AK_IDX_SETUP_5_FRONTRIGHT = (1);
  public const int AK_IDX_SETUP_5_CENTER = (2);
  public const int AK_IDX_SETUP_5_REARLEFT = (3);
  public const int AK_IDX_SETUP_5_REARRIGHT = (4);
  public const int AK_IDX_SETUP_5_LFE = (5);
  public const int AK_IDX_SETUP_6_FRONTLEFT = (0);
  public const int AK_IDX_SETUP_6_FRONTRIGHT = (1);
  public const int AK_IDX_SETUP_6_REARLEFT = (2);
  public const int AK_IDX_SETUP_6_REARRIGHT = (3);
  public const int AK_IDX_SETUP_6_SIDELEFT = (4);
  public const int AK_IDX_SETUP_6_SIDERIGHT = (5);
  public const int AK_IDX_SETUP_6_LFE = (6);
  public const int AK_IDX_SETUP_7_FRONTLEFT = (0);
  public const int AK_IDX_SETUP_7_FRONTRIGHT = (1);
  public const int AK_IDX_SETUP_7_CENTER = (2);
  public const int AK_IDX_SETUP_7_REARLEFT = (3);
  public const int AK_IDX_SETUP_7_REARRIGHT = (4);
  public const int AK_IDX_SETUP_7_SIDELEFT = (5);
  public const int AK_IDX_SETUP_7_SIDERIGHT = (6);
  public const int AK_IDX_SETUP_7_LFE = (7);
  public const int AK_SPEAKER_SETUP_0_1 = (0x8);
  public const int AK_SPEAKER_SETUP_1_0_CENTER = (0x4);
  public const int AK_SPEAKER_SETUP_1_1_CENTER = (0x4|0x8);
  public const int AK_SPEAKER_SETUP_2_0 = (0x1|0x2);
  public const int AK_SPEAKER_SETUP_2_1 = (0x1|0x2|0x8);
  public const int AK_SPEAKER_SETUP_3_0 = (0x1|0x2|0x4);
  public const int AK_SPEAKER_SETUP_3_1 = ((0x1|0x2|0x4)|0x8);
  public const int AK_SPEAKER_SETUP_FRONT = ((0x1|0x2|0x4));
  public const int AK_SPEAKER_SETUP_4_0 = (((0x1|0x2)|0x200|0x400));
  public const int AK_SPEAKER_SETUP_4_1 = ((((0x1|0x2)|0x200|0x400)|0x8));
  public const int AK_SPEAKER_SETUP_5_0 = ((((0x1|0x2)|0x200|0x400)|0x4));
  public const int AK_SPEAKER_SETUP_5_1 = (((((0x1|0x2)|0x200|0x400)|0x4)|0x8));
  public const int AK_SPEAKER_SETUP_6_0 = ((((0x1|0x2)|0x200|0x400)|0x10|0x20));
  public const int AK_SPEAKER_SETUP_6_1 = (((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x8));
  public const int AK_SPEAKER_SETUP_7_0 = (((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x4));
  public const int AK_SPEAKER_SETUP_7_1 = ((((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x4)|0x8));
  public const int AK_SPEAKER_SETUP_DEFAULT_PLANE = ((((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x4)|0x8));
  public const int AK_SUPPORTED_STANDARD_CHANNEL_MASK = (((((((0x1|0x2)|0x200|0x400)|0x10|0x20)|0x4)|0x8)|0x100|(((0x1000|0x4000|0x8000|0x20000)|0x2000)|0x10000)|0x800));
  public const int AK_STANDARD_MAX_NUM_CHANNELS = (8);
  public const int AK_NUM_SAMPLED_SPHERE_POINTS = (32);
  public const int AK_MAX_NUM_TEXTURE = 4;
  public const int AK_MAX_REFLECT_ORDER = 4;
  public const int AK_MAX_REFLECTION_PATH_LENGTH = (4+2);
  public const int AK_MAX_SOUND_PROPAGATION_DEPTH = 8;
  public const double AK_DEFAULT_DIFFR_SHADOW_DEGREES = (30.0);
  public const double AK_DEFAULT_DIFFR_SHADOW_ATTEN = (2.0);
  public const double AK_DEFAULT_MOVEMENT_THRESHOLD = (1.0);
  public const double AK_SA_EPSILON = (0.001);
  public const double AK_SA_DIFFRACTION_EPSILON = (0.1);
  public const double AK_SA_PLANE_THICKNESS_RATIO = (0.005);
}
#endif // #if UNITY_IOS && ! UNITY_EDITOR