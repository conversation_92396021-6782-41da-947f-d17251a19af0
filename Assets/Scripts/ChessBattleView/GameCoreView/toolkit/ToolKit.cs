#if ACGGAME_CLIENT
using UnityEngine;
using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using Lucifer.ActCore;

public class ToolKit : MonoBehaviour
{

    private static bool inited = false;
    private static DelayManager dm;

    public static void init(Transform root)
    {
        if (!inited)
        {
            GameObject dmObj = new GameObject();
            dmObj.name = "DelayManager";
            dmObj.transform.parent = root;
            //GameObject.DontDestroyOnLoad(dmObj);
            dm = dmObj.AddComponent<DelayManager>();
            dm.InitDelayCallerPool();
            inited = true;
        }

    }
    //public static ToolKit getInstence()
    //{
    //    if (instence == null)
    //    {
    //        GameObject go = new GameObject();
    //        go.name = "ToolKit";
    //        instence = go.AddComponent<ToolKit>();

    //        DontDestroyOnLoad(go);

    //        //GameObject tmObj = new GameObject();
    //        //tmObj.name = "TimeMachine";
    //        //tmObj.transform.parent = go.transform;
    //        //instence.tm = tmObj.AddComponent<TimeMachine>();

    //    }
    //    return instence;

    //}
    public static GameObject getKid(string name, GameObject parent)
    {
        if (parent == null) return null;
        foreach (Transform t in parent.GetComponentsInChildren<Transform>())
        {
            if (t.name == name)
            {
                return t.gameObject;
            }
        }
        return null;
    }

    //移出所有子对象
    public static void RemoveAllChild(Transform transform)
    {
        while (0 < transform.childCount)
        {
            GameObject.DestroyImmediate(transform.GetChild(0).gameObject);
        }
    }

    //获得一个组件，包括deactive的
    public static T GetComponentInChildren<T>(Transform parent) where T : Component
    {
        T component = GetComponent<T>(parent);
        return component;
    }
    //遍历所有子对象
    public static T GetComponent<T>(Transform transfrom) where T : Component
    {
        T component = transfrom.GetComponent<T>();
        if (null != component)
        {
            return component;
        }
        foreach (Transform child in transfrom)
        {
            return GetComponent<T>(child);
        }
        return null;
    }

    //获得所有组件，包括deactive的
    public static List<T> GetComponentsInChildren<T>(Transform parent) where T : Component
    {
        List<T> components = new List<T>();
        GetComponent<T>(parent, ref components);
        return components;
    }

    //遍历所有子对象
    private static void GetComponent<T>(Transform transfrom, ref List<T> list) where T : Component
    {
        T component = transfrom.GetComponent<T>();
        if (null != component)
        {
            list.Add(component);
        }
        foreach (Transform child in transfrom)
        {
            GetComponent<T>(child, ref list);
        }
    }

    public static string delayCall(float t, DelegateEnums.NoneParam fn, string ID = "")
    {
        // return getInstence().tm.addDelay(t, fn, ID);
        return dm.addDelay(t, fn, ID);
    }
    public static string delayCall(float t, DelegateEnums.DataParam fn, object data, string ID = "")
    {
        // return getInstence().tm.addDelay(t, fn, data, ID);
        return dm.addDelay(t, fn, data, ID);
    }

    //暂停一个延迟
    public static void pauseDelay(string ID)
    {
        dm.pauseDelay(ID);
    }
    //继续一个延迟
    public static void continuePlay(string ID)
    {
        dm.continuePlay(ID);
    }
    public static void stopDelayCall(string id)
    {
        //if (instence != null)
        //{
        //    getInstence().tm.stopDelay(id);
        //}
        if (dm != null)
        {
            dm.stopDelay(id);
        }
    }

    public static bool hasDelayCall(string id)
    {
        if (dm != null)
        {
            dm.hasDelay(id);
        }

        return false;
    }

    //设置基本属性
    public static void initUIAttr(GameObject ui, GameObject parent = null)
    {
        if (parent)
        {
            ui.transform.parent = parent.transform;
        }
        //初始化属性
        ui.gameObject.transform.localPosition = new Vector3(0, 0, 0);
        ui.gameObject.transform.localScale = new Vector3(1, 1, 1);
    }

    //协程做延迟
    public static void coroutineDelay(float t, DelegateEnums.NoneParam fn)
    {
        dm.addDelay(t, fn);
        //getInstence().StartCoroutine(instence.delayExec(t, fn));
    }
    //执行
    //private IEnumerator delayExec(float t, DelegateEnums.NoneParam fn)
    //{
    //    yield return new WaitForSeconds(t);
    //    fn();
    //}



    public static string GetColorStringByColor(Color tmpColor)
    {
        int rInt = (int)(tmpColor.r * 255.0f);
        int gInt = (int)(tmpColor.g * 255.0f);
        int bInt = (int)(tmpColor.b * 255.0f);

        string red = Convert.ToString(rInt, 16);
        string green = Convert.ToString(gInt, 16);
        string blue = Convert.ToString(bInt, 16);
        if (red.Length == 1)
        {
            red = "0" + red;
        }
        if (green.Length == 1)
        {
            green = "0" + green;
        }
        if (blue.Length == 1)
        {
            blue = "0" + blue;
        }

        string colorStr = "[" + red + green + blue + "ff]";

        return colorStr;
    }

    public static int toInt(string str)
    {
        return Convert.ToInt32(str);
    }
    public static ushort toShort(string str)
    {
        return Convert.ToUInt16(str);
    }

    public static void modifyRenderQ(GameObject go, int queue)
    {
        foreach (Renderer renderer in go.GetComponentsInChildren<Renderer>())
        {
            // 默认UI为3000，略高于UI
            renderer.material.renderQueue = queue;
        }
    }

    public static void SetLayer(GameObject go, int layer)
    {
        go.layer = layer;
        Transform child = null;
        Transform goTransform = go.transform;
        for (int i = 0, len = goTransform.childCount; i < len; i++)
        //foreach (Transform child in go.transform)
        {
            child = goTransform.GetChild(i);
            SetLayer(child.gameObject, layer);
        }
    }

    public static void setSkinMeshRenderLayer(GameObject go, int layer)
    {
        SkinnedMeshRenderer[] skinnes = go.GetComponentsInChildren<SkinnedMeshRenderer>();
        for (int i = 0, len = skinnes.Length; i < len; i++)
        {
            SkinnedMeshRenderer t = skinnes[i];
            t.gameObject.layer = layer;
        }

        MeshRenderer[] skinnesMesh = go.GetComponentsInChildren<MeshRenderer>();
        for (int i = 0, len = skinnesMesh.Length; i < len; i++)
        {
            MeshRenderer t = skinnesMesh[i];
            t.gameObject.layer = layer;
        }
    }

    public static GameObject GetChildByName(string name, GameObject _parent)
    {
        MeshRenderer[] list = _parent.GetComponentsInChildren<MeshRenderer>();
        foreach (MeshRenderer t in list)
        {
            if (t.gameObject.name == name)
            {
                return t.gameObject;
            }
        }
        return null;
    }

    public static void DestroyComponent<T>(GameObject go, bool needDestroyGo = false) where T : Component
    {
        T t = go.GetComponent<T>();
        if (t != null)
        {
            if (true == needDestroyGo)
            {
                Destroy(t.gameObject);
            }
            else
            {
                DestroyImmediate(t);
            }

        }
    }

    public static void DestroyChildrenComponent<T>(GameObject go, bool needDestroyGo = false) where T : Component
    {
        T t = go.GetComponentInChildren<T>();
        if (t != null)
        {
            if (true == needDestroyGo)
            {
                Destroy(t.gameObject);
            }
            else
            {
                Destroy(t);
            }

        }
    }

    public static void UnloadSceneAssetObj(UnityEngine.Object clearObj)
    {
        //foreach(Transform child in targetObj.transform)
        if (null == clearObj)
        {
            return;
        }
        if (clearObj is GameObject)
        {
            UnloadAssetObj(clearObj as GameObject);
        }
        else
        {
            Resources.UnloadAsset(clearObj);
        }

    }

    //移除 内存里的资源
    public static void UnloadAssetObj(GameObject clearObj)
    {
        //foreach(Transform child in targetObj.transform)
        if (null == clearObj)
        {
            return;
        }
        UnloadAsset(clearObj);
        Transform child = null;
        Transform objTransform = clearObj.transform;
        for (int i = 0, len = objTransform.childCount; i < len; i++)
        {
            child = objTransform.GetChild(i);
            //UnloadAsset(child.gameObject);
            UnloadAssetObj(child.gameObject);
        }
    }

    static string[] shaderTextPropertyName = new string[] { "_ArmorTex", "_WhiteMaskTexA", "_WhiteMaskTexB", "_RampTex", "_ScarSparkMask", "SpecularTexture", "ReflectMask", "_DissolveSrc" };
    static void UnloadMaterialAOT(Material mat)
    {
        if (mat == null)
            return;
        Texture tmp2D = null;

        for (int i = 0; i < shaderTextPropertyName.Length; i++)
        {
            Resources.UnloadAsset(mat.mainTexture);
#if UNITY_EDITOR
            if (mat.HasProperty(shaderTextPropertyName[i]))
            {
                tmp2D = mat.GetTexture(shaderTextPropertyName[i]);
                if (null != tmp2D)
                {
                    Resources.UnloadAsset(tmp2D);
                }
            }


#else
			if(mat.HasProperty(shaderTextPropertyName[i]))
			{
				tmp2D = mat.GetTexture(shaderTextPropertyName[i]);
				if (null != tmp2D)
				{
					Resources.UnloadAsset(tmp2D);
				}
			}

#endif
        }
    }


    public static void UnloadAsset(GameObject obj)
    {
        if (obj == null)
            return;
        SkinnedMeshRenderer[] list = obj.GetComponents<SkinnedMeshRenderer>();
        for (int i = 0, len1 = list.Length; i < len1; i++)
        {
            Resources.UnloadAsset(list[i].sharedMesh);

            Material[] mList = list[i].sharedMaterials;
            for (int j = 0, len = mList.Length; j < len; ++j)
            {
                UnloadMaterialAOT(mList[j]);
            }
        }
        MeshRenderer[] list2 = obj.GetComponents<MeshRenderer>();
        for (int i = 0; i < list2.Length; i++)
        {
            Material[] mList = list2[i].sharedMaterials;
            for (int j = 0, len = mList.Length; j < len; ++j)
            {
                //Resources.UnloadAsset(mList[j]);
                //Log.info(mList[j].mainTexture.name);
                UnloadMaterialAOT(mList[j]);
                // Resources.UnloadAsset(mList[j].mainTexture);
            }
        }
        MeshFilter[] meshFilterList = obj.GetComponents<MeshFilter>();

        for (int i = 0, len1 = meshFilterList.Length; i < len1; i++)
        {
            Resources.UnloadAsset(meshFilterList[i].sharedMesh);
        }
        //特效
        ParticleSystem[] particles = obj.GetComponents<ParticleSystem>();

        for (int i = 0, len1 = particles.Length; i < len1; i++)
        {
            Material[] mList = particles[i].GetComponent<Renderer>().sharedMaterials;
            for (int j = 0, len = mList.Length; j < len; ++j)
            {
                //Resources.UnloadAsset(mList[j]);
                //Log.info(mList[j].mainTexture.name);
                UnloadMaterialAOT(mList[j]);
                //Resources.UnloadAsset(mList[j].mainTexture);
            }
        }

        Animation[] animations = obj.GetComponents<Animation>();

        for (int i = 0, len1 = animations.Length; i < len1; i++)
        {
            foreach (AnimationState state in animations[i])
            {
                Resources.UnloadAsset(state.clip);
            }

        }
    }



    public static void ResetTransformValues(Transform to)
    {

        to.localScale = new Vector3(1, 1, 1);
        to.localRotation = Quaternion.identity;
        to.localPosition = new Vector3(0, 0, 0);

    }


    public static void CopyTransformValues(Transform from, Transform to)
    {
        //to.parent = from.parent;
        to.localScale = new Vector3(from.localScale.x, from.localScale.y, from.localScale.z);
        to.localRotation = Quaternion.Euler(from.localRotation.eulerAngles.x, from.localRotation.eulerAngles.y, from.localRotation.eulerAngles.z);
        to.localPosition = new Vector3(from.localPosition.x, from.localPosition.y, from.localPosition.z);

    }
    /// <summary>
    /// 遍历查找制定名称的孩子 只找一层
    /// </summary>
    public static GameObject SimpleFindChild(GameObject parent, string name, bool ignoreActive = false)
    {
        Transform trans = parent.transform;
        GameObject child = null;

        for (int i = 0; i < trans.childCount; i++)
        {
            child = trans.GetChild(i).gameObject;
            if (!ignoreActive && !child.activeSelf)
                continue;
            if (child.name == name)
                return child;
        }
        return null;

    }

    /// <summary>
    /// 遍历查找制定名称的孩子 只找一层
    /// </summary>
    //public static GameObject SimpleFindChildWithTag(GameObject parent, string tag, bool ignoreActive = false)
    //{
    //    Transform trans = parent.transform;
    //    GameObject child = null;

    //    for (int i = 0; i < trans.childCount; i++)
    //    {
    //        child = trans.GetChild(i).gameObject;
    //        if (!ignoreActive && !child.activeSelf)
    //            continue;
    //        if (child.tag == tag)
    //            return child;
    //    }
    //    return null;

    //}

    /// <summary>
    /// 遍历查找制定tag的孩子s 只找一层
    /// </summary>
    //public static List<GameObject> SimpleFindChildsWithTag(GameObject parent, string tag, bool ignoreActive = false)
    //{
    //    Transform trans = parent.transform;
    //    GameObject child = null;
    //    List<GameObject> childs = new List<GameObject>();
    //    for (int i = 0; i < trans.childCount; i++)
    //    {
    //        child = trans.GetChild(i).gameObject;
    //        if (!ignoreActive && !child.activeSelf)
    //            continue;
    //        if (child.tag == tag)
    //            childs.Add(child);
    //    }
    //    return childs;

    //}

    public static List<GameObject> SimpleFindChildsContainInfo(GameObject parent, string info, bool ignoreActive = false)
    {
        Transform trans = parent.transform;
        GameObject child = null;
        List<GameObject> childs = new List<GameObject>();
        for (int i = 0; i < trans.childCount; i++)
        {
            child = trans.GetChild(i).gameObject;
            if (!ignoreActive && !child.activeSelf)
                continue;
            if (child.name.Contains(info))
                childs.Add(child);
        }
        return childs;

    }



    //移出所有子对象
    public static void RemoveAllChildSafe(Transform transform)
    {
        for (int i = transform.childCount - 1; i >= 0; i--)
        {
            transform.GetChild(i).gameObject.SetActive(false);
            GameObject.Destroy(transform.GetChild(i).gameObject);
        }
    }

    //从一个 父级 替换到另外一个 
    public static void ReplaceAllChild(Transform fromParent, Transform toParent)
    {
        for (int i = fromParent.childCount - 1; i >= 0; i--)
        {
            fromParent.GetChild(i).parent = toParent;
        }
        //		while (0 < fromParent.childCount)
        //		{
        //			fromParent.GetChild (0).parent = toParent;
        //		}
    }
    //copy component
    public static T CopyComponent<T>(T original, GameObject destination) where T : Component
    {
        System.Type type = original.GetType();
        Component copy = destination.AddComponent<T>();
        System.Reflection.FieldInfo[] fields = type.GetFields();
        foreach (System.Reflection.FieldInfo field in fields)
        {
            field.SetValue(copy, field.GetValue(original));
        }
        return copy as T;
    }

    //copy component
    public static T CopyComponent<T>(T original, T destination) where T : Component
    {
        System.Type type = original.GetType();
        //		Component copy = destination.AddComponent(type);
        System.Reflection.FieldInfo[] fields = type.GetFields();
        foreach (System.Reflection.FieldInfo field in fields)
        {
            field.SetValue(destination, field.GetValue(original));
        }
        return destination;
    }

    /// <summary>
    /// 遍历查找制定名称的孩子
    /// </summary>
    public static GameObject FindChildDeep(GameObject parent, string name)
    {
        Transform trans = parent.transform;
        GameObject result = null;

        for (int i = 0; i < trans.childCount; i++)
        {
            GameObject child = trans.GetChild(i).gameObject;

            if (child.name == name)
                return child;
        }

        for (int i = 0; i < trans.childCount; i++)
        {
            GameObject child = trans.GetChild(i).gameObject;
            result = FindChildDeep(child, name);
            if (result != null)
                return result;
        }

        return null;
    }


    /// <summary>
    /// 返回所有子节点的集合
    /// </summary>
    //public static List<GameObject> GetAllChilds(GameObject parent)
    //{

    //    List<GameObject> allChilds = new List<GameObject>();
    //    Transform trans = parent.transform;

    //    for (int i = 0; i < trans.childCount; i++)
    //    {
    //        GameObject child = trans.GetChild(i).gameObject;

    //        allChilds.Add(child);
    //        allChilds.AddRange(GetAllChilds(child));
    //    }

    //    return allChilds;
    //}

    /// <summary>
    /// 获取用于TDR字符串长度，utf-8下，一个中文占3个长度
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static int GetStringTdrLength(string str)
    {
        string temp = str;
        int j = 0;
        for (int i = 0; i < temp.Length; i++)
        {
            if (Regex.IsMatch(temp.Substring(i, 1), @"[\u4e00-\u9fa5]+"))
            {
                j += 3;
            }
            else
            {
                j += 1;
            }
        }
        return j;
    }
    public static int GetStringLength(string str)
    {
        string temp = str;
        int j = 0;
        for (int i = 0; i < temp.Length; i++)
        {
            if (Regex.IsMatch(temp.Substring(i, 1), @"[\u4e00-\u9fa5]+"))
            {
                j += 2;
            }
            else
            {
                j += 1;
            }
        }
        return j;
    }

    public static void SetButtonState(GameObject button, bool enable)
    {
        if (null == button)
        {
            return;
        }
        BoxCollider box = button.GetComponent<BoxCollider>();
        if (null == box)
        {
            return;
        }
        box.enabled = enable;
    }

    //	var frustumHeight = 2.0f * distance * Mathf.Tan(camera.fieldOfView * 0.5f * Mathf.Deg2Rad);		
    //	var distance = frustumHeight * 0.5f / Mathf.Tan(camera.fieldOfView * 0.5f * Mathf.Deg2Rad);		
    //	var camera.fieldOfView = 2.0f * Mathf.Atan(frustumHeight * 0.5f / distance) * Mathf.Rad2Deg;
    //	var frustumWidth = frustumHeight * camera.aspect;
    //	var frustumHeight = frustumWidth / camera.aspect;
    /// <summary>
    /// 通过目标获取 在摄像机的 direcntion
    /// </summary>
    /// <returns>The camera left point.</returns>
    /// <param name="camera">Camera.</param>
    /// <param name="distance">Distance.</param>
    public static Vector3 GetCameraPoint(Camera camera, float distance, Vector3 direction)
    {

        float frustumH = 2.0f * distance * Mathf.Tan(camera.fieldOfView * 0.5f * Mathf.Deg2Rad);

        Vector3 tPoint = camera.transform.position + distance * camera.transform.forward + camera.transform.rotation * (direction * frustumH * camera.aspect / 2f);

        return tPoint;
    }

    public static float GetFrustumWidth(Camera camera, float distance)
    {
        float frustumH = 2.0f * distance * Mathf.Tan(camera.fieldOfView * 0.5f * Mathf.Deg2Rad);

        return frustumH * camera.aspect;
    }



    public static bool CheckMailAddress(string mailAddress)
    {
        Regex r = new Regex("^\\s*([A-Za-z0-9_-]+(\\.\\w+)*@(\\w+\\.)+\\w{2,5})\\s*$");
        if (r.IsMatch(mailAddress))
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    public static bool CheckUserName(string userName)
    {
        Regex r = new Regex("^[0-9]*$");
        if (r.IsMatch(userName))
        {
            return true;
        }
        else
        {
            return CheckMailAddress(userName);
        }
    }

    static int _fpsTime = 0;
    /// <summary>
    /// 获得真正运行时游戏时间（通过战斗逻辑帧计算）
    /// </summary>
    /// <returns></returns>
    public static int GetRealBattleTime()
    {
        if (_fpsTime == 0) _fpsTime = 1000 / (int)ActionCoreConfig.FPS;
        return _fpsTime * BattleCommonNet.GetBattleRunFrame();
    }
}
#endif