#if ACGGAME_CLIENT 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GameFramework.Common.FSM;
using Lucifer.ActCore;
using TDRConfig;
using UnityEngine;
using ZGameChess;
using AreaType = ZGameChess.AreaType;

namespace ACG.Core
{
    public class TransferState_Arrive_FlyIn_View : TransferEventHandleBase
    {
        protected override void OnCustom(ZGameClient.ACG_TCmdS2CTransferMsg e)
        {
            // 切换观察者的处理
            var battleModel = ChessModelManager.Instance.GetBattleModel();
            if (e.customEventName == "OnObserverChanged" && (int)e.param2 == battleModel.MyPlayerId)
            {
                var time = (int)e.param / 1000f;
                OnEnter(time);
            }
        }

        protected override void OnStateEnter(ZGameClient.ACG_TCmdS2CTransferMsg e)
        {
            OnEnter();
        }

        public void HidePlayers()
        {
            // 如果场上的玩家本来就在这个战场 就不需要从传送门出来，否则就需要
            ChessBattleGlobal.Instance.ChessPlayerCtrl.DoAction(HidePlayer, null);
        }

        private void HidePlayer(int playerId, ChessPlayerUnit chessPlayerUnit, object param)
        {
            // 他看的不是主场的 都需要隐藏
            var pm = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(playerId);
            var fightBattleIndex = GetFightBattleIndex();
            if (pm.observerId != fightBattleIndex
             && chessPlayerUnit.PlayerData != null && !chessPlayerUnit.PlayerData.isFake)
            {
                chessPlayerUnit.HideBody();
            }
        }

        private void OnEnter(float offsetTime = 0)
        {
            // 如果纯逻辑播放的话，可能这个ChessBattleGlobal.Instance.ChessPlayerCtrl是没有的
            if (ChessBattleGlobal.Instance.ChessPlayerCtrl == null)
            {
                return;
            }
            ACGEventManager.Instance.Send(EventType_BattleView.AutoChess_Battle_Hex_Info_Show_State, true);
            int index = GetTargetIndex();
            bool isMirrorPlayer = index == ChessBattleModel.MIRR_PLAYER_ID;

            // 隐藏英雄
            //FetchHeros(index, HideHero);
            // 隐藏不是当前战场的小队长
            HidePlayers();
            
            if (!isMirrorPlayer)
            {
                // 显示传送特效
                if (!ChessBattleGlobal.Instance.TransferEffectCtrl.IsTransferEffectVisible())
                    DisplayTransferEffect(index, EnemyTransferPos);
                SetWaitHeroVisible(index, true);
                SetOutFieldUnitVisible(index, true);
                ShowCounterEquipBox();
                // 不是当前战场的小队长从传送门飞过去
                AddDelayEvent(GetCorrectTime(0.4f) - offsetTime, PlayersFlyTo);
                //显示敌对tips
                //AddDelayEvent(0.6f - offsetTime, ShowCounterEquipTips);
                // 隐藏传送格子效果
                AddDelayEvent(GetCorrectTime(1.3f) - offsetTime, delegate ()
                {
                    DisplayTransferSound(GetTargetIndex(), (int)TranferSoundType.TransferEndSound);
                    FreeTransferEffect();
                });

                // 然后英雄变成光飞过去 然后显示格子光 然后显示英雄
                AddDelayEvent(GetCorrectTime(1.5f) - offsetTime, HerosFlyLightToGridBatch);
            }
            else
            {
                // 镜像没有等待区
                HideWaitHeros(index);

                DisplayHeros();
            }
            //AutoChessBattle_View.UpdateEnemyInterestView();
        }

        protected override void OnStateExit(ZGameClient.ACG_TCmdS2CTransferMsg e)
        {
            FreeTransferEffect();
        }

        public void HideWaitHeros(int playerId)
        {
            //var allBattleUnit = ChessBattleGlobal.Instance.BattleUnitService.GetAllBattleUnit();
            //for (int i = 0; i < allBattleUnit.Count; ++i)
            //{
            //    var unit = allBattleUnit[i];
            //    if (unit != null && unit.Data != null && unit.Data.areaType == AreaType.Wait && unit.Data.playerId == playerId)
            //    {
            //        unit.HideBody();
            //    }
            //}
        }

        private void PlayeFlyTo(int playerId, ChessPlayerUnit chessPlayerUnit, object param)
        {
            var playerModel = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(playerId);
            if (playerModel.IsDead())
                return;

            if (playerModel.observerId != GetFightBattleIndex())
            {
                chessPlayerUnit.ShowBody();
                ChessBattleGlobal.Instance.ChessPlayerCtrl.JumpFromImpl(chessPlayerUnit, EnemyTransferPos, 1.0f);
            }
        }

        private void PlayersFlyTo()
        {
            ChessBattleGlobal.Instance.ChessPlayerCtrl.DoAction(PlayeFlyTo, null);
            //更新魔法表情使用
            ACGEventManager.Instance.Send(EventType_UI.USE_MAGIC_EXPRESSION_STATUS);

            //客场小小英雄跳入棋盘触发
            var mapMgr = BattleMapManager.Instance;
            if (mapMgr != null)
            {
                var battleMap = mapMgr.GetCurrentBattleMap();
                if (battleMap != null)
                {
                    var triggerMgr = battleMap.GetBattleMapTrggierManager();
                    if (triggerMgr != null)
                    {
                        string littleEnmeyHero = GetEnemyUnitName();
                        triggerMgr.JumpToBattleMap(littleEnmeyHero);
                    }
                }
            }
        }
        private string GetEnemyUnitName()
        {
            string retname = string.Empty;
            if (ChessBattleGlobal.Instance.ChessPlayerCtrl != null)
            {
                PlayerModel curModel = ChessModelManager.Instance.GetBattleModel().GetCurPlayerModel();
                if(curModel != null && ChessBattleGlobal.Instance.ChessPlayerCtrl!=null)
                {
                    ChessPlayerUnit enemyUnit = ChessBattleGlobal.Instance.ChessPlayerCtrl.GetPlayerUnit(curModel.EnemyPlayerID);
                    if (enemyUnit != null)
                    {
                        retname = enemyUnit.model_name;
                    }
                }
            }
            return retname;
        }

        //private void HideHero(TransferEventHandleBase context, V_AutoChessBattleUnit unit)
        //{
        //    if (unit.HeroBc != null && unit.HeroBc.ShowInBattlefield)
        //    {

        //        ChessBattleUnit autoChessBattleUnit = unit.HeroBc.GetAutoChessBattleUnit();
        //        if (autoChessBattleUnit != null)
        //            autoChessBattleUnit.HideBody();
        //    }
        //}

        private void HerosFlyLightToGridBatch()
        {
            int index = GetTargetIndex();
            var playerCtrl = ChessBattleGlobal.Instance.ChessPlayerCtrl;
            if (playerCtrl == null)
                return;
            var playerUnit = playerCtrl.GetPlayerUnitAllCamp(index);
            bool showAnimation = playerUnit != null && playerUnit.PlayerData != null && !playerUnit.PlayerData.isFake;
           
            if (showAnimation)
            {
                OpoEquipFlyLightToGrid();
            }
            else
            {
                DisplayHeros();
            }
            //更新魔法表情使用
            ACGEventManager.Instance.Send(EventType_UI.USE_MAGIC_EXPRESSION_STATUS);
        }


        private void OpoEquipFlyLightToGrid()
        {

        }

        

        private void DisplayHeros()
        {
            int index = GetTargetIndex();
            DisplayOther();
        }

        private void DisplayOther()
        {
            ShowHomeEquipBox();
        }

        private void ShowHomeEquipBox()
        {

        }

        private void ShowCounterEquipBox()
        {

        }

        private void ShowCounterEquipTips()
        {
            ChessBattleModel battleModel = ChessModelManager.Instance.GetBattleModel();
            PlayerModel playerModel = battleModel.GetCurPlayerModel();
            if (playerModel.IsHomeCourt())
                return;
            //显示敌对装备列表
            //ChessBattleGlobal.Instance.ChessBattleScreen.AnimateOponentEquipmentTips(true, 1);
        }
    }
}

#endif