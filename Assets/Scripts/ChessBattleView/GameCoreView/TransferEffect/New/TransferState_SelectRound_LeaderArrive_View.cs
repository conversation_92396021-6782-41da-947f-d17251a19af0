#if ACGGAME_CLIENT 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Lucifer.ActCore;
using ZGameChess;

namespace ACG.Core
{
    public class TransferState_SelectRound_LeaderArrive_View : TransferEventHandleBase
    {
        protected override void OnCustom(ZGameClient.ACG_TCmdS2CTransferMsg e)
        {
            
        }

        protected override void OnStateEnter(ZGameClient.ACG_TCmdS2CTransferMsg e)
        {
            DisplayTransferSound(currentObserverIndex, (int)TranferSoundType.TransferTransSound);
        }

        protected override void OnStateExit(ZGameClient.ACG_TCmdS2CTransferMsg e)
        {
            //if (context.GetInt("TurnCount") != 1)
            //    RoundSelectGobal.Instance.roundSelectField.ShowUp();
            ChessBattleModel battleModel = ChessModelManager.Instance.GetBattleModel();
            DisplayTransferSound(battleModel.MyPlayerId, (int)TranferSoundType.TransferEndSound);
        }
    }
}

#endif