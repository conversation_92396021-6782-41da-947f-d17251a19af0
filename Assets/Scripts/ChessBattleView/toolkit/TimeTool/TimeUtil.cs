 
using ZGame;
#if ACGGAME_CLIENT
using UnityEngine;
using System.Collections;
using System;

public partial class TimeUtil
{
    public static DateTime startTime;

    public static long DateTimeToUnixTimestamp(DateTime dateTime)
    {
        var start = new DateTime(1970, 1, 1, 8, 0, 0, dateTime.Kind);
        return Convert.ToInt64((dateTime - start).TotalSeconds);
    }

    /// <summary>
    /// 东八区时间
    /// </summary>
    /// <returns></returns>
    public static DateTime dateTime_1970()
    {
        return new DateTime(1970, 1, 1, 8, 0, 0);
    }

    //seconds 服务器发过来的秒数
    public static DateTime serverTimeToMsTime(int seconds)
    {
        long serverTime = 10000000L * seconds + dateTime_1970().Ticks;
        return new DateTime(serverTime);
    }

    //商城通用，显示具体时间
    //大于30天：到期时间 **年*月*日
    //小于30天: 剩余 **天
    //小于24小时: 剩余 12:00:00
    /// <summary>
    /// 
    /// </summary>
    /// <param name="seconds"></param>
    /// <param name="type">1:>30day 2:>24h 3:<24h</param>
    /// <returns></returns>
    public static string GetCommonMallItemString(int seconds, out int type, int limitDay = 30)
    {
        type = -1;
        var span = seconds - GameUtil.ServerTimeSec;
        if (span < 0) return "";

        if (span > 30 * 24 * 3600)
        {
            type = 1;
            var t = serverTimeToMsTime(seconds);
            return string.Format(Localization.Trans("{0}年{1}月{2}日"), t.Year, t.Month, t.Day);
        }

        int d = span / (24 * 3600);
        int h = (span % (24 * 3600)) / 3600;
        int m = (span % 3600) / 60;
        int s = span % 60;
        if (span > 24 * 3600)
        {
            type = 2;
            return string.Format(Localization.Trans("{0}天{1}小时{2}分"), d, h, m);
        }

        type = 3;
        return string.Format(Localization.Trans("{0}时{1}分{2}秒"), h, m, s);
    }

    //商城通用，显示具体时间
    //大于30天：到期时间 **年*月*日
    //小于30天: 剩余 **天
    //小于24小时: 剩余 12:00:00
    /// <summary>
    /// Lua专用
    /// </summary>
    /// <param name="seconds"></param>
    /// <param name="type">1:>30day 2:>24h 3:<24h</param>
    /// <returns></returns>
    public static string GetCommonMallItemStringForLua(int seconds, out int type)
    {
        return GetCommonMallItemString(seconds, out type, 30);
    }

    /// <summary>
    /// 获取还剩余几天，向上取整
    /// </summary>
    /// <param name="seconds"></param>
    /// <returns></returns>
    public static int GetCommonMallDayRemain(int seconds)
    {
        var span = seconds - GameUtil.ServerTimeSec;
        if (span < 0) return 0;
        
        
        int d = Mathf.CeilToInt(span / (24 * 3600.0f));

        return d;

    }

    /// <summary>
    /// 2天23小时 23:24:25
    /// </summary>
    /// <param name="targetTime"></param>
    /// <param name="serverTime"></param>
    /// <returns></returns>
    public static string GetExperienceTime(long targetTime, long serverTime)
    {
        var span = targetTime - serverTime;
        if (span < 0)
            span = 0;
        if (span >= 86400)
        {
            var day = span / 86400;
            var hour = (span - day * 86400) / 3600;
            if (hour > 0)
            {
                return $"{day}天{hour}小时";
            }
            else
            {
                return $"{day}天";
            }
        }
        else
        {
            var hour = span / 3600;
            var min = (span - hour * 3600) / 60;
            var sec = span % 60;
            return $"{hour}:{min}:{sec}";
        }
    }

    /// <summary>
    /// 返回时间 11月13日 21:30
    /// </summary>
    /// <param name="seconds">服务器发过来的秒数</param>
    /// <returns></returns>
    public static string FormatTimeMMDD_HHMM(int seconds)
    {
        //var date = new DateTime(1970, 1, 1, 0, 0, 0);
        var date = dateTime_1970();
        date = new DateTime(10000000L * seconds + date.Ticks);

        return GetTimeMMDD_HHMMByDateTime(date);
    }
    
    /// <summary>
    /// 返回时间 11月13日 21:30
    /// </summary>
    /// <param name="seconds">服务器发过来的秒数</param>
    /// <returns></returns>
    public static string FormatTimeHHMM(int seconds)
    {
        //var date = new DateTime(1970, 1, 1, 0, 0, 0);
        var date = dateTime_1970();
        date = new DateTime(10000000L * seconds + date.Ticks);

        var hour = date.Hour;
        var minutes = date.Minute;

        string msg;
        
        msg = hour < 10 ? $"0{hour}" : $"{hour}";
        msg += minutes < 10 ? $":0{minutes}" : $":{minutes}";

        return msg;
    }

    public static string GetTimeMMDD_HHMMByDateTime(DateTime date)
    {
        string msg = Localization.Trans("{0}月{1}日  {2}:{3}");
        msg = string.Format(msg, date.Month, date.Day, date.Hour, date.Minute);

        return msg;
    }

    /// <summary>
    /// 程序执行时间测试
    /// </summary>
    /// <param name="dateBegin">开始时间</param>
    /// <param name="dateEnd">结束时间</param>
    /// <returns>返回(秒)单位，比如: 0.00239秒</returns>
    public static TimeSpan ExecDateDiff(DateTime dateBegin, DateTime dateEnd)
    {
        TimeSpan ts1 = new TimeSpan(dateBegin.Ticks);
        TimeSpan ts2 = new TimeSpan(dateEnd.Ticks);
        return ts1.Subtract(ts2).Duration();//计算时间差
        //返回天数、秒、还是年，取决你用什么属性，看下面的注释
        /*
         注:
        1.DateTime值类型代表了一个从公元0001年1月1日0点0分0秒到公元9999年12月31日23点59分59秒之间的具体日期时刻。因此，你可以用DateTime值类型来描述任何在想象范围之内的时间。一个DateTime值代表了一个具体的时刻
        2.TimeSpan值包含了许多属性与方法，用于访问或处理一个TimeSpan值
        下面的列表涵盖了其中的一部分：
        Add：与另一个TimeSpan值相加。 
        Days:返回用天数计算的TimeSpan值。 
        Duration:获取TimeSpan的绝对值。 
        Hours:返回用小时计算的TimeSpan值 
        Milliseconds:返回用毫秒计算的TimeSpan值。 
        Minutes:返回用分钟计算的TimeSpan值。 
        Negate:返回当前实例的相反数。 
        Seconds:返回用秒计算的TimeSpan值。 
        Subtract:从中减去另一个TimeSpan值。 
        Ticks:返回TimeSpan值的tick数。 
        TotalDays:返回TimeSpan值表示的天数。 
        TotalHours:返回TimeSpan值表示的小时数。 
        TotalMilliseconds:返回TimeSpan值表示的毫秒数。 
        TotalMinutes:返回TimeSpan值表示的分钟数。 
        TotalSeconds:返回TimeSpan值表示的秒数。  
         */
    }

    /// <summary>
    /// 时间 "hh:mm:ss"
    /// </summary>
    /// <param name="totalSeconds"></param>
    /// <param name="format"></param>
    /// <param name="isOnlyHour"></param>
    /// <returns></returns>
    public static string FormatTime(int totalSeconds, string format = "hh:mm:ss", bool isOnlyHour = true)
    {
        string time = format;

        int hour, minute, second;

        second = totalSeconds % 60;

        minute = (totalSeconds / 60) % 60;

        if (isOnlyHour)
        {
            hour = (totalSeconds / 60 / 60) % 24;
        }
        else
        {
            hour = totalSeconds / 60 / 60;
        }

        string hourStr = hour.ToString("00");
        string minuteStr = minute.ToString("00");
        string secondStr = second.ToString("00");

        // time = time.Replace("hh", hourStr);
        if (hourStr != "00")
        {
            time = time.Replace("hh", hourStr);
        }
        else
        {
            time = time.Replace("hh:", "");
        }

        time = time.Replace("mm", minuteStr);
        time = time.Replace("ss", secondStr);
        return time;
    }

    public static string FormatHHMMSS(int totalSeconds)
    {
        string format = "{0}:{1}:{2}";

        int hour, minute, second;

        second = totalSeconds % 60;
        minute = (totalSeconds / 60) % 60;
        hour = (totalSeconds / 60 / 60) % 24;

        string hourStr = hour > 0 ? hour.ToString() : "0" + hour;
        string minuteStr = minute > 0 ? minute.ToString() : "0" + minute;
        string secondStr = second > 0 ? second.ToString() : "0" + second;
        return string.Format(format, hourStr, minuteStr, secondStr);
    }


    /// <summary>
    /// 格式化时间 "mm:ss"
    /// </summary>
    /// <param name="totalSeconds"></param>
    /// <param name="format"></param>
    /// <returns></returns>
    public static string FormatTimeMMSS(int totalSeconds, string format = "mm:ss")
    {
        string time = format;

        int minute, second;

        second = totalSeconds % 60;
        minute = (totalSeconds / 60) % 60;

        string minuteStr = minute.ToString("00");
        string secondStr = second.ToString("00");

        time = time.Replace("mm", minuteStr);
        time = time.Replace("ss", secondStr);
        return time;
    }



    /// <summary>
    /// "1分钟内","分钟前","小时前","天前"
    /// </summary>
    /// <param name="time"></param>
    /// <returns></returns>
    public static string ReturnBackTimeDesc(double time)
    {
        
        string rs = "";
        if (time < 60)
        {
            rs = string.Format(Localization.Trans("{0}分钟内"), 1);
        }
        if (time < 3600 && time >= 60)
        {
            rs = string.Format(Localization.Trans("{0}分钟前"), (int)(time / 60)); 
        }
        if (time < (3600 * 24) && time >= 3600)
        {
            rs = string.Format(Localization.Trans("{0}小时前"), (int)(time / 3600));  
        }
        if (time >= (3600 * 24))
        {
            rs = string.Format(Localization.Trans("{0}天前"), (int)(time / (3600 * 24)));
        }
        return rs;
    }

    /// <summary>
    /// "分钟","小时","天"
    /// </summary>
    /// <param name="time"></param>
    /// <returns></returns>
    public static string FormateSimpleTime(int time)
    {
        string str;
        float diff = (float)time;
        if (diff < 60 * 60)
        {
            int before = (int)(diff / 60);
            str = string.Format(Localization.Trans("{0}分钟"), before);
        }
        else if (diff < 60 * 60 * 24)
        {
            int before = (int)(diff / (60 * 60));
            str = string.Format(Localization.Trans("{0}小时"), before);
        }
        else
        {
            int before = (int)(diff / (60 * 60 * 24));
            str = string.Format(Localization.Trans("{0}天"), before); 
        }
        return str;
    }

    /// <summary>
    ///  "天" ,"小时" , "分","秒"
    /// </summary>
    /// <param name="totalSeconds"></param>
    /// <returns></returns>
    public static string FormateSimpleTime2(int totalSeconds)
    {
        int hour, minute, second, day;
        second = totalSeconds % 60;
        minute = (totalSeconds / 60) % 60;
        hour = (totalSeconds / 60 / 60) % 24;
        day = totalSeconds / 60 / 60 / 24;

        if (day > 0)
        {
            return string.Format(Localization.Trans("{0}天{1}小时{2}分"), day, hour, minute);
        }
        else if (hour > 0)
        {
            return string.Format(Localization.Trans("{0}小时{1}分"), hour, minute); 
        }
        else if (minute > 0)
        {
            return string.Format(Localization.Trans("{0}分{1}秒"), minute, second);
        }
        else
        {
            return string.Format(Localization.Trans("{0}秒"), second); 
        }
    }

    public static string FormateSimpleTime3(int totalSeconds)
    {
        DateTime output = Converttimestamp(totalSeconds);
        string strResult = output.Year + "/" + output.Month + "/" + output.Day;
        return strResult;
    }

    //unix to c#
    public static DateTime Converttimestamp(double timestamp)
    {

        DateTime converted = new DateTime(1970, 1, 1, 0, 0, 0, 0);

        DateTime newdatetime = converted.AddSeconds(timestamp);

        return newdatetime.ToLocalTime();
    }
    //c# to unix
    public static double Converttotimestamp(DateTime value)
    {
        TimeSpan span = (value - new DateTime(1970, 1, 1, 0, 0, 0, 0));

        return (double)span.TotalSeconds;
    }

    //string格式有要求，必须是yyyy-MM-dd hh:mm:ss 其他格式需要补充
    public static DateTime ConvertStrToTime(string dateString)
    {
        return Convert.ToDateTime(dateString);
    }

    //获取星期几
    public static int DayOfWeak(DateTime date)
    {
        return Convert.ToInt32(date.DayOfWeek);
    }

    /// <summary>
    /// 把服务器的时间转换成字符串格式
    /// 例子：2021-6-5 10:45
    /// </summary>
    /// <param name="serverTime"></param>
    /// <returns></returns>
    public static string FormatServerTime_YYMMDD_MMSS(int serverTime)
    {
        var date = serverTimeToMsTime(serverTime);

        const string label = "{0}-{1}-{2} {3}:{4}";
        return string.Format(label, date.Year, date.Month, date.Day, date.Hour, date.Minute);
    }

    /// <summary>
    /// 把服务器的时间转换成字符串格式
    /// 例子：2021.6.5
    /// </summary>
    /// <param name="serverTime"></param>
    /// <returns></returns>
    public static string FormatServerTime_YYMMDD(int serverTime)
    {
        var date = serverTimeToMsTime(serverTime);

        const string label = "{0}.{1}.{2}";
        return string.Format(label, date.Year, date.Month, date.Day);
    }

    /// <summary>
    /// 检查时间是否有效
    /// </summary>
    /// <param name="seconds"></param>
    /// <returns></returns>
    public static bool CheckTimeBeyond(int seconds)
    {
        return seconds - GameUtil.ServerTimeSec >0;
    }
}

#endif