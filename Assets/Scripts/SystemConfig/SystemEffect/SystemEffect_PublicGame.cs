using System.Collections;
using System.Collections.Generic;

namespace SystemConfigDll
{
    public class SystemEffect_PublicGame
    {
        public static readonly string prefab__common__effectuiclick = "prefab/common/effectuiclick";
        public static readonly string art_tft_raw__effects__ui_effect__public_game = "art_tft_raw/effects/ui_effect/public_game";
        public static readonly string art_tft_raw__effects__ui_effect__public_game_config = "art_tft_raw/effects/ui_effect/public_game_config";
        public static readonly string art_tft_raw__effects__ui_effect = "art_tft_raw/effects/ui_effect";//UI分包需要拆分 //UI分包需要替换路径
        public static readonly string art_tft_raw__effects__ui_effect_config = "art_tft_raw/effects/ui_effect_config";//UI分包需要拆分 //UI分包需要替换路径
    }
}
