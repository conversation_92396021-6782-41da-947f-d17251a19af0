using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace SystemConfigDll
{
    public class SystemEffect_Lobby
    {
        public static readonly string art_tft_raw__effects__ui_effect__zhujiemian = "art_tft_raw/effects/ui_effect/zhujiemian";
        public static readonly string art_tft_raw__effects__ui_effect__zhujiemian_config = "art_tft_raw/effects/ui_effect/zhujiemian_config";
        public static readonly string art_tft_raw__effects__ui_effect__zhujiemian_new = "art_tft_raw/effects/ui_effect/zhujiemian_new";
        public static readonly string art_tft_raw__effects__ui_effect__zhujiemian_new_config = "art_tft_raw/effects/ui_effect/zhujiemian_new_config";
        public static readonly string art_tft_raw__effects__match_ui_effect = "art_tft_raw/effects/match_ui_effect";
        public static readonly string art_tft_raw__effects__match_ui_effect_config = "art_tft_raw/effects/match_ui_effect_config";
    }
}
