using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JKPackage
{
    public enum ResourceLOD
    {
        Default = -1,
        Normal = 0,
        Low = 1,
        SeaLow = 2,
    }

    public enum ResourceType
    {
        SingleAsset,        // 独立的资源，散图、视频、音频等
        Assetbundle,        // 工程里面的AB资源
    }

    public enum ResourceBuildType
    {
        InPackage = 0,                  // 包内资源
        LoginDownload = 1,              // COS资源 使用时手动下载
        GuideDownload = 2,              // COS资源 登陆时下载
        MainHallDownload = 3,           // COS资源 大厅后台下载
        MainHallManualDownload = 4,     // COS资源 大厅手动下载
        CustomDownload = 5,             // 自定义下载时机
        LangPackage = 6,                // 语言包，根据打包的时候选择的语言自动判断放在包内还是使用时下载
    }

    public enum DownloadRule
    {
        LoginDownload,      // 登陆时
        LobbyDownload,      // 大厅后台下载
        ManualDownload,     // 使用前边下边玩
    }

    public enum PackageType
    {
        Custom = 0,          // 由ResourceMatchRule指定的资源
        Item = 1,               // 道具资源
        Season = 4,             // 赛季资源
        Card = 7             // 魔典资源
    }

    public enum ItemResourceType
    {
        Icon = 0,               // 图标资源
        ShareImage = 1,         // 分享图资源
        OutGame = 2,            // 局外资源（如果是小小英雄，则会拆分局外资源和局内资源单独配置，如果是其他资源，则统一配置为局内资源。）
        InGame = 3,             // 局内资源
        LogicCfg = 4,           // 逻辑层数据
    }

    public enum EffectType
    {
        GlobalPublic = 0,   // 全局公共资源
        Login = 10,         // 登陆
        Lobby = 11,         // 大厅
        Outgame = 12,       // 局外所有系统
        Ingame = 13,        // 局内所有系统
        Guide = 14          // 新手引导
    }

    public enum GameplayType
    {
        Guide = 0,  // 新手引导
        Dual = 1,   // 双人模式
        Hundred = 2 // 百人玩法
    }

    /**
     * 使用语言地区代码作为枚举key;
     * ref: http://www.lingoes.net/zh/translator/langcode.htm
     *
     * 枚举中不能用-, 改为_
     */
    public enum RegionType
    {
        [EnumName("默认", "")]
        None = -1,
        [EnumName("国内", "")]
        zh_CN = 0,
        [EnumName("海外", "_OverSea")]
        OverSea = 1,
    }
}
