//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace JKPackage
//{
//    public class SeasonPackageConfigFactory
//    {
//        public static PackageConfig Create(int ruleId, int setId)
//        {
//            var platformName = AkBasePathGetter.GetPlatformName();
//            var setIdStr = setId.ToString();

//            PackageConfig config = new PackageConfig(ruleId, ResourceType.Assetbundle);
//            var data = JKPackageUtil.GetAssetbundles("SetBattleType.Public_Global");
//            List<string> assets = new List<string>();
//            for (int i = 0; i < data.Length; ++i)
//            {
//                PackageMatchRule.FindMatchingStringsInList(data[i], setIdStr, platformName, assets);
//            }
            
//            data = JKPackageUtil.GetAssetbundles("SetBattleType.Set");
//            if (data != null)
//            {
//                for (int i = 0; i < data.Length; ++i)
//                {
//                    PackageMatchRule.FindMatchingStringsInList(data[i], setIdStr, platformName, assets);
//                }
//            }

//            data = JKPackageUtil.GetAssetbundles($"SetBattleType.Set{setId}");
//            if (data != null)
//            {
//                for (int i = 0; i < data.Length; ++i)
//                {
//                    PackageMatchRule.FindMatchingStringsInList(data[i], setIdStr, platformName, assets);
//                }
//            }

//            config.AddAssets(assets);
//            return config;
//        }

//        //static string[] ProcessString(string[] strs, int setid)
//        //{
//        //    if (strs == null)
//        //        return null;

//        //    var newStrs = new string[strs.Length];
//        //    for (int i = 0; i < strs.Length; ++i)
//        //    {
//        //        var str = strs[i];
//        //        string pattern = "{setid}";
//        //        string replacement = setid.ToString();
//        //        newStrs[i] = str.Replace(pattern, replacement);
//        //    }
//        //    return newStrs;
//        //}
//    }
//}
