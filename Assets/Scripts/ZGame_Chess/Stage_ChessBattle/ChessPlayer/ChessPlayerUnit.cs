
#define Old_AsynAsset_Loading

using GameFramework.FMath;
using Lucifer.ActCore;
using System;
using System.Collections;
using System.Collections.Generic;
using TKFrame;
using TKPlugins;
using UnityEngine;
using ZGame;
using ZGame.Battle;
using ZGameClient;
using LogicFrameWork;
using Newtonsoft.Json;
using NTween;
using TriggerSystem;
using Coroutine = UnityEngine.Coroutine;
using ZGame.GameSystem;

namespace ZGameChess
{
    public class ChessPlayerData
    {
        public int ChessPlayerId;
#if UNITY_EDITOR
        public bool ManualSetBodyInEditor = false;
#endif
        //public int CurHpValue;
        //public int TotalHpValue;
        public string PlayerName;
        public int TinyId;
        public bool InBattle;
        public bool ForceLowModel = false;
        public bool LoadDefaultModelWhenNeedDownload = false;
        public bool isEnemy;
        public bool isFake;
        public bool isObserver;
        public bool isFrind;        // 是否是支援过来的
        public TAC_GameTinyData TinyData;
    }

    public enum BodyScaleChangeCondition
    {
        BATTLE_RESULT = 0, // 胜负结果一出就改变
        FIRST_DEDUCT = 1, // 扣血第一个子弹打到
        FINALLY_DEDUCT = 2, // 扣血所有子弹打完
    }

    public class ChessPlayerUnit : ChessBattleUnit
    {
        // 0 到 -100 是表现层的  -100以上是逻辑层的
        private const long HitEffectId = -2;        // 受击特效ID
        private const long RefreshShopEffectId = -3; //刷新商店特效ID
        private const long DeathEffectId = -4; //死亡特效ID
        private const long DoorEffectId = -5;  //传送门附带的特效
        private const long BuyExpCostLifeEffectId = -6;  //升级扣血的特效

        // 后台同步过来的动作
        protected readonly List<string> _animList = new List<string>()
        {
            "click_01",
            "click_02",
            "click_03",
            "click_04",
            "click_05",
            "click_06",
        };

        protected readonly List<string> _animLoopList = new List<string>()
        {
            "click_01_loop",
            "click_02_loop",
            "click_03_loop",
            "click_04_loop",
            "click_05_loop",
            "click_06_loop",
        };

        public static float[] ConWinScaleFactors { set; get; } = new float[]
        {
            1.0f,1.0f,1.2f,1.2f,1.4f,1.4f,1.4f,1.6f
        };

        private List<int> m_conWinEffectRules = new List<int>();

        public static string ConvertAssetbundleName(string assetName)
        {
            return ResPathProvider.ConvertLittleLegendABName(assetName);
        }

#if UNITY_EDITOR
        public static string ConvertAssetPath(string assetName)
        {
            string result = string.Empty;
            var arr = assetName.BeginSplit('_');
            if (arr.Length > 2)
            {
                if (arr.TryParse(2, out int level))
                {
                    //art_tft_raw/little_legend_res/model/t_aoshin/t_aoshin
                    result = string.Format("Assets/Art_TFT_Raw/little_legend_res/model/{0}_{1}/{0}_{1}/high/{2}.prefab", arr[0], arr[1], assetName);
                }
                else
                {
                    //art_tft_raw/little_legend_res/model/t_aoshin/t_aoshin_green
                    result = string.Format("Assets/Art_TFT_Raw/little_legend_res/model/{0}_{1}/{0}_{1}_{2}/high/{3}.prefab", arr[0], arr[1], arr[2], assetName);

                }
            }
            else
            {
                //art_tft_raw/little_legend_res/model/t_aoshin/t_aoshin
                result = string.Format("Assets/Art_TFT_Raw/little_legend_res/model/{0}/{0}/high/{0}.prefab", assetName);
            }
            arr.EndSplit();
            return result;
        }
#endif

        public static float initScale = -1.0f;

        public float Playerscale
        {
            get
            {
                {
                    return 1f;
                }
            }
        }
        public static bool transScale = true;


        private readonly int ShadowPlaneHash = Shader.PropertyToID("_ShadowPlane");
        private float m_PlayerHeight = 0.0f;

        private const string finale_failed_anim = "hurt_02";
        private const string hit_anim = "hurt_01";
        private const string jump_out_anim = "jump_out";

        private Transform m_selfTransform = null;
        private ChessPlayerEffectEvent m_effectEvent = null;
        private AnimEventReceiver _animEvtReceiver;
        //private ChessPlayerMount _mount;
        protected LittleLegendCfg _cfg;
        private Coroutine _bodyLoadCoroutine = null;
        private LittleLegendResLoader _loader = null;

        private string m_victoryAnimationHomeName;
        private string m_victoryAnimationAwayName;
        private float m_victoryAnimationHomeTime;
        private float m_victoryAnimationAwayTime;

        private ChessPlayerUnitHitConfig chessPlayerUnitHitConfig;
        public ChessPlayerUnitHitConfig ChessPlayerUnitHitConfig
        {
            get
            {
                if (chessPlayerUnitHitConfig == null)
                    chessPlayerUnitHitConfig = new ChessPlayerUnitHitConfig();
                return chessPlayerUnitHitConfig;
            }
        }

        protected ChessPlayerUnitInputHandler _inputHandler;
        public ChessPlayerUnitInputHandler InputHandler
        {
            get
            {
                return _inputHandler;
            }
        }

        protected BattleMapManager m_mapMgr = null;
        public BattleMapManager MapMgr
        {
            get
            {
                if (m_mapMgr == null)
                    return BattleMapManager.Instance;
                return m_mapMgr;
            }
            set
            {
                m_mapMgr = value;
            }
        }


        public Func<ChessPlayerUnit, int> GetBattleUnitSubtractFunctor { get; set; }

        public Action<ChessPlayerUnit> OnBodyLoadedCallback { get; set; }
        public ChessPlayerUnitInfo PlayerUnitInfo { get; set; }

        public float _curScaleFactor = 1.0f;
        protected Vector3 originBodyLocalScale;

        public ChessPlayerData PlayerData { get; set; }

        protected ViewGameObject m_viewGameObject = new ViewGameObject();


        protected ChessPlayerUnitAnimBehaviour _animBehaviour;
        public ChessPlayerUnitAnimBehaviour AnimBehaviour { get { return _animBehaviour; } }

        protected GameObject _footEffectGo;

        private int _hurtEffectId = 0;
        public bool _isCache = false;

        protected ActionStateController m_actionStateController;
        public ActionStateController ActionStateCtrl { get { return m_actionStateController; } }

        //private ChessPlayerEffectEvent effectEvent;
        public List<Coroutine> coroutines = new List<Coroutine>();
        public int allDmage = 0;

        private int _cacheQueueId = -1;

        protected List<EffectState> m_effectStates = null;
        public string castAbName = "scene_hero_bulletcast";
        public float casttime = 0.7f;

        // 小小队长缩放大小
        protected float m_heroScale = 1.0f;

        // 动态骨骼
        //private SpringManager m_springManager;

        public float heroScale
        {
            set { m_heroScale = value; }
        }

        public ChessPlayerUnitExpressionController ExpressionController { get; private set; }

        protected TriggerViewSystem m_triggerSystem;

        private BodyScaleChangeCondition m_BodyScaleChangeCondition = BodyScaleChangeCondition.FIRST_DEDUCT;

        private TinyAttackData m_attackData = null;

        private ChessPlayerUnitTriggerEffect m_triggerEffect = new ChessPlayerUnitTriggerEffect();

        public ChessPlayerUnitTriggerEffect TriggerEffect
        {
            get { return m_triggerEffect; }
        }

        private float m_minDis = 0.02f;

        protected bool m_isPre = false;

        protected bool m_isChangeModel = false;
        protected GameObject m_lastBody = null;

        public bool InDeathAni { get; protected set; } = false;

        public bool IgnoreJumpAction { get; set; }

        private HashSet<int> m_loopAnimationIds = new HashSet<int>();

        private string m_coinWinEffectPre = string.Empty;
        private GameObject m_conWinEffect = null;

        /// <summary>
        /// 是否在延迟显示的协程中
        /// </summary>
        public bool IsInDelayShowing
        {
            get;
            protected set;
        } = false;

        public void Init()
        {
            if (_inited)
                return;

            ACGEventManager.Instance.AddEventListener(EventType_BattleView.AutoChess_Battle_ChangeOBServerBattle, OnSwitchPlayer);

            chessPlayerUnitHitConfig = new ChessPlayerUnitHitConfig();
            chessPlayerUnitHitConfig.InitConfig();
            _action = gameObject.AddComponent<BaseAnimAction>();
            //_action.animChangeCallback = AnimChangeBack;
            //_moveHandler = gameObject.AddComponent<ChessBattleUnitMoveHandler>();
            _animatorHandler = gameObject.AddComponent<ChessBattleUnitAnimatorHandler>();
            _handlerList.Add(_animatorHandler);

            for (int i = 0; i < _handlerList.Count; i++)
            {
                _handlerList[i].Init(this);
            }

            var mgr = ChessPlayerConfigManager.Instance;
            ChessPlayerConfig playerConfig = null;
            if (mgr != null && PlayerData != null)
            {
                mgr.GetPlayerConfig(PlayerData.TinyId, (cfg) =>
                {
                    if (cfg != null)
                        SetConfig(cfg);
                    else
                        Diagnostic.Warn("Set Player Config Faild! " + PlayerData.TinyId);
                });
            }

            if (PlayerData != null && PlayerData.TinyData != null)
                _hurtEffectId = TinyAttackData.GetRealDamageId(PlayerData.TinyData.iDamageId, PlayerData.TinyData.iTinyId);

            m_actionStateController.AddState(PlayerMoveMessage.StartWalk, new StartWalkAction());
            m_actionStateController.AddState(PlayerMoveMessage.Walk, new WalkingAction());
            m_actionStateController.AddState(PlayerMoveMessage.StopWalk, new StopWalkAction());
            m_actionStateController.AddState(PlayerMoveMessage.StopWalk02, new StopWalk02Action());

            m_actionStateController.AddState(PlayerMoveMessage.StartRun, new StartRunAction());
            m_actionStateController.AddState(PlayerMoveMessage.Run, new RunningAction());
            m_actionStateController.AddState(PlayerMoveMessage.StopRun, new StopRunAction());
            m_actionStateController.AddState(PlayerMoveMessage.StopRun02, new StopRun02Action());

            m_actionStateController.AddState(PlayerMoveMessage.JumpTo, new JumpToAction());
            m_actionStateController.AddState(PlayerMoveMessage.JumpBack, new JumpBackAction());

            m_actionStateController.AddState(PlayerMoveMessage.Rush, new RushingAction());

            m_actionStateController.AddState(PlayerMoveMessage.PreStop, new IdleAction());
            m_actionStateController.Init(PlayerMoveMessage.PreStop);
            m_actionStateController.Start();

            _inputHandler = this.gameObject.AddComponent<ChessPlayerUnitInputHandler>();

            _curAngle = this.GetSelfTransform().rotation.eulerAngles.y;

            if (PlayerData != null && PlayerData.InBattle)
                BindBattleRoundModelMsg();
            _animBehaviour = this.gameObject.AddComponent<ChessPlayerUnitAnimBehaviour>();
            _animBehaviour.Init(this, m_actionStateController);
            _inited = true;

            if (_cacheQueueId != -1)
            {
                SetLogicQueue(_cacheQueueId);
                _cacheQueueId = -1;
            }

            ResetChessPlayerLogicConfig();

            // 加载胜利动画相关内容
            if (playerConfig != null)
            {
                m_victoryAnimationHomeName = playerConfig.m_victoryAnimationHomeName;
                m_victoryAnimationAwayName = playerConfig.m_victoryAnimationAwayName;
                m_victoryAnimationHomeTime = playerConfig.m_victoryAnimationHomeTime;
                m_victoryAnimationAwayTime = playerConfig.m_victoryAnimationAwayTime;
            }

            ExpressionController = new ChessPlayerUnitExpressionController(this);

            m_triggerEffect.SetPlayerUnit(this);

            InitCoinWinEffect();

            if (PlayerData != null
#if UNITY_EDITOR
                && !PlayerData.ManualSetBodyInEditor
#endif
                )
            {
                UpdateBody();
            }
        }

        #region TriggerSystem

        protected void InitTriggerSystem()
        {
            m_loopAnimationIds.Clear();
            m_triggerSystem = Body.GetComponent<TriggerViewSystem>();
            if (m_triggerSystem != null)
            {
                m_triggerSystem.data = this;

                string data = m_triggerSystem.GetTriggerParam<string>(TriggerEnum.LoopAnimationIds);

                if (!string.IsNullOrEmpty(data))
                {
                    string[] ids = data.Split('|');

                    foreach (string tempId in ids)
                    {
                        int actionId = -1;
                        if (int.TryParse(tempId, out actionId))
                        {
                            m_loopAnimationIds.Add(actionId);
                        }
                    }
                }
            }
        }

        public bool TriggerMoment(E_TRIGGER_MOMENT moment)
        {
            if (m_triggerSystem != null)
            {
                return m_triggerSystem.Execute(moment);
            }

            return false;
        }

        public void SetTriggerParam<T>(string paramName, T value)
        {
            if (m_triggerSystem != null)
                m_triggerSystem.SetTriggerParam(paramName, value);
        }

        public T GetTriggerParam<T>(string paramName)
        {
            if (m_triggerSystem != null)
                return m_triggerSystem.GetTriggerParam<T>(paramName);
            return default(T);
        }

        #endregion

        #region CFG设置

        public void ResetChessPlayerLogicConfig()
        {
            if (PlayerData == null)
                return;
            GetNewPlayerConfig(PlayerData.TinyId, (cp) =>
            {
                if (cp != null)
                {
                    castAbName = cp.CastAbName;
                    casttime = cp.CastFrame / 30f;
                }
            });
        }

        public void GetNewPlayerConfig(int tinyId, Action<ChessPlayerLogicConfig> onLoaded)
        {
            TACG_Item_Client itemCfg = DataBaseManager.Instance.SearchACGItem(tinyId);
            if (itemCfg != null && itemCfg.sTeamLeaderCfg != "")
            {
                LoadCfg(itemCfg.sTeamLeaderCfg + "_logic", onLoaded);
            }
        }

        public void LoadCfg(string file, Action<ChessPlayerLogicConfig> onLoaded)
        {
            ChessPlayerLogicConfig result = null;

            string assetBundle = ChessPlayerConfigManager.assetBundle;
            TKFrame.IAssetService assetService = TKFrame.Services.GetService<TKFrame.IAssetService>();
            assetService.LoadAsset<UnityEngine.TextAsset>(assetBundle + file, file, (asset, path) =>
            {
                if (asset != null)
                {
                    result = JsonConvert.DeserializeObject<ChessPlayerLogicConfig>(asset.text);
                    result.Init();

                    onLoaded?.Invoke(result);
                }
            });
        }

        #endregion

        private HashSet<string> m_allbankNames = new HashSet<string>();

        // 播放小队长一种配置的声音。
        //public void PlayerVoice(string voicePath)
        //{
        //    if (string.IsNullOrEmpty(voicePath))
        //        return;
        //    ModelPathUtil.SplitAssetPath(voicePath, out string bank_name, out string event_name);
        //    if (string.IsNullOrEmpty(bank_name) || string.IsNullOrEmpty(event_name))
        //        return;
        //    PlayWwiseBankByPath(bank_name, event_name, Body);
        //}

        public uint PlayWwiseBankByPath(string bank_name, string event_name, GameObject playGameObject,
            Action<string, uint> cb = null, AkCallbackManager.EventCallback eventCallBack = null)
        {
            m_allbankNames.Add(bank_name);
            return ChessUtil.PlayWwiseBankByPath(bank_name, event_name, playGameObject, cb, eventCallBack);
        }

        private void UnloadAllBankName()
        {
            if (m_allbankNames.Count == 0)
                return;

            foreach (string bankName in m_allbankNames)
            {
                //Chess_WwisePlayManager.instance.UnloadBNK(bankName);
            }

            m_allbankNames.Clear();
        }


        protected void InitCoinWinEffect()
        {

        }


        public override void ReleaseGameObj()
        {
            m_logicPos = Vector3.zero;
            m_isChangeModel = false;
            StopNewCor();
            ReleaseLogicQueue();
            UnBindBattleRoundModelMsg();
            //#if Old_AsynAsset_Loading
            //            _bodyAsset = null;
            //            _bodyLoadedCB = null;
            //#else
            //            if (m_model != null)
            //            {
            //                m_model.Release();
            //                m_model = null;
            //            }
            //#endif
            if (_bodyLoadCoroutine != null && SystemManager.getInstance() != null)
                SystemManager.getInstance().StopCoroutine(_bodyLoadCoroutine);

            m_effectStates = null;

            if (PlayerUnitInfo != null)
            {

                DoShowHeadInfo(false);

                PlayerUnitInfo = null;
            }

            PlayerData = null;
            if (gameObject)
                GameObject.Destroy(gameObject);


            if (ExpressionController != null)
            {
                ExpressionController.Release();
            }

            m_triggerEffect.Release();

            ReleaseFakeShadow();

            ClearConWinConfig();
        }

        #region 表现层

        //private bool _inHurt = false;       // 受击中
        //private float _hurtStartTime = 0.0f;
        // 表现层是否可移动
        /*public bool MoveEnable
        {
            get
            {
                return true;
            }
        }*/
        // 表现层是否可移动
        public bool MoveEnable
        {
            get
            {
                if (IsPlayingAnimStr(hit_anim))
                {
                    Diagnostic.Log("[Xiaobai] MoveEnable:FALSE hurt_01");
                    return false;
                }
                if (IsPlayingAnimStr(finale_failed_anim))
                {
                    Diagnostic.Log("[Xiaobai] MoveEnable:FALSE hurt_02");
                    return false;
                }
                if (IsPlayingAnimStr(jump_out_anim))
                {
                    Diagnostic.Log("[Xiaobai] MoveEnable:FALSE jump_out");
                    return false;
                }
                if (IsPlayingAnimStr("attack"))
                {
                    Diagnostic.Log("[Xiaobai] MoveEnable:FALSE attack");
                    return false;
                }
                if (!string.IsNullOrEmpty(baseAction.LockAnim))
                {
                    Diagnostic.Log("[Xiaobai] MoveEnable:FALSE baseAction.LockAnim: " + baseAction.LockAnim);
                    return false;
                }
                if (m_isChangeModel)
                {
                    Diagnostic.Log("[Xiaobai] MoveEnable:FALSE isChangeModel: " + m_isChangeModel);
                    return false;
                }

                return true;
            }
        }
        
        public bool IsChangeModel
        {
            get
            {
                return m_isChangeModel;
            }
        }

        protected Vector3 m_jumpDst;
        public Vector3 jumpDst
        {
            get
            {
                return m_jumpDst;
            }
            set
            {
                m_jumpDst = value;
            }
        }

        // 目标地点
        protected Vector3 m_dst;
        public Vector3 Dst
        {
            get
            {
                return m_dst;
            }
            set
            {
                //PrintLog("[Xiaobai!!][View]who set dst: " + value);
                m_dst = value;
            }
        }

        // 目标方向，只有二维的，也就是x和z，y为0
        protected Vector3 m_dir;
        public Vector3 Dir
        {
            get
            {
                return m_dir;
            }
        }

        // 速度
        protected float m_speed = 10f;
        public float speed
        {
            get
            {
                float realSpeed = m_speed;
                return realSpeed;
            }
        }

        // 跳跃时间
        private float m_jumpDuration;
        public float JumpDuration
        {
            get
            {
                return m_jumpDuration;
            }
        }
        // 跳跃动作
        private string m_jumpAnim;
        // 跳跃高度
        private float m_jumpHeight;

        private float m_lowestHeight;

        public float lowestHeight
        {
            get
            {
                return m_lowestHeight;
            }
        }

        public PlayerMoveMessage m_moveState
        {
            get;
            protected set;
        } = PlayerMoveMessage.Stop;

        public bool isNormalMoving
        {
            get
            {
                if (m_moveState == PlayerMoveMessage.Stop
                    || m_moveState == PlayerMoveMessage.JumpTo
                    || m_moveState == PlayerMoveMessage.JumpBack)
                    return false;
                else
                    return true;
            }
        }

        /// <summary>
        /// 能否变身
        /// </summary>
        public bool CanTransform
        {
            get
            {
                if (m_moveState == PlayerMoveMessage.Attack
                    || m_moveState == PlayerMoveMessage.Hit
                    || m_moveState == PlayerMoveMessage.WillHit
                    || m_moveState == PlayerMoveMessage.JumpTo
                    || m_moveState == PlayerMoveMessage.JumpBack)
                    return false;
                else
                    return true;
            }
        }

        public ChessPlayerConfig config
        {
            get;
            set;
        }

        protected float maxScale = float.MaxValue;
        protected float minScale = 0.1f;

        [System.Diagnostics.Conditional("DEBUG_LEVEL_LOG")]
        [System.Diagnostics.Conditional("DEBUG_LEVEL_WARN")]
        [System.Diagnostics.Conditional("DEBUG_LEVEL_ERROR")]
        public virtual void PrintLog(string msg)
        {
            if (PlayerData != null)
            {
                if (PlayerData.InBattle)
                {
                    const string logMsg = "View Chair:{0} cbp show : {1}";
                    Diagnostic.Log(logMsg, PlayerData.ChessPlayerId, msg);
                }
                else
                {
                    const string logMsg = "View OutBattle cbp : {0}";
                    Diagnostic.Log(logMsg, msg);
                }
            }
        }

        #region 初始化

        public void SetMoveMentState(PlayerMoveMessage state)
        {
            m_moveState = state;
            m_actionStateController.m_afterState = state;
        }

        public void SetLogicQueue(int queueId)
        {
            //if (!PlayerData.InBattle)
            //{
            //    return;
            //}

            if (!_inited)
            {
                _cacheQueueId = queueId;
                return;
            }

            if (m_viewGameObject.InitLogicQueue(queueId))
            {
                PrintLog("[xiaobai]Player Attach " + PlayerData.PlayerName + " Id " + PlayerData.ChessPlayerId + " queueId " + queueId);

                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.StartWalk, SetStartWalk);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.Walk, SetWalk);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.StopWalk, SetStopWalk);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.StopWalk02, SetShortStopWalk);

                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.StartRun, SetStartRun);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.Run, SetRun);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.StopRun, SetStopRun);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.StopRun02, SetShortStopRun);

                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.Rush, SetRush);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.Stop, SetStop);

                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.JumpBack, SetJumpBack);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.JumpTo, SetJumpTo);

                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.SimpleRun, SetSimpleRun);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.SimpleWalk, SetSimpleWalk);

                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.SetTarget, SetDst);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.CorrectPos, CorrectPos);

                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.ResetPlayer, ResetPlayerPostion);

                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.Rotate, SetRotation);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.PositionAndRotate, SetPositionAndRotation);

                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.PlayAnimation, PlayAnimation);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.StopAnimation, StopAnimation);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.PlayEffect, PlayEffect);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.StopEffect, StopEffect);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.PlayExpression, PlayExpression);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.Init, InitPrority);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.Teleport, Teleport);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.PlayAction, PlayAction);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.Attack, SetAttack);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.Hit, SetHit);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.SimpleHit, SetSimpleHit);
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.WillHit, SetWillHit);

#if UNITY_EDITOR
                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.UpdateConfig, SetConfig);
#endif

                m_viewGameObject.RegisterFunc((int)PlayerMoveMessage.TriggerAction, SetTriggerMoment);

                m_viewGameObject.updateAction = UpdateCallback;

                m_viewGameObject.UpdateQueue(); // 及时刷新一下位置
                m_viewGameObject.Reset();

                //RePlayAllEffect(logicPlayer.SoActions);
            }
            else
            {
                PrintLog("Player Attach " + PlayerData.PlayerName + " Id " + PlayerData.ChessPlayerId + " queueId failed ");
            }
        }

        private void ResetPlayerPostion(LogicMsg msg)
        {
            InitPlayerMsg initPlayerMsg = msg as InitPlayerMsg;
            if (initPlayerMsg != null)
            {
                SetTransform(initPlayerMsg.transform);
                SetInitMoveWay(initPlayerMsg.movingState);
            }
        }

        private void SetRotation(LogicMsg msg)
        {
            FQuaternionMsg quaternionMsg = msg as FQuaternionMsg;
            if (quaternionMsg != null)
            {
                if (quaternionMsg.withAnimation)
                {
                    FaceTo(Direction2Angle(quaternionMsg.value.eulerAngles.ToVector3()));
                }
                else
                {
                    SetRotation(quaternionMsg.value);
                }
            }
        }

        private void SetPositionAndRotation(LogicMsg msg)
        {
            FPositionAndQuaternionMsg positionQuaternionMsg = msg as FPositionAndQuaternionMsg;
            if (positionQuaternionMsg != null)
            {
                SetRotation(positionQuaternionMsg.rotationValue);
                SetPosition(positionQuaternionMsg.positionValue);
                FallToGround();

                var gfxRoots = GetComponentsInChildren<GfxFramework.GfxRoot_Unity>();
                foreach (var gfx in gfxRoots)
                {
                    gfx.gameObject.SetActive(false);
                    gfx.gameObject.SetActive(true);
                    //gfx.ResetTrails();
                }
            }
        }



        protected void PlayAnimation(LogicMsg msg)
        {
            SimpleCSoPlayAnimationMsg actionMsg = msg as SimpleCSoPlayAnimationMsg;
            if (actionMsg != null)
            {
                PrintLog("[xiaobai]PlayAnimation " + actionMsg.animationName);
                _animBehaviour.PlayAnim(actionMsg.animationName);
            }
        }


        protected void StopAnimation(LogicMsg msg)
        {
            SimpleCSoPlayAnimationMsg actionMsg = msg as SimpleCSoPlayAnimationMsg;
            if (actionMsg != null)
            {
                PrintLog("[xiaobai]Stop Animation " + actionMsg.animationName);
                StopAnim(actionMsg.animationName, false);
            }
        }

        private Dictionary<long, ChessPlayerEffectInfo> runEffectDict = new Dictionary<long, ChessPlayerEffectInfo>();

        protected void RePlayAllEffect(List<EffectState> effectStates)
        {
            if (effectStates != null)
            {
                List<long> stopList = new List<long>(runEffectDict.Keys);
                foreach (var id in stopList)
                {
                    StopEffect_Impl(id);
                }
                runEffectDict.Clear();

                for (int i = 0; i < effectStates.Count; ++i)
                {
                    var effectState = effectStates[i];
                    PlayEffect_Impl(effectState.id, effectState.effectName, effectState.hangPoint, E_SO_TRIGGER_ACTION_MODE.KEEP);
                }
            }
        }

        protected void PlayEffect(LogicMsg msg)
        {
            SimpleCSoPlayEffectMsg effectMsg = msg as SimpleCSoPlayEffectMsg;
            if (effectMsg != null)
            {
                PlayEffect_Impl(effectMsg.id, effectMsg.effectName, effectMsg.hangPoint, effectMsg.actionMode);
            }
        }

        public void PlayCostLifeEffect()
        {
            PlayEffect(ChessPlayerUnit.BuyExpCostLifeEffectId, "art_tft_raw/effects/battle/set/set7", "s7_hex_curelpact_hit", EffectHangPoint.Body, true);
        }

        /// <summary>
        /// 在小小英雄身上播一个会自动销毁的特效
        /// </summary>
        /// <param name="id"></param>
        /// <param name="assetbundle"></param>
        /// <param name="effectName"></param>
        /// <param name="hangPoint"></param>
        public void PlayEffect(long id, string assetbundle, string effectName, EffectHangPoint hangPoint, bool updatePosPerFrame, bool autoDestory = true, bool bindRotation = true, bool bindScale = false)
        {
            // 如果已经存在了同类型特效 则需要先清除
            if (runEffectDict.TryGetValue(id, out ChessPlayerEffectInfo effectInfo))
            {
                effectInfo.Destory();
            }

            //if (string.IsNullOrEmpty(effectName))
            //    return;

            // 查挂点
            Transform destTrans = GetHangPoint(this, (EffectHangPoint)hangPoint);
            if (destTrans == null)
                destTrans = this.GetSelfTransform();

            effectInfo = new ChessPlayerEffectInfo(id, assetbundle, effectName, autoDestory, destTrans, updatePosPerFrame, bindRotation, bindScale, OnEffectDestory);
            runEffectDict.Add(id, effectInfo);
            effectInfo.RunEffect(this);
        }

        public void PlayEffect(long id, string assetbundle, string effectName, EffectHangPoint hangPoint, Vector3 posOffset, Vector3 rotOffset)
        {
            // 如果已经存在了同类型特效 则需要先清除
            if (runEffectDict.TryGetValue(id, out ChessPlayerEffectInfo effectInfo))
            {
                effectInfo.Destory();
            }

            // 查挂点
            Transform destTrans = GetHangPoint(this, (EffectHangPoint)hangPoint);
            if (destTrans == null)
                destTrans = this.GetSelfTransform();

            effectInfo = new ChessPlayerEffectInfo(id, assetbundle, effectName, true, destTrans, true,
                posOffset, rotOffset, false, true, OnEffectDestory);
            runEffectDict.Add(id, effectInfo);
            effectInfo.RunEffect(this);
        }

        public void PlayEffect_Impl(long id, string effectName, int hangPoint, E_SO_TRIGGER_ACTION_MODE actionMode)
        {
            // 如果已经存在了同类型特效 则需要先清除
            if (runEffectDict.TryGetValue(id, out ChessPlayerEffectInfo effectInfo))
            {
                effectInfo.Destory();
            }

            // 查挂点
            Transform destTrans = GetHangPoint(this, (EffectHangPoint)hangPoint);
            if (destTrans == null)
                destTrans = this.GetSelfTransform();

            effectInfo = new ChessPlayerEffectInfo(id, effectName, actionMode != E_SO_TRIGGER_ACTION_MODE.KEEP, destTrans, OnEffectDestory);
            runEffectDict.Add(id, effectInfo);
            effectInfo.RunEffect(this);
        }

        public void AddRunEffect(long id, ChessPlayerEffectInfo info)
        {
            runEffectDict[id] = info;
        }

        public void OnEffectDestory(long id)
        {
            runEffectDict.Remove(id);
        }

        protected void StopEffect(LogicMsg msg)
        {
            SimpleCSoPlayEffectMsg actionMsg = msg as SimpleCSoPlayEffectMsg;
            if (actionMsg != null)
                StopEffect_Impl(actionMsg.id);
        }

        protected void StopEffect_Impl(long actionId)
        {
            if (runEffectDict.TryGetValue(actionId, out ChessPlayerEffectInfo effectInfo))
            {
                effectInfo.Destory();
            }
        }

        public bool HasEffectRun()
        {
            return runEffectDict.Count > 0;
        }

        protected void PlayExpression(LogicMsg msg)
        {
            PlayExpressionMsg expressionMsg = msg as PlayExpressionMsg;
            if (expressionMsg != null)
            {
                int expressionId = expressionMsg.expressId;
                PrintLog("[PlayExpression] Trigger " + expressionId);
                ExpressionController.ShowExpression(expressionId);
            }
        }

        private void Teleport(LogicMsg msg)
        {
            FVector3Msg fVector3Msg = msg as FVector3Msg;
            if (fVector3Msg != null && fVector3Msg.isNewValue)
            {
                FVector3 end = fVector3Msg.value;
                m_dst.x = end.x.ToSingle();
                m_dst.y = GetSelfTransform().localPosition.y;
                m_dst.z = end.z.ToSingle();
                m_dst.y = FallDown(m_dst);

                Vector3 dir = m_dst - GetSelfTransform().localPosition;
                FaceTo(Direction2Angle(dir));

                GetSelfTransform().localPosition = m_dst;
            }
        }

        private void PlayAction(LogicMsg msg)
        {
            PlayActionMsg playActionMsg = msg as PlayActionMsg;
            if (playActionMsg != null)
            {
                if (playActionMsg.actionId == -1)
                {
                    if (PlayerData.ChessPlayerId == ChessModelManager.Instance.GetBattleModel().MyPlayerId)
                        UIOverlay.Instance.ShowCommonTips(Localization.Trans("当前状态无法施放动作，请稍后再试"));
                    return;
                }

                if (m_moveState == PlayerMoveMessage.JumpTo
                    || m_moveState == PlayerMoveMessage.JumpBack)
                {
                    return;
                }

                // 及时播idle动作
                m_actionStateController.Update(Time.deltaTime);
                _animBehaviour.UpdateCallback();

                PlayAnim(playActionMsg.actionId);
            }
        }

        private bool CanPlayAttack()
        {
            if (PlayerData == null)
                return false;
            if (PlayerData.isObserver || PlayerData.isFrind)
                return false;
            return true;
        }

        protected void SetAttack(LogicMsg msg)
        {
            var attackMsg = msg as AttackMsg;
            if (attackMsg == null)
            {
                return;
            }

            if (!CanPlayAttack())
                return;

            if (attackMsg.m_inAttack)
            {
                m_attackData = attackMsg.ToAttackData();

                // 这里开始攻击 播放攻击动作（如果是发子弹和特效的话）
                PlayAttack((attackMsg.m_timeNow - attackMsg.m_startTime) / 1000f);
            }
            else
            {
                if (PlayerData.InBattle && attackMsg.m_isWin)
                {
                    // 这里继续播胜利动作
                    PlayWin();

                    // 胜利表现
                    PlayRoundWinEff();
                }

                if (m_attackData != null)
                {
#if UNITY_EDITOR
                    if (m_attackUnitEditor != null)
                        m_attackUnitEditor.LogicHit();
                    else
                    {
#endif
                        ChessBattleGlobal.Instance.ChessPlayerCtrl.chessViewAttackSet.LogicHit(m_attackData.m_attackCfgName, this);
#if UNITY_EDITOR
                    }
#endif
                    m_attackData.m_inAttack = false;
                }
            }
        }

        public void SetWillHit(LogicMsg msg)
        {
            if (!CanPlayAttack())
                return;

            if (m_attackData != null)
            {
#if UNITY_EDITOR
                if (m_attackUnitEditor != null)
                    m_attackUnitEditor.LogicWillHit();
                else
                {
#endif
                    ChessBattleGlobal.Instance.ChessPlayerCtrl.chessViewAttackSet.LogicWillHit(m_attackData.m_attackCfgName, this);
#if UNITY_EDITOR
                }
#endif
            }
        }

        public void SetSimpleHit(LogicMsg msg)
        {
            HitMsg hitMsg = msg as HitMsg;
            if (hitMsg == null)
            {
                return;
            }

            StopWinEff(hitMsg);
        }

        public void SetHit(LogicMsg msg)
        {
            HitMsg hitMsg = msg as HitMsg;
            if (hitMsg == null)
            {
                return;
            }

            // 被杀的消息不可忽视
            if (!hitMsg.willKill && !CanPlayAttack())
                return;

            OnHit(hitMsg);
        }

        protected void SetTransform(MovingTransform transform)
        {
            SetPosition(transform.pos);
            SetRotation(transform.rot);
            FallToGround();
        }

        public void CurveMove(AnimationCurve curve, float lastTime, float currentTime, float moveScale)
        {
            float lastDis = curve.Evaluate(lastTime);
            float currentDis = curve.Evaluate(currentTime);
            if (float.IsNaN(lastDis) || float.IsNaN(currentDis))
                return;

            float deltaDis = (currentDis - lastDis) * moveScale;

            //Diagnostic.Log("[View] CurveMove detla: " + deltaDis + " pos: " + transform.localPosition + " stepDistance: " + (Dst - transform.localPosition).magnitude + " stepDst: " + Dst + " moveScale: " + moveScale);

            //Vector3 deltaVec = Dir * deltaDis;

            MoveTo(/*deltaVec,*/ deltaDis);
            //transform.localPosition += deltaVec;
            //Debug.Log("[xiaobai view]lucky fuck deltaVec: " + deltaVec + " percent: " + percent + " pos: " + transform.localPosition);
            FallToGround();
        }

        public void SimpleMove(float moveSpeed, float deltaTime, float moveScale)
        {
            float moveDistance = moveSpeed * deltaTime * moveScale;
            //Vector3 delta = m_dir * moveDistance;
            //Diagnostic.Log("[View] SimpleMove detla: " + moveDistance + " pos: " + transform.localPosition + " speed: " + moveSpeed + " stepDistance: " + (Dst - transform.localPosition).magnitude + " stepDst: " + Dst + " moveScale: " + moveScale);
            MoveTo(/*delta,*/ moveDistance);

            //Diagnostic.Log(">>bai view speed -> " + moveSpeed + " deltaTime -> " + deltaTime + " moveScale -> " + moveScale + " pos -> " + transform.localPosition + " f: " + BattleCommonNet.Client_Frame);
        }

        // we have 8 small heros at most, so this value should be 8.
        private const int maximumCapacityForTransformList = 8;
        private readonly Vector4[] transformList = new Vector4[maximumCapacityForTransformList];
        private static int positionArrayID = 0;
        private static int positionsID = 0;

        private void SetShaderPosForHeros()
        {
            if (positionArrayID == 0 || positionsID == 0)
            {
                positionsID = Shader.PropertyToID("_Positions");
                positionArrayID = Shader.PropertyToID("_PositionArray");
            }

            // update player position in shader to interactive with scene effect.
            if (ChessBattleGlobal.Instance.ChessPlayerCtrl != null)
            {
                Array.Clear(transformList, 0, maximumCapacityForTransformList);
                int i = 0;

                foreach (var value in ChessBattleGlobal.Instance.ChessPlayerCtrl.ChessPlayerUnitDic.Values)
                {
                    if (value == null) continue;

                    if (i < maximumCapacityForTransformList)
                    {
                        transformList[i] = value.transform.position;
                        i++;
                    }
                }
                foreach (var value in ChessBattleGlobal.Instance.ChessPlayerCtrl.EnemyChessPlayerUnitDic.Values)
                {
                    if (value == null) continue;

                    if (i < maximumCapacityForTransformList)
                    {
                        transformList[i] = value.transform.position;
                        i++;
                    }
                }
                foreach (var value in ChessBattleGlobal.Instance.ChessPlayerCtrl.ObservedChessPlayerUnitDic.Values)
                {
                    if (value == null) continue;
                    if (i < maximumCapacityForTransformList)
                    {
                        transformList[i] = value.transform.position;
                        i++;
                    }
                }

                Shader.SetGlobalFloat(positionArrayID, maximumCapacityForTransformList);
                Shader.SetGlobalVectorArray(positionsID, transformList);
            }
        }

        protected bool m_isDir = false;

        private Vector3 m_lastDeltaPos = Vector3.zero;
        protected Vector3 m_logicPos = Vector3.zero;
        private bool m_needCorrectPos = false;

        private float kp = 0.3f;
        private float kd = 0.1f;

        private void MoveTo(/*Vector3 delta,*/ float maxDistance)
        {
            Transform selfTransform = GetSelfTransform();
            Vector3 nextPoint;
            if (m_isDir)
            {
                float maxDeltaLen = maxDistance * 0.2f;
                Vector3 deltaPos = m_logicPos - selfTransform.localPosition;
                Vector3 movePos = kp * deltaPos + kd * (deltaPos - m_lastDeltaPos);
                float movePosLen = movePos.magnitude;

                if (movePosLen > maxDeltaLen)
                {
                    movePos *= maxDeltaLen / movePosLen;
                }

                nextPoint = maxDistance * m_dir + movePos + selfTransform.localPosition;

                m_lastDeltaPos = deltaPos;
            }
            else
            {
                nextPoint = Vector3.MoveTowards(selfTransform.localPosition, Dst, maxDistance);
            }

            cacheFPos.x = nextPoint.x.ToFix64();
            cacheFPos.y = nextPoint.z.ToFix64();
            if (MapMgr != null && MapMgr.FindNearPosByMap(cacheFPos, Fix64.one, out FVector2 nearPos))
            {
                var sdfPath = MapMgr.GetSDFPath();
                if (sdfPath != null && sdfPath.Sample(ref nearPos) >= Fix64.one)
                {
                    Vector3 pos = selfTransform.localPosition;
                    pos.x = nearPos.x.ToSingle();
                    pos.z = nearPos.y.ToSingle();
                    selfTransform.localPosition = pos;
                }
            }
            else
            {
                selfTransform.localPosition = nextPoint;
            }

            if (m_isDir)
                CorrectPos();

            SetShaderPosForHeros();
        }

        protected void CorrectPos()
        {
            if (!m_needCorrectPos)
            {
                return;
            }
            m_needCorrectPos = false;

            //if (PlayerData.ChessPlayerId == 0)
            //   Diagnostic.Log("correct pos[apply]: " + m_logicPos);

            Transform selfTransform = GetSelfTransform();
            // Fix view pos error
            cacheFPos.x = selfTransform.localPosition.x.ToFix64();
            cacheFPos.y = selfTransform.localPosition.z.ToFix64();
            bool isReachable = MapMgr != null && MapMgr.IsReachable(cacheFPos, Fix64.one);
            if (!isReachable /*|| (m_logicPos - selfTransform.localPosition).magnitude >= 10*/)
            {  // 如果当前表现层的点不可到达
                selfTransform.localPosition = m_logicPos;
            }
        }

        public void MoveTo(Vector3 delta)
        {
            var nextPoint = GetSelfTransform().localPosition + delta;

            cacheFPos.x = nextPoint.x.ToFix64();
            cacheFPos.y = nextPoint.z.ToFix64();
            if (MapMgr != null && MapMgr.FindNearPosByMap(cacheFPos, Fix64.one, out FVector2 nearPos))
            {
                var sdfPath = MapMgr.GetSDFPath();
                if (sdfPath != null && sdfPath.Sample(ref nearPos) >= Fix64.one)
                {
                    Vector3 pos = GetSelfTransform().localPosition;
                    pos.x = nearPos.x.ToSingle();
                    pos.z = nearPos.y.ToSingle();
                    GetSelfTransform().localPosition = pos;
                }
            }
            else
            {
                GetSelfTransform().localPosition = nextPoint;
            }

            SetShaderPosForHeros();
        }

        protected void SetPosition(Vector3 pos)
        {
            Vector3 temp = GetSelfTransform().localPosition;
            temp.x = pos.x;
            temp.z = pos.z;
            GetSelfTransform().localPosition = temp;
        }

        protected void SetPosition(FVector3 pos)
        {
            Vector3 temp = GetSelfTransform().localPosition;
            temp.x = pos.x.ToSingle();
            temp.z = pos.z.ToSingle();
            GetSelfTransform().localPosition = temp;
        }

        protected void SetRotation(FQuaternion rot)
        {
            Quaternion tempRot = GetSelfTransform().rotation;
            tempRot.x = rot.x.ToSingle();
            tempRot.y = rot.y.ToSingle();
            tempRot.z = rot.z.ToSingle();
            tempRot.w = rot.w.ToSingle();
            GetSelfTransform().rotation = tempRot;
        }

        protected void SetInitMoveWay(MovingState movingState)
        {
            m_isDir = movingState.isDir;
            if (m_isDir)
                m_dir = movingState.value.ToVector3();
            else
                m_dst = movingState.value.ToVector3();

            _animBehaviour.Reset();

            PrintLog("Init Move " + movingState.moveState);
            switch (movingState.moveState)
            {
                case PlayerMoveMessage.SimpleRun:
                    SetRunStatus(movingState);
                    break;
                case PlayerMoveMessage.StartRun:
                    SetStartRunStatus(movingState);
                    break;
                case PlayerMoveMessage.Run:
                    SetRunStatus(movingState);
                    break;
                case PlayerMoveMessage.StopRun:
                    SetStopRunStatus(movingState);
                    break;
                case PlayerMoveMessage.SimpleWalk:
                    SetWalkStatus(movingState);
                    break;
                case PlayerMoveMessage.StartWalk:
                    SetStartWalkStatus(movingState);
                    break;
                case PlayerMoveMessage.Walk:
                    SetWalkStatus(movingState);
                    break;
                case PlayerMoveMessage.StopWalk:
                    SetStopWalkStatus(movingState);
                    break;
                case PlayerMoveMessage.Rush:
                    SetRushStatus(movingState);
                    break;
                case PlayerMoveMessage.Stop:
                    SetStopStataus(movingState);
                    break;
                default:
                    break;
            }
            m_actionStateController.moveScale = 1f;
        }

        #region 初始化各种状态

        protected virtual void InitPrority(LogicMsg msg)
        {
            InitPlayerMsg initMsg = msg as InitPlayerMsg;
            if (initMsg != null)
            {
                //m_viewGameObject.SetFrameCount(msg.frameCount);
                SetTransform(initMsg.transform);
                SetInitMoveWay(initMsg.movingState);
                if (Body != null)
                    RePlayAllEffect(initMsg.effectStates);
                else
                    m_effectStates = initMsg.effectStates;

                jumpDst = m_dst;
            }
        }

        private void SetStartRunStatus(MovingState movingState)
        {
            SetTarget(movingState.value, movingState.isDir);
            m_actionStateController.ChangeMoveStateNow(PlayerMoveMessage.StartRun);
            IActionState action = m_actionStateController.GetState(PlayerMoveMessage.StartRun);
            if (action != null)
                action.SyncState(movingState);
        }

        private void SetRunStatus(MovingState movingState)
        {
            SetTarget(movingState.value, movingState.isDir);
            // 正在攻击状态的话 用攻击的速度去替代
            //if (GetAttackSpeed(out float attackRunSpeed))
            //    m_speed = attackRunSpeed;
            //else
            m_speed = config != null ? config.runSpeed : 20;
            m_actionStateController.ChangeMoveStateNow(PlayerMoveMessage.Run);
        }

        private void SetStopRunStatus(MovingState movingState)
        {
            SetTarget(movingState.value, movingState.isDir);
            m_actionStateController.ChangeMoveStateNow(PlayerMoveMessage.StopRun);
            IActionState action = m_actionStateController.GetState(PlayerMoveMessage.StopRun);
            if (action != null)
                action.SyncState(movingState);
        }

        private void SetStartWalkStatus(MovingState movingState)
        {
            SetTarget(movingState.value, movingState.isDir);
            m_actionStateController.ChangeMoveStateNow(PlayerMoveMessage.StartWalk);
            IActionState action = m_actionStateController.GetState(PlayerMoveMessage.StartWalk);
            if (action != null)
                action.SyncState(movingState);
        }

        public void SetWalkStatus(MovingState movingState)
        {
            SetTarget(movingState.value, movingState.isDir);
            m_speed = config != null ? config.walkSpeed : 15;
            m_actionStateController.ChangeMoveStateNow(PlayerMoveMessage.Walk);
        }

        private void SetStopWalkStatus(MovingState movingState)
        {
            SetTarget(movingState.value, movingState.isDir);
            m_actionStateController.ChangeMoveStateNow(PlayerMoveMessage.StopWalk);
            IActionState action = m_actionStateController.GetState(PlayerMoveMessage.StopWalk);
            if (action != null)
                action.SyncState(movingState);
        }

        private void SetRushStatus(MovingState movingState)
        {
            SetTarget(movingState.value, movingState.isDir);
            m_actionStateController.ChangeMoveStateNow(PlayerMoveMessage.Rush);
            IActionState action = m_actionStateController.GetState(PlayerMoveMessage.Rush);
            if (action != null)
                action.SyncState(movingState);
        }

        private void SetStopStataus(MovingState movingState)
        {
            m_speed = 0;
            m_actionStateController.ChangeMoveStateNow(PlayerMoveMessage.PreStop);
        }

        #endregion

        #region 这三个只有表现层在做，逻辑层是直接赋值的，所以旋转只能在动画完成后，才能表现

        // 仅仅清空数据
        private void SetJumpTo(LogicMsg msg)
        {
            if (IgnoreJumpAction) return;

            FVector3Msg fVector3Msg = msg as FVector3Msg;
            if (fVector3Msg != null && fVector3Msg.isNewValue)
            {
                FVector3 end = fVector3Msg.value;
                Vector3 jumpBackDst = Vector3.zero;
                jumpBackDst.x = end.x.ToSingle();
                jumpBackDst.y = GetSelfTransform().localPosition.y;
                jumpBackDst.z = end.z.ToSingle();
                jumpBackDst.y = FallDown(jumpBackDst);
                this.jumpDst = jumpBackDst;
            }
            else
            {
                jumpDst = Dst;
            }

            PrintLog("[SetJumpTo]jumpDst: " + jumpDst);

            m_speed = 0;
            _animBehaviour.ClearDestination();
        }

        // 完完全全由表现层控制
        public void DoJumpTo(float time, float offset = 0, float height = -1)
        {
            if (IgnoreJumpAction) return;
            if (PlayerData.isFake) return;

            m_jumpDuration = time;
            m_jumpHeight = height;

            TriggerMoment(E_TRIGGER_MOMENT.ENTER_TRANSFER_GATE);

            if (m_actionStateController != null)
            {
                m_actionStateController.ChangeMoveStateNow(PlayerMoveMessage.JumpTo);
                if (offset > 0)
                {
                    var curState = m_actionStateController.CurrentState as JumpToAction;
                    if (curState != null)
                    {
                        curState.ResetStartTime(offset);
                    }
                }
            }

            JumpWithWings();
        }

        // 仅仅清空数据
        public void SetJumpBack(LogicMsg msg)
        {
            if (IgnoreJumpAction) return;

            FVector3Msg fVector3Msg = msg as FVector3Msg;
            if (fVector3Msg != null && fVector3Msg.isNewValue)
            {
                FVector3 end = fVector3Msg.value;
                Vector3 jumpBackDst = Vector3.zero;
                jumpBackDst.x = end.x.ToSingle();
                jumpBackDst.y = GetSelfTransform().localPosition.y;
                jumpBackDst.z = end.z.ToSingle();
                jumpBackDst.y = FallDown(jumpBackDst);
                this.jumpDst = jumpBackDst;
            }
            else
            {
                jumpDst = Dst;
            }

            PrintLog("[SetJumpBack]jumpDst: " + jumpDst);

            m_speed = 0;
            _animBehaviour.ClearDestination();
        }

        // 只有目的地由逻辑层控制
        public void DoJumpBack(float time, float height = -1)
        {
            if (IgnoreJumpAction) return;
            if (PlayerData == null || PlayerData.isFake) return;

            m_jumpDuration = time;
            m_jumpHeight = height;

            TriggerMoment(E_TRIGGER_MOMENT.OUT_TRANSFER_GATE);

            if (m_actionStateController != null)
                m_actionStateController.ChangeMoveStateNow(PlayerMoveMessage.JumpBack);

            JumpWithWings();
        }

        // 预览的时候用的 因为预览没有m_viewGameObject
        public void DoJump_Preview(string animName, float time, float height = -1)
        {
            //m_jumpDuration = time;
            //m_jumpAnim = animName;
            //m_jumpHeight = height;

            //Jumping(Vector3.zero);
        }

        public virtual void JumpFinish(bool interrupt)
        {
            // 朝向中心点 (ActionEnd里面会做 这里屏蔽掉)
            if (!interrupt)
            {
                var pos = new Vector3(0, GetSelfTransform().localPosition.y, 0);
                Vector3 dir = pos - GetSelfTransform().localPosition;
                //transform.rotation = Quaternion.LookRotation(dir);
                //Angle = transform.eulerAngles.y;
                FaceTo(Direction2Angle(dir));
            }

            FallToGround();
            StopEffect_Impl(DoorEffectId);
        }

        private void JumpWithWings()
        {
            int currentEffectId = ChessBattleGlobal.Instance.TransferEffectCtrl.DoorId;
            bool isContinue = false;
            if (PlayerData.TinyData != null)
            {
                if (!PlayerData.InBattle)
                {
                    currentEffectId = PlayerData.TinyData.iDoorId;
                    isContinue = true;
                }
                else
                {
                    if (PlayerData.TinyData != null && currentEffectId == PlayerData.TinyData.iDoorId)
                        isContinue = true;
                }
            }

            if (isContinue)
            {
                string abName = GetEffectAB(currentEffectId);
                string assetName = GetEffectName(currentEffectId);

                if (!string.IsNullOrEmpty(abName) && !string.IsNullOrEmpty(assetName))
                {
                    Vector3 start = GetSelfTransform().localPosition;
                    Vector3 dir = jumpDst - start;
                    dir.y = 0.0f;

                    float angle = Vector3.Angle(Vector3.back, dir);
                    if (dir.x > 0)
                        angle = -angle;

                    angle += 180.0f;

                    PlayEffect(DoorEffectId, abName, assetName, EffectHangPoint.Ground, Vector3.zero, new Vector3(0, angle, 0));
                }

            }
        }

        private string GetEffectAB(int effectId)
        {
            string abName = string.Empty;

            return abName;
        }

        private string GetEffectName(int effectId)
        {
            string assetName = string.Empty;
            return assetName;
        }

        #endregion

#if UNITY_EDITOR
        private void SetConfig(LogicMsg msg)
        {
            PlayerConfigMsg configMsg = msg as PlayerConfigMsg;
            if (string.IsNullOrEmpty(configMsg.configName))
            {
                ChessPlayerConfigManager.Instance.GetNewPlayerConfig(PlayerData.TinyId, (cfg) =>
                {
                    config = cfg;

                    _rotSpeed = config != null ? config.rotateSpeed : 5;
                    _configSpeed = _rotSpeed;
                    //Body.RemoveComponent<ChessPlayerEffectEvent>();
                    //m_effectEvent = Body.AddComponent<ChessPlayerEffectEvent>();
                    if (m_effectEvent != null)
                        m_effectEvent.Initialize(this, config);
                });
            }
            else
            {
                ChessPlayerConfigManager.Instance.LoadCfg(configMsg.configName, (cfg) =>
                {
                    config = cfg;

                    _rotSpeed = config != null ? config.rotateSpeed : 5;
                    _configSpeed = _rotSpeed;
                    //Body.RemoveComponent<ChessPlayerEffectEvent>();
                    //m_effectEvent = Body.AddComponent<ChessPlayerEffectEvent>();
                    if (m_effectEvent != null)
                        m_effectEvent.Initialize(this, config);
                });
            }

            m_actionStateController.ChangeMoveStateNow(PlayerMoveMessage.Stop);
        }
#endif

        public void SetConfig(ChessPlayerConfig playerConfig)
        {
            config = playerConfig;
            _rotSpeed = config.rotateSpeed;
            _configSpeed = _rotSpeed;
            switch (playerConfig.BodyScaleChangePlan)
            {
                case 0:
                case 1:
                case 2:
                    m_BodyScaleChangeCondition = (BodyScaleChangeCondition)playerConfig.BodyScaleChangePlan;
                    break;
            }
        }

        private void SetTriggerMoment(LogicMsg msg)
        {
            TriggerActionMsg triggerActionMsg = msg as TriggerActionMsg;
            if (triggerActionMsg != null)
            {
                TriggerMoment(triggerActionMsg.value);
            }
        }

        #region 跑步流程

        private void SetStartRun(LogicMsg msg)
        {
            m_actionStateController.ChangeMoveState(PlayerMoveMessage.StartRun);
        }

        private void SetRun(LogicMsg msg)
        {
            m_speed = config != null ? config.runSpeed : 20;
            m_actionStateController.ChangeMoveState(PlayerMoveMessage.Run);
        }

        private void SetSimpleRun(LogicMsg msg)
        {
            // 正在攻击状态的话 用攻击的速度去替代
            if (GetAttackSpeed(out float attackRunSpeed))
                m_speed = attackRunSpeed;
            else
                m_speed = config != null ? config.runSpeed : 20;

            m_actionStateController.ChangeMoveState(PlayerMoveMessage.Run);
        }

        private void SetStopRun(LogicMsg msg)
        {
            m_actionStateController.ChangeMoveState(PlayerMoveMessage.StopRun);
        }

        private void SetShortStopRun(LogicMsg msg)
        {
            m_actionStateController.ChangeMoveState(PlayerMoveMessage.StopRun02);
        }
        #endregion

        #region 走路流程

        public void SetStartWalk(LogicMsg msg)
        {
            m_actionStateController.ChangeMoveState(PlayerMoveMessage.StartWalk);
        }

        public void SetWalk(LogicMsg msg)
        {
            m_speed = config != null ? config.walkSpeed : 15;
            m_actionStateController.ChangeMoveState(PlayerMoveMessage.Walk);
        }

        public void SetSimpleWalk(LogicMsg msg)
        {
            m_speed = config != null ? config.walkSpeed : 15;
            m_actionStateController.ChangeMoveState(PlayerMoveMessage.Walk);
        }

        protected void SetStopWalk(LogicMsg msg)
        {
            m_actionStateController.ChangeMoveState(PlayerMoveMessage.StopWalk);
        }

        protected void SetShortStopWalk(LogicMsg msg)
        {
            m_actionStateController.ChangeMoveState(PlayerMoveMessage.StopWalk02);
        }
        #endregion

        private void SetRush(LogicMsg msg)
        {
            _animBehaviour._lastFrameActState = null;
            if (m_moveState == PlayerMoveMessage.Rush)
            {
                IActionState actionState = m_actionStateController.GetState(PlayerMoveMessage.Rush);
                if (actionState != null)
                    actionState.End(m_actionStateController);
            }
            m_actionStateController.ChangeMoveState(PlayerMoveMessage.Rush);
        }

        protected void SetStop(LogicMsg msg)
        {
            FVector3Msg fVector3Msg = msg as FVector3Msg;
            if (fVector3Msg != null && fVector3Msg.isNewValue)
            {
                FVector3 end = fVector3Msg.value;
                m_dst.x = end.x.ToSingle();
                m_dst.y = GetSelfTransform().localPosition.y;
                m_dst.z = end.z.ToSingle();

                m_dst.y = FallDown(m_dst);
            }

            m_actionStateController.ChangeMoveState(PlayerMoveMessage.PreStop);
        }

        #region 设置目标终点或者方向

        protected void CorrectPos(LogicMsg msg)
        {
            FVector3Msg fVector3Msg = msg as FVector3Msg;
            if (fVector3Msg != null)
            {
                Vector3 pos = fVector3Msg.value.ToVector3();
                pos.y = GetSelfTransform().localPosition.y;
                pos.y = FallDown(pos);
                m_logicPos = pos;
                m_needCorrectPos = true;
                //if (PlayerData.ChessPlayerId == 0)
                //    Diagnostic.Log("correct pos: " + m_logicPos);

                if (!fVector3Msg.isDir)
                    CorrectPos();
            }
        }

        protected void SetDst(LogicMsg msg)
        {
            FVector3Msg fVector3Msg = msg as FVector3Msg;
            if (fVector3Msg != null)
            {
                SetTarget(fVector3Msg.value, fVector3Msg.isDir);
                SetScale(fVector3Msg);
            }
        }

        protected virtual void SetTarget(FVector3 value, bool isDir)
        {
            m_isDir = isDir;
            if (m_isDir)
            {
                SetTargetByDir(value, m_isPre);
            }
            else
            {
                SetTargetByPos(value);
            }
        }

        private void SetTargetByDir(FVector3 dir, bool isPrev)
        {
            if (!isPrev)
            {
                m_dir = dir.ToVector3();
                float angle = Quaternion.LookRotation(m_dir).eulerAngles.y;
                FaceTo(angle);
            }

            _animBehaviour.SetDestination();
        }

        private void SetTargetByPos(FVector3 end)
        {
            m_dst.x = end.x.ToSingle();
            m_dst.y = GetSelfTransform().localPosition.y;
            m_dst.z = end.z.ToSingle();
            m_dst.y = FallDown(m_dst);
            _animBehaviour.SetDestination();

            Vector3 start = GetSelfTransform().localPosition;
            Vector3 delta = m_dst - start;
            delta.y = 0;
            if (delta.magnitude < m_minDis)
            {
                //PrintLog("[SetTarget] delta: " + delta + " m_dst:" + m_dst + " start: " + start);
                m_dst = GetSelfTransform().localPosition;
            }

            m_dir = delta.normalized;
            if (m_dir.Equals(Vector3.zero) == false)
            {
                float angle = Quaternion.LookRotation(m_dir).eulerAngles.y;
                FaceTo(angle);
            }

            //TKFrame.Diagnostic.Log(">>bai view set target -> " + m_dst + " pos: " + transform.localPosition + " f: " + BattleCommonNet.Client_Frame);
        }

        #endregion

        #region 设置移动尺度

        private void SetScale(FVector3Msg msg)
        {
            if (msg.isDir)
            {
                SetScaleByDir();
            }
            else
            {
                SetScaleByPos(msg.distance);
            }
        }

        private void SetScaleByDir()
        {
            m_actionStateController.moveScale = 1.0f;
        }

        protected void SetScaleByPos(Fix64 dis)
        {
            float scale = 1;
            if (dis < Fix64.maxValue && dis > Fix64.zero)
            {
                float logicDis = dis.ToSingle();
                Vector3 start = GetSelfTransform().localPosition;
                Vector3 delta = m_dst - start;
                delta.y = 0;
                if (delta == Vector3.zero)
                {
                    scale = 0;
                }
                else
                {
                    scale = delta.magnitude / logicDis;

                    //PrintLog("scale transform " + transform.localPosition + " scale  " + scale + " dst " + m_dst);
                    //PrintLog("scale dis " + logicDis + " delta " + delta.magnitude);
                }
            }
            else if (dis <= Fix64.zero)
            {
                scale = 0;
            }

            m_actionStateController.moveScale = scale;

            //TKFrame.Diagnostic.Log(">>bai view set target -> " + m_dst + " pos: " + transform.localPosition + " scale: " + scale + " f: " + BattleCommonNet.Client_Frame);
        }

        #endregion

        #endregion

        #region update移动

        private string m_lastTrigger = string.Empty;
        protected virtual void UpdateCallback(float deltaTime)
        {
            bool isStopState = m_moveState == PlayerMoveMessage.Stop;
            if (_animBehaviour == null) throw new ArgumentNullException(nameof(_animBehaviour));

            if (!isStopState)
            {
                if (isNormalMoving)
                {
                    float distSqrt = Vector3.SqrMagnitude(m_dst - GetSelfTransform().position);
                    if (distSqrt < (5.80f * this.GetSelfTransform().localScale.x))
                    {
                        _animBehaviour.IsArriving = true;
                    }
                }
            }

            if (m_actionStateController != null)
            {
                m_actionStateController.Update(deltaTime);
            }

            //if (PlayerData.InBattle)
            //if (hasCilloder || !isStopState)
            //    ClampPosition();

            _animBehaviour.UpdateCallback();

            if (!isStopState)
            {
                ExecuteRegionTrigger();
            }

            _animBehaviour.IsMoving = isNormalMoving;
            _animBehaviour.MSpeed = m_speed;

            if (m_PlayerHeight != GetSelfTransform().position.y)
            {
                m_PlayerHeight = GetSelfTransform().position.y;
                AutoSetShadowPlane();
            }
        }

        private void ExecuteRegionTrigger()
        {
            ChessBattleModel battleModel = ChessModelManager.Instance.GetBattleModel();
            PlayerModel playerModel = battleModel.GetPlayerModel(PlayerData.ChessPlayerId);

            if (playerModel.PlayerId != battleModel.CurrentPlayerId)
            {
                //目前棋盘的触发器，都是自己才能触发
                return;
            }

            bool isHomeCourt = playerModel.IsHomeCourt();

            Transform cacheTransform = GetSelfTransform();
            Vector3 pos = cacheTransform.position;
            cacheFPos.x = pos.x.ToFix64();
            cacheFPos.y = pos.z.ToFix64();
            if (MapMgr == null)
                return;

            string curTrigger = MapMgr.GetCurTrigger(cacheFPos, Fix64._05);
            if (m_lastTrigger == curTrigger)
                return;

            BattleMap battleMap = MapMgr.GetCurrentBattleMap();
            if (battleMap == null)
                return;

            BattleMapTriggerManager triggerMgr = battleMap.GetBattleMapTrggierManager();
            if (triggerMgr == null)
                return;

            if (m_lastTrigger.Filled())
            {
                // 上一个Trigger退出的事件
                triggerMgr.TriggerExit(model_name, m_lastTrigger, isHomeCourt, battleModel.MyPlayerId == playerModel.PlayerId);
            }

            m_lastTrigger = curTrigger;

            if (m_lastTrigger.Filled())
            {
                // 下一个Trigger进入的事件
                triggerMgr.TriggerEnter(model_name, m_lastTrigger, isHomeCourt, battleModel.MyPlayerId == playerModel.PlayerId);
            }
        }

        public void ClampPositionAndFallDown()
        {
            //ClampPosition();
            FallToGround();
        }

        protected virtual bool InSideField(ref FVector2 pos)
        {
            ChessBattleLogicField.StaticField ins = ChessBattleLogicField.StaticField.Ins;
            if (pos.x > ins.rightUp.x
                || pos.x < ins.leftDown.x
                || pos.y > ins.rightUp.y
                || pos.y < ins.leftDown.y)
            {
                pos.x = Fix64.Clamp(pos.x, ins.leftDown.x, ins.rightUp.x);
                pos.y = Fix64.Clamp(pos.y, ins.leftDown.y, ins.rightUp.y);
                return true;
            }
            return false;
        }

        FVector2 cacheFPos;
        FVector2 cacheFNearPos;
        bool hasCilloder = false;
        public bool CanSetPosition(Vector3 pos)
        {
            cacheFPos.x = pos.x.ToFix64();
            cacheFPos.y = pos.z.ToFix64();
            if (MapMgr != null && MapMgr.FindNearPosByMap(cacheFPos, Fix64._05, out cacheFNearPos))
            {
                return false;
            }
            else
            {
                return true;
            }
        }

        public override void ResetMat()
        {
            base.ResetMat();

            AutoSetShadowPlane();
        }

        private void AutoSetShadowPlane()
        {
            // 自动设置影子高度
            for (int i = 0; i < AllMaterials.Count; ++i)
            {
                var material = AllMaterials[i];
                if (!material.HasProperty(ShadowPlaneHash))
                    continue;
                var vector = material.GetVector(ShadowPlaneHash);
                if (vector.w != GetSelfTransform().position.y)
                {
                    vector.w = GetSelfTransform().position.y;
                    material.SetVector(ShadowPlaneHash, vector);
                }
            }
        }

        //public void Jumping(Vector3 dst)
        //{
        //    JumpTo(dst, m_jumpDuration, m_jumpAnim, m_jumpHeight);
        //}
        #endregion

        public void ReleaseLogicQueue()
        {

            if (PlayerData != null)
            {
                PrintLog("deatch " + PlayerData.PlayerName + " id " + PlayerData.ChessPlayerId + " queueId " + m_viewGameObject.queueId);
            }

            m_moveState = PlayerMoveMessage.Stop;
            m_viewGameObject.ClearLogicQueue();
        }

        #endregion

        #region 攻击表现

        protected void PlayLastHurtAction()
        {
            if (_animBehaviour != null)
            {
                var pm = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(PlayerData.ChessPlayerId);
                var playerCtrl = ChessBattleGlobal.Instance.ChessPlayerCtrl;
                var attacker = playerCtrl == null ? null : playerCtrl.GetPlayerUnitAllCamp(pm.EnemyPlayerID);
                //受击者转向  非野怪要面向敌方小小英雄 
                if (attacker != null && attacker.GetSpineNodeFromSocket() != null)
                {
                    Vector3 attackDir = attacker.GetSpineNodeFromSocket().position - GetSelfTransform().position;
                    if (!this._animBehaviour.IsMoving)
                        FaceTo(Direction2Angle(attackDir));
                    _animBehaviour.PlayHurtAction(true, -attackDir);
                }
                else
                {
                    // 野怪就按当前方向播放受击hurt01
                    Vector3 attackDir = this.GetSelfTransform().forward;
                    _animBehaviour.PlayHurtAction(false, -attackDir);
                }
            }
        }

        public void DeductLife(HitMsg hitMsg)
        {

        }

        protected void PlayHitEffect(HitMsg hitMsg)
        {
            if (!hitMsg.isPlayerHit)
            {
                PlayEffect(HitEffectId, ChessConst.SCENE_EFFECT_DIR, ChessPlayerUnitHitConfig.heroHitEffect, ChessPlayerUnitHitConfig.hitEffectHangPoint, false);
            }
        }

        private void StopWinEff(HitMsg hitMsg)
        {
            this.StopConWinEff();
        }

        public void OnHit(HitMsg hitMsg)
        {
            StopWinEff(hitMsg);

            // 受击特效
            PlayHitEffect(hitMsg);

            if (hitMsg.isLastHurt)
            {
                PlayLastHurtAction();
            }
            else if (_animBehaviour != null && !_animBehaviour.IsInAct() && !hitMsg.isPlayerHit)
            {
                Vector3 attackDir = this.GetSelfTransform().forward;
                _animBehaviour.PlayHurtAction(false, -attackDir);
            }

            DeductLife(hitMsg);

            if (hitMsg.isPlayerHit)
            {
                PlayBattleLose(hitMsg.willKill);
            }

            bool isBattle = PlayerData != null && PlayerData.InBattle;
            if (isBattle)
            {
                if (hitMsg.willKill && PlayerUnitInfo != null && hitMsg.isLastHurt)
                {
                    HandlePlayerDeath();
                }
            }
        }

#if UNITY_EDITOR
        protected ChessViewAttackUnit m_attackUnitEditor = null;
        public void SetAttackUnitInEditor(ChessViewAttackUnit attackUnit)
        {
            m_attackUnitEditor = attackUnit;
        }
#endif

        public void PlayAttack(float offsetTime)
        {
            if (m_attackData != null)
            {
                var pm = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(PlayerData.ChessPlayerId);
                var defener = ChessBattleGlobal.Instance.ChessPlayerCtrl.GetPlayerUnitAllCamp(pm.EnemyPlayerID);

#if UNITY_EDITOR
                if (m_attackUnitEditor != null)
                    m_attackUnitEditor.StartAttack(this, defener, offsetTime);
                else
                {
#endif
                    ChessBattleGlobal.Instance.ChessPlayerCtrl.chessViewAttackSet.SendAttack(m_attackData.m_attackCfgName, this, defener, offsetTime);
#if UNITY_EDITOR
                }
#endif
            }
        }

        public void PlayWin()
        {
            StartCoroutine(DelayPlayWin());
        }

        private IEnumerator DelayPlayWin()
        {
            // 等着攻击动作播完 回到idle才播胜利动作
            while (!IsPlayingAnimStr("idle") && !IsPlayingAnimStr("run") && !IsPlayingAnimStr("run_fast"))
                yield return null;

            PlayWinAnim();

            PlayWinVoice();
        }

        public void PlayWinAnim()
        {
            string winAnim = GetTriggerParam<string>(TriggerEnum.WinAnim);
            if (string.IsNullOrEmpty(winAnim))
                winAnim = ChessPlayerUnitAnimBehaviour.win_anim;
            if (_animBehaviour != null)
                _animBehaviour.PlayAnim(winAnim);
        }

        public bool GetAttackSpeed(out float speed)
        {
            if (m_attackData != null && m_attackData.m_inAttack && m_attackData.m_runSpeed != Fix64.zero)
            {
                speed = m_attackData.m_runSpeed.ToSingle();
                return true;
            }
            else
            {
                if (config != null)
                    speed = config.runSpeed;
                else
                    speed = 10.0f;
                return false;
            }
        }

        public bool InAttack()
        {
            return m_attackData != null && m_attackData.m_inAttack;
        }

        public bool GetAttackRunAction(out string actionName)
        {
            if (m_attackData != null && m_attackData.m_inAttack && !string.IsNullOrEmpty(m_attackData.m_runAction))
            {
                actionName = m_attackData.m_runAction;
                return true;
            }
            else
            {
                actionName = string.Empty;
                return false;
            }
        }

        public bool IsMeleeAttack()
        {
            if (m_attackData != null && m_attackData.m_inAttack)
            {
                return m_attackData.m_hasRun || m_attackData.m_hasTeleport;
            }
            else
            {
                return false;
            }
        }

        #endregion

        public virtual void FallToGround(bool bSmooth = false)
        {
            Vector3 wPos = GetSelfTransform().localPosition;
            if (bSmooth)
            {
                GetSelfTransform().localPosition = new Vector3(wPos.x, SmoothGroundHeight(wPos.y, FallDown(wPos)), wPos.z);
            }
            else
            {
                GetSelfTransform().localPosition = new Vector3(wPos.x, FallDown(wPos), wPos.z);
            }
        }

        private float SmoothGroundHeight(float curY, float targetY)
        {
            return Mathf.MoveTowards(curY, targetY, Time.deltaTime * 5.0f);
        }

        public Vector3 ClampHeight(Vector3 pos)
        {
            return new Vector3(pos.x, FallDown(pos), pos.z);
        }

        protected float FallDown(Vector3 curPos)
        {
            if (MapMgr != null)
            {
                float maxHight = 0;
                var scale = _curScaleFactor;
                var sdfPath = MapMgr.GetSDFPath();
                if (sdfPath != null)
                {
                    for (int i = -1; i < 2; i += 2)
                    {
                        for (int j = -1; j < 2; j += 2)
                        {
                            FVector2 pos = new FVector2(curPos.x + i * scale * 0.4f, curPos.z + j * scale * 0.4f);
                            if (sdfPath.Sample(ref pos) > Fix64._05) // 可移动范围才检测
                            {
                                var curHight = MapMgr.FindHeightByMay(pos).ToSingle();
                                maxHight = Mathf.Max(curHight, maxHight);
                            }
                        }
                    }

                    FVector2 tmpPos = new FVector2(curPos.x, curPos.z);
                    if (sdfPath.Sample(ref tmpPos) > Fix64._05) // 可移动范围才检测
                    {
                        var curHight = MapMgr.FindHeightByMay(tmpPos).ToSingle();
                        maxHight = Mathf.Max(curHight, maxHight);
                    }

                    m_lowestHeight = maxHight;
                }
                else
                {
                    FVector2 pos = new FVector2(curPos.x, curPos.z);
                    m_lowestHeight = MapMgr.FindHeightByMay(pos).ToSingle();
                }
            }
            else
            {
                m_lowestHeight = 0.0f;
            }
            return m_lowestHeight;
        }

        /// <summary>
        /// 隐藏角色模型
        /// </summary>
        public override void HideBody()
        {
            base.HideBody();

            DoShowHeadInfo(false);

            foreach (var runEffect in runEffectDict)
            {
                runEffect.Value.SetActive(false);
            }
        }

        /// <summary>
        /// 显示角色模型
        /// </summary>
        public override void ShowBody()
        {
            if (IsInDelayShowing)
            {
                return;
            }

            base.ShowBody();

            DoShowHeadInfo(true);

            foreach (var runEffect in runEffectDict)
            {
                runEffect.Value.SetActive(true);
            }

            if (_collider != null) // 小小英雄现在不需要这玩意 直接隐藏
                _collider.enabled = false;
        }

        public override Transform GetGroundNodeFromSocket()
        {
            Transform groundNode = base.GetGroundNodeFromSocket();
            if (groundNode != null)
                return groundNode;
            else
                return GetSelfTransform();
        }

        protected void GetBodyPath(out string model_path, out string model_name)
        {
            ChessPlayerBodyCache.GetBodyResName(PlayerData.TinyId, PlayerData.InBattle || PlayerData.ForceLowModel, out model_path, out model_name);
            if (string.IsNullOrEmpty(model_path))
                model_path = "art_tft_raw/little_legend_res/model/t_heliujingling/t_heliujingling";
            if (string.IsNullOrEmpty(model_name))
                model_name = "t_heliujingling_1_show";
        }
        
        public static void GetDefaultTinyBodyPath(out string model_path, out string model_name)
        {
            TTAC_Global_Client global_def = DataBaseManager.Instance.SearchTACGlobalCfg((int)EGlobalType.EGlobalType_MISS_TINY_ID);
            if (global_def != null)
            {
                int.TryParse(global_def.sParam1, out int tinyId);
                ChessPlayerBodyCache.GetBodyResName(tinyId, true, out model_path, out model_name);
                Diagnostic.Log("[GetDefaultTinyBodyPath] model_path: {0} model_name: {1} need download. use default tiny: {2}. time: {3}", model_path, model_name, tinyId, Time.time);
            }
            else
            {
                model_path = string.Empty;
                model_name = string.Empty;
                Diagnostic.Log("[GetDefaultTinyBodyPath] model_path: {0} model_name: {1} need download. but find default tiny id faild. time: {2}", model_path, model_name, Time.time);
            }
        }

        private void OnSwitchPlayer(GEvent e)
        {

        }
        
        protected void LoadBodyImpl(LittleLegendResLoader loader, Action<GameObject, LittleLegendCfg> onLoaded)
        {
            ReleaseBody();

            _loader = loader;

            if (_loader.IsLoaded())
            {
                onLoaded?.Invoke(_loader.model, _loader.cfg);
            }
            else
            {
                _loader.AddLoadedCallback(onLoaded);
            }
        }

        protected void LoadBodyImpl(string abName, string assetName, IReleaseList releaseList, Action<GameObject, LittleLegendCfg> onLoaded)
        {
            ReleaseBody();
            _loader = LittleLegendResLoader.Load(abName, assetName, releaseList, ELoadResMode.LocalFirst, (tplGo, cfg) =>
            {
                if (tplGo == null && PlayerData.LoadDefaultModelWhenNeedDownload)
                {
                    Diagnostic.Log("[LoadBodyImpl] load tiny faild. try load default tiny. model_path: {0} model_name: {1} time: {2}", abName, assetName, Time.time);
                    GetDefaultTinyBodyPath(out string model_path, out string model_name);
                    _loader = LittleLegendResLoader.Load(model_path, model_name, releaseList, ELoadResMode.LocalFirst, (defaultGo, defaultCfg) =>
                    {
                        onLoaded?.Invoke(defaultGo, defaultCfg);
                    });
                }
                else
                {
                    onLoaded?.Invoke(tplGo, cfg);
                }
            });
        }

        protected override void UpdateBody()
        {
            GetBodyPath(out model_path, out model_name);

            if (_bodyLoadCoroutine != null)
                SystemManager.getInstance().StopCoroutine(_bodyLoadCoroutine);

            LittleLegendResLoader.Load(model_path, model_name, this, ELoadResMode.LocalFirst, (tplGo, cfg) =>
            {
                ReleaseBody();

                Callback_LoadedBodyAsset(tplGo, cfg, model_path);
            });
        }

        public void SetEffectEvent()
        {
            m_effectEvent = Body.TryGetComponent<ChessPlayerEffectEvent>();
            m_effectEvent.Initialize(this);
        }

        private void Callback_LoadedBodyAssetWithLoadedAsset(LoadedAsset loadModelAsset, LittleLegendCfg cfg, string asset)
        {
            ReleaseBody();
            GameObject tplGo = loadModelAsset.GetAsset<GameObject>();
            Callback_LoadedBodyAsset(tplGo, cfg, asset);
        }

        private void Callback_LoadedBodyAsset(GameObject tplGo, LittleLegendCfg cfg, string asset)
        {
            if (PlayerData == null || gameObject == null)
                return;
            _cfg = cfg;
            OnBodyLoaded(tplGo, asset);
            if (Body != null)
            {
                SetEffectEvent();

                if (_collider != null) // 小小英雄现在不需要这玩意 直接隐藏
                    _collider.enabled = false;

                _animEvtReceiver = Body.AddComponent<AnimEventReceiver>();
                _animBehaviour.AnimEvtReceiver = _animEvtReceiver;

                InitTriggerSystem();
            }

            var playerModel = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(PlayerData.ChessPlayerId);

            bool canShowBody = ChessBattleGlobal.Instance.ChessPlayerCtrl == null
                 || ChessBattleGlobal.Instance.ChessPlayerCtrl.CheckCanShowPlayerBody(PlayerData.ChessPlayerId);

            if (PlayerUnitInfo != null)
            {
                CreateConWinEff();

                int nextExpressionId = ExpressionController.NextExpressionId;
                if (nextExpressionId != 0)
                {
                    ExpressionController.ShowExpression(nextExpressionId, ExpressionController.NextExpressionPriority);
                    ExpressionController.ResetNextExpression();
                }
            }

            if (m_effectStates != null)
            {
                RePlayAllEffect(m_effectStates);
                m_effectStates = null;
            }

            if (!canShowBody)
            {
                Body.SetActive2(false);
            }

            if (OnBodyLoadedCallback != null)
            {
                OnBodyLoadedCallback(this);
            }

            if (Body != null)
            {
                // 小小英雄动作千奇百怪 避免出去了就回不来了...
                var allRenderer = Body.GetComponentsInChildren<SkinnedMeshRenderer>();
                if (allRenderer != null)
                {
                    foreach (var renderer in allRenderer)
                    {
                        renderer.updateWhenOffscreen = true;
                    }
                }
            }

            // 小小英雄不需要碰撞框
            var collider = GetComponent<Collider>();
            if (collider != null)
            {
                collider.enabled = false;
            }
        }

        public void UpdateBloodProgerssInfo()
        {

        }

        protected override void OnBodyLoaded(GameObject obj, string assetName)
        {
            base.OnBodyLoaded(obj, assetName);
            if (Body != null)
            {
                originBodyLocalScale = BodyTransform.localScale;
                SetBodyScale();
                SetConWinEffect();
                ChangeHeroLayer(GameObjectLayer.MiniHero);

                //m_springManager = Body.GetComponent<SpringManager>();
            }
            gameObject.layer = GameObjectLayer.MiniHero;
        }

        // 战斗外调用移动接口
        public void Walk_OutOfBattle(Vector2 end)
        {
            m_dst.x = end.x;
            m_dst.y = GetSelfTransform().localPosition.y;
            m_dst.z = end.y;

            //_animBehaviour.SetDestination(m_dst, PlayerMoveMessage.Run);
            //_animBehaviour.IsMoving = true;
            float angle = Vector2.Angle(end - new Vector2(GetSelfTransform().localPosition.x, GetSelfTransform().localPosition.z), Vector2.up);
            if (end.y < 0) angle = 360 - angle;
            FaceTo(angle, 0);

            GetSelfTransform().localPosition = new Vector3(m_dst.x, FallDown(m_dst), m_dst.z);
        }

        protected void OnPlayLastBodyAnim(AnimatorTaskController player)
        {
            if (m_lastBody != null)
            {
                GameObject.Destroy(m_lastBody);
                m_lastBody = null;
                Diagnostic.Log("destory last body when OnPlayLastBodyAnim");
            }
        }
        private void OnSwitchModelLoaded(GameObject model, LittleLegendCfg cfg)
        {
            Diagnostic.Log("OnNewBody Loaded " + model_name + " obj:" + model.name);

            //_bodyLoadCoroutine = null;
            var lastBody = Body;
            Body = null;
            m_characterHangPoint = null;

            // 将MatCtrl转移到临时的Unit上 等播完最后一个动作再清理
            Unit lastBodyUnit = null;
            if (lastBody != null)
            {
                lastBodyUnit = lastBody.AddComponent<Unit>();
                if (lastBodyUnit != null)
                {
                    lastBodyUnit.InheritMatCtrl(this);
                }
            }

            ReleaseMatCtrl();

            Callback_LoadedBodyAsset(model, cfg, model_path);
            //Callback_LoadedBodyAssetWithLoadedAsset(loadModelAsset, cfg, model_path);

            m_isChangeModel = false;
            if (lastBody != null)
            {
                // 重新初始化一下动作特效 方便下面chang_leave播放
                var e = lastBody.GetComponent<ChessPlayerEffectEvent>();
                if (e != null)
                    e.Initialize(lastBodyUnit);

                if (m_lastBody != null)
                {
                    GameObject.Destroy(m_lastBody);
                    m_lastBody = null; 
                    Diagnostic.Log("destory last body when OnSwitchModelLoaded");
                }
                m_lastBody = lastBody;
                float fadeTime = config != null ? config.GetCrossFadeTime(_animatorHandler.CurrentAnim, "change_leave", 0.2f) : 0.2f;
                var atc = lastBody.TryGetComponent<AnimatorTaskController>();
                if (atc != null)
                    atc.PlayAnim("change_leave", fadeTime, OnPlayLastBodyAnim);
            }

            AnimBehaviour.PlayAnim("change_enter", 0.2f);
        }
        
        public void SwitchModel(int tinyId, bool withAction = true)
        {
            /*Diagnostic.Log("Switch Tiny: " + tinyId);
            //if (ChessItemModel.IsTinyItem(tinyId))
            {
                PlayerData.TinyId = tinyId;
                OnSummonEnd();
            }*/
            if (ChessItemModel.IsTinyItem(tinyId))
            {
                PlayerData.TinyId = tinyId;

                if (IsRelease)
                {
                    Diagnostic.Log("Switch Tiny: " + tinyId + " faild! isRelease");
                    return;
                }

                Diagnostic.Log("Switch Tiny: " + tinyId + " success");
                //if (_bodyLoadCoroutine != null)
                //    SystemManager.getInstance().StopCoroutine(_bodyLoadCoroutine);

                var stage = QQGameSystem.Instance.GetStage() as BaseStage;
                GetBodyPath(out model_path, out model_name);

                //IReleaseList releaseList = stage;
                //if (ChessUtil.Unload_Ab)
                //    releaseList = null;

                m_isChangeModel = true;
                // 配置也得变
                var mgr = ChessPlayerConfigManager.Instance;
                if (mgr != null && PlayerData != null)
                {
                    mgr.GetPlayerConfig(PlayerData.TinyId, (cfg) =>
                    {
                        if (cfg != null)
                            SetConfig(cfg);
                        else
                            Diagnostic.Warn("[SwitchModel]Set Player Config Faild! " + PlayerData.TinyId);

                        LoadBodyImpl(model_path, model_name, stage, OnSwitchModelLoaded);

                        //_bodyLoadCoroutine = SystemManager.getInstance().StartCoroutine(ChessPlayerLoader.Load(model_path, model_name, OnSwitchModelLoaded, releaseList));
                    });
                }
            }
            else
            {
                Diagnostic.Log("Switch Tiny: " + tinyId + " faild! tinyid error");
            }
        }

        private void OnSummonEnd()
        {
            if (IsRelease)
                return;

            if (_bodyLoadCoroutine != null)
                SystemManager.getInstance().StopCoroutine(_bodyLoadCoroutine);

            var stage = QQGameSystem.Instance.GetStage() as BaseStage;
            GetBodyPath(out model_path, out model_name);

            IReleaseList releaseList = stage;
            if (ChessUtil.Unload_Ab)
                releaseList = null;

            LittleLegendResLoader.Load(model_path, model_name, this, ELoadResMode.LocalFirst, (tplGo, cfg) =>
            {
                ReleaseBody();

                Callback_LoadedBodyAsset(tplGo, cfg, model_path);
            });

            //_bodyLoadCoroutine = SystemManager.getInstance().StartCoroutine(ChessPlayerLoader.Load(model_path, model_name, (loadModelAsset, cfg) =>
            //{
            //    GameObject model = loadModelAsset.GetAsset<GameObject>();
            //    Diagnostic.Log("OnNewBody Loaded " + model_name + " obj:" + model.name);

            //    _bodyLoadCoroutine = null;
            //    GameObject.Destroy(Body);
            //    m_characterHangPoint = null;
            //    Callback_LoadedBodyAssetWithLoadedAsset(loadModelAsset, cfg, model_path);

            //    //if (m_isChangeModel)
            //    //{
            //    PlayAnim("change_end");
            //    //    m_isChangeModel = false;
            //    //}

            //}, releaseList));
        }

        private void CreateConWinEff()
        {

        }

        protected override void Awake()
        {
            base.Awake();
            m_actionStateController = new ActionStateController(this);
            m_selfTransform = transform;
        }

        public Transform GetSelfTransform()
        {
            if (m_selfTransform == null)
                m_selfTransform = transform;
            return m_selfTransform;
        }

        protected override void Start()
        {
            base.Start();

            Init();
        }

        protected override void OnEnable()
        {
            base.OnEnable();

            if (Body != null)
            {
                DoShowHeadInfo(true);
            }
        }

        protected override void OnDisable()
        {
            base.OnDisable();

            DoShowHeadInfo(false);

            InDeathAni = false;
            IsInDelayShowing = false;
        }

        protected override void ApplyBody(GameObject body)
        {
            var hangPoint = body.GetComponent<CharacterHangPoint>();
            if (hangPoint != null)
                hangPoint.SetLittleLegendCfg(_cfg);

            DoApplyBody(body);
        }

        protected override void OnDestroy()
        {
            ACGEventManager.Instance.RemoveEventListener(EventType_BattleView.AutoChess_Battle_ChangeOBServerBattle, OnSwitchPlayer);

            Release();
            base.OnDestroy();

            m_isPre = false;
            GetBattleUnitSubtractFunctor = null;
            OnBodyLoadedCallback = null;
            _action = null;
            _animBehaviour = null;
            _inputHandler = null;
            _handlerList.Clear();
            _animatorHandler = null;
            _animEvtReceiver = null;
            m_characterHangPoint = null;

            List<ChessPlayerEffectInfo> tmp = new List<ChessPlayerEffectInfo>(runEffectDict.Values);
            foreach (var runEffect in tmp)
            {
                runEffect.Destory();
            }
            runEffectDict.Clear();

            if (PlayerUnitInfo != null)
            {
                PlayerUnitInfo = null;
            }
        }

        public virtual void LateUpdate()
        {
            this.Tick(Time.deltaTime);

            if (ExpressionController != null)
            {
                ExpressionController.Tick(Time.time);
            }
        }

        public override void SetScale(float scale)
        {
            //transform.localScale = new Vector3(scale, scale, scale);
        }

        public void SetInitScale()
        {
            if (Body != null)
            {
                if (originBodyLocalScale == Vector3.zero)
                {
                    originBodyLocalScale = BodyTransform.localScale;
                }
                SetConWinBodyScale();
            }
        }

        private void BindBattleRoundModelMsg()
        {

        }

        private void UnBindBattleRoundModelMsg()
        {

        }

        private TAC_GameState m_gameState = TAC_GameState.TAC_GameState_GameStart;

        private void OnStatsBattleRoundModelBindingMessage(ObservableMessage msg)
        {
            switch (msg.Name)
            {
                case BattleTurnModel.BINDNAME_MESSANGER_LEVEL:
                    break;
                case BattleTurnModel.BINDNAME_GAMESTATE:
                    m_gameState = (TAC_GameState)msg.intValue;
                    switch (m_gameState)
                    {
                        case TAC_GameState.TAC_GameState_FightOverTime:
                            TriggerMoment(E_TRIGGER_MOMENT.FIGHT_OVERTIME);
                            break;
                    }
                    break;
                case BattleTurnModel.BINDNAME_TURNQUESTPHASE:
                    if (m_gameState == TAC_GameState.TAC_GameState_FightOverTime ||
                        m_gameState == TAC_GameState.TAC_GameState_Play)
                    {
                        BattleTurnModel.TTurnQuestPhase result = msg.CastValue<BattleTurnModel.TTurnQuestPhase>();
                        PlayerModel playerModel = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(PlayerData.ChessPlayerId);
                        switch (result.questStatus)
                        {
                            case QuestPlayerStatus.QUESTPLAYERSTATUS_WIN:
                                SetTriggerParam(TriggerEnum.EnemyDeductLife, result.enemyDeductLife);
                                SetTriggerParam(TriggerEnum.WinCount, playerModel.iConWinCount);
                                TriggerMoment(E_TRIGGER_MOMENT.ROUND_WIN);
                                break;
                            case QuestPlayerStatus.QUESTPLAYERSTATUS_FAIL:
                                SetTriggerParam(TriggerEnum.LoseCount, playerModel.iConLoseCount);
                                TriggerMoment(E_TRIGGER_MOMENT.ROUND_FAILED);
                                break;
                            case QuestPlayerStatus.QUESTPLAYERSTATUS_STARTBATTLE:
                                TriggerMoment(E_TRIGGER_MOMENT.ROUND_START);
                                break;
                        }
                    }
                    break;
            }
        }

        #region 小队长动画

        public void PlayRandomAnim(float randomValue, RandomAnimStateCfg[] animCfgs)
        {
            for (int i = 0; i < animCfgs.Length; i++)
            {
                if (randomValue <= animCfgs[i].Threshold)
                {
                    PlayAnim(animCfgs[i].AnimName);
                    break;
                }
            }
        }

        public void PlayAnim(int animId)
        {
            if (animId >= 0 && _animList.Count > animId)
            {
                string animName = string.Empty;
                bool forceRepeat = false;

                if (m_loopAnimationIds.Contains(animId))
                {
                    if (IsInClickedAnim(animId))
                    {
                        animName = _animLoopList[animId];
                        forceRepeat = true;
                    }
                    else
                    {
                        animName = _animList[animId];
                    }
                }
                else
                {
                    animName = _animList[animId];
                }

                if (!string.IsNullOrEmpty(animName) && (IsExistState(animName) || HasParamInAnimator(animName, AnimatorControllerParameterType.Trigger)))
                {
                    // 强行停止所有音效
                    if (m_effectEvent != null)
                    {
                        m_effectEvent.CleanAllSound(-1);

                        // 强行停止之前的特效 （鬼畜功能）
                        m_effectEvent.ClearCurStateInfo();
                        m_effectEvent.ClearAllEventObject();
                    }

                    PlayAnim(animName, false, -1, forceRepeat);
                }
            }
        }

        public bool HasParamInAnimator(string parameterName, AnimatorControllerParameterType type)
        {
            if (animator == null)
                return false;

            var parameters = _animator.parameters;
            if (parameters != null)
            {
                for (int i = 0; i < parameters.Length; ++i)
                {
                    if (parameters[i].name == parameterName && parameters[i].type == type)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        public bool IsInClickedAnim(int animId)
        {
            if (animId >= 0)
            {
                if (_animList.Count > animId && IsPlayingAnimStr(_animList[animId]))
                    return true;

                if (_animLoopList.Count > animId && IsPlayingAnimStr(_animLoopList[animId]))
                    return true;
            }
            return false;
        }

        public bool IsInClickedAnim()
        {
            for (int i = 0; i < _animList.Count; ++i)
            {
                if (IsPlayingAnimStr(_animList[i]))
                    return true;
            }

            for (int i = 0; i < _animLoopList.Count; ++i)
            {
                if (IsPlayingAnimStr(_animLoopList[i]))
                    return true;
            }
            return false;
        }

        public void PlayAnim(string animName, bool lockAnim = false, float normalizedTime = -1.0f, bool forceReplay = false)
        {
            if (_animatorHandler != null)
            {
                float fadeTime = config != null ? config.GetCrossFadeTime(_animatorHandler.CurrentAnim, animName, 0.2f) : 0.2f;
                PlayAnim(animName, AnimType.NORMAL, lockAnim, forceReplay, -0.1f, fadeTime, normalizedTime);
            }
        }

        protected override void PlayBaseAnim(string animName, AnimType type, bool lockAnim, bool forceReplay, object[] values)
        {
            if (animName == "idle")
            {
                if (PlayerUnitInfo != null)
                {
                    PlayerUnitInfo.ResetMinHeight();
                }
            }
            //if (m_effectEvent != null)
            //{
            //    m_effectEvent.ClearCurStateInfo();
            //    m_effectEvent.ClearAllEventObject();
            //}
            base.PlayBaseAnim(animName, type, lockAnim, forceReplay, values);
        }

        #endregion


        /// <summary>
        /// 开始战斗 播放加油动画。
        /// </summary>
        public void OnBattlePlayStart()
        {
            PlayEnterFightingVoice();

            if (ExpressionController != null)
            {
                //开始战斗前清空一下魔法道具缓存数据
                ExpressionController.Release();
            }
        }

        public void SetPlayerHpInfo(int left)
        {

        }


        public void OnGameOut(bool win = false)
        {
            if (!win)
            {
                PlayAnim("death");
                PlayDeathEffect();
                var efMatControllers = GetComponentsInChildren<EffectMaterialScript>(true);
                if (efMatControllers != null && efMatControllers.Length > 0)
                {
                    foreach (var ef in efMatControllers)
                    {
                        ef.gameObject.SetActive(false);
                    }
                }
            }
        }

        public static bool DisplayPlayerResultPanel = true;


        #region 新表现 

        public void StopNewCor()
        {
            if (coroutines.Count > 0)
            {
                for (int i = 0; i < coroutines.Count; i++)
                {
                    if (coroutines[i] != null)
                    {
                        StopCoroutine(coroutines[i]);
                        coroutines[i] = null;
                    }
                }
            }
            coroutines.Clear();
        }
        #endregion

        public void HandlePlayerDeath()
        {
            var battleModel = ChessModelManager.Instance.GetBattleModel();
            var playerModel = battleModel.GetPlayerModel(PlayerData.ChessPlayerId);

            isDead = true;
            if (battleModel.IsArriveState() && !playerModel.IsHomeCourt())
            {
                ShowBody();
                GetSelfTransform().position = jumpDst;
            }

            if (!TriggerMoment(E_TRIGGER_MOMENT.DIE))
            {
                PlayAnim("death");
            }

            PlayDeathEffect();

            if (PlayerUnitInfo != null)
            {
                PlayerUnitInfo.DeathFadeOut();
                DoShowHeadInfo(false);
            }
            DestroyGlobalEffect();
        }

        public void PlayBattleLose(bool willKill)
        {
            bool ChangeFlag = NeedChangeScale(BodyScaleChangeCondition.BATTLE_RESULT);
            if (ChangeFlag)
            {
                StopConWinEff();
            }

            if (willKill)
            {
                PlayAnim("death");
                isDead = true;
                PlayDeathEffect();
                if (PlayerUnitInfo != null)
                    PlayerUnitInfo.DeathFadeOut();
            }
            else
            {
                PlayLoseVoice();
            }
        }

        public void SetUpHurtEffectId(int itemId)
        {
            _hurtEffectId = itemId;
        }

        public float Direction2Angle(Vector3 dir)
        {
            dir.Normalize();

            float angle = Vector3.Angle(Vector3.forward, dir);

            if (dir.x < 0)
                angle = -angle;
            return angle;
        }

        /// <summary>
        /// 设置血条显示隐藏状态
        /// </summary>
        public override void DoShowHeadInfo(bool isShow)
        {
            if (PlayerData != null)
            {
                // 死了就不要显示血条了
                var playerModel = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(PlayerData.ChessPlayerId);
                if (playerModel.IsDead())
                    isShow = false;
            }

            if (!gameObject.activeSelf) isShow = false;

            //血条显隐控制
            int id = -1;
            string name = string.Empty;
            if (PlayerData != null)
            {
                id = PlayerData.ChessPlayerId;
                name = PlayerData.PlayerName;
            }
            Diagnostic.Log("控制[{0}][{1}]小队长血条: {2}", id, name, isShow ? "显示" : "隐藏");

            if (PlayerUnitInfo != null)
            {
                //PlayerUnitInfo.gameObject.SetActive(isShow);
                if (isShow)
                    PlayerUnitInfo.SetBloodSplit();
                GameUtil.SetScaleToZero(PlayerUnitInfo.gameObject, !isShow, true);
            }
        }

        // 停止连胜表现
        public void StopConWinEff()
        {
            if (Body != null)
            {
                if (initScale > 0 && PlayerData.InBattle)
                    BodyTransform.localScale = originBodyLocalScale * initScale * Playerscale;
                else
                    BodyTransform.localScale = originBodyLocalScale * Playerscale;
            }

            _curScaleFactor = 1.0f;
        }

        //播放胜利表现。连胜 缩放。
        public void PlayRoundWinEff()
        {
            if (PlayerData == null)
                return;

            if (Body != null)
            {
                SetConWinBodyScale();
                SetConWinEffect();
            }
        }

        public void PlayWinVoice()
        {

        }

        private void PlayLoseVoice()
        {

        }

        private void PlayEnterFightingVoice()
        {
            if (PlayerData == null || PlayerData.isEnemy)
                return;
        }

        // 获取配置挂点
        public static Transform GetHangPoint(ChessBattleUnit chessBattleUnit, EffectHangPoint hangPoint)
        {
            switch (hangPoint)
            {
                case EffectHangPoint.Head: // 受保护的方法  目前用不到头部 先不加
                case EffectHangPoint.Body: return chessBattleUnit.GetSpineNodeFromSocket();
                case EffectHangPoint.Ground: return chessBattleUnit.GetGroundNodeFromSocket();
                default: return chessBattleUnit.GetSpineNodeFromSocket();
            }
        }

        // 播放小队长一种配置的声音。
        public void PlayerVoice(string voicePath)
        {
            if (string.IsNullOrEmpty(voicePath))
                return;
            ChessUtil.SplitAssetPath(voicePath, out string bank_name, out string event_name);
            if (string.IsNullOrEmpty(bank_name) || string.IsNullOrEmpty(event_name))
                return;
            ChessUtil.PlayWwiseBankByPath(bank_name, event_name, Body);
        }

        public static Vector3 CheckBound(Vector3 pos)
        {
            ChessBattleLogicField.StaticField ins = ChessBattleLogicField.StaticField.Ins;
            Vector2 min = new Vector2(ins.leftDown.x.ToSingle(), ins.leftDown.y.ToSingle());
            Vector2 max = new Vector2(ins.rightUp.x.ToSingle(), ins.rightUp.y.ToSingle());

            Vector3 ret = new Vector3(Mathf.Clamp(pos.x, min.x, max.x), pos.y, Mathf.Clamp(pos.z, min.y, max.y));

            return ret;
        }

        public void PlayFx_LevelUp()
        {
            if (Body != null)
            {
                var root = this.GetGroundNodeFromSocket();

                ChessUtil.LoadTFTEffect(ChessConst.TEAMLEADER_EFFECT_COMMON_AB, "effect_teamleader_levelup", null,
                    delegate (GameObject obj)
                {
                    obj.transform.SetParent(root, true);
                    obj.transform.localPosition = Vector3.zero;

                });
            }
        }

        public void PlayFx_RefreshShop()
        {
            if (Body != null)
            {
                PlayEffect(RefreshShopEffectId, ChessConst.TEAMLEADER_EFFECT_COMMON_AB, ChessPlayerUnitHitConfig.heroRefreshShop, ChessPlayerUnitHitConfig.hitEffectHangPoint, true, bindScale: true);
            }
        }


        private Dictionary<string, GameObject> m_AddAssetDict = new Dictionary<string, GameObject>();

        public void PlayAddictionFx(string assetBundlePath, string assetName)
        {
            if (gameObject != null)
            {
                if (m_AddAssetDict.ContainsKey(assetName) == false)
                {
                    ChessUtil.LoadTFTEffect(assetBundlePath, assetName, null,
                        delegate (GameObject obj)
                        {
                            obj.transform.SetParent(gameObject.transform, true);
                            obj.transform.localPosition = Vector3.zero;
                            m_AddAssetDict.Add(assetName, obj);
                        }, false);
                }
                else
                {
                    m_AddAssetDict[assetName].SetActive(true);
                }
            }
        }

        public void StopAddictionFx(string assetName)
        {
            GameObject targetFx = null;
            m_AddAssetDict.TryGetValue(assetName, out targetFx);
            if (targetFx != null)
            {
                targetFx.SetActive(false);
            }
        }

        public void SetConWinBodyScale()
        {
            if (!PlayerData.InBattle || BodyTransform == null)
                return;


            PlayerModel playerModel = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(PlayerData.ChessPlayerId);
            if (playerModel.iConWinCount == 0)
            {
                if (initScale > 0)
                    BodyTransform.localScale = originBodyLocalScale * initScale * Playerscale;
                else
                    BodyTransform.localScale = originBodyLocalScale * Playerscale;
            }
            else
            {
                float newScaleFactor = (playerModel.iConWinCount) > ConWinScaleFactors.Length ? ConWinScaleFactors[ConWinScaleFactors.Length - 1] : ConWinScaleFactors[playerModel.iConWinCount - 1];
                if (Mathf.Approximately(this._curScaleFactor, newScaleFactor))
                {
                    return;
                }
                _curScaleFactor = newScaleFactor;
                BodyTransform.localScale = originBodyLocalScale * _curScaleFactor * Playerscale;
            }
        }

        private int m_conWinEffectId = -1;
        public void SetConWinEffect()
        {
            if (!PlayerData.InBattle || BodyTransform == null || string.IsNullOrEmpty(m_coinWinEffectPre) || m_conWinEffectRules.Count == 0)
                return;

            PlayerModel playerModel = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(PlayerData.ChessPlayerId);
            int conWinCount = playerModel.iConWinCount;

            int result = -1;
            for (int i = m_conWinEffectRules.Count - 1; i > -1; i--)
            {
                if (conWinCount >= m_conWinEffectRules[i])
                {
                    result = i;
                    break;
                }
            }

            if (result == m_conWinEffectId)
                return;

            m_conWinEffectId = result;
        }

        protected void ClearConWinConfig()
        {
            m_coinWinEffectPre = string.Empty;
            m_conWinEffectRules.Clear();
        }

        private bool NeedChangeScale(BodyScaleChangeCondition condition)
        {
            return condition == m_BodyScaleChangeCondition;
        }

        public void SetBodyScale()
        {
            if (ChessBattleGlobal.Instance != null && ChessBattleGlobal.Instance.ChessPlayerCtrl != null && BodyTransform != null)
            {
                float bodyscale = 1f;
                // 当前进行到什么阶段了  
                BeatenType beatenType = ChessBattleGlobal.Instance.ChessPlayerCtrl.CanChagePlayerBodyScaleNow(PlayerData.ChessPlayerId, ref bodyscale);
                bool useOriginalScale = (int)beatenType < (int)m_BodyScaleChangeCondition;
                if (useOriginalScale)
                {
                    _curScaleFactor = bodyscale;
                    BodyTransform.localScale = originBodyLocalScale * _curScaleFactor * Playerscale;
                }
                else
                {
                    SetConWinBodyScale();
                }
            }
        }

        private void PlayDeathEffect()
        {
            StartCoroutine(PlayDeathDissolve());
        }

        private IEnumerator PlayDeathDissolve()
        {
            ReleaseFakeShadow();

            InDeathAni = true;

            float delayTime = 0;
            if (_animator != null && _animator.runtimeAnimatorController != null)
            {
                for (int i = 0; i < _animator.runtimeAnimatorController.animationClips.Length; ++i)
                {
                    var clip = _animator.runtimeAnimatorController.animationClips[i];
                    if (clip != null && clip.name.EndsWith("death"))
                    {
                        delayTime = clip.length;
                        break;
                    }
                }
            }
            else if (PlayerData != null && !PlayerData.InBattle)
            {
                delayTime = 1;
            }
            else
            {
                delayTime = 2;
            }

            // 防止ResetMat时序问题&等待动画播完
            yield return new WaitForSeconds(delayTime);

            if (chessHeroMatCtrl != null)
            {
                HeroMaterialChange(ChessHeroMaterialController.CurveType.TeamleaderDeathDissolve);

                while (chessHeroMatCtrl != null && chessHeroMatCtrl.GetCurveComposerRunType() == ACGameMaterialCurveComposer.RunType.Ing)
                {
                    yield return null;
                }

                // 死亡动画结束以后直接隐藏 避免残留 上面那个动画在追帧的时候会残留小小英雄
                HideBody();
            }

            // 局外的再等等 马上播有点奇怪
            if (PlayerData != null && !PlayerData.InBattle)
                yield return new WaitForSeconds(1f);

            InDeathAni = false;
        }

        public void DelayShow()
        {
            StartCoroutine(DelayShowBody());
        }

        private IEnumerator DelayShowBody()
        {
            IsInDelayShowing = true;

            Vector3 pos = transform.position;

            Vector3 finalLookPos = pos;
            finalLookPos.z = 0.0f;
            Vector3 finalDir = finalLookPos - pos;

            Vector3 firstLookPos = pos - finalDir;

            HideBody();
            yield return new WaitForSeconds(1f);
            IsInDelayShowing = false;

            bool isShow = true;
            if (PlayerData != null)
            {
                // 死了就不要显示血条了
                var playerModel = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(PlayerData.ChessPlayerId);
                if (playerModel.IsDead())
                    isShow = false;
            }
            if (isShow)
            {
                ShowBody();

                transform.LookAt(firstLookPos);
                _curAngle = transform.eulerAngles.y;

                FaceTo(Direction2Angle(finalDir), 0.5f);
            }
        }

        public void TriggerMapLogicArea(bool isRegain)
        {
            ChessBattleModel battleModel = ChessModelManager.Instance.GetBattleModel();
            PlayerModel playerModel = battleModel.GetPlayerModel(PlayerData.ChessPlayerId);

            bool needExcute = false;

            //我去别人的棋盘观战，看的不一定是别人的棋盘，可能是自己在和别人战斗，看到的是自己的棋盘，那么需要触发
            if (playerModel.PlayerId != battleModel.CurrentPlayerId && playerModel.BattleFiledVsPlayerPair.Key != null && playerModel.BattleFiledVsPlayerPair.Value != null)
            {
                int mapOwnerID = -1;
                int enemyID = -1;
                if (playerModel.BattleFiledVsPlayerPair.Key.isHomeCourt)
                {
                    mapOwnerID = playerModel.BattleFiledVsPlayerPair.Key.iChairid;
                    enemyID = playerModel.BattleFiledVsPlayerPair.Value.iChairid;
                }
                else if (playerModel.BattleFiledVsPlayerPair.Value.isHomeCourt)
                {
                    mapOwnerID = playerModel.BattleFiledVsPlayerPair.Value.iChairid;
                    enemyID = playerModel.BattleFiledVsPlayerPair.Key.iChairid;
                }

                if (mapOwnerID != -1 && enemyID != -1 &&
                    (playerModel.PlayerId == mapOwnerID && battleModel.CurrentPlayerId == enemyID))
                {
                    needExcute = true;
                }
            }
            else if (playerModel.PlayerId == battleModel.CurrentPlayerId)
            {
                //除此之外，去别人的棋盘，一定是看别人的棋盘，那么别人触发
                //此Unit是当前关注棋盘的玩家，直接触发
                //这里的CurrentPlayerId，是当前关注的棋盘玩家ID，并不是拥有者
                needExcute = true;
            }

            if (!needExcute)
            {
                return;
            }

            BattleMap battleMap = MapMgr.GetCurrentBattleMap();
            if (battleMap == null)
                return;

            BattleMapTriggerManager triggerMgr = battleMap.GetBattleMapTrggierManager();
            if (triggerMgr == null)
                return;

            //triggerMgr.TryTriggerMapLogicArea(this, isRegain, playerModel.GetMapLogicTriggerIndexs());
        }

        /// <summary>
        /// 使用地图逻辑触发的数据，对地图动效进行刷新
        /// </summary>
        public void TriggerMapLogic(bool isRegain)
        {
            
        }

        /// <summary>
        /// 触发地图重回，用于切换地图时，触发棋盘逻辑
        /// </summary>
        public void TriggerMapRegain()
        {
            BattleMap battleMap = MapMgr.GetCurrentBattleMap();
            if (battleMap == null)
                return;

            BattleMapTriggerManager triggerMgr = battleMap.GetBattleMapTrggierManager();
            if (triggerMgr == null)
                return;

            //注意，所有Unit，都会触发，需要在内部做处理
            triggerMgr.TriggerMapRegain(this);
        }


        #region 传送特效逻辑
        private const string effectABPath = "art_tft_raw/effects/scene_effect";
        private const string effectAssetFlashLight = "scene_heroflash";
        private const string effectAssetFlashFollow = "scene_followflash";
        public string GetPlayerCurTransferEffectAbPath()
        {
            if (PlayerData != null && PlayerData.TinyData != null)
            {
                var tempTransferCfgDesc = DataBaseManager.Instance.SearchTransferCfgClientDesc(PlayerData.TinyData.iTransportEffectId);
                if (tempTransferCfgDesc != null)
                {
                    return tempTransferCfgDesc.sAssetBundle;
                }
            }

            return effectABPath;
        }

        public string GetPlayerCurTransferEffectName(bool isFlashLight)
        {
            if (PlayerData != null && PlayerData.TinyData != null)
            {
                var tempTransferCfgDesc = DataBaseManager.Instance.SearchTransferCfgClientDesc(PlayerData.TinyData.iTransportEffectId);
                if (tempTransferCfgDesc != null)
                {
                    if (isFlashLight)
                        return tempTransferCfgDesc.sFlashLight;
                    else
                        return tempTransferCfgDesc.sFlashFollow;
                }
            }

            if (isFlashLight)
            {
                return effectAssetFlashLight;
            }

            return effectAssetFlashFollow;
        }
        #endregion
    }
}