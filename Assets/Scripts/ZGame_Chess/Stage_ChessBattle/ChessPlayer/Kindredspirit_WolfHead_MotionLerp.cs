using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TKPlugins;
using UnityChan_LION;

public class Kindredspirit_WolfHead_MotionLerp : MonoBehaviour
{
    public Transform parent;
    public Transform child;
    public float positionLerpSpeed=1f;
    public float rotationLerpSpeed=1f;
    public SpringManager SpringManager;

    private Vector3 init_offset;
    private Quaternion init_rot;
    private Vector3 tempPosition;
    private Vector3 targetPos;
    private Quaternion tempRotate;
    private Quaternion targetRot;
    private bool SmoothEnable=true;
    private bool initPos = true;
    void Start()
    {
        if(parent==null ||child==null||SpringManager==null){
            Debug.LogError("parent==null ||child==null||SpringManager==null");
            this.enabled=false;
            return;
        }
        init_offset= child.localPosition;
        init_rot= child.localRotation;
        tempRotate = child.rotation;
        tempPosition =child.position;
    }

    void LateUpdate()
    {
        SmoothEnable=SpringManager.enabled;
        if(!SmoothEnable){
		targetPos=init_offset;
		targetRot=init_rot;
        	child.localPosition=Vector3.Lerp(child.localPosition,targetPos,Time.deltaTime*positionLerpSpeed);
		
        	child.localRotation=Quaternion.Slerp(child.localRotation, targetRot, Time.deltaTime * rotationLerpSpeed);
        
            //if (initPos)
            //{
            //    initPos = false;
            //    child.position= parent.TransformPoint(init_offset);
            //    child.rotation = parent.rotation * init_rot;
            //}
            return;
        }
        targetPos=parent.TransformPoint(init_offset);
        tempPosition=Vector3.Lerp(tempPosition,targetPos,Time.deltaTime*positionLerpSpeed);
        child.position= tempPosition;
        targetRot=parent.rotation* init_rot;
        tempRotate = Quaternion.Slerp(tempRotate, targetRot, Time.deltaTime * rotationLerpSpeed);
        child.rotation= tempRotate;
        //initPos = true;
    }
}
