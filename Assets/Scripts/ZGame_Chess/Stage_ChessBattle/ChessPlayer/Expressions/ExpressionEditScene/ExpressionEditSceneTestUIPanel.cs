using System.Collections;
using System.Collections.Generic;
using Lucifer.ActCore;
using TKFrame;
using UnityEngine;
using UnityEngine.UI;
using ZGameChess;

public class ExpressionEditSceneTestUIPanel : TKUIBehaviour
{
    [UIObject("Portrait")]
    public Image PortraitImage { get; set; }




    protected override void OnEnable()
    {
        base.OnEnable();
        InitUI();

        RegisterEvents();

    }

    void RegisterEvents()
    {
        ACGEventManager.Instance.AddEventListener(EventType_UI.SEND_MAGIC_EXPRESSION_INFO,OnPlayMagicExpression);
    }
    
    void UnRegisterEvents()
    {
        ACGEventManager.Instance.RemoveEventListener(EventType_UI.SEND_MAGIC_EXPRESSION_INFO,OnPlayMagicExpression);
    
    }

    void InitUI()
    {
        
    }
    
    public void OnPlayMagicExpression(GEvent e)
    {
        var data = e.objData as MagicExpressionInfo;
        PlayMagicExpressionEffect(data);
    }
    
    public void PlayMagicExpressionEffect(MagicExpressionInfo data)
    {
        ChessUtil.LoadFxBattleInteractEmoji(data.effectName, this.transform, delegate (GameObject EffectGo)
        {
            foreach (Transform tran in EffectGo.GetComponentsInChildren<Transform>())
            {
                tran.gameObject.layer = GameLayerConfig.LAYER_UI_INT;
            }

            EffectGo.transform.localScale = data.scale;
            EffectGo.transform.localPosition = data.offset;
        });
    }
    
    
    protected override void OnDisable()
    {
        UnRegisterEvents();
        base.OnDisable();
    }
    
}
