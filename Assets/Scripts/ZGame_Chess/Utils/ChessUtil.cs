using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using TKFrame;
using TKPlugins;
using UnityEngine;
using Wup.Jce;
using ZGame;
using ZGame.GameSystem;
using ZGameClient;
using ZGame.Battle;
using UnityEngine.UI;

namespace ZGameChess
{
    public struct ItemKey
    {
        public int ItemId { get; private set; }
        public int SeqId { get; private set; }

        public ItemKey(int itemId, int seqId)
        {
            ItemId = itemId;
            SeqId = seqId;
        }
    }

    //public enum ESceneType
    //{
    //    NONE = 0,
    //    SINGLETON = 1,
    //    WAITING = 2,
    //    FRIENDS = 3,
    //    TEAM = 4,  //组队PK
    //    MULTIPLAYER = 5, //多人玩法
    //    TEAM_SHARELIFE = 6,
    //    MTPVE = 7, //PVE
    //    RANKING_SINGLE = 8,
    //    RANKING_TEAM = 9,
    //    RANKING = 10,
    //    BOUNTY = 11,//赏金赛快速
    //    CUSTOM = 12,//自定义
    //    HUNDREDPLAYERS = 13, //百人
    //    NEWER,
    //    DUAL_PLAY, //双人匹配
    //    TURBO,
    //    TURBO1,
    //    BOUNTYNORMAL,
    //    SET_X,
    //    EQUIPMENT,
    //    MatchSet5,
    //    RankSet5,
    //    BOSS_CHALLENGE,
    //    DAILY_CHALLENGE,
    //    GUIDE,
    //    DUAL_PLAY_RANK,
    //    SET5_5,
    //    NEW_CUSTOM_BEG = 990,
    //    NEW_CUSTOM = 999,
    //}

    public enum MACLIENTID
    {
        /// <summary>
        /// 无
        /// </summary>
        MA_NULL = 0,
        /// <summary>
        /// 主界面
        /// </summary>
        MA_MAINHALL = 1,
        /// <summary>
        /// 排行榜
        /// </summary>
        MA_LIST = 2,
        /// <summary>
        /// BP
        /// </summary>
        MA_BP = 3,
        /// <summary>
        /// 商城
        /// </summary>
        MA_MALL = 4,
        /// <summary>
        /// 好友系统
        /// </summary>
        MA_FRIEND = 5,
        /// <summary>
        /// 图鉴系统
        /// </summary>
        MA_HANDBOOK = 6,
        /// <summary>
        /// 聊天系统
        /// </summary>
        MA_Chat = 7,
        /// <summary>
        /// 邮件系统
        /// </summary>
        MA_MAIL = 8,
        /// <summary>
        /// 个人信息
        /// </summary>
        MA_USERINFO = 9,
        /// <summary>
        /// 仓库
        /// </summary>
        MA_WAREHOUSE = 10,
        /// <summary>
        /// 事件
        /// </summary>
        MA_ACTIVITYEVENT = 11,

        /// <summary>
        /// 对局记录
        /// </summary>
        MA_USERINFO_HISTORYRECORD = 12,
        /// <summary>
        /// 游戏资料
        /// </summary>
        MA_USERINFO_GAMEDATA = 13,
        /// <summary>
        /// 数据中心
        /// </summary>
        MA_USERINFO_DATASTATIS = 14,
        /// <summary>
        /// 战利品
        /// </summary>
        MA_TORPHIES = 15,
        /// <summary>
        /// 局外设置面板
        /// </summary>
        MA_GAME_SETTING_LOBBY = 16,
        /// <summary>
        /// 学院
        /// </summary>
        MA_GUIDE_COLLEGE = 17,
        /// <summary>
        /// 排位信息
        /// </summary>
        MA_RANKINFOFAMTION = 18,
        /// <summary>
        /// 主界面-左屏幕
        /// </summary>
        MA_MAINHALL_LEFTSCREEN = 19,
        /// <summary>
        /// 主界面-右屏幕
        /// </summary>
        MA_MAINHALL_RIGHTSCREEN = 20,
        /// <summary>
        /// 右屏幕—活动中心
        /// </summary>
        MA_MAINHALL_RIGHTSCREEN_ACTITIVITYCENTERPOP_CLICK = 21,
        /// <summary>
        /// 右屏幕—公告中心
        /// </summary>
        MA_MAINHALL_RIGHTSCREEN_PANDORAACTIVITYENTRACE_CLICK = 22,
        /// <summary>
        /// 主屏幕—日程（每日首胜）
        /// </summary>
        MA_MAINHALL_RIGHTSCREEN_SCHEDULE_CLICK = 23,
        /// <summary>
        /// 活动中心
        /// </summary>
        ACTIVITYCENTER = 24,
        /// <summary>
        /// 赏金赛门票
        /// </summary>
        BOUNTY_COST = 25,
        /// <summary>
        /// 赏金赛排名
        /// </summary>
        BOUNTY_RANK = 26,
        /// <summary>
        /// 赏金赛组队
        /// </summary>
        BOUNTY_TEAM = 27,
        /// <summary>
        /// 赏金赛单人
        /// </summary>
        BOUNTY_SINGLE = 28,
        /// <summary>
        /// 打开赠礼界面
        /// </summary>
        GIFT_DIALOG_OPEN = 29,
        /// <summary>
        /// 赠礼分享好友
        /// </summary>
        GIFT_SHARE_FRIEND = 30,
        /// <summary>
        /// 打开微社区
        /// </summary>
        COMMUNITY = 31,
        /// <summary>
        /// 微社区点击
        /// </summary>
        MICRO_COMMUNITY_CLICK = 32,
        /// <summary>
        /// 晶能点击
        /// </summary>
        JGAMEBLING_OPEN = 33,
        /// <summary>
        /// 战备系统
        /// </summary>
        PREPARE_BATTLE = 34,
        /// <summary>
        /// 晶能兑换打开
        /// </summary>
        JGAMEBLING_EXCHANGE_OPEN = 35,
        /// <summary>
        /// 阵容推荐
        /// </summary>
        TeamRecommend = 36,
        /// <summary>
        /// 查看小小英雄
        /// </summary>
        LOOK_TINY = 37,
        /// <summary>
        /// 查看事件中心
        /// </summary>
        ACTIVITY_EVENT = 38,
        /// <summary>
        /// 查看命运
        /// </summary>
        DESTINY_SYS = 39,
        /// <summary>
        /// 房间内，点击小小英雄进入战备
        /// </summary>
        MATCHINGROOM_CLICK_TINYHERO_BATTLEPREPARE = 46,
        /// <summary>
        /// 大厅点商业化英雄
        /// </summary>
        MAINHALL_CLICK_EXHIBIT_HERO_SHOWENTRY = 47,
        /// <summary>
        /// 大厅点英雄
        /// </summary>
        MAINHALL_CLICK_HERO_SHOWENTRY = 48,
        /// <summary>
        /// 经由大厅英雄进入相应的系统.
        /// </summary>
        MAINHALL_CLICK_HERO_GOENTRY = 49,
        /// <summary>
        /// 潘多拉
        /// </summary>
        PANDORA = 50,
        /// <summary>
        /// 生涯周报
        /// </summary>
        USERINFO_PANEL_WEEKLY_REPORT = 51,
        /// <summary>
        /// 魔典系统
        /// </summary>
        CARD_COLLECT = 52,
        /// <summary>
        /// 赛后结算界面
        /// </summary>
        BATTLEEND = 53,

        /// <summary>
        /// 俱乐部
        /// </summary>
        CLUB = 54,

        /// <summary>
        /// 主界面左上角提示
        /// </summary>
        MA_TIPS = 56,

        /// <summary>
        /// 赛事兑换
        /// actiontype:0 点击
        /// subview:1 查看商品
        /// extraInfo: 商品ID
        /// </summary>
        COMPETITION_STORE = 60,

        /// <summary>
        /// 赛事系统-宝典-当日查看人数
        /// </summary>
        COMPETITION_PASS = 61,
        /// <summary>
        /// 赛事系统-宝典-当日点击奖励人数
        /// </summary>
        COMPETITION_PASS_SEE_REWARD = 62,
        /// <summary>
        /// 月卡
        /// </summary>
        MONTHCARD = 63,
        /// <summary>
        /// 棋盘抽奖
        /// </summary>
        BOARDLOTTERY = 64,
        /// <summary>
        /// 大厅段位动画播放
        /// </summary>
        RANKUPPLAY = 65,

        /// <summary>
        /// 升级动画播放
        /// </summary>
        LEVELUP_POP = 66,

        /// <summary>
        /// 查看等级奖励
        /// </summary>
        LEVELUPINFO_POP = 67,

        /// <summary>
        /// 名片卡
        /// </summary>
        NAMECARD_POP = 68,
        /// <summary>
        /// 神话召唤
        /// </summary>
        SEASON_LOTTERY = 69,

        /// <summary>
        /// 成就
        /// </summary>
        ACHIEVEMENT = 70,

        /// <summary>
        /// 加载时loading的信息切换
        /// </summary>
        LOADINGCLICK = 71,

        /// <summary>
        /// 藏品羁绊系统
        /// </summary>
        WAREHOUSEFETTERSYS = 72,
        
        /// <summary>
        /// 通过主界面进入月卡
        /// </summary>
        MONTHCARD_BY_HALL = 73,
        /// <summary>
        /// PVE教学
        /// </summary>
        PVE_GUIDE = 1001,
    }

    public enum RankSystemReportType
    {
        /// <summary>
        /// 左侧一级页签点击，之后要带上点击Page
        /// </summary>
        LeftPageToggle_Layer_1 = 10,
        /// <summary>
        /// 上方二级页签点击，之后要带上点击Page
        /// </summary>
        TopPageToggle_Layer_2 = 20,
        /// <summary>
        /// 双人房间入口
        /// </summary>
        BattleSettingType_DaulMatching_Entry = 30,
    }

    public enum BattleSettingType
    {
        /// <summary>
        /// 左下角入口
        /// </summary>
        BattleSettingType_LeftBottom_Entry = 10,
        /// <summary>
        /// 小小英雄入口
        /// </summary>
        BattleSettingType_TinyHero_Entry = 20,
        /// <summary>
        /// 双人房间入口
        /// </summary>
        BattleSettingType_DaulMatching_Entry = 30,
    }

    public enum AchievementReportType
    {
        ACHIEVEMENT_SYS,
        ACHIEVEMENT_USERINFO_PANEL,
        ACHIEVEMENT_EDIT_PANEL,
        ACHIEVEMENT_RULE_PANEL,
    }
    public enum DestinyReportType
    {
        /// <summary>
        /// 点击大厅命运
        /// </summary>
        DESTINY_SYS_MAINHLL = 39,
        /// <summary>
        /// 点击晶能
        /// </summary>
        DESTINY_SYS_TOGGLE_JINGNENG = 40,
        /// <summary>
        /// 点击晶能——兑换入口
        /// </summary>
        DESTINY_SYS_TOGGLE_JINGNENG_EXCHANGE_CLICK = 41,
        /// <summary>
        /// 点击晶能——奖池
        /// </summary>
        DESTINY_SYS_TOGGLE_JINGNENG_REWARDPOOL = 42,
        /// <summary>
        /// 点击至臻
        /// </summary>
        DESTINY_SYS_TOGGLE_PERFECT = 43,
        /// <summary>
        /// 点击联盟
        /// </summary>
        DESTINY_SYS_TOGGLE_SEASONLOTTERY = 44,
        /// <summary>
        /// 点击联盟——奖池
        /// </summary>
        DESTINY_SYS_TOGGLE_SEASONLOTTERY_REWARDPOOL = 45,
        /// <summary>
        /// 晶能打开
        /// </summary>
        DESTINY_SYS_PANEL_OPEN_JIGNENG = 80,
        /// <summary>
        /// 至臻打开
        /// </summary>
        DESTINY_SYS_PANEL_OPEN_PERFECT = 81,
        /// <summary>
        /// 联盟打开
        /// </summary>
        DESTINY_SYS_PANEL_OPEN_ALLAY = 82,

    }

    public enum UserInfoWeekkyReportType
    {
        //左侧点击页签
        ClickToggle = 10,
        //点击生成周报
        ClickProduceWeeklyReport = 20,
    }

    public enum CollegeReportType
    {
        // 点击基础教学页签
        ClickBasicTeach = 10,
        // 点出学院二选一入口
        ClickGuideEntryPanel = 20,
        // 点击学院新手
        ClickCollegeRookieBattle = 30,
        // 点击学院大神
        ClickCollegeMasterBattle = 40,
        // 完成学院新手
        CompleteCollegeRookieBattle = 50,
        // 完成学院大神
        CompleteCollegeMasterBattle = 60,

        // 点击学院图鉴
        ClickCollegeHandBook = 70,
        // 点击学院微社区阵容推荐
        ClickComCollegeLineUp = 80,

    }

    public enum BattleEndReportType
    {
        //赛后收藏他人阵容
        BattleEndClickCollectOtherLineUp = 1,
    }
    public enum BattlePassReportType
    {
        Enter_BP_Main = 0,
        Enter_BP_Zhizhen = 1,
        Enter_BuyBp_By_Preview = 2,
        BuyBp_By_Preview = 3,
        Zhizhen_Exchange_Preview_Click = 4,
        Open_BuyBp_Preview = 5,
    }

    public enum CollegeTeamRecommend
    {
        //点击学院阵容收藏
        ClickCollegeTeamRecommend = 0,

        //点击学院阵容收藏的第一套阵容详情
        ClickCollegeTeamRecommendItem1 = 1,
        //点击学院阵容收藏的第二套阵容详情
        ClickCollegeTeamRecommendItem2 = 2,
        //点击学院阵容收藏的第三套阵容详情
        ClickCollegeTeamRecommendItem3 = 3,



        //编辑学院阵容收藏的第一套阵容
        ClickEditCollegeTeamRecommendPreview1 = 11,
        //编辑学院阵容收藏的第二套阵容
        ClickEditCollegeTeamRecommendPreview2 = 12,
        //编辑学院阵容收藏的第三套阵容
        ClickEditCollegeTeamRecommendPreview3 = 13,


        //保存学院阵容收藏的第一套阵容
        ClickSaveCollegeTeamRecommendPreview1 = 21,
        //保存学院阵容收藏的第二套阵容
        ClickSaveCollegeTeamRecommendPreview2 = 22,
        //保存学院阵容收藏的第三套阵容
        ClickSaveCollegeTeamRecommendPreview3 = 23,
    }

    /// <summary>
    /// 好友子页面
    /// 经分上报用
    /// </summary>
    public enum FriendReportType
    {
        /// <summary>
        /// 好友推荐页
        /// </summary>
        RecommendTabView = 1,

        /// <summary>
        /// 游戏知几
        /// </summary>
        GRobot
    }

    public enum SkillDescCalType
    {
        ORIGIN = 0,
        PHYSICS = 1,
        SUPERNATURAL = 2,
        PHYSICS_PERCENT = 3,
        SUPERNATURAL_PERCENT = 4,
        PHYSICS_ORIGIN = 5,
        SUPERNATURAL_SYMBOL = 6,
        PHYSICS_SYMBOL = 7,
        PHYSICS_SUPERNATURAL = 8,
        SUPERNATURAL_ADD = 9,
        SUPERNATURAL_MUL_TIMES = 10,
        PHYSICS_SUPERNATURAL_CRIT = 11,
        SUM_SUPERNATURAL_EXTRA_PHYSICS_PHYSICS_SYMBOL = 12,
        SUM_SUPERNATURAL_EXTRA_PHYSICS_SUPERNATURAL_SYMBOL = 13,
        PHYSICS_PERCENT_AND_SUPERNATURAL_PERCENT_PHYSICS_SYMBOL = 14,
        PHYSICS_PERCENT_AND_SUPERNATURAL_PERCENT_SUPERNATURAL_SYMBOL = 15,
        NUMBER_EXTRA_PROJECTILE_BY_ATKSPEED = 16,
        PHYSICS_PERCENT_AND_EXTRA_PHYSICS_PHYSICS_SYMBOL = 17,
        PHYSICS_PERCENT_AND_ATKSPEED_PERCENT_PHYSICS_AND_ATKSPEED_SYMBOL = 18,
        PHYSICS_PERCENT_DEFENCE_AND_SHIELD_SYMBOL = 19,
        PHYSICS_PERCENT_AND_EXTRA_SUPERNATURAL_AND_PHYSICS_SYMBOL = 20,
        SUPERNATURAL_AND_LIFE_PERCENT_AND_SUPERNATURAL_SYMBOL = 21,
        LIFE_SYMBOL = 22,
        SUPERNATURAL_PERCENT_AND_SUPERNATURAL_PERCENT_AND_SUPERNATURAL_SYMBOL = 23,
        SUPERNATURAL_DEFENCE_SYMBOL = 24,
        GOLD_COIN_SYMBOL = 25,
        STAR_SYMBOL = 26,
        SUPERNATURAL_PERCENT_AND_EXTRA_PERCENT_AND_SUPERNATURAL_SYMBOL = 27,
        BUFF_RECORD_AND_PARAMS = 28,//
        SUPERNATURAL_AND_LIFE_PERCENT_AND_SUPERNATURAL_LIFE_SYMBOL = 29,
        SUPERNATURAL_PERCENT_AND_MAX_MP = 30,
        MP_SYMBOL = 31,
        ARMOR_AND_MAGICRESIST_PERCENT_AND_PHYSICS_PERCENT = 32,
        ARMOR_SYMBOL = 33,
        SUPERNATURAL_PERCENT_AND_LIFE_PERCENT_LIFE_SYMBOL = 34,
        SUPERNATURAL_PERCENT_CEIL = 35,
        SUPERNATURAL_DEFENCE_PERCENT_AND_SUPERNATURAL_PERCENT = 36,
        ATKSPEED_PERCENT_CEIL_AND_ATKSPEED_SYMBOL = 37,
        LIFE_PERCENT_AND_LIFE_SYMBOL = 38,
        PHYSICS_PERCENT_AND_ATKSPEED_PERCENT_AND_PHYSICS_SYMBOL = 39,
        ARMOR_PERCENT_AND_SUPERNATURAL_PERCENT = 40,
    }

    //一些需要额外实现的局内实时战场计数器描述展示
    public enum LIVE_COUNTER_INFO
    {
        BARD = 0,//BARD音符数计数
        LAGOON = 1,//碧波龙施法及奖励计数
        STARCOUNT = 2,//总星级数，分为去重和不去重两种
        SUPERS_DAMAGE_INCREASE = 3, //超能战队增伤，固定参数加参数*三星英雄个数
        FOREVER_RECORD, //全局变量相关
        UNDERGROUND, //地底世界进度
    }

    public enum EQUIP_LIVE_COUNTER_INFO
    {
        RECORD_BATTLE_DATA = 0, 
        RECORD_BATTLE_DATA_PLUS = 1,//多个recordbattledata相加
    }


    public enum SkillDescCalNumType
    {
        INT = 0,
        FLOAT = 1,
    }

    /// <summary>
    /// 界面和经分上报类型
    /// </summary>
    public enum CardCollectReportType
    {
        OpenCardCollect = 0,   //打开魔典系统
        OpenDetail = 1,   //打开徽章详情
        OpenReward = 2,   //打开赛季奖励
        OpenRewardPreview = 3,   //打开奖励预览
        OpenRank = 4,   //打开排行榜
        RankStayTime = 5,   //排行榜界面停留时长
    }

    public static class ChessUtil
    {
        public static bool Unload_Ab = false;
        public static bool Optimized_Round_Select_Effect = false;
        private const int SKILL_DESC_NUM_TYPE_DIVISOR = 100;
        public static string Model_Path = "art_tft_raw/hero/hero_show/";
        public const string COS_Model_Path = "art_tft_raw/hero/hero_show_";
        static string[] _colorRace = new string[]
           {
                "",
                "#09380b",	// 1 精灵
                "#00343a",	// 2 龙
                "#3e291e",	// 3 人类
                "#182d26",	// 4 野兽
                "#2f1043",	// 5 恶魔
                "#320e21",	// 6 元素
                "#33240f",	// 7 矮人
                "#072846",	// 8 海民
                "#361c0c",	// 9 机械
                "#0a302f",	// 10 人造人
                "#00343a",	// 11 娜迦
                "#3a0e0f",	// 12 兽人
                "#1e2136",	// 13 亡灵
                "#402918",  // 14 熊猫
                "#292501",  // 15 骷髅
           };


        static string[] m_nameColor = new string[]
            {
                        "#a9a6a6",
                        "#a9a6a6",
                        "#9697d4",
                        "#7b7eff",
                        "#d246d7",
                        "#de9034",
            };

        public enum ModelLod
        {
            None,
            Low,
            High,
            Pad,
        }

        // GM命令设置的模型LOD
        public static ModelLod GlobalModelLod = ModelLod.None;

        //返回种族名
        public static string GetRace(int raceId)
        {
            {
                return "";
            }
        }


        //返回职业名
        public static string GetCareer(int career)
        {
            {
                return "";
            }
        }



        private static string[] lvlImgColors = new string[] { "#5A3B09", "#95BCC7", "#FABE0A", "#cf83ff", "#ffc54b" };


        public static void SplitAssetPath(string path, out string ab_path, out string ab_name)
        {
            FastStringSplit strs = path.BeginSplit('|' );
            if (strs.Length == 2)
            {
                ab_path = strs[0];
                ab_name = strs[1];
            }
            else
            {
                ab_path = "";
                ab_name = "";

                //Diagnostic.Error("SplitAssetPath: " + path);
            }

            strs.EndSplit();

            if (LocalizationData.Instance.GetCurrentLanguage() != SystemLanguage.Chinese)
            {
                if (ab_path.Contains("_cn") && ab_name.Contains("zh_cn_"))
                {
                    ab_path = ab_path.Replace("_cn", "_en");
                    ab_name = ab_name.Replace("zh_cn_", "");
                }
            }
        }

       
        //获取当前点触的屏幕坐标
        public static bool GetTouchPos(out Vector3 touchPos)
        {
            if (Input.touchCount > 0)
            {              
                var input = Input.GetTouch(0);
                if (input.phase == TouchPhase.Began)
                {
                    touchPos = input.position;
                    return true;
                }
            }

            touchPos = Vector3.zero;

            return false;
        }

        //获取当前点触的屏幕坐标
        public static bool GetTouchPosForVector2(out Vector2 touchPos)
        {
            if (Application.isEditor)
            {
                if (Input.GetMouseButtonDown(0))
                {
                    touchPos = Input.mousePosition;
                    return true;
                }
            }
            else if (Input.touchCount > 0)
            {
                var input = Input.GetTouch(0);
                if (input.phase == TouchPhase.Began)
                {
                    touchPos = input.position;
                    return true;
                }
            }

            touchPos = Vector2.zero;

            return false;
        }

        /// <summary>
        /// 是否点击了refGO外部屏幕地方
        /// </summary>
        /// <param name="refGO"></param>
        /// <returns></returns>
        public static bool IsTouchOut(RectTransform refRect)
        {
            Vector3 touch_pos;
            if (GetTouchPos(out touch_pos) && refRect != null)
            {
                Vector2 screen_pos = new Vector2(touch_pos.x, touch_pos.y);
                if (TKRectTransformUtil.RectangleContainsScreenPoint(refRect, screen_pos, Camera.main))
                {
                    return false;
                }
                return true;
            }
            return false;
        }

        public static bool IsTouchOutByUICamera(RectTransform refRect)
        {
            Vector3 touch_pos;
            if (GetTouchPos(out touch_pos) && refRect != null)
            {
                Vector2 screen_pos = new Vector2(touch_pos.x, touch_pos.y);
                if (TKRectTransformUtil.RectangleContainsScreenPoint(refRect, screen_pos, Services.GetService<TKCameraManager>().UICamera))
                {
                    return false;
                }
                return true;
            }
            return false;
        }

        //删除所有的子节点
        public static void DestroyChildren(Transform root)
        {
            if (root == null)
                return;

            foreach (Transform child in root)
            {
                if (child.gameObject)
                    GameObject.Destroy(child.gameObject);
            }
        }

        public static void CopyTransform(Transform obj, Transform other)
        {
            obj.localPosition = other.localPosition;
            obj.localRotation = other.localRotation;
            obj.localScale = other.localScale;
        }

        public static void MakeTransformIdentity(Transform node)
        {
            node.localPosition = Vector3.zero;
            node.localRotation = Quaternion.identity;
            node.localScale = Vector3.one;
        }

        private static bool GetHeroAssetPath(ref string model_path, string suffix, string model_name, bool fromCos)
        {
            string ab = string.IsNullOrEmpty(suffix) ? model_path : model_path + suffix;
            //if (!fromCos)
            {
                if (AssetBundleManager.CheckAssetExist(ab, model_name))
                {
                    model_path = ab;
                    return true;
                }
            }
            return false;
        }

        private static bool GetHeroAssetPathFromList(ref string model_path, List<string> suffixs, string model_name, bool fromCos)
        {
            for (int i = 0; i < suffixs.Count; ++i)
            {
                if (GetHeroAssetPath(ref model_path, suffixs[i], model_name, fromCos))
                {
                    return true;
                }
            }
            return false;
        }

        private static bool GetHeroAssetPathFromList(ELoadResMode loadModel, ref string model_path, List<string> suffixs, string model_name, out bool fromCos)
        {
            if (loadModel == ELoadResMode.COSFirst)
            {
                // 优先从COS加载 其次从本地加载
                if (GetHeroAssetPathFromList(ref model_path, suffixs, model_name, true))
                {
                    fromCos = true;
                    return true;
                }
                if (GetHeroAssetPathFromList(ref model_path, suffixs, model_name, false))
                {
                    Diagnostic.Warn("load model from local! model_path: {0} model_name: {1}", model_path, model_name);
                    fromCos = false;
                    return true;
                }
            }
            else if (loadModel == ELoadResMode.LocalFirst)
            { 
                // 优先从本地加载 其次从COS加载
                if (GetHeroAssetPathFromList(ref model_path, suffixs, model_name, false))
                {
                    fromCos = false;
                    return true;
                }
                if (GetHeroAssetPathFromList(ref model_path, suffixs, model_name, true))
                {
                    Diagnostic.Warn("load model from cos! model_path: {0} model_name: {1}", model_path, model_name);
                    fromCos = true;
                    return true;
                }
            }
            else if (loadModel == ELoadResMode.OnlyCOS)
            {
                if (GetHeroAssetPathFromList(ref model_path, suffixs, model_name, true))
                {
                    fromCos = true;
                    return true;
                }
            }
            else if (loadModel == ELoadResMode.OnlyLocal)
            {
                if (GetHeroAssetPathFromList(ref model_path, suffixs, model_name, false))
                {
                    fromCos = false;
                    return true;
                }
            }
            fromCos = false;
            return false;
        }

        // 无后缀说明这个英雄没有lod
        private static List<string> ms_highSuffixList = new List<string>() { "_high", "_low", "" };
        private static bool GetHighHeroAssetPath(ELoadResMode loadModel, ref string model_path, string model_name, out bool fromCos)
        {
            return GetHeroAssetPathFromList(loadModel, ref model_path, ms_highSuffixList, model_name, out fromCos);
        }

        private static List<string> ms_lowSuffixList = new List<string>() { "_low", "_high", "" };
        private static bool GetLowHeroAssetPath(ELoadResMode loadModel, ref string model_path, string model_name, out bool fromCos)
        {
            return GetHeroAssetPathFromList(loadModel, ref model_path, ms_lowSuffixList, model_name, out fromCos);
        }

        private static List<string> ms_padSuffixList = new List<string>() { "_pad", "_high", "_low", "" };
        private static bool GetPadHeroAssetPath(ELoadResMode loadModel, ref string model_path, string model_name, out bool fromCos)
        {
            return GetHeroAssetPathFromList(loadModel, ref model_path, ms_padSuffixList, model_name, out fromCos);
        }

        public static bool GetHeroAssetPath(ref string model_path, string model_name, out bool fromCos)
        {
            return GetHeroAssetPath(ChessBattleGlobal.Instance.LoadResFromCos ? ELoadResMode.COSFirst : ELoadResMode.LocalFirst, ref model_path, model_name, out fromCos);
        }

        public static ELoadResMode GetTinyLoadMode(int id)
        {
            //var tinyCfg = DataBaseManager.Instance.SearchTinyHero(id);
            //if (tinyCfg != null)
            //{
            //    var baseCfg = DataBaseManager.Instance.SearchTinyHero(tinyCfg.iConditionId);
            //    if (baseCfg != null && baseCfg.iResLoadSource != (int)ResLoadSourece.FromPackage)
            //        return ELoadResMode.COSFirst;
            //}
            return ELoadResMode.LocalFirst;
        }


        /// <summary>
        /// 获取小小英雄的路径
        /// </summary>
        /// <param name="inGame">是否是局内低模小小英雄</param>
        /// <param name="model_path"></param>
        /// <param name="model_name"></param>
        /// <param name="fromCos"></param>
        /// <returns></returns>
        public static bool GetLittleLegendAssetPath(ref string model_path, string model_name, out bool fromCos, ELoadResMode resMode = ELoadResMode.COSFirst)
        {
            if (resMode == ELoadResMode.COSFirst)
            {
                //if (COS.SettingPrefs.GetInt(TKFrame.COSResource.COSSettingPrefs.COS_BUNDLE_ENABLE, 1) != 1)     // 如果没开 就用本地加载
                {
                    resMode = ELoadResMode.LocalFirst;
                }
            }
            //ELoadResMode resMode = ELoadResMode.LocalFirst;
            //if (forceUseCos)
            //    resMode = ELoadResMode.OnlyCOS;
            //else if (!inGame && COS.SettingPrefs.GetInt(TKFrame.COSResource.COSSettingPrefs.COS_BUNDLE_ENABLE, 1) == 1) // 局外&开启COS加载，优先用COS上的
            //    resMode = ELoadResMode.COSFirst;
            //else if (inGame)    // 局内只用本地的
            //    resMode = ELoadResMode.OnlyLocal;

            return GetHeroAssetPath(resMode, ref model_path, model_name, out fromCos);
        }

        public static bool GetHeroAssetPath(ELoadResMode loadModel, ref string model_path, string model_name, out bool fromCos)
        {
//#if UNITY_EDITOR
//            if (ChessTrainBattleStage.IsTrainBattleMode && AssetBundleManager.SimulateAssetBundleInEditor)
//            {
//                fromCos = false;
//                return false;
//            }
//#endif
            switch (GlobalModelLod)
            {
                case ModelLod.None:  // 正常对局进入的话 走这里
                    {
                        if (HardwareCheck.GetMemoryGB() <= 2.1f)
                        { // 小于等于2G的机器 就不要用高模了 老老实实低模
                            return GetLowHeroAssetPath(loadModel, ref model_path, model_name, out fromCos);
                        }
                        else if (NotchSizeImp.IsPad)
                        {
                            return GetPadHeroAssetPath(loadModel, ref model_path, model_name, out fromCos);
                        }
                        else
                        {
                            var dp = GameLOD.Instance.DevicePower;
                            if (dp == EDevicePower.EDP_Low || dp == EDevicePower.EDP_Middle)
                            {
                                return GetLowHeroAssetPath(loadModel, ref model_path, model_name, out fromCos);
                            }
                            else
                            {
                                return GetHighHeroAssetPath(loadModel, ref model_path, model_name, out fromCos);
                            }
                        }
                    }
                case ModelLod.Low:
                    return GetLowHeroAssetPath(loadModel, ref model_path, model_name, out fromCos);
                case ModelLod.High:
                    return GetHighHeroAssetPath(loadModel, ref model_path, model_name, out fromCos);
                case ModelLod.Pad:
                    return GetPadHeroAssetPath(loadModel, ref model_path, model_name, out fromCos);
                default:
                    break;
            }
            fromCos = false;
            return false;
        }

        /// <summary>
        /// 获取英雄的模型AB资源路径
        /// </summary>
        public static void GetHeroAssetPath(int tabId, out string model_path, out string model_name, int playerId, out bool fromCos)
        {
            TACG_Hero_Client heroTab = DataBaseManager.Instance.SearchACGHero2(tabId);
            if (heroTab == null)
            {
                model_name = "";
                model_path = Model_Path;
                fromCos = false;
                return;
            }

            string modelName = GetHeroSkinModelName(heroTab.sPrefabShowID, tabId, playerId);

            int setId = heroTab != null ? heroTab.iSetNum : 1;
            string modelBasePath = Model_Path + ResPath.GetSetVersion(modelName, setId) + modelName.ToLower();

            GetHeroAssetPath(ChessBattleGlobal.Instance.LoadResFromCos ? ELoadResMode.COSFirst : ELoadResMode.LocalFirst, ref modelBasePath, modelName, out fromCos);

            model_name = modelName;
            model_path = modelBasePath;
        }


        /// <summary>
        /// 获取皮肤 假如有
        /// </summary>
        /// <param name="modelName"></param>
        /// <param name="tabId"></param>
        /// <param name="unit"></param>
        /// <returns></returns>
        public static string GetHeroSkinModelName(string modelName, int tabId, int playerId)
        {
            //通过 chairid 获取这个model 是否有皮肤
            //if (playerId != -1)
            //{
            //    var playerModel = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(playerId);
            //    if (playerModel != null)
            //    {
            //        modelName = playerModel.GetHeroShowModel(modelName, tabId);
            //    }
            //}

            return modelName;
        }

        public static void MakeFullRectTransform(RectTransform node)
        {
            node.anchorMin = Vector2.zero;
            node.anchorMax = Vector2.one;
            node.offsetMin = Vector2.zero;
            node.offsetMax = Vector2.zero;
        }

        public static void MakeFullRectTransform(Transform node)
        {
            RectTransform rc_node = node as RectTransform;
            MakeFullRectTransform(rc_node);
        }


        /// <summary>
        /// 控制相机的显示层
        /// </summary>
        public static void SetLayerVisible(int layer, bool visible)
        {
            if (visible)
                Camera.main.cullingMask |= (1 << layer);
            else
                Camera.main.cullingMask &= ~(1 << layer);

            //Debug.Log("AddLayerVisible: " + (1 << layer));
        }


        // 是否是那些低端设备 这些设备只能加载低配机专属prefab
        public static bool IsLowDevice()
        {
            DeviceSettingPerformer gs = Services.GetService<DeviceSettingPerformer>();
            bool loadLowRes = gs == null || gs.RecommendDevicePower == EDevicePower.EDP_Low || HardwareCheck.IsLowAndroidDevice || HardwareCheck.IsLowIOSDevice;
            return loadLowRes;
        }
        public static bool IsSeaLowDevice()
        {
            return IsLowDevice() && HardwareCheck.GetMemoryGB() < 1.5f;
        }

        public static void LoadFxBattleInteractEmoji(string assetName, Transform parent, Action<GameObject> cb)
        {

        }

        /// <summary>
        /// 加载英雄相关的特效
        /// </summary>
        public static void LoadTFTEffect(string path, string assetName, Transform parent, Action<GameObject> cb, bool autoLife = true)
        {

        }

        /// <summary>
        /// 加载英雄相关的特效
        /// </summary>
        public static LoadedAsset LoadTileEffect(string path, string assetName, out AssetLoadCallback<GameObject> outCb, AssetLoadCallback<GameObject> callBack = null, IReleaseList container = null)
        {
            return ResourceUtil.LoadAssetSmooth<GameObject>(path, assetName, callBack, out outCb, container);
        }

        /// <summary>
        /// 加载相关的特效(path格式如：ChessArt/HeroEffect|zzq_skill_yaoli)
        /// </summary>
        public static LoadedAsset LoadFxEffect(string path, AssetLoadCallback<GameObject> callBack = null)
        {
            string fx_path;
            string fx_name;
            ChessUtil.SplitAssetPath(path, out fx_path, out fx_name);

            AssetLoadCallback<GameObject> outCb;
            return ResourceUtil.LoadAssetSmooth<GameObject>(fx_path, fx_name, callBack, out outCb);
        }

        /// <summary>
        /// 加载英雄模型
        /// cb: 应用层传递过来的call back
        /// outCb: 真正用到的call back，如果需要cancel call back的话需要保存这个值，然后应用到cancel里面
        /// </summary>
        public static LoadedAsset LoadHeroAsset(int tabId, AssetLoadCallback<GameObject> cb, out AssetLoadCallback<GameObject> outCb, IReleaseList container = null)
        {
            string model_path;
            string model_name;
            GetHeroAssetPath(tabId, out model_path, out model_name, -1, out bool fromCos);      // todo 目前是魔典用 暂时不需要处理fromCos 因为他只展示当前赛季的英雄模型

            return ResourceUtil.LoadAssetSmooth<GameObject>(model_path, model_name, cb, out outCb, container);
        }


        public static uint PlayWwiseBankByPath_OutSource(string bank_name, string event_name, GameObject playGameObject,
           Action<string, uint> cb = null)
        {
            return PlayWwiseBankByPath(bank_name, event_name, playGameObject, cb);
        }

        public static uint PlayWwiseBankByPath(string bank_name, string event_name, GameObject playGameObject,
            Action<string, uint> cb = null, AkCallbackManager.EventCallback eventCallBack = null)
        {
            //Diagnostic.Error("## PlayWwiseBankByPath [" + bank_name + "] " + event_name);
            try
            {
                if (!string.IsNullOrEmpty(bank_name) && !string.IsNullOrEmpty(event_name))
                {
                    WwisePlayBankData playBankData = Chess_WwisePlayManager.instance.GetBankData();
                    playBankData.gameObject = playGameObject;
                    playBankData.wwise_bankName = bank_name;
                    playBankData.wwise_eventName = event_name;
                    Chess_WwisePlayManager.instance.PlayWwiseBank_Public(playBankData, cb, eventCallBack);

                    return playBankData.iPlayingID;
                }
                return 0;
            }
            catch (Exception e)
            {
                Diagnostic.Error("PlayWwiseBankByPath Error:" + e.ToString());
                return 0;
            }
        }


        public static uint PlayHexBankByPath(string event_name, Action<string, uint> cb = null)
        {
            return PlayWwiseBankByPath("UI_Hex", event_name, QQGameSystem.Instance.gameObject, cb);
        }

        public static void StopWwiseSound(uint playingID)
        {
            Chess_WwisePlayManager.instance.StopMusic(playingID);
        }

        public static void PlayBackGroundMusic(string bankName, string strEventName, string srtStateGroup, string strState)
        {
            if (bankName.Length > 0)
            {
                WwisePlayBankData playBankData = Chess_WwisePlayManager.instance.GetBankData();
                playBankData.wwise_bankName = bankName;
                playBankData.wwise_eventName = strEventName;
                playBankData.wwise_stateGroup = srtStateGroup;
                playBankData.wwise_state = strState;
                playBankData.isBackGround = true;
                if (TKPluginAudioPlayDelegate.PlayWwiseBankFunc != null)
                    TKPluginAudioPlayDelegate.PlayWwiseBankFunc(playBankData, null);
            }
        }

        public static void PlayBackGroundMusicCallBack(string bankName, string strEventName, Action<string, uint> cb)
        {
            if (bankName.Length > 0)
            {
                WwisePlayBankData playBankData = Chess_WwisePlayManager.instance.GetBankData();
                playBankData.wwise_bankName = bankName;
                playBankData.wwise_eventName = strEventName;
                playBankData.isBackGround = true;
                if (TKPluginAudioPlayDelegate.PlayWwiseBankFunc != null)
                    TKPluginAudioPlayDelegate.PlayWwiseBankFunc(playBankData, cb);
            }
        }

        private struct SkillBriefInfo
        {
            public string[] valueInfo;
        }
        public static string UnicodeToString(string str)
        {
            string outStr = "";
            if (!string.IsNullOrEmpty(str))
            {
                FastStringSplit strlist = str.Replace("\\", "").BeginSplit('u');
                try
                {
                    for (int i = 1; i < strlist.Length; i++)
                    {
                        //将unicode字符转为10进制整数，然后转为char中文字符
                        outStr += (char) int.Parse(strlist[i], System.Globalization.NumberStyles.HexNumber);
                    }
                }
                catch (FormatException ex)
                {
                    outStr = ex.Message;
                }
                finally
                {
                    strlist.EndSplit();
                }
            }
            return outStr;
        }

        static Color32[] color32s = { new Color32(27, 81, 117, 255), new Color32(136, 82, 47, 255), new Color32(63, 56, 115, 255), new Color32(10, 98, 80, 255) };
        public static Color32 GetTeamColor(int teamId)
        {
            if (teamId >= 0 && teamId < 4)
            {
                return color32s[teamId];
            }

            return new Color32(20, 26, 33, 255);
        }

        static Color32[] color32saphla = { new Color32(27, 81, 117, 234), new Color32(136, 82, 47, 234), new Color32(63, 56, 115, 234), new Color32(10, 98, 80, 234) };
        public static Color32 GetTeamColorWithAlpha234(int teamId)
        {
            if (teamId >= 0 && teamId < 4)
            {
                return color32saphla[teamId];
            }

            return new Color32(20, 26, 33, 234);
        }

        public static string GetTFTTeamHPName(int teamId)
        {
            string temp = string.Empty;
            return temp;
        }

        public static string GetItemQualityName(int quality)
        {
            string name = string.Empty;
            return name;
        }

        /// <summary>
        /// ！！！！！注意！！！！！表现层专用！
        /// 稳定排序
        /// </summary>

        private static Dictionary<int, int> _dicCostLife;
        /// <summary>
        /// turbo模式需要的血量不一样
        /// </summary>
        /// <param name="cfg"></param>
        public static void SetCostLife_Turbo(string cfg)
        {
            if (string.IsNullOrEmpty(cfg)) return;
            if (_dicCostLife == null) _dicCostLife = new Dictionary<int, int>();
            _dicCostLife.Clear();
            var configs = cfg.BeginSplit('|');
            for (int i = 0; i < configs.Length; i++)
            {
                var cs = configs[i].BeginSplit('=');
                _dicCostLife.Add(cs.ParseInt32(0), cs.ParseInt32(1));
                cs.EndSplit();
            }
            configs.EndSplit();
        }

        /// <summary>
        /// 检查挂点的初始化特效配置
        /// </summary>
        /// <param name="showGo"></param>
        /// <param name="chp"></param>
        /// <param name="goLayer"></param>
        public static void CheckInitEffectDataList(GameObject showGo, CharacterHangPoint chp, int goLayer)
        {
            if (chp == null || chp.GetInitEffectDatas() == null)
                return;
            CharacterHangPointUpdate chpUpdate = chp.characterHangPointUpdate;
            CharacterInitEffectData cieData = null;
            var initEffectDataList = chp.GetInitEffectDatas();
            for (int i = 0, len = initEffectDataList.Count; i < len; i++)
            {
                cieData = initEffectDataList[i];
                if (cieData != null && cieData.prefab != null)
                {
                    CharacterHangPointData hd = CharacterHangPoint.GetHangPointData(showGo, cieData.supportHangPointType);
                    if (hd != null && hd.bindTrans != null)
                    {
                        if (chpUpdate == null)
                        {
                            chpUpdate = chp.gameObject.TryGetComponent<CharacterHangPointUpdate>();
                            chp.characterHangPointUpdate = chpUpdate;
                        }
                        Transform effectInstantiateTrans = UnityEngine.Object.Instantiate(cieData.prefab, hd.bindTrans).transform;
                        chpUpdate.m_effects.Add(effectInstantiateTrans);

                        CharacterHangPointUpdate.VisibleData visibleData = new CharacterHangPointUpdate.VisibleData();
                        visibleData.effect = effectInstantiateTrans.gameObject;
                        visibleData.Init(cieData.hideAction);
                        chpUpdate.m_visibleDatas.Add(visibleData);

                        //effectInstantiateTrans.SetParent(hd.bindTrans);
                        effectInstantiateTrans.localScale = Vector3.one;
                        effectInstantiateTrans.localRotation = Quaternion.identity;
                        effectInstantiateTrans.gameObject.layer = goLayer;
                        effectInstantiateTrans.localPosition = hd.pos;
                        foreach (SkinnedMeshRenderer renderer in effectInstantiateTrans.GetComponentsInChildren<SkinnedMeshRenderer>(true))
                        {
                            renderer.gameObject.layer = goLayer;
                        }
                        foreach (ParticleSystemRenderer particleSystem in effectInstantiateTrans.GetComponentsInChildren<ParticleSystemRenderer>(true))
                        {
                            particleSystem.gameObject.layer = goLayer;
                        }
                        //设置scale;
                        if (cieData.needScaleWithCharacter)
                            effectInstantiateTrans.localScale = hd.scale;
                        else
                        {
                            Vector3 scale = effectInstantiateTrans.localToWorldMatrix.lossyScale;
                            effectInstantiateTrans.localScale = new Vector3(Mathf.Abs(scale.x) < 0.000001 ? 1 : 1 / scale.x,
                                Mathf.Abs(scale.y) < 0.000001 ? 1 : 1 / scale.y,
                                Mathf.Abs(scale.z) < 0.000001 ? 1 : 1 / scale.z);
                        }
                        //设置rotation;
                        if (cieData.needRotateWithBone)
                        {
                            effectInstantiateTrans.localEulerAngles = hd.eulerAngles;
                        }
                        else
                        {
                            effectInstantiateTrans.SetParent(chp.transform); //chp.transform.parent
                            effectInstantiateTrans.localEulerAngles = hd.eulerAngles;
                            CharacterHangPointUpdate.HangPointUpdateData hpuData = new CharacterHangPointUpdate.HangPointUpdateData();
                            hpuData.initEffectData = cieData;
                            hpuData.effectTrans = effectInstantiateTrans;
                            //记录信息用于脱离绑骨时用;
                            hpuData.bindTrans = hd.bindTrans;
                            // //拿父旋转的逆
                            // Quaternion quat = Quaternion.Inverse(hd.bindTrans.rotation);
                            // effectInstantiateTrans.rotation = quat;
                            chpUpdate._hangPointUpdateDataList.Add(hpuData);
                        }

                        var locComponent = effectInstantiateTrans.GetComponent<ChessPlayerEffectBindLoc>();
                        if (locComponent != null)
                        {
                            locComponent.enabled = false;
                            locComponent.m_player = showGo.transform;
                            locComponent.enabled = true;
                        }

                        effectInstantiateTrans.gameObject.SetActive(true);
                    }
                }
            }
        }
    }
}

