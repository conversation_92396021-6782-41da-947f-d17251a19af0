using System.Collections;
using System.Collections.Generic;
using TKFrame;
using UnityEngine;

namespace ZGameChess
{
    public class ChessUnitStrikeFlyAction : ChessBattleUnitActionInterface
    {
        enum FlyStage
        {
            None,
            Up,
            Down,
        }

        private FlyStage _stage;

        private float _g;
        private float _duration;
        private float _height;

        //初速度
        private float _v0;

        private float _h0;

        private float _v;

        private float _accum;

        private ChessBattleUnit m_unit;

        public override bool SetAction(string animName, BaseAnimAction baseAction, bool lockAnim, bool forceReplay, params object[] values)
        {
            base.SetAction(animName, baseAction, lockAnim, forceReplay, values);
            _stage = FlyStage.None;

            if(values != null && values.Length != 3)
            {
                Diagnostic.Log("StrikeFly param error");
                return false;
            }
            if (values == null) throw new System.ArgumentNullException(nameof(values));
            float height = (float)values[0];
            float duration = (float)values[1];
            m_unit = (ChessBattleUnit)values[2];

            if (height <= 0f || duration <= 0f)
                return false;

            _height = height / 1000f;
            _duration = duration / 1000f / 2f;

            return true;
        }

        public override void EndAction()
        {
            //m_unit.PlayAnim("hurtup_end");

           
        }

        public override void InterruptAction(bool unlockAnim)
        {
            base.InterruptAction(unlockAnim);

            _stage = FlyStage.None;

            Vector3 pos = m_unit.transform.localPosition;
            pos.y = 0f;
            m_unit.transform.localPosition = pos;
            m_baseAction.StopAction();
        }

        public override bool LoopAction(float deltaTime)
        {
            /*
            if (Input.GetKeyUp(KeyCode.Space))
            {
                StrikeFly(1000, 1f);
            }
            */

            if (_stage == FlyStage.None)
                return true;

            _accum += deltaTime;

            if (_stage == FlyStage.Up)
            {
                float t = _accum;
                // 当前高度
                float y = _h0 + (_v0 - _g * t / 2) * t;

                Vector3 pos = m_unit.transform.localPosition;
                // 到达顶部，开始下降
                if (y <= pos.y)
                {
                    _stage = FlyStage.Down;
                    _accum = 0f;
                    _v0 = 0f;
                    _h0 = y;
                }

                pos.y = y;
                m_unit.transform.localPosition = pos;
            }
            else if (_stage == FlyStage.Down)
            {
                float t = _accum;
                float y = _h0 + (_v0 - _g * t / 2) * t;

                if (y <= 0f)
                {
                    // 到达地面
                    _stage = FlyStage.None;
                    //m_unit.LockAnim(null);
                    //_unit.PlayAnim("hurtup_end");
                    PlayBaseAnim("hurtup_end");
                    y = 0f;
                    //return false;
                    if (m_unit.baseAction.Ended)
                        return false;
                }

                Vector3 pos = m_unit.transform.localPosition;
                pos.y = y;
                m_unit.transform.localPosition = pos;
            }
            return true;
        }

        public override void StartAction()
        {
            // 算出对应的g
            _g = _height * 2 / (_duration * _duration);
            // 算出落地速度，和击飞初始速度
            _v0 = _g * _duration;
            // 累计时间
            _accum = 0f;
            // 当前高度
            _h0 = m_unit.transform.localPosition.y;

            //Diagnostic.Error("_g: " + _g);

            //m_unit.LockAnim("hurtup_start");
            //m_unit.PlayAnim("hurtup_start", 0f);
            PlayBaseAnim("hurtup_start");

            _stage = FlyStage.Up;
        }
    }
}

