using System.Collections;
using System.Collections.Generic;
using TKFrame;
using UnityEngine;

namespace ZGameChess
{
    public class ChessUnitPlayTimeAction : ChessBattleUnitActionInterface
    {
        private float m_lastTime = 0f;
        private string m_anim = string.Empty;
        
        public override bool SetAction(string animName, BaseAnimAction baseAction, bool lockAnim, bool forceReplay, object[] values)
        {
            base.SetAction(animName, baseAction, lockAnim, forceReplay, values);

            if (values == null || values.Length != 2)
            {
                Diagnostic.Log("Play Time param null");
                return false;
            }

            m_anim = (string)values[0];
            m_lastTime = (float)values[1];
            return true;
        }

        public override void EndAction()
        {
        }

        public override void InterruptAction(bool unlockAnim)
        {
            base.InterruptAction(unlockAnim);
            m_lastTime = -1;
            m_baseAction.StopAction();
        }

        public override bool LoopAction(float deltaTime)
        {
            if (m_lastTime > 0)
            {
                m_lastTime -= deltaTime;
                return true;
            }
            else
            {
                return false;
            }
        }

        public override void StartAction()
        {
            PlayBaseAnim(m_anim);
        }
    }
}

