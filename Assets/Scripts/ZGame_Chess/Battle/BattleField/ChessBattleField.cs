using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TKFrame;
using ZGame.Battle;
using TKPlugins;
using ZGameClient;
using UnityEngine.EventSystems;
using ZGame;
using UnityEngine.SceneManagement;
using ACG.Core;
using ZGame.GameSystem;
using ZGame.Level;
using Z_PVE;
using Lucifer.ActCore;
namespace ZGameChess
{
    /// <summary>
    /// 玩家的棋盘战场信息及管理
    /// 包括等候区、战斗区等
    /// </summary>
    public class ChessBattleField : MonoBehaviour, IService
    {
        /// <summary>
        /// 战斗场景 root GameObject。
        /// </summary>
        public GameObject LevelRoot
        {
            get;
            private set;
        }

        /// <summary>
        /// 阵形格子容器
        /// </summary>
        public ChessTileContainer FormationContainer
        {
            get;
            private set;
        }

        /// <summary>
        /// 兵营格子容器
        /// </summary>
        public ChessTileContainer CampContainer
        {
            get;
            private set;
        }

        /// <summary>
        /// 兵营格子容器
        /// </summary>
        public ChessTileContainer EnemyCampContainer
        {
            get;
            private set;
        }

        /// <summary>
        /// 共享等候区格子容器
        /// </summary>
        public ChessTileContainer ShareCampContainer
        {
            get;
            private set;
        }

        private ChessInterestController _chessInterestCrtl;

        ChessBattleUnitServic _battleUnitService;

        public GameObject EquipmentNode;
        public GameObject OppsiteEquipmentNode;

        //当前关注的玩家对局
        PlayerModel _curPlayerModel;
        PlayerModel _curEnemyPlayerModel;


        private List<LoadedAsset> _commonFxLoadList;


        /// <summary>
        /// 查找 缓存场景中的GameObject
        /// </summary>
        /// <param name="services"></param>
        public IEnumerator Initialize(TKFrame.IServiceProvider services)
        {
            ACGEventManager.Instance.AddEventListener(EventType_BattleView.AutoChess_Battle_GameStart, OnGameStart);
            if (services == null)
            {
                LevelRoot = new GameObject("LevelRoot");
            }

            _battleUnitService = ChessBattleGlobal.Instance.BattleUnitService;

            //初始化阵形格子
            var foramtion_node = BattleWorldObject.Instance.WorldRoot.transform.Find("FormationNode");// GameObject.Find("FormationNode");
            FormationContainer = foramtion_node.gameObject.AddComponent<ChessTileContainer>();
            yield return FormationContainer.Init(AreaType.Battle, ChessConst.FORMATION_ROW_COUNT, ChessConst.FORMATION_COL_COUNT);

            //初始化等候区格子
            var camp_node = BattleWorldObject.Instance.WorldRoot.transform.Find("CampNode");
            CampContainer = camp_node.gameObject.AddComponent<ChessTileContainer>();
            yield return CampContainer.Init(AreaType.Wait, ChessConst.WAIT_ROW_COUNT, ChessConst.WAIT_COL_COUNT);

            //初始化敌方等候区格子
            var enemy_camp_node = BattleWorldObject.Instance.WorldRoot.transform.Find("EnemyCampNode");
            if (enemy_camp_node != null)
            {
                EnemyCampContainer = enemy_camp_node.gameObject.AddComponent<ChessTileContainer>();
                yield return EnemyCampContainer.Init(AreaType.EnemyWait, ChessConst.WAIT_ROW_COUNT, ChessConst.WAIT_COL_COUNT);
            }

            //初始化共享等候区格子
            var share_camp_node = BattleWorldObject.Instance.WorldRoot.transform.Find("ShareCampNode");
            if (share_camp_node)
            {
                ShareCampContainer = share_camp_node.gameObject.AddComponent<ChessTileContainer>();
                yield return ShareCampContainer.Init(AreaType.ShareWait, ChessConst.SHARE_WAIT_ROW_COUNT, ChessConst.SHARE_WAIT_COL_COUNT);
            }

            //装备区格子
            EquipmentNode = GameObject.Find("EquipmentNode");
            OppsiteEquipmentNode = GameObject.Find("OppsiteEquipmentNode");
            _chessInterestCrtl = new ChessInterestController();

            var serv = services as BaseStage;
            if (serv != null)
                StartCoroutine(_chessInterestCrtl.Initialize(serv));
            yield break;
        }

        private void OnGameStart(GEvent e)
        {

        }

        public void Release()
        {
            ACGEventManager.Instance.RemoveEventListener(EventType_BattleView.AutoChess_Battle_GameStart, OnGameStart);
            StopAllCoroutines();


            if (_curPlayerModel != null)
            {
                _curPlayerModel.ObserverList -= OnProcessPlayerModelMessage;
            }
        }


        /// <summary>
        /// 切换当前关注的玩家战斗场面
        /// </summary>
        /// <param name="playerId"></param>
        public void SwitchPlayerBattle(int playerId, bool switchBattleMap = true)
        {
            Diagnostic.Log("SwitchPlayerBattle: " + playerId);

            PlayerModel playerModel = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(playerId);


            if (_curPlayerModel != null)
            {
                _curPlayerModel.OnPlayerPropChange -= _chessInterestCrtl.OnMyMoneyChange;
                _curPlayerModel.ObserverList -= OnProcessPlayerModelMessage;

            }

            _curPlayerModel = playerModel;

            _curPlayerModel.OnPlayerPropChange -= _chessInterestCrtl.OnMyMoneyChange;
            _curPlayerModel.OnPlayerPropChange += _chessInterestCrtl.OnMyMoneyChange;
            _curPlayerModel.ObserverList += OnProcessPlayerModelMessage;


            if (switchBattleMap)
                ActiveCurrentBattleFieldMap();

            UpdatePlayerModel(playerModel);

            _chessInterestCrtl.SwitchPlayer(playerModel);

            var playerCtrl = ChessBattleGlobal.Instance.ChessPlayerCtrl;
            if (playerCtrl != null)
            {
                playerCtrl.SwitchPlayerBattle(playerId);
            }

            ResetGridEffect(playerId);
        }


        public void ResetGridEffect(int playerid)
        {

        }

        public void ActiveCurrentBattleFieldMap()
        {
            var battleModel = ChessModelManager.Instance.GetBattleModel();
            {
                PlayerModel curPlayerModel = _curPlayerModel;
                PlayerModel homePlayerModel = curPlayerModel;

                if (homePlayerModel.Playerinfo == null)
                    return;

                if (!curPlayerModel.IsHomeCourt() && curPlayerModel.BattleFiledVsPlayerPair.Key != null)
                {
                    int chaireId = curPlayerModel.BattleFiledVsPlayerPair.Key.isHomeCourt ? curPlayerModel.BattleFiledVsPlayerPair.Key.iChairid : curPlayerModel.BattleFiledVsPlayerPair.Value.iChairid;
                    homePlayerModel = battleModel.GetPlayerModel(chaireId);
                }

                if (!string.IsNullOrEmpty(homePlayerModel.MapName))
                {
                    BattleMapManager.Instance.ActiveByName(homePlayerModel.MapName);
                }
                else
                {
                    int userBuyMapId = homePlayerModel.Playerinfo.stTAC_GameTinyData.iMapId;
                    BattleMapManager.Instance.Active(userBuyMapId);
                }

                if (BattleWorldObject.Instance != null) BattleWorldObject.Instance.SetProjectorEnable(true);
            }

            _chessInterestCrtl.OnMapChanged();
        }

        private void UnitTriggerMapLogicArea(int playerID, ChessPlayerUnit unit)
        {
            if (unit != null)
            {
                unit.TriggerMapLogicArea(true);
            }
        }

        public void SetInterestCrtlEnemy()
        {
            int enemyId = _curPlayerModel.EnemyPlayerID;
            _curEnemyPlayerModel = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(enemyId);
        }

        public void SetListenEnemyOpt(bool bListen)
        {
            if (bListen)
            {
                int enemyId = _curPlayerModel.EnemyPlayerID;
                _curEnemyPlayerModel = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(enemyId);

                _curEnemyPlayerModel.OnPlayerPropChange -= _chessInterestCrtl.OnEnemyMoneyChange;
                _curEnemyPlayerModel.OnPlayerPropChange += _chessInterestCrtl.OnEnemyMoneyChange;
                // _chessInterestCrtl.OnVSEnemeyPlayer(_curEnemyPlayerModel);
            }
            else
            {
                if (_curEnemyPlayerModel != null)
                {
                    _curEnemyPlayerModel.OnPlayerPropChange -= _chessInterestCrtl.OnEnemyMoneyChange;
                    _curEnemyPlayerModel = null;
                }
            }
        }

        private void OnProcessPlayerModelMessage(ObservableMessage msg)
        {
            var msgID = (TAC_GAME_SC_MSG_ID)msg.ID;
            switch (msgID)
            {
                case TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_TURN_START:
                    {

                    }
                    break;
                case TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_TURN_ARRIVE:
                    UpdateEnemyPlayerModel();
                    HideEnemyHeroIfNeed();
                    //CheckChosenFx(false);
                    break;
                case TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_SWITCH_PLAYER:
              
                    break;
                case TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_UPDATE_FETTER_MERGE_HERO:
         
                    break;
                case TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_MOVE_BATTLEGROUND_HERO:
         
                    break;
                case TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_BATTLE_RESULT:

                    break;
                case TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_START_PLAY:
     
                    break;
            }

            if (msg.Name == UICommandId.MapEventMsg && msg.intValue == (int)ChessMapEvent.ChangeMap)
            {
                ActiveCurrentBattleFieldMap();
            }
        }


        /// <summary>
        /// S6 tamu
        /// </summary>
        /// <returns></returns>
        IEnumerator CheckPlayGluttonSound()
        {
            int i = 15;
            while (i > 0)
            {
                i--;
                yield return null;
            }

            var v = UnityEngine.Random.Range(0, 100);
            if (v < 50)
            {
                var m = ChessModelManager.Instance.GetBattleModel();
                if (m.IsMyBattle())
                {

                }
            }
        }



        //private void HideSurroundTile()
        //{
        //    ChessModelManager.Instance.GetChessElementModel().HideSurroundEffect();
        //}

        /// <summary>
        /// 新会合，同步一下战场的所有英雄
        /// </summary>
        /// <param name="playerModel"></param>
        List<int> removeList = new List<int>();
        public void SyncChessBattleUnit(PlayerModel playerModel)
        {

        }

        //清理我方英雄数据
        private void ResetFightingEndUnit(PlayerModel playerModel)
        {

        }

        public void UpdatePlayerModel(PlayerModel playerModel)
        {

        }

        void UpdateFetterMergeHero(int playerId)
        {
           
        }

        private void UpdateCurPlayerModel()
        {

        }

        private void UpdateEnemyPlayerModel()
        {

        }

        private void HideEnemyHeroIfNeed()
        {

        }

        private void UpdateHeroPos()
        {
        }


        private readonly RaycastHit[] m_Results = new RaycastHit[6];

        /// <summary>
        /// 检测当前手指触控位置的格子Tile
        /// </summary>
        public bool CheckTouchedTile(PointerEventData eventData, bool beginDrag, out Vector3 hit_pos, bool tileAndObject = true)
        {
            Vector3 pos;
            if (beginDrag)
            {
                pos = eventData.pressPosition;
            }
            else
            {
                pos = eventData.position;
            }

            pos.x = Mathf.Clamp(pos.x, 0f, (float)Screen.width);
            pos.y = Mathf.Clamp(pos.y, 0f, (float)Screen.height);

            var ray = CameraUtil.ScreenPointToRay(pos, Camera.main);

            hit_pos = Vector3.zero;

            {
                int hitCount = Physics.RaycastNonAlloc(ray, m_Results, 100f, 1 << GameObjectLayer.Hero | 1 << GameObjectLayer.CardRTT);
                if (hitCount > 0)
                {// object first
                    double minDist = Mathf.Infinity;
                    int id = -1;
                    for (int i = 0; i < hitCount; i++)
                    {
                        if (m_Results[i].transform)
                        {
                            var unit = m_Results[i].transform.gameObject.GetComponent<ChessBattleUnit>();
                            if (unit == null)
                            {
                                continue;
                            }

                            var tempUnit = unit as ChessPlayerUnit;
                            if (tempUnit != null)
                            {
                                continue;
                            }

                            Vector3 screenPos = CameraUtil.WorldToScreenPoint(unit.GetColliderPos(), Camera.main);
                            float xx = screenPos.x - pos.x;
                            float yy = screenPos.y - pos.y;
                            double dist = xx * xx + yy * yy;
                            if (dist < minDist)
                            {
                                minDist = dist;
                                id = i;
                            }
                        }
                    }
                    if (id > -1 && m_Results[id].transform)
                    {
                        hit_pos = m_Results[id].point;
                        var unit = m_Results[id].transform.gameObject.GetComponent<ChessBattleUnit>();
                        if (unit)
                        {
                            return true;
                        }
                    }
                }

            }

            return false;
        }

        public int CheckTouchedUnit(PointerEventData eventData, bool beginDrag, out Vector3 hit_pos, out ChessBattleUnit unit)
        {
            Vector3 pos;
            if (beginDrag)
            {
                pos = eventData.pressPosition;
            }
            else
            {
                pos = eventData.position;
            }

            pos.x = Mathf.Clamp(pos.x, 0f, (float)Screen.width);
            pos.y = Mathf.Clamp(pos.y, 0f, (float)Screen.height);

            var ray = CameraUtil.ScreenPointToRay(pos, Camera.main);

            unit = null;
            hit_pos = Vector3.zero;
            int currentLayer = GameObjectLayer.INVALID;

            int hitCount = Physics.RaycastNonAlloc(ray, m_Results, 100f, 1 << GameObjectLayer.Hero | 1 << GameObjectLayer.Scene | 1 << GameObjectLayer.MiniHero |
                                                1 << GameObjectLayer.CardRTT | 1 << GameObjectLayer.EquitMent | 1 << GameObjectLayer.LevelHelper);

            ChessPlayerUnit playerUnit = null;

            if (hitCount > 0)
            {// object first
                double minDist = Mathf.Infinity;
                int id = -1;
                for (int i = 0; i < hitCount; i++)
                {
                    if (m_Results[i].transform)
                    {
                        int layer = m_Results[i].transform.gameObject.layer;
                        // check equipment
                        if (layer == GameObjectLayer.EquitMent)
                        {
                            hit_pos = m_Results[i].point;
                            unit = null;
                            return GameObjectLayer.EquitMent;
                        }
                        // check unit and scene
                        unit = m_Results[i].transform.gameObject.GetComponent<ChessBattleUnit>();

                        //如果是小队长
                        var tempUnit = unit as ChessPlayerUnit;
                        if (tempUnit != null)
                        {
                            playerUnit = tempUnit;
                            continue;
                        }

                        if (unit == null)
                        {
                            // LevelHelper的优先级更加高
                            if (layer == GameObjectLayer.LevelHelper)
                            {
                                hit_pos = m_Results[i].point;
                                currentLayer = GameObjectLayer.LevelHelper;
                            }
                            else if (layer == GameObjectLayer.Scene && currentLayer != GameObjectLayer.LevelHelper)
                            {
                                hit_pos = m_Results[i].point;
                                currentLayer = GameObjectLayer.Scene;
                            }
                            continue;
                        }
                        Vector3 screenPos = CameraUtil.WorldToScreenPoint(unit.GetColliderPos(), Camera.main);
                        float xx = screenPos.x - pos.x;
                        float yy = screenPos.y - pos.y;
                        double dist = xx * xx + yy * yy;
                        //double dist = MathUtil.PointToLine(ray.GetPoint(-1000), ray.GetPoint(1000), unit.GetColliderPos());
                        if (dist < minDist)
                        {
                            minDist = dist;
                            id = i;
                        }
                    }

                }
                if (id > -1 && m_Results[id].transform)
                {
                    ChessBattleUnit temp = m_Results[id].transform.gameObject.GetComponent<ChessBattleUnit>();
                    //if (!temp.Data.IsScenceObj())
                    {
                        unit = temp;
                        return GameObjectLayer.Hero;
                    }

                }
                else if (playerUnit != null)
                {
                    unit = playerUnit;
                    return GameObjectLayer.MiniHero;
                }
                else if (hit_pos != Vector3.zero)
                {
                    if (currentLayer != GameObjectLayer.LevelHelper)
                    {
                        return GameObjectLayer.Scene;
                    }
                    else
                    {
                        return GameObjectLayer.LevelHelper;
                    }
                }
            }

            return GameObjectLayer.INVALID;
        }
    }
}
