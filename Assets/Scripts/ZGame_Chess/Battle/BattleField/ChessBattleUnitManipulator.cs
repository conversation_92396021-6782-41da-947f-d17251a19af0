using ACG.Core;
using GameFramework.FMath;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using TKAction;
using TKFrame;
using TKPlugins;
using UnityEngine;
using UnityEngine.EventSystems;
using Z_PVE;
using ZGame;
using ZGame.Battle;
using ZGame.GameSystem;
using ZGameClient;
using Coroutine = UnityEngine.Coroutine;

namespace ZGameChess
{

    /// <summary>
    /// 英雄操控类（布阵，拖动，点选等）
    /// </summary>
    public partial class ChessBattleUnitManipulator : MonoBehaviour, IPointerDownHandler, IPointerUpHandler, IChessManipulator
    {
        private ChessBattleUnit m_curUnit;
        // 记录拖动至其他战场的Unit
        private ChessBattleUnit m_SavedDragUnit;
        private bool m_dragCaptain = false;

        private PlayerModel _curPlayerModel;

        // 英雄从等待区到等待区的事件通知
        public event Action onHeroMoveToWaitNotifer;

        //英雄选中事件通知
        public event Action<ChessBattleUnit> onSelectBattleUnitNotifer;

        //TODO:
        private ChessBattleUnit _selectedUnit;
        private ChessBattleUnit m_clickedSelUnit = null;

        /// <summary>
        /// 战场随从是否屏蔽操作
        /// </summary>
        public bool BlockBattleManipulator { get; set; }

        public Action onClickSceneEvent;


        public Func<Vector2, bool> CheckSellAction;


        public bool canHideFormationHightlight = true;
        private float m_lastX = -100;
        private float m_lastY = -100;
        private float m_lastSendTime = 0;

        private Vector3 m_LastHitPos = Vector3.zero;


        public bool isOnAttackRangeShow = false;


        public void Initialize(TKFrame.IServiceProvider services)
        {
            BlockBattleManipulator = false;

            ACGEventManager.Instance
                .AddEventListener(EventType_BattleView.AutoChess_Battle_HideDisplayBlackMask, OnHideDisplayBlackMask);
            ACGEventManager.Instance
                .AddEventListener(EventType_BattleView.ChessBattleCameraAnimationEnd, OnBattleCameraAnimationEnd);
            ACGEventManager.Instance.AddEventListener("OnAttackRangeShow", OnAttackRangeShowChange);



            ChessModelManager.Instance.GetBattleModel().ObserverList += OnProcessChessBattleModelMessage;
        }

        public void Update()
        {
            /*
                        if (Input.GetKey(KeyCode.A))
                        {
                            QQGameSystem.Instance.StartCoroutine(PlayStealSkill(recordHeroId, 0));
                        }
                        else if (Input.GetKey(KeyCode.W))
                        {
                            QQGameSystem.Instance.StartCoroutine(PlayStealSkill(recordHeroId, 1));
                        }
                        else if (Input.GetKey(KeyCode.S))
                        {
                            QQGameSystem.Instance.StartCoroutine(PlayStealSkill(recordHeroId, 2));
                        }
                        else if (Input.GetKey(KeyCode.D))
                        {
                            QQGameSystem.Instance.StartCoroutine(PlayStealSkill(recordHeroId, 3));
                        }*/
        }

        void OnBattleCameraAnimationEnd(GEvent e)
        {

        }

        public void ResetDragDeployViewOldTile(ChessBattleUnit Unit)
        {

        }

        private Coroutine m_levelupUnitCor = null;

        public void Release()
        {
            if (m_levelupUnitCor != null)
                StopCoroutine(m_levelupUnitCor);

            ACGEventManager.Instance
                .RemoveEventListener(EventType_BattleView.AutoChess_Battle_HideDisplayBlackMask, OnHideDisplayBlackMask);
            ACGEventManager.Instance
                .RemoveEventListener(EventType_BattleView.ChessBattleCameraAnimationEnd, OnBattleCameraAnimationEnd);
            ACGEventManager.Instance.RemoveEventListener("OnAttackRangeShow", OnAttackRangeShowChange);

            BlockBattleManipulator = false;
            if (_curPlayerModel != null)
            {
                _curPlayerModel.ObserverList -= OnProcessPlayerModelMessage;
                _curPlayerModel = null;
            }

            onHeroMoveToWaitNotifer = null;
            onSelectBattleUnitNotifer = null;


            m_curUnit = null;
            m_SavedDragUnit = null;

            _selectedUnit = null;
            m_clickedSelUnit = null;

            onClickSceneEvent = null;

            ChessModelManager.Instance.GetBattleModel().ObserverList -= OnProcessChessBattleModelMessage;
        }

        public void SwitchPlayerBattle(int playerId)
        {
            int lastPlayerID = 0;
            if (_curPlayerModel != null)
            {
                lastPlayerID = _curPlayerModel.PlayerId;

                _curPlayerModel.ObserverList -= OnProcessPlayerModelMessage;

            }

            _curPlayerModel = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(playerId);

            _curPlayerModel.ObserverList += OnProcessPlayerModelMessage;

            var battleModel = ChessModelManager.Instance.GetBattleModel();

            bool saveDragHero = battleModel != null ? battleModel.IsBattleReadyState() : false;
            // 只有准备阶段才保留当前拖动的英雄
            Reset(true, saveDragHero);

            UpdateDragUnitOnSwitchPlayer(lastPlayerID, playerId);

        }

        public void Reset(bool handleLevelUp = true, bool saveDragHero = false)
        {
            BlockBattleManipulator = false;
            HideFormationHightlight();
        }

        private int recordHeroId = 0;

        private void OnProcessChessBattleModelMessage(ObservableMessage msg)
        {
            switch (msg.Name)
            {
                case ChessBattleModel.STEAL_SKILL:
                    {

                        break;
                    }
                case ChessBattleModel.PLAY_DRAGON_EFF:
                    {

                        break;
                    }
                case ChessBattleModel.DRAGMENCER_CHANGE_SCALE:
                    {
                        break;
                    }
                case ChessBattleModel.DRAGMENCER_CHANGE_EFF:
                    {

                        break;
                    }
                case ChessBattleModel.NOTIFY_EQUIPSTORE_S5_LIST:
  
                    break;
                case PlayerModel.NOTIFY_EQUIPSTORE_CLOSE:
      
      
                    break;
                case UICommandSubName.Notify_Tag_Hero_Data:
                    {

                        break;
                    }
                case UICommandSubName.Notify_Remove_Tag_Hero:
                    {

                        break;
                    }
            }
        }

        //监听购买英雄，英雄上阵，英雄下阵，英雄回收事件
        private void OnProcessPlayerModelMessage(ObservableMessage msg)
        {
            switch (msg.ID)
            {
                case (int)TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_TURN_START://回合开始
                    Reset();
                    break;
                case (int)TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_TURN_ARRIVE://回合抵达
                    {
  

                        // 准备阶段结束时，若处于他人战场，隐藏拖拽英雄
                        HideDragUnit(true);

                        Reset();
                    }
                    break;
                case (int)TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_BUY_HERO://英雄购买
                    {

                    }
                    break;
                case (int)TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_WAIT_HERO_PROMOTION:
                    {

                    }
                    break;
                case (int)TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_UP_HERO:
                    {

                    }
                    break;
                case (int)TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_BUY_HERO_TO_BATTLEGROUND:
                    {

                    }
                    break;
                case (int)TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_DOWN_HERO:
                    {

                    }
                    break;
                case (int)TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_SELL_HERO:
                    {

                    }
                    break;
                case (int)TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_MOVE_BATTLEGROUND_HERO:
                    {

                    }
                    break;
                case (int)TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_MOVE_WAIT_HERO:
                    {

                    }
                    break;
                case (int)TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_INITIATIVE_HERO_PROMOTION:

                    break;
                case (int)TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_HERO_PROMOTION_FINISH:

                    break;
                default:
                    {

                    }
                    break;
            }

            switch (msg.Name)
            {
                case PlayerModel.NOTIFY_STORE_MSG:
                    {

                    }
                    break;
            }
        }

        private bool CanOp(bool beginDrag = false)
        {
            if (!LockStepFightConfig.Normal_Switch)
                return false;
            if (_curPlayerModel == null || beginDrag && _curPlayerModel.IsMyself == false)
                return false;

            return true;
        }
        private void ResetDragFlags()
        {
            m_curUnit = null;
            m_dragCaptain = false;
        }

        private IEnumerator DelayShowTileEffectOnSwitchPlayer(bool selfToEnemy, bool enemyToSelf)
        {

            int delayFrame = 3;
            while (delayFrame > 0)
            {
                --delayFrame;
                yield return null;
            }

        }

        /// <summary>
        /// 上阵拖拽期间切换战场时更新棋子
        /// </summary>
        public void UpdateDragUnitOnSwitchPlayer(int lastPlayerID, int newPlayerID)
        {

        }

        private void HideDragUnit(bool clearUnitInfo = false)
        {

        }

        public void UpdateDragUnitPosition(Vector3 pos)
        {

        }

        public ChessBattleUnit GetDragUnit()
        {
            return m_curUnit;
        }

        public void RecordSavedDragUnit()
        {
            m_SavedDragUnit = m_curUnit;
        }

        public ChessBattleUnit GetSavedDragUnit()
        {
            return m_SavedDragUnit;
        }

        public void OnBeginDragContainer(PointerEventData eventData)
        {
            if (!CanOp(true))
                return;

            //if (m_zoom == null)
            //{
            //    var sceneCamera = ChessBattleGlobal.Instance.MapManager.GetActiveCamera();
            //    m_zoom = sceneCamera.gameObject.GetComponent<MapCameraZoom>();
            //}

            //if (m_zoom != null)
            //{
            //    m_zoom.isDragingHero = true;
            //    m_zoom.enabled = false;
            //}

            //Diagnostic.Log("[OnBeginDragContainer] dragging: {0} useDragThreshold: {1} scrollDelta: {2} clickCount: {3} clickTime: {4} pressPosition: {5} delta: {6} position: {7} pointerId:{8} eligibleForClick: {9} touchCount: {10} frameCount: {11}",
            //    eventData.dragging, eventData.useDragThreshold, eventData.scrollDelta, eventData.clickCount, eventData.clickTime, eventData.pressPosition, eventData.delta, eventData.position, eventData.pointerId, eventData.eligibleForClick, Input.touchCount, Time.frameCount);

            float sw = Input.GetAxis("Mouse ScrollWheel");
            if (Input.touchCount > 1 || sw != 0)
                return;
            // Diagnostic.Log("drag hero unit container begin");


            SelectUnit(null);

            ResetDragFlags();

            Vector3 hitPos;

            ChessBattleGlobal.Instance.BattleField.CheckTouchedTile(eventData, true, out hitPos, true);

            // 不在自己的战场不能控制小小英雄
            if (_curPlayerModel.IsMyself == false)
            {
                return;
            }

            ChessBattleUnit selUnit = null;
            int collisionType = ChessBattleGlobal.Instance.BattleField.CheckTouchedUnit(eventData, true, out hitPos, out selUnit);
            if (collisionType == GameObjectLayer.Scene || collisionType == GameObjectLayer.EquitMent)
            {
                //Diagnostic.Log("Will Move Tiny");
                m_dragCaptain = true;
            }


            // 屏蔽双指缩放
            CameraPanAndZoom.LockControl = true;
        }



        private void OnAttackRangeShowChange(GEvent e)
        {
            isOnAttackRangeShow = e.boolData;
        }


        public void HideAllAttackRange()
        {
            if (!isOnAttackRangeShow)
                return;

        }

        public void OnDragContainer(PointerEventData eventData)
        {
            if (!CanOp())
                return;

            //Diagnostic.Log("[OnDragContainer] dragging: {0} useDragThreshold: {1} scrollDelta: {2} clickCount: {3} clickTime: {4} pressPosition: {5} delta: {6} position: {7} pointerId:{8} eligibleForClick: {9} touchCount: {10} frameCount: {11}",
            //    eventData.dragging, eventData.useDragThreshold, eventData.scrollDelta, eventData.clickCount, eventData.clickTime, eventData.pressPosition, eventData.delta, eventData.position, eventData.pointerId, eventData.eligibleForClick, Input.touchCount, Time.frameCount);

            Vector3 hitPos;
            if (m_dragCaptain)
            {
                bool b = ChessBattleGlobal.Instance.BattleField.CheckTouchedTile(eventData, false, out hitPos, false);
                float cur = Time.time;
                var playerModel = ChessModelManager.Instance.GetBattleModel().GetMyPlayerModel();
                if (b && cur - m_lastSendTime > 0.033f && !playerModel.IsDead())
                {
                    //Diagnostic.Log("Move Tiny by Drag");
                    m_lastSendTime = cur;
                    //MoveCaptain(hitPos);
                    CallMoveCaptain(hitPos, false);
                }
            }
        }

        private void OnHideDisplayBlackMask(GEvent e)
        {

        }

        public void OnEndDragContainer(PointerEventData eventData)
        {

        }

        public void OnPointerDown(PointerEventData eventData)
        {
            OnClickedContainer(eventData);
        }
        public void OnPointerUp(PointerEventData eventData)
        {
            OnClickedUpContainer(eventData);
        }


        private Vector2 _downPos;
        public void OnClickedUpContainer(PointerEventData eventData)
        {
            float len = Mathf.Abs((eventData.position - _downPos).magnitude);
            if (len < 20)
            {

                if (onClickSceneEvent != null)
                {
                    onClickSceneEvent();
                }


                if (onSelectBattleUnitNotifer != null)// && !CheckLongPress())
                {
                    onSelectBattleUnitNotifer(m_clickedSelUnit);
                    SelectUnit(m_clickedSelUnit);
                }
            }
        }

        private Vector3 m_cacheHitPos = Vector3.zero;
        private bool m_isShowMapClick = false;
        private int m_moveCaptainFrameCount = 0;
        private bool m_isMovingCaptain = false;
        private UnityEngine.Coroutine m_moveCaptainCoroutine = null;

        private void CancelMoveCaptain()
        {
            if (m_moveCaptainCoroutine != null)
            {
                StopCoroutine(m_moveCaptainCoroutine);
                m_moveCaptainCoroutine = null;
            }
        }

        private void CallMoveCaptain(Vector3 hitPos, bool showMapClick)
        {
            m_cacheHitPos = hitPos;
            if (showMapClick)
                m_isShowMapClick = true;
            if (m_isMovingCaptain)
            {
                MoveCaptain_Impl();
            }
            else if (m_moveCaptainCoroutine == null || m_moveCaptainFrameCount < Time.frameCount)
            {
                CancelMoveCaptain();
                m_moveCaptainFrameCount = Time.frameCount + 1;
                m_moveCaptainCoroutine = StartCoroutine(DelayMoveCaptain());
            }
        }

        private IEnumerator DelayMoveCaptain()
        {
            // 延迟三帧 避免和缩放地图的操作冲突
            while (m_moveCaptainFrameCount >= Time.frameCount)
                yield return null;

            if (Input.touchCount <= 1)
            {
                m_isMovingCaptain = true;
                MoveCaptain_Impl();
            }
            m_isShowMapClick = false;
            m_moveCaptainCoroutine = null;
        }

        private void MoveCaptain_Impl()
        {
            MoveCaptain(m_cacheHitPos);

            if (m_isShowMapClick)
            {
                m_isShowMapClick = false;
            }

            m_cacheHitPos = Vector3.zero;
        }

        private void MoveCaptain(Vector3 hitPos)
        {
            var battleModel = ChessModelManager.Instance.GetBattleModel();
            var playerId = battleModel.MyPlayerId;
            var playerCtrl = ChessBattleGlobal.Instance.ChessPlayerCtrl;
            if (playerCtrl == null)
                return;
            var playerUnit = playerCtrl.GetPlayerUnit(playerId);
            if (playerUnit != null)
            {
                bool moveEnable = playerUnit.MoveEnable;
                if (!moveEnable)
                    return;
                // 转成本地坐标
                hitPos = playerUnit.transform.parent.InverseTransformPoint(hitPos);
            }

            // 点击同一个地方，尽量不发送
            float distance = Mathf.Pow(m_lastX - hitPos.x, 2) + Mathf.Pow(m_lastY - hitPos.z, 2);
            if (distance > 0.04)
            {
                SendMoveCommand(hitPos);
                m_lastX = hitPos.x;
                m_lastY = hitPos.z;

                //Diagnostic.Log("[Xiaobai] Move Captaion Success");
            }
        }


        protected virtual void SendMoveCommand(Vector3 hitPos)
        {
            ChessBattleModel battleModel = ChessModelManager.Instance.GetBattleModel();
            if (battleModel.IsRoundSelect())
                return;

            int myPlayerId = battleModel.MyPlayerId;
            ChessPlayerUnit playerUnit = ChessBattleGlobal.Instance.ChessPlayerCtrl.GetPlayer(myPlayerId);
            if (playerUnit == null)
                return;

            FVector2 fVector2 = new FVector2(hitPos.x, hitPos.z);
            byte[] movedata = GameUtil.FVector2ToMinCharArray(fVector2);
            ChessOperationInput.WriteInput((byte)ChessOperationType.OPT_CB_MOVE, movedata);
        }

        public void OnClickedContainer(PointerEventData eventData)
        {
            //Diagnostic.Log("[OnClickedContainer] dragging: {0} useDragThreshold: {1} scrollDelta: {2} clickCount: {3} clickTime: {4} pressPosition: {5} delta: {6} position: {7} pointerId:{8} eligibleForClick: {9} touchCount: {10} frameCount: {11}",
            //   eventData.dragging, eventData.useDragThreshold, eventData.scrollDelta, eventData.clickCount, eventData.clickTime, eventData.pressPosition, eventData.delta, eventData.position, eventData.pointerId, eventData.eligibleForClick, Input.touchCount, Time.frameCount);

            // 【【9.16内测】【局内】【战斗】缩放棋盘的时候会使得小小英雄也跟着移动】
            // http://tapd.oa.com/cchess/bugtrace/bugs/view/1020417564082262939
            if (Input.touchCount > 1)
                return;

            Vector3 hitPos;
            int collisionType = ChessBattleGlobal.Instance.BattleField.CheckTouchedUnit(eventData, true, out hitPos, out m_clickedSelUnit);

            ChessPlayerUnit playerUnit = m_clickedSelUnit as ChessPlayerUnit;
            if (playerUnit != null)
            {
                m_clickedSelUnit = null;
                playerUnit.InputHandler.ClickEvtFired = true;
                return;
            }

            {
                //Diagnostic.Log("Move Tiny by Click 2");
                m_isMovingCaptain = false;
                CallMoveCaptain(hitPos, true);
                //MoveCaptain(hitPos);
                //MapClickShow.ShowMapClick(hitPos);
            }
        }

        public void CancelSelect()
        {
            SelectUnit(null);
        }

        private void SelectUnit(ChessBattleUnit unit)
        {
            if (_selectedUnit == unit && unit != null)
            {
                return;//与上次相同的英雄
            }


            if (unit == null || !unit.IsNPC)//野怪不让选中
                _selectedUnit = unit;
        }


        public void HideFormationHightlight(bool bNeedEnemy = false)
        {
            if (canHideFormationHightlight == false)
                return;
        }
    }
}
