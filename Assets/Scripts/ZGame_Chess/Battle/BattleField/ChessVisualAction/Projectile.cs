using System;
using System.Collections.Generic;
using UnityEngine;
using TKFrame;
using ZGame.Battle;
using ZGame.Battle.VisualAction;
using ZGame.GameSystem;

namespace ZGame.Battle.ChessVisualAction
{
    public class Projectile : Actor
    {
        private bool _dead = false;

        public bool Done { get; private set; } = false;

        private Action _ArriveCB = null;

        public event Action ArriveCB
        {
            add
			{
				_ArriveCB = (Action)Delegate.Remove(_ArriveCB, value);
				_ArriveCB = (Action)Delegate.Combine(_ArriveCB, value);
            }

            remove
            {
                _ArriveCB = (Action)Delegate.Remove(_ArriveCB, value);
            }
        }
		
        //起始点
        private Vector3 _startPos;

        //目标点
        private Vector3 _destPos;

        private Transform _destTrans;

        //抛物线的高度
        private float _height;

        //飞行持续的时间
        private float _moveDuration;
        //停留的位置参照物
        private Transform _preStayNode = null;
        //飞行结束后停留的时间
        public float EndStayTime = 0.0f;


        //重力加速度
        float _g;

        //高度变化的初速度
        float _heightV0;

        private float _startTime;

        private EffectPoolBehaviour _effPoolBhv;
        /// <summary>
        /// 是否使用对象池资源
        /// </summary>
        public bool BPooledEffect
        {
            get;
            set;
        }

        // 是否使用真实时间
        public bool BUseRealTime
        {
            get;
            set;
        } = true;
		
		public void InitProperties()
		{
			Done = false;
			_dead = false;
			_ArriveCB = null;
            EndStayTime = 0.0f;
        }

        public static Projectile Create( string name, Transform parent = null)
		{
            GameObject go = null;
            Projectile proj;
            if (go != null)
            {
                go.SetActive(true);
                go.name = name;
                proj = go.GetComponent<Projectile>();
                proj.InitProperties();
            }
            else
            {
                if (string.IsNullOrEmpty(name))
                {
                    go = new GameObject();
                }
                else
                {
                    go = new GameObject(name);
                }
                proj = go.AddComponent<Projectile>();
            }

            if (parent != null)
            {
                go.transform.SetParent(parent, false);
            }

            return proj;
        }

        protected override void Start()
        {
            base.Start();
        }

        public void SetDisplayInfo(string resPath, string geomName)
        {
   //         BattlePooledEffectsContainer pooledEffectsContainer = PooledEffectsConatiner;
   //         if (pooledEffectsContainer != null)
   //         {
   //             pooledEffectsContainer.GetAsynEffect(resPath, geomName, transform, false, delegate (GameObject obj)
   //             {
   //                 if (obj != null)
   //                 {
   //                     _effPoolBhv = obj.GetComponent<EffectPoolBehaviour>();
   //                     obj.transform.localPosition = Vector3.zero;
   //                     obj.transform.localRotation = Quaternion.identity;
   //                     CommonUtil.SetLayerRecursive(obj, ZGameChess.GameObjectLayer.BattleEffect);
   //                 }
   //             });
			//}
        }

        private void RefershGfx()
        {
            if (_effPoolBhv != null)
            {
                var gfxRoot = _effPoolBhv.GetComponent<GfxFramework.GfxRoot_Unity>();
                if (gfxRoot != null)
                {
                    gfxRoot.ResetTrails();
                }
            }
        }

        public void SetUp(Vector3 startPoint, Transform destination, float duration = 0.3f, float height = 0f, float preStayTime = 0.0f, Transform preStayNode = null, bool faceToTarget = false)
        {
            transform.position = startPoint;
            RefershGfx();

            _startPos = startPoint;
            _destTrans = destination;
            _destPos = Vector3.zero;
            _moveDuration = duration - preStayTime;
            if (_moveDuration < 0)
            {
                _moveDuration = 0.001f;
            }
            _height = height;
            _preStayNode = preStayNode;
            _startTime = BUseRealTime ? Time.realtimeSinceStartup : Time.time + preStayTime;
            //设置特效的朝向
            if (faceToTarget)
            {
                Vector3 moveDir = destination.transform.position - startPoint;
                Quaternion moveRotation = moveDir == Vector3.zero ? Quaternion.identity : Quaternion.LookRotation(moveDir);
                this.transform.rotation = moveRotation;
            }

            if (_height > 0.00001f)
            {
                //通过固定的高度推算出重力加速度和上升的初速度
                _g = _height * 2 / ((_moveDuration / 2f) * (_moveDuration / 2f));
                _heightV0 = _g * _moveDuration / 2f;
            }

            //Diagnostic.Log("飞行道具{0} 初始化 开始时间：{1} 飞行时间: {2} 当前位置:{3} 目标位置: {4}", name, _startTime, _moveDuration, _startPos, _destTrans.position);
        }

        public void SetUp(Vector3 startPoint, Vector3 destination, float duration = 0.3f, float height = 0f, float preStayTime = 0.0f, Transform preStayNode=null)
        {
            transform.position = startPoint;
            RefershGfx();

            _startPos = startPoint;
            _destPos = destination;
            _destTrans = null;
            _moveDuration = duration - preStayTime;
            if(_moveDuration < 0)
            {
                _moveDuration = 0.001f;
            }
            _height = height;
            _preStayNode = preStayNode;
            _startTime = BUseRealTime ? Time.realtimeSinceStartup : Time.time + preStayTime;

            //设置特效的朝向
            Vector3 moveDir = destination - startPoint;
            Quaternion moveRotation = moveDir == Vector3.zero ? Quaternion.identity : Quaternion.LookRotation(moveDir);
			
            transform.rotation = moveRotation;

            if (_height > 0.00001f)
            {
                //通过固定的高度推算出重力加速度和上升的初速度
                _g = _height * 2 / ((_moveDuration / 2f) * (_moveDuration / 2f));
                _heightV0 = _g * _moveDuration / 2f;
            }
        }

        private void Update()
        {
			if (Done && !_dead)
			{
				if (_ArriveCB != null)
				{
					_ArriveCB();
				}
				_dead = true;
				
				Recycle();
			}

			if (Done)
                return;

            if (_destTrans != null)
            {
                DynamicTargetUpdate();
                return;
            }

            float accum = BUseRealTime ? Time.realtimeSinceStartup - _startTime: Time.time - _startTime;

            if(accum <= 0)
            {
                if(_preStayNode != null)
                {
                    transform.position = _preStayNode.position;
                    _startPos = _preStayNode.position;
                }
                return;
            }

            if(accum < _moveDuration)
            {
                float percent = accum / _moveDuration;
                Vector3 pos = Vector3.Lerp(_startPos, _destPos, percent);
                
                if (_height > 0.00001f)
                {
                    //如果有高度
                    pos.y += (_heightV0 - _g * accum / 2) * accum;
					
                    Vector3 moveDir = (pos - transform.position).normalized;
                    transform.rotation = Quaternion.LookRotation(moveDir);
                }

                transform.position = pos;
            }
            else if(accum < (_moveDuration+EndStayTime))
            {
                //在目标点停留
                transform.position = _destPos;
            }
            else
            {
                //到达目标点，结束
                transform.position = _destPos;
                //Diagnostic.Log("Done!" + transform.name);
                Done = true;
            }
		}

        private void DynamicTargetUpdate()
        {
            float accum = BUseRealTime ? Time.realtimeSinceStartup - _startTime : Time.time - _startTime;
            if (accum < _moveDuration)
            {
                float percent = accum / _moveDuration;
                Vector3 pos = Vector3.Lerp(_startPos, _destTrans.transform.position, percent);

                if (_height > 0.00001f)
                {
                    //如果有高度
                    pos.y += (_heightV0 - _g * accum / 2) * accum;

                    Vector3 moveDir = (pos - transform.position).normalized;
                    transform.rotation = Quaternion.LookRotation(moveDir);
                }

                transform.position = pos;
            }
            else
            {
                //到达目标点，结束
                transform.position = _destTrans.transform.position;
                //Diagnostic.Log("Done2!" + transform.name + " accum: " + accum + " _moveDuration: " + _moveDuration + " _startTime: " + _startTime);
                Done = true;
            }
        }

        protected override void OnDisable()
        {
            base.OnDisable();
        }
		
		private void Recycle()
		{
			gameObject.SetActive(false);
			gameObject.transform.SetParent(ZGameChess.ChessBattleGlobal.Instance.BattleProjectilePool);
			//if (ZGameChess.ChessBattleGlobal.Instance.ProjectPool != null)
			//	ZGameChess.ChessBattleGlobal.Instance.ProjectPool.Recycle(gameObject);

            //Diagnostic.Log("{0} 回收特效: {1} 父节点:{2}", name, _effPoolBhv.name, _effPoolBhv.transform.parent != null ? _effPoolBhv.transform.parent.name : "无");

            //if (_effPoolBhv != null && PooledEffectsConatiner != null)
            //{
            //    PooledEffectsConatiner.ReleasePooledEffectInst(_effPoolBhv);
            //}
        }

        public void Release()
        {
            Recycle();
        }
    }
}
