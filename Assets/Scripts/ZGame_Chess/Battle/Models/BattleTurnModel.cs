using UnityEngine;
using System.Collections;
using TKPlugins;
using ZGameClient;
using System.Collections.Generic;
using ZGame;
using TK<PERSON>rame;
using Z_PVE;
using System;

namespace ZGameChess
{
    //对战状态
    public enum QuestPlayerStatus
    {
        QUESTPLAYERSTATUS_NONE, //默认
        QUESTPLAYERSTATUS_CURRENT, //当前战斗
        QUESTPLAYERSTATUS_STARTBATTLE, //开始战斗
        QUESTPLAYERSTATUS_BATTLE,  //战斗中
        QUESTPLAYERSTATUS_WIN, //胜利
        QUESTPLAYERSTATUS_FAIL, //失败
        QUESTPLAYERSTATUS_SHOW, //共享
        QUESTPLAYERSTATUS_CURRENTSHOW, //当前共享
    }

    /// <summary>
    /// 顶部栏展示的全局回合数据信息
    /// </summary>
    public class BattleTurnModel : ObservableObject
    {
        public const string BINDNAME_TURNSTAGE = "TurnStage";
        public const string BINDNAME_TURNTIME = "TurnTime";
        public const string BINDNAME_TURNNAME = "TurnName";
        public const string BINDNAME_TURNQUESTPHASE = "TrunQuest";
        public const string BINDNAME_MEMBERCOUNT = "MemberCount";
        public const string BINDNAME_MONEY = "Money";
        public const string BINDNAME_HEROTYPE = "HeroType";
        public const string BINDNAME_MESSANGER_LEVEL = "MessangerLevel";
        public const string BINDNAME_MESSANGER_EXP = "MessangerExp";
        public const string BINDNAME_MESSANGER_ADDEXP = "MessangerAddExp";
        public const string BINDNAME_GAMESTATE = "GameState";
        public const string BINDNAME_ELEMENT_ID = "ElementID";
        public const string BINDNAME_GALAXY_ID = "BINDNAME_GALAXY_ID";
        public const string BINDNAME_EQUIPSTORE_CLOSE = "BINDNAME_EQUIPSTORE_CLOSE";

        public const string HERO_FETTER_CHANGE = "HERO_FETTER_CHANGE";
        public const string UPDATE_QUEST_STATUS = "UPDATE_QUEST_STATUS";

        //游戏状态
        public TAC_GameState _curGameState = TAC_GameState.TAC_GameState_Begin;
        public TAC_GameState CurGameState
        {
            set
            {
                _curGameState = value;
                NotifyUpdate(BINDNAME_GAMESTATE, (int)value);
            }
            get
            {
                return this._curGameState;
            }
        }

        public int LastTurnMoney
        {
            get;
            private set;
        }

        //当前拥有的金币； 
        private int _iMoney;
        public int Money
        {
            set
            {
                _iMoney = value;
                NotifyUpdate(BINDNAME_MONEY, value);
            }
            get
            {
                return this._iMoney;
            }
        }

        //当前占用人口 
        //最大人口 
        public struct TPeople
        {
            public int iCurPeople;
            public int iMaxPeople;
        }
        private TPeople _people = new TPeople();
        public TPeople People
        {
            set
            {
                _people = value;
                NotifyUpdate(BINDNAME_MEMBERCOUNT, value);
            }
            get
            {
                return this._people;
            }
        }

        //回合数           
        private int _iTurnCount;
        public int TurnCount
        {
            set
            {
                _iTurnCount = value;
            }
            get
            {
                return this._iTurnCount;
            }
        }
        
        //轮抽回合数           
        private int _iRoundSelectCount;
        public int RoundSelectCount
        {
            set
            {
                _iRoundSelectCount = value;
            }
            get
            {
                return this._iRoundSelectCount;
            }
        }
        
        public int TurnCountWithoutRoundselect
        {
            get
            {
                return _iTurnCount - _iRoundSelectCount;
            }
        }
        
        //下回合可获得的基础金币数           
        private int _iNextTurnAddMoney;
        public int NextTurnAddMoney
        {
            set
            {
                _iNextTurnAddMoney = value;
            }
            get
            {
                return this._iNextTurnAddMoney;
            }
        }

        //回合准备时间 单位秒                   
        private TTurnTime _tTurnTime;
        public TTurnTime TurnTime
        {
            set
            {
                _tTurnTime = value;
                //ObservableMessage _bindingMessage = GetBindingMessage();
                //Action<ObservableMessage> _onBindingEvent = GetOnBindingEvent();
                //if (_onBindingEvent != null)
                //{
                //    _bindingMessage.valueType = ObservableMessage.ValueType.TTurnTime;
                //    _bindingMessage.ID = 0;
                //    _bindingMessage.Name = BINDNAME_TURNTIME;
                //    _bindingMessage.turnTimeValue = value;
                //    _onBindingEvent(_bindingMessage);
                //}
                NotifyUpdate(BINDNAME_TURNTIME, value);
            }
            get
            {
                return this._tTurnTime;
            }
        }

        private string _turnName;
        public string turnName
        {
            set
            {
                _turnName = value;
                NotifyUpdate(BINDNAME_TURNNAME, value);
            }
            get
            {
                return this._turnName;
            }
        }

        private int _equipStoreTime = 0; 
        public int EquipStoreTime {

            set
            {
                _equipStoreTime = value;
                NotifyUpdate(BINDNAME_EQUIPSTORE_CLOSE, value);
            }
            get
            {
                return _equipStoreTime;
            }
        } 
        
        public int HAStoreTime = 0;

        //回合关卡状态， 输，赢，对战玩家情况
        public class TTurnQuestStatus
        {
            public bool bWin;
            public int EnemyPlayer;
            public string faceUrl;
            public int faceId;
            public TTAC_Quest_Client quest;
            public int deductLife;
        }
        //回合阶段信息(stage所有关卡总信息)
        public class TTurnStage
        {
            public bool StageToggled;
            public int CurStage;
            public int CurQuest;
            public int TurnCount;
            public List<TTurnQuestStatus> questStatusList;

            TTurnQuestStatus GetQuestStatus(int iQuest)
            {
                if (questStatusList == null)
                    return null;
                foreach (var questStatus in questStatusList)
                {
                    if (questStatus == null || questStatus.quest == null) continue;
                    if (questStatus.quest.iRound == iQuest)
                    {
                        return questStatus;
                    }
                }

                return null;
            }

            public void SetEnemyPlayer(int player)
            {
                TTurnQuestStatus questStatus = GetQuestStatus(CurQuest);
                if(questStatus != null) questStatus.EnemyPlayer = player;
            }

            public void SetResult(int iDeductLife, bool win)
            {
                TTurnQuestStatus questStatus = GetQuestStatus(CurQuest);
                if (questStatus != null) {
                    questStatus.bWin = win;
                    questStatus.deductLife = iDeductLife;
                } 
            }

            public void SetFace(string faceUrl, int faceId)
            {
                TTurnQuestStatus questStatus = GetQuestStatus(CurQuest);
                if (questStatus != null)
                {
                    questStatus.faceUrl = faceUrl;
                    questStatus.faceId = faceId;
                }
            }
        }
        private TTurnStage _turnStage;
        public TTurnStage TurnStage
        {
            set
            {
                _turnStage = value;
                NotifyUpdate(BINDNAME_TURNSTAGE, value);
            }
            get
            {
                return this._turnStage;
            }
        }

        //回合关卡环节(Stage中某个关卡状态通知)
        public class TTurnQuestPhase
        {
            //例如：阶段1
            public int stage;
            //例如：备战阶段
            public string questPhaseName;
            //敌人名字
            public TEnemyInfo enemyInfo;
            //状态
            public TAC_GameState state;
            //当前是Stage里面的第几个关卡
            public int curQuest;
            //当前关卡状态
            public QuestPlayerStatus questStatus;
            //关卡失血状况
            public int questDeductLife;
            //敌方被扣血量
            public int enemyDeductLife;
        }
        private TTurnQuestPhase _turnQuestPhase;
        public TTurnQuestPhase TurnQuestPhase
        {
            set
            {
                _turnQuestPhase = value;
                NotifyUpdate(BINDNAME_TURNQUESTPHASE, value);
            }
            get
            {
                return this._turnQuestPhase;
            }
        }

        public class TEnemyInfo
        {
            //敌人名字
            public string EnemyName;
            //玩家ID
            public int EnemyPlayerID;
            //玩家头像
            public string FaceUrl;
            //头像ID
            public int FaceIconId;

            public TEnemyInfo(int playerID)
            {
                this.EnemyName = "";
                this.FaceUrl = "";
                this.FaceIconId = 1;
                this.EnemyPlayerID = playerID;
            }

            public bool IsPlayer()
            {
                return this.EnemyPlayerID >= 0;
            }
        }

        public bool IsLevelChange = false;
        //信使等级
        public struct TMessangerLevel
        {
            public int iLevel;
            public bool bPlayEffect;
        }

        TMessangerLevel _tMessangerLevel;
        public TMessangerLevel MessangerLevel
        {
            set
            {
                _tMessangerLevel = value;
                NotifyUpdate(BINDNAME_MESSANGER_LEVEL, value);
            }
            get
            {
                return this._tMessangerLevel;
            }
        }

        //信使经验
        public struct TMessagerExp
        {
            public int iCurExp;
            public int iNextExp;
            public bool bPlayEffect;
        }
        private TMessagerExp _messagerExp;
        public TMessagerExp MessangerExp
        {
            set
            {
                _messagerExp = value;
                //ObservableMessage _bindingMessage = GetBindingMessage();
                //Action<ObservableMessage> _onBindingEvent = GetOnBindingEvent();
                //if (_onBindingEvent != null)
                //{
                //    _bindingMessage.valueType = ObservableMessage.ValueType.TMessagerExp;
                //    _bindingMessage.ID = 0;
                //    _bindingMessage.Name = BINDNAME_MESSANGER_EXP;
                //    _bindingMessage.messagerExpValue = value;
                //    _onBindingEvent(_bindingMessage);
                //}
                NotifyUpdate(BINDNAME_MESSANGER_EXP, value);
            }
            get
            {
                return this._messagerExp;
            }
        }

        //信使加经验
        public struct TMessagerAddExp
        {
            public int iNeedMoney;
            public int iAddedExp;
        }
        private TMessagerAddExp _messagerAddExp;
        public TMessagerAddExp MessangerAddExp
        {
            set
            {
                _messagerAddExp = value;
                NotifyUpdate(BINDNAME_MESSANGER_ADDEXP, value);
            }
            get
            {
                return this._messagerAddExp;
            }
        }

        private int _elementID = -1;
        public int ElementID
        {
            set
            {
                if (_elementID == -1)
                {
                    _elementID = value;
                    NotifyUpdate(BINDNAME_ELEMENT_ID, value);
                }
            }
            get
            {
                return this._elementID;
            }
        }
        private int _galaxyID = -1;
        public int GalaxyID
        {
            set
            {
                if (_galaxyID != value)
                {
                    _galaxyID = value;
                    NotifyUpdate(BINDNAME_GALAXY_ID, value);
                }
            }
            get
            {
                return this._galaxyID;
            }
        }
        
        public bool BattleEnd
        {
            get; set;
        }

        public struct TTurnTime
        {
            //准备时间
            public int readyTime;
            //战斗时间
            public int playTime;
            //战斗加时时间
            public int fightOverTime;
            //战斗延迟结束时间
            public int fightDelayTime;
            //即将准备时间
            public int prePlayTime;
            //启程环节时间
            public int departureTime;
            //轮转开始时间
            public int roundSelectTimeStart;
            //轮转选择时间
            public int roundSelectTimeChoose;
            //轮转结束时间
            public int roundSelectTimeStop;
            //回合状态
            public TAC_GameState state;

            //获取回合倒计时时间
            public int GetTurnTime(ref float turnTimef)
            {
                return 0;
            }
        }
        public TTurnTime turnTime = new TTurnTime();

        //敌方玩家 
        private int EnemyPlayerID = -1;
        
        private TTurnStage turnStageCache = new TTurnStage();

        private readonly TTurnQuestPhase turnPhaseCache = new TTurnQuestPhase();
        private TTurnQuestPhase GetTurnQuestPhase(TAC_GameState curState, int turnCount)
        {
            //当前阶段信息
            turnPhaseCache.state = curState;
            turnPhaseCache.enemyInfo = GetEnemyInfo(curState, turnCount, EnemyPlayerID);

            //阶段名称
            switch (curState)
            {
                case TAC_GameState.TAC_GameState_Ready:
                    turnPhaseCache.questPhaseName = Localization.Trans("备战环节");
                    turnPhaseCache.questStatus = QuestPlayerStatus.QUESTPLAYERSTATUS_CURRENT;
                    break;
                case TAC_GameState.TAC_GameState_Arrive:
                    turnPhaseCache.questPhaseName = Localization.Trans("战斗环节");
                    break;
                case TAC_GameState.TAC_GameState_Play:
                    turnPhaseCache.questPhaseName = "";
                    break;
                case TAC_GameState.TAC_GameState_FightOverTime:
                    turnPhaseCache.questPhaseName = Localization.Trans("加时环节");
                    break;
                case TAC_GameState.TAC_GameState_FightDelay:
                    break;
                case TAC_GameState.TAC_GameState_Departure:
                    turnPhaseCache.questPhaseName = Localization.Trans("启程环节");
                    break;
                case TAC_GameState.TAC_GameState_RoundSelectStart:
                    //turnPhaseCache.questPhaseName = Localization.Trans("共享选秀");
                    turnPhaseCache.questStatus = QuestPlayerStatus.QUESTPLAYERSTATUS_CURRENTSHOW;
                    break;
                default:
                    turnPhaseCache.questPhaseName = "";
                    break;
            }

            return turnPhaseCache;
        }

        private TPlayerInfo GetPlayerInfo(int id)
        {
            return null;
        }

        public bool IsMonsterLevel()
        {
            return IsMonsterLevel(TurnCount);
        }

        public bool IsMonsterLevel(int turnCount)
        {
            return false;
        }

        private TEnemyInfo GetEnemyInfo(TAC_GameState curState, int tCount, int enemyPlayerID)
        {
            TEnemyInfo enemyInfo = new TEnemyInfo(-1);

            if (curState == TAC_GameState.TAC_GameState_RoundSelectStart)
                return enemyInfo;

            //野怪
            if (IsMonsterLevel())
            {
                enemyInfo.EnemyPlayerID = -1;

                return enemyInfo;
            }
            else //真人玩家
            {
                enemyInfo.EnemyPlayerID = enemyPlayerID;
                int playerID = enemyPlayerID;
                /*if (enemyPlayerID == ChessBattleModel.MIRR_PLAYER_ID)
                {
                    PlayerModel player = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(playerID);
                    if (player != null)
                        playerID = player.RealPlayerId;
                }*/
                TPlayerInfo playerinfo = GetPlayerInfo(playerID);
                if (playerinfo != null)
                {
                    enemyInfo.EnemyName = playerinfo.strName;
                    enemyInfo.FaceUrl = playerinfo.sFaceUrl;
                    enemyInfo.FaceIconId = playerinfo.iconID;
                }
                else if(curState == TAC_GameState.TAC_GameState_Ready)
                {
                    //PVP回合准备阶段显示???
                    enemyInfo.EnemyName = "???";
                }
                return enemyInfo;
            }
        }


        public int _tempTotalDelayTime = 0;
        public void UpdateBattleAllFinished()
        {
            turnTime.state = TAC_GameState.TAC_GameState_FightDelay;
            CurGameState = TAC_GameState.TAC_GameState_FightDelay;
            TurnTime = turnTime;
            TurnQuestPhase = GetTurnQuestPhase(CurGameState, TurnCount);
        }

        public void UpdateArrive(int enemyPlayerID)
        {
            turnTime.state = TAC_GameState.TAC_GameState_Arrive;
            CurGameState = TAC_GameState.TAC_GameState_Arrive;
            TurnTime = turnTime;
            //野怪是-1
            EnemyPlayerID = enemyPlayerID;
            turnStageCache.SetEnemyPlayer(EnemyPlayerID);
            TurnQuestPhase = GetTurnQuestPhase(CurGameState, TurnCount);
            turnStageCache.SetFace(TurnQuestPhase.enemyInfo.FaceUrl, TurnQuestPhase.enemyInfo.FaceIconId);
        }

        public void UpdateGameEnd()
        {
            CurGameState = TAC_GameState.TAC_GameState_Finish;
        }


        //设置当前人口数
        public void SetCurrentPeople(int count)
        {
            People = new TPeople
            {
                iCurPeople = count,
                iMaxPeople = _people.iMaxPeople,
            };
        }
        void SetTurnQuestStatus(QuestStatusData questStatusData, List<TTurnQuestStatus> turnQuestStatusList)
        {
            foreach (var turnQuestStatus in turnQuestStatusList)
            {
                if (turnQuestStatus.quest.iTurnCount == questStatusData.turnCount)
                {
                    turnQuestStatus.bWin = questStatusData.bWin;
                    turnQuestStatus.deductLife = questStatusData.iDeductLife;
                    turnQuestStatus.EnemyPlayer = questStatusData.enemyID;

                    if (questStatusData.enemyID >= 0)
                    {
                        TPlayerInfo playerinfo = GetPlayerInfo(questStatusData.enemyID);
                        if (playerinfo != null)
                        {
                            turnQuestStatus.faceId = playerinfo.iconID;
                            turnQuestStatus.faceUrl = playerinfo.sFaceUrl;
                        }
                    }
                    break;
                }
            }
        }

    }
}

