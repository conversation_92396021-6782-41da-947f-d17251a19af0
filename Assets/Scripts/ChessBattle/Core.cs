
using GloryLockStep.Fight;
#if ACGGAME_CLIENT
//////////////////////////////////////////////////////////////////////////
//  游戏初始化
//////////////////////////////////////////////////////////////////////////
using System;
using System.Collections;
using UnityEngine;
using ACG.Core;
using GloryLockStep;
using GameFramework.Unity;
using Lucifer.ActCore;
using TKFrame;
using System.Threading;
using LogicFrameWork;
using ZGameChess;
using System.Collections.Generic;

namespace ACG.Core
{ 
    public class Core : MonoBehaviour
    {
        public static bool inited = false;
        public static GameObject DontDestroyOnLoadObj;

        private static Transform _core;

        private static Core _ins;

        public static Core Ins
        {
            get
            {
                return _ins;
            }
        }    

        public void Init()
        {
            if (!inited)
            {          
                createMonoRoot();
                ToolKit.init(Core.CorePluginRoot);

                V_DriverManager.Instance.Init();

                _ins = this;

                inited = true;
            }
        }


        public static Transform CorePluginRoot
        {
            get
            {
                if (_core == null) createMonoRoot();
                return _core;
            }
        }

	    public static void Setup()
        {
            if (!Core.inited)
            {
                Diagnostic.Log("Core Setup.");
                GameObject go = new GameObject();
                go.name = "Core";
                go.AddComponent<Core>().Init();        
                GameObject.DontDestroyOnLoad(go);
            }
        }

        public void UnloadThis()
        {
            _core = null;
            Destroy(this);
        }

        public void StartWaitDispose()
        {
            ClearAfterLogicDisposed();
        }

        private static void ClearAfterLogicDisposed()
        {
            MicroMgr.Instance.Dispose();
            ClearGameCoreStatic();
        }

        public static void ClearGameCoreStatic()
        {
            LogicHitBullectPool.Destroy();
            LogicMsgPool.Destroy();
            LogicQueueManager.Destroy();
            LockStepFightProcess.ClearStatic();
        }

        private static void createMonoRoot()
        {
            if (_core != null) return;

            DontDestroyOnLoadObj = GameObject.Find("DontDestroyOnLoad");
            if (DontDestroyOnLoadObj == null) DontDestroyOnLoadObj = new GameObject("DontDestroyOnLoad");
            GameObject.DontDestroyOnLoad(DontDestroyOnLoadObj);

            GameObject rootGO = new GameObject();
            rootGO.name = "_CorePlugins";
            rootGO.transform.parent = DontDestroyOnLoadObj.transform;
            _core = rootGO.transform;
        }
    }
}
#endif