using System.Collections.Generic;

namespace ACG.Core
{
    /// <summary>
    /// Runner.每帧执行IRunable接口
    /// </summary>
    public class Runner
    {
        /// <summary>
        /// 排序方法
        /// </summary>
        // static RunnerPriorityCompare runnerPriorityCompare = new RunnerPriorityCompare();
        static RunItemDataPriorityCompare runItemDataPriorityCompare = new RunItemDataPriorityCompare();

        /// <summary>
        /// 驱动器列表
        /// </summary>
        //public static List<Runner> driverList = new List<Runner>();

        // 在一帧内，当前执行到的驱动优先级
        // protected static int nowPriorityInFrame;

        // 数据列表
        List<RunItemData> _list = new List<RunItemData>(50);
        List<RunItemData> _addList = new List<RunItemData>(25);
        List<RunDelegate> _removeList = new List<RunDelegate>(25);
        LinkedHashMap<RunDelegate, RunItemData> _linker = new LinkedHashMap<RunDelegate, RunItemData>();
        bool _isSleep = false;

        // 优先级被睡眠列表
        LinkedHashMap<int, int> _sleepPriority = new LinkedHashMap<int, int>();
        LinkedHashMap<int, int> _priorityRunItemCount = new LinkedHashMap<int, int>();

        public void Clear()
        {
            _linker.Clear();
            _list.Clear();
            _addList.Clear();
            _removeList.Clear();

            _sleepPriority.Clear();
            _priorityRunItemCount.Clear();
            _isSleep = false;
        }

        /// <summary>
        /// 驱动器休眠
        /// </summary>
        public void Sleep()
        {
            _isSleep = true;
        }

        /// <summary>
        /// 驱动器唤醒
        /// </summary>
        public void WakeUp()
        {
            _isSleep = false;
        }

        /// <summary>
        /// 驱动对象
        /// </summary>
        /// <param name="ir">Ir.</param>
        /// <param name="priority">Priority.</param>
        public void Drive(IRunable ir, int priority = 1000)
        {
            DriveMethod(ir.RunerDelegate, priority);
            ir.SetDriver(this);
        }

        /// <summary>
        /// Determines whether this instance is driver running by priority the specified priority.
        /// </summary>
        /// <returns><c>true</c> if this instance is driver running by priority the specified priority; otherwise, <c>false</c>.</returns>
        /// <param name="priority">Priority.</param>
        public bool IsDriverRunningByPriority(int priority)
        {
            int tmpValue;
            if (_sleepPriority.Count > 0 && _sleepPriority.TryGetValue(priority, out tmpValue))
            {
                if (tmpValue != 0)
                {
                    return false;
                }
            }

            return true;

//            if (!_sleepPriority.ContainsKey(priority) || _sleepPriority[priority] == 0)
//                return true;
//
//            return false;
        }

        /// <summary>
        /// 休眠
        /// </summary>
        /// <param name="ir">Ir.</param>
        public void DrivePrioritySleep(int priority)
        {
            int tmpValue;
            if (_sleepPriority.TryGetValue(priority, out tmpValue))
            {
                _sleepPriority[priority] = ++tmpValue;
            }
            else
            {
                _sleepPriority[priority] = 1;
            }
        }

        /// <summary>
        /// 唤醒
        /// </summary>
        /// <param name="ir">Ir.</param>
        public void DrivePriorityWakeUp(int priority)
        {
            int tmpValue;
            if (_sleepPriority.TryGetValue(priority, out tmpValue))
            {
                if(tmpValue > 0)
                    _sleepPriority[priority] = --tmpValue;
            }
//             else
//             {
// 
//             }
        }



        /// <summary>
        /// 移除驱动
        /// </summary>
        /// <param name="ir">Ir.</param>
        public void RemoveDrive(IRunable ir)
        {
            RemoveDriveMethod(ir.RunerDelegate);
        }

        private RunItemData tmpRunItemData = null;
        /// <summary>
        /// 休眠
        /// </summary>
        /// <param name="ir">Ir.</param>
        public void DriveSleep(IRunable ir)
        {
            if (_linker.TryGetValue(ir.RunerDelegate, out tmpRunItemData))
            {
                tmpRunItemData.activation = false;
            }
        }

        /// <summary>
        /// 唤醒
        /// </summary>
        /// <param name="ir">Ir.</param>
        public void DriveWakeUp(IRunable ir)
        {
            if (_linker.TryGetValue(ir.RunerDelegate, out tmpRunItemData))
            {
                tmpRunItemData.activation = true;
            }
        }

        /// <summary>
        /// 方法休眠
        /// </summary>
        /// <param name="runDelegate">Run delegate.</param>
        public void MethodSleep(RunDelegate runDelegate)
        {
            if (_linker.ContainsKey(runDelegate))
                _linker[runDelegate].activation = false;
        }

        /// <summary>
        /// 唤醒方法
        /// </summary>
        public void MethodWakeUp(RunDelegate runDelegate)
        {
            if (_linker.ContainsKey(runDelegate))
                _linker[runDelegate].activation = true;
        }

        /// <summary>
        /// 驱动方法
        /// </summary>
        /// <param name="runDelegate">Run delegate.</param>
        /// <param name="priority">Priority.</param>
        public void DriveMethod(RunDelegate runDelegate, int priority)
        {
            // 当前priority的子priority,新加入的函数自动+1
            int tempCount = 0;

            if (!_priorityRunItemCount.TryGetValue(priority, out tempCount))
            {
                _priorityRunItemCount.Add(priority, tempCount);
            }

            _priorityRunItemCount[priority] = ++tempCount;

            RunItemData rid = null;
            if (_linker.TryGetValue(runDelegate, out rid))
            {
                // 如果连接列表存在重设优先级
                rid.activation = true;
                rid.priority = priority;
                rid.priorityIndex = tempCount;


                rid.needDelete = false;
//                 // 如果移除列表存在,则移除移除列表中的对应项(实际使用中,保留最后一次的操作-移除或是添加)
//                 int idx = _removeList.IndexOf(runDelegate);
//                 if (idx >= 0)
//                 {
//                     _removeList.RemoveRange(idx, 1);
//                 }
            }
            else
            {
                rid = new RunItemData(runDelegate, priority);
                rid.priorityIndex = tempCount;
                _addList.Add(rid);
                _linker.Add(runDelegate, rid);
            }
        }

        /// <summary>
        /// 移除驱动方法
        /// </summary>
        /// <param name="runDelegate">Run delegate.</param>
        public void RemoveDriveMethod(RunDelegate runDelegate)
        {
            if (runDelegate == null)
            {
                return;
            }

            RunItemData rid = null;
            if (_linker.TryGetValue(runDelegate, out rid))
            {
                rid.activation = false;
                rid.needDelete = true;
//                 // 移除列表里的移除方法避免重复
//                 if (_removeList.IndexOf(runDelegate) < 0)
//                     _removeList.Add(runDelegate);
            }
        }

        /// <summary>
        /// 优先级刷新 
        /// </summary>
        void FlushPriority()
        {
            _list.Sort(runItemDataPriorityCompare);
        }

        /// <summary>
        /// 执行所有Run函数
        /// </summary>
        public void Run()
        {
            if (_isSleep)
                return;

            // 刷新优先级
            if (_addList.Count > 0)
            {
                _list.AddRange(_addList);
                FlushPriority();
                _addList.Clear();
            }

            RunItemData rid = null;

            // 把移除列表的项目移除
//             if (_removeList.Count > 0)
//             {
//                 for (int l = 0, n1 = _removeList.Count; l < n1; l++)
//                 {
//                     for (int m = 0, n2 = _list.Count; m < n2; m++)
//                     {
//                         rid = _list[m];
//                         if (rid.runDelegate == _removeList[l])
//                         {
//                             _list.RemoveAt(m);
//                             _linker.Remove(rid.runDelegate);
//                             break;
//                         }
//                     }
//                 }
// 
//                 _removeList.Clear();
//             }
            //
            //优化remove
            for (int m = _list.Count - 1 ; m >= 0; m--)
            {
                rid = _list[m];
                if (rid.needDelete)
                {
                    _list.RemoveAt(m);
                    _linker.Remove(rid.runDelegate);                   
                }
            }


            // 执行
            for (int i = 0, n = _list.Count; i < n; i++)
            {
                rid = _list[i];
                if (rid.activation 
                    && IsDriverRunningByPriority(rid.priority))
                {
                    rid.runDelegate();
                }
            }

            // Runner.nowPriorityInFrame = this.priority;
        }

        /// <summary>
        /// Gets the length.
        /// </summary>
        /// <value>The length.</value>
        public int Length
        {
            get { return _list.Count; }
        }

    }

    /// <summary>
    /// Run item data priority compare.
    /// </summary>
    public class RunItemDataPriorityCompare : IComparer<RunItemData>
    {
        int IComparer<RunItemData>.Compare(RunItemData x, RunItemData y)
        {
            if (x.priority == y.priority)
            {
                return x.priorityIndex - y.priorityIndex;
            }

            return x.priority - y.priority;
        }
    }
}