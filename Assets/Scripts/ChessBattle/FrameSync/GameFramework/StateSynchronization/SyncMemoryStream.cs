///////////////////////////////////////////////////////////////////////////////
/// author : taodeng
/// time :   2017-5-15 22:52
/// version: 1.0
///////////////////////////////////////////////////////////////////////////////

using System;
using System.IO;
using System.Runtime.Serialization;

namespace GameFramework.StateSynchronization
{
    using FMath;

    /// <summary>
    /// system.MemoryStream implementation.
    /// BinaryWriter每次类型转换调用BitConverter
    /// BitConverter的转换实现，每帧都产生分配，后续考虑将接口改成()
    /// void GetBytes(byte[] buffer, short value)
    /// public static unsafe byte[] GetBytes(short value)
    /// {
    ///    byte[] buffer = new byte[2];
    ///    fixed (byte * numRef = buffer)
    ///    {
    ///       *((short*)numRef) = value;
    ///    }
    ///    return buffer;
    /// }
    /// 
    /// </summary>
    public class SyncMemoryStream : ISyncStream
    {
        MemoryStream mMemoryStream;
        BinaryWriter mWriter;
        BinaryReader mReader;

        public SyncMemoryStream()
        {
            mMemoryStream = new MemoryStream();
            mWriter = new BinaryWriter(mMemoryStream);
            mReader = new BinaryReader(mMemoryStream);
        }

        public SyncMemoryStream(byte[] buffer)
        {
            mMemoryStream = new MemoryStream(buffer);
            mWriter = new BinaryWriter(mMemoryStream);
            mReader = new BinaryReader(mMemoryStream);
        }

        public SyncMemoryStream(int capacity)
        {
            mMemoryStream = new MemoryStream(capacity);
            mWriter = new BinaryWriter(mMemoryStream);
            mReader = new BinaryReader(mMemoryStream);
        }

        public SyncMemoryStream(byte[] buffer, bool writable)
        {
            mMemoryStream = new MemoryStream(buffer, writable);

            if (writable)
            {
                mWriter = new BinaryWriter(mMemoryStream);
            }
            
            mReader = new BinaryReader(mMemoryStream);
        }

        public bool CanRead
        {
            get
            {
                return mMemoryStream.CanRead;
            }
        }

        public bool CanWrite
        {
            get
            {
                return mMemoryStream.CanWrite && null != mWriter;
            }
        }


        /// <summary>
        /// Initializes a new non-resizable instance of the System.IO.MemoryStream class
        ///    based on the specified region (index) of a byte array.
        /// </summary>
        /// <param name="buffer">The array of unsigned bytes from which to create this stream.</param>
        /// <param name="index">The index into buffer at which the stream begins.</param>
        /// <param name="count">The length of the stream in bytes.</param>
        public SyncMemoryStream(byte[] buffer, int index, int count)
        {
            mMemoryStream = new MemoryStream(buffer, index, count);
        }

        #region seek

        /// <summary>
        /// 返回数据buffer.
        /// </summary>
        /// <returns></returns>
        public byte[] GetBuffer()
        {
            return mMemoryStream.GetBuffer();
        }

        /// <summary>
        /// 取得当前读写指针的位置.
        /// </summary>
        public uint position
        {
            get
            {
                return (uint)mMemoryStream.Position;
            }

            set
            {
                mMemoryStream.Position = value;
            }
        }

        /// <summary>
        /// 取得当前数据的长度.
        /// </summary>
        public uint length
        {
            get
            {
                return (uint)mMemoryStream.Length;
            }
        }

        /// <summary>
        /// sets length of current stream.
        /// </summary>
        /// <param name="value"></param>
        public void SetLength(uint value)
        {
            mMemoryStream.SetLength(value);

            if (position > value)
            {
                position = value;
            }   
        }

        /// <summary>
        /// 移动当前的读写指针.
        /// </summary>
        /// <param name="origin"></param>
        /// <param name="offset"></param>
        public void Seek(int offset, SyncStreamSeekOrigin origin)
        {
            mMemoryStream.Seek(offset, (SeekOrigin)origin);
        }

        #endregion seek

        #region primitive read

        /// <summary>
        /// reads byte buffer from stream.
        /// </summary>
        /// <param name="recvBuffer">receive buffer</param>
        /// <param name="recvBufferOffset">offset of receiver buffer.</param>
        /// <param name="count">byte number of reading.</param>
        public void Read(byte[] recvBuffer, uint recvBufferOffset, uint count)
        {
            mReader.Read(recvBuffer, (int)recvBufferOffset, (int)count);
        }

        public bool ReadBoolean()
        {
            return mReader.ReadBoolean();
        }

        public byte ReadByte()
        {
            return mReader.ReadByte();
        }

        public char ReadChar()
        {
            return mReader.ReadChar();
        }

        public short ReadShort()
        {
            return mReader.ReadInt16();
        }

        public ushort ReadUShort()
        {
            return mReader.ReadUInt16();
        }

        public int ReadInt()
        {
            return mReader.ReadInt32();
        }

        public uint ReadUInt()
        {
            return mReader.ReadUInt32();
        }

        public long ReadLong()
        {
            return mReader.ReadInt64();
        }

        public ulong ReadULong()
        {
            return mReader.ReadUInt64();
        }

        public float ReadSingle()
        {
            return mReader.ReadSingle();
        }

        public double ReadDouble()
        {
            return mReader.ReadDouble();
        }

        public Fix64 ReadFix64()
        {
            return Fix64.FromRawValue(mReader.ReadInt64());
        }

        #endregion read

        #region primitive write

        /// <summary>
        /// write binary data to stream.
        /// </summary>
        /// <param name="data"></param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        public void Write(byte[] data, int offset, int count)
        {
            mWriter.Write(data, offset, count);
        }

        public void Write(byte[] data)
        {
            mWriter.Write(data);
        }

        public void WriteBoolean(bool value)
        {
            mWriter.Write(value);
        }

        public void WriteByte(byte value)
        {
            mWriter.Write(value);
        }

        public void WriteChar(char value)
        {
            mWriter.Write(value);
        }

        public void WriteShort(short value)
        {
            mWriter.Write(value);
        }

        public void WriteUShort(ushort value)
        {
            mWriter.Write(value);
        }

        public void WriteInt(int value)
        {
            mWriter.Write(value);
        }

        public void WriteUInt(uint value)
        {
            mWriter.Write(value);
        }

        public void WriteLong(long value)
        {
            mWriter.Write(value);
        }

        public void WriteULong(ulong value)
        {
            mWriter.Write(value);
        }

        public void WriteSingle(float value)
        {
            mWriter.Write(value);
        }

        public void WriteDouble(double value)
        {
            mWriter.Write(value);
        }

        public void WriteFix64(Fix64 value)
        {
            mWriter.Write(value.rawValue);
        }

        #endregion write
    }
}
