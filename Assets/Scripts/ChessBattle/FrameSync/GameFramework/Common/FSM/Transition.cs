///////////////////////////////////////////////////////////////////////////////
/// author : taodeng
/// time :   2017-6-3 11:31
/// version: 1.0
///////////////////////////////////////////////////////////////////////////////

namespace GameFramework.Common.FSM
{
    public class Transition<T>
    {
        public Transition(State<T> fromState,
            State<T> toState, ITransitCondition<T> c)
        {
            from = fromState;
            to = toState;
            condition = c;

            Enable();
        }


        public State<T> from { get; set; }

        public State<T> to { get; set; }

        public bool enabled { get; protected set; }

        public void Enable()
        {
            enabled = true;
        }

        public void Disable()
        {
            enabled = false;
        }

        public bool Check(T context, State<T> state)
        {
            if (null != condition)
            {
                return condition.Check(context, state);
            }

            return true;
        }

        public override string ToString()
        {
            return string.Format("{0}->{1}", from.name, to.name);
        }

        public ITransitCondition<T> condition { get; set; }
    }
}