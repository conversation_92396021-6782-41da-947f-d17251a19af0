///////////////////////////////////////////////////////////////////////////////
/// created by : taodeng
/// time : 2017-6-3 17:51
/// version: 1.0
///////////////////////////////////////////////////////////////////////////////

namespace GameFramework.Common.FSM.TransitConditions
{
    class NotCondition<T> : ITransitCondition<T>
    {
        public NotCondition(ITransitCondition<T> condition)
        {
            Set(condition);
        }

        public void Reset()
        {
            mCondition = null;
        }

        public void Set(ITransitCondition<T> condition)
        {
            mCondition = condition;
        }

        public bool Check(T context,
            State<T> state)
        {
            if (null != mCondition)
            {
                return !mCondition.Check(context, state);
            }

            return false;
        }

        ITransitCondition<T> mCondition;
    }
}
