#if ACGGAME_CLIENT


using System;
using UnityEngine;
using System.Collections;
using UnityEngine.SceneManagement;

namespace GameFramework.Unity
{
    using Common.FSM;

    /// <summary>
    /// gameframework对接unity的部件.
    /// 
    /// 界面平滑的机制：
    /// 
    /// 1.使用插值帧
    ///   界面和逻辑分离，逻辑帧率固定为15帧，
    ///   通过在两个逻辑帧之间加入插值帧，使快速运动的物体看起来更连贯。
    ///   适用于包括自身角色在内的所有对象。
    ///   
    /// 2.预表现
    ///   自身角色的运动，在收到服务器下发的input前，
    ///   本地先开始运动，这样可以降低网络延时带来的卡顿的感觉。
    ///   注意，预表现会导致角色render中的位置出现偏移的问题。
    ///   只适用于自身角色。
    /// </summary>
    public class UnityGameAdapter : MonoBehaviour
    {
        // 是否使用插值帧。
        // 如果是且渲染帧率高于逻辑帧率时，界面表现会更平滑。
        public bool m_useInterpolatedFrames;

        // 插值帧的间隔
        public int m_interpolatedFrameInterval;

        // 是否使用预表现（自身角色）
        public bool m_usePrePerformance;

        private static bool isInit = false;

        /// <summary>
        /// unity回调
        /// </summary>
        void Awake()
        {
            if (!isInit)
            {
                isInit = true;

                DontDestroyOnLoad(gameObject);
            }
            else
            {
                throw new Exception("UnityGameAdapter 只能初始化1个！！！！");
            }
        }

        // Use this for initialization
        void Start()
        {
            m_useInterpolatedFrames = true;
            m_interpolatedFrameInterval = (66 / 4);
            m_usePrePerformance = true;

            StateGraph<Game> graph = null;

            UnityGameStateGraphProvider graphProvider =
                GetComponent<UnityGameStateGraphProvider>();

            if (null != graphProvider)
            {
                graph = graphProvider.GetStateGraph();
            }

            if (graph == null) throw new ArgumentNullException();
            Game.singleton.Start(this, graph);
        }

        /// <summary>
        /// unity每帧回调。
        /// </summary>
        void FixedUpdate()
        {
            Game.singleton.FixedUpdate((int) (UnityEngine.Time.fixedDeltaTime * 1000));
            //Game.singleton.Update((int)(UnityEngine.Time.deltaTime * 1000));
        }

        private void Update()
        {
            Game.singleton.Update((int) (UnityEngine.Time.deltaTime * 1000));
        }


        private void OnDestroy()
        {
            isInit = false;
        }
    }
}
#endif