#if ACGGAME_CLIENT 
///////////////////////////////////////////////////////////////////////////////
/// author : taodeng
/// time :   2017-6-6 10:06
/// version: 1.0
///////////////////////////////////////////////////////////////////////////////

namespace GameFramework.FPhysics
{
    using FMath;

    class FPhysicsSettings
    {
        public static FVector3 gravity = new FVector3(Fix64.zero, Fix64.FromSingle(-9.8f), Fix64.zero);
        public static Fix64 sleepEpsilon = Fix64.FromSingle(0.001f);
    }
}

#endif