using TKFrame;

namespace GameFramework.FrameDriving
{
    /// <summary>
    /// 默认的FrameDispatcher实现，在FrameDriver中会保留一份默认的实例。
    /// </summary>
    class DefaultFrameDispatcher : IFrameDispatcher
    {
        /// <summary>
        /// 分发帧。
        /// </summary>
        /// <param name="frameDriver">frame的第一个接受者</param>
        public void DispatchFrames(FrameDriver frameDriver)
        {
            // 该函数是在frameDriver中调用的，所以frameDriver不会为null。
            // Debug.Assert(null != frameDriver && null != frameDriver.frameQueue);

            int frameCounter = 0;

            while (frameDriver.frameQueue.Count > 0 && frameCounter < maxFramePerDispatch)
            {
                Frame frame = frameDriver.frameQueue.Dequeue();
                frameCounter++;
                if (frame == null)
                {
                    Diagnostic.Log("error : frame is null !!!!! ");
                    continue;
                }

                frameDriver.DoFrame(frame);

                if (null != frameDriver.frameGenerator)
                {
                    frameDriver.frameGenerator.OnFrameDispatched(frame);
                }

                if (null != frameDriver.framePool)
                {
                    frameDriver.framePool.Free(frame);
                }
            }
        }

        /// <summary>
        /// 设置一个数值，规定每次最多可以分发多少帧
        /// </summary>
        public uint maxFramePerDispatch
        {
            get
            {
                return mMaxFramePerDispatch;
            }

            set
            {
                mMaxFramePerDispatch = value;
            }
        }

        private uint mMaxFramePerDispatch = 50; //uint.MaxValue;
    }
}

