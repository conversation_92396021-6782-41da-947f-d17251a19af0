///////////////////////////////////////////////////////////////////////////////
/// author : taodeng
/// time :   2017-7-10 15:40
/// version: 1.0
///////////////////////////////////////////////////////////////////////////////

namespace GameFramework.FAnimate
{
    public struct FKeyFrame<T>
    {
        public int time;           //时间搓毫秒（相对于动画的起始时间）
        public T value;
        public T inSlope;         // 斜率，用于三次插值算法(第一阶段实现的线性插值不需要斜率)
        public T outSlope;

        public FKeyFrame(int time)
        {
            this.time = time;
            this.value = default(T);
            inSlope = default(T);
            outSlope = default(T);
        }

        public FKeyFrame(int time, T value)
        {
            this.time = time;
            this.value = value;
            inSlope = default(T);
            outSlope = default(T);
        }

        public override int GetHashCode()
        {
            return time;
        }

        public static int Compare(FKeyFrame<T> a, FKeyFrame<T> b)
        {
            return a.time - b.time;
        }
    }
}
