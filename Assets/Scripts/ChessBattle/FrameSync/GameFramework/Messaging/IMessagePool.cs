///////////////////////////////////////////////////////////////////////////////
/// created by : taodeng
/// time : 2017-5-10 20:00
/// version: 1.0
///////////////////////////////////////////////////////////////////////////////

namespace GameFramework.Messaging
{
    public interface IMessagePool<T>
    {
        Message<T> Alloc();
        Message<T> Alloc(T key, object extraData);
        void Free(Message<T> msg);
    }
}
