#if ACGGAME_CLIENT 
using GameFramework.Common.FSM;
using UnityEngine;
using GameFramework.FrameSync;
using TKFrame;
using GameFramework;

namespace GloryLockStep.Room.RoomStates
{
	class RoomState_NoNetworkLoadingFight : RoomState_BaseLoading
	{

		public RoomState_NoNetworkLoadingFight(IRoomControlSerializer serializer)
        {
            this.serializer = serializer;
        }

        IRoomControlSerializer serializer;
		
		bool mMoveToNextState;
		private RoomControl mContext;

		public override void OnStateEnter(RoomControl context, State<RoomControl> state)
		{
			Log.InfoChannel(LogChannel.LockStep, "RoomState_NoNetworkLoadingFight.OnStateEnter");
			mContext = context;

            if(serializer != null)
            {
                serializer.Serializer();
            }
			
			ACGEventManager.Instance.Send(EventType_RoomFight.EnterLoadingFight);
			ACGEventManager.Instance.AddEventListener(SceneEvents.EVENT_LOAD_PROGRESS, MyLoadProgressHandler);

			RoomEnterParameters enterParam = Game.singleton.GetData("RoomEnterParameters") as Room.RoomEnterParameters;

			NoNetworkData.isNoNetWorkFighting = true;
		}

		public override void OnStateUpdate(RoomControl context, State<RoomControl> state)
		{

		}

		public override void OnStateExit(RoomControl context, State<RoomControl> state)
		{
			ACGEventManager.Instance.RemoveEventListener(SceneEvents.EVENT_LOAD_PROGRESS, MyLoadProgressHandler);
            Debug.Log("RoomState_NoNetworkLoadingFight.OnStateExit");
		}
		
		public override void SyncState(MovingState movingState)
		{
		}

		private void MyLoadProgressFinish(GEvent g = null)
		{
			m_IsLoadFinish = true;
            ACGEventManager.Instance.Send(EventType_RoomFight.OnNotice_AllParticipantAreLoaded);
			mContext.StateConfig.TriggerToFight();
		}

		private void MyLoadProgressHandler(GEvent g)
		{
			OnProgress(mContext, g.floatData);
		}

		void OnProgress(RoomControl context, float progress)
		{
			foreach(var participant in context.participants.ParticipantieList.Values)
			{
				if (participant != null && !participant.isAi)
				{
					ACG.Core.ACGTuple<long, float> msgInfo = ACG.Core.ACGTuple.Create<long, float>(participant.participantUin,  progress);
					ACGEventManager.Instance.Send(EventType_RoomFight.OnLoadingProgressInRoom, msgInfo);
				}
			}
		}
	}
}
#endif