 
using System;
#if ACGGAME_CLIENT
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using ZGame;
using ZGameClient;

public class NoNetworkNormalConfig : NoNetworkBaseConfig
{
    public NoNetworkNormalConfig(int setID) : base(setID)
    {

    }


    public override string GetPlayerName(int playerIndex)
    {
        if (playerIndex == 0)
            return String.Empty;
        return AIName[playerIndex - 1];
    }

    public override int GetTinyHeroID(int playerIndex)
    {
        int result = 100001;
        return result;
        
    }

    public override int GetIconID(int playerIndex)
    {
        return 0;
    }

    public static readonly string[] dicSpec = new string[]{ "测试名字","测试名字1", "测试名字2", "测试名字3", "测试名字4", "测试名字5", "测试名字6", "测试名字7", "测试名字8" };
}
#endif