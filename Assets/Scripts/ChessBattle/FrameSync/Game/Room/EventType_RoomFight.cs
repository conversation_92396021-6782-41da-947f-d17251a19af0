using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GloryLockStep.Room
{
    public class EventType_RoomFight
    {
        public const string RoomMessageBase = "RoomMessageBase";
        public const string EnterLoadingFight = "EnterLoadingFight";
        public const string EnterWaitingForParticipants = "EnterWaitingForParticipants";
        public const string LeaveLoadinFight = "LeaveLoadinFight";
        public const string OnUserJoinRoom = "OnUserJoinRoom";
        public const string OnUserReadyInRoom = "OnUserReadyInRoom";
        public const string BuildingFightSceneIsDone = "BuildingFightSceneIsDone";
        public const string OnLoadingProgressInRoom = "OnLoadingProgressInRoom";
        public const string OnUiLoadingProgressInRoomEnd = "OnUiLoadingProgressInRoomEnd";
        public const string OnNotice_AllParticipantAreLoaded = "OnNotice_AllParticipantAreLoaded";
        public const string EnterRoomStateFighting = "EnterRoomStateFighting";
        public const string ShowReplayView = "ShowReplayView";
        public const string ShowResultView = "ShowResultView";
        public const string StartReady = "StartReady";
        public const string UpdateAILevelAndMaxCount = "UpdateAILevelAndMaxCount";
        public const string MatchingFailed = "MatchingFailed";

        public const string StartFight = "StartFight";
        public const string FightTimeIsUp = "FightTimeIsUp";
        
        public const string ReconnectSuccess = "ReconnectSuccesss";

    }
}