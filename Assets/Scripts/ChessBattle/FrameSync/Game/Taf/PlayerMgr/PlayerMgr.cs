using System.Collections.Generic;
using ZGameClient;
using System;
using Lucifer.ActCore;

// 翻译自PlayerMgr.cpp
namespace Z_PVE
{
    public class PlayerRankInfo
    {
        public int iChairID;
        public int iLife;
        public int iRank;
        public static int SortPlayerRankInfo(PlayerRankInfo first, PlayerRankInfo second)
        {
            if (first.iLife <=0  && second.iLife <= 0)
            {
                return (first.iRank - second.iRank);
            }

            int iFirstScore = first.iLife * 100 - first.iRank;
            int iSecondScore = second.iLife * 100 - second.iRank;
            if (iFirstScore > iSecondScore)
            {
                return -1;
            }
            else if (iSecondScore > iFirstScore)
            {
                return 1;
            }
            else
            {
                if (first.iChairID < second.iChairID)
                {
                    return -1;
                }
                else if (first.iChairID > second.iChairID)
                {
                    return 1;
                }
            }
            return 0;
        }
    }

    class CPlayerPool : GameFramework.Common.ObjectPool<CPlayer>,
            GameFramework.Common.ObjectPool<CPlayer>.IConstructImpl
    {
        public CSoGame CSoGame;
        public CPlayerPool()
        {
            constructImpl = this;
        }

        public CPlayer ConstructObject()
        {
            CPlayer newContext = new CPlayer(-1, CSoGame);
           
            return newContext;
        }

        public void ResetObject(CPlayer obj)
        {
            if (obj != null)
            {
                obj.Reset();
            }
        }
    }

    public class CPlayerMgr : System.IDisposable
    {
        private CPlayerPool _playerPool = new CPlayerPool();
        private CSoGame CSoGame;

        protected LinkedHashMap<int, CPlayer> m_vecPlayer = new LinkedHashMap<int, CPlayer>();
        protected LinkedHashMap<int, CPlayer> m_obPlayer = new LinkedHashMap<int, CPlayer>();
        protected LinkedHashMap<int, CPlayer> m_judgePlayer = new LinkedHashMap<int, CPlayer>();
        public List<PlayerRankInfo> m_listRankInfo = new List<PlayerRankInfo>();
        public LinkedHashMap<int, CPlayer> allPlayer
        {
            get
            {
                return m_vecPlayer;
            }
        }

        public CPlayerMgr(CSoGame ptr)
        {
            CSoGame = ptr;
            _playerPool.CSoGame = CSoGame;
        }

        public virtual void Dispose()
        {
            foreach (var item in m_vecPlayer)
                item.Value.Dispose();
#if ACGGAME_CLIENT
            CSoGame = null;
#endif
        }

        public void Reset()
        {
            m_obPlayer.Clear();
            m_judgePlayer.Clear();
            m_listRankInfo.Clear();
            foreach(var item in m_vecPlayer)
            {
                _playerPool.Free(item.Value);
            }
            m_vecPlayer.Clear();
        }

        public void PreAllocPlayer(int cnt)
        {
            List<CPlayer> tempPlayers = new List<CPlayer>();
            while(cnt > 0)
            {
                cnt--;
                tempPlayers.Add(_playerPool.Alloc());
            }
            foreach(var player in tempPlayers)
            {
                _playerPool.Free(player);
            }
        }
        

        public virtual CPlayer GetPlayerByChairId(int i8ChairId, int iBattleIndex = -1)
        {
            if (i8ChairId == MIRR_PLAYER_ID)
                return MirroPlayer;
            if (iBattleIndex >= 0)
            {
                var listBattleVSPlayers = CSoGame.GetListBattleVSPlayers();
                if (listBattleVSPlayers != null)
                {
                    foreach (var item in listBattleVSPlayers)
                    {
                        if (item.Key.iChairid == iBattleIndex && item.Value.iChairid == i8ChairId)
                        {
                            if (item.Value.eBattlePlayerType == BattlePlayerType.BATTLE_MIRROR_PLAYER)
                                return MirroPlayer;
                        }
                    }
                }
            }

            if (m_vecPlayer.ContainsKey(i8ChairId))
            {
                return m_vecPlayer[i8ChairId];
            }

            if (m_obPlayer.ContainsKey(i8ChairId))
                return m_obPlayer[i8ChairId];
            if (m_judgePlayer.ContainsKey(i8ChairId))
                return m_judgePlayer[i8ChairId];

            return null;
        }

        public CPlayer GetPlayerByUin(long uin)
        {
            foreach(var player in m_vecPlayer.Values)
            {
                if (player.UIN == uin)
                    return player;
            }
            
            foreach(var player in m_obPlayer.Values)
            {
                if (player.UIN == uin)
                    return player;
            }
            foreach(var player in m_judgePlayer.Values)
            {
                if (player.UIN == uin)
                    return player;
            }
            return null;
        }

        private void UpdatePlayer(CPlayer player, TABLE_USER_INFO pUserInfo, bool isRealAi)
        {
            //DEBUG_MSG("There is already player UIN = %lld, ChairId = %d.", pUserInfo.m_lUin, pUserInfo.m_nChair);
            // TODO:
            //player1.Initialize();
            
            player.PlayerId = pUserInfo.m_nPlayerID;
            //player1.SetPhysicalChairId = pUserInfo.m_nChair;
            player.ChairId = pUserInfo.m_nChair;
            player.UserInfo = pUserInfo;
            player.isDirController = pUserInfo.m_bJoyStickSwitch;

            int life = CSoGame.loadConfig.GetInitialLife();
            player.TAC_SetInitLife(life);
            player.iPlayerLife = life;
            player.TAC_SetLife(life);

            player.TriggerAgent?.BindTrigger(pUserInfo.stUsedCaptainInfo.iCaptainId);
        }
        
        public virtual CPlayer AddPlayer(ZGameClient.TABLE_USER_INFO pUserInfo, bool isRealAi)
        {
            //Debug.Assert(pUserInfo);
            //SO_SLOGV("AddPlayer", 2).With("Player", pUserInfo).Log();
            TKFrame.Diagnostic.Log("#CPlayerMgr# player size:{0} , add uin:{1}, chairid:{2}", m_vecPlayer.Count, pUserInfo.m_lUin, pUserInfo.m_nChair);
           
            foreach (var player1 in m_vecPlayer.Values)
            {
                if (player1.UIN != pUserInfo.m_lUin)
                {
                    continue;
                }
                
                player1.ClearGameData();
                UpdatePlayer(player1, pUserInfo, isRealAi);              
                return player1;
            }

            CPlayer player = _playerPool.Alloc();//new CPlayer(pUserInfo.m_nChair, CSoGame);
            //DEBUG_MSG("New player UIN:%lld, ChairId:%d.", pUserInfo.m_lUin, pUserInfo.m_nChair);
            // TODO:
            //player.Initialize();
            player.UIN = pUserInfo.m_lUin;
            UpdatePlayer(player, pUserInfo, isRealAi);
            player.SetMoney(CSoGame.loadConfig.GetInitialCoin(), false);
            player.TAC_SetLevel(CSoGame.loadConfig.GetInitialLevel());
            player.TAC_SetExp(CSoGame.loadConfig.GetInitialExp());
            m_vecPlayer.Add(player.ChairId,player);

            return player;
        }

        public virtual void RemovePlayer(int i16PlayerId, bool bFakeRemove)
        {
            //DEBUG_MSG("i16PlayerId:%d, bFakeRemove:%d", i16PlayerId, bFakeRemove);
            CPlayer pPlayer = GetPlayerByChairId(i16PlayerId);
            if (null != pPlayer)
            {
                RemovePlayer(pPlayer, bFakeRemove);
            }
        }

        public virtual void RemovePlayer(CPlayer pPlayer, bool bFakeRemove)
        {
            if (null != pPlayer)
            {
                //DEBUG_MSG("i16PlayerId:%d, uin:%u bFakeRemove:%d", pPlayer.GetPlayerId(), pPlayer.GetUIN(), bFakeRemove);
                {
                    pPlayer.ClearGameData();

                    for (int i = 0; i < m_vecPlayer.Count; ++i)
                    {
                        if (m_vecPlayer[i] == pPlayer)
                        {
                            m_vecPlayer[i] = null;
                            m_vecPlayer.Remove(i);
                            break;
                        }
                    }
                }
            }
        }


        public virtual CPlayer GetPlayerAt(int i8Idx)
        {
            CPlayer rtnPlayer = null;
            m_vecPlayer.TryGetValue(i8Idx, out rtnPlayer);
            return rtnPlayer;
        }


        public LinkedHashMap<int, CPlayer> GetPlayers()
        {
            return m_vecPlayer; //new List<CPlayer>(m_vecPlayer);
        }

        public int GetFirstChairIdIngoreAI()
        {
            return -1;
        }


        List<CPlayer> _listOutPlayer = new List<CPlayer>(8);
        public List<CPlayer> GetOutPlayers()
        {
            _listOutPlayer.Clear();
            var players = CSoGame.PlayerMgr.allPlayer.Values;
            foreach (var player in players)
            {
                //还有血量或者不是本轮淘汰的
                if (player.TAC_GetLife() > 0)
                    continue;

                _listOutPlayer.Add(player);
            }
            return _listOutPlayer;
        }

        Dictionary<int, int> _dicRank = new Dictionary<int, int>(8);
        public Dictionary<int, int> GetDicRank(List<CPlayer> outPlayers)
        {
            return _dicRank;
        }

        private void SortPlayerRankInfo()
        {
            {
                m_listRankInfo.StableSortSoGame_Logic(PlayerRankInfo.SortPlayerRankInfo);
                int iRank = 1;
                foreach (var item in m_listRankInfo)
                {
                    CPlayer result = null;
                    if (m_vecPlayer.TryGetValue(item.iChairID, out result))
                    {
                        item.iRank = iRank;
                        iRank++;
                    }
                }
            }
        }
        public void InitPlayerRankInfo()
        {
            m_listRankInfo.Clear();
            foreach(var player in m_vecPlayer.Values)
            {
                PlayerRankInfo stRankInfo = new PlayerRankInfo();
                stRankInfo.iChairID = player.ChairId;
                stRankInfo.iLife = player.TAC_GetLife();
                m_listRankInfo.Add(stRankInfo);
            }

            SortPlayerRankInfo();
        }
        public void ReSortPlayerRankInfo()
        {

        }

        public void ResetSelectStatus()
        {

        }
        
        public void ResetMirrorPlayerFlag()
        {

        }


        /// <summary>
        /// 
        /// </summary>
        public const short MIRR_PLAYER_ID = 9999;
        public CPlayer MirroPlayer;
        public bool HasMirrorPlayer = false;
        /// <summary>
        /// 回合状态该触发器
        /// </summary>
        public void TriggerSoGameTurnState(int turnstate)
        {
            
        }
    }
}