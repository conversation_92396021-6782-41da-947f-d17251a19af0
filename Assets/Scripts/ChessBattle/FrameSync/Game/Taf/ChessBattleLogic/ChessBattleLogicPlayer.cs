using ACG.Core;
using GameFramework.FMath;
using System;
using System.Collections.Generic;
using LogicFrameWork;
using TKFrame;
using Z_PVE;
using ZGame;
using ZGameClient;
using ZGameLog;
using ZGameChess;

public class ChessBattleLogicPlayer : BaseLogicMovement, IResetable
{
    public static Fix64 colliderR = Fix64._08;

    public enum PlayerState
    {
        Owner,
        Enemy,
        Friend,
        EnemyFriend,
        Observer,
    }

    public int ChairId { get; private set; }

    public PlayerState playerState = PlayerState.Owner;

    private int m_battleField;
    public bool m_isHome = true;

    public int BattleFieldId
    {
        get { return m_battleField; }
        set
        {
            m_battleField = value;
            if (m_battleField == playerData.ChairId)
                m_isHome = true;
            else
                m_isHome = false;
        }
    }

    private ChessBattleLogicField m_battleLogicField;

    public ChessBattleLogicField BattleLogicField
    {
        get { return m_battleLogicField; }
        set
        {
            if (m_battleLogicField == null || m_battleLogicField != value || value.ID != m_battleField)
            {
                m_battleLogicField = value;
                if (onChangeBattleFieldCallback != null)
                    onChangeBattleFieldCallback(m_battleLogicField);
            }
        }
    }

    // 升星
    private List<int> m_heroPromotionList = new List<int>();
    private int m_maxFrame = 100;
    private int m_nowFrame = 0;
    private int m_currentId = -1;

    // 能否发送位置
    private bool _isSendTarget = true;

    public bool isSendTarget
    {
        get { return _isSendTarget; }
        private set
        {
            if (_isSendTarget != value)
            {
                _isSendTarget = value;
            }
        }
    }

    public bool AutoEnableSendTargetInIdle = false;

    // 宝箱最大碰撞距离
    private Fix64 maxBoxDis;
    private Fix64 sqrMaxBoxDis;

    // 野怪最大碰撞距离
    private Fix64 maxItemDis;
    private Fix64 sqrMaxItemDis;

    // 金币最大碰撞距离
    private Fix64 maxIconDis;
    private Fix64 sqrMaxIconDis;

    // 等待加入碰撞物体的队列
    private List<ChessBattleLogicDropItem> m_waitDropItems = new List<ChessBattleLogicDropItem>();

    // 所有掉落物品
    private LinkedHashMap<int, ChessBattleLogicDropItem> m_allDropItems =
        new LinkedHashMap<int, ChessBattleLogicDropItem>();

    public LinkedHashMap<int, ChessBattleLogicDropItem> allDropItems
    {
        get { return m_allDropItems; }
    }

    // 所有正在碰撞物体
    private List<ChessBattleLogicDropItem> m_collisioningDropItems = new List<ChessBattleLogicDropItem>();

    // 需要调用装备接口的掉落物
    private List<ChessBattleTempDropItem> m_executeItems = new List<ChessBattleTempDropItem>();

    // 正在调用装备接口的掉落物
    private List<ChessBattleTempDropItem> m_executingItems = new List<ChessBattleTempDropItem>();

    // 射线达到的物体
    private List<ChessBattleLogicDropItem> m_rayCastItems = new List<ChessBattleLogicDropItem>();

    // 会经过的物体
    private List<ChessBattleLogicDropItem> m_passItems = new List<ChessBattleLogicDropItem>();

    // 小队长编辑模式下，强行使用一种移动方式
    private PlayerMoveMessage m_moveWay = PlayerMoveMessage.StartWalk;

    private LinkedHashMap<int, List<int>> m_usedDropboxDict = new LinkedHashMap<int, List<int>>();

    public LinkedHashMap<int, List<int>> UsedDropboxDict
    {
        get { return m_usedDropboxDict; }
    }


    public Action<ChessBattleLogicField> onChangeBattleFieldCallback = null;

    public bool isObserver
    {
        get { return playerState == PlayerState.Observer; }
    }

    private ChessBattleLogicPlayerData m_logicPlayerData = null;
    public ChessBattleLogicPlayerData LogicPlayerData
    {
        get { return m_logicPlayerData; }
    }

    public ChessBattleLogicPlayerObserverModel ObserverModel
    {
        get
        {
            if (m_logicPlayerData != null)
                return m_logicPlayerData.ObserverModel;

            return null;
        }
    }

    public bool isFake = false;

    private EquipmentMgr m_equipMgr;

    private Fix64 m_waitTime = Fix64.zero;

    // 追随这个玩家一起走的玩家
    private List<int> m_followers = new List<int>();

    // 局内不会引用
    public ChessBattleLogicPlayer() : base(MicroMgr.Instance.GetMicroObj())
    {
        m_coliderR = colliderR;

        maxBoxDis = Fix64.one + m_coliderR;
        sqrMaxBoxDis = maxBoxDis * maxBoxDis;

        maxItemDis = EquipmentMgr.DROP_ITEM_COLIDER_R + m_coliderR;
        sqrMaxItemDis = maxItemDis * maxItemDis;

        maxIconDis = EquipmentMgr.DROP_ITEM_COLIDER_R + m_coliderR;
        sqrMaxIconDis = maxIconDis * maxIconDis;

#if UNITY_EDITOR && ACGGAME_CLIENT
        logicType = LogicType.ChessBattleLogicPlayer;
#endif
    }

    public ChessBattleLogicPlayer(MicroObject world) : base(world)
    {
        //m_world = world;

        m_coliderR = colliderR;

        maxBoxDis = Fix64.one + m_coliderR;
        sqrMaxBoxDis = maxBoxDis * maxBoxDis;

        maxItemDis = EquipmentMgr.DROP_ITEM_COLIDER_R + m_coliderR;
        sqrMaxItemDis = maxItemDis * maxItemDis;

        maxIconDis = EquipmentMgr.DROP_ITEM_COLIDER_R + m_coliderR;
        sqrMaxIconDis = maxIconDis * maxIconDis;

        m_speed = Fix64.zero;

        //m_taskCtrl = new LogicTaskCtrl(this);

#if UNITY_EDITOR && ACGGAME_CLIENT
        logicType = LogicType.ChessBattleLogicPlayer;
#endif
    }

    public void InitData(CPlayer player, ChessBattleLogicPlayerData logicPlayerData = null)
    {
        playerData = player;
        player.SetLogicPlayer(this);
        ChairId = playerData.ChairId;
        TinyId = playerData.UserInfo.stUsedCaptainInfo.iCaptainId;
        m_equipMgr = player.EquipmentMgr;
        m_logicPlayerData = logicPlayerData;
        playerState = PlayerState.Owner;

        Init();
    }

    public void SetSendTargetState(bool value)
    {
        if (isSendTarget != value)
        {
            isSendTarget = value;
            if (value == false)
            {
                m_waitTime = ACG.Core.AutoChessConfig.HurtActionLength;
                if (moveState != PlayerMoveMessage.Stop)
                    StopMove();
            }
        }
    }

    public void OnTurnStart()
    {
        if (LogicPlayerData != null)
            LogicPlayerData.ClearAttackData();
    }


    public void TriggerCSoAction(byte[] paramData)
    {
        E_SO_TRIGGER_TYPE triggerType = (E_SO_TRIGGER_TYPE)paramData[0];
        int param = (int)paramData[1];
        TriggerCSoAction(triggerType, param);
    }

    public void TriggerCSoAction(E_SO_TRIGGER_TYPE type, int value)
    {
        World.m_triggerMgr.SendPlayerEvent(ChairId, type, value);
    }

    public void Teleport(FVector3 targetPos)
    {
        if (isFake)
            return;

        // 传送
        dst = fTransform.position = targetPos;
        TriggerCollision(true);
        SetState(PlayerMoveMessage.Stop);

#if ACGGAME_CLIENT
        if (isOpenQueue())
        {
            FVector3Msg fVector3Msg = LogicMsgPool.Instance.GetMsg<FVector3Msg>();
            fVector3Msg.value = dst;
            fVector3Msg.isNewValue = true;
            fVector3Msg.msgType = (int)PlayerMoveMessage.Teleport;
            SendData(fVector3Msg);
        }
#endif
    }

#if ACGGAME_CLIENT
    protected override void OnSendMovingState(InitPlayerMsg msg)
    {
        base.OnSendMovingState(msg);

        playerData.TriggerAgent?.OnSendMovingState(msg);
    }
#endif

    public void Reset()
    {
        Release();
    }

    public void ResetColliderAndSpeed()
    {

    }

    public void ClearDropData()
    {
        m_allDropItems.Clear();
        m_waitDropItems.Clear();
        m_rayCastItems.Clear();
        m_passItems.Clear();
        m_collisioningDropItems.Clear();
        m_executeItems.Clear();
        m_executingItems.Clear();
    }

    private void ClearFlag()
    {
        AutoEnableSendTargetInIdle = false;
        _isSendTarget = true;
        m_nowFrame = 0;
        m_currentId = -1;
        m_heroPromotionList.Clear();

    }

    private void ClearObserver()
    {
        isFake = false;
    }

    public override void Release()
    {
        //         Diagnostic.Log(m_soGame.World.fightContext.director.frameCount+" " 
        //                        + playerData.ChairId + " " 
        //                        + m_soGame.CurrentTotalTurnCount
        //                        + " ChessBattle Detach Queue " + queueId);
        base.Release();
        SetState(PlayerMoveMessage.Stop);
        ClearDropData();
        ClearFlag();
        ClearObserver();

        m_waitTime = Fix64.zero;
    }

    // 掉落盒子皮肤
    public bool CanUseDropBoxSkin(int skinId)
    {
        if (m_usedDropboxDict.TryGetValue(TinyId, out List<int> usedList))
        {
            if (usedList.Contains(skinId))
                return false;
        }
        return true;
    }

    // 掉落盒子皮肤
    public void AddUsedDropBoxSkin(int skinId)
    {
        if (!m_usedDropboxDict.TryGetValue(TinyId, out List<int> usedList))
        {
            usedList = new List<int>();
            m_usedDropboxDict.Add(TinyId, usedList);
        }
        usedList.Add(skinId);
    }

    public void LockSendTargetByTime(Fix64 lockTime)
    {
        if (lockTime > Fix64.zero)
        {
            isSendTarget = false;
            m_waitTime = lockTime;
            AutoEnableSendTargetInIdle = false;
        }

        PrintLog("Lock Send Target By Time locktime: " + m_waitTime);
    }

    public void LockSendTargetByIdle()
    {
        isSendTarget = false;
        m_waitTime = Fix64.zero;
        AutoEnableSendTargetInIdle = true;

        PrintLog("Lock SendTarget By Idle");
    }

    public void UnlockSendTarget()
    {
        if (!isSendTarget)
        {
            isSendTarget = true;
            PrintLog("Unlock Send Target");
        }

        m_waitTime = Fix64.zero;
        AutoEnableSendTargetInIdle = false;

        //Diagnostic.Log("[Xiaobai][ChairId:{0}] UnlockSendTarget", ChairId);
    }

    // 需要增加断线重连的 执行装备功能
    #region 断线重连
    public override void DoRegainData(RegainPlayerData regainPlayerData, RegainLogicPlayerData regainLogicPlayerData)
    {
        

        base.DoRegainData(regainPlayerData, regainLogicPlayerData);

    }


    public int GetExecuteEquipmentCount()
    {
        int result = 0;
        foreach (var v in m_executeItems)
        {
            int entityid = v.iEntityID;
            ACG_MonsterDropItem dropItem = m_equipMgr.GetMonsterDropItem(entityid);
            if (dropItem != null && dropItem.vecEquipment != null)
            {
                result += dropItem.vecEquipment.Count;
            }
        }
        return result;
    }


    public override void GetRegainData(RegainPlayerData regainPlayerData, RegainLogicPlayerData regainLogicPlayerData)
    {
        base.GetRegainData(regainPlayerData, regainLogicPlayerData);

       
    }

    #endregion

    #region 设置移动方式

    #region 这三个只有表现层在做，逻辑层是直接赋值的

    private FVector2 end = new FVector2();

    /// <summary>
    /// 
    /// </summary>
    /// <param name="transform"></param>
    /// <param name="runAfterDir">跑回去以后小小英雄的朝向</param>
    public void GoBack(FTransform transform, FVector3 runAfterDir)
    {
        if (isFake)
            return;

        LockCustomAction = true;
        end.x = transform.position.x;
        end.y = transform.position.z;
        m_afterDir = runAfterDir;

        m_movementState.SetInt(MovementStateMachine.MoveNum, (int)EndMoveWay.Idle);
        base.SetTarget(end);
        StartRunWay();
        LockSendTargetByIdle();
        InnerLog("Go Back");
    }

    public void JumpTo(FTransform transform)
    {
        if (isFake)
            return;

        ResetTransform(transform);
        // 避免开跳的时候位置闪现
        //end.x = transform.position.x;
        //end.y = transform.position.z;
        //base.SetTarget(end);
        SetState(PlayerMoveMessage.JumpTo);
    }

    public void JumpBack(FTransform transform)
    {
        if (isFake)
            return;

        ResetTransform(transform);
        // 避免开跳的时候位置闪现
        //end.x = transform.position.x;
        //end.y = transform.position.z;
        //base.SetTarget(end);
        SetState(PlayerMoveMessage.JumpBack);
    }
    #endregion

    public bool SetTargetCore(FVector2 end)
    {
        if (base.SetTarget(end))
        {
            PrepareMove();

            return true;
        }

        return false;
    }

    public void PrepareMove()
    {
        m_passItems.Clear();
        FindPassItem();

        DecideNextMoveWay();
    }

    public bool IsLockOperation()
    {
        return m_battleLogicField != null && m_battleLogicField.IsLockOperation(ChairId);
    }

    // 这个决定移动方式
    public override bool SetTarget(FVector2 end)
    {
        if (isFake)
            return false;
        if (!isSendTarget)
            return false;
        if (IsLockOperation())
            return false;

        //if (moveState == PlayerMoveMessage.JumpTo
        //    || moveState == PlayerMoveMessage.JumpBack
        //    || moveState == PlayerMoveMessage.GoBack)
        //    return;

        return SetTargetCore(end);
    }

    public override void PlayAction(int actionId)
    {
        if (moveState == PlayerMoveMessage.JumpTo
            || moveState == PlayerMoveMessage.JumpBack
            || LockCustomAction)
        { // 表示这个时候不能播动作
            actionId = -1;
        }

        if (actionId != -1)
        {
            if (isMoving)
            {
                StopMove(true);
            }

            bool isRootMotion = _playerConfig.isRootMotion(actionId);
            if (isRootMotion)
            {
                if (isFake || !isSendTarget || IsLockOperation())
                {
                    actionId = -1;
                }
                else
                {
                    Fix64 dis = _playerConfig.GetDistance(actionId);
                    FVector3 dst = m_dir * dis + fTransform.position;
                    FVector2 tmpDst = new FVector2(dst.x, dst.z);
                    SDFPathConfig sdfPathConfig = m_movementCtrl.SDFPath;
                    if (sdfPathConfig.FindNearPosition(ref tmpDst, ref Radius))
                    {
                        var r = sdfPathConfig.Sample(ref tmpDst);
                        if (r < Radius)
                        {
                            actionId = -1;
                        }
                    }

                    if (actionId != -1)
                    {
                        m_actionId = actionId;
                        SetTargetCore(tmpDst);
                        SetState(PlayerMoveMessage.RootMotion);
                    }
                }
            }
        }

#if ACGGAME_CLIENT
        if (isOpenQueue())
        {
            PlayActionMsg actionMsgMsg = LogicMsgPool.Instance.GetMsg<PlayActionMsg>();
            actionMsgMsg.actionId = actionId;
            SendData(actionMsgMsg);
        }
#endif

        World.m_triggerMgr.SendPlayerEvent(ChairId, E_SO_TRIGGER_TYPE.PlayClickAction, actionId);

        for (int i = 0; i < m_followers.Count; ++i)
        {
            var followerId = m_followers[i];
            World.m_triggerMgr.SendPlayerEvent(followerId, E_SO_TRIGGER_TYPE.FollowPlayerPlayClickAction, actionId);
        }
    }


    #endregion

    #region 小队长编辑模式

    public void SetUpdateConfig(string configName)
    {
        LoadNewConfig(configName);

#if ACGGAME_CLIENT
        if (isOpenQueue())
        {
            PlayerConfigMsg playerConfigMsg = LogicMsgPool.Instance.GetMsg<PlayerConfigMsg>();
            playerConfigMsg.configName = configName;

            SendData(playerConfigMsg);
        }
#endif
    }

    public void SetPlayerMoveWay(PlayerMoveMessage moveWay)
    {
        GoBack(BattleLogicField.ownerTransform, (BattleLogicField.ownerTransform.position - fTransform.position).normalized);
        m_moveWay = moveWay;
    }

    public void ClearCollisionData()
    {
        m_waitDropItems.Clear();
        m_allDropItems.Clear();
        m_collisioningDropItems.Clear();
        m_executeItems.Clear();
        m_executingItems.Clear();
        m_rayCastItems.Clear();
        m_passItems.Clear();
        m_removeWaitItem.Clear();
    }

    public void SetMovingWay(FVector2 end)
    {
        if (m_moveWay == PlayerMoveMessage.None)
        {
            SetTarget(end);
        }

        if (base.SetTarget(end))
        {
            switch (m_moveWay)
            {
                case PlayerMoveMessage.StartRun:
                    StartRunWay();
                    break;
                case PlayerMoveMessage.Rush:
                    StartRushWay();
                    break;
                case PlayerMoveMessage.StartWalk:
                    StartWalkWay();
                    break;
                case PlayerMoveMessage.JumpTo:
                    JumpTo(BattleLogicField.ownerTransform);
                    break;
                case PlayerMoveMessage.JumpBack:
                    JumpBack(BattleLogicField.ownerTransform);
                    break;
                default:
                    break;
            }
        }
    }

    #endregion

    #region Update

    public override void TriggerCollision(bool teleport = false)
    {
        if (isFake)
            return;

        if (Collision())
        {
            // 判断碰撞的物体是否是最近的物体或者最远的物体
            int deleteType = (int)LogicDropType.None;
            Fix64 deleteColiderR = Fix64.zero;

            foreach (ChessBattleTempDropItem item in m_executeItems)
            {
                int deleteId = -1;
                for (int i = 0; i < m_passItems.Count; i++)
                {
                    if (item.iEntityID == m_passItems[i].iEntityID)
                    {
                        deleteType = item.itemType;
                        deleteColiderR = m_passItems[i].coliderR;
                        deleteId = i;
                        break;
                    }
                }

                if (deleteId != -1)
                    m_passItems.RemoveAt(deleteId);
            }

            if (deleteType != (int)LogicDropType.None)
            {
                /*
                Fix64 sqrDis = FVector3.SqrDistance(dst, fTransform.position);
                Fix64 minDis = deleteColiderR * 2 + m_coliderR;
                minDis *= minDis;

                if (sqrDis > minDis)
                {
                    // 只有在移动和碰撞到物体才会做下一个决策
                    if (isMoving)
                        DecideNextMoveWay();
                }
                */

                World.m_triggerMgr.SendPlayerEvent(playerData.PlayerId, E_SO_TRIGGER_TYPE.PickDropbox, teleport ? 1 : 0);
                if (m_allDropItems.Count == 0)
                {
                    World.m_triggerMgr.SendPlayerEvent(playerData.PlayerId, E_SO_TRIGGER_TYPE.PickAllDropbox, teleport ? 1 : 0);
                }
            }
        }
    }

    private void DeletePassItem(int entityId)
    {
        int deleteId = -1;
        for (int i = 0; i < m_passItems.Count; i++)
        {
            if (entityId == m_passItems[i].iEntityID)
            {
                deleteId = i;
                break;
            }
        }

        if (deleteId != -1)
            m_passItems.RemoveAt(deleteId);
    }

    public override void RunTick(Fix64 deltaTime)
    {
        if (!isFake)
        {
            tempEquipCount = 0;
            tempHeroCount = 0;

            ExecuteCollisionResult();

            base.RunTick(deltaTime);

            UpdateWaitItem(deltaTime);

            // 是否可碰撞
            if (isMoving)
            {
                // 碰撞
                TriggerCollision();
            }

            // 阻止出场地
            //InSideField();

            if (m_waitTime > Fix64.zero)
            {
                m_waitTime -= deltaTime;
                if (m_waitTime <= Fix64.zero)
                {
                    m_waitTime = Fix64.zero;
                    isSendTarget = true;

                    PrintLog("AutoUnlockSendTarget by wait time");
                    //Diagnostic.Log("[Xiaobai][ChairId:{0}] AutoUnlockSendTarget By Time", ChairId);
                }
            }

            if (AutoEnableSendTargetInIdle)
            {
                if (movementState.currentState.name == "Stop")
                {
                    isSendTarget = true;
                    AutoEnableSendTargetInIdle = false;

                    PrintLog("AutoUnlockSendTarget by idle");
                    //Diagnostic.Log("[Xiaobai][ChairId:{0}] AutoUnlockSendTarget By Idle", ChairId);
                }
            }

            // 升星专用
            if (m_currentId != -1)
            {
                m_nowFrame++;
                if (m_nowFrame > m_maxFrame)
                {
#if ACGGAME_CLIENT
                    TAC_TCmdS2CNotifyPromotionFinish msg = playerData.Alloc<TAC_TCmdS2CNotifyPromotionFinish>();
                    msg.i8ChairID = playerData.ChairId;
                    msg.type = m_currentId;
                    playerData.EqueueMessage(msg);
#endif
                    m_currentId = -1;
                    m_nowFrame = 0;
                }
            }
            else if (m_heroPromotionList.Count > 0)
            {
                m_currentId = m_heroPromotionList[0];
                m_heroPromotionList.RemoveAt(0);
            }
        }

        //m_taskCtrl.Update();
    }
    #endregion

    #region 判断移动方式
    // 判断以哪种方式移动
    protected override void DecideNextMoveWay()
    {
        m_movementState.SetInt(MovementStateMachine.MoveNum, (int)EndMoveWay.Idle);
        if (InAttackAndSimpleRun())
        {
            SetState(PlayerMoveMessage.SimpleRun);
            return;
        }

        // 无装备
        if (m_passItems.Count == 0)
        {
            // 从停到走
            StartWalkWay();
            return;
        }

        // 有装备
        ChessBattleLogicDropItem item = m_passItems[0];

        if (!m_isHome && item.isHomeDrop)
        {
            // 从停到走
            StartWalkWay();
            return;
        }

        // 判断障碍物距离
        bool ignoreStartRun = false;
        Fix64 disSqr = FVector3.SqrDistance(fTransform.position, m_sdfMovementCtrl.finalDst);
        Fix64 rushDisSqr = _playerConfig.rushDisFix64 * _playerConfig.rushDisFix64;
        if (rushDisSqr > disSqr)
        {
            SetState(PlayerMoveMessage.Rush);
        }
        else
        {
            SDFPathConfig config = GetSDFPath();
            if (config != null && disSqr < config.RuntimeRealMapHalfSizeSqr)
            {
                ignoreStartRun = true;
            }

            // 走冲刺
            m_movementState.SetInt(MovementStateMachine.MoveNum, (int)EndMoveWay.Rush);

            StartRunWay(ignoreStartRun);
        }
    }

    #endregion

    #region 碰撞

    // 清理Coin
    public void ClearCoin()
    {
        if (isFake)
            return;

        for (int i = m_waitDropItems.Count - 1; i > -1; i--)
        {
            ChessBattleLogicDropItem item = m_waitDropItems[i];
            if (item.itemType == LogicDropType.Coin)
            {
                PrintLog("Clear WaitIcon " + item.iEntityID);
                item.Release();
                m_waitDropItems.RemoveAt(i);
            }
        }

        List<KeyValuePair<int, ChessBattleLogicDropItem>> dropItems = m_allDropItems.GetLinkedHashMapList();
        foreach (KeyValuePair<int, ChessBattleLogicDropItem> valuePair in dropItems)
        {
            ChessBattleLogicDropItem item = valuePair.Value;
            if (item.itemType == LogicDropType.Coin)
            {
                PrintLog("Clear CollisonIcon " + item.iEntityID);
                item.Release();
                m_allDropItems.Remove(valuePair.Key);
                DeletePassItem(item.iEntityID);
            }
        }

        for (int i = m_executeItems.Count - 1; i > -1; i--)
        {
            ChessBattleTempDropItem item = m_executeItems[i];
            if (item.itemType == (int)LogicDropType.Coin)
            {
                PrintLog("Clear ExecuteIcon " + item.iEntityID);
                m_executeItems.RemoveAt(i);
            }
        }

        //DecideNextMoveWay();
    }

    public void ClearDoot(List<int> doots)
    {
        if (isFake)
            return;

        for (int i = m_waitDropItems.Count - 1; i > -1; i--)
        {
            ChessBattleLogicDropItem item = m_waitDropItems[i];
            if (item.itemType == LogicDropType.Doot)
            {
                doots.Add(item.iEntityID);
                PrintLog("Clear WaitIcon Doot " + item.iEntityID);
                item.Release();
                m_waitDropItems.RemoveAt(i);
            }
        }

        List<KeyValuePair<int, ChessBattleLogicDropItem>> dropItems = m_allDropItems.GetLinkedHashMapList();
        foreach (KeyValuePair<int, ChessBattleLogicDropItem> valuePair in dropItems)
        {
            ChessBattleLogicDropItem item = valuePair.Value;
            if (item.itemType == LogicDropType.Doot)
            {
                doots.Add(item.iEntityID);
                PrintLog("Clear CollisonIcon Doot " + item.iEntityID);
                item.Release();
                m_allDropItems.Remove(valuePair.Key);
                DeletePassItem(item.iEntityID);
            }
        }

        for (int i = m_executeItems.Count - 1; i > -1; i--)
        {
            ChessBattleTempDropItem item = m_executeItems[i];
            if (item.itemType == (int)LogicDropType.Doot)
            {
                doots.Add(item.iEntityID);
                PrintLog("Clear ExecuteIcon Doot " + item.iEntityID);
                m_executeItems.RemoveAt(i);
            }
        }
    }

    public void RemoveIconById(int id)
    {
        if (isFake)
            return;

        for (int j = m_waitDropItems.Count - 1; j >= 0; j--)
        {
            if (m_waitDropItems[j].iEntityID == id)
            {
                m_waitDropItems[j].Release();
                m_waitDropItems.RemoveAt(j);
                return;
            }
        }

        for (int j = m_executeItems.Count - 1; j >= 0; j--)
        {
            if (m_executeItems[j].iEntityID == id)
            {
                m_executeItems.RemoveAt(j);
                return;
            }
        }

        if (m_allDropItems.ContainsKey(id))
        {
            m_allDropItems[id].Release();
            m_allDropItems.Remove(id);
            DeletePassItem(id);
        }
    }

    public void ClearSpacePirateIcon(List<ACG_DropItemCoin> coins)
    {
        if (isFake)
            return;

        for (int i = 0; i < coins.Count; i++)
        {
            for (int j = m_waitDropItems.Count - 1; j >= 0; j--)
            {
                if (m_waitDropItems[j].iEntityID == coins[i].iEntityID)
                {
                    m_waitDropItems[j].Release();
                    m_waitDropItems.RemoveAt(j);
                    break;
                }
            }

            for (int j = m_executeItems.Count - 1; j >= 0; j--)
            {
                if (m_executeItems[j].iEntityID == coins[i].iEntityID)
                {
                    m_executeItems.RemoveAt(j);
                    break;
                }
            }

            if (m_allDropItems.ContainsKey(coins[i].iEntityID))
            {
                m_allDropItems[coins[i].iEntityID].Release();
                m_allDropItems.Remove(coins[i].iEntityID);
                DeletePassItem(coins[i].iEntityID);
            }
        }
        //DecideNextMoveWay();
    }

    // 掉落物品加入等待队列
    public ChessBattleLogicDropItem AddDropItem(LogicDropType type, int entity, FVector3 homePos, FVector3 enemyPos)
    {
        bool isHomeDrop = m_isHome;
        if (isObserver)
        {
            isHomeDrop = m_logicPlayerData.faker.m_isHome;
        }

        ChessBattleLogicDropItem logicDropItem = new ChessBattleLogicDropItem(ChairId, type, entity, homePos, enemyPos, isHomeDrop);
        m_waitDropItems.Add(logicDropItem);
        InnerLog("Add Wait Drop item " + entity + " " + type + " home " + homePos + " enemy " + enemyPos);
        return logicDropItem;
    }

    public void ResetAllDropItem()
    {
        foreach (var item in m_allDropItems.Values)
        {
            item.isHomeDrop = true;
        }
    }


    // 断线重连，直接加入到可碰撞队列
    private ChessBattleLogicDropItem AddExitDropTime(LogicDropType type, int entity, FVector3 homePos, FVector3 enemyPos, Fix64 time)
    {
        ChessBattleLogicDropItem result = null;
        if (!m_allDropItems.TryGetValue(entity, out result))
        {
            result = new ChessBattleLogicDropItem(ChairId, type, entity, homePos, enemyPos, m_isHome);
            result.SetTime(time);
            InnerLog("Add Drop item " + result.iEntityID + " " + result.itemType + " pos = " + fTransform.position);
            m_allDropItems.Add(result.iEntityID, result);
        }
        else
        {
            PrintLog("same entity " + entity + " add twice");
        }

        return result;
    }

    // 加入可碰撞队列
    private void AddDropItem(ChessBattleLogicDropItem dropItem)
    {
        bool hasAddDropItem = false;
        if (m_allDropItems.ContainsKey(dropItem.iEntityID))
        {
            //海盗盒子ID会重复
            if (dropItem.itemType != LogicDropType.DorpBox)
            {
                Diagnostic.Error("same entity " + dropItem.iEntityID + " add twice");
            }
            else
            {
                m_allDropItems[dropItem.iEntityID] = dropItem;
                hasAddDropItem = true;
            }
        }

        if (!hasAddDropItem)
        {
            InnerLog("Add Drop item " + dropItem.iEntityID + " " + dropItem.itemType + " pos = " + fTransform.position);
            m_allDropItems.Add(dropItem.iEntityID, dropItem);
        }

        if (isMoving)
        {
            if (FindPassItem())
            {
                DecideNextMoveWay();
            }
        }

        // 碰撞
        TriggerCollision();
    }

    private List<int> m_removeWaitItem = new List<int>(8);
    // 将超时物品加入可碰撞队列
    private void UpdateWaitItem(Fix64 deltaTime)
    {
        if (m_waitDropItems.Count <= 0)
        {
            return;
        }
        m_removeWaitItem.Clear();
        for (int i = 0; i < m_waitDropItems.Count; i++)
        {
            ChessBattleLogicDropItem logicDropItem = m_waitDropItems[i];
            logicDropItem.RunTick(deltaTime);
            if (logicDropItem.isCollision)
            {
                m_removeWaitItem.Add(logicDropItem.iEntityID);
            }
        }

        foreach (int deleteItemId in m_removeWaitItem)
        {
            ChessBattleLogicDropItem result = null;
            int resultId = -1;
            for (int i = 0; i < m_waitDropItems.Count; i++)
            {
                ChessBattleLogicDropItem item = m_waitDropItems[i];
                if (item.iEntityID == deleteItemId)
                {
                    result = item;
                    resultId = i;
                    break;
                }
            }

            if (resultId != -1)
            {
                AddDropItem(result);
                m_waitDropItems.RemoveAt(resultId);
            }
        }
    }

    #region 是否可碰撞

    public bool CanCollision = true;

    #endregion


    int tempEquipCount = 0;
    int tempHeroCount = 0;
    // 因为调用捡装备接口会立刻生成新装备，然后又会触发Collision，从而导致Collison触发两次，导致迭代器不能使用
    // 所以只能分帧，先删除本地的m_allDropItems，然后下一帧再执行装备接口
    // 所以断线重连要注意
    private bool Collision()
    {
        bool result = false;
        m_collisioningDropItems.Clear();
        m_collisioningDropItems.AddRange(m_allDropItems.Values);

        if (!CanCollision)
            return result;

        // 先把碰撞物体加入到调用装备的列表中，同时删除装备
        foreach (ChessBattleLogicDropItem item in m_collisioningDropItems)
        {
            if (m_isHome || !item.isHomeDrop)
            {
                Fix64 sqrDis = FVector3.SqrDistance(fTransform.position, item.GetPos(m_isHome));
                // 判断是否大于半径
                if (item.itemType == LogicDropType.DorpBox)
                {
                    if (sqrDis < sqrMaxBoxDis)
                    {
                        ChessBattleTempDropItem tempItem = new ChessBattleTempDropItem(item);
                        //if (m_equipMgr.OnCheckCanPickUpEquip(m_dropBoxMgr.GetEquipCount()))
                        {
                            m_executeItems.Add(tempItem);
                            m_allDropItems.Remove(item.iEntityID);
                            item.Release();
                            InnerLog("Collision Drop Box " + item.iEntityID);
                        }
                        result = true;
                    }
                }
                else if (item.itemType == LogicDropType.Monster)
                {
                    if (sqrDis < sqrMaxItemDis)
                    {
                        ChessBattleTempDropItem tempItem = new ChessBattleTempDropItem(item);
                        ACG_MonsterDropItem dropitem = m_equipMgr.GetMonsterDropItem(item.iEntityID);
                        if (dropitem == null) throw new ArgumentNullException(nameof(dropitem));

                        if (CheckCanPickUpDropItem(dropitem))
                        {
                            m_executeItems.Add(tempItem);
                            m_allDropItems.Remove(item.iEntityID);
                            item.Release();
                            InnerLog("Collision monster drop item " + item.iEntityID);
                        }
                        result = true;
                    }
                }
                else if (item.itemType == LogicDropType.Coin)
                {
                    if (sqrDis < sqrMaxIconDis)
                    {
                        InnerLog("Collision drop icon " + item.iEntityID);

                        ChessBattleTempDropItem tempItem = new ChessBattleTempDropItem(item);
                        m_executeItems.Add(tempItem);

                        m_allDropItems.Remove(item.iEntityID);
                        item.Release();
                        result = true;
                    }
                }
                else if (item.itemType == LogicDropType.ShoesOrBones)
                {
                    if (sqrDis < sqrMaxIconDis)
                    {
                        InnerLog("Collision drop ShoesOrBones " + item.iEntityID);

                        ChessBattleTempDropItem tempItem = new ChessBattleTempDropItem(item);
                        m_executeItems.Add(tempItem);

                        m_allDropItems.Remove(item.iEntityID);
                        item.Release();
                        result = true;
                    }
                }
                else if (item.itemType == LogicDropType.Doot)
                {
                    if (sqrDis < sqrMaxIconDis)
                    {
                        InnerLog("Collision drop Doot " + item.iEntityID);

                        ChessBattleTempDropItem tempItem = new ChessBattleTempDropItem(item);
                        m_executeItems.Add(tempItem);

                        m_allDropItems.Remove(item.iEntityID);
                        item.Release();
                        result = true;
                    }
                }
            }
        }

        return result;
    }

    #region 检查碰撞后是否可以拾取


    private bool CheckCanPickUpDropItem(ACG_MonsterDropItem dropitem)
    {
        bool canPickUp = true;
        //应捡尽捡功能
        if (PickEquipmentAndCoinAsMuch(dropitem))
            canPickUp = false;

        //检测装备是否满了
        if (dropitem.vecEquipment != null && dropitem.vecEquipment.Count != 0
            && !m_equipMgr.OnCheckCanPickUpEquip(dropitem.vecEquipment.Count + tempEquipCount))
            canPickUp = false;

        //检测英雄备战区是否满了
        if (GameUtilCore.CanPickUpOnWaitAreaFull(dropitem.itemType))
        {
            if (dropitem.vecDropHeroID != null && dropitem.vecDropHeroID.Count != 0
               /* && !m_equipMgr.OnCheckCanPickUpHero(dropitem.vecDropHeroID.Count + tempHeroCount)*/)
            {
                PickHeroAsMuch(dropitem);
                canPickUp = false;
            }
        }
        //if (World.CSoGame.DaulModeCtlIns.IsRoyalRuneDrop(dropitem))
        //{
        //    if (!m_equipMgr.OnCheckCanPickUpHeroWithPromotion(tempHeroCount, dropitem.vecDropHeroID))
        //        canPickUp = false;
        //    if (!m_equipMgr.OnCheckCanPickUpEquip(dropitem.vecEquipment.Count + tempEquipCount))
        //        canPickUp = false;
        //}

        if (canPickUp)
        {
            //已经捡了的装备数量
            if (dropitem.vecEquipment != null)
                tempEquipCount += dropitem.vecEquipment.Count;
            //已经捡了的英雄数量
            if (dropitem.vecDropHeroID != null)
                tempHeroCount += dropitem.vecDropHeroID.Count;
        }

        return canPickUp;
    }

    /// <summary>
    /// 应捡尽捡功能
    /// 目前只用于海克斯掉落 不支持英雄
    /// True:该球不能捡干净，所以不能移除球
    /// </summary>
    /// <param name="dropitem"></param>
    /// <param name="tempEquipCount"></param>
    /// <returns></returns>
    private bool PickEquipmentAndCoinAsMuch(ACG_MonsterDropItem dropitem)
    {
        var player = World.CSoGame.PlayerMgr.GetPlayerAt(ChairId);
        if (dropitem.itemType == (int)MonsterDropItemType.Draconic && player != null)
        {
            ACG_MonsterDropItem cloneDropItem = null;
            if (dropitem.vecEquipment != null && !m_equipMgr.OnCheckCanPickUpEquip(dropitem.vecEquipment.Count + tempEquipCount))
            {
                cloneDropItem = new ACG_MonsterDropItem();
                cloneDropItem.vecEquipment = new List<ACG_Equipment>();
                int totalEquipCount = dropitem.vecEquipment.Count;
                int canPickEquipCount = GameDataMng.Instance.MAX_PLAYER_EQUIPMENT_COUNT - m_equipMgr.PlayerEquipments.Count - tempEquipCount;
                for (int i = totalEquipCount - 1; i >= totalEquipCount - canPickEquipCount; i--)
                {
                    cloneDropItem.vecEquipment.Add(dropitem.vecEquipment[i]);
                    dropitem.vecEquipment.RemoveAt(i);
                }
            }

            //if (dropitem.vecDropHeroID != null && m_soGame != null)
            //{
            //    if (cloneDropItem == null) cloneDropItem = new ACG_MonsterDropItem();
            //    cloneDropItem.vecDropHeroID = new List<int>();
            //    if (dropitem.vecDropHeroID.Contains(m_soGame.TomeOfEmblemsCtrl.EMBLEMS_CONFIG))
            //    {
            //        if(player != null)
            //        {
            //            int emptyPosCount = player.GetReadyHero().GetEmptyPos();
            //            int totalHeroCount = dropitem.vecDropHeroID.Count;
            //            if(dropitem.vecDropHeroID.Count > emptyPosCount)
            //            {
            //                for(int i = totalHeroCount - 1; i >= totalHeroCount - emptyPosCount; i--)
            //                {
            //                    cloneDropItem.vecDropHeroID.Add(dropitem.vecDropHeroID[i]);
            //                    dropitem.vecDropHeroID.RemoveAt(i);
            //                }
            //            }
            //            else
            //            {
            //                cloneDropItem.vecDropHeroID.AddRange(dropitem.vecDropHeroID);
            //                dropitem.vecDropHeroID.Clear();
            //            }
            //        }
            //    }
            //    else
            //    {
            //        cloneDropItem.vecDropHeroID.AddRange(dropitem.vecDropHeroID);
            //        dropitem.vecDropHeroID.Clear();
            //    }
            //}

            if (cloneDropItem != null)
            {
                //cloneDropItem.iEntityID = player.DropItemSeqNo;
                cloneDropItem.iDropPoolID = dropitem.iDropPoolID;
                cloneDropItem.iHeroCoinCount = dropitem.iHeroCoinCount;
                dropitem.iHeroCoinCount = 0;
                cloneDropItem.iSkinID = dropitem.iSkinID;
                cloneDropItem.itemCount = dropitem.itemCount;
                dropitem.itemCount = 0;
                cloneDropItem.itemType = dropitem.itemType;
                cloneDropItem.iTurnCount = dropitem.iTurnCount;
                cloneDropItem.stDropPosInfo = (ACG_DropPosInfo)dropitem.stDropPosInfo.DeepClone();
                cloneDropItem.stDropPosInfo.iEntityID = cloneDropItem.iEntityID;
                cloneDropItem.stHeroEntity = dropitem.stHeroEntity;

                player.EquipmentMgr.MonsterDropItems.Add(cloneDropItem);
                ChessBattleLogicDropItem cloneLogicTempDropItem = new ChessBattleLogicDropItem(ChairId, LogicDropType.Monster, cloneDropItem.iEntityID,
                    player.EquipmentMgr.GetDropPos(cloneDropItem.iEntityID, cloneDropItem.stDropPosInfo, true),
                    player.EquipmentMgr.GetDropPos(cloneDropItem.iEntityID, cloneDropItem.stDropPosInfo, false), m_isHome);

                ChessBattleTempDropItem tempDropItem = new ChessBattleTempDropItem(cloneLogicTempDropItem);
                m_executeItems.Add(tempDropItem);
                cloneLogicTempDropItem.Release();

                if (cloneDropItem.vecEquipment != null)
                    tempEquipCount += cloneDropItem.vecEquipment.Count;

                return true;
            }
        }

        return false;
    }

    private void PickHeroAsMuch(ACG_MonsterDropItem dropitem)
    {
//        var player = World.CSoGame.PlayerMgr.GetPlayerAt(ChairId);
//        if (dropitem.itemType == (int)MonsterDropItemType.Draconic && player != null)
//        {
//            ACG_MonsterDropItem cloneDropItem = null;
//            if (dropitem.vecDropHeroID != null && dropitem.vecDropHeroID.Count > 0)
//            {
//                if (cloneDropItem == null) cloneDropItem = new ACG_MonsterDropItem();
//                cloneDropItem.vecDropHeroID = new List<int>();
//                int waitEmptyCount = player.GetReadyHero().GetEmptyPosCount();
//                waitEmptyCount -= tempHeroCount;
//                for (int i = dropitem.vecDropHeroID.Count - 1; i >= 0 && waitEmptyCount > 0; i--, waitEmptyCount--)
//                {
//                    cloneDropItem.vecDropHeroID.Add(dropitem.vecDropHeroID[i]);
//                    dropitem.vecDropHeroID.RemoveAt(i);
//                }
//            }

//            if (cloneDropItem != null && cloneDropItem.vecDropHeroID.Count > 0)
//            {
//#if ACGGAME_CLIENT
//                ACG_TCmdS2CNotifyMonsterDrop cloneDrop = player.Alloc<ACG_TCmdS2CNotifyMonsterDrop>();
//                cloneDrop.iPlayerID = player.ChairId;
//#endif
//                cloneDropItem.iEntityID = player.DropItemSeqNo;
//                cloneDropItem.iDropPoolID = dropitem.iDropPoolID;
//                cloneDropItem.iHeroCoinCount = 0;
//                cloneDropItem.iSkinID = dropitem.iSkinID;
//                cloneDropItem.itemCount = 0;
//                cloneDropItem.itemType = dropitem.itemType;
//                cloneDropItem.iTurnCount = dropitem.iTurnCount;
//                cloneDropItem.stDropPosInfo = (ACG_DropPosInfo)dropitem.stDropPosInfo.DeepClone();
//                cloneDropItem.stDropPosInfo.iEntityID = cloneDropItem.iEntityID;
//                cloneDropItem.stDropPosInfo.iDropType = (int)EDropType.FROM_NONE;
//                cloneDropItem.stHeroEntity = dropitem.stHeroEntity;

//                player.EquipmentMgr.MonsterDropItems.Add(cloneDropItem);
//                ChessBattleLogicDropItem cloneLogicTempDropItem = new ChessBattleLogicDropItem(ChairId, LogicDropType.Monster, cloneDropItem.iEntityID,
//                    player.EquipmentMgr.GetDropPos(cloneDropItem.iEntityID, cloneDropItem.stDropPosInfo, true),
//                    player.EquipmentMgr.GetDropPos(cloneDropItem.iEntityID, cloneDropItem.stDropPosInfo, false), m_isHome);

//#if ACGGAME_CLIENT
//                cloneDrop.stMonsterDropItem = cloneDropItem;
//                cloneDrop.vecMonsterDropItem = player.EquipmentMgr.MonsterDropItems;
//                player.EqueueMessage(cloneDrop);
//#endif

//                ChessBattleTempDropItem tempDropItem = new ChessBattleTempDropItem(cloneLogicTempDropItem);
//                m_executeItems.Add(tempDropItem);
//                cloneLogicTempDropItem.Release();

//                if (cloneDropItem.vecDropHeroID != null)
//                    tempHeroCount += cloneDropItem.vecDropHeroID.Count;
//            }
//        }
    }

    #endregion

    // 执行碰撞结果
    private void ExecuteCollisionResult()
    {
        // 将当前删除物体加入队列
        m_executingItems.Clear();
        m_executingItems.AddRange(m_executeItems);

        // 清空当前删除队列，为下一个加入
        m_executeItems.Clear();

        if (World == null)
            return;

        foreach (ChessBattleTempDropItem item in m_executingItems)
        {
            switch (item.itemType)
            {
                case (int)LogicDropType.DorpBox:
                    InnerLog("Pick up dropBox");
                    World.CSoGame.ReqOpenDropBox(playerData.ChairId);
                    break;
                case (int)LogicDropType.Monster:
                    InnerLog("Pick up monster " + item.iEntityID);
                    World.CSoGame.ReqPickUpMonsterDrop(playerData.ChairId, item.iEntityID);
                    break;
                case (int)LogicDropType.Coin:
                    InnerLog("Pick up Coin " + item.iEntityID);
                    World.CSoGame.ReqPickUpDropCoin(playerData.ChairId, item.iEntityID);
                    break;
                case (int)LogicDropType.ShoesOrBones:
                    InnerLog("Pick up ShoesOrBones " + item.iEntityID);
                    World.CSoGame.ReqPickUpShoesOrBones(playerData.ChairId, item.iEntityID);
                    break;
                case (int)LogicDropType.Doot:
                    InnerLog("Pick up Doot " + item.iEntityID);
                    World.CSoGame.ReqPickUpDoot(playerData.ChairId, item.iEntityID);
                    break;
                default:
                    break;
            }
        }

    }

    // 移动范围检测
    //private void InSideField()
    //{
    //    FVector3 pos = this.cacheFTrans.position;
    //    if (pos.x > ChessBattleLogicField.m_rightUp.x
    //        || pos.x < ChessBattleLogicField.m_leftDown.x
    //        || pos.z > ChessBattleLogicField.m_rightUp.y
    //        || pos.z < ChessBattleLogicField.m_leftDown.y)
    //    {
    //        pos.x = Fix64.Clamp(pos.x, ChessBattleLogicField.m_leftDown.x, ChessBattleLogicField.m_rightUp.x);
    //        pos.z = Fix64.Clamp(pos.z, ChessBattleLogicField.m_leftDown.y, ChessBattleLogicField.m_rightUp.y);
    //        dst = pos;
    //        m_movementState.DoTransitionManually(m_movementState.GetStateName(PlayerMoveMessage.Stop));
    //        //m_movementState.SetInt("MoveState", (int)PlayerMoveMessage.Stop);
    //        InnerLog("Reach edge");
    //    }
    //}

    protected override bool InSideField(ref FVector2 pos)
    {
        ChessBattleLogicField.StaticField ins = ChessBattleLogicField.StaticField.Ins;
        if (pos.x > ins.rightUp.x
            || pos.x < ins.leftDown.x
            || pos.y > ins.rightUp.y
            || pos.y < ins.leftDown.y)
        {
            pos.x = Fix64.Clamp(pos.x, ins.leftDown.x, ins.rightUp.x);
            pos.y = Fix64.Clamp(pos.y, ins.leftDown.y, ins.rightUp.y);
            return true;
        }
        return false;
    }

    #endregion

    #region 射线检测

    // 判断这个射线是否会碰撞
    private bool RayCast()
    {
        m_rayCastItems.Clear();
        if (m_allDropItems.Count == 0)
            return false;

        bool result = false;

        // 计算函数 ax+by+c=0;
        FVector3 pos = fTransform.position;
        Fix64 a = dst.z - pos.z;
        Fix64 b = pos.x - dst.x;
        Fix64 c = dst.x * pos.z - pos.x * dst.z;

        foreach (ChessBattleLogicDropItem item in m_allDropItems.Values)
        {
            if (m_isHome || !item.isHomeDrop)
            {
                // 判断是否是同一个方向
                FVector3 itemPos = item.GetPos(m_isHome);
                FVector3 delta = itemPos - fTransform.position;
                if (FVector3.Dot(m_dir, delta) <= Fix64.zero)
                    continue;

                // 计算点到函数的距离
                Fix64 sqrDis = PointToVectorDistance(a, b, c, itemPos);

                // 判断是否大于半径
                if (item.itemType == LogicDropType.DorpBox)
                {
                    if (sqrDis < sqrMaxBoxDis)
                    {
                        Fix64 sqrPlayerDis = FVector3.SqrDistance(itemPos, fTransform.position);
                        item.sqrPlayerDis = sqrPlayerDis;
                        //Debug.Log("lucky add " + item.itemType + " " + item.iEntityID);
                        m_rayCastItems.Add(item);
                        result = true;
                    }
                }
                else if (item.itemType == LogicDropType.Monster)
                {
                    if (sqrDis < sqrMaxItemDis)
                    {
                        Fix64 sqrPlayerDis = FVector3.SqrDistance(itemPos, fTransform.position);
                        item.sqrPlayerDis = sqrPlayerDis;
                        //Debug.Log("lucky add " + item.itemType + " " + item.iEntityID);
                        m_rayCastItems.Add(item);
                        result = true;
                    }
                }
                else if (item.itemType == LogicDropType.Coin)
                {
                    if (sqrDis < sqrMaxIconDis)
                    {
                        Fix64 sqrPlayerDis = FVector3.SqrDistance(itemPos, fTransform.position);
                        item.sqrPlayerDis = sqrPlayerDis;
                        //Debug.Log("lucky add " + item.itemType + " " + item.iEntityID);
                        m_rayCastItems.Add(item);
                        result = true;
                    }
                }
                else if (item.itemType == LogicDropType.ShoesOrBones)
                {
                    if (sqrDis < sqrMaxIconDis)
                    {
                        Fix64 sqrPlayerDis = FVector3.SqrDistance(itemPos, fTransform.position);
                        item.sqrPlayerDis = sqrPlayerDis;
                        //Debug.Log("lucky add " + item.itemType + " " + item.iEntityID);
                        m_rayCastItems.Add(item);
                        result = true;
                    }
                }
                else if (item.itemType == LogicDropType.Doot)
                {
                    if (sqrDis < sqrMaxIconDis)
                    {
                        Fix64 sqrPlayerDis = FVector3.SqrDistance(itemPos, fTransform.position);
                        item.sqrPlayerDis = sqrPlayerDis;
                        //Debug.Log("lucky add " + item.itemType + " " + item.iEntityID);
                        m_rayCastItems.Add(item);
                        result = true;
                    }
                }
            }
        }

        return result;

    }

    // 计算点到函数的距离
    private Fix64 PointToVectorDistance(Fix64 a, Fix64 b, Fix64 c, FVector3 pos)
    {
        Fix64 up = a * pos.x + b * pos.z + c;
        Fix64 upSqr = up * up;
        Fix64 downSqr = a * a + b * b;
        Fix64 distancSqr = upSqr / downSqr;
        return distancSqr;
    }

    private ChessBattleLogicDropItem findNearItem(List<ChessBattleLogicDropItem> dropItems)
    {
        Fix64 minSqrDis = Fix64.maxValue;
        ChessBattleLogicDropItem result = null;
        foreach (ChessBattleLogicDropItem item in dropItems)
        {
            if (minSqrDis > item.sqrPlayerDis)
            {
                minSqrDis = item.sqrPlayerDis;
                result = item;
            }
        }
        return result;
    }

    private ChessBattleLogicDropItem findFarItem(List<ChessBattleLogicDropItem> dropItems)
    {
        Fix64 maxSqrDis = Fix64.zero;
        ChessBattleLogicDropItem result = null;
        foreach (ChessBattleLogicDropItem item in dropItems)
        {
            if (maxSqrDis < item.sqrPlayerDis)
            {
                maxSqrDis = item.sqrPlayerDis;
                result = item;
            }
        }
        return result;
    }

    private bool FindPassItem()
    {
        if (RayCast())
        {
            m_passItems.Clear();
            ChessBattleLogicDropItem nearItem = findNearItem(m_rayCastItems);
            m_passItems.Add(nearItem);
            if (m_rayCastItems.Count > 1)
            {
                ChessBattleLogicDropItem farItem = findFarItem(m_rayCastItems);
                if (farItem != nearItem)
                    m_passItems.Add(farItem);
            }

            World.m_triggerMgr.SendPlayerEvent(playerData.PlayerId, E_SO_TRIGGER_TYPE.WillPickDropbox, 0);
            return true;
        }

        return false;
    }

    public void Die()
    {
        int observeId = ObserverModel.ObserveId;
        if (observeId != ChairId)
        {
            // 之前观察别人
            ChessBattleCore core = World.CSoGame.chessBattleCore;

            // 移除观察信息
            ObserverModel.ObservePlayer(this);

            // 回到自己战场
            ChessBattleLogicField logicField = core.GetBattleFieldByChairId(ChairId);
            core.GoToBattleField(this, logicField, PlayerState.Owner);
        }
    }

    #endregion

    #region 新手

    public void GuideForceRunToTarget(FVector2 target)
    {
        SetTarget(target);
        StartRunWay();
    }

    public void SetPositionAndRotation(FVector3 Pos, FVector3 Dir)
    {
        if (moveState != PlayerMoveMessage.Stop)
            StopMove();
        fTransform.position = Pos;
        m_dir = Dir;
        dst = Pos;
        fTransform.rotation = FQuaternion.LookRotation(Dir);

#if ACGGAME_CLIENT
        if (isOpenQueue())
        {
            FPositionAndQuaternionMsg positionAndQuaternionMsg = LogicMsgPool.Instance.GetMsg<FPositionAndQuaternionMsg>();
            positionAndQuaternionMsg.msgType = (int)PlayerMoveMessage.PositionAndRotate;
            positionAndQuaternionMsg.positionValue = fTransform.position;
            positionAndQuaternionMsg.rotationValue = fTransform.rotation;
            SendData(positionAndQuaternionMsg);
        }
#endif
    }

    public TinyAttackData GetAttack()
    {
        return LogicPlayerData != null ? LogicPlayerData.GetAttack() : null;
    }

    public Fix64 GetDefenderLockTime()
    {
        var attackData = GetAttack();
        if (attackData != null)
            return attackData.m_defenderLockTime;
        else
            return Fix64.zero;
    }

    private void SyncAttackDataToView()
    { // 只有这个玩家仍然再当前战场才会走到这里
        var attackData = GetAttack();
        if (attackData != null)
        {
            if (attackData.m_inAttack)
            {
                if (isMoving)
                    StopMove(true);
            }

#if ACGGAME_CLIENT
            if (isOpenQueue())
            {
                AttackMsg attackMsg = LogicMsgPool.Instance.GetMsg<AttackMsg>();
                attackMsg.msgType = (int)PlayerMoveMessage.Attack;
                attackMsg.Init(attackData);
                SendData(attackMsg);
            }
#endif
        }
    }

    public void SetAttack(ChessAttackLogicConfig attackCfg, bool isWin = false, bool sendView = true)
    {
        if (LogicPlayerData != null)
        {
            if (sendView)
                LogicPlayerData.SetAttack(World.CSoGame, attackCfg, SyncAttackDataToView, isWin);
            else
                LogicPlayerData.SetAttack(World.CSoGame, attackCfg, null, isWin);
        }
    }

    public void SyncAttackState()
    {
        if (LogicPlayerData != null)
        {
            if (LogicPlayerData.SyncAttackState(World.CSoGame))
            {
#if ACGGAME_CLIENT
                if (isOpenQueue())
                {
                    AttackMsg attackMsg = LogicMsgPool.Instance.GetMsg<AttackMsg>();
                    attackMsg.msgType = (int)PlayerMoveMessage.Attack;
                    attackMsg.Init(GetAttack());
                    SendData(attackMsg);
                }
#endif
            }
        }
    }

#if ACGGAME_CLIENT

    public override int SyncMovingState()
    {
        var q = base.SyncMovingState();

        SyncAttackState();

        return q;
    }
#endif

    public void SetWillHit()
    {
#if ACGGAME_CLIENT
        if (isOpenQueue())
        {
            WillHitMsg attackMsg = LogicMsgPool.Instance.GetMsg<WillHitMsg>();
            attackMsg.msgType = (int)PlayerMoveMessage.WillHit;
            SendData(attackMsg);
        }
#endif
    }

    public void SetHit(bool isPlayerHit, bool isLastHurt, bool willKill, int deductLife, int allDeductLife, int life)
    {
        // 因为野怪关卡的isPlayerHit是瞬间发过来的  这时候就不停下来了 
        if (playerData != null && playerData.CSoGame != null && playerData.CSoGame.IsMonsterQuest() && isPlayerHit)
        {
            if (deductLife == 0) // 如果野怪关卡不扣血 甚至都不用走下去了
            {
                return;
            }
        }
        else
        {
            // 策划需求 受击时禁止移动 并且锁定移动状态2s
            if (isMoving && !InAttack()) // 平局的时候不卡死流程
            {
                StopMove();
            }

            //LockSendTargetByTime(Fix64._01);
        }

#if ACGGAME_CLIENT
        if (isOpenQueue())
        {
            HitMsg hitMsg = LogicMsgPool.Instance.GetMsg<HitMsg>();
            hitMsg.msgType = (int)PlayerMoveMessage.Hit;
            hitMsg.isPlayerHit = isPlayerHit;
            hitMsg.isLastHurt = isLastHurt;
            hitMsg.willKill = willKill;
            hitMsg.deductLife = deductLife;
            hitMsg.allDeductLife = allDeductLife;
            hitMsg.life = life;
            SendData(hitMsg);
        }
        else if (isFake)       // 如果当前是个假人 那就跑去向他真人发一个简单的命中消息 通知真人做一些必备的行为 例如缩小连胜小小英雄
        {
            // 向他真人发消息
            var reader = LogicPlayerData.realer;
            if (reader != null && reader.isOpenQueue())
            {
                HitMsg hitMsg = LogicMsgPool.Instance.GetMsg<HitMsg>();
                hitMsg.msgType = (int)PlayerMoveMessage.SimpleHit;
                hitMsg.isPlayerHit = isPlayerHit;
                hitMsg.isLastHurt = isLastHurt;
                hitMsg.willKill = willKill;
                hitMsg.deductLife = deductLife;
                hitMsg.allDeductLife = allDeductLife;
                hitMsg.life = life;
                reader.SendData(hitMsg);
            }
        }
#endif
    }

    public bool InAttack()
    {
        var attackData = GetAttack();
        return attackData != null && attackData.m_inAttack;
    }

    public bool InAttackAndSimpleRun()
    {
        if (InAttack())
        {
            var attackData = GetAttack();
            return attackData.m_hasRun;
        }
        return false;
    }

    //public bool GetAttackSpeed(out Fix64 speed)
    //{
    //    if (InAttack() && m_attackData.m_attackType == TinyAttackType.RunAndAttack)
    //    {
    //        speed = m_attackData.m_runSpeed;
    //        return true;
    //    }
    //    else
    //    {
    //        speed = Fix64.one;
    //        return false;
    //    }
    //}

    #endregion

    #region 操作
    public void HeroPromotion(TAC_TNotifyWaitHeroPromotion msg)
    {
        foreach (TAC_WaitHeroPromotionInfo info in msg.vecWaitHeroPromotionInfo)
        {
            m_heroPromotionList.Add(info.stNewHero.stHeroEntity.iEntityID);
        }
    }
    #endregion

    #region 观察者模式

    public void SetPosById(int posId)
    {
        if (posId != -1)
        {
            FVector3 newPos = fTransform.position;
            ChessBattleLogicField.StaticField ins = ChessBattleLogicField.StaticField.Ins;
            ChessBattleLogicPlayer observer = ObserverModel.observeLogicPlayer;
            if (observer.m_isHome)
            {
                newPos.x = ins.observerPos[posId];
                newPos.z = ins.up;
            }
            else
            {

                newPos.x = ins.observerPos[6 - posId];
                newPos.z = ins.down;
            }

            fTransform.position = newPos;

            // 刷新一下目标点
            dst = newPos;
            isNewTarget = true;
        }
    }

    #endregion

    public override void PrintLog(string msg)
    {
        if (playerData == null || World == null)
            return;

        LogicGameObjectPrint.ChessBattleLogicPlayerLog(
            World.fightContext != null ? World.fightContext.director.frameCount : 0,
            playerData.UIN,
            playerData.ChairId,
            World.CSoGame != null ? World.CSoGame.CurrentTotalTurnCount : 0,
            msg
            );
    }

    [System.Diagnostics.Conditional("USE_DOTRACE_MANUAL_API")]
    [System.Diagnostics.Conditional("DEBUG_LEVEL_LOG")]
    [System.Diagnostics.Conditional("DEBUG_LEVEL_WARN")]
    [System.Diagnostics.Conditional("DEBUG_LEVEL_ERROR")]
    private void InnerLog(string msg)
    {
        this.PrintLog(msg);
    }
}
