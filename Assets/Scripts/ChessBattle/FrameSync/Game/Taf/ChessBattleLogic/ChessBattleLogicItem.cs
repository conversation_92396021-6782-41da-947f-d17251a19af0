using GameFramework.FMath;
using System;
using Z_PVE;
using ZGame;

[Serializable]
public sealed class ChessBattleTempDropItem : Wup.Jce.JceStruct
{
    public int iEntityID;
    public int itemType;
    public char[] time;
    public bool isHomeDrop;

    public override void WriteTo(Wup.Jce.JceOutputStream _os)
    {
        _os.Write(iEntityID, 0);
        _os.Write(itemType, 1);
        _os.Write(time, 2);
        _os.Write(isHomeDrop, 3);
    }

    public override void ReadFrom(Wup.Jce.JceInputStream _is)
    {
        iEntityID = (int)_is.Read(iEntityID, 0, false);
        itemType = (int)_is.Read(itemType, 1, false);
        time = (char[])_is.Read(time, 2, false);
        isHomeDrop = (bool)_is.Read(isHomeDrop, 3, false);
    }

    public ChessBattleTempDropItem()
    {

    }

    public ChessBattleTempDropItem(ChessBattleLogicDropItem item)
    {
        iEntityID = item.iEntityID;
        itemType = (int)item.itemType;
        time = GameUtil.Fix64ToCharArray(item.currentTime);
        isHomeDrop = item.isHomeDrop;
    }
}

public class ChessBattleLogicDropItem : LogicGameObject
{
    public int iEntityID
    {
        get;
        private set;
    }

    public LogicDropType itemType
    {
        get;
        private set;
    }

    private FVector3 m_homePos;
    private FVector3 m_enemyPos;

    public Fix64 sqrPlayerDis
    {
        get;
        set;
    }

    public int PlayerId
    {
        get;
        private set;
    }

    public bool isHomeDrop
    {
        get;
        set;
    } = true;

    private static Fix64 collisionTime = 1.2.ToFix64();

    private Fix64 m_time = Fix64.zero;
    public Fix64 currentTime
    {
        get
        {
            return m_time;
        }
    }

    public bool isCollision
    {
        get;
        set;
    } = false;

    public ChessBattleLogicDropItem(int playerId, LogicDropType type, int entityID, FVector3 homePos, FVector3 enemyPos,bool homeDrop)
    {
        itemType = type;
        iEntityID = entityID;
        m_homePos = homePos;
        m_enemyPos = enemyPos;
        PlayerId = playerId;
        isHomeDrop = homeDrop;
        m_coliderR = Fix64.one;
        logicType = LogicType.ChessBattleLogicItem;
    }

    public void SetTime(Fix64 time)
    {
        m_time = time;
        if (m_time > collisionTime)
            isCollision = true;
    }

    public FVector3 GetPos(bool isHome)
    {
        if (isHome)
            return m_homePos;
        else
            return m_enemyPos;
    }

    public override void RunTick(Fix64 deltaTime)
    {
        base.RunTick(deltaTime);
        if (!isCollision)
        {
            if (m_time > collisionTime)
            {
                isCollision = true;
            }
            else
            {
                m_time += deltaTime;
            }
            
        }
    }
}

