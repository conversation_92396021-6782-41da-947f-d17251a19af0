using System;
using System.Collections;
using System.Collections.Generic;
using GameFramework.FMath;
using UnityEngine;

namespace MidPointCircle
{
    public class CirclePoint
    {
        public int x = 0;
        public int y = 0;

        public CirclePoint(int tx, int ty)
        {
            x = tx;
            y = ty;
        }
    }
    
    public class MidpointCircle
    {
        private List<CirclePoint> m_allPoints = new List<CirclePoint>();

        private static Fix64 m_first = 0.41.ToFix64();
        private static Fix64 m_second = 0.7.ToFix64();

        public List<CirclePoint> allPoints
        {
            get { return m_allPoints; }
        }

        private CirclePoint m_offset;

        private int m_r;
        private Fix64 m_rFix64;
    
        public MidpointCircle(int x, int y, int r)
        {
            m_offset = new CirclePoint(x, y);
            m_r = r;
            m_rFix64 = m_r.ToFix64();
        }

        public void Calculate()
        {
            int x = 0;
            int y = m_r;

            int one = (m_first * m_rFix64).Ceiling();
            int two = (m_second * m_rFix64).Ceiling();

            for (; x < one; x++)
            {
                AddPoint(x, y);
            }

            for (; x < two; x++)
            {
                y--;
                AddPoint(x, y);
                AddPoint(x-1, y);
            }
            
            AddPoint(x-1, y-1);
        }

        private void AddPoint(int x, int y)
        {
            m_allPoints.Add(new CirclePoint(x + m_offset.x, y + m_offset.y));
            m_allPoints.Add(new CirclePoint(x + m_offset.x, -y + m_offset.y));
            m_allPoints.Add(new CirclePoint(-x + m_offset.x, y + m_offset.y));
            m_allPoints.Add(new CirclePoint(-x + m_offset.x, -y + m_offset.y));
            m_allPoints.Add(new CirclePoint(y + m_offset.x, x + m_offset.y));
            m_allPoints.Add(new CirclePoint(y + m_offset.x, -x + m_offset.y));
            m_allPoints.Add(new CirclePoint(-y + m_offset.x, x + m_offset.y));
            m_allPoints.Add(new CirclePoint(-y + m_offset.x, -x + m_offset.y));
        }
    }

}


