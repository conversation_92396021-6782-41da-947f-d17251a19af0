#if ACGGAME_CLIENT
using ACG.Core;
using GameFramework;
using GameFramework.FMath;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using UnityEngine;
using ZGameChess;

[ExecuteInEditMode]
public class SDFView : MonoBehaviour
{
    public enum ShowText
    {
        坐标点,
        R值,
        触发器,
        高度,
        动态碰撞物
    }

    #region Gizmos
    /// <summary>
    /// The gizmos color.
    /// </summary>
    public Color color = Color.green;
    public bool showDrawGizoms = true;
    #endregion

    public TextAsset m_sdfConfigAsset;
    protected SDFPathConfig m_sdfConfig;

    [Header("起始点")]
    public Transform startPoint;
    [Header("目标点")]
    public Transform endPoint;
    [Header("玩家体积半径")]
    public float playerRadius = 0.5f;

    public ShowText m_showText = ShowText.坐标点;

    public bool 路径平滑 = true;

    GameObject[,] viewPoints;

    public static FixedDataList_Struct<FVector2> paths = new FixedDataList_Struct<FVector2>(32);

    public static Action<JPSNode, bool> OnFind;

    public List<JPSNode> m_tempList = new List<JPSNode>();

    private void Awake()
    {
        OnFind = (point, isEnd) =>
        {
            m_tempList.Add(point);
        };

        string name = gameObject.name.Replace("(Clone)", "");
        ResourceUtil.LoadAsset<TextAsset>("art_tft_raw/scenes/sdf_data/" + name, name, (asset, ab) =>
        {
            m_sdfConfigAsset = asset;
        });
    }

#if UNITY_EDITOR
    private void Update()
    {
        for (int i = 0; i < m_tempList.Count; ++i)
        {
            var point = m_tempList[i];

            int x = point.x;
            int y = point.y;

            if (viewPoints != null && viewPoints[x, y] != null)
            {
                var textMesh = viewPoints[x, y].GetComponent<TextMesh>();
                if (textMesh != null && textMesh.color == Color.white)
                {
                    textMesh.text = x + "," + y;
                    textMesh.color = Color.green;
                }
                else
                {
                    //Debug.LogError("Pos Error: (" + x + "," + y + ")");
                }
            }
        }
        m_tempList.Clear();
    }
#endif

    public void Init(SDFPathConfig cfg)
    {
        m_sdfConfig = cfg;
    }

    [ContextMenu("FindPath")]
    private void FindPath()
    {
        if (startPoint == null || endPoint == null)
            return;

        if (m_sdfConfig == null)
            return;

        for (int x = 0; x < m_sdfConfig.RuntimeMapSize; ++x)
        {
            for (int y = 0; y < m_sdfConfig.RuntimeMapSize; ++y)
            {
                if (viewPoints != null && viewPoints[x, y] != null)
                {
                    var textMesh = viewPoints[x, y].GetComponent<TextMesh>();
                    if (textMesh != null && textMesh.color == Color.green)
                    {
                        textMesh.color = Color.white;
                    }
                }
            }
        }

        OnFind = (point, isEnd) =>
        {
            int x = point.x;
            int y = point.y;

            if (viewPoints != null && viewPoints[x, y] != null)
            {
                var textMesh = viewPoints[x, y].GetComponent<TextMesh>();
                if (textMesh != null && textMesh.color == Color.white)
                {
                    textMesh.text = x + "," + y;
                    textMesh.color = Color.green;
                }
                else
                {
                    Debug.LogError("Pos Error: (" + x + "," + y + ")");
                }
            }
        };

        FVector2 startPos = new FVector2(startPoint.transform.localPosition.x, startPoint.transform.localPosition.z);
        FVector2 endPos = new FVector2(endPoint.transform.localPosition.x, endPoint.transform.localPosition.z);
        Fix64 pr = Fix64.FromSingle(playerRadius);
        paths.Clear();
        if (m_sdfConfig.JPS_FindPath(ref startPos, ref endPos, ref pr, ref paths, 路径平滑))
        {
            // 起始点和终点不管
            int halfMapSize = m_sdfConfig.RuntimeMapSize / 2;
            for (int i = 1; i < paths.Count - 1; ++i)
            {
                var pos = paths[i];
                int x = (pos.data.x / m_sdfConfig.RuntimeRealMapSize * m_sdfConfig.RuntimeMapSize).Floor() + halfMapSize;
                int y = (pos.data.y / m_sdfConfig.RuntimeRealMapSize * m_sdfConfig.RuntimeMapSize).Floor() + halfMapSize;
                if (viewPoints != null && viewPoints[x, y] != null)
                {
                    var textMesh = viewPoints[x, y].GetComponent<TextMesh>();
                    if (textMesh != null && textMesh.color == Color.white)
                    {
                        textMesh.color = Color.green;
                    }
                    else
                    {
                        Debug.LogError("Pos Error: (" + x + "," + y + ")");
                    }
                }
            }

            Debug.Log("查找路径成功！");
        }
        else
        {
            Debug.LogError("查找路径失败!");
        }
    }

    [ContextMenu("RefershSDFData")]
    private void RefershSDFData()
    {
        m_sdfConfig = null;
    }

    [ContextMenu("ClearSDFDebugText")]
    private void ClearSDFDebugText()
    {
        if (viewPoints != null)
        {
            for (int x = 0; x < m_sdfConfig.RuntimeMapSize; ++x)
            {
                for (int y = 0; y < m_sdfConfig.RuntimeMapSize; ++y)
                {
                    if (viewPoints[x, y] != null)
                        GameObject.Destroy(viewPoints[x, y]);
                }
            }
        }
    }

    [ContextMenu("LoadDynamicBox")]
    private void LoadDynamicBox()
    {
#if UNITY_EDITOR
        if (m_sdfConfig == null)
        {
            if (m_sdfConfigAsset != null)
                m_sdfConfig = new SDFPathConfig(m_sdfConfigAsset);
        }
        if (m_sdfConfig == null)
            return;

        
#endif
    }

    [ContextMenu("ShowSDFDebugText")]
    private void ShowSDFDebugText()
    {
        if (m_sdfConfig == null)
        {
            if (m_sdfConfigAsset != null)
                m_sdfConfig = new SDFPathConfig(m_sdfConfigAsset);
        }
        if (m_sdfConfig == null)
            return;

        if (viewPoints != null)
        {
            for (int x = 0; x < m_sdfConfig.RuntimeMapSize; ++x)
            {
                for (int y = 0; y < m_sdfConfig.RuntimeMapSize; ++y)
                {
                    if (viewPoints[x, y] != null)
                        GameObject.Destroy(viewPoints[x, y]);
                }
            }
        }
        else
        {
            viewPoints = new GameObject[m_sdfConfig.RuntimeMapSize, m_sdfConfig.RuntimeMapSize];
        }

        GameObject containerGo = new GameObject("DEBUG_SDF_Container");
        containerGo.transform.SetParent(transform);
        ChessUtil.MakeTransformIdentity(containerGo.transform);
        containerGo.transform.localScale = new Vector3(1 /  transform.localScale.x, 1 / transform.localScale.y, 1 / transform.localScale.z);
        int halfRuntimeMapSize = m_sdfConfig.RuntimeMapSize / 2;
        float itemSize = (m_sdfConfig.RuntimeRealMapSize / m_sdfConfig.RuntimeMapSize).ToSingle();
        for (int x = 0; x < m_sdfConfig.RuntimeMapSize; ++x)
        {
            for (int y = 0; y < m_sdfConfig.RuntimeMapSize; ++y)
            {
                int xPos = (x - halfRuntimeMapSize);
                int yPos = (y - halfRuntimeMapSize);

                GameObject go = new GameObject(x + "," + y);
                go.transform.SetParent(containerGo.transform);
                ChessUtil.MakeTransformIdentity(go.transform);
                go.transform.localRotation = Quaternion.Euler(90, 0, 0);
                go.transform.localPosition = new Vector3(xPos * itemSize, 0, yPos * itemSize);

                UpdateText(go, x, y);

                viewPoints[x, y] = go;
            }
        }
    }

    private void UpdateText(GameObject go, int x, int y)
    {
        var textMesh = go.TryGetComponent<TextMesh>();

        switch (m_showText)
        {
            case ShowText.坐标点:
                {
                    var pos = m_sdfConfig.GetR(x, y);
                    textMesh.text = string.Format("{0},{1}", x, y);
                    if (pos <= Fix64.zero)
                        textMesh.color = Color.red;
                    else
                        textMesh.color = Color.white;
                    textMesh.characterSize = 0.07f;
                }
                break;
            case ShowText.R值:
                {
                    var pos = m_sdfConfig.GetR(x, y);
                    textMesh.text = pos.ToSingle().ToString("F1");
                    if (pos <= Fix64.zero)
                        textMesh.color = Color.red;
                    else
                        textMesh.color = Color.white;
                    textMesh.characterSize = 0.1f;
                }
                break;
            case ShowText.触发器:
                {
                    var trigger = m_sdfConfig.m_triggerPoints[x, y];
                    textMesh.text = trigger.ToString();
                    if (trigger >= 0 && trigger < m_sdfConfig.m_triggers.Count)
                        textMesh.color = Color.red;
                    else
                        textMesh.color = Color.white;
                    textMesh.characterSize = 0.1f;
                }
                break;
            case ShowText.高度:
                {
                    var height = m_sdfConfig.m_heights[x, y];
                    textMesh.text = height.ToSingle().ToString("F2");
                    if (height >= Fix64.zero)
                        textMesh.color = Color.green;
                    else
                        textMesh.color = Color.red;
                    textMesh.characterSize = 0.1f;
                }
                break;
            case ShowText.动态碰撞物:
                {
                    /*
                    var dynamicObjects = m_sdfConfig.GetDynamicObjects();
                    if (dynamicObjects != null && dynamicObjects.Contains(JPSPath.HashKey(x, y)))
                    {
                        textMesh.color = Color.red;
                        textMesh.text = "-1";
                    }
                    else
                    {
                        textMesh.text = "";
                    }
                    textMesh.characterSize = 0.1f;
                    */
                }
                break;
            default:
                break;
        }
        textMesh.anchor = TextAnchor.MiddleCenter;
        textMesh.alignment = TextAlignment.Center;
    }

    [ContextMenu("ReFindDebugText")]
    private void ReFindDebugText()
    {
        if (m_sdfConfig == null)
        {
            if (m_sdfConfigAsset != null)
                m_sdfConfig = new SDFPathConfig(m_sdfConfigAsset);
        }
        if (m_sdfConfig == null)
            return;

        var root = transform.Find("DEBUG_SDF_Container");
        if (root != null)
        {
            viewPoints = new GameObject[m_sdfConfig.RuntimeMapSize, m_sdfConfig.RuntimeMapSize];
            foreach (Transform child in root)
            {
                var arr = child.name.Split(',');
                var x = int.Parse(arr[0]);
                var y = int.Parse(arr[1]);
                UpdateText(child.gameObject, x, y);
                viewPoints[x, y] = child.gameObject;
            }
        }
    }

    #region Gizmos

    void OnDrawGizmos()
    {
        if (!showDrawGizoms)
            return;
        if (m_sdfConfig == null)
        {
            if (m_sdfConfigAsset != null)
                m_sdfConfig = new SDFPathConfig(m_sdfConfigAsset);
        }

        if (m_sdfConfig == null)
            return;
        Gizmos.color = this.color;

        float halfMapSize = (m_sdfConfig.RuntimeRealMapSize / 2).ToSingle();
        float itemSize = (m_sdfConfig.RuntimeRealMapSize / m_sdfConfig.RuntimeMapSize).ToSingle();
        float halfItemSize = itemSize / 2;
        var position = transform.position;
        // 画线
        for (int x = 0; x < m_sdfConfig.RuntimeMapSize; ++x)
        {
            float offset = itemSize * x - halfItemSize;
            Gizmos.DrawLine(new Vector3(-halfMapSize, 0, -halfMapSize + offset) + position, new Vector3(halfMapSize, 0, -halfMapSize + offset) + position);
            Gizmos.DrawLine(new Vector3(-halfMapSize + offset, 0, -halfMapSize) + position, new Vector3(-halfMapSize + offset, 0, halfMapSize) + position);
        }

        // 画寻路的线条
        if (paths.Count != 0)
        {
            Gizmos.color = Color.green;
            for (int i = 0; i < paths.Count - 1; ++i)
            {
                var point = paths[i];
                var nextPoint = paths[i + 1];
                Gizmos.DrawLine(new Vector3(point.data.x.ToSingle(), 0, point.data.y.ToSingle()) + position,
                    new Vector3(nextPoint.data.x.ToSingle(), 0, nextPoint.data.y.ToSingle()) + position);
            }
        }

        var world = MicroMgr.Instance.GetMicroObj();
        if (world != null && world.CSoGame != null)
        {
            var allPlayer = world.CSoGame.chessBattleCore.allPlayerDatas;
            foreach (var player in allPlayer)
            {
                Gizmos.DrawSphere(player.Value.realer.fTransform.position.ToVector3() + position, 0.5f);
            }
        }
    }
    #endregion
}
#endif