using System.Collections.Generic;
using TKPlugins;

public static class CSoTriggerConditionUtil
{
    public static List<CSoTriggerVar> SystemTriggerVars = new List<CSoTriggerVar>()
    {
        new CSoTriggerVar() { name = CSoTriggerVar.FRAME_VAR, type = CSoTriggerVar.VarType.Int, isSystemVar = true },
        new CSoTriggerVar() { name = CSoTriggerVar.TIME_VAR, type = CSoTriggerVar.VarType.Int, isSystemVar = true },
        new CSoTriggerVar() { name = CSoTriggerVar.HERO_ATTACK_COUNT_VAR, type = CSoTriggerVar.VarType.Int, isSystemVar = true },
    };

    /// <summary>
    /// 快速创建一个达成某个羁绊的条件
    /// </summary>
    /// <param name="fetterId"></param>
    /// <returns></returns>
    public static CSoDoubleTriggerCondition CreateActiveFetterCondition(int fetterId)
    {
        return new CSoDoubleTriggerCondition(
                        new CSoTriggerConditionInfo() { type = E_SO_TRIGGER_TYPE.ActiveFetter, compare = E_SO_TRIGGER_COMPARE.Equal, value = fetterId },
                        new CSoTriggerConditionInfo() { type = E_SO_TRIGGER_TYPE.UnactiveFetter, compare = E_SO_TRIGGER_COMPARE.Equal, value = fetterId });
    }

    /// <summary>
    /// 创建见到某个起源的小小英雄的条件
    /// </summary>
    /// <param name="originId"></param>
    /// <returns></returns>
    //public static CSoTriggerCondition CreateMeetTinyOrigin(int originId)
    //{
    //    return new CSoTriggerCondition(E_SO_TRIGGER_TYPE.MEET_TINY_ORIGIN, E_SO_TRIGGER_COMPARE.Equal, originId);
    //}

    //public static CSoTriggerCondition CreateWinTinyOrigin(int originId)
    //{
    //    return new CSoTriggerCondition(E_SO_TRIGGER_TYPE.WIN_TINY_ORIGIN, E_SO_TRIGGER_COMPARE.Equal, originId);
    //}
}

/// <summary>
/// 变量
/// </summary>
public class CSoTriggerVar
{
    #region 内置变量
    public const string FRAME_VAR = "帧数";
    public const string TIME_VAR = "时间(秒)";
    public const string HERO_ATTACK_COUNT_VAR = "英雄攻击次数";
    #endregion
    public enum VarType
    {
        Bool,
        Int,
    }

    public string name;
    public VarType type = VarType.Int;
    public int value;
    public bool isSystemVar = false;

    public CSoTriggerVar()
    {

    }

    public CSoTriggerVar(CSoTriggerVar other)
    {
        name = other.name;
        type = other.type;
        value = other.value;
        isSystemVar = other.isSystemVar;
    }
}

public class CSoTriggerConditionInfo
{
    public E_SO_TRIGGER_TYPE type = E_SO_TRIGGER_TYPE.ChangeTurnState;
    public string varName;
    public E_SO_TRIGGER_COMPARE compare = E_SO_TRIGGER_COMPARE.Equal;
    public int value;
    public bool onlyBattleCheck;        // 局外预览的时候不检测这个，只有在局内真正作战的时候才检测这个条件

    //public CSoTriggerConditionInfo(E_SO_TRIGGER_TYPE type, E_SO_TRIGGER_COMPARE compare, int value)
    //{
    //    this.type = type;
    //    this.compare = compare;
    //    this.value = value;
    //}

    public bool Check(E_SO_TRIGGER_TYPE type, string varName, int param, out bool newActive)
    {
        if (this.type != type)
        {
            newActive = false;
            return false;
        }

        if (type == E_SO_TRIGGER_TYPE.VarChanged && this.varName != varName)
        {
            newActive = false;
            return false;
        }

        switch (compare)
        {
            case E_SO_TRIGGER_COMPARE.Greater:
                newActive = param > value;
                break;
            case E_SO_TRIGGER_COMPARE.GreaterOrEqual:
                newActive = param >= value;
                break;
            case E_SO_TRIGGER_COMPARE.Equal:
                newActive = param == value;
                break;
            case E_SO_TRIGGER_COMPARE.LessOrEqual:
                newActive = param <= value;
                break;
            case E_SO_TRIGGER_COMPARE.Less:
                newActive = param < value;
                break;
            case E_SO_TRIGGER_COMPARE.AnyValue:
                newActive = true;
                break;
            case E_SO_TRIGGER_COMPARE.Multiple:
                newActive = param % value == 0;
                break;
            default:
                newActive = false;
                break;
        }
        return true;
    }
}

public abstract class CSoTriggerConditionBase
{
    // 如果是-1 则表示一次满足永久达成（直到下次触发） 如果是0则表示只有这一帧满足要求 如果大于0则表示是达成有效时间(PS:只有在有多个条件下，这个参数才有用)
    public int validFrameCount;

    protected int activeFrameCount;
    private bool active = false;

    public int ActiveFrameCount { get { return activeFrameCount; } }

    public CSoTriggerConditionBase(int validFrameCount)
    {
        this.validFrameCount = validFrameCount;
    }

    public void Regain(int activeFrameCount)
    {
        this.activeFrameCount = activeFrameCount;
        this.active = true;
    }

    public abstract bool IsEnable();

    public abstract bool IsType(E_SO_TRIGGER_TYPE type);
    
    public abstract bool IsCheckType();

    public abstract bool Check(MicroObject world, E_SO_TRIGGER_TYPE type, string varName, int param);

    public bool IsActive(MicroObject world)
    {
        if (!active)
            return false;
        if (validFrameCount != -1)
        {
            var curFrameCount = BattleCommonNet.GetBattleRunFrame(world);
            if (curFrameCount - activeFrameCount > validFrameCount)
            {
                return false;
            }
        }
        return true;
    }

    public void SetActive(bool newActive)
    {
        active = newActive;
    }
}

public class CSoDoubleTriggerCondition : CSoTriggerConditionBase
{
    public CSoTriggerConditionInfo activeCondition;
    public CSoTriggerConditionInfo disactiveCondition;

    public CSoDoubleTriggerCondition(CSoTriggerConditionInfo active,
        CSoTriggerConditionInfo disactive, int validFrameCount = -1) : base(validFrameCount)
    {
        activeCondition = active;
        disactiveCondition = disactive;
    }

    public override bool IsEnable()
    {
        return true;
    }

    public override bool IsType(E_SO_TRIGGER_TYPE type)
    {
        return activeCondition.type == type || disactiveCondition.type == type;
    }
    
    public override bool IsCheckType()
    {
        return activeCondition.type == E_SO_TRIGGER_TYPE.CheckVar || disactiveCondition.type == E_SO_TRIGGER_TYPE.CheckVar;
    }

    public override bool Check(MicroObject world, E_SO_TRIGGER_TYPE type, string varName, int param)
    {
        bool oldActive = IsActive(world);
        if (!oldActive)
        {
            if (!activeCondition.Check(type, varName, param, out bool newActive))
                return oldActive;
            if (newActive)
            {
                SetActive(true);
                activeFrameCount = BattleCommonNet.GetBattleRunFrame(world);
            }
        }
        else
        {
            if (!disactiveCondition.Check(type, varName, param, out bool newActive))
                return oldActive;

            if (newActive)
            {
                SetActive(false);
            }
        }

        return IsActive(world);
    }
}

public class CSoTriggerCondition : CSoTriggerConditionBase
{
    public ChessBattleLogicPlayerTriggerAgent m_agent;
    public CSoTriggerConditionInfo m_info;

    public CSoTriggerCondition(CSoTriggerConditionInfo condition, ChessBattleLogicPlayerTriggerAgent agent, int validFrameCount = 0) : base(validFrameCount)
    {
        m_info = condition;
        m_agent = agent;
    }

    public override bool IsEnable()
    {
        if (!m_agent.InBattle && m_info.onlyBattleCheck)
            return false;
        return true;
    }
    
    public override bool IsCheckType()
    {
        return m_info.type == E_SO_TRIGGER_TYPE.CheckVar;
    }

    public override bool IsType(E_SO_TRIGGER_TYPE type)
    {
        if (m_info.type == E_SO_TRIGGER_TYPE.CheckVar)
            return true;
        return m_info.type == type;
    }

    public override bool Check(MicroObject world, E_SO_TRIGGER_TYPE type, string varName, int param)
    {
        bool oldActive = IsActive(world);

        // CheckVar因为外界不触发，所以直接实时取数据
        if (m_info.type == E_SO_TRIGGER_TYPE.CheckVar)
        {
            type = m_info.type;
            varName = m_info.varName;
            param = m_agent.GetVar(m_info.varName);
        }
   
        if (!m_info.Check(type, varName, param, out bool newActive))
            return oldActive;

        if (newActive != oldActive)
        {
            SetActive(newActive);
            if (newActive)
            {
                activeFrameCount = BattleCommonNet.GetBattleRunFrame(world);
            }
        }

        return newActive;
    }
}