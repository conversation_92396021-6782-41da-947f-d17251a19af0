using ACG.Core;
using GameFramework.FMath;
using GloryLockStep.Fight;
using System.Collections.Generic;
using LogicFrameWork;
using TKFrame;
using ZGame;

//public enum LogicType
//{
//    None,
//    ChessBattleLogicPlayer,
//    ChessBattleLogicItem,
//    LogicHitBullet,
//    RoundSelectBattleLogicPlayer,
//    RoundSelectBattleLogicHero,
//}  

public class LogicGameObject
{
    private int m_queueId = -1;
    public int queueId
    {
        get
        {
            return m_queueId;
        }
    }

    private FTransform m_fTransform;
    public FTransform fTransform
    {
        get
        {
            if (m_fTransform == null)
            {
                m_fTransform = new FTransform(null);
            }
            return m_fTransform;
        }
    }

    protected Fix64 m_coliderR;
    public Fix64 coliderR
    {
        get
        {
            return m_coliderR * ColiderFactor;
        }
    }
    
#if ACGGAME_CLIENT
    private LogicQueue<LogicMsg> m_logicQueue;
#endif

    public Fix64 ColiderFactor = Fix64.one;


    public LogicType logicType
    {
        get;
        protected set;
    } = LogicType.None;

    private bool m_isAdd = false;

#if UNITY_EDITOR && ACGGAME_CLIENT
    
    public void AddDebug()
    {
        m_isAdd = true;
    }

    public void RemoveDebug()
    {
        if (m_isAdd)
        {
            m_isAdd = false;
        }
    }
#endif

    public virtual void RunTick(Fix64 deltaTime)
    {
        
    }

    public virtual void Release()
    {
        if(m_fTransform != null)
        {
            m_fTransform.Reset();
            m_fTransform.rotation = new FQuaternion(Fix64.zero, Fix64.zero, Fix64.zero, Fix64.zero);
        }
            
#if ACGGAME_CLIENT
        CloseQueue();
        
#if UNITY_EDITOR
        RemoveDebug();
#endif

#endif
    }
    
    [System.Diagnostics.Conditional("USE_DOTRACE_MANUAL_API")]
    [System.Diagnostics.Conditional("DEBUG_LEVEL_LOG")]
    [System.Diagnostics.Conditional("DEBUG_LEVEL_WARN")]
    [System.Diagnostics.Conditional("DEBUG_LEVEL_ERROR")]
    public virtual void PrintLog(string msg)
    {

    }

    #region 断线重连

    public virtual void DoRegainData(RegainPlayerData regainPlayerData, RegainLogicPlayerData regainLogicPlayerData)
    {
    }

    public virtual void GetRegainData(RegainPlayerData regainPlayerData, RegainLogicPlayerData regainLogicPlayerData)
    {
    }

    #endregion

#if ACGGAME_CLIENT
    
    #region 传送到表现层的数据

    public bool isOpenQueue()
    {
        return m_logicQueue != null;
    }
    
    public void OpenQueue()
    {
        m_logicQueue = LogicQueueManager.Instance.NewQueue();
        m_queueId = m_logicQueue.id;
        Diagnostic.Log("open logic logic queue index " + m_logicQueue.index);
    }

    public void CloseQueue()
    {
        if (m_logicQueue != null)
        {
            Diagnostic.Log("close logic logic queue index " + m_logicQueue.index);
            LogicQueueManager.Instance.RecycleQueue(m_logicQueue, false);
            m_logicQueue = null;
            m_queueId = -1;
        }
    }

    public void SendData(LogicMsg data)
    {
        var world = MicroMgr.Instance.GetMicroObj();
        if (world != null && world.fightContext != null && world.fightContext.director != null)
            data.frameCount = world.fightContext.director.frameCount;
        m_logicQueue.WriteData(data);
    }
    #endregion
    
#endif
}