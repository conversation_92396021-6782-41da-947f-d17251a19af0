using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using Z_PVE;
using ZGame;
using ZGameChess;
using ZGameClient;

public class ChessLogicConfigCache
{
    public const string attack_config_cfg_dir = "Art_TFT_Raw/cfg/team_leader_attack_cfg/";
    private TKDictionary<string, ChessAttackLogicConfig> m_allAttackConfigDict = new TKDictionary<string, ChessAttackLogicConfig>();

    public const string player_config_cfg_dir = "Art_TFT_Raw/cfg/team_leader_cfg/";
    private TKDictionary<string, ChessPlayerLogicConfig> m_allPlayerConfig = new TKDictionary<string, ChessPlayerLogicConfig>();

    public const string trigger_config_cfg_dir = "Art_TFT_Raw/cfg/TriggerConfig/Logic/";
    private TKDictionary<string, ChessPlayerTriggerCfg> m_allTriggerConfig = new TKDictionary<string, ChessPlayerTriggerCfg>();

    private TKDictionary<string, MoveStateMachinePool> m_AllMoveStateMachine;

    protected MicroObject world;

    protected List<int> m_damageList;
    protected List<int> m_tinyList;
    protected bool m_isTrainBattleMode;

    protected HashSet<string> m_loadingCfgs = new HashSet<string>();

    public ChessLogicConfigCache(MicroObject world)
    {
        this.world = world;
    }

    public void PreloadInClient(List<int> tinyList, List<int> damageList, bool isTrainBattleMode = false)
    {
        m_tinyList = tinyList;
        m_damageList = damageList;
        m_isTrainBattleMode = isTrainBattleMode;
    }

    private IEnumerator LoadPlayerCfgIfHasSummon(bool inGame, HashSet<int> loadedTinyIds)
    {
        // 召唤功能预加载 
        foreach (var trigger in m_allTriggerConfig)
        {
            foreach (var item in trigger.Value.triggers)
            {
                foreach (var action in item.m_actions)
                {
                    if (action.action == E_SO_TRIGGER_ACTION.Summon || action.action == E_SO_TRIGGER_ACTION.Transform)
                    {
                        if (loadedTinyIds.Contains(action.intParam))
                            continue;
                        if (m_tinyList == null)
                            m_tinyList = new List<int>();
                        m_tinyList.Add(action.intParam);
                    }
                }
            }
        }

        if (m_tinyList != null)
        {
#if ACGGAME_CLIENT
            LoadTriggerCfgForClient(m_tinyList, inGame);
            LoadPlayerCfgForClient(m_tinyList, inGame);
#endif

            for (int i = 0; i < m_tinyList.Count; ++i)
            {
                // 加载召唤英雄的斩杀效果
                int tinyId = m_tinyList[i];
                var tinyCfg = DataBaseManager.Instance.SearchTinyHero(tinyId);
                if (tinyCfg != null && tinyCfg.iEliminateId != 0)
                {
                    if (m_damageList == null)
                    {
                        m_damageList = new List<int>();
                        m_damageList.Add(tinyCfg.iEliminateId);
                    }
                    else if (!m_damageList.Contains(tinyCfg.iEliminateId))
                        m_damageList.Add(tinyCfg.iEliminateId);
                }
            }
#if ACGGAME_CLIENT
            LoadAttackCfgForClient(m_damageList);
#endif
            m_damageList = null;
            m_tinyList = null;
        }

        while (m_loadingCfgs.Count != 0)
        {
            yield return null;
        }
    }

    public IEnumerator LoadAllCfgForClient(bool inGame)
    {
#if ACGGAME_CLIENT
        float maxTime = m_damageList != null ? m_damageList.Count * 0.2f : 0;
        LoadAttackCfgForClient(m_damageList);
        m_damageList = null;

        maxTime += m_tinyList != null ? m_tinyList.Count * 0.2f : 0;
        LoadTriggerCfgForClient(m_tinyList, inGame);
        LoadPlayerCfgForClient(m_tinyList, inGame);

        HashSet<int> loadedTinyIds = new HashSet<int>();
        if (m_tinyList != null)
        {
            foreach (var id in m_tinyList)
            {
                if (!loadedTinyIds.Contains(id))
                    loadedTinyIds.Add(id);
            }
            m_tinyList = null;
        }

        maxTime = UnityEngine.Mathf.Min(10f, maxTime);

        while (m_loadingCfgs.Count != 0 && maxTime > 0)
        {
            if (m_isTrainBattleMode)  // 只有战场编辑器有超时机制
                maxTime -= UnityEngine.Time.deltaTime;
            yield return null;
        }

        yield return LoadPlayerCfgIfHasSummon(inGame, loadedTinyIds);

        if (m_loadingCfgs.Count > 0)
        {
            StringBuilder sb = MicroObjectPool<StringBuilder>.Get();
            foreach (var cfg in m_loadingCfgs)
            {
                sb.Append(cfg).Append(" ");
            }
            TKFrame.Diagnostic.Error("[LoadAllCfgForClient]Time out! but cfg: " + sb.ToString() + " not load!");
            MicroObjectPool<StringBuilder>.Release(sb);
        }
#else
        yield break;
#endif
    }

    public void LoadAllCfgForServer()
    {
        LoadAttackCfgForServer();
        LoadTriggerCfgForServer();
        LoadPlayerCfgForServer();
    }

#if ACGGAME_CLIENT

    public void LoadTriggerCfgForClient(List<int> tinyIds, bool inGame)
    {
        //if (inGame)
        //{
        //    TKDictionary<int, TACG_TinyHero_Client> allTinyHero = DataBaseManager.Instance.GetTinyHeros();
        //    if (allTinyHero != null)
        //    {
        //        foreach (TACG_TinyHero_Client cfg in allTinyHero.Values)
        //        {
        //            var cfgName = cfg.sTriggerConfig;
        //            if (!string.IsNullOrEmpty(cfgName))
        //                LoadTriggerCfg_Client(cfgName);
        //        }
        //    }
        //}
        //else
        {
            for (int i = 0; i < tinyIds.Count; ++i)
            {
                var tinyId = tinyIds[i];
                if (tinyId == 0)
                    continue;

                var cfg = DataBaseManager.Instance.SearchTinyHero(tinyId);
                if (cfg != null)
                {
                    var cfgName = cfg.sTriggerConfig;
                    if (!string.IsNullOrEmpty(cfgName))
                        LoadTriggerCfg_Client(cfgName);
                    else
                        TKFrame.Diagnostic.Log("[ChessLogicConfigCache]小小英雄触发器配置名称为空: " + tinyId);
                }
                else
                {
                    TKFrame.Diagnostic.Error("[ChessLogicConfigCache]找不到触发器小小英雄配置: " + tinyId);
                }
            }
        }

        //m_waitLoadTinyId.AddRange(tinyIds);
        //while (m_waitLoadTinyId.Count > 0)
        //{
        //    tinyIds.Clear();
        //    tinyIds.AddRange(m_waitLoadTinyId);
        //    m_waitLoadTinyId.Clear();
        //    for (int i = 0; i < tinyIds.Count; ++i)
        //    {
        //        var tinyId = tinyIds[i];
        //        if (tinyId == 0)
        //            continue;

        //        var cfg = DataBaseManager.Instance.SearchTinyHero(tinyId);
        //        if (cfg != null)
        //        {
        //            var cfgName = cfg.sTriggerConfig;
        //            if (!string.IsNullOrEmpty(cfgName))
        //                LoadTriggerCfg_Client(cfgName);
        //            else
        //                TKFrame.Diagnostic.Log("[ChessLogicConfigCache]小小英雄触发器配置名称为空: " + tinyId);
        //        }
        //        else
        //        {
        //            TKFrame.Diagnostic.Error("[ChessLogicConfigCache]找不到触发器小小英雄配置: " + tinyId);
        //        }
        //    }
        //}

    }

    public void LoadAttackCfgForClient(List<int> attackIds)
    {
        if (attackIds == null)
            return;
        for (int i = 0; i < attackIds.Count; ++i)
        {
            var attackId = attackIds[i];
            var cfg = DataBaseManager.Instance.SearchACGAttackEffect(attackId);
            if (cfg != null)
            {
                var attackDict = TinyAttackData.GetAttackCfgDict(cfg.sAttackConfig);
                foreach (var attackItem in attackDict)
                {
                    LoadAttackCfg_Client(attackItem.Value);
                }
            }
            else
            {
                TKFrame.Diagnostic.Error("[ChessLogicConfigCache]找不到攻击配置: " + attackId);
            }
        }
    }

    public void LoadPlayerCfgForClient(List<int> tinyIds, bool inGame)
    {
        //if (inGame)
        //{
        //    TKDictionary<int, TACG_TinyHero_Client> allTinyHero = DataBaseManager.Instance.GetTinyHeros();
        //    if (allTinyHero != null)
        //    {
        //        foreach (TACG_TinyHero_Client tinyCfg in allTinyHero.Values)
        //        {
        //            var cfg = DataBaseManager.Instance.SearchACGItem(tinyCfg.iID);
        //            var cfgName = cfg.sTeamLeaderCfg;
        //            if (!string.IsNullOrEmpty(cfgName))
        //                LoadPlayerCfg_Client(tinyCfg.iID, cfgName);
        //        }
        //    }
        //}
        //else
        {
            if (tinyIds == null)
                return;
            for (int i = 0; i < tinyIds.Count; ++i)
            {
                var tinyId = tinyIds[i];
                if (tinyId == 0)
                {
                    TTAC_Global_Client global_def = DataBaseManager.Instance.SearchTACGlobalCfg((int)EGlobalType.EGlobalType_DEFAULT_TINY_HERO_ID);
                    if (global_def != null)
                    {
                        int.TryParse(global_def.sParam1, out tinyId);
                    }
                }

                var cfg = DataBaseManager.Instance.SearchACGItem(tinyId);
                if (cfg != null)
                {
                    var cfgName = cfg.sTeamLeaderCfg;
                    if (!string.IsNullOrEmpty(cfgName))
                        LoadPlayerCfg_Client(tinyId, cfgName);
                    else
                        TKFrame.Diagnostic.Error("[ChessLogicConfigCache]小小英雄配置名称为空: " + tinyId);
                }
                else
                {
                    TKFrame.Diagnostic.Error("[ChessLogicConfigCache]找不到小小英雄配置: " + tinyId);
                }
            }
        }
    }

#endif
    public void LoadAttackCfgForServer()
    {
        var allCfg = DataBaseManager.Instance.GetAttackEffectTable();
        if (allCfg != null)
        {
            foreach (var item in allCfg.Values)
            {
                if (string.IsNullOrEmpty(item.sAttackConfig))
                    continue;

                var attackDict = TinyAttackData.GetAttackCfgDict(item.sAttackConfig);
                foreach (var attackItem in attackDict)
                {
                    LoadAttackConfigCfg_Server(attackItem.Value);
                }
            }
        }
    }

    public void LoadTriggerCfgForServer()
    {
        var allTinyHero = DataBaseManager.Instance.GetTinyHeros();
        if (allTinyHero != null)
        {
            foreach (TACG_TinyHero_Client cfg in allTinyHero.Values)
            {
                var cfgName = cfg.sTriggerConfig;
                if (!string.IsNullOrEmpty(cfgName))
                    LoadTriggerCfg_Server(cfgName);
            }
        }
    }

    public void LoadPlayerCfgForServer()
    {
        var allTinyHero = DataBaseManager.Instance.GetTinyHeros();
        if (allTinyHero != null)
        {
            foreach (TACG_TinyHero_Client item in allTinyHero.Values)
            {
                int tinyId = item.iID;
                TACG_Item_Client itemCfg = DataBaseManager.Instance.SearchACGItem(tinyId);
                if (itemCfg != null && itemCfg.sTeamLeaderCfg != "")
                {
                    LoadPlayerCfg_Server(tinyId, itemCfg.sTeamLeaderCfg);
                }
                else
                {
                    TKFrame.Diagnostic.Error("[ChessLogicConfigCache]找不到小小英雄配置: " + tinyId);
                }
            }
        }
    }

    public void Release()
    {
        TKFrame.Diagnostic.Log("[ChessAttackLogicConfigManager]清理逻辑配置");
        m_allAttackConfigDict.Clear();
        m_allPlayerConfig.Clear();
    }

#if ACGGAME_CLIENT

    public void LoadTriggerCfg_Client(string cfgName)
    {
        if (!m_allTriggerConfig.ContainsKey(cfgName))
        {
            LoadCfg_Client(trigger_config_cfg_dir, cfgName, (text) =>
            {
                if (!m_allTriggerConfig.ContainsKey(cfgName) && text != null)
                {
                    var t = text.text;
                    if (!string.IsNullOrEmpty(t))
                    {
                        ChessPlayerTriggerCfg cfg = ChessPlayerTriggerCfg.Parse(t);
                        if (cfg != null)
                        {
                            m_allTriggerConfig.Add(cfgName, cfg);
                        }
                    }
                }
            }, "");
        }
        else
        {
            TKFrame.Diagnostic.Log(cfgName + " 逻辑配置文件已加载，此次忽略");
        }
    }

    public void LoadAttackCfg_Client(string cfgName)
    {
        if (!m_allAttackConfigDict.ContainsKey(cfgName))
        {
            LoadCfg_Client(attack_config_cfg_dir, cfgName, (text) =>
            {
                if (!m_allAttackConfigDict.ContainsKey(cfgName) && text != null && !string.IsNullOrEmpty(text.text))
                {
                    var cfg = JsonConvert.DeserializeObject<ChessAttackLogicConfig>(text.text);
                    if (cfg != null)
                    {
                        cfg.Init(cfgName);
                        m_allAttackConfigDict.Add(cfgName, cfg);
                    }
                }
            });
        }
        else
        {
            TKFrame.Diagnostic.Log(cfgName + " 逻辑配置文件已加载，此次忽略");
        }
    }

    public void LoadPlayerCfg_Client(int tinyId, string cfgName, bool force = false, Action<ChessPlayerLogicConfig> onFinished = null)
    {
        if (force)
        {
            m_allPlayerConfig.Remove(cfgName);
        }

        if (!m_allPlayerConfig.ContainsKey(cfgName))
        {
            LoadCfg_Client(player_config_cfg_dir.ToLower(), cfgName, (text) =>
            {
                if (!m_allPlayerConfig.ContainsKey(cfgName) && text != null && !string.IsNullOrEmpty(text.text))
                {
                    var cfg = JsonConvert.DeserializeObject<ChessPlayerLogicConfig>(text.text);
                    if (cfg != null)
                    {
                        cfg.Init();
                        m_allPlayerConfig.Add(cfgName, cfg);
                    }
                    onFinished?.Invoke(cfg);
                }
            });
        }
        else
        {
            TKFrame.Diagnostic.Log(cfgName + " 逻辑配置文件已加载，此次忽略");
        }
    }

#endif

    public void LoadTriggerCfg_Server(string cfgName)
    {
        if (!m_allTriggerConfig.ContainsKey(cfgName))
        {
            var cfg = LoadTriggerCfg_Server(trigger_config_cfg_dir, cfgName);
            if (cfg != null)
            {
                m_allTriggerConfig.Add(cfgName, cfg);
            }
        }
        else
        {
            TKFrame.Diagnostic.Log(cfgName + " 逻辑触发器配置文件已加载，此次忽略");
        }
    }

    public void LoadAttackConfigCfg_Server(string cfgName)
    {
        if (!m_allAttackConfigDict.ContainsKey(cfgName))
        {
            var cfg = LoadCfg_Server<ChessAttackLogicConfig>(attack_config_cfg_dir, cfgName);
            if (cfg != null)
            {
                cfg.Init(cfgName);
                m_allAttackConfigDict.Add(cfgName, cfg);
            }
        }
        else
        {
            TKFrame.Diagnostic.Log(cfgName + " 逻辑配置文件已加载，此次忽略");
        }
    }

    public void LoadPlayerCfg_Server(int tinyId, string cfgName)
    {
        if (!m_allPlayerConfig.ContainsKey(cfgName))
        {
            var cfg = LoadCfg_Server<ChessPlayerLogicConfig>(player_config_cfg_dir, cfgName);
            if (cfg != null)
            {
                cfg.Init();
                m_allPlayerConfig.Add(cfgName, cfg);
            }
        }
        else
        {
            TKFrame.Diagnostic.Log(cfgName + " 逻辑配置文件已加载，此次忽略");
        }
    }

#if ACGGAME_CLIENT
    protected void LoadCfg_Client(string assetbundle, string cfgName, Action<UnityEngine.TextAsset> onLoaded, string suffix = "_logic")
    {
        var logicCfgName = cfgName + suffix;
        string abName = (assetbundle + logicCfgName).ToLower();
        string assetName = logicCfgName.Contains("/") ? logicCfgName.Substring(logicCfgName.LastIndexOf("/") + 1) : logicCfgName;

        if (!TKFrame.AssetBundleManager.CheckAssetExist(abName, assetName))
        {
            TKFrame.Diagnostic.Warn("[LoadCfg_Client.Finished faild] " + logicCfgName + " not exist!");
            onLoaded?.Invoke(null);
            return;
        }

        var key = abName + assetName;
        m_loadingCfgs.Add(key);
        TKFrame.Diagnostic.Log("[LoadCfg_Client.Start] " + logicCfgName);

        TKFrame.IReleaseList container = null;
        if (ZGame.GameSystem.QQGameSystem.Instance != null)
            container = ZGame.GameSystem.QQGameSystem.Instance.GetStage() as TKFrame.IReleaseList;
        else
            container = TKFrame.SystemManager.getInstance();

        var loadedAsset = ResourceUtil.LoadAsset<UnityEngine.TextAsset>(abName, assetName, (asset, ab) =>
        {
            m_loadingCfgs.Remove(key);

            if (asset != null)
                TKFrame.Diagnostic.Log("[LoadCfg_Client.Finished success] " + key);
            else
                TKFrame.Diagnostic.Warn("[LoadCfg_Client.Finished faild] " + key);

            if (asset != null)
            {
                onLoaded?.Invoke(asset);
            }
            else
            {
                TKFrame.Diagnostic.Warn(logicCfgName + " 逻辑配置文件缺失");

                onLoaded?.Invoke(null);
            }
        }, container);
    }
#endif

    protected ChessPlayerTriggerCfg LoadTriggerCfg_Server(string dir, string cfgName)
    {
        var logicCfgName = cfgName + "_logic";

#if !ACGGAME_CLIENT
        string fileName = PathHandler.DealPath(dir + cfgName + "_logic.json");
#elif UNITY_EDITOR
        string fileName = UnityEngine.Application.dataPath + "/" + dir + cfgName + "_logic";
#else
        string fileName = "";
#endif

        if (File.Exists(fileName))
        {
            string text = File.ReadAllText(fileName);
            return ChessPlayerTriggerCfg.Parse(text);
        }
        else
        {
            TKFrame.Diagnostic.Log(logicCfgName + " 逻辑配置文件缺失");
            return null;
        }
    }

    protected T LoadCfg_Server<T>(string dir, string cfgName) where T : class
    {
        var logicCfgName = cfgName + "_logic";

#if !ACGGAME_CLIENT
        string fileName = PathHandler.DealPath(dir + cfgName + "_logic.json");
#elif UNITY_EDITOR
        string fileName = UnityEngine.Application.dataPath + "/" + dir + cfgName + "_logic";
#else
        string fileName = "";
#endif

        if (File.Exists(fileName))
        {
            string jsonData = File.ReadAllText(fileName);
            var cfg = JsonConvert.DeserializeObject<T>(jsonData);
            return cfg;
        }
        else
        {
            TKFrame.Diagnostic.Log(logicCfgName + " 逻辑配置文件缺失");
            return null;
        }
    }

    public ChessPlayerTriggerCfg GetTriggerCfg(string cfgName)
    {
        if (m_allTriggerConfig.TryGetValue(cfgName, out ChessPlayerTriggerCfg cfg))
        {
            return cfg;
        }
        TKFrame.Diagnostic.Error("GetTriggerInfo Faild! cfgName: " + cfgName);
        return null;
    }

    public ChessAttackLogicConfig GetAttackCfg(string cfgName)
    {
        if (m_allAttackConfigDict.TryGetValue(cfgName, out ChessAttackLogicConfig cfg))
        {
            return cfg;
        }
        TKFrame.Diagnostic.Error("GetAttackCfg Faild! cfgName: " + cfgName);
        return null;
    }

    private string GetPlayerCfgName(int tinyId)
    {
        if (tinyId == 0)
        {
            TTAC_Global_Client global_def = DataBaseManager.Instance.SearchTACGlobalCfg((int)EGlobalType.EGlobalType_DEFAULT_TINY_HERO_ID);
            if (global_def != null)
            {
                int.TryParse(global_def.sParam1, out tinyId);
            }
        }

        var itemCfg = DataBaseManager.Instance.SearchACGItem(tinyId);
        if (itemCfg != null)
        {
            var cfgName = itemCfg.sTeamLeaderCfg;
            if (!string.IsNullOrEmpty(cfgName))
            {
                if (m_allPlayerConfig.TryGetValue(cfgName, out ChessPlayerLogicConfig cfg))
                {
                    return cfgName;
                }

                TTAC_Global_Client global_def = DataBaseManager.Instance.SearchTACGlobalCfg((int)EGlobalType.EGlobalType_DEFAULT_TINY_HERO_ID);
                if (global_def != null)
                {
                    int.TryParse(global_def.sParam1, out tinyId);
                    itemCfg = DataBaseManager.Instance.SearchACGItem(tinyId);
                    if (itemCfg != null)
                    {
                        if (m_allPlayerConfig.TryGetValue(itemCfg.sTeamLeaderCfg, out cfg))
                        {
                            return itemCfg.sTeamLeaderCfg;
                        }
                    }
                    else
                    {
                        TKFrame.Diagnostic.Error("[ChessLogicConfigCache]找不到默认小小英雄配置: " + tinyId);
                    }
                }
            }
            else
            {
                TKFrame.Diagnostic.Error("[ChessLogicConfigCache]小小英雄配置名称为空: " + tinyId);
            }
        }
        else
        {
            TKFrame.Diagnostic.Error("[ChessLogicConfigCache]找不到小小英雄配置: " + tinyId);
        }
        return string.Empty;
    }

    public ChessPlayerLogicConfig GetPlayerCfg(int tinyId)
    {
        var cfgName = GetPlayerCfgName(tinyId);
        if (m_allPlayerConfig.TryGetValue(cfgName, out ChessPlayerLogicConfig cfg))
        {
            return cfg;
        }
        return null;
    }

    #region 小小英雄状态机

    public void LoadAllMachine(CSoGame soGame)
    {
        m_AllMoveStateMachine = new TKDictionary<string, MoveStateMachinePool>();
        foreach (var pair in m_allPlayerConfig)
        {
            if (pair.Value != null && !m_AllMoveStateMachine.ContainsKey(pair.Key))
            {
                m_AllMoveStateMachine.Add(pair.Key, new MoveStateMachinePool());
                m_AllMoveStateMachine[pair.Key].soGame = soGame;
                m_AllMoveStateMachine[pair.Key].playerConfig = pair.Value;
                PreAlloc(16, m_AllMoveStateMachine[pair.Key]);
            }
        }
    }

    private void PreAlloc(int cnt, MoveStateMachinePool pool)
    {
        List<MovementStateMachine> tempStateMachine = new List<MovementStateMachine>();
        while (cnt > 0)
        {
            cnt--;
            tempStateMachine.Add(pool.Alloc());
        }
        foreach (var player in tempStateMachine)
        {
            pool.Free(player);
        }
    }

    public MovementStateMachine GetMoveStateMachine(int tinyId, CSoGame soGame)
    {
        MovementStateMachine stateMachine = null;
        if (m_AllMoveStateMachine != null)
        {
            var cfgName = GetPlayerCfgName(tinyId);

            if (m_AllMoveStateMachine.ContainsKey(cfgName))
            {
                stateMachine = m_AllMoveStateMachine[cfgName].Alloc();
            }
            else
            {
                var playerConfig = GetPlayerCfg(tinyId);
                if (playerConfig != null)
                {
                    m_AllMoveStateMachine.Add(cfgName, new MoveStateMachinePool());
                    m_AllMoveStateMachine[cfgName].soGame = soGame;
                    m_AllMoveStateMachine[cfgName].playerConfig = playerConfig;
                    PreAlloc(16, m_AllMoveStateMachine[cfgName]);
                    stateMachine = m_AllMoveStateMachine[cfgName].Alloc();
                }
            }
        }
        return stateMachine;
    }

    public void RecycleMoveStateMachine(int tinyId, MovementStateMachine stateMachine)
    {
        if (m_AllMoveStateMachine != null)
        {
            var cfgName = GetPlayerCfgName(tinyId);

            MoveStateMachinePool pool;
            m_AllMoveStateMachine.TryGetValue(cfgName, out pool);
            if (pool != null)
            {
                pool.Free(stateMachine);
            }
        }
    }

    #endregion
}

