using ACG.Core;
using System.Collections;
using Lucifer.ActCore;
using TK<PERSON>rame;

public class LockStepCoroutineAdapter : ReleaseSingleton<LockStepCoroutineAdapter>, IWorldManager
{
    protected MicroObject _World;

    public MicroObject World
    {
        get
        {
            return _World;
        }
        set
        {
            _World = value;
        }
    }
    private TimeCoroutineManager uiCoroutine;
    private TimeCoroutineManager aiCoroutine;
    private TimeCoroutineManager logicCoroutine;

    public static void Create(MicroObject World)
    {
        if(World.lockStepCoroutineAdapter == null)
            World.lockStepCoroutineAdapter = new LockStepCoroutineAdapter(World);
        World.lockStepCoroutineAdapter.Init();
    }

    public LockStepCoroutineAdapter()
    {
        
    }

    public LockStepCoroutineAdapter(MicroObject World)
    {
        this.World = World;
        
        uiCoroutine = new TimeCoroutineManager();
        aiCoroutine = new TimeCoroutineManager();
        logicCoroutine = new TimeCoroutineManager();

        ms_instance = this;
    }

    public void Init()
    {
        World.DriverMgr.netRunner.DriveMethod(RunAI, RunPriority.AI);
        World.DriverMgr.netRunner.DriveMethod(RunLogic, RunPriority.TURN_TICK);
    }

    public void RunAI()
    {
        aiCoroutine.Run(ActionCoreConfig.DefaultFrameInterval);
    }

    public void RunLogic()
    {
        logicCoroutine.Run(ActionCoreConfig.DefaultFrameInterval);
    }

    public void Release()
    {
        Reset();
        
        ms_instance = null;
    }

    
    public void Reset()
    {
        World.DriverMgr.netRunner.RemoveDriveMethod(RunAI);
        World.DriverMgr.netRunner.RemoveDriveMethod(RunLogic);

        uiCoroutine.Release();
        aiCoroutine.Release();
        logicCoroutine.Release();
       
    }
    public void StartCoroutine(object obj, IEnumerator enumerator, int priority)
    {
        TimeCoroutineManager timeCoroutineManager = GetCoroutineManager(priority);

        if (timeCoroutineManager != null)
            timeCoroutineManager.StartCoroutine(obj, enumerator);
    }

    public void StopCoroutine(object obj, IEnumerator enumerator, int priority)
    {
        TimeCoroutineManager timeCoroutineManager = GetCoroutineManager(priority);

        if (timeCoroutineManager != null)
            timeCoroutineManager.StopCoroutine(obj, enumerator);
    }

    public void StopAllCoroutine(object obj, int priority)
    {
        TimeCoroutineManager timeCoroutineManager = GetCoroutineManager(priority);

        if (timeCoroutineManager != null)
            timeCoroutineManager.StopAllCoroutine(obj);
    }

    private TimeCoroutineManager GetCoroutineManager(int priority)
    {
        TimeCoroutineManager timeCoroutineManager = null;
        if (priority == RunPriority.AI)
            timeCoroutineManager = aiCoroutine;
        else if (priority == RunPriority.TURN_TICK)
            timeCoroutineManager = logicCoroutine;
        return timeCoroutineManager;
    }
}


