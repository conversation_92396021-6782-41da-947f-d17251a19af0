using System.Collections;

public static class TimeCoroutineExtend
{
    #region LogicGameObject
    public static void StartLockStepCoroutine(object obj, IEnumerator coroutine, int priority)
    {
        if (LockStepCoroutineAdapter.Instance != null)
            LockStepCoroutineAdapter.Instance.StartCoroutine(obj, coroutine, priority);
    }

    public static void StartLockStepCoroutine(this LogicGameObject thisRef, IEnumerator coroutine, int priority)
    {
        if (LockStepCoroutineAdapter.Instance != null)
            LockStepCoroutineAdapter.Instance.StartCoroutine(thisRef, coroutine, priority);
    }

    public static void StopLockStepCoroutine(this LogicGameObject thisRef, IEnumerator coroutine, int priority)
    {
        if (LockStepCoroutineAdapter.Instance != null)
            LockStepCoroutineAdapter.Instance.StopCoroutine(thisRef, coroutine, priority);
    }

    public static void StopAllLockStepCoroutine(this LogicGameObject thisRef, int priority)
    {
        if (LockStep<PERSON><PERSON>utineAdapter.Instance != null)
            LockStepCoroutineAdapter.Instance.StopAllCoroutine(thisRef, priority);
    }
    #endregion
}


