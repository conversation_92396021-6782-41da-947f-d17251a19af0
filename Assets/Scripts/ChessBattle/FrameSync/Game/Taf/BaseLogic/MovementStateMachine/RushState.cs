using GameFramework.Common.FSM;
using GameFramework.FMath;
using GloryLockStep.Fight;
using System.Collections.Generic;

public class RushState : CurveMoveState
{
	private static readonly string m_stateName = PlayerMoveMessage.Rush.ToString();
    public static string StateName
    {
        get
        {
            return m_stateName;
        }
    }
	
    protected override PlayerMoveMessage Type => PlayerMoveMessage.Rush;
    
    protected override List<Fix64> GetCurve()
    {
        return m_config != null ? m_config.rushFix64 : new List<Fix64>();
    }
    
    protected override Fix64 GetStopDis()
    {
        return m_config.rushDisFix64;
    }
}


