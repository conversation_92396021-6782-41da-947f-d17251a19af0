using ACG.Core;
using GameFramework.FMath;
using LogicFrameWork;
using System;
using System.Collections.Generic;
using TKFrame;
using Z_PVE;
using ZGame;
using ZGameChess;

public class BaseLogicMovement : LogicGameObject
{
    private static readonly Fix64 _fix64Zero = Fix64.zero;
    protected MovementStateMachine m_movementState;
    public MovementStateMachine movementState
    {
        get
        {
            return m_movementState;
        }
    }

    protected ChessPlayerLogicConfig _playerConfig = null;
    public ChessPlayerLogicConfig playerConfig
    {
        get { return _playerConfig; }
        protected set
        {
            _playerConfig = value;
        }
    }

    private LinkedHashMap<int, ChessAttackLogicConfig> m_attackConfigDict = new LinkedHashMap<int, ChessAttackLogicConfig>();

    public CPlayer playerData
    {
        get;
        protected set;
    }

    public int TinyId
    {
        get;
        protected set;
    }

    // 速度
    private Fix64 _speed;
    public Fix64 m_speed
    {
        get
        {
            return _speed * SpeedFactor;
        }
        set { _speed = value; }
    }

    // 锁定点击动作
    public bool LockCustomAction = false;

    public Fix64 SpeedFactor = Fix64.one;

    // 寻路的每个点
    private FVector3 m_dst;
    public FVector3 dst
    {
        get
        {
            return m_dst;
        }
        protected set
        {
            m_dst = value;
            if (value.x != fTransform.position.x || value.z != fTransform.position.z)
            {
                isNewTarget = true;
            }

            //PrintLog("[Xiaobai!!][Logic]who set dst: " + m_dst);
        }
    }

    protected SDFMovementCtrl m_sdfMovementCtrl;
    protected MovementCtrl m_movementCtrl;

    public event Action<SDFMovementCtrl.EventType> onMoveEvent;
    public event Action<PlayerMoveMessage> onMoveStateChanged;

    public Fix64 Radius = Fix64.one;

    // 目标方向
    protected FVector3 m_dir;
    public FVector3 dir
    {
        get
        {
            return m_dir;
        }
    }

    // 如果不为0 那么回到idle的时候 重新设置一下方向为m_afterDir
    public FVector3 m_afterDir = FVector3.zero;

    // 运动状态
    public PlayerMoveMessage moveState
    {
        get;
        protected set;
    } = PlayerMoveMessage.Stop;

    // 是否移动
    public bool isMoving
    {
        get
        {
            if (moveState == PlayerMoveMessage.Stop
                || moveState == PlayerMoveMessage.JumpTo
                || moveState == PlayerMoveMessage.JumpBack)
                return false;
            else
                return true;
        }
    }

    public bool isNewTarget
    {
        get;
        set;
    }

    protected bool m_isDirControlWay = false;

    private MicroObject m_world;

    public MicroObject World
    {
        get
        {
            return m_world;
        }
    }

    #region 初始化

    protected BaseLogicMovement(MicroObject world)
    {
        m_world = world;
        m_sdfMovementCtrl = new SDFMovementCtrl(this, OnMoveEvent);
        SetControlWay(false);
    }

    private void OnMoveEvent(SDFMovementCtrl.EventType e)
    {
        if (onMoveEvent != null)
            onMoveEvent(e);
    }

    protected void Init()
    {
        if (m_world != null)
        {
            LoadConfig();
            LoadAttackConfig(playerData.UserInfo.stUsedCaptainInfo.iDamageId, m_world.m_logicCfgCache);
        }
        moveState = PlayerMoveMessage.Stop;
    }

    public void SwitchModel(int tinyId)
    {
        // 重置小小英雄的攻击特效
        var defaultDamageId = TinyAttackData.GetRealDamageId(0, TinyId);
        if (defaultDamageId == playerData.UserInfo.stUsedCaptainInfo.iDamageId)
            playerData.UserInfo.stUsedCaptainInfo.iDamageId = TinyAttackData.GetRealDamageId(0, tinyId);

        TinyId = tinyId;

        Init();

        if (moveState != PlayerMoveMessage.Stop)
            StopMove();
    }

    #endregion

    #region 切换

    public void SetControlWay(bool isDir)
    {
        PrintLog("Control Way " + isDir);
        if (m_isDirControlWay != isDir || m_movementCtrl == null)
        {
            if (!m_isDirControlWay && isMoving && m_movementCtrl != null)
            {
                StopMove(true);
            }

            if (m_movementCtrl != null)
                m_movementCtrl.ResetMoveState();

            m_isDirControlWay = isDir;
            //if (m_isDirControlWay)
            //{
            //    m_movementCtrl = new DirMovementCtrl(this, OnMoveEvent);
            //    m_movementCtrl.SwitchMap(m_sdfMovementCtrl.SDFPath);
            //    m_movementCtrl.SetConfig(m_movementState, _playerConfig);
            //}
            //else
                m_movementCtrl = m_sdfMovementCtrl;
        }
    }

    public void SetMap(SDFPathConfig sdfPath)
    {
        m_movementCtrl.SwitchMap(sdfPath);
        if (m_isDirControlWay)
            m_sdfMovementCtrl.SwitchMap(sdfPath);
    }

    private void SetConfig(MovementStateMachine movementStateMachine, ChessPlayerLogicConfig playerConfig)
    {
        m_movementCtrl.SetConfig(movementStateMachine, playerConfig);
        if (m_isDirControlWay)
            m_sdfMovementCtrl.SetConfig(movementStateMachine, playerConfig);
    }

    #endregion

    #region 配置

    #region 攻击配置

#if UNITY_EDITOR
    public void SetAttackConfig(ChessAttackLogicConfig logicCfg)
    {
        m_attackConfigDict.Clear();
        m_attackConfigDict.Add(0, logicCfg);
    }
#endif

    public void LoadAttackConfig(int damageId, ChessLogicConfigCache logicCfgCache)
    {
        if (damageId == 0)
            damageId = TinyAttackData.GetRealDamageId(damageId, TinyId);
        var attackCfg = DataBaseManager.Instance.SearchACGAttackEffect(damageId);
        if (attackCfg != null)
        {
            m_attackConfigDict.Clear();
            TKDictionary<int, string> attackDict = TinyAttackData.GetAttackCfgDict(attackCfg.sAttackConfig);
            foreach (var item in attackDict)
            {
                var logicCfg = logicCfgCache.GetAttackCfg(item.Value);
                m_attackConfigDict.Add(item.Key, logicCfg);
            }
        }

        UpdateTinyKillAttackConfig(logicCfgCache);
    }

    // 斩杀特效优先用英雄自己的
    private void UpdateTinyKillAttackConfig(ChessLogicConfigCache logicCfgCache)
    {
        var tinyCfg = DataBaseManager.Instance.SearchTinyHero(TinyId);
        int playerEquipEliminateId = playerData.UserInfo.stUsedCaptainInfo.iEliminateId;
        if (playerEquipEliminateId == 0)
        {
            if (tinyCfg != null && tinyCfg.iEliminateId != 0)
            {
                playerEquipEliminateId = tinyCfg.iEliminateId;
            }
        }

        //如果有服务器下发的数据，优先用服务器下发
        if (playerEquipEliminateId != 0)
        {
            var attackCfg = DataBaseManager.Instance.SearchACGAttackEffect(playerEquipEliminateId);
            if (attackCfg != null)
            {
                // 因为是斩杀特效 所以这里不用配成0:xxx;1:xxx这种形式 直接把配置文件配上去 这里直接读就好了
                var logicCfg = logicCfgCache.GetAttackCfg(attackCfg.sAttackConfig);
                if (logicCfg != null)
                {
                    if (m_attackConfigDict.ContainsKey((int)TinyAttackTriggerType.Kill))
                        m_attackConfigDict[(int)TinyAttackTriggerType.Kill] = logicCfg;
                    else
                        m_attackConfigDict.Add((int)TinyAttackTriggerType.Kill, logicCfg);
                }
            }
        }
    }

    public ChessAttackLogicConfig GetAttackConfig(TinyAttackTriggerType type)
    {
        if (m_attackConfigDict.TryGetValue((int)type, out ChessAttackLogicConfig logicCfg))
        {
            return logicCfg;
        }
        else if (m_attackConfigDict.TryGetValue((int)TinyAttackTriggerType.Normal, out logicCfg))
        {
            return logicCfg;
        }
        return null;
    }

    #endregion

    #region 移动配置

    //public void LoadConfig(ChessLogicConfigCache logicCfgCache)
    //{
    //    if (logicCfgCache == null)
    //        return;

    //    _playerConfig = logicCfgCache.GetPlayerCfg(TinyId);
    //    if (_playerConfig != null)
    //    {
    //        InitConfig();

    //        OnLoadConfig();
    //    }
    //}

    public void LoadConfig()
    {
        if (m_world == null)
            return;

        ChessLogicConfigCache logicCfgCache = m_world.m_logicCfgCache;

        _playerConfig = logicCfgCache.GetPlayerCfg(TinyId);
        InitConfig();

        if (_playerConfig != null)
            OnLoadConfig();
    }

    protected virtual void OnLoadConfig()
    {

    }

    protected void LoadNewConfig(string configName)
    {
        if (string.IsNullOrEmpty(configName))
        {
            var tinyCfg = DataBaseManager.Instance.SearchACGItem(TinyId);
            if (tinyCfg != null)
                configName = tinyCfg.sTeamLeaderCfg;
        }
        //        else
        //        {

        ////#if UNITY_EDITOR && !LOGIC_THREAD
        ////            _playerConfig = ChessPlayerLogicConfigManager.Instance.GetNewPlayerConfig(TinyId);
        ////#else
        ////            _playerConfig = ChessPlayerLogicConfigManager.Instance.GetPlayerConfig(TinyId);
        ////#endif
        //        }

#if ACGGAME_CLIENT
        m_world.m_logicCfgCache.LoadPlayerCfg_Client(TinyId, configName, true, (cfg) =>
        {
            _playerConfig = cfg;

            if (_playerConfig != null)
            {
                InitConfig();
            }
        });
#endif

    }

    private void InitConfig()
    {
        if (m_world == null)
            return;

        m_movementState = m_world.m_logicCfgCache.GetMoveStateMachine(TinyId, m_world.CSoGame);

        if (m_movementState == null)
            m_movementState = new MovementStateMachine(this, m_world.CSoGame);
        else
            m_movementState.InitMovementData(this);

        moveState = PlayerMoveMessage.Stop;

        SetConfig(m_movementState, _playerConfig);
    }

    #endregion

    #endregion

    #region 设置移动

    protected virtual void DecideNextMoveWay()
    {

    }

    public void MoveTo(Fix64 detla)
    {
        m_movementCtrl.OnMove(ref detla);
    }

    protected void StartWalkWay()
    {
        PlayerMoveMessage result = m_movementCtrl.DecideWalkWay();
        SetState(result);
    }

    protected void StartRushWay()
    {
        m_movementState.DoTransitionManually(m_movementState.GetStateName(PlayerMoveMessage.Rush));
    }

    public void SetIdle()
    {
        m_movementState.DoTransitionManually(m_movementState.GetStateName(PlayerMoveMessage.Stop));
    }

    protected void StartRunWay(bool ignoreStartRun = false)
    {
        PlayerMoveMessage result = m_movementCtrl.DecideRunWay(ignoreStartRun);
        SetState(result);
    }

    public void StopMove(bool immediatelyExcute = false)
    {
        m_movementCtrl.StopMove();
        m_dst = fTransform.position;
        isNewTarget = true;
        SetState(PlayerMoveMessage.Stop);

        if (immediatelyExcute)
            m_movementState.UpdateStates();
    }

    #endregion

    #region 动作

    public void PlayAction(string action)
    {
        if (moveState != PlayerMoveMessage.Stop)
            StopMove();

#if ACGGAME_CLIENT
        if (isOpenQueue())
        {
            SimpleCSoPlayAnimationMsg actionMsg = LogicMsgPool.Instance.GetMsg<SimpleCSoPlayAnimationMsg>();
            actionMsg.msgType = (int)PlayerMoveMessage.PlayAnimation;
            actionMsg.animationName = action;
            SendData(actionMsg);
        }
#endif
    }

    public int m_actionId = -1;

    public virtual void PlayAction(int actionId)
    {
        if (moveState == PlayerMoveMessage.JumpTo
            || moveState == PlayerMoveMessage.JumpBack
            || LockCustomAction)
        { // 表示这个时候不能播动作
            actionId = -1;
        }

        if (actionId != -1 && isMoving)
        {
            StopMove(true);
        }
#if ACGGAME_CLIENT
        if (isOpenQueue())
        {
            PlayActionMsg actionMsgMsg = LogicMsgPool.Instance.GetMsg<PlayActionMsg>();
            actionMsgMsg.actionId = actionId;
            SendData(actionMsgMsg);
        }
#endif
    }

    #endregion

    #region 断线重连

    public override void DoRegainData(RegainPlayerData regainPlayerData, RegainLogicPlayerData regainLogicPlayerData)
    {
        base.DoRegainData(regainPlayerData, regainLogicPlayerData);

        //SendMovingState();
    }

    public override void GetRegainData(RegainPlayerData regainPlayerData, RegainLogicPlayerData regainLogicPlayerData)
    {
        base.GetRegainData(regainPlayerData, regainLogicPlayerData);

    }

    #endregion

    #region 设置目标

    public virtual bool SetTarget(FVector2 end)
    {
        //PrintLog("[Xiaobai!!][Logic]who set target: " + end + " start " + start);
        return m_sdfMovementCtrl.SetTarget(end);
    }

    public void SetTargetImpl(ref FVector2 target, Fix64 stepDistance)
    {
        m_dst.x = target.x;
        m_dst.z = target.y;
        isNewTarget = true;

        FVector3 start = fTransform.position;
        FVector3 delta = m_dst - start;
        delta.y = Fix64.zero;
        m_dir = delta.normalized;
        //PrintLog("[Xiaobai!!][Logic]m_dir: " + m_dir);
        fTransform.rotation = FQuaternion.LookRotation(m_dir);

        SendTarget(stepDistance);
        //m_movementCtrl.SendTarget();
    }

    public void SendTarget(Fix64 stepDistance)
    {
        if (isNewTarget)
        {
#if ACGGAME_CLIENT
            if (isOpenQueue())
            {
                FVector3Msg fVector3Msg = LogicMsgPool.Instance.GetMsg<FVector3Msg>();
                fVector3Msg.msgType = (int)PlayerMoveMessage.SetTarget;
                fVector3Msg.isDir = m_isDirControlWay;
                fVector3Msg.value = m_isDirControlWay ? dir : dst;
                fVector3Msg.isNewValue = true;
                fVector3Msg.distance = stepDistance;

                SendData(fVector3Msg);
            }
#endif
            isNewTarget = false;
        }
    }

    #endregion

    #region 设置方向

    protected void SetDirection(Fix64 degree, FQuaternion initRot)
    {
        if (degree > Fix64.pi * 2)
        {
            m_movementState.DoTransitionManually(m_movementState.GetStateName(PlayerMoveMessage.Stop));
            m_movementCtrl.ResetMoveState();
        }
        else
        {
            isNewTarget = true;
            FQuaternion quaternion = FQuaternion.AngleAxis(degree, FVector3.up);
            fTransform.rotation = initRot * quaternion;
            m_dir = fTransform.rotation * FVector3.forward;
            SendTarget(m_movementCtrl.StepDistance);
            m_movementState.SetInt(MovementStateMachine.MoveNum, (int)EndMoveWay.Idle);
            StartWalkWay();
        }
    }

    #endregion

    public SDFPathConfig GetSDFPath()
    {
        if (m_movementCtrl != null)
            return m_movementCtrl.SDFPath;

        return null;
    }

    protected virtual bool InSideField(ref FVector2 pos)
    {
        return false;
    }

    public void SetSpeedFactor(Fix64 factor)
    {
        SpeedFactor = factor;
    }

    public virtual void TriggerCollision(bool teleport = false)
    {

    }

    public override void RunTick(Fix64 deltaTime)
    {
        base.RunTick(deltaTime);
        if (_playerConfig != null)
        {
            m_movementState.deltaTime = deltaTime;

            m_movementState.UpdateStates();

            //PrintLog("[Xiaobai!!][Logic]position: " + fTransform.position + " rot:" + fTransform.rotation);
        }
    }

    // 状态机直接调用的，发送动作和目标使用的
    public void SendMoveData(PlayerMoveMessage move)
    {
        moveState = move;
        m_movementState.SetInt(MovementStateMachine.MoveState, (int)move);

        if (move == PlayerMoveMessage.Idle || move == PlayerMoveMessage.Stop)
        {
            m_movementCtrl.ResetMoveState();
        }

        if (onMoveStateChanged != null)
        {
            onMoveStateChanged(move);
        }

        m_movementCtrl.SendAction(move);

        isNewTarget = false;
    }

    public void ResetPlayer()
    {
#if ACGGAME_CLIENT
        if (isOpenQueue())
        {
            SimpleMsg simpleMsg = LogicMsgPool.Instance.GetMsg<SimpleMsg>();
            simpleMsg.msgType = (int)PlayerMoveMessage.ResetPlayer;
            SendData(simpleMsg);
        }
#endif
    }

    public void SetRotation(FVector3 Dir, bool withAnimattion = false)
    {
        m_dir = Dir;
        fTransform.rotation = FQuaternion.LookRotation(Dir);

#if ACGGAME_CLIENT
        if (isOpenQueue())
        {
            FQuaternionMsg quaternionMsg = LogicMsgPool.Instance.GetMsg<FQuaternionMsg>();
            quaternionMsg.msgType = (int)PlayerMoveMessage.Rotate;
            quaternionMsg.value = fTransform.rotation;
            quaternionMsg.withAnimation = withAnimattion;
            SendData(quaternionMsg);
        }
#endif
    }
#if ACGGAME_CLIENT
    public void TriggerAction(E_TRIGGER_MOMENT moment)
    {

        if (isOpenQueue())
        {
            TriggerActionMsg triggerActionMsg = LogicMsgPool.Instance.GetMsg<TriggerActionMsg>();
            triggerActionMsg.msgType = (int)PlayerMoveMessage.TriggerAction;
            triggerActionMsg.value = moment;
            SendData(triggerActionMsg);
        }

    }
#endif    
    // 设置状态或者更换目标使用，一般是状态机外的直接设置
    protected void SetState(PlayerMoveMessage state)
    {
        if (m_movementState == null)
            return;

        m_movementState.logicMovement.PrintLog("set state " + state);
        if (moveState != state)
        {
            m_movementState.SetInt(MovementStateMachine.MoveState, (int)state);
        }
        else
        {
            SendTarget(m_movementCtrl.StepDistance);
        }
    }

    #region 同步

    public void CorrectPos(bool isDir)
    {
#if ACGGAME_CLIENT
        if (isOpenQueue())
        {
            FVector3Msg fVector3Msg = LogicMsgPool.Instance.GetMsg<FVector3Msg>();
            fVector3Msg.msgType = (int)PlayerMoveMessage.CorrectPos;
            fVector3Msg.isNewValue = true;
            fVector3Msg.value = fTransform.position;
            fVector3Msg.isDir = isDir;

            SendData(fVector3Msg);
        }
#endif
    }

#if ACGGAME_CLIENT

    public virtual int SyncMovingState()
    {
        if (!isOpenQueue())
            OpenQueue();
        Diagnostic.Log("ChessBattleLogicPlayer " + playerData.PlayerId + " Attach Queue " + queueId);

        SendMovingState();

        return queueId;
    }

    public void SendMovingState()
    {
        if (isOpenQueue())
        {
            InitPlayerMsg initPlayerMsg = LogicMsgPool.Instance.GetMsg<InitPlayerMsg>();

            initPlayerMsg.transform.pos = fTransform.position;
            initPlayerMsg.transform.rot = fTransform.rotation;

            initPlayerMsg.movingState.moveState = moveState;

            if (m_isDirControlWay)
                initPlayerMsg.movingState.value = dir;
            else
                initPlayerMsg.movingState.value = dst;

            initPlayerMsg.movingState.isDir = m_isDirControlWay;

            OnSendMovingState(initPlayerMsg);

            movementState.currentState.listener.SyncState(initPlayerMsg.movingState);

            SendData(initPlayerMsg);
        }
    }

    protected virtual void OnSendMovingState(InitPlayerMsg msg)
    {

    }

#endif

    #endregion

    public void ResetTransform(FTransform transform)
    {
        fTransform.position = transform.position;
        fTransform.rotation = transform.rotation;
        dst = transform.position;
        m_dir = (transform.rotation * FVector3.forward).normalized;
    }

    public override void Release()
    {
        base.Release();
        moveState = PlayerMoveMessage.Stop;
        m_dir = FVector3.zero;
        m_dst = FVector3.zero;
        SpeedFactor = Fix64.one;
        m_movementCtrl.Release();
        _speed = _fix64Zero;
        if (playerData != null)
            m_world.m_logicCfgCache.RecycleMoveStateMachine(playerData.UserInfo.stUsedCaptainInfo.iCaptainId, m_movementState);
    }
}
