using System;
using System.Collections.Generic;
using ACG.Core;
using TKFrame;
using ZGame;
using ZGameClient;
using GameFramework.FRand;
using GameFramework.FMath;
using ZGameChess;
using UniqueInfo;

namespace Z_PVE
{
    public enum QuestType
    {
        [EnumName("PVP")]
        QUESTTYPE_PVP = 0, //PVP
        [EnumName("野怪")]
        QUESTTYPE_PVE, //野怪
        [EnumName("轮抽")]
        QUESTTYPE_ROUND_SELECT,
        [EnumName("4V4决战回合")]
        QUESTTYPE_ROUND_DECISIVE_BATTLE,
    }

    public enum SharedDraftCfg
    {
        FIRST_RELEASE_TIME = 1, //开始释放时间（毫秒）
        RELEASE_INTERNAL = 2, //释放间隔
        END_TIME = 3, //释放完结束时间
        FIRST_ROUND_END_TIME = 4, //首轮轮抽释放完结束时间（特殊处理）
        ROOKIE_FIRST_RELEASE_TIME = 5, // 小白新手开始释放时间（毫秒）
        ROOKIE_RELEASE_INTERNAL = 6, // 小白新手释放间隔

        BR_SHARED_HERO_NUMBER_RATE = 10, //百人玩法英雄个数及概率
    }

    public enum HeroDraftRule
    {
        NONE = 0,
        REMOVE = 1,
        ONLY = 2,
    }

    // 逻辑帧捡装备使用
    public enum LogicDropType
    {
        None,
        DorpBox,
        Monster,
        Coin,
        ShoesOrBones,//臭靴子或鱼骨头
        Doot, //S7 Bard技能掉落的音符
    }

    //金币掉落类型
    public enum CoinDropType
    {
        None,       //
        Monster,    //野怪
        DropBox,    //宝箱
        SpacePirate, //太空海盗
    }

    public enum HeroEquipmentUpdateType
    {
        Init,               //初始化
        Promote,            //升星
        HeroChange,         //变身
        Add,                //装备添加
        Remove,             //装备移除
        Exclusive,         //排斥卸装
        BackEquipmentOP,    //回退装备操作
        UpdateProperty,     //更新属性;
    }

    public enum HeroCombatEffectUpdateType
    {
        Init,
        Add,
        Remove,
    }

    public enum HextechHeroIDChangeType
    {
        Add,                //增加
        Remove,             //移除
        Clear,              //清除
    }

    /// <summary>
    /// 掉落池的掉落的物品类型
    /// </summary>
    public enum DropPoolItemType
    {
        Equipment = 0,          //装备
        Coin = 1,               //金币
        Exp = 2,                //经验
        Hero = 3,               //英雄
        DropPool = 4,           //掉落池
        MixEquipmentCoin = 5,   //混合金币装备
        FixedHero = 6,          //固定英雄
        MixDrop = 7,            //所有的混合掉落
        TriggerBuff = 100,            //触发器Protoid
        EffectBuff = 101,            //效果protoid
        Arena = 102,            //兵工厂
        UnderGroud = 103,
    }

    /// <summary>
    /// 掉落球类型和属性
    /// </summary>
    public enum MonsterDropItemType
    {
        None,       //空掉落
        Equipment,  //装备
        Coin,       //金币
        Experience, //经验
        Hero,       //英雄
        MixEquipmentCoin, //金币和装备
        DropBox,
        Draconic,   //龙族羁绊掉落
        FixedHero, //固定英雄
        MixDrop,    //所有的混合掉落
        Draconic_HeroLimit,//通用龙族掉落逻辑，但拾取时判定英雄是否超限
        NormalDrop,         //通用掉落
        DirectDrop,         //非妮蔻掉落   备战区满不捡 ↑↑↑
    }

    public enum EquipmentGenrateType
    {
        None,
        RoundSelect,    //轮抽
        PlayerSynthesis,    //玩家装备栏合成
        HeroSynthesis,      //英雄身上合成
        Stealed, //窃贼手套偷到的
        TempTransfer, //临时转移来的;
        SoulEquip, //首次2星的灵魂装备
        ScrapEquip,//废工厂合成装备
        ExtraAddHexEquip,//海克斯科技随机生成的装备
    }

    public enum DropPoolType
    {
        limited,            //有限池,随机随到的物品需要从池中移掉
        unlimited,          //无限池,随机随到的物品不需要从池中移掉
    }

    public class FetterHeroStruct
    {
        public int iKey;
        public int iType;
        public int iCheckId;
        public List<int> vecHeroGroupList = new List<int>(); //hero group iD
        public static int GenKey(int type, int checkId)
        {
            return type * 10000 + checkId;
        }
    }

    public class CSoConfig
    {
        public static FVector3 s_fvDropBoxPos = new FVector3(-6f, 0f, 3.5f);    //宝箱掉落位置
        public static FVector3 s_fvDropOffset = new FVector3(2f, 0f, 0f);       //金币散落半径
        public static FVector2 s_fvLeftDownPos = new FVector2(-6f, -3f);
        public static FVector2 s_fvRightTopPos = new FVector2(6f, 3f);
        public static Fix64 s_f6DropXRange = new Fix64(12);
        public static Fix64 s_f6DropYRange = new Fix64(6);
        public static FVector3 s_fvOutFieldDrop = new FVector3();
        
        protected TAllGameCfgClient m_stAllInfo = new TAllGameCfgClient();
        public TAllGameCfgClient stAllInfo
        {
            get { return m_stAllInfo; }
        }
        // 
        public static Fix64 heroBulletSpeed = new Fix64(14);
        public static Fix64 playerBulletSpeed = new Fix64(11);
        // 
        public LinkedHashMap<int, FetterHeroStruct> m_mapFetterHeroInfo = new LinkedHashMap<int, FetterHeroStruct>();
        public List<int> m_summonWithFetterHero = new List<int>();
        

        protected RoundConfigCache m_roundConfig;


        protected LinkedHashMap<int, LinkedHashMap<int, int>> m_mapLevelWeigth = new LinkedHashMap<int, LinkedHashMap<int, int>>();
        protected LinkedHashMap<int, int> m_mapLevelWeigthLuckyHero = new LinkedHashMap<int, int>();


        protected LinkedHashMap<int, LinkedHashMap<int, LinkedHashMap<int, LinkedHashMap<int, int>>>> m_mapNewLevelWeigth = new LinkedHashMap<int, LinkedHashMap<int, LinkedHashMap<int, LinkedHashMap<int, int>>>>(); // 新等级的配置

        protected List<TACG_DamageRules_Hero_Cfg> m_lstDamageRulesHeroCfgs = new List<TACG_DamageRules_Hero_Cfg>();

        protected List<TACG_DamageRules_Round_Cfg> m_lstDamageRulesRoundCfgs = new List<TACG_DamageRules_Round_Cfg>();


        protected LinkedHashMap<int, TAC_DraftRateItem> m_mapHeroDraftRate = new LinkedHashMap<int, TAC_DraftRateItem>();


        protected LinkedHashMap<int, TAC_DraftRateItem> m_mapEquipDraftRate = new LinkedHashMap<int, TAC_DraftRateItem>();


        protected LinkedHashMap<int, int> m_mapShareFraftCfg = new LinkedHashMap<int, int>();
  
        protected List<int> m_listDraftReleaseInterval = new List<int>();

        protected List<int> m_listRookieDraftReleaseInterval = new List<int>();


        protected LinkedHashMap<int, LinkedHashMap<int, int>> m_mapHeroEquipMatchRate = new LinkedHashMap<int, LinkedHashMap<int, int>>();


        protected List<int> m_baseEquipmentIDList = new List<int>();


        protected LinkedHashMap<int, int> m_mapSceneTypeCount = new LinkedHashMap<int, int>();

        // 
        private LinkedHashMap<int, int> m_mapTurnAddMoney = new LinkedHashMap<int, int>();
        // 
        private LinkedHashMap<int, int> m_mapConWinAddMoney = new LinkedHashMap<int, int>();
        // 
        private LinkedHashMap<int, int> m_mapConLoseAddMoney = new LinkedHashMap<int, int>();
        // 
        private LinkedHashMap<int, int> m_mapTurnAddExp = new LinkedHashMap<int, int>();
        // 
        private LinkedHashMap<int, int> m_mapRankAddMoney = new LinkedHashMap<int, int>();
        // 
        private LinkedHashMap<int, int> m_mapRankAddExp = new LinkedHashMap<int, int>();
        // 
        private int m_countInterest = 0;
        // 
        private int m_addTeamCoinRoundCount = 0;
        // 
        private int m_addTeamCoinNum = 0;
        // 
        private LinkedHashMap<int, TACG_Tiny_Origin_Cfg> m_mapTinyOrigin = new LinkedHashMap<int, TACG_Tiny_Origin_Cfg>();
        private LinkedHashMap<int, TACG_Tiny_Cfg> m_mapTiny = new LinkedHashMap<int, TACG_Tiny_Cfg>();
        // 
        private LinkedHashMap<int, TTAC_Round_Client> m_mapRound = new LinkedHashMap<int, TTAC_Round_Client>();
        protected ConfigCacheBaseMap<TACG_Equipment_Client> m_mapEquipmentConfig;
        //
        protected LinkedHashMap<int, TACG_Equipment_Client> m_mapEquipmentClient = new LinkedHashMap<int, TACG_Equipment_Client>();
        protected LinkedHashMap<int, TACG_Equipment_Client> m_mapEquipmentSynthesisCfg = new LinkedHashMap<int, TACG_Equipment_Client>();
        // -
        private LinkedHashMap<int, List<int>> m_EquipHeroMutexDic = new LinkedHashMap<int, List<int>>();
        
        //
        private Dictionary<int, int> m_sionStagePlusTransfer = new Dictionary<int, int>();

        private TGlobalConf m_globalConf;

        private TRoundConf m_roundConf;

        private int DraftPlanId = 0;

        
        public CSoConfig()
        {
            InitExtraData();
            m_roundConfig = new RoundConfigCache();
            m_roundConf = new TRoundConf();
            m_roundConf.Reset();
        }
        
#region ExtraMap -- PlayMode

        private void InitExtraData()
        {
            m_mapTACHeroConfig = new TACHeroCacheMap(this);
            m_mapEquipmentConfig = new ConfigCacheBaseMap<TACG_Equipment_Client>(this);
        }
        
        protected TACHeroCacheMap m_mapTACHeroConfig;



        private void LoadExtraPlayModeData(PlayModeData playModeType)
        {
            m_mapTACHeroConfig.SetSrcMap(m_stAllInfo.mapACG_Hero_Client);
            m_mapEquipmentConfig.SetSrcMap(m_stAllInfo.mapACG_Equipment_Client);

            m_mapTACHeroConfig.SetExMap(ZGame.DataBaseManager.Instance.GetACGHeroTableByMode(playModeType));
            m_mapEquipmentConfig.SetExMap(ZGame.DataBaseManager.Instance.GetEquipmentClientTableByMode(playModeType));
        }
#endregion
        
        


        public virtual void Dispose()
        {
            m_mapTACHeroConfig.clear();
            m_roundConfig.Release();
            return;
        }

        public void SetAllCfgInfo(TAllGameCfgClient stAllGameCfg)
        {
            m_stAllInfo = stAllGameCfg;
        }
        
        public int SetSoConfig(TAllGameCfgClient stAllGameCfg , PlayModeData playModeType)
        {
            m_stAllInfo = stAllGameCfg;
            

            LoadAutoChessAllHero();
            
            LoadExtraPlayModeData(playModeType);
            

            LoadAutoChessAllHeroKu();

            LoadDraftTimeCfg();
            LoadLogicBulletSpeed();
            LoadTinyConfig();
            LoadTinyOriginConfig();


            LoadGlobalConf();

            LoadBasicBuffData();
            LoadKeyTargetDropConf();
            return 0;
        }

        public void LoadBasicBuffData()
        {
            
        }

        public void LoadKeyTargetDropConf()
        {
            
        }

        public ConfigHashMap<int, TACG_DropPoolItem_Client> GetAllDropPoolItemConfig()
        {
            return m_stAllInfo.mapACG_DropPoolItem_Client;
        }

        public bool IsSynthesisEquipment(int equipmentID)
        {
            TACG_Equipment_Client equipmentClient = GetEquipmentClient(equipmentID);
            return IsSynthesisEquipment(equipmentClient);
        }

        public bool IsSynthesisEquipment(TACG_Equipment_Client equipmentClient)
        {
            if (equipmentClient != null)
            {
                // 高级装备
                return (equipmentClient.iSynthesisID1 != 0 && equipmentClient.iSynthesisID2 != 0) ||
                       (equipmentClient.iSynthesisID3 != 0 && equipmentClient.iSynthesisID4 != 0) ||
                       (equipmentClient.iSynthesisID5 != 0 && equipmentClient.iSynthesisID6 != 0);
            }
            return false;
        }

        public bool GetSynthesisEquipmentID(int equipmentID, ref int iSynthesisID1, ref int iSynthesisID2)
        {
            TACG_Equipment_Client equipmentClient = GetEquipmentClient(equipmentID);
            if (equipmentClient != null && equipmentClient.iSynthesisID1 != 0 && equipmentClient.iSynthesisID2 != 0)
            {
                iSynthesisID1 = equipmentClient.iSynthesisID1;
                iSynthesisID2 = equipmentClient.iSynthesisID2;
                // 高级装备
                return true;
            }
            return false;
        }

        /// <summary>
        /// 根据2个基础装备查找对应合成装备
        /// </summary>
        public TACG_Equipment_Client GetSynthesisEquipment(int equipmentID1, int equipmentID2)
        {
            TACG_Equipment_Client cfg = null;
            if (equipmentID1 != 0 && equipmentID2 != 0)
            {
                m_mapEquipmentSynthesisCfg.TryGetValue(equipmentID1 * 100000 + equipmentID2, out cfg);
                if (cfg == null)
                    m_mapEquipmentSynthesisCfg.TryGetValue(equipmentID2 * 100000 + equipmentID1, out cfg);
                if (cfg != null)
                    return cfg;
            }
            if (cfg == null)
            {
                for (var iter = m_mapEquipmentClient.GetEnumerator(); iter.MoveNext();)
                {
                    var equipConfig = iter.Current.Value;
                    if (IsSynthesisBuildEquipment(equipConfig.iSynthesisID1, equipConfig.iSynthesisID2,
                        equipmentID1, equipmentID2) ||
                        IsSynthesisBuildEquipment(equipConfig.iSynthesisID3, equipConfig.iSynthesisID4,
                            equipmentID1, equipmentID2) ||
                        IsSynthesisBuildEquipment(equipConfig.iSynthesisID5, equipConfig.iSynthesisID6,
                            equipmentID1, equipmentID2))
                    {
                        return equipConfig;
                    }
                }
            }
            return null;
        }

        public TACG_Equipment_Client GetEquipmentClient(int equipmentID)
        {
            return SearchEquipment(equipmentID);
        }

        public TACG_Equipment_Client SearchEquipment(int id)
        {
            return m_mapEquipmentConfig.GetByID(id);
        }

        public void LoadGlobalConf()
        {
            m_globalConf.SurplusHeroStategy = GetRoomCfgPlanId((int)GlobalDataConst.E_SURPLUS_HERO_STRATEGY);
            m_globalConf.PromoteHeroEquipStrategy = GetRoomCfgPlanId((int)GlobalDataConst.E_PROMOTE_HERO_EQUIP_STRATEGY);
            var outFieldCfg = GetRoomCfg((int)GlobalDataConst.E_OUT_FIELD_DROP);
            if (outFieldCfg != null)
            {
                float x, z;
                float.TryParse(outFieldCfg.sParamContent, out x);
                float.TryParse(outFieldCfg.sParam1, out z);
                s_fvOutFieldDrop = new FVector3(x, 0, z);
            }
        }

        public void LoadLogicBulletSpeed()
        {
            // 
            string sPlayerSpeed = GetRoomCfgClient((int)GlobalDataConst.E_HERO_BULLET_SPEED);
            string sHeroSpeed = GetRoomCfgClient((int)GlobalDataConst.E_PLAYER_BULLET_SPEED);
            heroBulletSpeed = new Fix64(int.Parse(sHeroSpeed));
            playerBulletSpeed = new Fix64(int.Parse(sPlayerSpeed));
        }

        // 自走棋英雄相关配置
        public int LoadAutoChessAllHero()
        {
            m_mapTACHeroConfig.clear();
            return 0;
        }

        public void LoadFetterHeroInfo(CSoGame csoGame)
        {

        }

        public void ParseFetter(int iHeroId)
        {

        }

        List<TTAC_Quest_Client> m_QusetInfoList;
        public void LoadQuestInfo(int type)
        {
            m_QusetInfoList = new List<TTAC_Quest_Client>();
            foreach (var item in m_stAllInfo.mapTAC_Quest_Client)
            {
                if (type == item.Value.iQuestType)
                    m_QusetInfoList.Add(item.Value);
            }
            m_QusetInfoList.StableSortSoGame_Logic((a, b) => a.iTurnCount.CompareTo(b.iTurnCount));
        }

        //加载关卡信息
        public TTAC_Quest_Client GetQuestInfoByTotalRound(int totalRound, bool isLogicUse)
        {
            foreach (var item in m_QusetInfoList)
            {
                if (item.iTurnCount == totalRound)
                    return item;
            }
            return GetRecycleQuestClient(totalRound, isLogicUse);
        }

        //获取循环关卡(最后一个Stage循环)
        private TTAC_Quest_Client GetRecycleQuestClient(int totalRound, bool isLogicUse)
        {
            if (m_QusetInfoList.Count <= 0) return null;

            var client = m_QusetInfoList[m_QusetInfoList.Count - 1];
            int curQuestIndex = Math.Max((totalRound - m_QusetInfoList.Count - 1) % client.iRound, 0) + 1;
            foreach (var item in m_QusetInfoList)
            {
                if (item.iStage == client.iStage && item.iRound == curQuestIndex)
                    return item;
            }
            return null;
        }

        public string GetRoomCfgClient(int id)
        {
            TTAC_RoomCfg_Client roomCfg = null;
            bool ret = m_stAllInfo.mapTAC_RoomCfg_Client.TryGetValue(id, out roomCfg);
            if (ret) return roomCfg.sParamContent;

            return "";
        }

        public int GetRoomCfgPlanId(int id)
        {
            TTAC_RoomCfg_Client roomCfg = null;
            bool ret = m_stAllInfo.mapTAC_RoomCfg_Client.TryGetValue(id, out roomCfg);
            if (ret) return int.Parse(roomCfg.sPlan);

            return 0;
        }

        public TTAC_RoomCfg_Client GetRoomCfg(int id)
        {
            TTAC_RoomCfg_Client roomCfg = null;
            bool ret = m_stAllInfo.mapTAC_RoomCfg_Client.TryGetValue(id, out roomCfg);
            return roomCfg;
        }

        public TACHeroCacheMap GetHeroCacheMap()
        {
            return m_mapTACHeroConfig;
        }

        public TACG_Hero_Client GetAutoChessHeroInfoByID(int uHeroId)
        {
            TACG_Hero_Client heroClient = m_mapTACHeroConfig.GetByID(uHeroId);
            return heroClient;
        }

        //取指定种族的拉克丝
        public TACG_Hero_Client GetLuxBySpec(string spec)
        {
            return m_mapTACHeroConfig.GetLuxBySpec(spec);
        }

        public TACG_Hero_Client GetHeroClientByGroupStar(int group, int star)
        {
            var cfg = m_mapTACHeroConfig.GetByGroupStar(group, star);
            if (cfg == null)
            {
                Diagnostic.Error("group = " + group + "   找不到对应的星级配置 star = " + star);
            }

            return cfg;
        }

        /// <summary>
        /// 所有一星lux
        /// </summary>
        /// <returns></returns>
        public List<int> GetAllLevel1Lux()
        {
            return m_mapTACHeroConfig.GetAllLevel1Lux();
        }
        
        //加载英雄库
        //[IFix.Patch]
        public int LoadAutoChessAllHeroKu()
        {
           
            return 0;
        }

        public int GetHeroStarByID(int uHeroId)
        {
            TACG_Hero_Client heroClient = m_mapTACHeroConfig.GetByID(uHeroId);
            return heroClient == null ? -1 : heroClient.iStar;
        }

        public LinkedHashMap<int, LinkedHashMap<int, int>> GetHeroKuLevel()
        {
            return new LinkedHashMap<int, LinkedHashMap<int, int>>(m_mapLevelWeigth);
        }
        
        public static int CompareQuestNPCClient(TTAC_Quest_NPC_Client a, TTAC_Quest_NPC_Client b)
        {
            if (a.iID > b.iID)
                return 1;
            else if (a.iID < b.iID)
                return -1;
            return 0;

        }
        
       
        public void LoadTinyConfig()
        {
            m_mapTiny.Clear();
            if (m_stAllInfo.stTAllGameCfgClientExt1.mapACG_TinyHero_Client != null)
            {
                foreach (var client in m_stAllInfo.stTAllGameCfgClientExt1.mapACG_TinyHero_Client.Values)
                {
                    m_mapTiny.Add(client.iID,
                        new TACG_Tiny_Cfg()
                        {
                            originId = client.iSubType,
                            classId = client.iType,
                            summonTinyId = client.iSummonTinyId,
                            canTeleport = client.iCanTeleport == 1,
                        });
                }
            }
        }

        public static TACG_Tiny_Cfg GetTinyCfg(TACG_TinyHero_Client client)
        {
            return new TACG_Tiny_Cfg()
            {
                originId = client.iSubType,
                classId = client.iType,
                summonTinyId = client.iSummonTinyId,
                canTeleport = client.iCanTeleport == 1,
            };
        }

        public TACG_Tiny_Cfg GetTiny(int tinyId)
        {
            TACG_Tiny_Cfg client = null;
            m_mapTiny.TryGetValue(tinyId, out client);
            return client;
        }

        public TACG_Tiny_Origin_Cfg GetTinyOrigin(int tinyOriginId)
        {
            TACG_Tiny_Origin_Cfg client = null;
            m_mapTinyOrigin.TryGetValue(tinyOriginId, out client);
            return client;
        }

        public static TACG_Tiny_Origin_Cfg GetTinyOriginCfg(TACG_TinyOrigin_Client client)
        {
            List<TACG_Tiny_Origin_Cfg.Fetter> cfgFetters = new List<TACG_Tiny_Origin_Cfg.Fetter>();
            if (!string.IsNullOrEmpty(client.sLinkFetter))
            {
                var fetters = client.sLinkFetter.BeginSplit(';');
                for (int i = 0; i < fetters.Length; ++i)
                {
                    var fetter = fetters[i];
                    var fetterArr = fetter.BeginSplit(':');
                    if (fetterArr.Length == 3)
                    {
                        TACG_Tiny_Origin_Cfg.Fetter cfgFetter = new TACG_Tiny_Origin_Cfg.Fetter();
                        bool success = fetterArr.TryParse(0, out cfgFetter.fetterId);
                        cfgFetter.effect = fetterArr[1];
                        success &= fetterArr.TryParse(2, out cfgFetter.effectPos);
                        if (success)
                        {
                            cfgFetters.Add(cfgFetter);
                        }
                    }
                    fetterArr.EndSplit();
                }
                fetters.EndSplit();
            }

            List<int> meetExpressions = new List<int>();
            if (!string.IsNullOrEmpty(client.sMeetExpression))
            {
                var arr = client.sMeetExpression.BeginSplit(';');
                for (int i = 0; i < arr.Length; ++i)
                {
                    if (arr.TryParse(i, out int val))
                    {
                        meetExpressions.Add(val);
                    }
                }
                arr.EndSplit();
            }

            List<int> winExpressions = new List<int>();
            if (!string.IsNullOrEmpty(client.sWinExpression))
            {
                var arr = client.sWinExpression.BeginSplit(';');
                for (int i = 0; i < arr.Length; ++i)
                {
                    if (arr.TryParse(i, out int val))
                    {
                        winExpressions.Add(val);
                    }
                }
                arr.EndSplit();
            }

            if (cfgFetters.Count > 0 || meetExpressions.Count > 0 || winExpressions.Count > 0)
            {
                TACG_Tiny_Origin_Cfg cfgTinyOrigin = new TACG_Tiny_Origin_Cfg();
                cfgTinyOrigin.linkFetters = cfgFetters;
                cfgTinyOrigin.meetExprisions = meetExpressions;
                cfgTinyOrigin.winExprisions = winExpressions;
                return cfgTinyOrigin;
            }
            return null;
        }

        public void LoadTinyOriginConfig()
        {
            m_mapTinyOrigin.Clear();
            if (m_stAllInfo.stTAllGameCfgClientExt1.mapACG_TinyOrigin_Client != null)
            {
                foreach (var client in m_stAllInfo.stTAllGameCfgClientExt1.mapACG_TinyOrigin_Client.Values)
                {
                    var cfgTinyOrigin = GetTinyOriginCfg(client);
                    if (cfgTinyOrigin != null)
                        m_mapTinyOrigin.Add(client.iID, cfgTinyOrigin);
                }
            }
        }


        private bool IsSynthesisBuildEquipment(int synthesisID1, int synthesisID2, int id1, int id2)
        {
            return (synthesisID1 == id1 && synthesisID2 == id2) || (synthesisID2 == id1 && synthesisID1 == id2);
        }

        protected T SearchItem<T>(Dictionary<int, T> dict, int id, bool waring_no_found = true)
        {
            T t = default(T);
            if (dict.TryGetValue(id, out t) == false)
            {
                if (waring_no_found)
                {
                    //如果表里没有这个ID
                    //Diagnostic.Error("not found id {0} in table file:  {1}", id, typeof(T).Name);
                }
            }
            return t;
        }

        public int SplitSposition(string strPos, ref int iX, ref int iY)
        {
            // hardCode 8*8格子
            iX = 7;
            iY = 7;
            FastStringSplit splitResults = strPos.BeginSplit('|');
            if (splitResults.Length == 2)
            {
                iX = splitResults.ParseInt32(0);
                iY = splitResults.ParseInt32(1);
                splitResults.EndSplit();
                return 0;
            }
            splitResults.EndSplit();
            return -1;
        }

#region 英雄特殊扣血规则类型
        enum HeroDamageRuleType
        {
            None, // 
            CountRule,// 
        }
#endregion
        
        // 
        //  
        public int GetHeroDeductLife(int CurrentTotalTurnCount, int iHeroType, int iStar, int iQuality, int LeftHeroCount)
        {
            return 0;
        }
        
        public int GetDeductLifeValue(int CurrentTotalTurnCount, int iIsHome, int Result)
        {
            // result : 0平局  2 输
            
            return 0;
        }

        public int GetDeductLifeExtraValue(int CurrentTotalTurnCount, int iIsHome)
        {
            
            return 0;
        }
        
        public int GetMinDeductLifeValue(int CurrentTotalTurnCount, int iIsHome)
        {
            
            return 0;
        }

        public TAC_LevelRate ParseDraftLevelRate(int iLevel, int iRate, string sLimitNum, string sStarNum = null)
        {
            TAC_LevelRate stLevelRate = new TAC_LevelRate();
            stLevelRate.iLevel = iLevel;
            stLevelRate.iRate = iRate;
            FastStringSplit templist = sLimitNum.BeginSplit('|');
            if (templist.Length != 2)
            {
                Diagnostic.Error("ParseDraftLevelRate, iLevel:{0} ,iRate:{1}, sLimitNum:{2}",
                    iLevel, iRate, sLimitNum);
                templist.EndSplit();
                return null;
            }
            int min = 0;
            templist.TryParse(0, out min);
            stLevelRate.iMinNum = min;
            int max = 0;
            templist.TryParse(1, out max);
            stLevelRate.iMaxNum = max;

            if (!string.IsNullOrEmpty(sStarNum))
            {
                stLevelRate.mHeroStarNum = new LinkedHashMap<int, int>();
                FastStringSplit sStar = sStarNum.BeginSplit(':');
                for(int i = 0;i < sStar.Length; i++)
                {
                    FastStringSplit cfg = sStar[i].BeginSplit('|');
                    stLevelRate.mHeroStarNum.Add(cfg.ParseInt32(0), cfg.ParseInt32(1));
                    cfg.EndSplit();
                }
                sStar.EndSplit();
            }
            templist.EndSplit();

            return stLevelRate;
        }


        public int LoadDraftRate(int planID)
        {
            DraftPlanId = planID;

            m_mapHeroDraftRate.Clear();
            m_mapEquipDraftRate.Clear();
 
            return 0;
        }

        public int LoadDraftTimeCfg()
        {
            m_mapShareFraftCfg.Clear();

            
            return 0;
        }

        public int GetDraftTimeCfgById(int id)
        {
            int cfgTime = -1;
            if (m_mapShareFraftCfg.ContainsKey(id))
            {
                cfgTime = m_mapShareFraftCfg[id];
            }
            return cfgTime;
        }

        public int GetDraftIntervalTime(int turnCount)
        {
            if (string.IsNullOrEmpty(m_roundConf.ShardDraftTime) || m_roundConf.ShardDraftTime.Equals("-1"))
            {
                if (turnCount < m_listDraftReleaseInterval.Count && turnCount >= 0)
                {
                    return m_listDraftReleaseInterval[turnCount];
                }
                return m_listDraftReleaseInterval[m_listDraftReleaseInterval.Count - 1];
            }
            else
            {
                int[] time = m_roundConfig.GetSharedDraftTime(m_roundConf.ShardDraftTime);

                if(time == null || time.Length == 0)
                {
                    if (turnCount < m_listDraftReleaseInterval.Count && turnCount >= 0)
                    {
                        return m_listDraftReleaseInterval[turnCount];
                    }
                    return m_listDraftReleaseInterval[m_listDraftReleaseInterval.Count - 1];
                }

                if (turnCount < time.Length && turnCount >= 0)
                {
                    return time[turnCount];
                }
                return time[time.Length - 1];
            }
        }

        public int GetRookieDraftIntervalTime(int turnCount)
        {
            if (turnCount < m_listRookieDraftReleaseInterval.Count && turnCount >= 0)
            {
                return m_listRookieDraftReleaseInterval[turnCount];
            }
            return m_listRookieDraftReleaseInterval[m_listRookieDraftReleaseInterval.Count - 1];
        }

        public List<int> GetDraftIntervalTimeList()
        {
            if (m_roundConf.ShardDraftTime.Equals("-1"))
            {
                return m_listDraftReleaseInterval;
            }
            else
            {
                int[] time = m_roundConfig.GetSharedDraftTime(m_roundConf.ShardDraftTime);
                List<int> ret = new List<int>();
                ret.AddRange(time);
                return ret;
            }
        }

        public int GetDraftMaxRound()
        {
            return m_mapHeroDraftRate.Count;
        }

        public TSetConf GetSetConf(int setID, int setConfigID)
        {
            TSetConf tTSetConf = new TSetConf();
            tTSetConf.SetID = setID;
            if (m_stAllInfo.mapACG_Set_Client.ContainsKey(setConfigID))
            {
                TACG_Set_Client config = m_stAllInfo.mapACG_Set_Client[setConfigID];
                tTSetConf.HeroPoolId = config.iHeroKuID;
                tTSetConf.ElementFlag = config.iElementOpen == 1;
                tTSetConf.DamageRuleType = config.iDamageRuleType;
                tTSetConf.MonsterQuestType = config.iMonsterType;
                tTSetConf.QuestType = config.iQuestType;
                tTSetConf.LevelPlanID = config.iLevel;
                tTSetConf.ShareDraftPlanID = config.iRoundSelectPlanID;
                tTSetConf.InitLife = config.iInitLife;
                tTSetConf.InitDrop = config.iInitDrop;
                tTSetConf.RankDropPlanID = config.iRankDrop;
                tTSetConf.KillDropPlanID = config.iKillDrop;
                tTSetConf.HolyDropPlanID = config.iHolyDrop;
                tTSetConf.ThreeStarReward = config.iThreeStarReward != 0;
                tTSetConf.SoulEquipFlag = config.iSoulEquip == 1;
                tTSetConf.AutoRefreshFlag = config.iAutoRefresh == 1;
                tTSetConf.GalaxyModeID = config.iGalaxyModeID;
                tTSetConf.DropPlanId = config.iDropPlanID;
                tTSetConf.MirrorDeduct = config.iMirroDeduct != 0;
                tTSetConf.AiUseSet = config.iAiUseSet;
                tTSetConf.LootType = config.iLootType;
                tTSetConf.GetGoldPlanID = config.iGetGold;
                tTSetConf.GetExpPlanID = config.iGetExp;
                tTSetConf.GetCtrlPlanID = config.iSetControllerPlan;
                tTSetConf.BuyExpConfData = new BuyExpConf()
                {
                    BuyExpAddExp = config.iBuyExpAddExp,
                    BuyExpPrice = config.iBuyExpPrice,
                    BuyExpOpen = config.iBuyExpOpen != 0
                };
                tTSetConf.MatchConfData = new MatchConf {MatchID = config.iMatchID, CellReduce = config.iCellReduce};
                tTSetConf.HeroUpConfData = new HeroUpConf {HeroCount = config.iHeroUpCount};
                tTSetConf.EquipConfData = new EquipConf
                {
                    EquipmentPlanId = config.iEquipmentPlanID,
                    UnLoadEquipPlanId = config.iUnLoadEquipPlanId,
                    EquipmentStorePlanId_S5 = config.iEquipStorePlanID
                };
                tTSetConf.HAConfData = new HAConf()
                {
                    HAPlanID = config.iHextechAugmentPlan,
                    HAHeroPlanID = config.iHextechAugmentPlan
                };
                tTSetConf.HeroPackConfData = new HeroPackConf()
                {
                    PKGPlanID = config.iHeroPackPlan
                };
                tTSetConf.GameOverConfData = new GameOverConf {GameOverPlanId = config.iGameOverPlanID};
                tTSetConf.HeavenSelectBuffConfData = new HeavenSelectBuffConf();
                tTSetConf.HeavenSelectBuffConfData.HeavenSelectBuffFlag = config.iHeavenSelectBuffOpen == 1;
                tTSetConf.HeavenSelectBuffConfData.HeavenSelectBuffPlanId = config.iHeavenSelectBuffPlanId;
                tTSetConf.ExtraRoomCfgPlanID = config.iExtraConfigId;
                tTSetConf.LuckyHeroRefreshConfData = new LuckyHeroRefreshConf();
                tTSetConf.LuckyHeroRefreshConfData.HeroKuId = config.iLuckyHeroKu;
                tTSetConf.LuckyHeroRefreshConfData.HeroNumLimit = config.iLuckyHeroMaxNum;
                tTSetConf.HeroSellPrice = config.sHeroSellPrice;
                tTSetConf.RefreshMoney = config.iRefreshMoney;
                tTSetConf.FakeHeroRule = config.sFakeHeroRules;
                tTSetConf.LimitedHeroRule = config.iLimitHeroPlan;
                tTSetConf.PlayMode = (PlayModeType)config.iPlayMode;
                tTSetConf.PlayMode_Data = (PlayModeData)config.iModeData;
                tTSetConf.sBadLuckProtectConf = config.sBadLuckProtectionConf;

                tTSetConf.HeavenChooserConfData = new HeavenChooserConf(config.sHeavenChooser);
                tTSetConf.VipHeroConf = config.sVipHeroConf;
                tTSetConf.StagePlusPlan = config.iStagePlusPlan;
            }


            return tTSetConf;
        }

#region 根据set加载相关配置

        public int GetLuckyHeroLevelWeight(int level)
        {
            return m_mapLevelWeigthLuckyHero[level];
        }

        public void LoadLevelWeigth(int planID)
        {
            m_mapLevelWeigth.Clear();

        }

        // 自走棋信使等级相关配置
        public int LoadAutoChessLevelInfo(int levelPlanID)
        {
            return 0;
        }

        public void LoadStealEquipRateSetConf(int planId)
        {
        }

        public void LoadMonsterDropSetConfig(int planId)
        {
          
        }

        public void LoadEquipmentClientConfig(int planId)
        {
            try
            {
                m_mapEquipmentClient.Clear();
                m_mapEquipmentSynthesisCfg.Clear();
                m_baseEquipmentIDList.Clear();

                int i = 0;
                foreach (var item in m_mapEquipmentConfig.GetAllDic())
                {
                    //战场编辑器模式下，载入全量装备配置
                    //// TODO：百人模式是否改用planid 区分
                    if (item.Value.iPlanID == planId)
                    {
                        m_mapEquipmentClient.Add(item.Key, item.Value);

                        if (item.Value.iSynthesisID1 > 0 && item.Value.iSynthesisID2 > 0)
                            m_mapEquipmentSynthesisCfg.Add((item.Value.iSynthesisID1 * 100000 + item.Value.iSynthesisID2), item.Value);

                        if (item.Value.iSynthesisID3 > 0 && item.Value.iSynthesisID4 > 0)
                            m_mapEquipmentSynthesisCfg.Add((item.Value.iSynthesisID3 * 100000 + item.Value.iSynthesisID4), item.Value);

                        if (item.Value.iSynthesisID5 > 0 && item.Value.iSynthesisID6 > 0)
                            m_mapEquipmentSynthesisCfg.Add((item.Value.iSynthesisID5 * 100000 + item.Value.iSynthesisID6), item.Value);

                        if (item.Value.iLevel == 1 || item.Value.iLevel == 2)
                        {
                            i++;
                        }

                        if (item.Value.iLevel == 1)
                        {
                            m_baseEquipmentIDList.Add(item.Key);
                        }
                    }
                }
            }
            catch(Exception ex)
            {

            }
            
        }
        
        private void LoadRoundCoinExpConfig(int coinType, int expType)
        {
           
        }

        private void LoadRankDropList(int iDropId)
        {

        }

        private void LoadKillDropData(int iDropId)
        {

        }

        private void LoadHolyDropData(int planId)
        {
          
        }

        public void LoadDamageRulesHeroConfig(int iDemageRuleType)
        {
           
        }

        public void LoadDamageRulesRoundConfig(int iDemageRuleType)
        {

        }

#endregion


        void LoadSionExtraConfig(string sParams)
        {
            if (string.IsNullOrEmpty(sParams)) return;
            FastStringSplit configs = sParams.BeginSplit('|');

            for (int i = 0; i < configs.Length; i++)
            {
                FastStringSplit cs = configs[i].BeginSplit('=');
                int k = cs.ParseInt32(0);
                int v = cs.ParseInt32(1);
                cs.EndSplit();
                
                if (!m_sionStagePlusTransfer.ContainsKey(k))
                {
                    m_sionStagePlusTransfer.Add(k, v);
                }
            }
            
            configs.EndSplit();
        }

        public void LoadSetConf(TSetConf setConf)
        {
            LoadQuestInfo(setConf.QuestType);
            LoadLevelWeigth(setConf.LevelPlanID);
            LoadAutoChessLevelInfo(setConf.LevelPlanID);
            LoadStealEquipRateSetConf(setConf.EquipConfData.EquipmentPlanId);
            LoadMonsterDropSetConfig(setConf.DropPlanId);
            LoadEquipmentClientConfig(setConf.EquipConfData.EquipmentPlanId);
            LoadRoundCoinExpConfig(setConf.GetGoldPlanID, setConf.GetExpPlanID);
            LoadRankDropList(setConf.RankDropPlanID);
            LoadKillDropData(setConf.KillDropPlanID);
            LoadHolyDropData(setConf.HolyDropPlanID);
            LoadDamageRulesHeroConfig(setConf.DamageRuleType);
            LoadDamageRulesRoundConfig(setConf.DamageRuleType);

            LoadCanNotTransferEquipment();
            LoadFakeHeroRules(setConf.FakeHeroRule);
            LoadEquipHeroMutexConfig(setConf.SetID);
        }
        
        LinkedHashMap<int, int> FakeHeroRules;
        private void LoadFakeHeroRules(string temp)
        {
            if(string.IsNullOrEmpty(temp))
                return;
            FakeHeroRules = new LinkedHashMap<int, int>();
            FastStringSplit turnRule = temp.BeginSplit('|');
            for (int i = 0; i < turnRule.Length; i++)
            {
                FastStringSplit rules = turnRule[i].BeginSplit('=');
                int turncount = rules.ParseInt32(0);
                int level = rules.ParseInt32(1);
                FakeHeroRules.Add(turncount, level);
                rules.EndSplit();
            }
            turnRule.EndSplit();
        }

        public LinkedHashMap<int, int> GetFakeHeroRules()
        {
            return FakeHeroRules;
        }


        private void LoadCanNotTransferEquipment()
        {
            
        }

        private void LoadEquipHeroMutexConfig(int setId)
        {
           
        }

        public List<int> GetBaseEquipmentIDList()
        {
            return m_baseEquipmentIDList;
        }

        public TACG_Hero_Client GetACGHeroByStar(TACG_Hero_Client acgHeroClient, int star)
        {
            //野怪没有iGroup，这里要判断下;
            if (acgHeroClient.iGroup > 0)
            {
                TACG_Hero_Client starCfg = m_mapTACHeroConfig.GetByGroupStar(acgHeroClient.iGroup, star);
                return starCfg == null ? GetAutoChessHeroInfoByID(acgHeroClient.iGroup) : starCfg;//没有对应星级取1星
            }
            return acgHeroClient;
        }

        
        public TGlobalConf GetGlobalConf()
        {
            return m_globalConf;
        }
    }
}