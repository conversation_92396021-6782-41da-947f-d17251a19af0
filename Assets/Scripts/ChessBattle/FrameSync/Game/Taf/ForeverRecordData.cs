using GameFramework.FMath;
using GameFramework.FRand;
using MiniGameClientProto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using TDRConfig;
using TKFrame;
using ZGame;
using ZGameClient;

namespace Z_PVE
{
    /// <summary>
    /// 战斗中保存到Player的全局数据记录
    /// </summary>
    public class ForeverRecordData
    {
        private LinkedHashMap<int, LinkedHashMap<int, LinkedHashMap<int, int>>> _foreverRecordDict;
        CPlayer m_player;
        public ForeverRecordData(CPlayer player)
        {
            _foreverRecordDict = new LinkedHashMap<int, LinkedHashMap<int, LinkedHashMap<int, int>>>();

            m_player = player;
        }
        
        public void Reset()
        {
            _foreverRecordDict.Clear();
        }


        
        private static void SetValueToRecordDict(LinkedHashMap<int, LinkedHashMap<int, LinkedHashMap<int, int>>> dict, int type, int uniqueId, int key, int value)
        {
            if (dict == null)
            {
                dict = new LinkedHashMap<int, LinkedHashMap<int, LinkedHashMap<int, int>>>();
            }

            if (!dict.ContainsKey(type))
            {
                dict.Add(type, new LinkedHashMap<int, LinkedHashMap<int, int>>());
            }

            if (!dict[type].ContainsKey(uniqueId))
            {
                dict[type].Add(uniqueId, new LinkedHashMap<int, int>());
            }
             
            if(!dict[type][uniqueId].ContainsKey(key))
            {
                dict[type][uniqueId].Add(key, value);
            }
            else
            {
                dict[type][uniqueId][key] = value;
            }
        }

        /// <summary>
        /// 深复制数据并设置
        /// </summary>
        /// <param name="data"></param>
        public void SetRecordDict(LinkedHashMap<int, LinkedHashMap<int, LinkedHashMap<int, int>>> data)
        {
            DeepCopyRecordDict(data, ref _foreverRecordDict);
            foreach (var keyValue in _foreverRecordDict)
            {
                NotifyForeverRecordData((ForeverRecordType)keyValue.Key, keyValue.Value);
            }
        }

        public LinkedHashMap<int, LinkedHashMap<int, int>> GetRecordDict(ForeverRecordType type)
        {
            if(!_foreverRecordDict.ContainsKey((int)type))
                _foreverRecordDict.Add((int)type, new LinkedHashMap<int, LinkedHashMap<int, int>>());
            return _foreverRecordDict[(int)type];
        }

        /// <summary>
        /// 深复制数据并返回
        /// </summary>
        /// <returns></returns>
        public LinkedHashMap<int, LinkedHashMap<int, LinkedHashMap<int, int>>> GetDeepRecordDict()
        {
            LinkedHashMap<int, LinkedHashMap<int, LinkedHashMap<int, int>>> res = null;
            DeepCopyRecordDict(_foreverRecordDict, ref res);
            return res;
        }

        private static void DeepCopyRecordDict(LinkedHashMap<int, LinkedHashMap<int, LinkedHashMap<int, int>>> data, ref LinkedHashMap<int, LinkedHashMap<int, LinkedHashMap<int, int>>> res)
        {
                
            if (data != null)
            {
                if (res == null)
                {
                    res = new LinkedHashMap<int, LinkedHashMap<int, LinkedHashMap<int, int>>>();
                }
                
                foreach (var item1 in data)
                {
                    int type = item1.Key;
                    foreach (var item2 in item1.Value)
                    {
                        int uniqueId = item2.Key;
                        foreach (var item3 in item2.Value)
                        {
                            int key = item3.Key;
                            int value = item3.Value;
                            SetValueToRecordDict(res, type, uniqueId, key, value);
                        }
                    }
                }
            }
        }

        public void NotifyForeverRecordData(ForeverRecordType type, LinkedHashMap<int, LinkedHashMap<int, int>> dict)
        {

        }
    }
}
