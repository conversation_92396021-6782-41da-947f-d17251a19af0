using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


public class TransferState_SelectRound_LeaderBack : TransferState_AutoNext
{
    public override int State_Durtion
    {
        get
        {
            return 1500;
        }
    }

    public override string Next_Trigger
    {
        get
        {
            return TransferStateMachine.SelectRound_End;
        }
    }
}