using GameFramework.Common.FSM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

class TransferState_Back_Transfer : TransferStateBase
{
    public override void OnStateEnter(TransferStateMachine context, State<TransferStateMachine> state)
    {
        base.OnStateEnter(context, state);

        context.SoGame.chessBattleCore.Departure();

#if ACGGAME_CLIENT
        // 强制切一下 避免逻辑表现层位置不一致
        {
            var players = context.SoGame.chessBattleCore.allPlayerDatas;
            foreach (var player in players)
            {
                player.Value.faker.SendMovingState();
                player.Value.realer.SendMovingState();
            }
        }   
#endif
    }

    public override void OnStateExit(TransferStateMachine context, State<TransferStateMachine> state)
    {
        base.OnStateExit(context, state);
    }
}