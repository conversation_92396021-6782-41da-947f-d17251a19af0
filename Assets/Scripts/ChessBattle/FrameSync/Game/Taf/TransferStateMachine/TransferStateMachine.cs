using GameFramework.Common.FSM;
using GameFramework.Common.FSM.TransitConditions;


public class TransferStateMachine : StateMachine<TransferStateMachine>
{
    public const string Idle = "Idle";
    public const string Arrive_Monster = "Arrive_Monster";
    public const string Arrive_FlyOut = "Arrive_FlyOut";
    public const string Arrive_Transfer = "Arrive_Transfer";
    public const string Arrive_FlyIn = "Arrive_FlyIn";

    public const string Back_FlyOut = "Back_FlyOut";
    public const string Back_Transfer = "Back_Transfer";
    public const string Back_FlyIn = "Back_FlyIn";

    public const string SelectRound_FlyOut = "SelectRound_FlyOut";        // 前往轮抽
    public const string SelectRound_Transfer = "SelectRound_Transfer";
    public const string SelectRound_PreStartState = "SelectRound_PreStartState";
    public const string SelectRound = "SelectRound";               // 轮抽环节
    public const string SelectRound_PreEndState = "SelectRound_PreEndState";
    public const string SelectRound_BackTransfer = "SelectRound_BackTransfers";
    public const string SelectRound_FlyIn = "SelectRound_FlyIn";

    public const string Arrive_To_Monster = "Arrive_Monster";
    public const string Arrive_Start = "Arrive_Start";
    public const string To_Arrive_Transfer = "To_Arrive_Transfer";
    public const string To_Arrive_FlyIn = "To_Arrive_FlyIn";
    public const string To_Idle = "Arrive_To_Idle";

    public const string Back_Start = "Back_Start";
    public const string To_Back_Transfer = "To_Back_Transfer";
    public const string Turn_Start = "Turn_Start";

    public const string To_SelectRound = "To_SelectRound";
    public const string To_SelectRound_Transfer = "To_SelectRound_Transfer";
    public const string SelectRound_PreStart = "SelectRound_PreStart";
    public const string SelectRound_Start = "SelectRound_Start";
    public const string SelectRound_PreEnd = "SelectRound_PreEnd";
    public const string SelectRound_End = "SelectRound_End";

    public const string NeedFlyIn = "NeedFlyIn";
    public const string IsMonster = "IsMonster";

    public const string Current_Observer_Index = "Current_Observer_Index";

    protected CSoGame m_soGame;

    public CSoGame SoGame
    {
        get { return m_soGame; }
    }

    public TransferStateMachine(CSoGame soGame)
    {
        m_soGame = soGame;
        context = this;
    }

    public void Initialize()
    {
        StateGraph<TransferStateMachine> graph = new StateGraphT<TransferStateMachine, StateGraphStorage_AdjacencyList<TransferStateMachine>>();
        State<TransferStateMachine> idle = graph.AddState(Idle, new TransferState_Idle());
        idle.SetAsDefaultState();

        #region 启程 - 抵达
        // 野怪启程和普通启程不同
        State<TransferStateMachine> arrive_monster = graph.AddState(Arrive_Monster, new TransferState_Arrive_Monster());
        graph.AddTransition(idle, arrive_monster, new CheckTrigger<TransferStateMachine>(Arrive_To_Monster));
        graph.AddTransition(arrive_monster, idle, new CheckTrigger<TransferStateMachine>(To_Idle));

        State<TransferStateMachine> arrive_flyOut = graph.AddState(Arrive_FlyOut, new TransferState_Arrive_FlyOut());
        State<TransferStateMachine> arrive_transfer = graph.AddState(Arrive_Transfer, new TransferState_Arrive_Transfer());
        State<TransferStateMachine> arrive_flyIn = graph.AddState(Arrive_FlyIn, new TransferState_Arrive_FlyIn());

        graph.AddTransition(idle, arrive_flyOut, new CheckTrigger<TransferStateMachine>(Arrive_Start));                     // 小队长飞出
        graph.AddTransition(arrive_flyOut, arrive_transfer, new CheckTrigger<TransferStateMachine>(To_Arrive_Transfer));    // 黑屏
        graph.AddTransition(arrive_transfer, arrive_flyIn, new CheckTrigger<TransferStateMachine>(To_Arrive_FlyIn));        // 小队长飞进客场
        graph.AddTransition(arrive_flyIn, idle, new CheckTrigger<TransferStateMachine>(To_Idle));                           // 回到idle状态
        #endregion

        #region 回城 - 抵达
        State<TransferStateMachine> back_flyOut = graph.AddState(Back_FlyOut, new TransferState_Back_FlyOut());
        State<TransferStateMachine> back_transfer = graph.AddState(Back_Transfer, new TransferState_Back_Transfer());
        State<TransferStateMachine> back_flyIn = graph.AddState(Back_FlyIn, new TransferState_Back_FlyIn());

        graph.AddTransition(idle, back_flyOut, new CheckTrigger<TransferStateMachine>(Back_Start));                 // 小队长飞到传送点
        graph.AddTransition(back_flyOut, back_transfer, new CheckTrigger<TransferStateMachine>(To_Back_Transfer));  // 黑屏
        graph.AddTransition(back_transfer, back_flyIn, new CheckTrigger<TransferStateMachine>(Turn_Start));         // 小队长从传送点飞到自己的位置
        graph.AddTransition(back_flyIn, idle, new CheckTrigger<TransferStateMachine>(To_Idle));
        #endregion

        #region 启程 - 轮抽

        // 前往轮抽
        State<TransferStateMachine> arrive_to_select_round = graph.AddState(SelectRound_FlyOut, new TransferState_SelectRound_FlyOut());
        // 轮抽转场黑屏
        State<TransferStateMachine> select_round_transfer_enter = graph.AddState(SelectRound_Transfer, new TransferState_SelectRound_Transfer_Enter());
        // 轮抽场景英雄跳入
        State<TransferStateMachine> select_round_leader_arrive = graph.AddState(SelectRound_PreStartState, new TransferState_SelectRound_LeaderArrive());
        // 轮抽阶段
        //State<TransferStateMachine> select_round = graph.AddState(SelectRound, new TransferState_Arrive_SelectRound());
        // 轮抽场景 结束英雄跳出
        State<TransferStateMachine> select_round_leader_back = graph.AddState(SelectRound_PreEndState, new TransferState_SelectRound_LeaderBack());
        // 出轮抽黑屏
        State<TransferStateMachine> back_select_round_transfer_exit = graph.AddState(SelectRound_BackTransfer, new TransferState_SelectRound_Transfer_Exit());
        // 轮抽回来
        State<TransferStateMachine> back_select_round_flyIn = graph.AddState(SelectRound_FlyIn, new TransferState_SelectRound_FlyIn());

        graph.AddTransition(idle, arrive_to_select_round, new CheckTrigger<TransferStateMachine>(To_SelectRound));                              // 进入轮抽 （外界通知）
        graph.AddTransition(arrive_to_select_round, select_round_transfer_enter, new CheckTrigger<TransferStateMachine>(SelectRound_Transfer));       // 黑屏
        graph.AddTransition(select_round_transfer_enter, select_round_leader_arrive, new CheckTrigger<TransferStateMachine>(SelectRound_PreStart));   // 所有小队长跳出 （内部状态判断）
        graph.AddTransition(select_round_leader_arrive, idle, new CheckTrigger<TransferStateMachine>(SelectRound_Start));               // 轮抽开始

        graph.AddTransition(idle, select_round_leader_back, new CheckTrigger<TransferStateMachine>(SelectRound_PreEnd));                // 结束 小队长跳走
        graph.AddTransition(select_round_leader_back, back_select_round_transfer_exit, new CheckTrigger<TransferStateMachine>(SelectRound_End));          // 轮抽结束
        graph.AddTransition(back_select_round_transfer_exit, back_select_round_flyIn, new CheckTrigger<TransferStateMachine>(Turn_Start));                // 英雄飞入 （内部状态判断）
        graph.AddTransition(back_select_round_flyIn, idle, new CheckTrigger<TransferStateMachine>(To_Idle));

        graph.AddTransition(idle, select_round_leader_arrive, new CheckTrigger<TransferStateMachine>(SelectRound_PreStart));    // 断线重连回来 如果是轮抽阶段 就走这里进流程
                                                                                                                                //graph.AddTransition(idle, select_round, new CheckTrigger<TransferStateMachine>(SelectRound_Start));                     // 开始战斗的时候 直接进入轮抽

        #endregion

        InitWithGraph(graph);
        Start(); // 启动状态机
    }

    public void ClearAllTrigger()
    {
        ResetTrigger(Arrive_Start);
        ResetTrigger(To_Arrive_Transfer);
        ResetTrigger(To_Arrive_FlyIn);
        ResetTrigger(To_Idle);
        ResetTrigger(Back_Start);
        ResetTrigger(To_Back_Transfer);
        ResetTrigger(Turn_Start);

        ResetTrigger(To_SelectRound);
        ResetTrigger(SelectRound_Transfer);
        ResetTrigger(SelectRound_PreStart);
        ResetTrigger(SelectRound_Start);
        ResetTrigger(SelectRound_PreEnd);
        ResetTrigger(SelectRound_End);
    }

    public void OnObserverChanged(int playerId)
    {
        var state = currentState.listener as TransferStateBase;
        if (state != null)
        {
            state.SendEvent(this, currentState, "OnObserverChanged", SoGame.World.fightContext.director.realtimeSinceStartup - state.enter_time, playerId);
        }
    }

    // 断线重连的时候 记得记录还原一下状态...

    public void OnRspReplayData(RegainData data)
    {
        
    }

    public void OnReqReplayData(ref RegainData data)
    {

    }
}