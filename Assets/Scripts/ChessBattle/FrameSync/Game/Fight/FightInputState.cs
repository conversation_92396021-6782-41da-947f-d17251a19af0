///////////////////////////////////////////////////////////////////////////////
/// created by : taodeng
/// time : 2017-6-3 22:38
/// version: 1.0
///////////////////////////////////////////////////////////////////////////////


namespace GloryLockStep.Fight
{
    public class FightInputState : LinkListItemBase
    {
        public bool isInited { get; private set; } = false;
        bool[] mHasInput;

        FightPlayerInput[] mPlayerInputs;

        public int playerCount { get; set; }

        public void InitWithPlayerCount(int maxPlayerCount)
        {
            this.isInited = true;
            playerCount = maxPlayerCount;

            if (playerCount > 0)
            {
                mPlayerInputs = new FightPlayerInput[playerCount];
                mHasInput = new bool[playerCount];

                for (int i = 0; i < playerCount; ++i)
                {
                    mPlayerInputs[i] = new FightPlayerInput();
                    mHasInput[i] = false;
                }
            }
        }

        public void CopyTo(FightInputState other)
        {
            for (int i = 0; i < playerCount; ++i)
            {
                mPlayerInputs[i].CopyTo(other.mPlayerInputs[i]);
            }
        }

        public override void Reset()
        {
            if (mPlayerInputs is null)
            {
                return;
            }

            for (int i = 0; i < playerCount; ++i)
            {
                mPlayerInputs[i].Reset();
                mHasInput[i] = false;
            }
        }

        public FightPlayerInput this[int index]
        {
            get { return mPlayerInputs[index]; }
        }

        public bool HasChange(int index)
        {
            return mHasInput[index];
        }

        public void SetHasChange(int index, bool has)
        {
            mHasInput[index] = has;
        }
    }
}