///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////


/// created by : taodeng
/// time : 2017-5-11 16:13
/// version: 1.0

using GameFramework.FMath;
using GloryLockStep.Room;
using Lucifer.ActCore;
using UnityEngine;

namespace GloryLockStep.Fight
{
    public class FightRule
    {
	    private FightContext fightContext;
        public FightRule(FightContext fightContext)
        {
	        this.fightContext = fightContext;
	        timeScale = ActionCoreConfig.FPS_Rate;
        }
		bool IsComplete { get; set; }
		
		public bool IsFillFrame { get; set; }

        public Fix64 timeScale { get; set; }

#if ACGGAME_CLIENT

	    public void StartFight()
	    {

	    }

#endif

        public void FinishFight()
	    {
            if (IsComplete)
            {
                return;
            }
            IsComplete = true;
#if ACGGAME_CLIENT
            Time.timeScale = 1;
#endif
        }

		bool mStartStopTime;

		public int mStopTimeOnEnd = 60 * 1000 * 300;

#if ACGGAME_CLIENT
		public bool CanStopFighting()
		{
			if (!IsComplete && !mStartStopTime)
			{
				mStartStopTime = true;
			}
			if (mStartStopTime)
			{
				mStopTimeOnEnd -= fightContext.director.deltaTime;
                if (mStopTimeOnEnd < 0)
				{
					return true;
				}
			}
			return false;
		}  
#endif

    }
}
