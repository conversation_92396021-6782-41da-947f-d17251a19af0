using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using GloryLockStep.Room;
using Lucifer.ActCore;
using TKFrame;
using UnityEngine;
using ZGameChess;

/// <summary>
/// 房间数据准备适配器;(注：这里的代码应该只有logic相关，不能有显示对象的操作)
/// 该适配器用于将房间准备的逻辑操作和UI进行分离开，使得服务器部分代码可以直接使用。
/// (目前是从ChessBattleStage中分离出来的);
/// 场景使用哪个适配器，由BattleReadyManager统一管理;
/// created:xfilsonpan
/// date:2020-3-30
/// </summary>
public class ChessBattleReadyAdapter : IWorldObject, IResetable
{
    public MicroObject World
    {
        get;
        set;
    }
    protected RoomControl roomControl { get; set; }

    public bool IsProcessCompleted { get; private set; } = false;

    public ChessBattleReadyAdapter()
    {
    }

    public ChessBattleReadyAdapter(MicroObject microObject)
    {
#if ACGGAME_AUTOFIGHT
        Diagnostic.Log("##### set world:"+microObject==null?"null":"1111");
#endif
        this.SetData(microObject);
    }

    /// <summary>
    /// 设置必须的数据;
    /// </summary>
    /// <param name="microObject"></param>
    public void SetData(MicroObject microObject)
    {
        if (microObject == null)
        {
#if ACGGAME_AUTOFIGHT
            string str = "microObject is null." + new StackTrace().ToString();
            QQGameSystem.GetInstance().PushError2Wechat(str);
            Process.GetCurrentProcess().Kill();
#endif


        }

        this.World = microObject;
        this.RemoveListener();
        this.AddListener();
    }


    /// <summary>
    /// 开始处理了;
    /// </summary>
    /// <returns></returns>
    protected IEnumerator StartProcess()
    {
        this.OnProcessStart();

        LockStepCoroutineAdapter.Create(this.World);

        //预加载小小英雄状态机配置
        yield return LoadStateMachineConfig();

        //预加载地图碰撞数据(服务器版本)
        yield return LoadAllSDFMap();

        //预加载小小英雄配置\攻击特效配置
        yield return LoadLogicCfg();

#if ACGGAME_CLIENT
        //移动到ChessBattleStage buildUI加载 相当于提前一些
        //加载模板选择的节点编辑数据;
        //yield return InitMicroCfg();

        //yield return InitTargetSelectAtomCfg();

        //yield return BuffDataCfg.ClientLoad();
#endif
#if !ACGGAME_CLIENT
        //初始化游戏逻辑; (包括微内核)
        yield return InitGameLogic();
#endif
        //处理结束;
        this.OnProcessEnd();
    }
    /**
    * 检查开始处理;
    */
    protected void OnCheckToStartProcess()
    {
        //step1: 微内核对象是否创建了;
        if (this.World == null) throw new Exception("World has not set or is null");
        //step2: roomControl是否创建了;
        if (this.roomControl == null) throw new Exception("roomControl has not set");
        //检测通过-开始处理;
        IEnumerator startProcessIEnumerator = this.StartProcess();
#if ACGGAME_CLIENT
        CoroutineDelegate.instance.StartCoroutine(startProcessIEnumerator);
#else
        this.DoIEnumerator(startProcessIEnumerator);
#endif
    }

    /// <summary>
    /// 为了服务器端也能够调用到携程，而不用改变之前的编写方式，用这个接口来递归调用;
    /// </summary>
    /// <param name="enumerator"></param>
    private void DoIEnumerator(IEnumerator enumerator)
    {
        while (enumerator.MoveNext())
        {
            if (enumerator.Current != null)
            {
                IEnumerator tmpEnumerator = enumerator.Current as IEnumerator;
                if (tmpEnumerator != null)
                {
                    this.DoIEnumerator(tmpEnumerator);
                }
            }
        }
    }

    /// <summary>
    /// 处理的最后阶段;
    /// (只当特殊场景处理特殊需求时采用，通用的操作要放在StartProcess里添加)
    /// </summary>
    protected virtual void OnProcessEnd()
    {
        this.IsProcessCompleted = true;
    }

    /// <summary>
    /// 处理的开始阶段;
    /// (只当特殊场景处理特殊需求时采用，通用的操作要放在StartProcess里添加)
    /// </summary>
    protected virtual void OnProcessStart()
    {
        this.IsProcessCompleted = false;
    }

    /// <summary>
    /// 添加监听器;
    /// (注意: 因为这里可能由服务器开多线程跑，而传事件没有加上唯一标识的，所以服务器的准备适配器里，直接调用设置数据接口即可)
    /// </summary>
    protected void AddListener()
    {
        //进入房间前-进去前要加载相关数据加载（这里是纯逻辑，只做逻辑数据处理即可）;
        ACGEventManager.Instance.AddEventListener(EventType_Battle.LogicRequestLoadingDataForFighting, this.OnLogicRequestLoadingDataForFighting);
        //进入房间-战斗;
        ACGEventManager.Instance.AddEventListener(EventType_Battle.GameState_Room_Fighting, this.OnGameStateRoomFighting);
    }

    protected void RemoveListener()
    {
        ACGEventManager.Instance.RemoveEventListener(EventType_Battle.LogicRequestLoadingDataForFighting, this.OnLogicRequestLoadingDataForFighting);
        ACGEventManager.Instance.RemoveEventListener(EventType_Battle.GameState_Room_Fighting, this.OnGameStateRoomFighting);
    }

    /// <summary>
    /// 进入GameState_Room 加载，这里开始处理逻辑层的东西;
    /// </summary>
    /// <param name="e"></param>
    private void OnLogicRequestLoadingDataForFighting(GEvent e)
    {
        this.OnCheckToStartProcess();
    }

    /// <summary>
    /// 设置房间进入参数;
    /// </summary>
    /// <param name="roomEnterParameters"></param>
    public void SetRoomEnterParameters(RoomEnterParameters roomEnterParameters)
    {
#if ACGGAME_CLIENT
        this.roomControl = MicroMgr.Instance.GetMicroObj().fightContext.roomControl;
#endif
    }

    /// <summary>
    /// 准备数据完毕，可以开战了;
    /// </summary>
    /// <param name="e"></param>
    private void OnGameStateRoomFighting(GEvent e)
    {
        OnStartFighting();
    }

    protected void OnStartFighting()
    {
        MicroMgr.Instance.GetMicroObj().fightContext.director.Start();
        this.StartChessTurnManager();
        this.StartFightContextRule();
    }

    protected virtual void StartChessTurnManager()
    {
#if ACGGAME_CLIENT
        MicroMgr.Instance.GetMicroObj().m_ChessTurnManager.StartGame();
#endif
    }

    protected virtual void StartFightContextRule()
    {
#if ACGGAME_CLIENT
        MicroMgr.Instance.GetMicroObj().fightContext.rule.StartFight();
#endif
    }

    /// <summary>
    /// 重置数据;
    /// </summary>
    public void Reset()
    {
#if ACGGAME_CLIENT
        if (CoroutineDelegate.instance != null)
        {
            CoroutineDelegate.instance.StopCoroutine(StartProcess());
        }
#endif
        this.World = null;
        this.roomControl = null;
        this.IsProcessCompleted = false;
        this.RemoveListener();
    }

    /// <summary>
    /// 销毁;
    /// </summary>
    public void Dispose()
    {
        this.Reset();
        this.RemoveListener();
    }

    //战场和实际战斗，以及服务器时的不一样;
    protected virtual void InitNetFrame()
    {
        GloryLockStep.LockStepControl.InitSceneInitLogic(World);
        GloryLockStep.LockStepControl.InitNetFrameSyncMode(World); //todo server模式下不需要;
        // 需要在一个全局地方创建
        GloryLockStep.LockStepControl.InitNetFrame(World);
    }

    protected IEnumerator LoadStateMachineConfig()
    {
        if (MovementStateMachine.useConfigMode)
        {
            if (!MovementStateMachine.s_cacheNodeDic.ContainsKey(MovementStateMachine.commonConfingName))
            {
                byte[] buff = null;

#if ACGGAME_CLIENT
                //加载AIList配置
                var loadAsset = ResourceUtil.LoadAsset<TextAsset>("art_tft_raw/cfg/team_leader_state_machine_cfg", MovementStateMachine.commonConfingName, null);
                yield return new WaitUntil(() => loadAsset.IsLoaded);

                buff = loadAsset.GetAsset<TextAsset>().bytes;
#else
                //逻辑库加载配置
                //var prefix = System.AppDomain.CurrentDomain.BaseDirectory;
                var prefix = Environment.CurrentDirectory;
                var path = prefix + "/Art_TFT_Raw/cfg/team_leader_state_machine_cfg/" + MovementStateMachine.commonConfingName;
                buff = FileManager.Instance.FileOpen(path, ".bytes");
#endif

                using (var ms = new MemoryStream(buff))
                {
                    var br = new BinaryReader(ms);
                    var nodeList = TransitionNode.Decode(br);
                    MovementStateMachine.s_cacheNodeDic.Add(MovementStateMachine.commonConfingName, nodeList);

                    br.Close();
                    ms.Close();
                }
            }
        }

        yield return null;
    }

    protected IEnumerator LoadLogicCfg()
    {
#if !ACGGAME_CLIENT
        World.m_logicCfgCache.LoadAllCfgForServer();
        yield return null;
#else
        yield return World.m_logicCfgCache.LoadAllCfgForClient(true);
#endif
    }

    // 服务器版在这里加载所有的地图碰撞数据
    protected IEnumerator LoadAllSDFMap()
    {
#if !ACGGAME_CLIENT
        World.m_sdfCache.Initialize((int)World.CSoGame.SetID, (int) World.CSoGame.SceneId, World.CSoGame.SetCfgID);        
        World.m_sdfCache.PreInitAllMap();
        yield return null;
#else
        yield return World.m_sdfCache.Preload();
#endif
    }

    //TODO 这里可以拆的再细化一些;
    public IEnumerator InitGameLogic()
    {
#if ACGGAME_CLIENT
        yield return GloryLockStep.LockStepControl.InitConfig(World); //todo server模式下不需要;
#endif
        InitNetFrame();
#if ACGGAME_CLIENT
        int count = ChessModelManager.Instance.GetBattleModel().GetPlayerModelCount();

        LogicToUIViewCommandManager manager = LogicToUIViewCommandManager.Instance;

        // 提前创建
        List<BaseChessUICommand> list = new List<BaseChessUICommand>();
        for (int i = 0; i < count; i++)
        {
            list.Add(manager.Alloc<UICommandGameStart>());
            list.Add(manager.Alloc<UICommandOtherMaxHeroNum>());
            list.Add(manager.Alloc<UICommandOtherMaxHeroNum>());
            list.Add(manager.Alloc<UICommandOtherLife>());
            list.Add(manager.Alloc<UICommandOtherMoney>());
            list.Add(manager.Alloc<UICommandStartDraftTurn>());
            list.Add(manager.Alloc<UICommandTurnStart>());
        }

        for (int i = 0; i < list.Count; ++i)
        {
            manager.Free(list[i]);
        }
        yield return null;
#endif
        GEvent pmEvent = new GEvent();
        //pmEvent.objData = CreateUserInfoDic();
        World.m_ChessTurnManager.InitGame(pmEvent);
        yield return null;
    }  
}