using GloryLockStep.Room;
using Lucifer.ActCore;
using ZGameChess;
using ZGameClient;

/// <summary>
/// 房间数据准备管理器（如果场景采用注册的方式重构了的话，这个类就不需要了，直接注册对应的类型即可）
/// 用于为当前战斗选择合适的准备数据适配器;
/// created:xfilsonpan
/// date:2020-3-30
/// </summary>
public class BattleReadyManager : IWorldObject, IResetable
{
    public MicroObject World
    {
        get;
        set;
    }

    /// <summary>
    /// 当前适配器;
    /// </summary>
    public ChessBattleReadyAdapter readyAdapter { get; private set; } = null;

    /// <summary>
    /// 是否处理完毕了;
    /// </summary>
    public bool IsProcessCompleted {
        get
        {
            if (this.readyAdapter != null)
            {
                return this.readyAdapter.IsProcessCompleted;
            }

            return false;
        }
    }

    public BattleReadyManager(MicroObject world)
    {
        this.World = world;
        this.AddListener();
    }

    private void AddListener()
    {
        //进入房间-开始进去;
        ACGEventManager.Instance.AddEventListener(EventType_Battle.GameState_Room_Enter, this.OnGameStateRoomEnter);
    }

    private void RemoveListener()
    {
        ACGEventManager.Instance.RemoveEventListener(EventType_Battle.GameState_Room_Enter, this.OnGameStateRoomEnter);
    }
    
    /// <summary>
    /// GameState_Room 进入
    /// </summary>
    /// <param name="e"></param>
    private void OnGameStateRoomEnter(GEvent e)
    {
        //获取对应的适配器;
        this.MakeSuitableAdapter();
        //使用新的适配器执行进入房间的逻辑操作;
        RoomEnterParameters roomEnterParameters = e.objData as RoomEnterParameters;
        this.readyAdapter.SetRoomEnterParameters(roomEnterParameters);
    }


    /// <summary>
    /// 选择出一个合适的适配器;
    /// </summary>
    private void MakeSuitableAdapter()
    {
        ChessBattleReadyAdapter selectReadyAdapter = new ChessBattleReadyAdapter(this.World);

        if (this.readyAdapter != null)
        {
            this.readyAdapter.Dispose();
            this.readyAdapter = null;
        }
        this.readyAdapter = selectReadyAdapter;
    }

    public void Dispose()
    {
        if (this.readyAdapter != null)
        {
            this.readyAdapter.Dispose();
            this.readyAdapter = null;
        }
        this.RemoveListener();

#if ACGGAME_CLIENT
        World = null;
#endif
    }

    public void Reset()
    {
        if (this.readyAdapter != null)
        {
            this.readyAdapter.Reset();
        }
    }
}