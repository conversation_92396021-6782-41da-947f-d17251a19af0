using System;
using System.Collections.Generic;
using System.Linq;
using TKFrame;

[System.Serializable]
public class VOOperationData
{
    public byte OperationType;
    private byte[] m_sourceDatas = new byte[30];
    private int m_sourceDataLen = 0;
    
    public long PlayerUin;
    public int PlayerChairId;
    public byte PrePerformanceOperationSeq;

    public int InputFrameSeq;
    
    public int dataLen;

    public void Reset()
    {
        OperationType = 0;
        m_sourceDataLen = 0;
        
        PlayerUin = 0;
        PlayerChairId = 0;
        PrePerformanceOperationSeq = 0;
        InputFrameSeq = 0;
        dataLen = 0;
    }

    public void PutBytes(byte[] bytes, int len)
    {
	    if (bytes != null)
			Array.Copy(bytes, m_sourceDatas, len);
	    m_sourceDataLen = len;
    }

    public void PutListByte(List<byte> bytes)
    {
	    if (bytes != null)
	    {
		    bytes.CopyTo(m_sourceDatas);
		    m_sourceDataLen = bytes.Count;
	    }
    }

    public byte[] GetSourceDatas()
    {
	    return m_sourceDatas;
    }
    public int GetSourceDatasLen()
    {
	    return m_sourceDataLen;
    }

    public byte this[int index]
    {
        get
        {
            if (index < m_sourceDataLen)
                return m_sourceDatas[index];
            Diagnostic.Error("Out Of Index index:" + index + " len: " + m_sourceDatas.Length);
            return 0;
        }
    }

    byte[] bytes = new byte[34];
	public byte[] ToBytes()
 	{
        bytes[0] = OperationType;
        if (m_sourceDataLen > 0)
        {
	        Array.Copy(m_sourceDatas, 0, bytes, 1, m_sourceDataLen);
	        dataLen = m_sourceDataLen + 1;
        }
        else
        {
	        dataLen = 1;
        }
        return bytes;
 	}

	public bool Equal(VOOperationData data)
	{
	
		if (OperationType != data.OperationType)
		{
			return false;
		}
		
		if (m_sourceDataLen == data.GetSourceDatasLen())
		{
			for (int i = 0; i < m_sourceDataLen; i++)
			{
				if (m_sourceDatas[i] != data.GetSourceDatas()[i])
				{
					return false;
				}
			}
			return true;
		}
		return false;
	}
}
