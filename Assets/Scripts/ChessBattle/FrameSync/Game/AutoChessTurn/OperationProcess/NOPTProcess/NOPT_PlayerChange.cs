#if ACGGAME_CLIENT 

using System;
using ZGameClient;

public class NOPT_PlayerChange : IOperationProcess
{
    public void Dispose()
    {
    }

    public void Execute(VOOperationData data)
    {
    }
    public void ProcessMicroCoreMsg(Type msgType, Wup.Jce.JceStruct msgData)
    {
        UICommandPlayerChange commandPlayerChange = LogicToUIViewCommandManager.Instance.Alloc<UICommandPlayerChange>();
        commandPlayerChange.CommandId = UICommandId.NotifyPlayerChange;
        commandPlayerChange.playerChange = msgData as ACG_TCmdS2CNotifyPlayerChange;
        LogicToUIViewCommandManager.Instance.Send(commandPlayerChange);
    }
}

#endif
