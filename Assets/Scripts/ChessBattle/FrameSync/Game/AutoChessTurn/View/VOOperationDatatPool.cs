using System.Collections;
using System.Collections.Generic;
using GameFramework.Common;
using UnityEngine;

public class VOOperationDatatPool : ObjectPool<VOOperationData>,
    ObjectPool<VOOperationData>.IConstructImpl
{
    public VOOperationDatatPool()
    {
        constructImpl = this;
    }

    public VOOperationData ConstructObject()
    {
        VOOperationData fis = new VOOperationData();
        fis.Reset();
        return fis;
    }
    
    
    

    public void ResetObject(VOOperationData obj)
    {
        if (null!=obj)
        {
            obj.Reset();
        }
    }
}

