///////////////////////////////////////////////////////////////////////////////
/// author : taodeng
/// time :   2017-5-8 14:15
/// version: 1.0
///////////////////////////////////////////////////////////////////////////////

using System;
using System.IO;
using System.Runtime.Serialization;

namespace GloryLockStep
{
    [Serializable]
    public class PlayerInfo: ISerializable
    {
        /// <summary>
        ///  自定义序列化类
        /// </summary>
        /// <param name="info">Info.</param>
        /// <param name="context">Context.</param>
        public void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            info.AddValue("version", 1);
            //info.AddValue("name", name);
            info.AddValue("playerUin", playerUin);
            info.AddValue("lockStepPlayerId", lockStepPlayerId);
        }

        /// <summary>
        /// 自定义反序列化类
        /// </summary>
        /// <param name="info">Info.</param>
        /// <param name="context">Context.</param>
        public PlayerInfo(SerializationInfo info, StreamingContext context)
        {
            //name = info.GetString("name");
            playerUin = info.GetInt64("playerUin");
            lockStepPlayerId = info.GetInt64("lockStepPlayerId");
            //roleId = info.GetUInt64("roleId");
            //isRoomMaster = info.GetBoolean("isRoomMaster");
            MemoryStream stream = info.GetValue("roomInfo", typeof(MemoryStream)) as MemoryStream;
            stream.Position = 0;
            IFormatter formatter = context.Context as IFormatter;
        }

        /// <summary>
        /// default constructor
        /// </summary>
        public PlayerInfo()
        {
        }

        //public string name { get; set; }
        //public long heroId { get; set; }
        public long playerUin { get; set; }
        public long lockStepPlayerId { get; set; }
    }
}