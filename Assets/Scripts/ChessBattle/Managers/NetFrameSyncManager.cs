using System;
using ACG.Core;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using GloryLockStep;
using GloryLockStep.Fight;
using Lucifer.ActCore;
using GloryLockStep.Room;
using ZGameLog;
using ZGame;

//TODO 去单例化 去静态化
public class NetFrameSyncManager : BaseWorldManager
{

    // public Dictionary<ulong, VOOperationData> CurrentFrameOptData = new Dictionary<ulong, VOOperationData>();

    public Dictionary<long, List<VOOperationData>> CurrentFrameOptData = new Dictionary<long, List<VOOperationData>>();


    public static void Init(MicroObject World)
    {
        if (World.NetFrameSyncMgr == null)
        {
            World.NetFrameSyncMgr = new NetFrameSyncManager(World);
        }

        World.NetFrameSyncMgr.Init();
    }

    public void Clear()
    {
        Dispose();
    }

    public void Reset()
    {
        Dispose();
    }

    public NetFrameSyncManager(MicroObject World)
    {
        this.World = World;
    }

    public void Init()
    {
        World.DriverMgr.netRunner.DriveMethod(UpdateFrame, RunPriority.FRAME_INPUT);
        World.DriverMgr.netRunner.DriveMethod(Run, RunPriority.TURN_TICK);
    }

    public void NetMode()
    {
#if ACGGAME_CLIENT
        World.fightContext.director.netAdapter = new NetDriverAdapter(World);
#else
        World.fightContext.director.netAdapter = new ServerDriverAdapter(World);
#endif
    }

    public void Run()
    {
        World.m_ChessTurnManager.FrameTimeTick();
    }

    /// <summary>
    /// mCurrInput数据复制和转换到OperationData
    /// 输入 转操作 
    /// </summary>
    public void UpdateFrame()
    {
#if LOGIC_THREAD
        if (World.fightContext.input.m_LogicThreadFightInputCount <= 0)
            return;
#else
        if (!World.fightContext.input.IsContextChange())
            return;
#endif
#if LOGIC_THREAD
        FightInputState inputData = World.fightContext.input.GetLogicThreadFightInput(World.fightContext.director.frameCount);
        if (inputData == null)
        {
            //TKFrame.Diagnostic.Error("NetFrameSyncMng inputData is null");
            return;
        }
        if (inputData.m_FrameIdForLogicThread != World.fightContext.director.frameCount - 1)
        {
            //TKFrame.Diagnostic.Error("NetFrameSyncMng inputData FrameID:" + inputData.m_FrameIdForLogicThread + " CurFrame:" + World.fightContext.director.frameCount);
            return;
        }
#else
        FightInputState inputData = World.fightContext.input.mCurrInput;
#endif
        if (World.roomControl == null || World.roomControl.participants == null)
        {
            return;
        }
        UpdateFrame2(World.roomControl.participants.ParticipantieList, inputData);
        UpdateFrame2(World.roomControl.participants.JudgeParticipantieList, inputData);
    }

    private void UpdateFrame2(LinkedHashMap<int, Participant> participants, FightInputState inputData)
    {
        foreach (var pi in participants.Values)
        {
            if (pi != null)
            {
                List<VOOperationData> voOperationDatas;
                if (!CurrentFrameOptData.ContainsKey(pi.participantUin))
                {
                    voOperationDatas = new List<VOOperationData>(4);
                    CurrentFrameOptData[pi.participantUin] = voOperationDatas;
                }

                voOperationDatas = CurrentFrameOptData[pi.participantUin];


                FightInput.CovertToKeyStateData(inputData[pi.participantChariId], pi.participantUin, pi.participantChariId, voOperationDatas);
                // CurrentFrameOptData[(ulong)pi.participantUin] = voOperationData;
                //debug for local message received 测试，后期干掉！
#if ACGGAME_CLIENT

                foreach (var voOperationData in voOperationDatas)
                {
                    if (World.roomControl.playerInfo.playerUin == voOperationData.PlayerUin)
                    {
                        LockStepLog.ReceiveLog(LockStepType.ReceiveMy, LogChannel.LockStep, (byte) voOperationData.OperationType, voOperationData.PlayerUin,
                            voOperationData.PlayerChairId, voOperationData.PlayerChairId);
                    }
                    else
                    {
                        if (voOperationData.OperationType != 0)
                            LockStepLog.ReceiveLog(LockStepType.ReceiveOther, LogChannel.LockStep, (byte) voOperationData.OperationType,
                                voOperationData.PlayerUin, voOperationData.PlayerChairId, voOperationData.PlayerChairId);
                    }
                }
#endif
            }
        }
    }

    public void Dispose()
    {
        World.DriverMgr.netRunner.RemoveDriveMethod(UpdateFrame);
        World.DriverMgr.netRunner.RemoveDriveMethod(Run);
    }
}