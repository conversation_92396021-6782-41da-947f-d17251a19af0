#if ACGGAME_CLIENT
using UnityEngine;

/// <summary>
/// 艺术镜头同步3dMax处理版;
/// created: xfilsonpan
/// date:2020-6-17
/// </summary>
public class ArtCameraSync3dMax : ArtCameraSyncBase
{
    private float filmWidth = 36.0f;
    private float filmHeight = 24.0f;
    private Transform oprCameraTrans = null;
    private Transform locFovTransNodeTrans = null;

    protected override void ReallySyncData()
    {
        base.ReallySyncData();
        if (this.oprCameraTrans == null)
        {
            this.oprCameraTrans = this.oprCamera.transform;
        }

        if (this.locFovTransNodeTrans == null)
        {
            this.locFovTransNodeTrans = this.locFovTransNode.transform;
        }

        this.oprCameraTrans.position = this.locPosTransNode.position;
        this.oprCameraTrans.rotation = this.locPosTransNode.rotation;
        float Focal_Length = this.locFovTransNodeTrans.localScale.x;
        this.artNodeCamera.focalLength = Focal_Length;
        float hFOV_R = 2.0f * Mathf.Atan(filmWidth / (2.0f * Focal_Length));
        float vFOV_R = 2.0f * Mathf.Atan(Mathf.Tan(hFOV_R / 2.0f) / (16f / 9f));
        float vFOV_D = vFOV_R * Mathf.Rad2Deg;
        this.oprCamera.fieldOfView = vFOV_D;
    }
}
#endif