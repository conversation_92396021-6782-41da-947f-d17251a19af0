#if ACGGAME_CLIENT
using System.Collections.Generic;
using UnityEngine;
/// <summary>
/// 主界面的镜头管理配置;
/// creatd:xfilsonpan
/// date:2020-10-13
/// </summary>
[CreateAssetMenu(fileName = "lobbyCameraLookConfig", menuName = "ScriptableObjects/LobbyCameraLookConfig", order = 1)]
public class LobbyCameraLookConfig : ScriptableObject
{
    [System.Serializable]
    public class LobbyCameraLookPreset
    {
        public string Name;
        [Header("镜头相对查看点的相对位置")]
        public Vector3 relativePos;
        [Header("镜头的角度")]
        public Vector3 globalEuler;
        [Header("是否需要改变fov")]
        public bool needChangeFov;
        public float fov;
        [Header("至少需要切换的持续时间(s)")]
        public float atLeastSwitchDurationS;
        [Header("[编辑器下]测试用的焦点对象名字")]
        [HideInInspector]
        public string exampleForcusName;

        public void CopyTo(LobbyCameraLookPreset other)
        {
            other.Name = this.Name;
            other.relativePos = this.relativePos;
            other.globalEuler = this.globalEuler;
            other.needChangeFov = this.needChangeFov;
            other.fov = this.fov;
            other.atLeastSwitchDurationS = this.atLeastSwitchDurationS;
            other.exampleForcusName = this.exampleForcusName;
        }
    }
    
    // public List<ShakePreset> shakePresetList;
    private LobbyCameraLookPreset emptyLobbyCameraLookPreset = new LobbyCameraLookPreset();

    [Header("切换焦点时，每秒的移动速度(米)")]
    public float switchFocusSpeedPerSecond;
    public List<LobbyCameraLookPreset> shakeDataList;
    
    public LobbyCameraLookPreset GetValue(string keyName)
    {
        for (int i = 0; i < shakeDataList.Count; i++)
        {
            if (shakeDataList[i].Name == keyName)
            {
                return shakeDataList[i];
            }
        }
        return emptyLobbyCameraLookPreset;
    }
    
    public LobbyCameraLookPreset this[int index]
    {
        get
        {
            if (index >= this.Count)
            {
                return this.shakeDataList[this.Count - 1];
            }
            return this.shakeDataList[index];
        }
    }

    public int Count
    {
        get
        {
            return shakeDataList.Count;
        }
    }
}
#endif