using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using UnityEngine;

namespace PBRTools
{
    [Serializable]
    public class LightLodModule
    {
        [Serializable]
        public class LightLod
        {
            public Light light;
            public EDevicePower lod = EDevicePower.EDP_Low;
        }

        public bool enable = false;
        public List<LightLod> m_lightLodList = new List<LightLod>();

        public void Apply()
        {
            if (!enable)
                return;

            DeviceSettingPerformer gs = Services.GetService<DeviceSettingPerformer>();
            var lod = GameLOD.Instance.DevicePower;
            if (lod == EDevicePower.EDP_None)
                return;
            if (gs != null && gs.RecommendDevicePower == EDevicePower.EDP_Low)
                lod = EDevicePower.EDP_Low;

            Apply(lod);
        }

        public void Apply(EDevicePower lod)
        {
            for (int i = 0; i < m_lightLodList.Count; ++i)
            {
                var lightLod = m_lightLodList[i];
                if (lightLod.light != null)
                {
                    lightLod.light.gameObject.SetActive(lod >= lightLod.lod);
                }
            }
        }

        /// <summary>
        /// 低端机开启的灯光
        /// </summary>
        /// <returns></returns>
        public int GetLowLightCount()
        {
            if (!enable)
                return -1;

            int lowCount = 0;
            for (int i = 0; i < m_lightLodList.Count; ++i)
            {
                var lightLod = m_lightLodList[i];
                if (lightLod.light != null && lightLod.lod == EDevicePower.EDP_Low)
                   ++lowCount;
            }
            return lowCount;
        }

        public bool HasLightLod()
        {
            if (!enable)
                return false;

            if (m_lightLodList.Count == 0)
                return false;

            for (int i = 0; i < m_lightLodList.Count; ++i)
            {
                var lightLod = m_lightLodList[i];
                if (lightLod.light != null)
                    return true;
            }

            return false;
        }


        public void ClearEmptyLight()
        {
            for (int i = m_lightLodList.Count - 1; i >= 0; --i)
            {
                var lightLod = m_lightLodList[i];
                if (lightLod.light == null)
                {
                    m_lightLodList.RemoveAt(i);
                }
            }
        }

        public bool HasLight(Light l)
        {
            for (int i = 0; i < m_lightLodList.Count; ++i)
            {
                var lightLod = m_lightLodList[i];
                if (lightLod.light == l)
                {
                    return true;
                }
            }

            return false;
        }
    }
}
