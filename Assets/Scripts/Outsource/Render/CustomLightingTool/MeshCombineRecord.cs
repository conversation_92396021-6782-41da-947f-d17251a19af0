using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace PBRTools
{
    [Serializable]
    public class MeshCombineRecordInfo
    {
        [Serializable]
        public class MeshPair
        {
            public MeshFilter filter;
            public string meshGuid;
        }

        public MeshFilter filter;
        public List<MeshPair> combineMeshs;
    }

    public class MeshCombineRecord : MonoBehaviour
    {
        public List<MeshCombineRecordInfo> recordInfo = new List<MeshCombineRecordInfo>();

        [ContextMenu("还原")]
        public void Revert()
        {
#if UNITY_EDITOR
            foreach (var item in recordInfo)
            {
                foreach (var meshPair in item.combineMeshs)
                {
                    meshPair.filter.sharedMesh = UnityEditor.AssetDatabase.LoadAssetAtPath<Mesh>(UnityEditor.AssetDatabase.GUIDToAssetPath(meshPair.meshGuid));
                }
                if (item.filter != null)
                    GameObject.DestroyImmediate(item.filter.gameObject);
            }
#endif
        }
    }
}
