using System.Collections.Generic;
using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Rendering;


#if UNITY_EDITOR
using UnityEditor;
#endif

namespace PBRTools
{
    [ExecuteInEditMode]
    public class CustomLightingTool : MonoBehaviour
    {
        //测试功能开关
        public bool DebugMode = false;
        //专家模式开关
        public bool ExpertMode = false;


        //新增轮廓光设置
        public bool rimProEnable = false;
        
        [SerializeField]
        public RimProVlaue rimProValue = new RimProVlaue();

        [Serializable]
        public class RimProVlaue
        {
            public bool Rim1 = true;
            public bool Rim2 = true;
            public Vector3 RimDir1 = new Vector3(0.638f, 0.456f, -0.62f);
            public Vector3 RimDir2 = new Vector3(-0.754f, 0.305f, -0.580f);
            public Color RimColor1 = new Color(0, 1.24f, 2.3f, 1);
            public Color RimColor2 = new Color(3, 1.63f, 0.09f, 1);
            public Vector4 RimShape = new Vector4(0.3f, 0.75f, 0, 0);
            public Vector4 RimMaskClamp = new Vector4(0.3f, 0.75f, 0, 0);
            public bool RimAdd = false;


            public void Copy(RimProVlaue rv)
            {
                Rim1 = rv.Rim1;
                Rim2 = rv.Rim2;
                RimDir1 = rv.RimDir1;
                RimDir2 = rv.RimDir2;
                RimColor1 = rv.RimColor1;
                RimColor2 = rv.RimColor2;
                RimAdd = rv.RimAdd;
                RimShape = rv.RimShape;
                RimMaskClamp = rv.RimMaskClamp;
                
            }


            public void Rest()
            {
                Rim1 = true;
                Rim2 = true;
                RimDir1 = new Vector3(0.638f, 0.456f, -0.62f);
                RimDir2 = new Vector3(-0.754f, 0.305f, -0.580f);
                RimColor1 = new Color(0, 1.24f, 2.3f, 1);
                RimColor2 = new Color(3, 1.63f, 0.09f, 1);
                RimAdd = false;
                RimShape = new Vector4(-0.754f, 0.305f, -0.580f,0);
            }

            //
            //_RimMaskClamp("RimMaskClamp", Vector) = (0.3,0.75,0,0)
            //	

            //	
            //[LightDirScreenSpace]_RimDir1("RimVector", Vector) = (0.638,0.456,-0.62,1)
            //[HDR]_RimCol1("RimColor", Color) = (0,1.24,2.3,1)
            //_RimPower1("RimPower", Range(0.0001, 10)) = 2
            //_RimScale1("RimScale", Range(0, 2)) = 0

            //[LightDirScreenSpace]_RimDir2("RimVector", Vector) = (-0.754,0.305,-0.580,1)
            //[HDR]_RimCol2("RimColor", Color) = (3,1.63,0.09,1)
            //_RimPower2("RimPower", Range(0.0001, 10)) = 2
            //_RimScale2("RimScale", Range(0, 2)) = 0

        }


        //自定义光照列表
        public GameObject[] CustomLightObjs = new GameObject[0] { };


        //漫反射参数
        public ESHMode LightProbeMode = ESHMode.CustomProbe;
        public enum ESHMode
        {
            CustomProbe = 0,
            CubeMapSH = 1,
            AmbientProbe = 2,
            LightProbe = 3
        }

        [SerializeField]
        public SphericalHarmonicsL2 SH1;
        public Vector4[] SHV1 = new Vector4[9];
        [SerializeField]
        public SphericalHarmonicsL2 SH2;
        public Vector4[] SHV2 = new Vector4[9]; // for unity 2018 way
        [SerializeField]
        public SphericalHarmonicsL2 SH3;
        public Vector4[] SHV3 = new Vector4[9];

        public bool UseCustomRefProbe = false;
        public bool onEditorRef = false;
        public bool refreshRefProbe = false;
        public ReflectionProbe CustomReflectionProbe = null;
        public Texture CustomReflectionTexture = null;
        public RenderTexture CustomRefEditorTex = null;
        public float CustomRefProbeIntensity = 1f;
        [ColorUsage(true, true)]
        public Color AmbientColor;
        public float Instensity = 1f;
        public bool UseRealtimeSH = false;
        public bool RefreshRealtimeSH = false;


        //public bool CubeMapToSH = false;
        // public bool UseNormal = false;
        //public Cubemap Cubemap;
        //public Vector4[] ve4 = new Vector4[9];

        //public bool UseCubeMap1 = false;
        //public bool UseCubeMap2 = false;
        //public bool UseCubeMap3 = false;
        //public bool UseCubeMap4 = false;

        //[SerializeField]
        //public GameObject[] refLightList = null;



        //自定义反射cubemap
        [SerializeField]
        public RenderTexture CustomReflectionRT = null;

#if UNITY_EDITOR && !ENABLE_TYPE_TREE_IGNORE

        //Debug HDRI
   [SerializeField]
        public  GameObject sphereGO1;
   [SerializeField]
        public  GameObject sphereGO2;


        //模板管理

        public Texture2D DebugTex1 = null;

        public Cubemap CustomReflectionCubemap = null;
        public Cubemap RealtimeCubemap = null;
        //public Material mate = null;
        public Cubemap SHCubemap = null;
        public Cubemap SHRealtimeCubemap = null;
        public int UpdateRealtimeSHMipmap = 2;
        //public RenderTexture rttttt = null;


        //储存功能
        //[SerializeField]
        //static GameObject prefab = null;
        //public int SaveRenderProbeID = -1;
        //public bool inSaveProcess = false;


        //镜面反射参数
        public bool canMoveRefLight = false;
        public bool ForceBakeFormat = false;
        public bool isBakeEXR = false;

        public Material SkyboxMate = null;
        public Transform SkyboxBase = null;
        public SkyboxValue skyboxValue = new SkyboxValue();
        [System.Serializable]
        public class SkyboxValue
        {
            public bool enable = false;
            public float maxValue = 35;
            public bool maxValueOn = false;
            public Color color = Color.white;
            public float exposure = 0;
            public float rotation = 0;
            public bool disabSun = false;
            public Cubemap cubemap = null;
            public bool panoromicEnabled = false;
            public Texture2D PanoromicHDR = null;

            public void CopyFrom(SkyboxValue sb)
            {
                enable = sb.enable;
                maxValue = sb.maxValue;
                maxValueOn = sb.maxValueOn;
                color = sb.color;
                exposure = sb.exposure;
                rotation = sb.rotation;
                disabSun = sb.enable;
                cubemap = sb.cubemap;
                panoromicEnabled = sb.panoromicEnabled;
                PanoromicHDR = sb.PanoromicHDR;
            }
        }

        public Vector2 lightHandlePos = new Vector2(500, 500);
        public Transform lightParent = null;

        public int CurrentDirLightIndex = -1;
        public int CurrentRefLightIndex = -1;
        public int tab = 1; // 漫反射类型选择
        public int tabLightMode = 0;  // 编辑光照种类
                                      // public RenderTexture targetTexture = null;

        [SerializeField]
        public Texture2D[] reflectionTexs = null; //反射灯光贴图列表

        [SerializeField]
        public List<DiffuseLight> AmbientLights;
        [SerializeField]
        public List<ReflectionLight> reflectionLights;
        //public Cubemap reflectionBase;

        public Material CubemapToEquirectangularMipmapMat = null;
        public Material CubemapToEquirectangularMat = null;
        public Material CubemapToEXR = null;
        public Material CubemapToMipmapMat = null;
        public RenderTexture ReflectionTexVisual = null;
        public float ReflectionTexVisualMip = 0;

        [System.Serializable]
        public class DiffuseLight
        {
            [SerializeField]
            public bool enabled = true;
            [SerializeField]
            public Vector3 direction = new Vector3(0.5f, 0.5f, 0.5f);
            [SerializeField]
            public Quaternion rotation = Quaternion.Euler(0, 0, 0);
            [SerializeField]
            [ColorUsage(true, true)]
            public Color color = new Color(0.5f, 0.5f, 0.5f, 1f);
            [SerializeField]
            public float ColorTemperature = 6500;
            [SerializeField]
            public bool useColorTemperature = false;

            public bool useColorTemperatureList = false;
            [SerializeField]
            public float intensity = 1f;
            [SerializeField]
            public bool isEdit = false;

            public DiffuseLight()
            {
                rotation = Quaternion.identity;

                direction = Quaternion.identity * Vector3.forward;
                color = new Color(0.5f, 0.5f, 0.5f, 1f);
                intensity = 1f;
                isEdit = false;
            }
            public void CopyFrom(DiffuseLight dl)
            {
                enabled = dl.enabled;
                direction = dl.direction;
                rotation = dl.rotation;
                color = dl.color;
                ColorTemperature = dl.ColorTemperature;
                useColorTemperature = dl.useColorTemperature;
                useColorTemperatureList = dl.useColorTemperatureList;
                intensity = dl.intensity;
                isEdit = dl.isEdit;
            }
        }

        [System.Serializable]
        public class ReflectionLight
        {
            public int index = 0;

            //public enum RLightType
            //{
            //    Sphere = 0,
            //    Rect = 1,
            //    CustomTex = 2
            //}


            //public RLightType type = RLightType.Sphere;

            [SerializeField]
            public bool enabled = true;
            [SerializeField]
            public Vector3 direction = new Vector3(0.5f, 0.5f, 0.5f);
            [SerializeField]
            public Vector3 pos = new Vector3(0, 0, -5);
            [SerializeField]
            public Quaternion rotation = Quaternion.Euler(0, 0, 0);

            public Rect clickRect = Rect.zero;
            [SerializeField]
            [ColorUsage(true, true)]
            public Color color = new Color(1f, 1f, 1f, 1f);
            [SerializeField]
            public float ColorTemperature = 6500;
            [SerializeField]
            public bool useColorTemperature = false;
            [SerializeField]
            public float Exposure = 0f;
            [SerializeField]
            public bool isEdit = false;

            [SerializeField]
            public float size = 1;
            public Vector3 scale = new Vector3(1f, 1f, 1f);
            public int falloff = 1;
            public float falloffPower = 1;

            public Texture2D tex = null;


            public GameObject goParent = null;
            public GameObject go = null;
            public Material mat = null;

            //public class SphereLigt { 

            //}

            public void CopyFrom(ReflectionLight dl)
            {
                enabled = dl.enabled;
                direction = dl.direction;
                pos = dl.pos;
                rotation = dl.rotation;

                clickRect = dl.clickRect;
                color = dl.color;
                ColorTemperature = dl.ColorTemperature;
                useColorTemperature = dl.useColorTemperature;
                Exposure = dl.Exposure;

                size = dl.size;
                scale = dl.scale;
                falloff = dl.falloff;
                falloffPower = dl.falloffPower;
                tex = dl.tex;
                //public GameObject goParent = null;
                //public GameObject go = null;
                //public Material mat = null;
                isEdit = dl.isEdit;
            }

            public ReflectionLight()
            {

                //rotation = Quaternion.identity;

                direction = rotation * Vector3.forward;
                //color = new Color(0.5f, 0.5f, 0.5f, 1f);
                //intensity = 1f;
                //isEdit = false;


            }

            public GameObject CreateLight(Transform tra, Texture2D refTex)
            {

                GameObject tempParent = new GameObject("light");
                GameObject temp = GameObject.CreatePrimitive(PrimitiveType.Plane);


                tempParent.hideFlags = HideFlags.NotEditable;
                tempParent.hideFlags = HideFlags.DontSaveInEditor;
                temp.hideFlags = HideFlags.NotEditable;
                temp.hideFlags = HideFlags.DontSaveInEditor;

                GameObjectUtility.SetStaticEditorFlags(temp, StaticEditorFlags.ReflectionProbeStatic);
                temp.transform.parent = tempParent.transform;
                temp.layer = 31;
                temp.transform.localRotation = Quaternion.Euler(90, 0, 0);
                temp.transform.localPosition = pos;
                tempParent.transform.parent = tra;
                tempParent.transform.localPosition = Vector3.zero;
                tempParent.transform.localRotation = rotation;
                temp.transform.localScale = scale * size;
                mat = new Material(Shader.Find("Hidden/ReflectionLight"));
                mat.hideFlags = HideFlags.NotEditable;
                mat.hideFlags = HideFlags.DontSaveInEditor;
                //mat.SetFloat("_Mode", 3);
                mat.renderQueue = 3000;
                //mat.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
                //mat.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
                //mat.SetInt("_ZWrite", 0);
                //mat.DisableKeyword("_ALPHATEST_ON");
                //mat.EnableKeyword("_ALPHABLEND_ON");
                //mat.DisableKeyword("_ALPHAPREMULTIPLY_ON");
                //mat.EnableKeyword("_EMISSION");

                goParent = tempParent;
                go = temp;

                temp.GetComponent<MeshRenderer>().sharedMaterial = mat;


                tex = refTex;


                UpdateSetting();
                return tempParent;
            }



            public void UpdateSetting()
            {
                if (mat != null)
                {
                    if (useColorTemperature)
                    {
                        mat.SetColor("_Tint", Mathf.CorrelatedColorTemperatureToRGB(ColorTemperature));

                    }
                    else
                    {
                        mat.SetColor("_Tint", color);
                    }
                    mat.SetFloat("_Exposure", Exposure);
                    mat.SetFloat("_Falloff", falloff);
                    mat.SetFloat("_FalloffPower", falloffPower);

                    mat.SetTexture("_LightTex", tex);
                    mat.renderQueue = 3005 + index;

                }
                if (go != null)
                {

                    go.SetActive(enabled);
                    go.transform.localPosition = pos;
                    goParent.transform.localRotation = rotation;
                    go.transform.localScale = scale * size;
                }


            }

        }



        public ReflectionLight CreateRefLight()
        {
            ReflectionLight rl = new ReflectionLight();
            rl.CreateLight(lightParent, reflectionTexs[1]);

            return rl;
        }

        public void RefChangeIndex(int a, int b)
        {
            if (reflectionLights.Count + 1 > a && reflectionLights.Count + 1 > b)
            {
                ReflectionLight arl = reflectionLights[a];
                ReflectionLight brl = reflectionLights[b];
                reflectionLights[b] = arl;
                reflectionLights[a] = brl;
            }

        }

#endif

        public void SetRefTextrue()
        {
#if UNITY_EDITOR && !ENABLE_TYPE_TREE_IGNORE

            if (!Application.isPlaying)
            {
                if (CustomReflectionProbe != null)
                {
                    if (onEditorRef && CustomReflectionProbe.realtimeTexture != null)
                    {
                        //CustomReflectionTexture = CustomReflectionProbe.realtimeTexture;
                        CustomReflectionTexture = CustomReflectionProbe.bakedTexture;
                    }
                    else if (CustomReflectionProbe.bakedTexture != null)
                    {
                        CustomReflectionTexture = CustomReflectionProbe.bakedTexture;
                    }
                    CustomRefProbeIntensity = CustomReflectionProbe.intensity;

                }

            }
            else
            {

                onEditorRef = false;
                if (lightParent != null)
                {
                    DestroyImmediate(lightParent.gameObject);
                }
            }
#endif

            if (Application.isPlaying && CustomReflectionTexture != null)
            {
                if (CustomReflectionProbe != null)
                {
                    CustomReflectionProbe.mode = UnityEngine.Rendering.ReflectionProbeMode.Custom;
                    CustomReflectionProbe.customBakedTexture = CustomReflectionTexture;
                    CustomReflectionProbe.intensity = CustomRefProbeIntensity;

                }
            }
        }

        private void OnDestroy()
        {
            CloseLighting();

            if (null != CustomLightObjs)
            {
                CustomLightObjs = null;
            }

            CustomReflectionProbe = null;
            CustomRefEditorTex = null;
            CustomReflectionTexture = null;
            CustomReflectionRT = null;
        }

        private void OnEnable()
        {

#if UNITY_EDITOR && !ENABLE_TYPE_TREE_IGNORE

            //CustomLightingTool.prefab = this.gameObject;
            //Transform tra = this.transform.Find("skybox");

            lightParent = this.transform.Find("ReflectionLights");
            //if (tra != null)
            //{
            //    SkyboxMate = tra.GetComponent<MeshRenderer>().sharedMaterial;
            //    SkyboxBase = tra;
            //}
            if (Shader.Find("Skybox/HDRI_bake") == null)
            {
                Debug.LogError("Shader 丢失请联系 fengxzeng");
            }
            SkyboxMate = new Material(Shader.Find("Skybox/HDRI_bake"));

            //CubemapToEquirectangularMat = new Material(Shader.Find("Hidden/CubemapToEquirectangular1"));
            if (Shader.Find("Hidden/CubemapToEXR") == null)
            {
                Debug.LogError("Shader 丢失请联系 fengxzeng");
            }
            CubemapToEXR = new Material(Shader.Find("Hidden/CubemapToEXR"));

            CubemapToEXR.EnableKeyword("_GET_MIPMAP");
            if (Shader.Find("Hidden/CubemapToEquirectangular1") == null)
            {
                Debug.LogError("Shader 丢失请联系 fengxzeng");

            }
            CubemapToEquirectangularMipmapMat = new Material(Shader.Find("Hidden/CubemapToEquirectangular1"));



            // CubemapToMipmapMat = new Material(Shader.Find("Hidden/CustomBakeCubemapMipmap"));

            if (ReflectionTexVisual != null)
                DestroyImmediate(ReflectionTexVisual);
            ReflectionTexVisual = new RenderTexture(512, 256, 0, RenderTextureFormat.ARGB32);
            ReflectionTexVisual.name = "ReflectionTexVisual";

            if (AmbientLights == null)
            {
                AmbientLights = new List<DiffuseLight>() { };
            }
            if (reflectionLights == null)
            {
                reflectionLights = new List<ReflectionLight>() { };
            }

            lightHandlePos = new Vector2(Screen.width / 2, Screen.height / 2);
            UnityEditor.SceneManagement.EditorSceneManager.sceneSaved += OnSceneSaved;
            lightHandlePos = new Vector2(Screen.width / 2, Screen.height / 2);



            if(sphereGO1==null){

                   sphereGO1=AssetDatabase.LoadAssetAtPath<GameObject>("Assets/CustomLightingTool/Res/HDRI.prefab"); 

            }
            if(sphereGO2==null){

                   sphereGO2=AssetDatabase.LoadAssetAtPath<GameObject>("Assets/CustomLightingTool/Res/HDRISH.prefab"); 

            }
    
//         public  GameObject sphereGO1;
//    [SerializeField]
//         public  GameObject sphereGO2;



#endif

            SetRefTextrue();
            UpdateLightingInternal();
            //if (Application.isPlaying)
            //{
            //    Debug.Log("xx");

            //}
        }

        private void CloseLighting()
        {
            if (CustomReflectionProbe != null)
            {
                CustomReflectionProbe.customBakedTexture = null;
                CustomReflectionProbe.bakedTexture = null;
            }

            if (CustomLightObjs != null)
            {
                foreach (var obj in CustomLightObjs)
                {
                    if (obj != null)
                    {
                        foreach (var m in obj.GetComponentsInChildren<Renderer>(true))
                        {
                            if (m != null)
                                m.probeAnchor = null;
                        }
                    }
                }
            }
        }

        private void OnDisable()
        {
            CloseLighting();

#if UNITY_EDITOR && !ENABLE_TYPE_TREE_IGNORE
            UnityEditor.SceneManagement.EditorSceneManager.sceneSaved -= OnSceneSaved;

            if (ReflectionTexVisual != null)
            {
                DestroyImmediate(ReflectionTexVisual);
                ReflectionTexVisual = null;
            }
#endif
        }

#if UNITY_EDITOR && !ENABLE_TYPE_TREE_IGNORE
        public void DeleteOnsave()
        {

            UnityEditor.SceneManagement.EditorSceneManager.sceneSaved -= OnSceneSaved;
        }
#endif

        // 传入需要处理光照的 GameObject
        public void UpdateLighting(GameObject[] objs)
        {
            CustomLightObjs = objs;
            UpdateLightingInternal();
        }

#if UNITY_EDITOR && !ENABLE_TYPE_TREE_IGNORE

        public static bool SavePrefab()
        {
            bool suc = false;


            //PrefabUtility.ApplyPrefabInstance(prefab, InteractionMode.AutomatedAction);


            return suc;

        }

        public void CopyRimLight(CustomLightingTool cl)
        {
            rimProEnable = cl.rimProEnable;
            rimProValue.Copy(cl.rimProValue);
        }



        public void CopyLight(CustomLightingTool cl)
        {

            LightProbeMode = cl.LightProbeMode;
            SH1 = cl.SH1;
            SHV1 = cl.SHV1;
            SH2 = cl.SH2;
            SHV2 = cl.SHV2;
            SH3 = cl.SH3;
            SHV3 = cl.SHV3;

            UseCustomRefProbe = cl.UseCustomRefProbe;
            CustomRefProbeIntensity = cl.CustomRefProbeIntensity;

            AmbientColor = cl.AmbientColor;
            Instensity = cl.Instensity;
            UseRealtimeSH = cl.UseRealtimeSH;
            RefreshRealtimeSH = true;

            skyboxValue.CopyFrom(cl.skyboxValue);

            isBakeEXR = cl.isBakeEXR;
            ForceBakeFormat = cl.ForceBakeFormat;
            UseRealtimeSH = cl.UseRealtimeSH;


            CurrentDirLightIndex = cl.CurrentDirLightIndex;
            CurrentRefLightIndex = cl.CurrentRefLightIndex;

            tab = cl.tab;
            tabLightMode = cl.tabLightMode;
            ReflectionTexVisualMip = cl.ReflectionTexVisualMip;



            AmbientLights = new List<DiffuseLight>();
            reflectionLights = new List<ReflectionLight>();

            foreach (var item in cl.AmbientLights)
            {
                if (item != null)
                {
                    DiffuseLight al = new DiffuseLight();
                    al.CopyFrom(item);
                    AmbientLights.Add(al);
                }
            }
            foreach (var item in cl.reflectionLights)
            {
                if (item != null)
                {
                    ReflectionLight al = new ReflectionLight();
                    al.CopyFrom(item);
                    reflectionLights.Add(al);
                }
            }

            CopyRimLight(cl);
        }





        static void OnSceneSaved(UnityEngine.SceneManagement.Scene scene)
        {
            // bool suc = SavePrefab();

        }

#endif

#if UNITY_EDITOR && !ENABLE_TYPE_TREE_IGNORE
        void OnValidate()
        {
            UpdateLightingInternal();

            //if (inSaveProcess)
            //{
            //    ligthSetting.CustomReflectionProbe.IsFinishedRendering()
            //    Debug.Log(ligthSetting.CustomReflectionProbe.realtimeTexture);
            //    BakeRealtimeProbe();

            //    if (ligthSetting.onEditorRef && ligthSetting.RefreshRealtimeSH && ligthSetting.refreshRefProbe)
            //    {
            //        UpdateRealtimeSH();
            //        //Debug.Log("UpdateRealtimeSH Bake");
            //    }


            //    ligthSetting.SetRefTextrue();
            //    ligthSetting.onEditorRef = false;
            //    ligthSetting.CustomReflectionProbe.mode = ReflectionProbeMode.Custom;
            //    ligthSetting.CustomReflectionProbe.customBakedTexture = ligthSetting.CustomReflectionTexture;

            //    //if (ligthSetting.lightParent != null)
            //    //{
            //    //    DestroyImmediate(ligthSetting.lightParent.gameObject);

            //    //}

            //    PrefabUtility.ApplyPrefabInstance(ligthSetting.gameObject, InteractionMode.AutomatedAction);
            //    RefreshRefProbeVisualTex();
            //    ligthSetting.refreshRefProbe = true;
            //    ligthSetting.onEditorRef = false;
            //    Repaint();
            //    inSaveProcess = false;
            //}
        }
#endif



        public void UpdateLightingInternal()
        {
#if UNITY_EDITOR && !ENABLE_TYPE_TREE_IGNORE
            if (PrefabUtility.IsPartOfPrefabAsset(this))
            {
                //Debug.Log("UpdateLightingInternal False, this is asset");
                return;
            }

            if (lightParent != null)
            {
                lightParent.position = this.transform.position;

            }
            SH1 = new SphericalHarmonicsL2();
            switch (LightProbeMode)
            {
                case ESHMode.CustomProbe:
                    SH1.AddAmbientLight(AmbientColor);
                    if (AmbientLights != null || AmbientLights.Count != 0)
                    {
                        foreach (var l in AmbientLights)
                        {
                            if (l != null && l.enabled)
                            {
                                if (l.useColorTemperature)
                                {
                                    SH1.AddDirectionalLight(-l.direction, Mathf.CorrelatedColorTemperatureToRGB(l.ColorTemperature), l.intensity);
                                }
                                else
                                {
                                    SH1.AddDirectionalLight(-l.direction, l.color, l.intensity);
                                }
                            }
                        }
                    }
                    //2018 SphericalHarmonicsL2 不能序列化存储 。更换成 vector
                    for (int i = 0; i < 9; i++)
                    {
                        SHV1[i].x = SH1[0, i];
                        SHV1[i].y = SH1[1, i];
                        SHV1[i].z = SH1[2, i];
                    }
                    break;
                case ESHMode.AmbientProbe:
                    for (int i = 0; i < 9; i++)
                    {
                        SH1[0, i] = RenderSettings.ambientProbe[0, i];
                        SH1[1, i] = RenderSettings.ambientProbe[1, i];
                        SH1[2, i] = RenderSettings.ambientProbe[2, i];
                    }
                    break;
                case ESHMode.LightProbe:
                    break;
                case ESHMode.CubeMapSH:
                    SH1 = SH2;
#if UNITY_2019_1_OR_NEWER

#else
                    //2018 SphericalHarmonicsL2 不能序列化存储 。更换成 vector
                    SHV1 = SHV2;
                    for (int i = 0; i < 9; i++)
                    {
                        SH1[0, i] = SHV2[i].x;
                        SH1[1, i] = SHV2[i].y;
                        SH1[2, i] = SHV2[i].z;
                    }
#endif
                    break;
                default:
                    break;
            }
            SH1 *= Instensity;


            //Update Reflection Probe 转移到 OnInsperct GUI
            //if (CustomReflectionProbe != null && onEditorRef)
            //{
            //    if (refreshRefProbe)
            //    {

            //        //Editor[] ed = (Editor[])Resources.FindObjectsOfTypeAll<Editor>();
            //        //for (int i = 0; i < ed.Length; i++)
            //        //{
            //        //    if (ed[i].GetType() == CustomLightingToolEditor)
            //        //    {
            //        //        ed[i].Repaint();
            //        //        return;
            //        //    }
            //        //}

            //        //CustomReflectionProbe.RenderProbe();
            //        //RefreshRefProbeVisualTex();
            //        //Debug.Log("refresh refprobe!");
            //        refreshRefProbe = false;


            //        //if (UseRealtimeSH)
            //        //{

            //        //    RefreshRealtimeSH = true;
            //        //}
            //    }

            //}




            //镜面反射设置
            if (Application.isPlaying)
            {
                CustomReflectionProbe.mode = ReflectionProbeMode.Custom;
                CustomReflectionProbe.customBakedTexture = CustomReflectionTexture;
                CustomReflectionProbe.bakedTexture = CustomReflectionTexture;
                CustomReflectionProbe.intensity = CustomRefProbeIntensity;

            }else{

                CustomReflectionProbe.customBakedTexture = CustomReflectionTexture;
                CustomReflectionProbe.bakedTexture = CustomReflectionTexture;
                CustomReflectionProbe.intensity = CustomRefProbeIntensity;
            }



#endif
            if (gameObject.activeInHierarchy)
            {
                StartCoroutine(UpdateRimProProperties());
            }


            //漫反射 球谐设置

            SphericalHarmonicsL2[] shs = null;

            // light probe
            if (LightProbeMode == ESHMode.LightProbe)
            {
                //SphericalHarmonicsL2 target;
                //LightProbes.GetInterpolatedProbe(Vector3.zero, null, out target);
                if (LightmapSettings.lightProbes != null)
                {
                    shs = LightmapSettings.lightProbes.bakedProbes;

                }
            }
            else
            {
#if UNITY_2019_1_OR_NEWER

#else
                //2018 SphericalHarmonicsL2 不能序列化存储 。更换成 vector
                SH1 = new SphericalHarmonicsL2();
                for (int i = 0; i < 9; i++)
                {
                    SH1[0, i] = SHV1[i].x;
                    SH1[1, i] = SHV1[i].y;
                    SH1[2, i] = SHV2[i].z;
                }
                SH1 *= Instensity;

#endif

                shs = new SphericalHarmonicsL2[1] { SH1 };
            }

            MaterialPropertyBlock te = new MaterialPropertyBlock();
            if (CustomLightObjs != null)
            {
                foreach (var obj in CustomLightObjs)
                {
                    if (obj != null)
                    {
                        foreach (var m in obj.GetComponentsInChildren<Renderer>(true))
                        {
                            if (LightProbeMode == ESHMode.LightProbe)
                            {
                                m.lightProbeUsage = LightProbeUsage.BlendProbes;
                            }
                            else
                            {
                                m.lightProbeUsage = LightProbeUsage.CustomProvided;
                                m.GetPropertyBlock(te);
                                te.CopySHCoefficientArraysFrom(shs);
                                m.SetPropertyBlock(te);
                            }

                            if (UseCustomRefProbe && CustomReflectionProbe != null)
                            {
                                m.probeAnchor = CustomReflectionProbe.transform;
                            }
                            else
                            {
                                m.probeAnchor = null;
                            }
                        }
                    }
                }
            }
        }

        private IEnumerator UpdateRimProProperties()
        {
            yield return null;
            
            yield return new WaitForEndOfFrame();
            
            bool needUpdate = false;
            if (CustomLightObjs != null)
            {
                foreach (var obj in CustomLightObjs)
                {
                    if (obj != null)
                    {
                        var r = obj.GetComponentInChildren<Renderer>();
                        if (r != null && r.isVisible)
                        {
                            needUpdate = true;
                            break;
                        }
                    }
                }
            }

            if (needUpdate)
            {
                if (rimProEnable)
                {

                    Shader.SetGlobalColor("_RimCol1", rimProValue.RimColor1);
                    Shader.SetGlobalColor("_RimCol2", rimProValue.RimColor2);
                    Shader.SetGlobalVector("_RimDir1", rimProValue.RimDir1);
                    Shader.SetGlobalVector("_RimDir2", rimProValue.RimDir2);
                    Shader.SetGlobalVector("_RimMaskClamp", rimProValue.RimMaskClamp);
                    Shader.SetGlobalVector("_RimShape", rimProValue.RimShape);
                    Shader.SetGlobalFloat("_RimLerp", 1);


                    if (!rimProValue.Rim1)
                    {
                        Shader.SetGlobalColor("_RimCol1", Color.black);
                    }

                    if (!rimProValue.Rim2)
                    {
                        Shader.SetGlobalColor("_RimCol2", Color.black);
                    }

                    if (!rimProValue.RimAdd)
                    {
                        Shader.SetGlobalFloat("_RimLerp", 0);

                    }

                }
                else
                {
                    //Shader.DisableKeyword("_RIM_PRO");
                    Shader.SetGlobalColor("_RimCol1", Color.black);
                    Shader.SetGlobalColor("_RimCol2", Color.black);
                    Shader.SetGlobalFloat("_RimLerp", 0);

                }
            }
        }

#if UNITY_EDITOR && !ENABLE_TYPE_TREE_IGNORE
        ////// 修改属性后 刷新 reflection 预览图
        //public void RefreshRefProbeVisualTex()
        //{
        //    //Debug.Log("RefreshRefProbeVisualTex up");
        //    //RenderVisualTex(CustomReflectionProbe.realtimeTexture, ReflectionTexVisual, CustomReflectionProbe.bakedTexture, CubemapToEquirectangularMipmapMat, ReflectionTexVisualMip);

        //    RenderTexture texRealtime = CustomReflectionProbe.realtimeTexture;

        //    //if (texRealtime != null)
        //    //{
        //    RenderTexture temprt = RenderTexture.active;
        //    RenderTexture.active = ReflectionTexVisual;
        //    CubemapToEquirectangularMipmapMat.SetFloat("_LodNum", ReflectionTexVisualMip);
        //    CubemapToEquirectangularMipmapMat.DisableKeyword("_FLIP_Y_FOR_EXR");
        //    CubemapToEquirectangularMipmapMat.EnableKeyword("_GET_MIPMAP");

        //    if (onEditorRef && texRealtime != null)
        //    {
        //        Graphics.Blit(texRealtime, ReflectionTexVisual, CubemapToEquirectangularMipmapMat);

        //    }
        //    else
        //    {
        //        Graphics.Blit(CustomReflectionProbe.bakedTexture, ReflectionTexVisual, CubemapToEquirectangularMipmapMat);
        //    }
        //    RenderTexture.active = temprt;


        //    //Debug.Log("update ref probe");
        //    //}

        //}


        public Cubemap GetRealtimeCubemap()
        {
            Cubemap cp = new Cubemap(32, TextureFormat.RGBAHalf, true);
            //CustomReflectionCubemap =cp;
            for (int i = 0; i < 6; i++)
            {
                for (int im = 0; im < 8; im++)
                {
                    Graphics.CopyTexture(CustomReflectionProbe.realtimeTexture, i, im, cp, i, im);
                }
            }



            return cp;
        }

#endif

        public void SetRimColor2(Color templateRimColor2)
        {
            if (rimProEnable)
            {
                

                rimProValue.RimColor2 = templateRimColor2;
                Shader.SetGlobalColor("_RimCol2", rimProValue.RimColor2);
                if (!rimProValue.Rim2)
                {
                    rimProValue.RimColor2 = Color.black;
                    Shader.SetGlobalColor("_RimCol2", Color.black);
                }

            }
            else
            {
                rimProValue.RimColor2 = Color.black;
                Shader.SetGlobalColor("_RimCol2", Color.black);
            }
        }

        public void SetRimColor1(Color templateRimColor1)
        {
            if (rimProEnable)
            {

                
                rimProValue.RimColor1 = templateRimColor1;
                Shader.SetGlobalColor("_RimCol1", rimProValue.RimColor1);
                if (!rimProValue.Rim1)
                {
                    
                    rimProValue.RimColor1 = Color.black;
                    Shader.SetGlobalColor("_RimCol1", Color.black);
                }

            }
            else
            {
                //Shader.DisableKeyword("_RIM_PRO");
                rimProValue.RimColor1 = Color.black;
                Shader.SetGlobalColor("_RimCol1", Color.black);
            }
        }

        public void SetRimDir1(Vector3 templateRimDir1)
        {
            if (rimProEnable)
            {
                rimProValue.RimDir1 = templateRimDir1;
                Shader.SetGlobalVector("_RimDir1", rimProValue.RimDir1);
            }
        }
        
        public void SetRimDir2(Vector3 templateRimDir2)
        {
            if (rimProEnable)
            {
                rimProValue.RimDir2 = templateRimDir2;
                Shader.SetGlobalVector("_RimDir2", rimProValue.RimDir2);
            }
        }
    }
}
