using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using UnityEditor;
using UnityEngine;
using Event = UnityEngine.Event;

namespace FxEffect
{
    public class EffectLodTreeViewItemEditor
    {
        public string name;
        public EffectLodTreeViewItemEditor parent;
        private EffectCommonNodeLodLevel component;
        private EffectNodeLodLevel nodeLodLevel;
        private CanvasNodeLodLevel nodeCanvasLodLevel;

        public Renderer renderer
        {
            get
            {
                if (component != null)
                    return component.ownerRenderer;
                if (nodeLodLevel != null)
                    return nodeLodLevel.ownerRenderer;
                return null;
            }
        }

        public Transform transform
        {
            get
            {
                if (component != null)
                    return component.ownerRenderer.transform;
                if (nodeLodLevel != null)
                    return nodeLodLevel.ownerRenderer.transform;
                if (nodeCanvasLodLevel != null)
                    return nodeCanvasLodLevel.canvas.transform;
                return null;
            }
        }

        public EDevicePower displayLevel
        {
            get
            {
                if (component != null)
                    return component.displayLevel;
                if (nodeLodLevel != null)
                    return nodeLodLevel.displayLevel;
                if (nodeCanvasLodLevel != null)
                    return nodeCanvasLodLevel.displayLevel;
                return EDevicePower.EDP_None;
            }
            set
            {
                if (component != null)
                    component.displayLevel = value;
                if (nodeLodLevel != null)
                    nodeLodLevel.displayLevel = value;
                if (nodeCanvasLodLevel != null)
                    nodeCanvasLodLevel.displayLevel = value;
            }
        }

        protected List<EffectLodTreeViewItemEditor> items = new List<EffectLodTreeViewItemEditor>();

        private bool foldout = true;

        public EffectLodTreeViewItemEditor(string name, EffectCommonNodeLodLevel component, EffectNodeLodLevel nodeLodLevel, CanvasNodeLodLevel nodeCanvasLodLevel)
        {
            this.name = name;
            this.component = component;
            this.nodeLodLevel = nodeLodLevel;
            this.nodeCanvasLodLevel = nodeCanvasLodLevel;
        }

        public void AddItem(EffectLodTreeViewItemEditor item)
        {
            if (item != null && !items.Contains(item))
            {
                item.parent = this;
                items.Add(item);
            }
        }

        public GUIContent m_AddText = new GUIContent("+", "快速添加ParticleSystemLodLevel组件，方便细调不同Lod下的ParticleSystem参数");
        public GUIContent m_SkipToNodeText = new GUIContent("·", "快速跳转到节点");
        public virtual void Draw()
        {
            if (items.Count == 0)
            {
                GUILayout.BeginHorizontal();
                {
                    bool highlight = displayLevel <= GameLOD.Instance.DevicePower;
                    if (highlight)
                        GUI.backgroundColor = Color.green;

                    GUILayout.Label(name, GUILayout.Height(EffectLodTreeViewEditor.LINE_HEIGHT));

                    if (transform != null)
                    {
                        var parentLod = GetParentMaxLod();
                        if (displayLevel < parentLod)
                        {
                            GUILayout.Label(EditorGUIUtility.TrTextContentWithIcon("", string.Format("父节点中最大lod为:{0}, 大于本节点中设置的lod:{1}, 本节点的lod显示设置将被覆盖！", parentLod, displayLevel), MessageType.Warning),
                               GUILayout.Width(EffectLodTreeViewEditor.LINE_HEIGHT), GUILayout.Height(EffectLodTreeViewEditor.LINE_HEIGHT));
                        }

                        if (component == null && renderer != null && renderer is ParticleSystemRenderer)
                        {
                            if (GUILayout.Button(m_AddText, GUILayout.Width(20), GUILayout.Height(15)))
                            {
                                var lodLevel = transform.gameObject.AddComponent<ParticleSystemLodLevel>();
                                lodLevel.displayLevel = displayLevel;
                                Selection.activeGameObject = transform.gameObject;
                            }
                        }

                        if (GUILayout.Button("·", GUILayout.Width(15), GUILayout.Height(15)))
                        {
                            Selection.activeGameObject = transform.gameObject;
                        }
                    }

                    drowLodEnumPopup();

                    if (highlight)
                        GUI.backgroundColor = Color.white;
                }
                GUILayout.EndHorizontal();
            }
            else
            {
                bool highlight = displayLevel <= GameLOD.Instance.DevicePower;
                if (highlight)
                    GUI.backgroundColor = Color.green;

                GUILayout.BeginHorizontal();
                {
                    GUILayout.Space(10);
                    foldout = EditorGUILayout.Foldout(foldout, name);

                    if (transform != null)
                    {
                        var parentLod = GetParentMaxLod();
                        if (displayLevel < parentLod)
                        {
                            GUILayout.Label(EditorGUIUtility.TrTextContentWithIcon("", string.Format("父节点中最大lod为:{0}, 大于本节点中设置的lod:{1}, 本节点的lod显示设置将被覆盖！", parentLod, displayLevel), MessageType.Warning),
                               GUILayout.Width(EffectLodTreeViewEditor.LINE_HEIGHT), GUILayout.Height(EffectLodTreeViewEditor.LINE_HEIGHT));
                        }

                        if (GUILayout.Button("·", GUILayout.Width(15), GUILayout.Height(15)))
                        {
                            Selection.activeGameObject = transform.gameObject;
                        }
                    }

                    drowLodEnumPopup();
                }
                GUILayout.EndHorizontal();

                if (highlight)
                    GUI.backgroundColor = Color.white;

                if (foldout)
                {
                    GUILayout.BeginHorizontal();
                    {
                        GUILayout.Space(12);

                        GUILayout.BeginVertical();
                        {
                            foreach (var item in items)
                            {
                                item.Draw();
                            }
                        }
                        GUILayout.EndVertical();
                    }
                    GUILayout.EndHorizontal();
                }
            }
        }

        private void drowLodEnumPopup()
        {
            if (transform != null)
            {
                var displayLevel = (EDevicePower)EditorGUILayout.EnumPopup(this.displayLevel, GUILayout.Width(EffectLodTreeViewEditor.TOGGLE_WIDTH), GUILayout.Height(EffectLodTreeViewEditor.LINE_HEIGHT));
                if (displayLevel == EDevicePower.EDP_None)
                {
                    displayLevel = EDevicePower.EDP_Low;
                }
                if (displayLevel != this.displayLevel)
                {
                    CheckChildLod(displayLevel);

                    // check parent lod
                    var parentLod = GetParentMaxLod();
                    if (parentLod > displayLevel)
                    {
                        if (EditorUtility.DisplayDialog("提示", string.Format("父节点中最大lod为:{0}, 大于本节点将要设置的lod:{1}, 是否确认修改？", parentLod, displayLevel), "确定", "取消"))
                        {
                            this.displayLevel = displayLevel;
                        }
                    }
                    else
                    {
                        this.displayLevel = displayLevel;
                    }
                }
            }
            else
            {
                GUILayout.Space(EffectLodTreeViewEditor.TOGGLE_WIDTH);
            }
        }

        private void CheckChildLod(EDevicePower curLod)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("检测到子节点中有:");
            List<EffectLodTreeViewItemEditor> list = new List<EffectLodTreeViewItemEditor>();
            for (int i = 0; i < items.Count; ++i)
            {
                var item = items[i];
                if (item.transform != null && item.displayLevel < curLod)
                {
                    if (list.Count != 0)
                        sb.Append(",");
                    sb.Append(item.transform.name + "(" + item.displayLevel + ") ");
                    list.Add(item);
                }
            }
            if (list.Count > 0)
            {
                sb.Append(string.Format("低于当前节点的lod, 是否一键修改为父节点的lod ({0})", curLod));
                if (EditorUtility.DisplayDialog("提示", sb.ToString(), "确定", "取消"))
                {
                    foreach (var item in list)
                    {
                        item.displayLevel = curLod;
                    }
                }
            }
        }

        public EDevicePower GetParentMaxLod()
        {
            EDevicePower parentLod = EDevicePower.EDP_None;
            var fParent = parent;
            while (fParent != null)
            {
                if (parentLod < fParent.displayLevel)
                {
                    parentLod = fParent.displayLevel;
                }
                fParent = fParent.parent;
            }
            return parentLod;
        }

        public override string ToString()
        {
            return string.Format("name:{0} childcount:{1} hasComponent:{2} hasNodeLevel:{3}", name, items.Count, component != null, nodeLodLevel != null);
        }
    }

    public class EffectLodTreeViewEditor : EffectLodTreeViewItemEditor
    {
        public const float TOGGLE_WIDTH = 100f;
        public const float LINE_HEIGHT = 18;

        private string[] _captions = { "特效层", "Lod" };
        private GUILayoutOption[] _options = { GUILayout.ExpandWidth(true), GUILayout.Width(TOGGLE_WIDTH) };

        public EffectLodTreeViewEditor() : base ("", null, null, null)
        {

        }

        public override void Draw()
        {
            DrawColumnHeader(_captions, _options);
            GUILayout.BeginVertical(GUILayout.MinHeight(10));
            GUILayout.Space(2);

            foreach (var item in items)
            {
                item.Draw();
            }

            GUILayout.Space(3);
            GUILayout.EndVertical();
        }

        public static void DrawColumnHeader(string[] captions, GUILayoutOption[] options)
        {
            if (captions.Length != options.Length)
            {
                Debug.LogError("The argurments of DrawColumnHeader function is invalid");
                return;
            }

            GUILayout.BeginHorizontal(EditorStyles.toolbar);
            {
                for (int i = 0; i < captions.Length; i++)
                {
                    GUILayout.Label(captions[i], options[i]);
                    if (i != captions.Length - 1)
                    {
                        DrawTreeSeparator();
                    }
                }
            }
            GUILayout.EndHorizontal();
        }

        private static void DrawTreeSeparator()
        {
            GUILayout.Space(1);

            if (Event.current.type == EventType.Repaint)
            {
                Texture2D tex = EditorGUIUtility.whiteTexture;
                Rect rect = GUILayoutUtility.GetLastRect();
                GUI.color = new Color(0f, 0f, 0f, 0.5f);
                GUI.DrawTexture(new Rect(rect.x, rect.yMin, 1, 17), tex);
                GUI.color = Color.white;
            }
        }
    }
}
