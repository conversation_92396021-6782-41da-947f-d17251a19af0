using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;

namespace ArtEditor
{
	[ExecuteInEditMode]
	public class BakeLightData : MonoBehaviour 
	{
		[SerializeField]
		private float m_distance = 0.0f;//radius or height
		[SerializeField]
		private bool m_distanceFixed = true;
		[SerializeField]
		private Texture m_texture = null;
		//[SerializeField][Range(0.0f,1.0f)]
		//private float m_cutoff = 0.5f;
		[SerializeField]
		private ShadowCastingMode m_shadowCastingMode = ShadowCastingMode.TwoSided;
		[SerializeField]
		private bool m_restraint = true;
		private Light m_thisLight = null;
		private GameObject m_cookie = null;
		private Material m_material = null;
        private Renderer m_renderer;

        private void Start()
        {
            if (m_cookie == null)
            {
                CreateCookie();
                if (m_thisLight.type.Equals(UnityEngine.LightType.Spot))
                {
                    this.transform.localRotation *= Quaternion.Euler(new Vector3(90.0f, 0.0f, 0.0f));
                }
            }
            if (m_cookie != null)
                m_renderer = m_cookie.GetComponent<Renderer>();
        }

        private void Update()
		{
			if (m_thisLight == null) 
			{
				return;
			}

			m_renderer.shadowCastingMode = m_shadowCastingMode;
			
			if (m_thisLight.type.Equals (UnityEngine.LightType.Spot)) 
			{
				//spot's range means height.
				if (m_restraint) 
				{
					m_cookie.transform.localPosition = new Vector3 (0.0f, 0.0f, m_thisLight.range - m_distance);
					if (m_distanceFixed) 
					{
						float t_radius = Mathf.Tan (m_thisLight.spotAngle * 0.5f * Mathf.Deg2Rad) * (m_thisLight.range - m_distance);
						float t_zoom = t_radius * Mathf.Sin (45.0f * Mathf.Deg2Rad);
						t_zoom *= 2;
						m_cookie.transform.localScale = new Vector3 (t_zoom,t_zoom,t_zoom);
					}
				}
			}
			else if (m_thisLight.type.Equals (UnityEngine.LightType.Point)) 
			{
				if (m_restraint) 
				{
					m_cookie.transform.localPosition = Vector3.zero;
					float t_zoom = m_thisLight.range + m_distance;
					t_zoom *= 2;
					m_cookie.transform.localScale = new Vector3 (t_zoom, t_zoom, t_zoom);
				}
			}
			if (m_material != null) 
			{
				m_material.mainTexture = m_texture;
				//m_material.SetFloat ("_Cutoff", m_cutoff);
			}
		}
		public Light ThisLight
		{
			get
			{
				if (m_thisLight == null) 
				{
					m_thisLight = this.gameObject.AddComponent<Light> ();
				}
				return m_thisLight;
			}
			set
			{
				m_thisLight = value;
			}
		}
		private void CreateCookie()
		{
			m_cookie = new GameObject ("Cookie");
			MeshFilter t_mf = m_cookie.AddComponent<MeshFilter> ();
			if (m_thisLight.type.Equals (UnityEngine.LightType.Spot)) 
			{
				//quad.
				Mesh t_mesh = new Mesh ();
				Vector3[] vertices = new Vector3[4];
				vertices [0] = new Vector3 (-0.5f, -0.5f, 0.0f);
				vertices [1] = new Vector3 (0.5f, 0.5f, 0.0f);
				vertices [2] = new Vector3 (0.5f, -0.5f, 0.0f);
				vertices [3] = new Vector3 (-0.5f, 0.5f, 0.0f);
				Vector2[] uvs = new Vector2[4];
				uvs [0] = new Vector2 (0.0f, 0.0f);
				uvs [1] = new Vector2 (1.0f, 1.0f);
				uvs [2] = new Vector2 (1.0f, 0.0f);
				uvs [3] = new Vector2 (0.0f, 1.0f);
				Vector3[] normals = new Vector3[4];
				normals [0] = new Vector3 (0.0f, 0.0f, -1.0f);
				normals [1] = new Vector3 (0.0f, 0.0f, -1.0f);
				normals [2] = new Vector3 (0.0f, 0.0f, -1.0f);
				normals [3] = new Vector3 (0.0f, 0.0f, -1.0f);
				int[] triangles = new int[6];
				triangles [0] = 0;
				triangles [1] = 1;
				triangles [2] = 2;
				triangles [3] = 0;
				triangles [4] = 3;
				triangles [5] = 1;
				t_mesh.vertices = vertices;
				t_mesh.uv = uvs;
				t_mesh.normals = normals;
				t_mesh.triangles = triangles;
				if (null != t_mf) 
				{
					t_mf.mesh = t_mesh;
				}
			}
			else if (m_thisLight.type.Equals (UnityEngine.LightType.Point)) 
			{
				//sphere.
				GameObject t_sphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
				#if UNITY_EDITOR
				Mesh t_sphereMesh = t_sphere.GetComponent<MeshFilter> ().sharedMesh;
				#else
				Mesh t_sphereMesh = t_sphere.GetComponent<MeshFilter> ().mesh;
				#endif
				Mesh t_mesh = new Mesh ();
				Vector3[] vertices = new Vector3[t_sphereMesh.vertexCount];
				for (int i = 0; i < vertices.Length; ++i) 
				{
					vertices [i] = t_sphereMesh.vertices [i];
				}
				Vector2[] uvs = new Vector2[t_sphereMesh.uv.Length];
				for (int i = 0; i < uvs.Length; ++i) 
				{
					uvs [i] = t_sphereMesh.uv [i];
				}
				Vector3[] normals = new Vector3[t_sphereMesh.normals.Length];
				for (int i = 0; i < normals.Length; ++i) 
				{
					normals [i] = t_sphereMesh.normals [i];
				}
				int[] triangles = new int[t_sphereMesh.triangles.Length];
				for (int i = 0; i < triangles.Length; ++i) 
				{
					triangles [i] = t_sphereMesh.triangles [i];
				}
				t_mesh.vertices = vertices;
				t_mesh.uv = uvs;
				t_mesh.normals = normals;
				t_mesh.triangles = triangles;	
				if (null != t_mf) 
				{
					t_mf.mesh = t_mesh;
				}
				#if UNITY_EDITOR
				DestroyImmediate(t_sphere);
				#else
				Destroy(t_sphere);
				#endif
			}
			else 
			{
				//Debug.LogError ("light type is error.");
				return;
			}
			MeshRenderer t_mr = m_cookie.AddComponent<MeshRenderer> ();
			//material setting.
			Shader t_shader = null;
			//if (m_thisLight.type.Equals (UnityEngine.LightType.Spot)) 
			//{
			//	t_shader = Shader.Find ("Legacy Shaders/Transparent/Cutout/Diffuse");
			//}
			//else 
			//{
			t_shader = Shader.Find ("Legacy Shaders/Transparent/Diffuse");
			//}
			m_material = new Material(t_shader);
			if (null == m_material) 
			{
				Debug.Log ("material is null.");
				return;
			}
			t_mr.sharedMaterial = m_material;
			//other setting.
			t_mr.receiveShadows = false;
			t_mr.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.TwoSided;
			t_mr.lightProbeUsage = UnityEngine.Rendering.LightProbeUsage.Off;
			t_mr.reflectionProbeUsage = UnityEngine.Rendering.ReflectionProbeUsage.Off;
			m_cookie.transform.parent = this.transform;
			if (m_thisLight.type.Equals (UnityEngine.LightType.Spot)) 
			{
				m_cookie.transform.localPosition += new Vector3 (0.0f,0.0f,10.0f);
			}
			//UnityEditor.GameObjectUtility.SetStaticEditorFlags (m_cookie, UnityEditor.StaticEditorFlags.LightmapStatic);
		}
	}
}