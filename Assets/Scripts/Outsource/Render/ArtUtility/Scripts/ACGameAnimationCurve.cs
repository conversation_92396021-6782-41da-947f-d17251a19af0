using System.Collections;
using System.Collections.Generic;
using UnityEngine;
namespace ArtEditor
{
    public class ACGameAnimationCurve : MonoBehaviour
    {

#if UNITY_EDITOR
        public ACGameAnimationCurve p_clone = null;
#endif

        /// <summary>
        /// Main processing logic
        /// </summary>
        [SerializeField] private List<ACGameAnimationCurveItem> p_items = null;
        public List<ACGameAnimationCurveItem> Items
        {
            get
            {
                if (null == p_items)
                {
                    p_items = new List<ACGameAnimationCurveItem>();
                }
                return p_items;
            }
            set
            {
                p_items = value;
            }
        }
        public void Start()
        {
            if (null != p_items)
            {
                for (int i = 0; i < p_items.Count; ++i)
                {
                    p_items[i].GetActiveMaterial();
                    p_items[i].isPlaying = true;
                }
            }
        }
        private void LateUpdate()
        {
            if (null != p_items)
            {
                for (int i = 0; i < p_items.Count; ++i)
                {
                    p_items[i].CustomUpdate();
                }
            }
        }

        public void Stop()
        {
            if (null != p_items)
            {
                for (int i = 0; i < p_items.Count; ++i)
                {
                    p_items[i].isPlaying = false;
                }
            }
        }

        public void ReStart()
        {
            enabled = true;
            if (null != p_items)
            {
                for (int i = 0; i < p_items.Count; ++i)
                {
                    p_items[i].Replacement();
                }
#if UNITY_EDITOR
                //effect object
                if (null != p_clone)
                {
                    for (int i = 0; i < p_clone.Items.Count; ++i)
                    {
                        p_clone.Items[i].Replacement();
                    }
                }
#endif       
            }
        }

        public void OnEnable()
        {
            if (null != p_items)
            {
                for (int i = 0; i < p_items.Count; ++i)
                {
                    //Debug.Log("Curve:   " + transform.position);
                    p_items[i].OnChangeTransform();
                }
            }
        }

        public void OnDisable()
        {
            //Convenient Debugging.
            //enabled = false;
            if (null != p_items)
            {
                for (int i = 0; i < p_items.Count; ++i)
                {
                    p_items[i].Replacement();
                }
            }
        }

        public void AddItem(int enumFlag)
        {
            if (null != p_items)
            {
                ACGameAnimationCurveItem item = new ACGameAnimationCurveItem();
                switch (enumFlag)
                {
                    case 0:
                        item.Initialization(ACGameGlobelArtUtility.EffectCurveType.POSITION, transform);
                        break;
                    case 1:
                        item.Initialization(ACGameGlobelArtUtility.EffectCurveType.ROTATION, transform);
                        break;
                    case 2:
                        item.Initialization(ACGameGlobelArtUtility.EffectCurveType.SCALE, transform);
                        break;
                    case 3:
                        item.Initialization(ACGameGlobelArtUtility.EffectCurveType.MESHCOLOR, transform);
                        break;
                    case 4:
                        item.Initialization(ACGameGlobelArtUtility.EffectCurveType.MATERIALCOLOR, transform);
                        break;
                    case 5:
                        item.Initialization(ACGameGlobelArtUtility.EffectCurveType.TEXTUREUV, transform);
                        break;
                    case 6:
                        item.Initialization(ACGameGlobelArtUtility.EffectCurveType.DISSOLVEVALUE, transform);
                        break;
                    case 7:
                        item.Initialization(ACGameGlobelArtUtility.EffectCurveType.MASKVALUE, transform);
                        break;
                    case 8:
                        item.Initialization(ACGameGlobelArtUtility.EffectCurveType.NONE, transform);
                        break;
                    default:
                        //Debug.LogError ("The enum flag is error.");
                        Destroy(this);
                        break;
                }
                p_items.Add(item);
            }
        }
    }
}