#if ACGGAME_CLIENT
using System;
using ACG.Core;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using GameFramework.FMath;
using GameFramework.FrameSync;
using TKFrame;
using TKPlugins;
using TriggerSystem;
using UnityEditor;
using UnityEngine;
using ZGameChess;
using ZGameClient;

public class TeamLeaderSoGame : MonoBehaviour
{
    //#if UNITY_EDITOR

    public static bool ReloadConfig
    {
        get;
        set;
    } = false;

    private FVector3 m_startPos = new FVector3(-10.04f, 0f, -6.8f);

    public bool isActive = false;

    public bool AutoEnterInStart = false;

    public PlayerMoveMessage moveWay = PlayerMoveMessage.StartWalk;

    private ChessBattleLogicPlayer m_enemyLogicPlayer;

    private TKDictionary<int, ChessPlayerUnit> m_playerList = new TKDictionary<int, ChessPlayerUnit>();
    public TKDictionary<int, ChessPlayerUnit> showPlayerList
    {
        get
        {
            return m_playerList;
        }
    }

    private ChessPlayerUnit m_enemyPlayer;

    public ChessPlayerUnit EnemyPlayer
    {
        get { return m_enemyPlayer; }
    }

    private TKDictionary<int, TPlayerInfo> m_playerInfoList = new TKDictionary<int, TPlayerInfo>();
    public TKDictionary<int, TPlayerInfo> playerInfoList
    {
        get
        {
            return m_playerInfoList;
        }
    }

    private static TeamLeaderSoGame m_instance;
    public static TeamLeaderSoGame Instance
    {
        get
        {
            return m_instance;
        }
    }

    private TransferEffectController _transferEffectCtrl = null;

    public int currentId = 0;
    private int m_count = 1;
    public int count
    {
        get
        {
            return m_count;
        }
        set
        {
            m_count = value;
        }
    }

    private Vector2 m_delta = new Vector2(0.0f, 0.0f);
    public Vector2 delta
    {
        get
        {
            return m_delta;
        }
    }

    private static object locker = new object();
    private static Queue<string> m_allNameData = new Queue<string>();

    public static void PushString(string str)
    {
        lock (locker)
        {
            m_allNameData.Enqueue(str);
        }
    }

    public static string GetString()
    {
        string data = string.Empty;
        lock (locker)
        {
            if (m_allNameData.Count > 0)
                data = m_allNameData.Dequeue();
        }
        return data;
    }

    private void Awake()
    {
        m_instance = this;
        moveWay = PlayerMoveMessage.StartWalk;

        //BattleTransfer.Cache();

        _transferEffectCtrl = new TransferEffectController();
        _transferEffectCtrl.InitTransferEffect("");

        SetDelta(false);
    }

    private IEnumerator Start()
    {
        if (AutoEnterInStart)
        {
            yield return TKFrame.CoroutineWait.GetWaitForSeconds(6f);
            LockStepFightProcess.StartNoNetworkFight(8, (int)NoNetworkSceneID.TEAM_LEADER_MODE, NoNetworkData.setNum, NoNetworkData.NoNetworkEntrance.GM);
        }

    }

    private void Update()
    {
        if (ReloadConfig)
        {
            string configName = TeamLeaderPanel.configName;
            PushString(configName);

            ChessOperationInput.WriteInput((byte)ChessOperationType.OPT_TEAMLEADER_EDIT_MSG, (byte)CommonId.EditorMsgType.ChangeCfg, (byte)currentId);
            
            ReloadConfig = false;
        }
    }

    public void SendCommand(string command, params object[] data)
    {
        switch (command)
        {
            case "捡装备模式":
#if !OUTSOURCE
                ChessBattleGlobal.Instance.EquipmentController.ClearMonsterDrop();
#endif
                ChessOperationInput.WriteInput(
                    (byte)ChessOperationType.OPT_TEAMLEADER_EDIT_MSG,
                    (byte)CommonId.EditorMsgType.SetCollider);
                break;
            case "走路":
                ChessOperationInput.WriteInput(
                    (byte)ChessOperationType.OPT_TEAMLEADER_EDIT_MSG,
                    (byte)CommonId.EditorMsgType.ChangeMoveWay,
                    (byte)PlayerMoveMessage.StartWalk);
                break;
            case "跑步":
                ChessOperationInput.WriteInput(
                    (byte)ChessOperationType.OPT_TEAMLEADER_EDIT_MSG,
                    (byte)CommonId.EditorMsgType.ChangeMoveWay,
                    (byte)PlayerMoveMessage.StartRun);
                break;
            case "冲刺":
                ChessOperationInput.WriteInput(
                    (byte)ChessOperationType.OPT_TEAMLEADER_EDIT_MSG,
                    (byte)CommonId.EditorMsgType.ChangeMoveWay,
                    (byte)PlayerMoveMessage.Rush);
                break;
            case "跳到客场流程":
                JumpOtherBattleField();
                break;
            case "更换英雄":
                ChangeHero(data);
                break;
            case "更换玩家":
                ChangePlayer(data);
                break;
            case "受击移动":
                DoHit();
                break;
            case "编辑新英雄":
                ChangeNewHero(data);
                break;
            case "hurt_01":
                PlayHurt01();
                break;
            case "hurt_02":
                PlayHurt02();
                break;
            case "attack":
                PlayAttack();
                break;
            case "Deduct":
                PlayerDeductLife();
                break;
            case "攻击对方":
                Deduct(false);
                break;
            case "击杀对方":
                Deduct(true);
                break;
            case "变换模型1":
                ChangeModel(1);
                break;
            case "变换模型2":
                ChangeModel(2);
                break;
            case "变换模型3":
                ChangeModel(3);
                break;
            case "变换模型4":
                ChangeModel(4);
                break;
            default:
                PlayAnim(command);
                break;
        }
    }

    private void ChangeModel(byte id)
    {
        ChessOperationInput.WriteInput((byte)ChessOperationType.OPT_CSOACTION_TRIGGER, (byte)E_SO_TRIGGER_TYPE.ChangeModel, id);
    }

    private void Deduct(bool isKiller)
    {
        if (m_playerList.Count != 2)
        {
            UIOverlay.Instance.ShowCommonTips(Localization.Trans("请调整小小英雄数量为2！！！"));
            return;
        }
        int enemyindex = currentId == 0 ? 1 : 0;
        var enemy = m_playerList[enemyindex];
        var cur = m_playerList[currentId];
        var sourcePos = new FVector2(cur.transform.position.x, cur.transform.position.z);
        var targetPos = new FVector2(enemy.transform.position.x, enemy.transform.position.z);
        byte[] sourcePosData = ZGame.GameUtilCore.FVector2ToMinCharArray(sourcePos);
        byte[] movedata = ZGame.GameUtilCore.FVector2ToMinCharArray(targetPos);
        List<byte> data = new List<byte>();
        data.Add(isKiller ? (byte)CommonId.EditorMsgType.Deduct_Killer : (byte)CommonId.EditorMsgType.Deduct_Normal);
        data.AddRange(sourcePosData);
        data.AddRange(movedata);
        ChessOperationInput.WriteInput(
            (byte)ChessOperationType.OPT_TEAMLEADER_EDIT_MSG,
            data);
    }

    private void PlayerDeductLife()
    {
        if (m_playerList.Count != 2)
        {
            UIOverlay.Instance.ShowCommonTips(Localization.Trans("请调整小小英雄数量为2！！！"));
            return;
        }

        UIOverlay.Instance.ShowCommonTips(Localization.Trans("已失效 请使用 【攻击对方】和【击杀对方】！！！"));
        //int enemyindex = currentId == 0 ? 1 : 0;
        //m_playerList[currentId].PlayPlayerBattleRetDmg(m_playerList[enemyindex]);
    }

    public bool isControllAll = false;
    
    public void ExecuteCommand(Action<ChessPlayerUnit, bool> action)
    {
        if (action == null)
            return;
        
        if (isControllAll)
        {
            foreach (ChessPlayerUnit playerUnit in m_playerList.Values)
            {
                action(playerUnit, true);
            }
        }
        else
        {
            ChessPlayerUnit playerUnit = m_playerList[currentId];
            action(playerUnit, false);
        }
    }
    
    private void PlayHurt01()
    {
        ExecuteCommand(delegate(ChessPlayerUnit playerUnit, bool isControllAll)
        {
            playerUnit.AnimBehaviour.Reset();
            playerUnit.AnimBehaviour.PlayHurtAction(false, -playerUnit.Dir);
        });
    }

    private void PlayHurt02()
    {
        ExecuteCommand(delegate(ChessPlayerUnit playerUnit, bool isControllAll)
        {
            playerUnit.AnimBehaviour.PlayHurtAction(true, -playerUnit.Dir);
        });
    }

    private void PlayAttack()
    {
        ExecuteCommand(delegate(ChessPlayerUnit playerUnit, bool isControllAll)
        {
            playerUnit.PlayAttack(0);
        });
    }

    private void PlayAnim(string command)
    {
        ExecuteCommand(delegate(ChessPlayerUnit playerUnit, bool isControllAll)
        {
            playerUnit.PlayAnim(command);
        });
    }

    public void PlayTriggerMoment(E_TRIGGER_MOMENT moment)
    {
        ExecuteCommand(delegate(ChessPlayerUnit playerUnit, bool isControllAll)
        {
            playerUnit.TriggerMoment(moment);
        });
    }

    private void DoHit()
    {
        Vector3 src = new Vector3(-1, 0, -1);
        ExecuteCommand(delegate(ChessPlayerUnit playerUnit, bool isControllAll)
        {
            Vector3 target = new Vector3(src.x + delta.x, src.y, src.z + delta.y);
            ChessPlayerUnitAnimBehaviour animBehaviour = playerUnit.GetComponent<ChessPlayerUnitAnimBehaviour>();
            animBehaviour.PlayHurtAction(true, target);
        });
    }

    private void JumpTo()
    {
        ChessPlayerUnit showPlayer = m_playerList[currentId];
        if (showPlayer != null)
        {
            //showPlayer.transform.position = logicPlayer.BattleLogicField.ownerTransform.position.ToVector3();
            //showPlayer.transform.position = m_startPos.ToVector3();
#if !OUTSOURCE
            var tile = ChessBattleGlobal.Instance.BattleField.GetTile(3, 4, AreaType.Battle);
            if (tile != null)
                showPlayer.jumpDst = tile.Position;
#else
            showPlayer.jumpDst = new Vector3(0, 5, 0);
#endif
            showPlayer.DoJumpTo(1f);
        }
    }

    private void JumpBack()
    {
        ChessPlayerUnit showPlayer = m_playerList[currentId];
        if (showPlayer != null)
        {
#if !OUTSOURCE
            var tile = ChessBattleGlobal.Instance.BattleField.GetTile(3, 4, AreaType.Battle);
            if (tile != null)
                showPlayer.transform.position = tile.Position;
#else
            showPlayer.transform.position = new Vector3(0, 5, 0);
#endif

            ChessOperationInput.WriteInput((byte)ChessOperationType.OPT_TEAMLEADER_EDIT_MSG, (byte)CommonId.EditorMsgType.ResetOwnerTransform);

            FVector3Msg fVector3Msg = new FVector3Msg();
            fVector3Msg.isNewValue = true;
            //fVector3Msg.value = logicPlayer.BattleLogicField.ownerTransform.position;
            fVector3Msg.value = m_startPos;
            showPlayer.SetJumpBack(fVector3Msg);
            showPlayer.DoJumpBack(1f);
        }
    }

    private bool isJumpOtherBattleFielding = false;
    private void JumpOtherBattleField()
    {
        if (isJumpOtherBattleFielding)
            return;
        ChessPlayerUnit showPlayer = m_playerList[currentId];
        if (showPlayer != null)
            StartCoroutine(JumpOtherBattleField_Impl(showPlayer));
    }

    private IEnumerator JumpOtherBattleField_Impl(ChessPlayerUnit showPlayer)
    {
        isJumpOtherBattleFielding = true;
        Vector3 pos = new Vector3(0, 5, 0);
#if !OUTSOURCE
        var tile = ChessBattleGlobal.Instance.BattleField.GetTile(3, 4, AreaType.Battle);
        if (tile != null)
        {
            pos = tile.Position;
        }
#endif
        _transferEffectCtrl.DisplayTransferEffect(pos, true);
        yield return TKFrame.CoroutineWait.GetWaitForSeconds(0.5f);
        JumpTo();
        yield return TKFrame.CoroutineWait.GetWaitForSeconds(1.0f);
        _transferEffectCtrl.FreeTransferEffect(true);
        ACGEventManager.Instance.Send(EventType_BattleView.AutoChess_Battle_StartTransfer);
#if !OUTSOURCE
        BattleMovieMaskNew.Display();
#endif
        yield return TKFrame.CoroutineWait.GetWaitForSeconds(1.0f);
        ACGEventManager.Instance.Send(EventType_BattleView.AutoChess_Battle_EndTransfer);
#if !OUTSOURCE
        BattleMovieMaskNew.Hide();
#endif
        showPlayer.HideBody();
        _transferEffectCtrl.DisplayTransferEffect(pos, false);
        yield return TKFrame.CoroutineWait.GetWaitForSeconds(0.4f);
        showPlayer.ShowBody();
        JumpBack();
        yield return TKFrame.CoroutineWait.GetWaitForSeconds(1.0f);
        _transferEffectCtrl.FreeTransferEffect(false);
        isJumpOtherBattleFielding = false;
    }

    private void ChangeNewHero(params object[] data)
    {
        int playerId = 0;
        if (data.Length > 0)
        {
            playerId = (int)data[0];
        }

        TPlayerInfo playerInfo = m_playerInfoList[playerId];
        playerInfo.stTAC_GameTinyData.iTinyId = -1;
        ChessModelManager.Instance.GetBattleModel().GetPlayerModel(playerId).Playerinfo = playerInfo;

        ChessOperationInput.WriteInput(
            (byte)ChessOperationType.OPT_TEAMLEADER_EDIT_MSG,
            (byte)CommonId.EditorMsgType.ResetPlayer,
            (byte)playerId);

        ChessPlayerController playerController = ChessBattleGlobal.Instance.ChessPlayerCtrl;
        ChessPlayerUnit playerUnit = playerController.GetPlayer(playerId);

        m_playerList[playerId] = playerUnit;

        ReloadConfig = true;
    }

    private void ChangeHero(params object[] data)
    {
        if (data.Length > 0)
        {
            TACG_Item_Client item_Client = data[0] as TACG_Item_Client;
            ChangeHero(currentId, item_Client);
        }
    }

    public void ChangeHero(int playerId, TACG_Item_Client data)
    {
        TPlayerInfo playerInfo = m_playerInfoList[playerId];
        playerInfo.stTAC_GameTinyData.iTinyId = data.iID;
        ChessModelManager.Instance.GetBattleModel().GetPlayerModel(playerId).Playerinfo = playerInfo;
        TeamLeaderPanel.configName = data.sTeamLeaderCfg;

        ReloadConfig = true;
    }

    public void ReCreatePlayer(int playerId)
    {
        ChessBattleModel battleModel = ChessModelManager.Instance.GetBattleModel();
        ChessPlayerController playerController = ChessBattleGlobal.Instance.ChessPlayerCtrl;
        battleModel.battleTableInfo.iSeatID = -1;
        playerController.DestroyChessPlayerUnit(playerId, false);
        playerController.CreateBattleFieldById(0, true);
        battleModel.battleTableInfo.iSeatID = 0;
    }

    public void SetVS()
    {
        if (count > 1)
        {
            var playerModel = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(currentId);
            if (playerModel != null)
            {
                //设置默认的敌人ID
                int enemyPlayerId = 1;
                playerModel.EnemyPlayerID = enemyPlayerId;

                var playerUnit = ChessBattleGlobal.Instance.ChessPlayerCtrl.GetPlayer(0);

                var enemyUnit = ChessBattleGlobal.Instance.ChessPlayerCtrl.GetPlayer(enemyPlayerId);
                ChessBattleGlobal.Instance.ChessPlayerCtrl.SetVS(playerUnit, enemyUnit);

                var enemyPlayerModel = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(enemyPlayerId);
                enemyPlayerModel.EnemyPlayerID = 0;
            }
        }
    }
#if !OUTSOURCE
    #region GM逻辑调用
    public void GMForceChangePlayerTransferIdx()
    {
        ChessPlayerController playerController = ChessBattleGlobal.Instance.ChessPlayerCtrl;
        var allTransferDesc = ZGame.DataBaseManager.Instance.GetAllTransferCfgClientDesc();
        List<int> tempList = new List<int>();
        foreach (var item in allTransferDesc)
        {
            tempList.Add(item.Value.iID);
        }

        for (int i = 0; i < 8; i++)
        {
            PlayerModel playerModel = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(i);

            if (tempList.Count > i)
            {
                if (playerModel != null && playerModel.Playerinfo != null)
                    playerModel.Playerinfo.stTAC_GameTinyData.iTransportEffectId = tempList[i];
            }
            else
            {
                break;
            }
        }

        ZGame.Battle.BattlePooledEffectsContainer battlePooledEffectsContainer = ChessBattleGlobal.Instance.PooledEffectsContainer;
        if (battlePooledEffectsContainer != null)
        {
            ChessBattleModel battleModel = ChessModelManager.Instance.GetBattleModel();
            var playersDict = battleModel.GetAllPlayers();
            var enumerator = playersDict.GetEnumerator();

            TKDictionary<string, List<string>> chessTransfers = new TKDictionary<string, List<string>>();

            while (enumerator.MoveNext())
            {
                PlayerModel playerModel = enumerator.Current.Value;
                if (playerModel.Playerinfo == null)
                {
                    continue;
                }

                var chessTransportEffectId = playerModel.Playerinfo.stTAC_GameTinyData.iTransportEffectId;
                var chessTransportEffectCfg = ZGame.DataBaseManager.Instance.SearchTransferCfgClientDesc(chessTransportEffectId);
                if (chessTransportEffectCfg != null && !string.IsNullOrEmpty(chessTransportEffectCfg.sAssetBundle) && !string.IsNullOrEmpty(chessTransportEffectCfg.sFlashLight))
                {
                    if (!chessTransfers.ContainsKey(chessTransportEffectCfg.sAssetBundle))
                    {
                        List<string> insertList = new List<string>();
                        chessTransfers.Add(chessTransportEffectCfg.sAssetBundle, insertList);
                        insertList.Add(chessTransportEffectCfg.sFlashLight);
                    }
                }
            }

            foreach (var item in chessTransfers)
            {
                if (AssetBundleManager.CheckAssetBundleExist(item.Key))
                    battlePooledEffectsContainer.PreloadEffectAsset(item.Key, item.Value);
            }
        }
    }
    #endregion
#endif
    private void ChangePlayer(params object[] data)
    {
        if (data.Length > 0)
        {
            int moveStep = (int)data[0];
            currentId += moveStep;
            if (currentId >= count)
            {
                currentId = count - 1;
            }
            if (currentId < 0)
            {
                currentId = 0;
            }
        }
    }

    public int GetCurrentPlayerTabId()
    {
        TPlayerInfo playerInfo = m_playerInfoList[currentId];
        return playerInfo.stTAC_GameTinyData.iTinyId;
    }

    public void ChangePlayerNum(int num)
    {
        if (num == count)
            return;

        if (num < 1)
            num = 1;

        count = num;

        ChessOperationInput.WriteInput(
            (byte)ChessOperationType.OPT_TEAMLEADER_EDIT_MSG,
            (byte)CommonId.EditorMsgType.AddPlayer,
            (byte)num);
    }

    public void SetDelta(bool isDemo)
    {
        if (isDemo)
        {
            m_delta = new Vector2(0.0f, 0.0f);
        }
        else
        {
            m_delta = new Vector2(2.0f, 0.0f);
        }
    }

    public void RequestInit()
    {
        ChessOperationInput.WriteInput(
            (byte)ChessOperationType.OPT_TEAMLEADER_EDIT_MSG,
            (byte)CommonId.EditorMsgType.Init);
    }

    public void InitAllPlayer()
    {
        if (count > 8)
            count = 8;

        m_playerInfoList.Clear();
        m_playerList.Clear();


        StartCoroutine(InitAllPlayerIter());

    }

    private IEnumerator InitAllPlayerIter()
    {
        ChessBattleModel battleModel = ChessModelManager.Instance.GetBattleModel();
        ChessPlayerController playerController = ChessBattleGlobal.Instance.ChessPlayerCtrl;

        int initCount = 0;
        while (initCount != count)
        {
            initCount = playerController.ChessPlayerUnitDic.Count;
            initCount += playerController.EnemyChessPlayerUnitDic.Count;
            initCount += playerController.ObservedChessPlayerUnitDic.Count;
            yield return null;
        }
        
        for (int i = 0; i < count; i++)
        {
            ChessPlayerUnit playerUnit = playerController.GetPlayer(i);

            m_playerInfoList.Add(i, battleModel.GetPlayerModel(i).Playerinfo.DeepClone() as TPlayerInfo);
            m_playerList.Add(i, playerUnit);
        }

        SetVS();
    }

    //#endif
}
#endif
