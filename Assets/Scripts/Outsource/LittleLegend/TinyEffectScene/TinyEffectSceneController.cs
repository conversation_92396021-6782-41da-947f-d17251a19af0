#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using ZGameChess;
using TKFrame;
using System.Collections;
using ZGame;

namespace TinyTool
{
    public class TinyEffectSceneController : MonoBehaviour
    {
        public GameObject 小小英雄模型;
        public LittleLegendCfg 小小英雄模型配置;
        public GameObject 场景模型;

        protected GameObject 根节点;
        protected TinyModelPreviewSceneInst _场景实体;

        public TinyModelPreviewSceneInst 场景实体 { get { return _场景实体; } }

        private void Awake()
        {
            StartCoroutine(初始化());
        }

        public IEnumerator 初始化()
        {
            if (根节点 != null)
            {
                GameObject.Destroy(根节点);
            }

            ACG.Core.Core.Setup();

            AssetService assetService = gameObject.AddComponent(typeof(AssetService)) as AssetService;
            yield return assetService.Initialize();

            //挂接相机管理服务：提供相机管理
            TKCameraManager camMgr = gameObject.AddComponent(typeof(TKCameraManager)) as TKCameraManager;
            yield return camMgr.Initialize();

            StageRunner stageRunner = gameObject.AddComponent(typeof(StageRunner)) as StageRunner;
            yield return stageRunner.Initialize();

            DataBaseManager.Instance.Initialize();

            GameLOD.Instance.ChangeLOD(EDevicePower.EDP_Ultra, GameLOD.Instance.ResolutionMode, GameLOD.Instance.FPSMode, GameLOD.Instance.PowerSaving);

            if (ChessBattleGlobal.Instance.HeroMatCfg == null)
            {
                ChessBattleGlobal.Instance.HeroMatCfg = new HeroMaterialConfig();
                yield return ChessBattleGlobal.Instance.HeroMatCfg.Init();
            }

            根节点 = new GameObject("根节点");
            根节点.transform.SetParent(transform);
            ChessUtil.MakeTransformIdentity(根节点.transform);
            _场景实体 = 根节点.AddComponent<TinyModelPreviewSceneInst>();
            _场景实体.OnResLoaded(场景模型, string.Empty);

            var abName = LittleLegendCfg.GetAssetbundleName(小小英雄模型.name, 小小英雄模型.gameObject.tag);
            var 小小英雄配置路径 = AssetDatabase.GetAssetPathsFromAssetBundleAndAssetName(abName + ".unity3d", 小小英雄模型.name);
            if (小小英雄配置路径.Length == 0)
            {
                _场景实体.OnTinyModelImplSync(小小英雄模型, null);
                小小英雄模型配置 = null;
            }
            else
            {
                小小英雄模型配置 = AssetDatabase.LoadAssetAtPath<LittleLegendCfg>(小小英雄配置路径[0]);
                小小英雄模型.GetComponent<CharacterHangPoint>().SetLittleLegendCfg(小小英雄模型配置);
                _场景实体.OnTinyModelImplSync(小小英雄模型, 小小英雄模型配置);
                _场景实体.AdapterCameraPoint(TinySceneUIType.Warehouse, TinySceneCameraType.Center, 0.5f, true);
            }
            _场景实体.Active = true;
        }
    }
}

#endif