using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using UnityEngine;

public class TKEditorScreenMonobehaviour : MonoBehaviour
{
#if UNITY_EDITOR

    protected bool m_needAdapterCameraPos = false;
    protected int m_screenWidth;
    protected int m_screenHeight;
    protected Action onScreenChanged;

    private void Update()
    {
        if (m_needAdapterCameraPos)
        {
            m_needAdapterCameraPos = false;
            SetScreenImpl();
        }
    }

    public void SetScreenInEditor(int width, int height, Action onScreenChanged)
    {
        m_needAdapterCameraPos = true;
        m_screenWidth = width;
        m_screenHeight = height;
        this.onScreenChanged = onScreenChanged;
    }

    private void SetScreenImpl()
    {
        //重置分辨率
        ScreenResolutionSystem.Instance.InitRawSize(m_screenWidth, m_screenHeight);
        GameLOD.Instance.GeneratePhoneLODConfig(EDevicePower.EDP_Ultra);
        GameLOD.Instance.ChangeScreenResolution(GameLOD.Instance.ResolutionMode);

        NotchSizeImp.IsPad = false;
        NotchSizeImp.InitNotchSize();// = isPad;

        if (DeviceOrientationNotify.Instance != null)
            DeviceOrientationNotify.Instance.OnSizeChange();

        onScreenChanged?.Invoke();

        GameObject.Destroy(this);
    }
#endif
}

