using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using TKFrame;
using TriggerSystem;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using ZGame;
using ZGameChess;
using ZGameClient;

public class InGameBusUIPanel : UIPanel
{
    public static string modelName = string.Empty;
    public static string configName = string.Empty;

    #region TeamLeader

    [UIObject("TeamLeaderPanel")]
    public GameObject teamLeaderPanel
    {
        get;
        set;
    }

    [UIObject("ShowLeader")]
    public Button showLeader
    {
        get;
        set;
    }

    [UIObject("TeamLeader")]
    public ScrollRect teamLeaderScrollRect
    {
        get;
        set;
    }

    [UIObject("OneLeader")]
    public GameObject oneLeader
    {
        get;
        set;
    }

    [UIObject("OneLeaderItem")]
    public GameObject oneLeaderItem
    {
        get;
        set;
    }

    #endregion

    #region Action

    [UIObject("ActionPanel")]
    public GameObject actionPanel
    {
        get;
        set;
    }

    [UIObject("ShowAction")]
    public Button showAction
    {
        get;
        set;
    }

    [UIObject("Action")]
    public ScrollRect actionScrollRect
    {
        get;
        set;
    }

    [UIObject("OneAction")]
    public GameObject oneAction
    {
        get;
        set;
    }

    #endregion

    #region Moment

    [UIObject("MomentPanel")]
    public GameObject momentPanel
    {
        get;
        set;
    }

    [UIObject("ShowMoment")]
    public Button showMoment
    {
        get;
        set;
    }

    [UIObject("Moment")]
    public ScrollRect momentScrollRect
    {
        get;
        set;
    }

    [UIObject("OneMoment")]
    public GameObject oneMoment
    {
        get;
        set;
    }

    #endregion

    #region TeamLeaderType

    [UIObject("TeamLeaderTypePanel")]
    public GameObject teamLeaderTypePanel
    {
        get;
        set;
    }

    [UIObject("TeamLeaderType")]
    public Button teamLeaderTypeButton
    {
        get;
        set;
    }

    [UIObject("TeamLeaderTypeList")]
    public ScrollRect teamLeaderTypeScrollRect
    {
        get;
        set;
    }

    #endregion

    #region Search

    [UIObject("Search")]
    public Button searchButton
    {
        get;
        set;
    }

    [UIObject("SearchField")]
    public InputField searchField
    {
        get;
        set;
    }

    #endregion

    #region Trigger

    [UIObject("ShowTrigger")]
    public Button showTrigger
    {
        get;
        set;
    }

    [UIObject("EditorPanel")]
    public GameObject editorPanel
    {
        get;
        set;
    }

    #endregion

    [UIObject("SelectHero")]
    public GameObject selectHero
    {
        get;
        set;
    }

    [UIObject("ShowHeroNum")]
    public InputField playerCountInput
    {
        get;
        set;
    }

    [UIObject("DemoButton")]
    public Button demoButton
    {
        get;
        set;
    }


    [UIObject("ModelConfig")]
    public InputField modelConfigField
    {
        get;
        set;
    }

    [UIObject("PlayerConfig")]
    public InputField playerConfigField
    {
        get;
        set;
    }

    [UIObject("LoadNewHero")]
    public Button loadNewHeroButton
    {
        get;
        set;
    }

    [UIObject("MagicExpressionPanel/Panel/MagicExpId")]
    public InputField magicExpId
    {
        get;
        set;
    }

    [UIObject("MagicExpressionPanel/Panel/Button")]
    public Button magicExpUseBtn
    {
        get;
        set;
    }

    [UIObject("MagicExpressionPanel/Panel/SelfUseToggle")]
    public Toggle magicSelfUseToggle
    {
        get;
        set;
    }

    [UIObject("HidePanelBtn")]
    public Button HidePanelBtn { get; set; }

    [UIObject("ShowPanelBtn")]
    public Button ShowPanelBtn { get; set; }


    [UIObject("ToggleRecordMode")]
    public Toggle RecordModeToggle
    {
        get;
        set;
    }

    [UIObject("Debug_UI_Touch")]
    public Text Debug_UI_Touch
    {
        get;
        set;
    }

    [UIObject("ChangeMapInput")]
    public InputField ChangeMapInput
    {
        get;
        set;
    }

    [UIObject("ChangeMapButton")]
    public Button ChangeMapButton
    {
        get;
        set;
    }


    private List<bool> listStauts = new List<bool>();

    private TeamLeaderModel m_model;
    private TeamLeaderSoGame m_soGame;
    private SelectHeroAction m_selectHeroAction;

    private bool m_isDemo = false;

    private List<GameObject> m_allCamera = new List<GameObject>();

    private int m_oldNum = 1;
    private int m_oldLayer = 0;

    private const float gap = 0.005f;
    private const float xStart = 0.01f;
    private const float width = 0.22f;
    private const float height = 0.7f;
    private const float yStart = 0.2f;

    private List<OneLeader> m_teamLeaderActiveCache = new List<OneLeader>();
    private TKDictionary<int, OneLeader> m_teamLeaderDictionary = new TKDictionary<int, OneLeader>();

    private Rect viewportRect = new Rect(0, 0, 1, 1);
    private Vector3 camPos = new Vector3(0, -16.2f, 15.6f);
    private Vector3 m_oldCamPos = Vector3.zero;

    private List<int> m_hideLayer = new List<int>() { 28, 29, 30 };

    private List<GameObject> m_allContent = new List<GameObject>();

    private HookButtonAction[] _hookBtnActions;

    private UnityEngine.Coroutine _hideCoroutine;

    public override IEnumerator OnInit()
    {
        m_model = new TeamLeaderModel();
        m_soGame = TeamLeaderSoGame.Instance;
        m_soGame.isActive = true;

        m_allContent.Add(actionScrollRect.gameObject);
        m_allContent.Add(momentScrollRect.gameObject);
        m_allContent.Add(teamLeaderTypeScrollRect.gameObject);
        m_allContent.Add(editorPanel);

        ShowGameObject(actionScrollRect.gameObject);

        InitButton();
        yield return null;

        InitTeamLeaderContent(m_model.allTeamLeader);
        yield return null;

        InitTeamLeaderAction(TeamLeaderModel.moveType);
        yield return null;

        InitTeamLeaderTriggerMoment();
        yield return null;

        InitHookButtons();
        yield return null;

        GameUtil.SetScaleToZero(teamLeaderTypePanel, true);

        ACGEventManager.Instance.AddEventListener("TKGraphicRaycaster.Raycast", (data) =>
        {
            var list = (List<RaycastResult>)data.objData;
            StringBuilder sb = new StringBuilder();
            for (int i = list.Count - 1; i >= 0; --i)
            {
                if (i != 0) sb.Append("\n");
                var first = list[i];
                var curObj = first.gameObject;
                Transform trans = curObj.transform;
                while (trans.parent != null)
                {
                    sb.Append(trans.name).Append("/");
                    trans = trans.parent;
                }
            }

            Debug_UI_Touch.text = sb.ToString();
        });

        ChangeMapInput.text = BattleMapManager.Instance.GetCurrentBattleMap().MapId.ToString();
        ChangeMapButton.onClick.AddListener(OnChangeMapClick);
    }

    float changeMapTime = 0f;
    private void OnChangeMapClick()
    {
        if (Time.time - changeMapTime < 0.5f)
            return;
        changeMapTime = Time.time;
        int mapId = int.Parse(ChangeMapInput.text);
        var myPlayer = ChessModelManager.Instance.GetBattleModel().GetMyPlayerModel();
        if (myPlayer != null)
        {
            myPlayer.Playerinfo.stTAC_GameTinyData.iMapId = mapId;
        }

        // 还得改逻辑层的
        TeamLeaderSoGame.Instance.playerInfoList[0].stTAC_GameTinyData.iMapId = mapId;

        int curMapId = BattleMapManager.Instance.GetCurrentBattleMap().MapId;
        if (mapId != curMapId)
        {
            StartCoroutine(CheckLoadMap(mapId));
        }
    }
    private IEnumerator CheckLoadMap(int mapId)
    {
        var sdfCache = MicroMgr.Instance.GetMicroObj().m_sdfCache;
        sdfCache.PreInit(new List<int>() { mapId }, false);
        yield return sdfCache.Preload();

        var mapName = BattleMapManager.Instance.GetMapNameById(mapId);
        if (mapName.Filled())
        {
            BattleMapManager.Instance.LoadMap(mapId);
            while (!BattleMapManager.Instance.IsAllLoaded())
                yield return null;
            var map = BattleMapManager.Instance.GetMapDataById(mapId);
            if (map != null)
                map.InitSDFData();
            var battleField = MicroMgr.Instance.GetMicroObj().CSoGame.chessBattleCore.GetBattleFieldByChairId(0);
            battleField.SwitchMap(mapName, false);
        }
        else
        {
            UIOverlay.Instance.ShowCommonTips("棋盘ID不存在");
        }
    }

    void InitHookButtons()
    {
        _hookBtnActions = transform.GetComponentsInChildren<HookButtonAction>(true);
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        m_soGame.isActive = false;
    }

    private void OnGUI()
    {
        if (Input.GetKeyDown(KeyCode.H))
        {
            ClickOnHide();
        }
        else if (Input.GetKeyDown(KeyCode.S))
        {
            ClickOnShow();
        }
    }

    #region 初始化

    private void InitButton()
    {
        m_selectHeroAction = selectHero.AddComponent<SelectHeroAction>();
        m_selectHeroAction.choosenHero = delegate (int moveStep)
        {
            m_soGame.SendCommand("更换玩家", moveStep);
            m_selectHeroAction.SetId(m_soGame.currentId, m_soGame.GetCurrentPlayerTabId());
        };

        demoButton.onClick.AddListener(delegate ()
        {
            if (m_isDemo)
            {
                DemoHide();
            }
            else
            {
                DemoShow();
            }
        });

        playerCountInput.onValueChanged.AddListener(OnChangePlayerCount);

        showLeader.onClick.AddListener(delegate ()
        {
            if (teamLeaderScrollRect.gameObject.activeSelf)
                teamLeaderScrollRect.gameObject.SetActive(false);
            else
                teamLeaderScrollRect.gameObject.SetActive(true);
        });

        showAction.onClick.AddListener(delegate ()
        {
            if (actionScrollRect.gameObject.activeSelf)
                actionScrollRect.gameObject.SetActive(false);
            else
                ShowGameObject(actionScrollRect.gameObject);
        });

        teamLeaderTypeButton.onClick.AddListener(delegate ()
        {
            if (teamLeaderTypeScrollRect.gameObject.activeSelf)
                teamLeaderTypeScrollRect.gameObject.SetActive(false);
            else
                ShowGameObject(teamLeaderTypeScrollRect.gameObject);
        });

        showMoment.onClick.AddListener(delegate ()
        {
            if (momentScrollRect.gameObject.activeSelf)
                momentScrollRect.gameObject.SetActive(false);
            else
            {
                ShowGameObject(momentScrollRect.gameObject);
            }
        });

        showTrigger.onClick.AddListener(delegate ()
        {
            if (editorPanel.activeSelf)
                editorPanel.SetActive(false);
            else
            {
                ShowGameObject(editorPanel);
            }
        });

        HidePanelBtn.onClick.AddListener(ClickOnHide);

        ShowPanelBtn.onClick.AddListener(ClickOnShow);

#if UNITY_EDITOR && !LOGIC_THREAD
        loadNewHeroButton.onClick.AddListener(delegate ()
        {
            TeamLeaderPanel.modelName = modelConfigField.text;
            TeamLeaderPanel.configName = playerConfigField.text;
            m_soGame.SendCommand("编辑新英雄", 0);
        });
#endif

        searchButton.onClick.AddListener(delegate ()
        {
            string content = searchField.text;
            TKDictionary<int, List<TeamLeaderMsg>> result = m_model.SearchData(content);
            BuildTeamLeaderContent(result);
        });

        magicExpUseBtn.onClick.AddListener(OnUseMagicExp);


        RecordModeToggle.onValueChanged.AddListener(OnRecordModeChanged);






    }



    private void ClickOnHide()
    {
        listStauts.Clear();
        foreach (Transform item in teamLeaderPanel.transform.parent)
        {
            if (item.name == "ShowPanelBtn")
            {
                item.gameObject.SetActive(true);
                continue;
            }
            listStauts.Add(item.gameObject.activeSelf);
            item.gameObject.SetActive(false);
        }
        //UIOverlay.Instance.ShowCommonTips("点击左上角空白区域显示");
    }

    private void ClickOnShow()
    {
        int index = 0;
        foreach (Transform item in teamLeaderPanel.transform.parent)
        {
            if (item.name == "ShowPanelBtn")
            {
                item.gameObject.SetActive(false);
                continue;
            }
            if (index >= 0 && index < listStauts.Count)
            {
                item.gameObject.SetActive(listStauts[index]);
                index++;
            }
            item.gameObject.SetActive(true);
        }
    }
    private void ShowGameObject(GameObject obj)
    {
        obj.SetActive(true);
        foreach (GameObject content in m_allContent)
        {
            if (content != obj)
            {
                content.SetActive(false);
            }
        }
    }

    private void OnChangePlayerCount(string value)
    {
        int num = 1;
        if (!int.TryParse(value, out num))
        {
            return;
        }

        if (num == 0)
            return;

        m_soGame.ChangePlayerNum(num);
        m_selectHeroAction.SetId(m_soGame.currentId, m_soGame.GetCurrentPlayerTabId());

        //simonlai 魔法道具测试需求，遇无法新增enemy 的bug。临时解决（有他人计划在修）。如若疏漏忘记删除提交了，请将下面1行代码恢复（延迟去除）。
        // ToolKit.delayCall(0.5f, () =>
        // {
        m_soGame.SetVS();
        // });

    }



    private void InitTeamLeaderContent(TKDictionary<int, List<TeamLeaderMsg>> datas)
    {
        oneLeader.AddComponent<OneLeader>();
        oneLeaderItem.AddComponent<OneLeaderItem>();
        foreach (var oneData in datas)
        {
            GameObject obj = GameObject.Instantiate<GameObject>(oneLeader, teamLeaderScrollRect.content);
            OneLeader temp = obj.GetComponent<OneLeader>();
            temp.SetData(oneData, oneLeaderItem, ChangeHero);
            ChessUtil.MakeTransformIdentity(obj.transform);

            m_teamLeaderDictionary.Add(oneData.Key, temp);
            m_teamLeaderActiveCache.Add(temp);
        }

        oneLeader.SetActive(false);
    }

    private void InitTeamLeaderAction(string[] datas)
    {
        oneAction.AddComponent<OneAction>();
        foreach (string actionName in datas)
        {
            GameObject obj = GameObject.Instantiate<GameObject>(oneAction, actionScrollRect.content);
            OneAction temp = obj.GetComponent<OneAction>();
            temp.actionName.text = actionName;
            temp.onClick = ChangeAction;
            ChessUtil.MakeTransformIdentity(obj.transform);
        }
        oneAction.SetActive(false);
    }

    private void InitTeamLeaderTriggerMoment()
    {
        oneMoment.AddComponent<OneMoment>();
        foreach (E_TRIGGER_MOMENT e in Enum.GetValues(typeof(E_TRIGGER_MOMENT)))
        {
            GameObject obj = GameObject.Instantiate<GameObject>(oneMoment, momentScrollRect.content);
            OneMoment temp = obj.GetComponent<OneMoment>();
            temp.triggerMoment = e;
            temp.onClick = ChangeMoment;
            ChessUtil.MakeTransformIdentity(obj.transform);
        }
        oneMoment.SetActive(false);
    }

    #endregion

    private void BuildTeamLeaderContent(TKDictionary<int, List<TeamLeaderMsg>> datas)
    {
        for (int i = 0; i < m_teamLeaderActiveCache.Count; i++)
        {
            OneLeader oneLeader = m_teamLeaderActiveCache[i];
            oneLeader.gameObject.SetActive(false);
        }

        m_teamLeaderActiveCache.Clear();

        foreach (var oneData in datas)
        {
            OneLeader result = null;
            if (m_teamLeaderDictionary.TryGetValue(oneData.Key, out result))
            {
                result.levelContent.SetActive2(false);
                result.gameObject.SetActive2(true);

                m_teamLeaderActiveCache.Add(result);
            }
        }
    }

    public void InitPanel()
    {
        m_selectHeroAction.SetId(m_soGame.currentId, m_soGame.GetCurrentPlayerTabId());
        playerCountInput.text = m_soGame.count.ToString();
    }

    #region 点击动作

    private void ChangeHero(TeamLeaderMsg data)
    {
        TeamLeaderPanel.configName = string.Empty;
        TeamLeaderPanel.modelName = string.Empty;

        m_soGame.SendCommand("更换英雄", data.client);

        modelConfigField.text = data.client.sPreviewResource;
        playerConfigField.text = data.client.sTeamLeaderCfg;

        m_selectHeroAction.SetId(m_soGame.currentId, m_soGame.GetCurrentPlayerTabId());
    }

    private void ChangeAction(string name)
    {
        m_soGame.SendCommand(name);
    }

    private void ChangeMoment(E_TRIGGER_MOMENT moment)
    {
        m_soGame.PlayTriggerMoment(moment);
    }

    private void ChangeHeroType(List<TACG_Item_Client> data)
    {
        int count = data.Count;
        m_soGame.ChangePlayerNum(3);
        for (int i = 0; i < 3; i++)
        {
            int index = i;
            if (i >= count)
            {
                index = count - 1;
            }
            m_soGame.ChangeHero(i, data[index]);
        }

        for (int i = 0; i < 3; i++)
        {
            int layer = m_hideLayer[i];
            GameUtil.SetLayerRecursive(m_soGame.showPlayerList[i].gameObject, layer);
        }
    }

    #endregion

    private void DemoShow()
    {
        GameUtil.SetScaleToZero(teamLeaderPanel, true);
        GameUtil.SetScaleToZero(teamLeaderTypePanel, false);

        teamLeaderTypeScrollRect.gameObject.SetActive(true);
        actionScrollRect.gameObject.SetActive(false);

        GameObject camObj = Camera.main.gameObject;

        Transform parent = camObj.transform.parent;
        m_oldCamPos = parent.position;
        parent.position = camPos;

        Rect rect = Camera.main.rect;
        rect.x = xStart;
        rect.y = yStart;
        rect.width = width;
        rect.height = height;
        Camera.main.rect = rect;

        m_oldLayer = Camera.main.cullingMask;

        for (int i = 0; i < 2; i++)
        {
            int n = i + 1;
            GameObject obj = GameObject.Instantiate<GameObject>(camObj, camObj.transform.parent);
            Camera camera = obj.GetComponent<Camera>();

            rect.x = xStart + (width + gap) * n;
            camera.rect = rect;

            obj.GetComponent<PhysicsRaycaster>().enabled = false;

            camera.tag = "Untagged";

            HideLayer(camera, n);

            m_allCamera.Add(obj);
        }

        HideLayer(Camera.main, 0);

        m_oldNum = m_soGame.count;
        m_soGame.ChangePlayerNum(3);
        m_selectHeroAction.SetId(m_soGame.currentId, m_soGame.GetCurrentPlayerTabId());

        for (int i = 0; i < 3; i++)
        {
            int layer = m_hideLayer[i];
            GameUtil.SetLayerRecursive(m_soGame.showPlayerList[i].gameObject, layer);
        }

        m_isDemo = true;
        m_soGame.SetDelta(m_isDemo);
    }

    private void DemoHide()
    {
        GameUtil.SetScaleToZero(teamLeaderPanel, false);
        GameUtil.SetScaleToZero(teamLeaderTypePanel, true);
        //GameUtil.SetScaleToZero(MonitorBehaviour.instance.gameObject, false);

        teamLeaderTypeScrollRect.gameObject.SetActive(false);
        actionScrollRect.gameObject.SetActive(true);

        foreach (GameObject obj in m_allCamera)
        {
            GameObject.Destroy(obj);
        }
        m_allCamera.Clear();

        Camera.main.rect = viewportRect;
        Camera.main.cullingMask = m_oldLayer;
        Camera.main.transform.parent.position = m_oldCamPos;

        m_soGame.ChangePlayerNum(m_oldNum);
        m_selectHeroAction.SetId(m_soGame.currentId, m_soGame.GetCurrentPlayerTabId());

        m_isDemo = false;

        m_soGame.SetDelta(m_isDemo);
    }

    private void HideLayer(Camera camera, int n)
    {
        for (int i = 0; i < m_hideLayer.Count; i++)
        {
            if (i != n)
            {
                camera.cullingMask &= ~(1 << m_hideLayer[i]);
            }
        }
    }

    private void OnUseMagicExp()
    {
        string msg = magicExpId.text;
        if (msg.Filled())
        {
            if (int.TryParse(msg, out int value))
            {
                var battleModel = ChessModelManager.Instance.GetBattleModel();
                int curPlayerId = 0;
                if (magicSelfUseToggle.isOn)
                {
                    curPlayerId = battleModel.MyPlayerId;
                }
                else
                {
                    curPlayerId = battleModel.GetMyPlayerModel().EnemyPlayerID;
                }

                ChessBattleGlobal.Instance.ChessPlayerCtrl.ShowMagicExpression(curPlayerId, value);
            }
        }
    }

    void OnRecordModeChanged(bool isOn)
    {
        if (isOn != m_model.isRecordVideoMode)
        {
            if (!m_model.isRecordVideoMode)
            {
                if (null != _hookBtnActions && _hookBtnActions.Length > 0)
                {
                    for (int i = 0, imx = _hookBtnActions.Length; i < imx; i++)
                    {
                        _hookBtnActions[i].BindActionAhead(ClickOnHide, 1);
                    }
                }
            }
            else
            {
                if (null != _hookBtnActions && _hookBtnActions.Length > 0)
                {
                    for (int i = 0, imx = _hookBtnActions.Length; i < imx; i++)
                    {
                        _hookBtnActions[i].UnBindActionAhead();
                    }
                }
            }

            m_model.isRecordVideoMode = isOn;
        }
    }
}
