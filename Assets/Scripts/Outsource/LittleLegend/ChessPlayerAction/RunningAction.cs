using Lucifer.ActCore;
using UnityEngine;

namespace ZGameChess
{
    public class RunningAction : IActionState
    {
        private float m_speed;
        private ChessPlayerUnit m_player;
        
        //private float totalTime;
        //private int preMoveStep = -1;

        public void End(ActionStateController controller)
        {
            //preMoveStep = -1;
            //totalTime = 0;
        }

        public void Start(ActionStateController controller)
        {
            //记录当前逻辑帧
            //preMoveStep = -1;
            //totalTime = 0;

            m_player = controller.playerUnit;
            m_speed = m_player.speed;
            m_player.SetMoveMentState(PlayerMoveMessage.Run);
            m_player.PrintLog("Action Run");
        }

        public void Update(ActionStateController controller, float deltaTime)
        {
            UpdatePos(controller, deltaTime);
            m_player.FallToGround();
        }

        private void UpdatePos(ActionStateController controller, float deltaTime)
        {
            //var curLogicFrame = BattleCommonNet.GetBattleRunFrame();
            m_player.SimpleMove(m_speed, deltaTime, controller.moveScale);
        }

        public void SyncState(MovingState movingState)
        {
            
        }

        public void PlayAnim()
        {
            if (m_player.GetAttackRunAction(out string runAction))
            {
                m_player.PlayAnim(runAction);
            }
            else
            {
                m_player.PlayAnim("run_fast");
            }
        }

        public bool IsLoop()
        {
            return true;
        }
    }
}