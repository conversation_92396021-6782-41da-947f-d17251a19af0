using PBRTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using UnityEngine;
using UnityEngine.UI;

namespace ZGameChess
{
    public enum TinySceneUIType
    {
        None = 0,
        Rank = 1,           // 排行榜
        Mall = 2,           // 商城
        Warehouse = 3,      // 藏品
        BattlePass = 4,     // BP
        UserInfo = 5,       // 个人信息
        BattleEnd_First = 6,// 结算第一名
        BattleEnd_BattlePass = 7, // 结算BP
        GetNewTiny = 8,     // 获得新英雄
        SeasonLottery = 9,  // 联盟抽奖
        JReward = 10,       // J科技
        RewardPool = 11,     // 奖池x
        CompetitionStore = 12,     // 兑换商店x
        CompetitionPass = 13,     // 赛事宝典x
        Recommand = 15,           // 推荐页
        CardCollectReward = 16,           // 魔典赛季奖励
        ParallelBattlePass = 17,           // 并行宝典

        Max = 18,           // 请添加新界面的时候把max的数值也增加一个
    }

    public enum TinyEnterAction
    {
        Enter,
        Idle,
        Fly,
    }

    public enum TinySceneCameraType
    {
        Center,
        Right,
    }

    public class TinyPreviewParam
    {
        public TinySceneUIType uiType = TinySceneUIType.None;
        public Vector3 spawnPos = new Vector3(1000, 1000, 1000);       // 尝试减小一下出生点 太大了动作会抖
        public TinyEnterAction enterAction = TinyEnterAction.Idle;
        public TinySceneCameraType cameraType = TinySceneCameraType.Right;
        public float axes = 0.5f;       // 小小英雄中轴线
        public bool playSceneSound = true;

        public TinyPreviewParam(TinySceneUIType uiType)
        {
            this.uiType = uiType;
        }
    }

    /// <summary>
    /// 小小英雄展示统一用这个单例管理 避免不同系统产生多个拷贝的情况
    /// </summary>
    public class TinyPreviewSceneManager : Singleton<TinyPreviewSceneManager>
    {
        protected GameObject m_workspaceRoot = null;
        protected TinyModelPreviewSceneService m_service = null;
        protected bool m_inited = false;
        protected BackStack m_currentStack = null;

        protected TKDictionary<int, TinyPreviewParam> m_uiTypeOpenParams = new TKDictionary<int, TinyPreviewParam>();
        protected TinyPreviewParam m_defaultOpenParam = new TinyPreviewParam(TinySceneUIType.None);

        protected TKStack<BackStack> m_backStack = new TKStack<BackStack>();

        public bool Inited => m_inited;

        protected class BackStack
        {
            public int tinyId;
            public TinySceneUIType uiType;
            public Action<TinyModelPreviewSceneInst> onTinyLoaded = null;
            public Action<TinyModelPreviewSceneInst> onSceneLoaded = null;
            public Action<CustomLightingTool> onSetCustomLighting = null;
            public Action<AnimatorStateInfo> onTinyActionChanged = null;
            public bool useTKPPS;
        }

        protected void InitOpenParams()
        {
            List<TinyPreviewParam> list = new List<TinyPreviewParam>()
            {
                new TinyPreviewParam(TinySceneUIType.Rank)
                {
                    enterAction = TinyEnterAction.Enter,
                    cameraType = TinySceneCameraType.Right,
                    axes = 0.75f
                },
                new TinyPreviewParam(TinySceneUIType.Mall)
                {
                    enterAction = TinyEnterAction.Enter,
                    cameraType = TinySceneCameraType.Center,
                    axes = 0.75f
                },
                new TinyPreviewParam(TinySceneUIType.Warehouse)
                {
                    enterAction = TinyEnterAction.Enter,
                    cameraType = TinySceneCameraType.Center,
                    axes = 0.5f
                },
                new TinyPreviewParam(TinySceneUIType.BattlePass)
                {
                    enterAction = TinyEnterAction.Idle,
                    cameraType = TinySceneCameraType.Center,
                    axes = 0.5f
                },
                new TinyPreviewParam(TinySceneUIType.UserInfo)
                {
                    enterAction = TinyEnterAction.Enter,
                    cameraType = TinySceneCameraType.Right,
                    axes = 0.75f
                },
                new TinyPreviewParam(TinySceneUIType.BattleEnd_First)
                {
                    enterAction = TinyEnterAction.Enter,
                    cameraType = TinySceneCameraType.Center,
                    axes = 0.5f,
                    playSceneSound = false
                },
                new TinyPreviewParam(TinySceneUIType.BattleEnd_BattlePass)
                {
                    enterAction = TinyEnterAction.Enter,
                    cameraType = TinySceneCameraType.Right,
                    axes = 0.7f,
                    playSceneSound = false
                },
                new TinyPreviewParam(TinySceneUIType.GetNewTiny)
                {
                    enterAction = TinyEnterAction.Enter,
                    cameraType = TinySceneCameraType.Center,
                    axes = 0.5f
                },
                new TinyPreviewParam(TinySceneUIType.SeasonLottery)
                {
                    enterAction = TinyEnterAction.Enter,
                    cameraType = TinySceneCameraType.Center,
                    axes = 0.5f
                },
                new TinyPreviewParam(TinySceneUIType.JReward)
                {
                    enterAction = TinyEnterAction.Enter,
                    cameraType = TinySceneCameraType.Center,
                    axes = 0.5f
                },
                new TinyPreviewParam(TinySceneUIType.RewardPool)
                {
                    enterAction = TinyEnterAction.Enter,
                    cameraType = TinySceneCameraType.Right,
                    axes = 0.65f
                },
                new TinyPreviewParam(TinySceneUIType.CompetitionPass)
                {
                    enterAction = TinyEnterAction.Idle,
                    cameraType = TinySceneCameraType.Right,
                    axes = 0.4f
                },
                new TinyPreviewParam(TinySceneUIType.Recommand)
                {
                    enterAction = TinyEnterAction.Enter,
                    cameraType = TinySceneCameraType.Center,
                    axes = 0
                },
                new TinyPreviewParam(TinySceneUIType.CardCollectReward)
                {
                    enterAction = TinyEnterAction.Enter,
                    cameraType = TinySceneCameraType.Right,
                    axes = 0.75f,
                    playSceneSound = false

                },
                new TinyPreviewParam(TinySceneUIType.ParallelBattlePass)
                {
                    enterAction = TinyEnterAction.Idle,
                    cameraType = TinySceneCameraType.Center,
                    axes = 0.5f,
                    playSceneSound = false
                },
            };
            m_uiTypeOpenParams.Clear();
            for (int i = 0; i < list.Count; ++i)
            {
                m_uiTypeOpenParams.Add((int)list[i].uiType, list[i]);
            }
        }

        public void Initialize()
        {
            if (m_inited)
            {
                Diagnostic.Warn("TinyPreviewSceneManager.Initialize m_inited = true");
                return;
            }

            InitOpenParams();

            Dispose();

            m_workspaceRoot = new GameObject("TinyPreviewScene_WorkSpace");
            ChessUtil.MakeTransformIdentity(m_workspaceRoot.transform);
            m_service = m_workspaceRoot.TryGetComponent<TinyModelPreviewSceneService>();
            m_service.OnDestoryCallback = Dispose;

            SetEvent(true);

            Diagnostic.Log("TinyPreviewSceneManager.Initialize");
            m_inited = true;
        }

        private void SetEvent(bool add)
        {
            StageRunner stageRunner = Services.GetService<IStageRunner>() as StageRunner;
            if (stageRunner != null)
            {
                if (add)
                {
                    //if (stageRunner.Transition != null)
                    stageRunner.StageInitCallback += BindStage;

                    //stageRunner.StagePreEnterCallBack += BindStage;
                }
                else
                {
                    //stageRunner.StagePreEnterCallBack -= BindStage;
                    //if (stageRunner.Transition != null)
                    stageRunner.StageInitCallback -= BindStage;
                }
            }
        }

        public void Dispose()
        {
            Diagnostic.Log("TinyPreviewSceneManager.Dispose");

            m_inited = false;

            SetEvent(false);

            m_currentStack = null;

            if (m_workspaceRoot != null)
            {
                GameObject.Destroy(m_workspaceRoot);
                m_workspaceRoot = null;
            }

            m_service = null;
            //TinyPreviewSceneManager.Destroy();
        }

        public bool IsLoading()
        {
            var service = GetService();
            if (service != null)
            {
                return service.IsLoading();
            }
            return false;
        }

        public virtual void NotifyStageStep(STAGE_STEP step)
        {
            if (step == STAGE_STEP.STAGE_STEP_LOAD_CONTENT)
            {
                // 准备切换新界面
                StageRunner stageRunner = Services.GetService<IStageRunner>() as StageRunner;
                if (stageRunner != null)
                {
                    var stage = stageRunner.CurrentStage;
                    BindStage(stage);
                }
            }
        }

        public void BindStage(IStage stage)
        {
            if (m_service != null)
            {
                m_service.BindStage(stage);
            }
        }

        public TinyModelPreviewSceneService GetService()
        {
            if (!m_inited)
            {
                Initialize();
            }

            return m_service;
        }

        public TinyModelPreviewSceneInst GetCurPreview()
        {
            var service = GetService();
            if (service != null)
            {
                return service.GetCurPreview();
            }
            return null;
        }
#if !OUTSOURCE
        public void Load(int tinyId)
        {
            var service = GetService();
            if (service != null)
            {
                service.Load(tinyId);
            }
        }
#endif
        public int Show(int tinyId, TinySceneUIType uiType,
            Action<TinyModelPreviewSceneInst> onTinyLoaded = null,
            Action<TinyModelPreviewSceneInst> onSceneLoaded = null,
            Action<CustomLightingTool> onSetCustomLighting = null,
            Action<AnimatorStateInfo> onTinyActionChanged = null,
            bool useTKPPS = true, bool recordToBackStack = true, bool forceIdle = false, bool forceReshow = false)
        {
            var service = GetService();
            if (service != null)
            {
                if (m_currentStack != null)
                {
                    m_backStack.Push(m_currentStack);
                }

                if (recordToBackStack)
                {
                    m_currentStack = new BackStack()
                    {
                        tinyId = tinyId,
                        uiType = uiType,
                        onTinyLoaded = onTinyLoaded,
                        onSceneLoaded = onSceneLoaded,
                        onSetCustomLighting = onSetCustomLighting,
                        onTinyActionChanged = onTinyActionChanged,
                        useTKPPS = useTKPPS,
                    };
                }


                if (!m_uiTypeOpenParams.TryGetValue((int)uiType, out TinyPreviewParam param))
                    param = m_defaultOpenParam;
                bool switchTinyScene = service.Show(tinyId, param.spawnPos.x, param.spawnPos.y, param.spawnPos.z, onTinyLoaded, onSceneLoaded, onSetCustomLighting, onTinyActionChanged, useTKPPS, forceReshow);

                var preview = service.GetCurPreview();
                if (preview != null)
                {
                    if (param.enterAction == TinyEnterAction.Idle || forceIdle)
                        preview.SetAlwayIdle();
                    else if (param.enterAction == TinyEnterAction.Fly)
                        preview.SetAlwayFly();
                    else
                        preview.SetActionToEnter();

                    preview.AdapterCameraPoint(param.uiType, param.cameraType, param.axes, param.enterAction != TinyEnterAction.Enter && !switchTinyScene);

                    if (!switchTinyScene)
                        preview.UpdateBGList();

                    if (param.playSceneSound)
                        preview.OpenSceneSound();
                    else
                        preview.CloseSceneSound();
                }

                return switchTinyScene == false ? 1 : 0;
            }
            return -1;
        }

        public void BackToLast()
        {
            if (m_backStack.Count > 0)
            {
                var back = m_backStack.Pop();
                Show(back.tinyId, back.uiType, back.onTinyLoaded, back.onSceneLoaded, back.onSetCustomLighting, back.onTinyActionChanged, back.useTKPPS);
            }
            else
            {
                Hide();
            }
        }

        public void Hide()
        {
            m_backStack.Clear();
            m_currentStack = null;

            var service = GetService();
            if (service != null)
            {
                service.Hide();
            }
        }

        public void RotateItem(float delta)
        {
            var service = GetService();
            if (service != null)
            {
                service.RotateItem(delta);
            }
        }
    }
}
