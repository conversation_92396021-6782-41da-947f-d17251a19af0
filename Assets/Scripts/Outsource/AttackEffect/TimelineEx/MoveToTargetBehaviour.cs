using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Playables;

namespace TimelineEx
{
    public class MoveToTargetBehaviour : PlayableBehaviour
    {
        public Transform source;
        public Transform target;

        public bool moveToTarget = true;
        public bool lookAtTarget = false;
        public bool applyRootMotion = false;

        private float percent = 0;
        private Vector3 sourcePosition;

        public override void OnBehaviourPlay(Playable playable, FrameData info)
        {
            base.OnBehaviourPlay(playable, info);

            if (source != null)
            {
                sourcePosition = source.position;
            }
        }

        /// <summary>
        /// timeLine 每次更新都会执行此方法
        /// </summary>
        /// <param name="playable"></param>
        /// <param name="info"></param>
        /// <param name="playerData"></param>
        public override void ProcessFrame(Playable playable, FrameData info, object playerData)
        {
            base.ProcessFrame(playable, info, playerData);

            if (target == null)
                return;

            Transform transform = playerData as Transform;
            if (transform == null)
                return;

            double duration = playable.GetDuration();
            double time = playable.GetTime();
            percent = (float)(time / duration);

            //float currentPercent = 0;
            //float totalWeight = 0;
            //int inputCount = playable.GetInputCount();
            //for (int i = 0; i < inputCount; i++)
            //{
            //    float inputWeight = playable.GetInputWeight(i);
            //    if (inputWeight > 0f)
            //    {
            //        ScriptPlayable<MoveToTargetBehaviour> inputPlayable =
            //            (ScriptPlayable<MoveToTargetBehaviour>)playable.GetInput(i);
            //        MoveToTargetBehaviour input = inputPlayable.GetBehaviour();

            //        currentPercent = inputWeight * input.percent;
            //        totalWeight += inputWeight;
            //    }
            //}

            var animator = transform.GetComponent<Animator>();
            if (animator != null)
                animator.applyRootMotion = applyRootMotion;

            if (moveToTarget)
            {
                transform.position = Vector3.Lerp(sourcePosition, target.position, percent /** (1f - totalWeight) + currentPercent*/);
            }

            if (lookAtTarget)
            {
                transform.LookAt(target);
            }
        }
    }
}
