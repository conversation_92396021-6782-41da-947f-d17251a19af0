using Cinemachine;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TimelineEx;
using TKFrame;
using UnityEngine;
using UnityEngine.Playables;
using ZGameChess;

public class ChessViewAttackTimeline
{
    protected ChessViewAttackTimelineUnit m_unit;
    protected ChessViewAttackUnit m_owner;

    protected GameObject m_timelineInst;
    protected int endTrigger;
    protected PlayableDirector m_director;
    protected TKDictionary<string, UnityEngine.Object> m_tracks = new TKDictionary<string, UnityEngine.Object>();

    protected List<CinemachineCameraParamSync> m_cameraParamSyncList = new List<CinemachineCameraParamSync>();
    protected List<ChessAttackBodyCtrl> m_attackBodyCtrlList = new List<ChessAttackBodyCtrl>();
    protected List<ChessAttackBindCtrl> m_attackBindList = new List<ChessAttackBindCtrl>();
    protected CinemachineVirtualCamera m_defaultVCam;

    protected List<GameObject> m_activeGameObjects = new List<GameObject>();
    protected List<GameObject> m_deactiveGameObjects = new List<GameObject>();

    private List<Camera> m_allCustomCameras = new List<Camera>();

    public bool Enable { get; private set; } = false;
    public bool IsPlaying
    {
        get
        {
            if (m_director != null)
                return m_director.state == PlayState.Playing;
            else
                return false;
        }
    }

    /// <summary>
    /// 是否有自定义摄像头，
    /// 如果返回true，说明这个斩杀是有独立场景的。 (不准确，也许这个场景有相机，但是美术没用，始终隐藏着)
    /// 如果返回false，说明这个斩杀是直接在棋盘上播放的。
    /// </summary>
    public bool HasCustomCamera
    {
        get
        {
            if (m_timelineInst == null)
                return false;
            return m_allCustomCameras.Count > 0;
        }
    }

    public ChessViewAttackTimeline(ChessViewAttackTimelineUnit unit)
    {
        m_unit = unit;
    }

    public void Init(GameObject timeline, ChessViewAttackUnit owner, int endTrigger)
    {
        if (timeline == null)
        {
            Enable = false;
            //IsPlaying = false;
            return;
        }
        this.endTrigger = endTrigger;
        m_owner = owner;
        m_timelineInst = GameObject.Instantiate(timeline, owner.transform);
        ChessUtil.MakeTransformIdentity(m_timelineInst.transform);

        m_director = m_timelineInst.GetComponent<PlayableDirector>();
        m_director.stopped += OnTimelineStop;
        InitTracks();

        var gfxRoot = m_timelineInst.GetComponent<GfxFramework.GfxRoot>();
        if (gfxRoot != null && gfxRoot.m_playFinishedHide)
        {
            gfxRoot.OnGfxPlayEnd = GFXPlayEndHandler;
        }

        m_attackBindList.Clear();
        m_timelineInst.GetComponentsInChildren<ChessAttackBindCtrl>(true, m_attackBindList);

        m_attackBodyCtrlList.Clear();
        m_timelineInst.GetComponentsInChildren<ChessAttackBodyCtrl>(true, m_attackBodyCtrlList);

        m_cameraParamSyncList.Clear();
        m_timelineInst.GetComponentsInChildren<CinemachineCameraParamSync>(true, m_cameraParamSyncList);

        // 离屏渲染优化
        if (ChessAttackOffscreenCamera.Open)
        {
            var cameras = m_timelineInst.GetComponentsInChildren<Camera>(true);
            foreach (var camera in cameras)
            {
                //if (camera.GetComponent<TKPostProcessingStack>() != null)
                {
                    camera.gameObject.TryGetComponent<ChessAttackOffscreenCamera>().SetOnActiveChanged(OnOffscreenRenderActiveChanged);
                }
            }
        }

        var st = m_timelineInst.GetComponentInChildren<PBRTools.SceneTool>(true);
        if (st != null)
            st.ApplyAll();

        var defaultVCam = m_timelineInst.transform.Find("default_vcam");
        if (defaultVCam != null)
            m_defaultVCam = defaultVCam.GetComponent<CinemachineVirtualCamera>();

        var curStage = SystemManager.getInstance().GetStage() as BaseStage;
        if (curStage != null)
        {
            bool canSet = curStage is ChessBattleStage
                || curStage is TinyAttackSceneUIStage;
            if (!canSet)
            {
                // 降低一下相机层级 避免在局外预览压UI的情况
                var cameras = m_timelineInst.GetComponentsInChildren<Camera>(true);
                foreach (var item in cameras)
                {
                    if (item.depth >= 5)
                        item.depth = 4;
                }
            }
        }

        m_allCustomCameras.Clear();
        m_timelineInst.GetComponentsInChildren<Camera>(true, m_allCustomCameras);

        InitGameObjects();

        m_timelineInst.SetActive(false);
        Enable = true;
    }

    private void GFXPlayEndHandler(GfxFramework.GfxRoot root)
    {
        if (m_director != null)
            m_director.Stop();
    }

    private HashSet<int> m_activeCameras = new HashSet<int>();

    private void OnOffscreenRenderActiveChanged(GfxFramework.GfxOffScreenRenderCamera camera, bool active)
    {
        int instId = camera.GetInstanceID();
        if (active)
        {
            if (!m_activeCameras.Contains(instId))
                m_activeCameras.Add(instId);
            Diagnostic.Log("[Xiaobai] OnOffscreenRenderActiveChanged active! " + m_activeCameras.Count);
            var rs = m_timelineInst.GetComponentsInChildren<Renderer>(true);
            for (int i = 0; i < rs.Length; ++i)
            {
                var r = rs[i];
                if (r == null)
                    continue;
                for (int j = 0; j < r.sharedMaterials.Length; ++j)
                {
                    var m = r.sharedMaterials[j];
                    if (m == null || m.shader == null)
                        continue;
                    if (r.gameObject.layer != GameObjectLayer.UIAni && m.shader.name.StartsWith("Mt/Effect"))
                    {
                        r.gameObject.layer = GameObjectLayer.BattleEffect;
#if UNITY_EDITOR
                        r.material.EnableKeyword("_OFFSCREEN_RENDER");
#else
                        m.EnableKeyword("_OFFSCREEN_RENDER");
#endif
                    }
                    else
                    {
                        if (r.gameObject.layer != GameObjectLayer.UIAni)
                            r.gameObject.layer = GameObjectLayer.DEFAULT;
                        m.DisableKeyword("_OFFSCREEN_RENDER");
                    }
                }
            }
        }
        else
        {
            if (m_activeCameras.Contains(instId))
                m_activeCameras.Remove(instId);
            Diagnostic.Log("[Xiaobai] OnOffscreenRenderActiveChanged disactive! " + m_activeCameras.Count);
            if (m_activeCameras.Count == 0)
            {
                var rs = m_timelineInst.GetComponentsInChildren<Renderer>(true);
                for (int i = 0; i < rs.Length; ++i)
                {
                    var r = rs[i];
                    if (r == null)
                        continue;
                    for (int j = 0; j < r.sharedMaterials.Length; ++j)
                    {
                        var m = r.sharedMaterials[j];
                        if (m == null || m.shader == null)
                            continue;
                        m.DisableKeyword("_OFFSCREEN_RENDER");
                    }
                }
            }
        }
    }

    private void InitGameObjects()
    {
        m_activeGameObjects.Clear();
        m_deactiveGameObjects.Clear();
        Fetch(m_timelineInst.transform, InsertObj);
    }

    private void InsertObj(GameObject go)
    {
        if (go.activeSelf)
            m_activeGameObjects.Add(go);
        else
            m_deactiveGameObjects.Add(go);
    }

    private void Fetch(Transform t, Action<GameObject> callback)
    {
        for (int i = 0; i < t.childCount; ++i)
        {
            var child = t.GetChild(i);

            Fetch(child, callback);

            callback?.Invoke(child.gameObject);
        }
    }

    private void InitTracks()
    {
        m_tracks.Clear();
        if (m_director != null && m_director.playableAsset != null)
        {
            var outputs = m_director.playableAsset.outputs;
            if (outputs != null)
            {
                foreach (PlayableBinding item in outputs)
                {
                    if (!m_tracks.ContainsKey(item.streamName))
                    {
                        m_tracks.Add(item.streamName, item.sourceObject);
                    }
                }
            }
        }
    }

    public void RevertActiveState()
    {
        for (int i = 0; i < m_activeGameObjects.Count; ++i)
        {
            var activeGameObject = m_activeGameObjects[i];
            if (activeGameObject != null && !activeGameObject.activeSelf)
                activeGameObject.SetActive(true);
        }

        for (int i = 0; i < m_deactiveGameObjects.Count; ++i)
        {
            var deactiveGameObject = m_deactiveGameObjects[i];
            if (deactiveGameObject != null && deactiveGameObject.activeSelf)
                deactiveGameObject.SetActive(false);
        }
    }

    public void Start(ChessPlayerUnit attacker, ChessPlayerUnit target, CinemachineBrain brain, float offsetTime)
    {
        if (!Enable)
            return;

        if (m_timelineInst != null)
        {
            m_timelineInst.SetActive(true);

            if (SceneCameraManager.instance != null)
            {
                if (SceneCameraManager.instance.IsNormal)
                    m_timelineInst.transform.rotation = Quaternion.Euler(0, 0, 0);
                else
                    m_timelineInst.transform.rotation = Quaternion.Euler(0, 180, 0);
            }

            var camera = m_timelineInst.GetComponentInChildren<Camera>(true);
            if (camera != null && brain != null && brain.OutputCamera != null)
            {
                brain.OutputCamera.gameObject.SetActive2(false);
                // 播放Timeline时全局屏蔽飘字
                //ChessBattleUnitFloatTextController.HideAll(true);
            }
        }

        Diagnostic.Log("[ChessViewAttackTimeline.Start] " + (m_timelineInst != null ? m_timelineInst.name : "null") + " offsetTime: " + offsetTime);

        RevertActiveState();

        Setup(attacker, target, brain);

        // 追帧 （切旁观）
        if (offsetTime > 0 && m_timelineInst != null)
        {
            var animators = m_timelineInst.GetComponentsInChildren<Animator>();
            if (animators != null)
            {
                for (int i = 0; i < animators.Length; ++i)
                {
                    if (animators[i] != null)
                    {
                        animators[i].Update(offsetTime);
                    }
                }
            }

            float psTime = offsetTime;
            var particleSystems = m_timelineInst.GetComponentsInChildren<ParticleSystem>();
            while (psTime > 0)
            {
                if (particleSystems != null)
                {
                    for (int i = 0; i < particleSystems.Length; ++i)
                    {
                        if (particleSystems[i] != null && particleSystems[i].main.duration + particleSystems[i].main.startDelay.constant > psTime)
                        {
                            particleSystems[i].Simulate(Time.deltaTime, false, false);
                        }
                    }
                }
                psTime -= Time.deltaTime;
            }

        }

        if (m_director != null)
        {
            m_director.time = 0;
            m_director.Play();
            // 追帧 （切旁观）
            if (offsetTime > 0)
            {
                m_director.time += offsetTime;
                m_director.Evaluate();
            }
        }
        //IsPlaying = true;
    }

    private void Setup(ChessPlayerUnit attacker, ChessPlayerUnit target, CinemachineBrain brain)
    {
        if (m_director != null)
        {
            // 攻击者、受击者位置绑定
            for (int i = 0; i < m_attackBindList.Count; ++i)
            {
                if (m_attackBindList[i] != null)
                    m_attackBindList[i].Bind(attacker, target);
            }

            // 攻击者、受击者受控节点绑定
            for (int i = 0; i < m_attackBodyCtrlList.Count; ++i)
            {
                if (m_attackBodyCtrlList[i] != null)
                    m_attackBodyCtrlList[i].Bind(attacker, target);
            }

            for (int i = 0; i < m_cameraParamSyncList.Count; ++i)
            {
                if (m_cameraParamSyncList[i] != null)
                    m_cameraParamSyncList[i].SetMainCamera(brain.OutputCamera);
            }

            if (brain != null)
            {
                brain.enabled = true;
            }

            // 主相机位置绑定
            if (m_defaultVCam != null && brain != null)
            {
                m_defaultVCam.transform.SetPositionAndRotation(brain.transform.position, brain.transform.rotation);
                m_defaultVCam.m_Lens = LensSettings.FromCamera(brain.OutputCamera);
            }

            // 绑定主相机
            if (m_tracks.TryGetValue("Cinemachine Track", out UnityEngine.Object key))
            {
                m_director.SetGenericBinding(key, brain);
            }
        }
    }

    public void OnTimelineBreak()
    {
        OnTimelineStop(null);

        for (int i = 0; i < m_attackBodyCtrlList.Count; ++i)
        {
            if (m_attackBodyCtrlList[i] != null)
                m_attackBodyCtrlList[i].Restore();
        }
    }

    private void OnTimelineStop(PlayableDirector obj)
    {
        Diagnostic.Log("[ChessViewAttackTimeline.OnTimelineStop] " + (m_timelineInst != null ? m_timelineInst.name : "null"));

        //IsPlaying = false;
        if (m_unit != null)
        {
            m_unit.OnTimelineStop();
        }

        if (m_timelineInst != null)
        {
            m_timelineInst.SetActive(false);
        }

        m_owner.TriggerEvent(endTrigger);
    }

    public bool IsCustomCameraOverUI(int uiCamDepth)
    {
        for (int i = 0; i < m_allCustomCameras.Count; ++i)
        {
            var cam = m_allCustomCameras[i];
            if (cam.gameObject.activeSelf && cam.gameObject.activeInHierarchy)
            {
                if (cam.depth > uiCamDepth)
                    return true;
            }
        }

        return false;
    }
}
