using Cinemachine;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Playables;
using ZGameChess;

namespace ZGameChess
{
    public class CameraState
    {
        protected Camera camera;

        public Vector3 pos;
        protected Quaternion rot;
        protected Vector3 scale;

        protected float fov;
        protected float orthographicSize;
        protected float nearClip;
        protected float farClip;

        protected Vector2 lensShift;
        public bool orthographic;
        public bool isPhysicalCamera;
        public Vector2 sensorSize;

        public void Record(Camera camera)
        {
            this.camera = camera;

            if (camera == null)
                return;

            var t = camera.transform;
            pos = t.localPosition;
            rot = t.localRotation;
            scale = t.localScale;

            fov = camera.fieldOfView;
            //规避安卓的GPU Hang问题，参见 https://tapd.woa.com/cchess/bugtrace/bugs/view?bug_id=1020417564123290677
            if (Application.platform != RuntimePlatform.IPhonePlayer && fov == 31.0f)
            {
                fov = 29.0f;
            }
            orthographicSize = camera.orthographicSize;

            nearClip = camera.nearClipPlane;
            farClip = camera.farClipPlane;

            lensShift = camera.lensShift;
            orthographic = camera.orthographic;
            sensorSize = new Vector2(camera.aspect, 1f);
            isPhysicalCamera = camera.usePhysicalProperties;
            if (isPhysicalCamera)
                sensorSize = camera.sensorSize;
            else
                lensShift = Vector2.zero;
        }

        public void Restore()
        {
            if (camera == null)
                return;

            var cpaz = camera.GetComponent<CameraPanAndZoom>();
            if (cpaz != null)
            {
                cpaz.RevertOriginParam();
                camera.transform.localScale = scale;
            }
            else
            {
                var t = camera.transform;
                t.localPosition = pos;
                t.localRotation = rot;
                t.localScale = scale;
            }
            //规避安卓的GPU Hang问题，参见 https://tapd.woa.com/cchess/bugtrace/bugs/view?bug_id=1020417564123290677
            if (Application.platform == RuntimePlatform.IPhonePlayer || camera.fieldOfView != 31.0f) 
            {
                camera.fieldOfView = fov;
            }
            camera.orthographicSize = orthographicSize;

            camera.nearClipPlane = nearClip;
            camera.farClipPlane = farClip;

            camera.lensShift = lensShift;
            camera.orthographic = orthographic;
            camera.usePhysicalProperties = isPhysicalCamera;
            if (isPhysicalCamera)
                camera.sensorSize = sensorSize;

            // 修正一下视角，防止相机旋转错误
            ACGEventManager.Instance.Send(EventType_BattleView.AutoChess_Battle_CorrectViewport);
        }
    }

    public class ChessViewAttackTimelineUnit
    {
        protected ChessViewAttackUnit m_owner;
        protected ChessAttackTimelineConfig m_cameraConfig;
        protected ChessPlayerUnit m_attacker;
        protected ChessPlayerUnit m_target;

        protected CinemachineBrain m_mainCameraBrain;
        protected CameraState m_defaultCameraState = new CameraState();

        protected ChessViewAttackTimeline m_attackTimeline;
        protected ChessViewAttackTimeline m_hitTimeline;

        protected ChessViewAttackTimeline m_customTimeline1;
        protected ChessViewAttackTimeline m_customTimeline2;
        protected ChessViewAttackTimeline m_customTimeline3;

        protected bool m_needRevertOutline = false;
        protected bool m_outline = false;

        public bool HasTimeline
        {
            get
            {
                return m_attackTimeline.Enable || m_hitTimeline.Enable
                    || m_customTimeline1.Enable || m_customTimeline2.Enable || m_customTimeline3.Enable;
            }
        }

        public bool HasAttackTimeline
        {
            get
            {
                return m_attackTimeline.Enable;
            }
        }

        public bool HasCustomCameraInAttackTimeline
        {
            get
            {
                return HasAttackTimeline && m_attackTimeline.HasCustomCamera;
            }
        }

        public bool IsPlaying
        {
            get
            {
                return m_attackTimeline.IsPlaying || m_hitTimeline.IsPlaying
                     || m_customTimeline1.IsPlaying || m_customTimeline2.IsPlaying || m_customTimeline3.IsPlaying;
            }
        }

        public ChessViewAttackTimelineUnit()
        {
            m_attackTimeline = new ChessViewAttackTimeline(this);
            m_hitTimeline = new ChessViewAttackTimeline(this);
            m_customTimeline1 = new ChessViewAttackTimeline(this);
            m_customTimeline2 = new ChessViewAttackTimeline(this);
            m_customTimeline3 = new ChessViewAttackTimeline(this);
        }

        public void InitEnv(ChessViewAttackUnit owner, ChessAttackTimelineConfig cameraConfig)
        {
            m_owner = owner;
            m_cameraConfig = cameraConfig;

            if (owner != null && m_cameraConfig != null)
            {
                m_attackTimeline.Init(m_cameraConfig.m_attackTimelineGo, owner, m_cameraConfig.m_attackTimelineEndTrigger);
                m_hitTimeline.Init(m_cameraConfig.m_hitTimelineGo, owner, m_cameraConfig.m_hitTimelineEndTrigger);

                m_customTimeline1.Init(m_cameraConfig.m_custom1TimelineGo, owner, m_cameraConfig.m_custom1TimelineEndTrigger);
                m_customTimeline2.Init(m_cameraConfig.m_custom2TimelineGo, owner, m_cameraConfig.m_custom2TimelineEndTrigger);
                m_customTimeline3.Init(m_cameraConfig.m_custom3TimelineGo, owner, m_cameraConfig.m_custom3imelineEndTrigger);
            }
        }

        private void SetupMainCamera()
        {
            var allCameras = Camera.allCameras;
            if (allCameras != null)
            {
                for (int i = 0; i < allCameras.Length; ++i)
                {
                    var c = allCameras[i];
                    if (c != null && c.name == "Main Camera"/* && c.tag == "MainCamera"*/)
                    {
                        m_mainCameraBrain = c.gameObject.TryGetComponent<CinemachineBrain>();
                        if (m_mainCameraBrain != null)
                        {
                            m_mainCameraBrain.enabled = true;
                            m_mainCameraBrain.m_DefaultBlend.m_Style = m_cameraConfig != null ? m_cameraConfig.m_cameraBlendStype : CinemachineBlendDefinition.Style.Linear;
                            m_mainCameraBrain.m_DefaultBlend.m_Time = m_cameraConfig != null ? m_cameraConfig.m_cameraBlendTime : 1f;
                            m_defaultCameraState.Record(c);
                        }

                        break;
                    }
                }
            }

            m_outline = (bool)GameLOD.Instance.Outline;
            if (m_outline)
            {
                m_needRevertOutline = true;
                GameLOD.Instance.ChangeOutline(false);
            }
        }

        private void DisableMainCameraBrain()
        {
            if (m_mainCameraBrain != null)
            {
                m_mainCameraBrain.enabled = false;

                if (m_mainCameraBrain.OutputCamera != null)
                {
                    if (m_mainCameraBrain.OutputCamera != null)
                    {
                        m_mainCameraBrain.OutputCamera.gameObject.SetActive2(true);
                        // Timeline播放完毕全局恢复飘字
                        //ChessBattleUnitFloatTextController.HideAll(false);
                    }
                }
            }

            if (m_needRevertOutline)
            {
                m_needRevertOutline = false;
                GameLOD.Instance.ChangeOutline(m_outline);
            }
        }

        public void OnAttackBegin()
        {
            if (HasTimeline)
            {
                SetupMainCamera();

                //var buim = ChessBattleGlobal.Instance.BattleUnitInfoMgr;
                //if (buim != null)
                //{
                //    buim.SetActive(false);
                //}
            }
        }

        public void OnAttackBreak()
        {
            if (m_attackTimeline.IsPlaying)
            {
                m_attackTimeline.OnTimelineBreak();
            }

            if (m_hitTimeline.IsPlaying)
            {
                m_hitTimeline.OnTimelineBreak();
            }

            if (m_customTimeline1.IsPlaying)
            {
                m_customTimeline1.OnTimelineBreak();
            }

            if (m_customTimeline2.IsPlaying)
            {
                m_customTimeline2.OnTimelineBreak();
            }

            if (m_customTimeline3.IsPlaying)
            {
                m_customTimeline3.OnTimelineBreak();
            }

            OnTimelineStop();

            if (m_cameraConfig != null && m_cameraConfig.m_attackResetAttackerScale
                && m_attacker != null)
            {
                m_attacker.SetConWinBodyScale();
                m_attacker.SetConWinEffect();
            }

            // 强制还原相机状态
            RestoreCamera();
        }

        private void RestoreCamera()
        {
            m_defaultCameraState.Restore();
        }

        public void Trigger(ChessAttackCameraTimePoint triggerTime, ChessPlayerUnit attacker, ChessPlayerUnit target, float offsetTime)
        {
            if (m_mainCameraBrain == null)
            {
                SetupMainCamera();
            }

            if (m_cameraConfig != null && m_cameraConfig.m_attackResetAttackerScale && triggerTime == ChessAttackCameraTimePoint.AttackStart && attacker != null)
            {
                attacker.StopConWinEff();
                m_attacker = attacker;
                m_target = target;
            }

            switch (triggerTime)
            {
                case ChessAttackCameraTimePoint.AttackStart:
                    m_attackTimeline.Start(attacker, target, m_mainCameraBrain, offsetTime);
                    break;
                case ChessAttackCameraTimePoint.HitStart:
                    m_hitTimeline.Start(attacker, target, m_mainCameraBrain, offsetTime);
                    break;
                case ChessAttackCameraTimePoint.CustomEvent1:
                    m_customTimeline1.Start(attacker, target, m_mainCameraBrain, offsetTime);
                    break;
                case ChessAttackCameraTimePoint.CustomEvent2:
                    m_customTimeline2.Start(attacker, target, m_mainCameraBrain, offsetTime);
                    break;
                case ChessAttackCameraTimePoint.CustomEvent3:
                    m_customTimeline3.Start(attacker, target, m_mainCameraBrain, offsetTime);
                    break;
                default:
                    break;
            }
        }

        public void OnTimelineStop()
        {
            if (IsPlaying)
                return;

            DisableMainCameraBrain();

            if (HasTimeline)
            {
                //var buim = ChessBattleGlobal.Instance.BattleUnitInfoMgr;
                //if (buim != null)
                //{
                //    buim.SetActive(true);
                //}
                if (BattleMapManager.Instance != null)
                    BattleMapManager.Instance.OnCameraPositionChanged();
            }

            if (m_cameraConfig != null
                && m_cameraConfig.m_attackResetAttackerScale
                && m_attacker != null)
            {
                m_attacker.SetConWinBodyScale();
                m_attacker.SetConWinEffect();
            }

            // 强制还原相机状态(保底策略)
            RestoreCamera();
        }

        public bool IsAttackCustomCameraOverUI(int uiCamDepth)
        {
            if (!m_attackTimeline.Enable || m_attackTimeline.IsPlaying)
                return false;

            return m_attackTimeline.IsCustomCameraOverUI(uiCamDepth);
        }
    }


}
