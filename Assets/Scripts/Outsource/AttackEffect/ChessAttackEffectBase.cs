using GameFramework.FMath;
using TKFrame;
using UnityEngine;
using ZGame.Stage;
using ZGameChess;

public abstract class ChessAttackEffectBase
{
    protected ChessViewAttackUnit m_owner;
    protected GameObject m_castEffect;

    protected float m_startTime = 0.0f;
    protected ChessAttackItemConfig m_config;
    protected bool m_isMainAtack = false;

    protected ChessPlayerUnit m_attacker;
    protected ChessPlayerUnit m_target;

    //protected Transform m_castLocTrans;

    protected bool m_finished = false;
    public bool Finished
    {
        get
        {
            return m_finished;
        }
        protected set
        {
            m_finished = value;
            if (m_finished)
            {
                CheckThenShow(ChessAttackItemConfig.HideParam.Phase.Finished);

                Reset();
            }
        }
    }

    public bool HideUI
    {
        get { return m_hideUI; }
    }

    protected float m_delayShowOwnerLittleLegend = -1f;
    protected float m_delayShowEnemyLittleLegend = -1f;
    protected float m_delayShowHero = -1f;
    protected float m_delayShowScene = -1f;
    protected float m_delayShowUI = -1f;

    protected bool m_hideOwnerLittleLegend = false;
    protected bool m_hideEnemyLittleLegend = false;
    protected bool m_hideHero = false;
    protected bool m_hideScene = false;
    protected bool m_hideUI = false;

    private uint m_soundId = 0;
    private float m_soundStartTime = 0f;

    public ChessAttackEffectBase(ChessViewAttackUnit owner, GameObject castEffect, ChessAttackItemConfig config, bool mainAttack)
    {
        m_owner = owner;
        m_castEffect = castEffect;
        LimitCameraDepth(m_castEffect);
        m_config = config;
        m_isMainAtack = mainAttack;
    }

    public bool IsMainAttack()
    {
        return m_isMainAtack;
    }

    public virtual bool IsInited()
    {
        return true;
    }

    public bool CanInit(ChessAttackItemConfig.TriggerCondition triggerCondition)
    {
        if (m_config.m_triggerCondition != triggerCondition)
            return false;
        return true;
    }

    protected void LimitCameraDepth(GameObject effect)
    {
        if (SystemManager.getInstance() == null)
            return;
        var curStage = SystemManager.getInstance().GetStage() as BaseStage;
        if (curStage != null && effect != null)
        {
            bool canSet = curStage is ChessBattleStage
                || curStage is TinyAttackSceneUIStage;
            if (!canSet)
            {
                // 降低一下相机层级 避免在局外预览压UI的情况
                var cameras = effect.GetComponentsInChildren<Camera>(true);
                foreach (var item in cameras)
                {
                    if (item.depth >= 5)
                        item.depth = 4;
                }
            }
        }
    }

    public virtual void Init(ChessPlayerUnit attacker, ChessPlayerUnit target, float startTime, ChessAttackItemConfig.TriggerCondition triggerCondition, float currentTime)
    {
        if (!CanInit(triggerCondition))
            return;

        Reset();

        m_attacker = attacker;
        m_target = target;
        m_startTime = startTime + (m_config.m_hasCast ? m_config.m_cast.m_castTime : 0);
        Finished = false;

        if (m_attacker == null)
        {
            TKFrame.Diagnostic.Warn("m_attacker is null");
        }

        if (m_target == null)
        {
            TKFrame.Diagnostic.Warn("m_target is null");
        }

        // 创建施法前摇
        if (m_attacker != null && m_config.m_hasCast && m_castEffect != null)
        {
            SpawnEffect(m_castEffect, m_attacker, m_target, m_config.m_cast.m_spawnPos, m_config.m_cast.m_trajectoryPercent, m_config.m_cast.m_spawnDir, m_config.m_cast.m_spwanLoc, m_config.m_cast.m_scale);

            //var loc = ChessViewAttackUnit.GetLoc(m_attacker, m_config.m_cast.m_spwanLoc);
            //m_castEffect.transform.position = loc.position;
            //m_castEffect.transform.rotation = loc.rotation;
            //if (m_config.m_cast.m_scale)
            //    m_castEffect.transform.localScale = loc.localToWorldMatrix.lossyScale;
            //m_castEffect.SetActive(true);

            //m_castLocTrans = loc;

            Log("Start Cast: " + m_castEffect.name);
        }

        CheckThenHide(startTime, ChessAttackItemConfig.HideParam.Phase.Cast);
        //CheckThenShow(ChessAttackItemConfig.HideParam.Phase.Cast);  // 这种情况没有必要
    }

    public virtual void OnLogicHit(float time)
    {
        Log("OnLogicHit");

        CheckThenHide(time, ChessAttackItemConfig.HideParam.Phase.Hit);
        CheckThenShow(ChessAttackItemConfig.HideParam.Phase.Hit);

        if (m_isMainAtack && !string.IsNullOrEmpty(m_config.m_hitAttackerAction) && m_attacker != null)
        {
            m_attacker.PlayAnim(m_config.m_hitAttackerAction);
        }
    }

    public virtual void OnLogicWillHit(float time)
    {
        Log("OnLogicWillHit");
    }

    public void RunTick(float time)
    {
        SyncEffect(m_castEffect, m_attacker, m_target, m_config.m_cast.m_spawnPos, m_config.m_cast.m_spawnDir, m_config.m_cast.m_spwanLoc, false, m_config.m_cast.m_syncLocRot, m_config.m_cast.m_syncLocPos);

        //if (m_castEffect != null && m_castEffect.activeSelf && m_castLocTrans != null)
        //{
        //    if (m_config.m_cast.m_syncLocPos)
        //        m_castEffect.transform.position = m_castLocTrans.position;
        //    if (m_config.m_cast.m_syncLocRot)
        //        m_castEffect.transform.rotation = m_castLocTrans.rotation;
        //}

        CheckTimeThenShow(time);

        if (m_startTime > time)
        {
            return;
        }

        if (!Finished)
            RunTick_Impl(time);
    }

    protected abstract void RunTick_Impl(float time);

    public virtual void OnDestory()
    {

    }

    public void ResetVisible()
    {
        m_delayShowOwnerLittleLegend = -1;
        m_delayShowEnemyLittleLegend = -1;
        m_delayShowHero = -1;
        m_delayShowScene = -1;
        m_delayShowUI = -1;

        if (m_hideOwnerLittleLegend)
        {
            SetLittleLegendVisible(true, true);
        }

        if (m_hideEnemyLittleLegend)
        {
            SetLittleLegendVisible(false, true);
        }

        if (m_hideHero)
        {
            SetBattleHeroVisible(true);
        }

        if (m_hideScene)
        {
            SetSceneVisible(true);
        }

        if (m_hideUI)
        {
            SetUIVisible(true);
        }
    }

    public virtual void Reset()
    {
        if (m_castEffect != null)
        {
            m_castEffect.SetActive(false);
        }

        ResetVisible();

        TryStopAttackSound();

        m_startTime = 0;
        m_attacker = null;
        m_target = null;

        Log("Reset");
    }

    protected virtual GameObject GetAttackSoundBindTarget()
    {
        if (m_attacker == null || m_config == null)
            return null;
        GameObject go = m_attacker.gameObject;
        switch (m_config.attackStartSoundBindTarget)
        {
            case ChessAttackItemConfig.EffectSpawnPos.Attacker:
                go = m_attacker.gameObject;
                break;
            case ChessAttackItemConfig.EffectSpawnPos.Target:
                if(m_target != null){
                    go = m_target.gameObject;
                }
                break;
            case ChessAttackItemConfig.EffectSpawnPos.DistancePoint:
                go = m_attacker.gameObject;
                break;
            case ChessAttackItemConfig.EffectSpawnPos.Bullet:
                go = m_attacker.gameObject;
#if UNITY_EDITOR
                Diagnostic.Warn("当前节点不支持音效绑定到子弹");
#endif
                break;
            case ChessAttackItemConfig.EffectSpawnPos.MapCenter:
                go = m_attacker.gameObject;
                break;
            default:
                break;
        }
        return go;
    }

    protected void PlayAttackSound()
    {
        if (!string.IsNullOrEmpty(m_config.attackStartSoundBnk) && !string.IsNullOrEmpty(m_config.attackStartSoundName) && m_attacker != null)
        {
            m_soundId = ChessUtil.PlayWwiseBankByPath(m_config.attackStartSoundBnk, m_config.attackStartSoundName, GetAttackSoundBindTarget(), FixPlayingID);
            m_soundStartTime = Time.time;
            Diagnostic.Log("Sound startPlay " + m_soundId + " time: " + m_soundStartTime + "bnk: " + m_config.attackStartSoundBnk + " name:" + m_config.attackStartSoundName);
        }
    }

    protected void TryStopAttackSound()
    {
        if (m_soundId != 0 && m_config.autoStopAttackStartSound)
        {
            if (m_finished && m_config.attackStartSoundTime + m_soundStartTime > Time.time)
            { // 如果是正常结束 并且有延迟播放时间 就启动一个延迟停止任务
                uint soundId = m_soundId;
                m_soundId = 0;
                Diagnostic.Log("Sound willStopPlayDelay " + soundId + " time: " + Time.time);
                ToolKit.delayCall(m_config.attackStartSoundTime + m_soundStartTime - Time.time, () =>
                {
                    Diagnostic.Log("Sound stopPlayDelay " + soundId + " time: " + Time.time);
                    ChessUtil.StopWwiseSound(soundId);
                });
            }
            else
            {
                Diagnostic.Log("Sound stopPlay " + m_soundId + " time: " + Time.time);
                ChessUtil.StopWwiseSound(m_soundId);
                m_soundId = 0;
            }
        }
    }

    public void StopSound()
    {
        if (m_soundId != 0 && m_config.autoStopAttackStartSound)
        {
            Diagnostic.Log("Sound stopPlay " + m_soundId + " time: " + Time.time);
            ChessUtil.StopWwiseSound(m_soundId);
            m_soundId = 0;
        }
    }

    private void FixPlayingID(string key, uint playingId)
    {
        m_soundId = playingId;
    }

    protected void Log(string log)
    {
        int attackId = -1;
        if (m_attacker != null && m_attacker.PlayerData != null)
            attackId = m_attacker.PlayerData.ChessPlayerId;
        int targetId = -1;
        if (m_target != null && m_target.PlayerData != null)
            targetId = m_target.PlayerData.ChessPlayerId;
        if (attackId != -1)
            TKFrame.Diagnostic.Log("[AttackUnit]a:{0} t:{1} l:{2}", attackId, targetId, log);
    }

    protected void CheckTimeThenShow(float time)
    {
        if (m_delayShowOwnerLittleLegend > 0 && m_delayShowOwnerLittleLegend <= time)
        {
            m_delayShowOwnerLittleLegend = -1;
            SetLittleLegendVisible(true, true);
        }

        if (m_delayShowEnemyLittleLegend > 0 && m_delayShowEnemyLittleLegend <= time)
        {
            m_delayShowOwnerLittleLegend = -1;
            SetLittleLegendVisible(false, true);
        }

        if (m_delayShowHero > 0 && m_delayShowHero <= time)
        {
            m_delayShowHero = -1;
            SetBattleHeroVisible(true);
        }

        if (m_delayShowScene > 0 && m_delayShowScene <= time)
        {
            m_delayShowScene = -1;
            SetSceneVisible(true);
        }

        if (m_delayShowUI > 0 && m_delayShowUI <= time)
        {
            m_delayShowUI = -1;
            SetUIVisible(true);
        }
    }

    public void CheckThenHide(float time, ChessAttackItemConfig.HideParam.Phase phase)
    {
        if (!m_isMainAtack)  // 只有主攻击特效有这个功能
        {
            return;
        }

        if (m_config.m_hideOwnerLettleLengend.m_enable && m_config.m_hideOwnerLettleLengend.m_hideStage == phase)
        {
            SetLittleLegendVisible(true, false);

            if (m_config.m_hideOwnerLettleLengend.m_hideByTime)
                m_delayShowOwnerLittleLegend = time + m_config.m_hideOwnerLettleLengend.m_hideTime;
        }

        if (m_config.m_hideEnemyLettleLengend.m_enable && m_config.m_hideEnemyLettleLengend.m_hideStage == phase)
        {
            SetLittleLegendVisible(false, false);

            if (m_config.m_hideEnemyLettleLengend.m_hideByTime)
                m_delayShowEnemyLittleLegend = time + m_config.m_hideEnemyLettleLengend.m_hideTime;
        }

        if (m_config.m_hideHero.m_enable && m_config.m_hideHero.m_hideStage == phase)
        {
            SetBattleHeroVisible(false);

            if (m_config.m_hideHero.m_hideByTime)
                m_delayShowHero = time + m_config.m_hideHero.m_hideTime;
        }

        if (m_config.m_hideScene.m_enable && m_config.m_hideScene.m_hideStage == phase)
        {
            SetSceneVisible(false);

            if (m_config.m_hideScene.m_hideByTime)
                m_delayShowScene = time + m_config.m_hideScene.m_hideTime;
        }

        if (m_config.m_hideUI.m_enable && m_config.m_hideUI.m_hideStage == phase)
        {
            SetUIVisible(false);

            if (m_config.m_hideUI.m_hideByTime)
                m_delayShowUI = time + m_config.m_hideUI.m_hideTime;
        }
    }

    protected void CheckThenShow(ChessAttackItemConfig.HideParam.Phase phase)
    {
        if (!m_isMainAtack)  // 只有主攻击特效有这个功能
        {
            return;
        }

        if (m_config.m_hideOwnerLettleLengend.m_enable 
            && !m_config.m_hideOwnerLettleLengend.m_hideByTime
            && m_config.m_hideOwnerLettleLengend.m_showStage == phase)
        {
            SetLittleLegendVisible(true, true);
        }

        if (m_config.m_hideEnemyLettleLengend.m_enable
            && !m_config.m_hideEnemyLettleLengend.m_hideByTime
            && m_config.m_hideEnemyLettleLengend.m_showStage == phase)
        {
            SetLittleLegendVisible(false, true);
        }

        if (m_config.m_hideHero.m_enable
            && !m_config.m_hideHero.m_hideByTime
            && m_config.m_hideHero.m_showStage == phase)
        {
            SetBattleHeroVisible(true);
        }

        if (m_config.m_hideScene.m_enable
            && !m_config.m_hideScene.m_hideByTime
            && m_config.m_hideScene.m_showStage == phase)
        {
            SetSceneVisible(true);
        }

        if (m_config.m_hideUI.m_enable
            && !m_config.m_hideUI.m_hideByTime
            && m_config.m_hideUI.m_showStage == phase)
        {
            SetUIVisible(true);
        }
    }

    protected void SetLittleLegendVisible(bool owner, bool visible)
    {
        if (owner)
        {
            if (m_attacker != null)
            {
                if (visible)
                    m_attacker.ShowBody();
                else
                    m_attacker.HideBody();
            }
        }
        else
        {
            if (m_target != null)
            {
                if (visible)
                    m_target.ShowBody();
                else
                    m_target.HideBody();
            }
        }

        //var ctrl = ChessBattleGlobal.Instance.ChessPlayerCtrl;
        //if (ctrl != null)
        //    ctrl.SetVisible(owner ? ctrl.ChairId : ctrl.EnemyPlayerId, visible);
        //else
        //    TKFrame.Diagnostic.Warn("[SetLittleLegendVisible]ChessBattleGlobal.Instance.ChessPlayerCtrl is null");

        if (owner)
            m_hideOwnerLittleLegend = !visible;
        else
            m_hideEnemyLittleLegend = !visible;
    }

    protected void SetBattleHeroVisible(bool visible)
    {
        var battleUnitService = ChessBattleGlobal.Instance.BattleUnitService;
        if (battleUnitService == null)
        {
            //TKFrame.Diagnostic.Error("[TransferEventHandleBase.SetHerosVisible] battleUnitService == null");
            return;
        }
        var allBattleUnit = battleUnitService.GetAllBattleUnit();
        for (int i = 0; i < allBattleUnit.Count; ++i)
        {
            var unit = allBattleUnit[i];
            if (unit != null && unit.Data != null)
            {
                if (unit.Data.areaType == AreaType.Battle && unit.isDead)
                    continue;

                // 消失
                if (visible)
                    unit.ShowBody();
                else
                {
                    if (unit.Data.areaType == AreaType.Battle)
                        unit.HideBodyAndCacheAction();
                    else
                        unit.HideBody();
                }
            }
        }

        m_hideHero = !visible;
    }

    protected void SetSceneVisible(bool visible)
    {
        var mapMgr = BattleMapManager.Instance;
        if (mapMgr != null)
        {
            var map = mapMgr.GetCurrentBattleMap();
            if (map != null)
                map.Active = visible;
            //var scene = mapMgr.GetCurrentActiveMap();
            //if (scene != null)
            //{
            //    scene.transform.localPosition = visible ? Vector3.zero : new Vector3(10000, -10000, 10000);
            //}

            Diagnostic.Log("[SetSceneVisible] scene: " + (map != null ? map.MapName : "null") + " visible: " + visible);

            m_hideScene = !visible;
        }
    }

    protected void SetUIVisible(bool visible)
    {
        TKPostProcessingStack.TKPPSCameraDepthMustSmallerThanThisValue = visible ? (LoginManager.Instance.IsInGame ? 2 : 5) : 100;
        bool success = false;
        var curStage = SystemManager.getInstance().GetStage() as BaseStage;
        if (curStage != null)
        {
            bool canSet = curStage is ChessBattleStage
                || curStage is TinyAttackSceneUIStage;
            if (canSet)
            {
                Canvas canvas = curStage.UIList.gameObject.GetComponent<Canvas>();
                if (canvas != null && canvas.worldCamera != null)
                {
                    success = true;
                    canvas.worldCamera.enabled = visible;
                }
            }
        }

        Diagnostic.Log("[SetUIVisible] curStage: " + (curStage != null ? curStage.name : "null") + " visible: " + visible + " success: " + success);

        if (success)
            m_hideUI = !visible;
    }

    public void TriggerEvent(int eventType, float time = -1)
    {
        m_owner.TriggerEvent(eventType, time);
    }

    public static bool SpawnEffect(GameObject spawnEffect, ChessPlayerUnit attacker, ChessPlayerUnit target,
        ChessAttackItemConfig.EffectSpawnPos pos, float trajectoryPercent,
        ChessAttackItemConfig.HitEffectDir dir,
        CharacterHangPoint.SupportHangPointType spawnloc, bool spawnScaleWithLoc)
    {
        bool success = false;
        Vector3 spawnPos = Vector3.zero;
        Quaternion spawnRot = attacker.transform.rotation;
        Vector3 spawnScale = Vector3.one;
        if (pos == ChessAttackItemConfig.EffectSpawnPos.Attacker)
        {
            var loc = ChessViewAttackUnit.GetLoc(attacker, spawnloc);
            if (loc != null)
            {
                spawnPos = loc.position;
                if (dir == ChessAttackItemConfig.HitEffectDir.FromLocDir)
                    spawnRot = loc.rotation;
                else if (dir == ChessAttackItemConfig.HitEffectDir.AttackDir)
                    spawnRot = attacker.transform.rotation;
                else if (dir == ChessAttackItemConfig.HitEffectDir.TargetDir)
                    spawnRot = target.transform.rotation;
                else if (dir == ChessAttackItemConfig.HitEffectDir.Center)
                    spawnRot = Quaternion.LookRotation(attacker.transform.position - Vector3.zero, Vector3.up);
                else if (dir == ChessAttackItemConfig.HitEffectDir.FollowCamera)
                    spawnRot = SceneCameraManager.instance.IsNormal ? Quaternion.identity : Quaternion.Euler(0, 180, 0);

                if (spawnScaleWithLoc)
                    spawnScale = loc.lossyScale;
                success = true;
            }
            else
            {
                Diagnostic.Warn("active spawn effect faild! m_attacker.loc is null loc: " + spawnloc);
            }
        }
        else if (pos == ChessAttackItemConfig.EffectSpawnPos.Target)
        {
            var loc = ChessViewAttackUnit.GetLoc(target, spawnloc);
            if (loc != null)
            {
                spawnPos = loc.position;
                if (dir == ChessAttackItemConfig.HitEffectDir.FromLocDir)
                    spawnRot = loc.rotation;
                else if (dir == ChessAttackItemConfig.HitEffectDir.AttackDir)
                    spawnRot = attacker.transform.rotation;
                else if (dir == ChessAttackItemConfig.HitEffectDir.TargetDir)
                    spawnRot = target.transform.rotation;
                else if (dir == ChessAttackItemConfig.HitEffectDir.Center)
                    spawnRot = Quaternion.LookRotation(attacker.transform.position - Vector3.zero, Vector3.up);
                else if (dir == ChessAttackItemConfig.HitEffectDir.FollowCamera)
                    spawnRot = SceneCameraManager.instance.IsNormal ? Quaternion.identity : Quaternion.Euler(0, 180, 0);

                if (spawnScaleWithLoc)
                    spawnScale = loc.lossyScale;
                success = true;
            }
            else
            {
                Diagnostic.Warn("active spawn effect faild! m_target.loc is null loc: " + spawnloc);
            }
        }
        else if (pos == ChessAttackItemConfig.EffectSpawnPos.DistancePoint)
        {
            if (target != null)
            {
                spawnPos = Vector3.Lerp(attacker.transform.position, target.transform.position, trajectoryPercent);
                if (dir == ChessAttackItemConfig.HitEffectDir.AttackDir)
                    spawnRot = attacker.transform.rotation;
                else if (dir == ChessAttackItemConfig.HitEffectDir.TargetDir)
                    spawnRot = target.transform.rotation;
                else if (dir == ChessAttackItemConfig.HitEffectDir.Center)
                    spawnRot = Quaternion.LookRotation(attacker.transform.position - Vector3.zero, Vector3.up);
                else if (dir == ChessAttackItemConfig.HitEffectDir.FollowCamera)
                    spawnRot = SceneCameraManager.instance.IsNormal ? Quaternion.identity : Quaternion.Euler(0, 180, 0);

                if (spawnScaleWithLoc)
                {
                    var loc = ChessViewAttackUnit.GetLoc(attacker, CharacterHangPoint.SupportHangPointType.GROUND_LOC);
                    if (loc != null)
                        spawnScale = loc.lossyScale;
                }

                success = true;
            }
            else
            {
                Diagnostic.Warn("active spawn effect faild! m_target is null");
            }
        }
        else if (pos == ChessAttackItemConfig.EffectSpawnPos.MapCenter)
        {
            if (target != null)
            {
                spawnPos = new Vector3(0, BattleMapManager.Instance.FindHeightByMay(FVector2.zero).ToSingle(), 0) ;
                if (dir == ChessAttackItemConfig.HitEffectDir.AttackDir)
                    spawnRot = attacker.transform.rotation;
                else if (dir == ChessAttackItemConfig.HitEffectDir.TargetDir)
                    spawnRot = target.transform.rotation;
                else if (dir == ChessAttackItemConfig.HitEffectDir.Center)
                    spawnRot = Quaternion.LookRotation(attacker.transform.position - Vector3.zero, Vector3.up);
                else if (dir == ChessAttackItemConfig.HitEffectDir.FollowCamera)
                    spawnRot = SceneCameraManager.instance.IsNormal ? Quaternion.identity : Quaternion.Euler(0, 180, 0);

                if (spawnScaleWithLoc)
                {
                    var loc = ChessViewAttackUnit.GetLoc(attacker, CharacterHangPoint.SupportHangPointType.GROUND_LOC);
                    if (loc != null)
                        spawnScale = loc.lossyScale;
                }

                success = true;
            }
            else
            {
                Diagnostic.Warn("active spawn effect faild! m_target is null");
            }
        }

        if (spawnEffect != null && success)
        {
            var t = spawnEffect.transform;
            t.position = spawnPos;
            t.rotation = spawnRot;
            if (spawnScaleWithLoc)
                t.localScale = spawnScale;

            bool isNormal = SceneCameraManager.instance != null ? SceneCameraManager.instance.IsNormal : true;
            var zhuchang = t.Find("zhuchang");
            if (zhuchang != null)
            {
                zhuchang.gameObject.SetActive(isNormal);
            }
            var kechang = t.Find("kechang");
            if (kechang != null)
            {
                kechang.gameObject.SetActive(!isNormal);
            }

            ChessAttackBindCtrl[] bindCtrls = spawnEffect.GetComponentsInChildren<ChessAttackBindCtrl>(true);
            foreach (var item in bindCtrls)
            {
                item.Bind(attacker, target);
            }

            spawnEffect.SetActive(true);
        }

        return success;
    }

    //private void 

    public static void SyncEffect(GameObject effect, ChessPlayerUnit attacker, ChessPlayerUnit target,
        ChessAttackItemConfig.EffectSpawnPos pos, ChessAttackItemConfig.HitEffectDir dir, CharacterHangPoint.SupportHangPointType spawnloc,
        bool syncScale, bool syncRot, bool syncPos)
    {
        if (effect == null || !effect.gameObject.activeSelf || !effect.gameObject.activeInHierarchy)
            return;

        if (pos == ChessAttackItemConfig.EffectSpawnPos.DistancePoint)
            return;

        if (syncRot)
            syncRot = dir == ChessAttackItemConfig.HitEffectDir.AttackDir;
        if (!syncScale && !syncRot && !syncPos)
            return;

        Transform t = null;
        if (pos == ChessAttackItemConfig.EffectSpawnPos.Attacker)
        {
            t = ChessViewAttackUnit.GetLoc(attacker, spawnloc);
        }
        else if (pos == ChessAttackItemConfig.EffectSpawnPos.Target)
        {
            t = ChessViewAttackUnit.GetLoc(target, spawnloc);
        }

        if (effect != null && t != null)
        {
            var et = effect.transform;
            if (syncScale)
                et.localScale = t.lossyScale;

            if (syncRot)
                et.rotation = t.rotation;

            if (syncPos)
                et.position = t.position;
        }
    }
}

