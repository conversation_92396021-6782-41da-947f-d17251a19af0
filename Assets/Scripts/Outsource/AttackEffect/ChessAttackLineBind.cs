using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using ZGameChess;

/// <summary>
/// 绑定攻击者位置和受击者位置
/// </summary>
public class ChessAttackLineBind : MonoBehaviour
{
    [Serializable]
    public class Node
    {
        public ChessAttackItemConfig.EffectSpawnPos m_spawnPos;
        public CharacterHangPoint.SupportHangPointType m_spwanLoc = CharacterHangPoint.SupportHangPointType.SPINE_LOC;
        public float m_percent;
        public Vector3 offset;
    }

    public List<Node> m_nodeList = new List<Node>()
    {
        new Node() { m_spawnPos = ChessAttackItemConfig.EffectSpawnPos.Attacker },
        new Node() { m_spawnPos = ChessAttackItemConfig.EffectSpawnPos.Target }
    };

    protected ChessViewAttackUnit m_owner;
    protected LineRenderer m_lineRenderer;

    private void Awake()
    {
        m_owner = GetComponentInParent<ChessViewAttackUnit>();
        m_lineRenderer = GetComponent<LineRenderer>();
    }

    private void Update()
    {
        if (m_owner != null && m_owner.m_attacker != null && m_owner.m_target != null && m_lineRenderer != null)
        {
            UpdateBind(m_owner.m_attacker, m_owner.m_target);
        }
    }

    public void UpdateBind(ChessPlayerUnit attacker, ChessPlayerUnit defener)
    {
        if (m_lineRenderer.positionCount != m_nodeList.Count)
            m_lineRenderer.positionCount = m_nodeList.Count;

        for (int i = 0; i < m_nodeList.Count; ++i)
        {
            var node = m_nodeList[i];
            Vector3 pos = Vector3.zero;
            if (node.m_spawnPos == ChessAttackItemConfig.EffectSpawnPos.Attacker 
                || node.m_spawnPos == ChessAttackItemConfig.EffectSpawnPos.Target)
            {
                var p = node.m_spawnPos == ChessAttackItemConfig.EffectSpawnPos.Attacker ? attacker : defener;
                pos = ChessViewAttackUnit.GetLoc(p, node.m_spwanLoc).position;
            }
            else if (node.m_spawnPos == ChessAttackItemConfig.EffectSpawnPos.Bullet)
            {
                int bulletIndex = (int)node.m_percent;
                pos = m_owner.GetBulletPos(bulletIndex);
            }
            else
            {
                pos = Vector3.Lerp(attacker.transform.position, defener.transform.position, node.m_percent);
            }

            if (node.offset.x != 0)
            {
                var dir = Vector3.Cross(Vector3.up, (defener.transform.position - attacker.transform.position).normalized);
                pos += dir * node.offset.x;
            }

            if (node.offset.y != 0)
            {
                pos += Vector3.up * node.offset.y;
            }

            if (node.offset.z != 0)
            {
                var dir = (defener.transform.position - attacker.transform.position).normalized;
                pos += dir * node.offset.z;
            }

            m_lineRenderer.SetPosition(i, pos);
        }
    }
}

