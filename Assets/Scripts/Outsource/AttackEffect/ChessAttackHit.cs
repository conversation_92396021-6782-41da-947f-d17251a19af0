using GfxFramework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using ZGameChess;

public class ChessAttackHit : ChessAttackEffectBase
{
    protected float m_hitTime = 0.0f;
    public GameObject m_hitEffect;

    public ChessAttackHit(ChessViewAttackUnit owner, GameObject hitEffect, GameObject caseEffect, ChessAttackItemConfig config, bool mainAttack)
        : base(owner, caseEffect, config, mainAttack)
    {
        m_hitEffect = hitEffect;
    }

    public override void Init(ChessPlayerUnit attacker, ChessPlayerUnit target, float startTime, ChessAttackItemConfig.TriggerCondition triggerCondition, float currentTime)
    {
        if (!CanInit(triggerCondition))
            return;

        base.Init(attacker, target, startTime, triggerCondition, currentTime);

        m_hitTime = 0.0f;

        PlayAttackSound();
    }

    public override void Reset()
    {
        base.Reset();
        if (m_hitEffect != null)
        {
            m_hitEffect.SetActive(false);
        }
    }

    protected override void RunTick_Impl(float time)
    {
        if (m_hitTime == 0.0f)
        {
            Hit(time);
        }
        else if (m_hitTime <= time)
        {
            if (m_config.m_hideAttackerWhenHit)
            {
                if (m_attacker != null)
                {
                    m_attacker.ShowBody();
                }
            }

            if (!string.IsNullOrEmpty(m_config.m_hitShakeCamera) && BattleMapManager.Instance != null)
            {
                BattleMapManager.Instance.Shake(m_config.m_hitShakeCamera);
            }

            if (m_config.m_hitVibration)
            {
                //DeviceVibrator.SetVibrator(GameingSettingModel.ParamNames.HeroSkillVibration);
            }

            Finished = true;
        }
    }

    public void Hit(float time)
    {
        // 击中特效
        if (m_target != null)
        {
            m_hitTime = time + 1.0f;

            if (m_hitEffect != null)
            {
                var loc = ChessViewAttackUnit.GetLoc(m_target, m_config.m_hitLoc);
                m_hitEffect.transform.position = loc.position;
                if (m_config.m_hitOffset != Vector3.zero)
                    m_hitEffect.transform.position += m_config.m_hitOffset;

                if (m_config.m_hitEffectDir == ChessAttackItemConfig.HitEffectDir.FromLocDir)
                    m_hitEffect.transform.rotation = loc.rotation;
                else if (m_config.m_hitEffectDir == ChessAttackItemConfig.HitEffectDir.AttackDir && m_attacker != null)
                    m_hitEffect.transform.rotation = Quaternion.LookRotation(m_target.transform.position - m_attacker.transform.position);
                else if (m_config.m_hitEffectDir == ChessAttackItemConfig.HitEffectDir.TargetDir && m_target != null)
                    m_hitEffect.transform.rotation = m_target.transform.rotation;
                else if (m_config.m_hitEffectDir == ChessAttackItemConfig.HitEffectDir.Center)
                    m_hitEffect.transform.LookAt(new Vector3(0, m_hitEffect.transform.position.y, 0));
                else if (m_config.m_hitEffectDir == ChessAttackItemConfig.HitEffectDir.FollowCamera)
                    m_hitEffect.transform.rotation = SceneCameraManager.instance.IsNormal ? Quaternion.identity : Quaternion.Euler(0, 180, 0);

                if (m_config.m_hitScaleWithLoc)
                    m_hitEffect.transform.localScale = loc.localToWorldMatrix.lossyScale;
                m_hitEffect.SetActive(true);

                var gfx = m_hitEffect.GetComponent<GfxRoot>();
                if (gfx != null)
                {
                    m_hitTime = time + gfx.m_timeSystem.m_endTime;
                }

                var bindCtrls = m_hitEffect.GetComponentsInChildren<ChessAttackBodyCtrl>(true);
                for (int i = 0; i < bindCtrls.Length; ++i)
                {
                    var ctrl = bindCtrls[i];
                    ctrl.Bind(m_attacker, m_target);
                }
            }

            if (m_config.m_hideAttackerWhenHit)
            {
                if (m_attacker != null)
                {
                    m_attacker.HideBody();
                }
            }
        }
    }
}
