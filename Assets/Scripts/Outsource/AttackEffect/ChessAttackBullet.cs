using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using ZGameChess;
using GfxFramework;
using TKFrame;
using ZGame;
using ZGame.GameSystem;

public class ChessAttackBullet : ChessAttackEffectBase
{
    public enum BulletState
    {
        Wait,
        Fly,
        AfterHit,
        HideWait,
    }

    private class BulletInfo
    {
        public Transform bullet;
        public ChessAttackBulletMonobehaviour behaviour;
        public Animator[] animators;
        public TrailRenderer[] trails;

        public void Init(ChessPlayerUnit attacker, ChessPlayerUnit target)
        {
            if (bullet == null)
                return;

            bullet.gameObject.SetActive(false);
            var pub = bullet.Find("pub");
            if (pub != null)
            {
                pub.gameObject.SetActive(true);
            }

            var bindCtrls = bullet.GetComponentsInChildren<ChessAttackBodyCtrl>(true);
            for (int i = 0; i < bindCtrls.Length; ++i)
            {
                var ctrl = bindCtrls[i];
                ctrl.Bind(attacker, target);
            }
        }

        public bool HideBullet()
        {
            if (bullet != null && bullet.gameObject.activeSelf)
            {
                var pub = bullet.Find("pub");
                if (pub != null)
                {
                    pub.gameObject.SetActive(true);
                }
                bullet.gameObject.SetActive(false);

                return true;
            }

            return false;
        }
    }

    private BulletInfo m_normalBullet;
    private BulletInfo m_mirrorBullet;

    private GameObject m_normalSpawnEffect;
    private GameObject m_mirrorSpawnEffect;

    private BulletInfo m_bullet;
    //protected ChessAttackBulletMonobehaviour m_bulletBehaviour;
    //public TrailRenderer[] m_trails;
    public Animator[] m_animators;
    protected GfxParabolaCurve m_parabolaCurve = new GfxParabolaCurve();
    protected Vector3 m_currentBulletPos = Vector3.zero;
    protected float m_startMoveTime = 0.0f;
    protected float m_startHitTime = 0.0f;
    protected float m_hideTime = 0.0f;
    protected float m_hitTime = 0.0f;
    //protected bool m_isHit = false;
    private float m_runtimeSpeed = 10;
    protected float RuntimeSpeed
    {
        get
        {
            return m_runtimeSpeed;
        }
        set
        {
            if (m_parabolaCurve != null)
                m_parabolaCurve.SetSpeed(value);
            m_runtimeSpeed = value;
        }
    }
    public GameObject m_hitEffect;
    public GameObject m_spawnEffect;
    protected BulletState m_bulletState = BulletState.Wait;
    protected float m_distance = 0f;
    protected List<ChessAttackItemBulletTimeEvent> m_timeEventList = new List<ChessAttackItemBulletTimeEvent>();
    protected int m_timeEventIndex = 0;


    protected GfxParabolaCurveParam m_param;
    protected bool m_spawnEffectShow = false;

    public ChessAttackBullet(ChessViewAttackUnit owner, GameObject hitEffect, GameObject caseEffect, ChessAttackItemConfig config, bool mainAttack)
        : base(owner, caseEffect, config, mainAttack)
    {

        m_hitEffect = hitEffect;
        LimitCameraDepth(m_hitEffect);
        m_param = config.m_parabolaCurve;
        m_bulletState = BulletState.Wait;

        InitTimeEvents();
    }

    public void SetBullet(Transform normalBullet, Transform mirrorBullet)
    {
        m_normalBullet = InitBullet(normalBullet);
        m_mirrorBullet = InitBullet(mirrorBullet);
    }

    public void SetSpawnEffect(GameObject normalEffect, GameObject mirrorEffect)
    {
        m_normalSpawnEffect = normalEffect;
        m_mirrorSpawnEffect = mirrorEffect;

        List<Animator> animators = new List<Animator>();
        if (m_normalSpawnEffect != null)
        {
            m_normalSpawnEffect.GetComponentsInChildren<Animator>(true, animators);

            LimitCameraDepth(m_normalSpawnEffect);
        }
        if (m_mirrorSpawnEffect != null)
        {
            m_mirrorSpawnEffect.GetComponentsInChildren<Animator>(true, animators);

            LimitCameraDepth(m_mirrorSpawnEffect);
        }
        m_animators = animators.ToArray();
    }

    private void SetSpawnEffectByCameraEye()
    {
        if (m_mirrorSpawnEffect == null || m_attacker == null)
        {
            m_spawnEffect = m_normalSpawnEffect;
            return;
        }

        ChessBattleModel battleModel = ChessModelManager.Instance.GetBattleModel();
        PlayerModel playerModel = battleModel.GetPlayerModel(m_attacker.PlayerData.ChessPlayerId);
        if (playerModel.BattleIndex == playerModel.PlayerId)
        {
            // 如果是攻击者自己的战场 
            m_spawnEffect = m_normalSpawnEffect;
        }
        else
        {
            m_spawnEffect = m_mirrorSpawnEffect;
        }
    }

    private void SetBulletByCameraEye()
    {
        if (m_mirrorBullet == null || m_mirrorBullet.bullet == null || m_attacker == null)
        {
            m_bullet = m_normalBullet;
            return;
        }

        if (QQGameSystem.Instance != null)
        {
            var stage = QQGameSystem.Instance.GetStage();
            if (stage is ChessBattleStage)
            {
                ChessBattleModel battleModel = ChessModelManager.Instance.GetBattleModel();
                PlayerModel playerModel = battleModel.GetPlayerModel(m_attacker.PlayerData.ChessPlayerId);
                if (playerModel.BattleIndex == playerModel.PlayerId)
                {
                    // 如果是攻击者自己的战场 
                    m_bullet = m_normalBullet;
                }
                else
                {
                    m_bullet = m_mirrorBullet;
                }
            }
            else
            {
                m_bullet = m_normalBullet;
            }
        }
        else
        {
            /*ChessBattleModel battleModel = ChessModelManager.Instance.GetBattleModel();
            PlayerModel playerModel = battleModel.GetPlayerModel(m_attacker.PlayerData.ChessPlayerId);
            if (playerModel.BattleIndex == playerModel.PlayerId)
            {
                // 如果是攻击者自己的战场 
                m_bullet = m_normalBullet;
            }
            else
            {
                m_bullet = m_mirrorBullet;
            }*/
            // 用于攻击特效制作专用场景测试用
            if (IsAttackBattleField)
            {
                m_bullet = m_normalBullet;
            }
            else
            {
                m_bullet = m_mirrorBullet;
            }
        }
    }

    public static bool IsAttackBattleField = true;

    private BulletInfo InitBullet(Transform bullet)
    {
        if (bullet == null)
            return null;

        ChessAttackBulletMonobehaviour behaviour = bullet.gameObject.TryGetComponent<ChessAttackBulletMonobehaviour>();
        var animators = bullet.GetComponentsInChildren<Animator>(true);
        var trails = bullet.GetComponentsInChildren<TrailRenderer>(true);

        LimitCameraDepth(bullet.gameObject);

        BulletInfo info = new BulletInfo();
        info.bullet = bullet;
        info.behaviour = behaviour;
        info.animators = animators;
        info.trails = trails;
        return info;
    }

    public override void Init(ChessPlayerUnit attacker, ChessPlayerUnit target, float startTime, ChessAttackItemConfig.TriggerCondition triggerCondition, float currentTime)
    {
        if (!CanInit(triggerCondition))
            return;

        base.Init(attacker, target, startTime, triggerCondition, currentTime);

        SetBulletByCameraEye();
        SetSpawnEffectByCameraEye();

        m_hideTime = 0.0f;
        m_hitTime = 0;
        m_startMoveTime = 0f;
        m_startHitTime = 0f;
        m_timeEventIndex = 0;
        //m_isHit = false;
        m_spawnEffectShow = false;
        RuntimeSpeed = m_config.m_speed;
        m_bulletState = BulletState.Wait;

        if (m_bullet != null)
        {
            m_bullet.Init(attacker, target);
        }
    }

    protected override GameObject GetAttackSoundBindTarget()
    {
        GameObject go = m_attacker.gameObject;
        switch (m_config.attackStartSoundBindTarget)
        {
            case ChessAttackItemConfig.EffectSpawnPos.Attacker:
                go = m_attacker.gameObject;
                break;
            case ChessAttackItemConfig.EffectSpawnPos.Target:
                go = m_target.gameObject;
                break;
            case ChessAttackItemConfig.EffectSpawnPos.DistancePoint:
                go = m_attacker.gameObject;
                break;
            case ChessAttackItemConfig.EffectSpawnPos.Bullet:
                if (m_bullet != null && m_bullet.bullet)
                    go = m_bullet.bullet.gameObject;
#if UNITY_EDITOR
                else
                    Diagnostic.Warn("未配置子弹，音效绑定节点回退到攻击者自身");
#endif
                break;
            case ChessAttackItemConfig.EffectSpawnPos.MapCenter:
                go = m_attacker.gameObject;
                break;
            default:
                break;
        }
        return go;
    }

    public Vector3 GetCurrentBulletPos()
    {
        return m_currentBulletPos;
    }

    public override void Reset()
    {
        base.Reset();
        if (m_bullet != null && m_bullet.bullet != null)
            m_bullet.bullet.SetActive(false);
        if (m_hitEffect != null)
            m_hitEffect.SetActive(false);
        if (m_spawnEffect != null)
            m_spawnEffect.SetActive(false);
    }

    private void CheckFinished(float time)
    {
        if (m_hitTime != 0)
        {
            if (m_hitTime <= time)
            {
                if (m_config.m_hideAttackerWhenHit)
                {
                    if (m_attacker != null)
                    {
                        m_attacker.ShowBody();
                    }
                }

                if (!string.IsNullOrEmpty(m_config.m_hitShakeCamera) && BattleMapManager.Instance != null)
                {
                    BattleMapManager.Instance.Shake(m_config.m_hitShakeCamera);
                }

                if (m_config.m_hitVibration)
                {
                    //DeviceVibrator.SetVibrator(GameingSettingModel.ParamNames.HeroSkillVibration);
                }

                if (m_hitEffect != null)
                {
                    m_hitEffect.SetActive(false);
                }

                // 命中特效播完了
                Finished = true;
            }
        }
    }

    protected bool CheckSpawnEffect(float time)
    {
        bool spawnBullet = false;
        if (!m_spawnEffectShow && m_attacker != null && m_target != null)
        {
            if (SpawnEffect(m_spawnEffect, m_attacker, m_target, m_config.m_spawnPos, m_config.m_trajectoryPercent, m_config.m_spawnDir, m_config.m_spwanLoc, m_config.m_spawnScaleWithLoc))
            {
                m_spawnEffectShow = true;
            }

            SpawnBullet(time);
            spawnBullet = true;

            PlayAttackSound();
        }

        if (m_spawnEffectShow)
        {
            SyncEffect(m_spawnEffect, m_attacker, m_target, m_config.m_spawnPos, m_config.m_spawnDir, m_config.m_spwanLoc,
                false, m_config.m_spawnUpdateRotation, false);
        }
        return spawnBullet;
    }

    protected void SpawnBullet(float time)
    {
        ChessPlayerUnit attacker;
        ChessPlayerUnit target;
        if (m_config.m_bulletFlyMode == ChessAttackItemConfig.BulletFlyMode.FromTargetToAttacker)
        {
            attacker = m_target;
            target = m_attacker;
        }
        else
        {
            attacker = m_attacker;
            target = m_target;
        }

        var position = GetSpawnPos(attacker, target);// m_config.m_useStaticSpawnPosition ? m_config.m_spawnPosition : ChessViewAttackUnit.GetLoc(attacker, m_config.m_spwanLoc).position;

        var attackLoc = ChessViewAttackUnit.GetLoc(attacker, m_config.m_spwanLoc);

        var rotation = m_config.m_useStaticSpawnPosition ? Quaternion.Euler(m_config.m_spawnRotation) : attackLoc.rotation;

        var targetLoc = ChessViewAttackUnit.GetLoc(target, m_config.m_targetLoc);

        var targetPos = GetTargetPos(attackLoc, targetLoc);

        m_currentBulletPos = position;

        if (m_config.m_hasParabolaCurve)
            m_parabolaCurve.InitParam(m_param, m_currentBulletPos, targetLoc.position, m_config.m_speed);

        if (m_bullet != null)
        {
            var bullet = m_bullet.bullet;
            bullet.transform.rotation = rotation;
            if (m_config.m_useStaticSpawnPosition)
            {
                // 客场斩杀 改成镜像位置
                if (m_mirrorBullet == null && SceneCameraManager.instance != null)
                {
                    if (!SceneCameraManager.instance.IsNormal)
                    {
                        m_currentBulletPos.z = -m_currentBulletPos.z;

                        var rot = m_config.m_spawnRotation;
                        rot.y += 180;
                        bullet.transform.rotation = Quaternion.Euler(rot);
                    }
                }

                bullet.localPosition = m_currentBulletPos;
                m_currentBulletPos = bullet.position;     // 修正一下


            }
            else
                bullet.position = m_currentBulletPos;

            if (m_config.m_bulletScaleWithLoc)
            {
                if (m_config.m_useStaticSpawnPosition)
                    bullet.transform.localScale = attacker.transform.localToWorldMatrix.lossyScale;
                else
                    bullet.transform.localScale = ChessViewAttackUnit.GetLoc(attacker, m_config.m_spwanEffectLoc).localToWorldMatrix.lossyScale;
            }

            if (m_config.m_spawnLookAtTarget && targetLoc != null)
            {
                bullet.LookAt(targetPos);
            }

            bullet.SetActive(true);
        }

        m_bulletState = BulletState.Fly;
        m_timeEventIndex = 0;
        if (targetLoc != null)
            m_distance = Vector3.Distance(m_currentBulletPos, targetPos);
        else
            m_distance = 0;

        m_startMoveTime = time + m_config.m_bulletDelaySendTime;

        CheckThenHide(time, ChessAttackItemConfig.HideParam.Phase.Bullet);
        CheckThenShow(ChessAttackItemConfig.HideParam.Phase.Bullet);
        TriggerEvent(m_config.m_bulletSpawnTrigger, time);
    }

    private void ClearBulletTrails()
    {
        if (m_bullet != null && m_bullet.trails != null)
        {
            for (int i = 0; i < m_bullet.trails.Length; ++i)
            {
                if (m_bullet.trails[i] != null)
                    m_bullet.trails[i].Clear();
            }
        }
    }

    private Vector3 GetSpawnPos(ChessPlayerUnit attacker, ChessPlayerUnit target)
    {
        var position = m_config.m_useStaticSpawnPosition ? m_config.m_spawnPosition : ChessViewAttackUnit.GetLoc(attacker, m_config.m_spwanLoc).position;

        if (m_config.m_bulletSpawnOffset != 0)
        {
            var dir = (ChessViewAttackUnit.GetLoc(target, m_config.m_targetLoc).position - position).normalized;
            position += dir * m_config.m_bulletSpawnOffset;
        }

        if (m_config.m_bulletSpawnOffsetVertical != 0)
        {
            position += Vector3.up * m_config.m_bulletSpawnOffsetVertical;
        }

        if (m_config.m_bulletSpawnOffsetHorizontal != 0)
        {
            var dir = Vector3.Cross(Vector3.up, (ChessViewAttackUnit.GetLoc(target, m_config.m_targetLoc).position - position).normalized);
            position += dir * m_config.m_bulletSpawnOffsetHorizontal;
        }

        return position;
    }

    private Vector3 GetTargetPos(Transform attackLoc, Transform targetLoc)
    {
        if (m_config.m_useStaticTargetPosition)
        {
            return m_config.m_targetPosition;
        }

        if (Mathf.Abs(m_config.m_bulletTargetOffset) < 0.001f
            && Mathf.Abs(m_config.m_bulletTargetOffsetVertical) < 0.001f
            && Mathf.Abs(m_config.m_bulletTargetOffsetHorizontal) < 0.001f)
        {
            return targetLoc.position;
        }

        var pos = targetLoc.position;

        if (m_config.m_bulletTargetOffset != 0)
        {
            var dir = (targetLoc.position - attackLoc.position).normalized;
            pos -= dir * m_config.m_bulletTargetOffset;
        }

        if (m_config.m_bulletTargetOffsetVertical != 0)
        {
            pos += Vector3.up * m_config.m_bulletTargetOffsetVertical;
        }

        if (m_config.m_bulletTargetOffsetHorizontal != 0)
        {
            var dir = Vector3.Cross(Vector3.up, (targetLoc.position - attackLoc.position).normalized);
            pos -= dir * m_config.m_bulletTargetOffsetHorizontal;
        }

        return pos;
    }

    protected void CheckBulletHide(float time)
    {
        if (m_bulletState == BulletState.HideWait && m_hideTime != 0.0f)
        {
            if (time > m_hideTime)
            {
                if (m_bullet != null && m_bullet.HideBullet())
                    m_hideTime = 0;
            }
        }
    }

    protected void UpdateParabolaCurve(float time)
    {
        float percent = m_parabolaCurve.Sample(time - m_startMoveTime, out m_currentBulletPos);
        if (percent >= 1)
        {
            OnHit(time);
            return;
        }

        if (m_bullet != null)
        {
            if (m_config.m_bulletLookAtTargetInFly)
            {
                if (percent == 0)
                {  // 修复初始位置指向错误的bug
                    m_bullet.bullet.position = m_currentBulletPos;
                    m_bullet.bullet.LookAt(m_parabolaCurve.GetPos(percent + 0.1f));
                }
                else
                {
                    m_bullet.bullet.LookAt(m_currentBulletPos);
                    m_bullet.bullet.position = m_currentBulletPos;
                }
            }
            else
            {
                m_bullet.bullet.position = m_currentBulletPos;
            }
        }
    }

    protected float FixPositionHieght(ref Vector3 pos)
    {
        if (m_config.m_bulletFalldownInFly)
        {
            float height = 0;
            var mapMgr = BattleMapManager.Instance;
            if (mapMgr != null)
            {
                height = mapMgr.FindHeightByMay(new GameFramework.FMath.FVector2(pos.x, pos.z)).ToSingle();
            }
            else
            {
#if UNITY_EDITOR
                if (TinyTool.TinyAttackSceneController.SDFPath != null)
                {
                    var v2 = new GameFramework.FMath.FVector2(pos.x, pos.z);
                    height = TinyTool.TinyAttackSceneController.SDFPath.SimpleHeight(ref v2).ToSingle();
                }
#endif
            }
            return height;
        }
        return pos.y;
    }

    protected void UpdateBullet(float time)
    {
        if (m_attacker != null && m_target != null && m_config != null)
        { // 新的位移方式 直接逼近目标

            ChessPlayerUnit target;
            if (m_config.m_bulletFlyMode == ChessAttackItemConfig.BulletFlyMode.FromTargetToAttacker)
            {
                target = m_attacker;
            }
            else
            {
                target = m_target;
            }

            var attackLoc = ChessViewAttackUnit.GetLoc(m_attacker, m_config.m_spwanLoc);
            var loc = ChessViewAttackUnit.GetLoc(target, m_config.m_targetLoc);
            var targetPos = GetTargetPos(attackLoc, loc);
            if (m_config.m_bulletFalldownInFly)
                targetPos.y = m_currentBulletPos.y;
            m_currentBulletPos = Vector3.MoveTowards(m_currentBulletPos, targetPos, RuntimeSpeed * Time.deltaTime);

            if (m_bullet != null)
            {
                if (m_config.m_bulletLookAtTargetInFly)
                    m_bullet.bullet.LookAt(targetPos);

                m_currentBulletPos.y = FixPositionHieght(ref m_currentBulletPos);

                m_bullet.bullet.position = m_currentBulletPos;
            }

            float xDelta = Mathf.Abs(targetPos.x - m_currentBulletPos.x);
            float zDelta = Mathf.Abs(targetPos.z - m_currentBulletPos.z);

            //TKFrame.Diagnostic.Log("子弹位置: (" + m_currentBulletPos.x + "," + m_currentBulletPos.y + "," + m_currentBulletPos.z + ") 目标位置: "
            //    + "(" + loc.position.x + ", " + loc.position.y + ", " + loc.position.z + ")"
            //     + " x差距: " + xDelta + " z差距: " + zDelta);

            if (xDelta < 0.01f && zDelta < 0.01f)
            { // 表现层已经到达目的地了 直接隐藏了

                //TKFrame.Diagnostic.Log("击中");
                OnHit(time);
            }
        }
        else
        {
            OnHit(time);
        }
    }

    protected void CheckTimeEvent()
    {
        if (m_bulletState == BulletState.Fly)
        {
            ChessPlayerUnit target;
            if (m_config.m_bulletFlyMode == ChessAttackItemConfig.BulletFlyMode.FromTargetToAttacker)
            {
                target = m_attacker;
            }
            else
            {
                target = m_target;
            }

            var targetLoc = ChessViewAttackUnit.GetLoc(target, m_config.m_targetLoc);
            if (targetLoc != null)
            {
                var dis = Vector3.Distance(m_currentBulletPos, targetLoc.position);
                var percent = m_distance > 0 ? (m_distance - dis) / m_distance : 100;

                UpdateTimeEvent(percent);
            }
        }
        else if (m_bulletState == BulletState.AfterHit || m_bulletState == BulletState.HideWait)
        {
            UpdateTimeEvent(1);
        }
    }

    protected void UpdateTimeEvent(float percent)
    {
        while (m_timeEventList.Count > m_timeEventIndex)
        {
            var timeEvent = m_timeEventList[m_timeEventIndex];
            if (timeEvent.m_timePercent > percent)
            {
                break;
            }
            else
            {
                ++m_timeEventIndex;
                TriggerTimeEvent(timeEvent);
            }
        }
    }

    protected void TriggerTimeEvent(ChessAttackItemBulletTimeEvent timeEvent)
    {
        if (timeEvent.m_eventType == ChessAttackItemBulletTimeEvent.EventType.ChangeSpeed)
        {
            float speed;
            if (float.TryParse(timeEvent.m_param, out speed) && speed > 0)
            {
                RuntimeSpeed = speed;
            }
        }
        else if (timeEvent.m_eventType == ChessAttackItemBulletTimeEvent.EventType.HideBulletSubNode)
        {
            if (m_bullet != null && m_bullet.behaviour != null)
                m_bullet.behaviour.HideSubNode(timeEvent.m_param);
        }
        else if (timeEvent.m_eventType == ChessAttackItemBulletTimeEvent.EventType.ShowBulletSubNode)
        {
            if (m_bullet != null && m_bullet.behaviour != null)
                m_bullet.behaviour.ShowSubNode(timeEvent.m_param);
        }
    }

    protected void InitTimeEvents()
    {
        m_timeEventIndex = 0;
        m_timeEventList.Clear();
        m_timeEventList.AddRange(m_config.m_timeEvents);
        m_timeEventList.Sort(SortTimeEvent);
    }

    protected static int SortTimeEvent(ChessAttackItemBulletTimeEvent l, ChessAttackItemBulletTimeEvent r)
    {
        return l.m_timePercent.CompareTo(r.m_timePercent);
    }

    protected override void RunTick_Impl(float time)
    {
        CheckFinished(time);

        bool spawnBullet = CheckSpawnEffect(time);

        CheckBulletHide(time);

        if (m_bulletState == BulletState.Fly)
        {
            OnBulletFly(time, spawnBullet);
        }
        else if (m_bulletState == BulletState.AfterHit)
        {
            OnBulletAfterFly(time);
        }

        CheckTimeEvent();
    }

    private void OnBulletFly(float time, bool spawnBullet)
    {
        if (m_startMoveTime >= 0 && m_startMoveTime <= time)
        {
            if (m_config != null && m_config.m_hasParabolaCurve)
                UpdateParabolaCurve(time);
            else
                UpdateBullet(time);

            if (m_config != null && m_config.m_acceleratedVelocity != 0)
            {
                RuntimeSpeed += (m_config.m_acceleratedVelocity * Time.deltaTime);

                // 保底
                if (RuntimeSpeed < 0.1f)
                    RuntimeSpeed = 0.1f;
            }

            if (spawnBullet)        // 避免首次播放的时候 因为计算位置关系出现折线
                ClearBulletTrails();
        }
    }

    private void OnBulletAfterFly(float time)
    {
        if (m_config != null)
        {
            if (time - m_startHitTime > m_config.m_hitActionDelay)
            {
                if (m_config.m_hitBulletAction == ChessAttackItemConfig.HitBulletAction.Return)
                {
                    if (!m_config.m_hasParabolaCurve)
                        ReturnBulletAfterHit(time);
                }
                else
                {
                    if (!m_config.m_hasParabolaCurve)
                        KeepBulletAfterHit(time);
                    else
                        KeepParabolaCurve(time);
                }
            }
        }
        else
        {
            SetHideWait(time);
        }
    }

    private void KeepParabolaCurve(float time)
    {
        m_parabolaCurve.Sample(time - m_startMoveTime, out m_currentBulletPos);

        if (m_bullet != null)
        {
            if (m_config.m_bulletLookAtTargetInFly)
                m_bullet.bullet.LookAt(m_currentBulletPos);

            m_bullet.bullet.position = m_currentBulletPos;
        }

        if (Mathf.Abs(m_currentBulletPos.x) >= 20f || Mathf.Abs(m_currentBulletPos.z) >= 20f)
        {
            SetHideWait(time);
        }
    }

    private void ReturnBulletAfterHit(float time)
    {
        if (m_attacker != null && m_config != null)
        {
            var position = m_config.m_useStaticSpawnPosition ? m_config.m_spawnPosition : ChessViewAttackUnit.GetLoc(m_attacker, m_config.m_spwanLoc).position;
            if (m_config.m_bulletFalldownInFly)
                position.y = m_currentBulletPos.y;
            m_currentBulletPos = Vector3.MoveTowards(m_currentBulletPos, position, RuntimeSpeed * Time.deltaTime);

            if (m_config.m_acceleratedVelocity != 0)
            {
                RuntimeSpeed += (m_config.m_acceleratedVelocity * Time.deltaTime);

                // 保底
                if (RuntimeSpeed < 0.1f)
                    RuntimeSpeed = 0.1f;
            }

            if (m_bullet != null)
            {
                if (m_config.m_bulletLookAtTargetInFly)
                {
                    var loc = m_config.m_useStaticSpawnPosition ? m_attacker.transform : ChessViewAttackUnit.GetLoc(m_attacker, m_config.m_spwanLoc);
                    m_bullet.bullet.LookAt(loc);
                }

                m_currentBulletPos.y = FixPositionHieght(ref m_currentBulletPos);

                m_bullet.bullet.position = m_currentBulletPos;
            }

            float xDelta = Mathf.Abs(position.x - m_currentBulletPos.x);
            float zDelta = Mathf.Abs(position.z - m_currentBulletPos.z);
            if (xDelta < 0.01f && zDelta < 0.01f)
            {
                SetHideWait(time);
            }
        }
        else
        {
            SetHideWait(time);
        }
    }

    private void KeepBulletAfterHit(float time)
    {
        if (m_config != null && m_attacker != null && m_target != null)
        {
            var startPos = m_config.m_useStaticSpawnPosition ? m_config.m_spawnPosition : ChessViewAttackUnit.GetLoc(m_attacker, m_config.m_spwanLoc).position;
            var loc = ChessViewAttackUnit.GetLoc(m_target, m_config.m_targetLoc);
            if (loc == null)
                return;
            var dir = (loc.position - startPos).normalized;

            m_currentBulletPos += dir * RuntimeSpeed * Time.deltaTime;

            if (m_config.m_acceleratedVelocity != 0)
            {
                RuntimeSpeed += (m_config.m_acceleratedVelocity * Time.deltaTime);

                // 保底
                if (RuntimeSpeed < 0.1f)
                    RuntimeSpeed = 0.1f;
            }

            if (m_bullet != null)
            {
                m_currentBulletPos.y = FixPositionHieght(ref m_currentBulletPos);

                m_bullet.bullet.LookAt(m_currentBulletPos);

                m_bullet.bullet.position = m_currentBulletPos;
            }

            if (Mathf.Abs(m_currentBulletPos.x - startPos.x) >= 20f || Mathf.Abs(m_currentBulletPos.z - startPos.z) >= 20f)
            {
                SetHideWait(time);
            }
        }
        else
        {
            SetHideWait(time);
        }
    }

    private void SetHideWait(float time)
    {
        m_bulletState = BulletState.HideWait;
        TryHideBullet(time);
    }

    public void OnHit(float time)
    {
        m_startHitTime = time;
        if (m_config.m_hitBulletAction != ChessAttackItemConfig.HitBulletAction.Hide)
        {
            m_bulletState = BulletState.AfterHit;

            if (m_config.m_hitDelay > 0)
                m_hideTime = time + m_config.m_hitDelay;

            if (m_animators != null && m_animators.Length != 0)
            {
                for (int i = 0; i < m_animators.Length; ++i)
                {
                    if (HasParameter("attack", m_animators[i]))
                        m_animators[i].SetTrigger("attack");
                }
            }
            if (m_bullet != null && m_bullet.animators != null)
            {
                for (int i = 0; i < m_bullet.animators.Length; ++i)
                {
                    if (HasParameter("attack", m_bullet.animators[i]))
                        m_bullet.animators[i].SetTrigger("attack");
                }
            }
        }
        else
        {
            m_bulletState = BulletState.HideWait;
            TryHideBullet(time);
        }

        // 击中特效
        PlayHitEffect(time);

        TryHideAttackerBody();

        TriggerEvent(m_config.m_bulletHitTrigger, time);

    }

    protected void TryHideAttackerBody()
    {
        if (m_config.m_hideAttackerWhenHit)
        {
            if (m_attacker != null)
            {
                m_attacker.HideBody();
            }
        }
    }

    protected void TryHideBullet(float time)
    {
        if (m_bullet != null)
        {
            float delayTime = m_config.m_hitDelay;
            if (delayTime == 0)
            {
                m_bullet.bullet.gameObject.SetActive(false);
            }
            else
            {
                var pub = m_bullet.bullet.Find("pub");
                if (pub != null)
                {
                    pub.gameObject.SetActive(false);
                }
                m_hideTime = time + delayTime;
            }

            if (m_animators != null && m_animators.Length != 0)
            {
                for (int i = 0; i < m_animators.Length; ++i)
                {
                    if (HasParameter("hit", m_animators[i]))
                        m_animators[i].SetTrigger("hit");
                }
            }
            if (m_bullet != null && m_bullet.animators != null)
            {
                for (int i = 0; i < m_bullet.animators.Length; ++i)
                {
                    if (HasParameter("hit", m_bullet.animators[i]))
                        m_bullet.animators[i].SetTrigger("hit");
                }
            }
        }
    }

    private static bool HasParameter(string paramName, Animator animator)
    {
        foreach (AnimatorControllerParameter param in animator.parameters)
        {
            if (param.name == paramName)
                return true;
        }
        return false;
    }

    protected void PlayHitEffect(float time)
    {
        if (m_hitEffect != null)
        {
            if (m_target != null)
            {
                var loc = ChessViewAttackUnit.GetLoc(m_target, m_config.m_hitLoc);
                m_hitEffect.transform.position = loc.position;
                if (m_config.m_hitOffset != Vector3.zero)
                    m_hitEffect.transform.position += m_config.m_hitOffset;

                if (m_config.m_hitEffectDir == ChessAttackItemConfig.HitEffectDir.FromLocDir)
                    m_hitEffect.transform.rotation = loc.rotation;
                else if (m_config.m_hitEffectDir == ChessAttackItemConfig.HitEffectDir.AttackDir && m_attacker != null)
                    m_hitEffect.transform.rotation = Quaternion.LookRotation(m_target.transform.position - m_attacker.transform.position);
                else if (m_config.m_hitEffectDir == ChessAttackItemConfig.HitEffectDir.TargetDir && m_target != null)
                    m_hitEffect.transform.rotation = m_target.transform.rotation;
                else if (m_config.m_hitEffectDir == ChessAttackItemConfig.HitEffectDir.Center)
                    m_hitEffect.transform.LookAt(new Vector3(0, m_hitEffect.transform.position.y, 0));
                else if (m_config.m_hitEffectDir == ChessAttackItemConfig.HitEffectDir.FollowCamera)
                    m_hitEffect.transform.rotation = SceneCameraManager.instance == null || SceneCameraManager.instance.IsNormal ? Quaternion.identity : Quaternion.Euler(0, 180, 0);

                if (m_config.m_hitScaleWithLoc)
                    m_hitEffect.transform.localScale = loc.localToWorldMatrix.lossyScale;

                m_hitEffect.SetActive(true);

                var bindCtrls = m_hitEffect.GetComponentsInChildren<ChessAttackBodyCtrl>(true);
                for (int i = 0; i < bindCtrls.Length; ++i)
                {
                    var ctrl = bindCtrls[i];
                    ctrl.Bind(m_attacker, m_target);
                }
            }

            var gfx = m_hitEffect.GetComponent<GfxRoot>();
            if (gfx != null)
            {
                m_hitTime = time + gfx.m_timeSystem.m_endTime;
            }
            else
            {
                m_hitTime = time + 1.0f;
            }

            Log("Hit " + m_hitEffect.name + " target: " + (m_target != null ? m_target.name : ""));
        }
        else
        {
            m_hitTime = time;
        }

        if (m_hitTime < m_hideTime)
        {
            m_hitTime = m_hideTime;
        }
    }
}

