using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using UnityEngine;
#if !OUTSOURCE
using ZGame.Stage;
#endif
namespace ZGameChess
{
    public class BattleMapCameraAnimation : MonoBehaviour
    {
        public enum CameraAnimationHomeCourtType
        {
            None,
            /// <summary>
            /// 移动到主场固定视角
            /// </summary>
            MoveToHomeCourt,
            /// <summary>
            /// 从主场视角离开
            /// </summary>
            AwayFromHomeCourt,
        }

        [Serializable]
        public class ClipInfo
        {
            public int type;
            public int param;
            public bool isSpecial;
            public bool needRadialBlur;         // 相机中是否需要镜像模糊
            public bool fixAnimView;           // 强制保证视角和初始化的一样，完事了再blend回去
            public float blendTime = 1f;        // 视角blend回去的时间
            public CameraAnimationHomeCourtType courtType;
            public AnimationClip clip;
        }

        public const int CLIP_TYPE_TURN_START = 0;          // 回合开始
        public const int CLIP_TYPE_ROUND_SELECT_START = 1;  // 轮抽开始
        public const int CLIP_TYPE_USER_OUT = 2;            // 游戏结束 第一名诞生

        public Action<string> OnAnimationStart;
        public Action<string, bool> OnAnimationUpdate;
        public Action<string, bool> OnAnimationStop;

        [SerializeField]
        private List<ClipInfo> _animationList = new List<ClipInfo>();
        [SerializeField]
        private ArtCameraBattleMap _artCamera = null;

        private TKDictionary<string, AnimationClip> m_clicDict = new TKDictionary<string, AnimationClip>();

        private Transform _cameraTrans;
        private Animation _cameraAnimation;
        private CameraPanAndZoom _cameraControl;
        public Animation CameraAnimation => _cameraAnimation;

        private string _currentAnimationName = string.Empty;
        private CameraAnimationHomeCourtType _currentCourtType = CameraAnimationHomeCourtType.None;
        private float _currentAnimationStartTime = -1f;
        private float _currentAnimationTotalTime = -1f;
        private bool _needRevertCamPosAndRot = false;
        private bool _radialBlur = false;
        private Vector3 _cameraInitPosition = Vector3.zero;
        private Quaternion _cameraInitRotation = Quaternion.identity;
        private float _cameraBlendTime = 1f;
        private float _blendStartTime = -1f;

        private bool _isPause = false;

        public List<ClipInfo> AnimationList => _animationList;
        public bool isPlaying
        {
            get { return !string.IsNullOrEmpty(_currentAnimationName) || (_artCamera != null && _artCamera.IsPlaying()); }
        }
        public bool CameraAnimationInited
        {
            get { return _cameraAnimation != null; }
        }
        public bool ArtCameraEnable
        {
            get { return _artCamera != null; }
        }
        public bool ArtCameraInited
        {
            get { return _artCamera != null && _artCamera.Inited; }
        }
        public ArtCameraBattleMap ArtCamera
        {
            get { return _artCamera; }
        }
#if UNITY_EDITOR
        /// <summary>
        /// 废弃，改为animationList直接配置
        /// </summary>
        /// <param name="sceneName"></param>
        /// <param name="animation"></param>
        /// <returns></returns>
        public bool SetClips(string sceneName, Animation animation)
        {
            _animationList.Clear();
            AnimationClip[] clips = UnityEditor.AnimationUtility.GetAnimationClips(animation.gameObject);
            HashSet<string> clipNameSet = new HashSet<string>();
            foreach (var clip in clips)
            {
                if (clip == null)
                    continue;
                if (clipNameSet.Contains(clip.name))
                    continue;

                if (_setClip(sceneName, clip))
                    clipNameSet.Add(clip.name);
                else
                    return false;
            }

            if (animation.clip != null && !clipNameSet.Contains(animation.clip.name))
            {
                if (!_setClip(sceneName, animation.clip))
                    return false;
            }
            return true;
        }

        private bool _setClip(string sceneName, AnimationClip clip)
        {
            var clipNameArr = clip.name.BeginSplit('-');
            if (clipNameArr.Length < 3)
            {
                UnityEditor.EditorUtility.DisplayDialog("警告", string.Format("检查到摄像机镜头动画：{0} 命名格式错误，正确的命名为: 场景名字(或者common)-动画类型（数字）-参数（数字）", clip.name), "确定");
                clipNameArr.EndSplit();
                return false;
            }

            string scene_name = clipNameArr[0];
            if (scene_name != sceneName && scene_name != "common")     // 这里做一个通用的
            {
                UnityEditor.EditorUtility.DisplayDialog("警告", string.Format("检查到摄像机镜头动画：{0} 在[场景名字]上命名格式错误，正确的命名为: 场景名字(或者common)-动画类型（数字）-参数（数字）", clip.name), "确定");
                clipNameArr.EndSplit();
                return false;
            }

            int animationType;
            if (!clipNameArr.TryParse(1, out animationType))
            {
                UnityEditor.EditorUtility.DisplayDialog("警告", string.Format("检查到摄像机镜头动画：{0} 在[动画类型]上命名格式错误，正确的命名为: 场景名字(或者common)-动画类型（数字）-参数（数字）", clip.name), "确定");
                clipNameArr.EndSplit();
                return false;
            }

            int param;
            if (!clipNameArr.TryParse(2, out param))
            {
                UnityEditor.EditorUtility.DisplayDialog("警告", "检查到摄像机镜头动画：{0} 在[参数]上命名格式错误，正确的命名为: 场景名字(或者common)-动画类型（数字）-参数（数字）", "确定");
                clipNameArr.EndSplit();
                return false;
            }
            clipNameArr.EndSplit();

            ClipInfo clipInfo = new ClipInfo();
            clipInfo.type = animationType;
            clipInfo.param = param;
            clipInfo.clip = clip;
            _animationList.Add(clipInfo);
            return true;
        }
#endif

        private void Awake()
        {
            InitArtCamera(Camera.main);
        }

        public void InitCameraAnimation(Animation cameraAnimation)
        {
            if (cameraAnimation == null)
            {
                Diagnostic.Error("InitCameraAnimation faild [cameraAnimation == null]");
                return;
            }

            m_clicDict.Clear();
            for (int i = 0; i < _animationList.Count; ++i)
            {
                var animation = _animationList[i];
                if (animation.clip == null)
                    continue;
                if (cameraAnimation.GetClip(animation.clip.name) == null)
                    cameraAnimation.AddClip(animation.clip, animation.clip.name);
                if (!m_clicDict.ContainsKey(animation.clip.name))
                    m_clicDict.Add(animation.clip.name, animation.clip);
            }

            _cameraAnimation = cameraAnimation;

            if (cameraAnimation != null)
                _cameraControl = cameraAnimation.GetComponentInChildren<CameraPanAndZoom>(true);
        }

        #region 新的美术相机，支持FBX导入
        public void InitArtCamera(Camera cam)
        {
            if (_artCamera != null && cam != null)
            {
                _artCamera.InitializeCamera(cam);
                _artCamera.SetCallback(OnArtCameraStart, OnArtCameraEnd);
            }
        }

        private void OnArtCameraStart(int nameHash)
        {
            SetCamEnv(true);

            if (OnAnimationStart != null)
            {
                OnAnimationStart(nameHash.ToString());
            }
            ACGEventManager.Instance.Send(EventType_BattleView.ChessBattleCameraAnimationBegin, GetCourtType(nameHash));
        }

        private CameraAnimationHomeCourtType GetCourtType(int nameHash)
        {
            CameraAnimationHomeCourtType courtType = CameraAnimationHomeCourtType.None;
            if (nameHash == ArtCameraBattleMap.HOME_VIEW_WIN_STATE || nameHash == ArtCameraBattleMap.NO_HOME_VIEW_WIN_STATE)
            {
                courtType = CameraAnimationHomeCourtType.AwayFromHomeCourt;
            }
            else if (nameHash == ArtCameraBattleMap.ENTER_STATE)
            {
                courtType = CameraAnimationHomeCourtType.MoveToHomeCourt;
            }
            return courtType;
        }

        private void OnArtCameraEnd(int nameHash, bool pause, bool isBreak)
        {
            SetCamEnv(false);

            if (OnAnimationStop != null)
            {
                OnAnimationStop(nameHash.ToString(), _isPause);
            }
            ACGEventManager.Instance.Send(EventType_BattleView.ChessBattleCameraAnimationEnd, GetCourtType(nameHash));
        }

        public void StopArtCameraAnimation()
        {
            if (_artCamera != null)
            {
                _artCamera.StopCameraAnimation();
            }
        }
        #endregion

        private void SetRadialBlur(bool enable)
        {
            if (_cameraAnimation != null)
            {
                var tkpps = _cameraAnimation.GetComponentInChildren<TKPostProcessingStack>();
                if (tkpps != null)
                {
                    tkpps.radialBlur.enabled = enable;
                    tkpps.radialBlur.SampleStrength = 0f;
                    // 然后这个镜头模糊需要动态控制
                    if (enable)
                    {
                        var animation = tkpps.gameObject.TryGetComponent<TKPostProcessingStackParameterAnimation>();
                        animation.SampleStrength = 0f;
                    }
                }
            }
        }

        private void PlayAnimation(string animationName, CameraAnimationHomeCourtType courtType, bool needRadialBlur, bool fixAnimView, float blendTime)
        {
            Diagnostic.Log("Play Animation: " + animationName);

            CameraParentResetPosition();

            if (m_clicDict.TryGetValue(animationName, out AnimationClip cilp))
            {
                if (_cameraAnimation.GetClip(animationName) == null)
                    _cameraAnimation.AddClip(cilp, animationName);
                _cameraAnimation.clip = cilp;
            }

            bool success = _cameraAnimation.Play(animationName);
            if (!success)
                Diagnostic.Warn("Play Animation: " + animationName + " faild!");
            _currentAnimationName = animationName;
            _currentCourtType = courtType;
            _currentAnimationStartTime = Time.time;
            _currentAnimationTotalTime = cilp.length;
            _isPause = false;

            if (OnAnimationStart != null)
            {
                OnAnimationStart(_currentAnimationName);
            }
            ACGEventManager.Instance.Send(EventType_BattleView.ChessBattleCameraAnimationBegin, _currentCourtType);

            // 有时候局外会在某些时候自己加上这个组件 
            if (_cameraControl == null && _cameraAnimation != null)
            {
                _cameraControl = _cameraAnimation.GetComponentInChildren<CameraPanAndZoom>(true);
            }

            _radialBlur = needRadialBlur;

            if (_cameraControl != null && fixAnimView)
            {
#if !OUTSOURCE
                if (LoginManager.Instance.IsInGame)
                {
#endif
                    _needRevertCamPosAndRot = true;
                    _cameraInitPosition = _cameraControl.transform.localPosition;
                    _cameraInitRotation = _cameraControl.transform.localRotation;
                    _cameraBlendTime = blendTime;
                    _blendStartTime = -1;
#if !OUTSOURCE
                }
                else

                {
                    // 如果局外则需要临时将缩放组件给干掉，保证展示的一致性
                    _cameraControl.enabled = false;
                    _cameraControl.RevertInitCamParam();
                    _needRevertCamPosAndRot = false;
                }
#endif
            }
            else
            {
                _needRevertCamPosAndRot = false;
            }

            SetCamEnv(true);
        }

        private void CameraParentResetPosition()
        {
            // 还原位置
            if (_cameraAnimation != null)
            {
                _cameraAnimation.Stop();
                _cameraAnimation.clip = null;

                var t = _cameraAnimation.transform;
                ChessUtil.MakeTransformIdentity(t);
            }
        }

        private void OnAnimationEnd()
        {
            var animationName = _currentAnimationName;

            if (!_isPause)
            {
                CameraParentResetPosition();
                SetCamEnv(false);
            }

            _radialBlur = false;
            _needRevertCamPosAndRot = false;

            var courtType = _currentCourtType;
            _currentAnimationName = string.Empty;
            _currentCourtType = CameraAnimationHomeCourtType.None;

            if (OnAnimationStop != null)
            {
                OnAnimationStop(animationName, _isPause);
            }
            ACGEventManager.Instance.Send(EventType_BattleView.ChessBattleCameraAnimationEnd, courtType);
        }

        public void OnTurnStart(int turnCount, bool preview = false)
        {
            CameraParentResetPosition();

            if (_cameraAnimation != null)
            {
                if (_artCamera != null)
                {
                    _artCamera.StopCameraAnimation(false);
                    //if (!preview || turnCount != 1)
                    {
                        _artCamera.SetIntParam(ArtCameraBattleMap.TURN_COUNT, turnCount);
                        _artCamera.SetTrigger(ArtCameraBattleMap.TURN_START_TRIGGER);
                    }
                    //else
                    //{
                    //    _artCamera.PlayEnterState();
                    //}
                    _artCamera.UpdateAnimator();
                }

                for (int i = 0; i < _animationList.Count; ++i)
                {
                    ClipInfo animation = _animationList[i];
                    if (animation.type == CLIP_TYPE_TURN_START && animation.clip != null)
                    {
                        string animationName = animation.clip.name;
                        if (animation.param == turnCount)
                        {
                            Diagnostic.Log("[OnTurnStart] Camera Animation =>> " + animationName);
                            PlayAnimation(animationName, animation.courtType, animation.needRadialBlur, animation.fixAnimView, animation.blendTime);
                            break;
                        }
                        else
                        {
                            //在当前回合，如果还有其他的回合动画在播，那不正常
                            if (_cameraAnimation.IsPlaying(animationName))
                            {
                                //_cameraAnimation.Stop();
                                BattleMapManager.Instance.StopSceneCameraAnimation();
                            }
                        }
                    }
                }
            }
        }

        public void OnRounSelectStart(int pos)
        {
            CameraParentResetPosition();

            if (_cameraAnimation != null)
            {
                if (_artCamera != null)
                {
                    _artCamera.StopCameraAnimation();
                    _artCamera.SetIntParam(ArtCameraBattleMap.ROUND_SELECT_POS, pos);
                    _artCamera.SetTrigger(ArtCameraBattleMap.ROUND_SELECT_START_TRIGGER);
                    _artCamera.UpdateAnimator();
                }

                for (int i = 0; i < _animationList.Count; ++i)
                {
                    var animation = _animationList[i];
                    if (animation.type == CLIP_TYPE_ROUND_SELECT_START && animation.param == pos && animation.clip != null)
                    {
                        var animationName = animation.clip.name;
                        Diagnostic.Log("[OnRounSelectStart] Camera Animation =>> " + animationName);
                        PlayAnimation(animationName, animation.courtType, animation.needRadialBlur, animation.fixAnimView, animation.blendTime);
                        break;
                    }
                }
            }
        }

        public void OnFirstRankFoucs(bool isHomeField, bool isSpecial)
        {
            CameraParentResetPosition();

            if (_cameraAnimation != null)
            {
                if (_artCamera != null)
                {
                    _artCamera.StopCameraAnimation();
                    _artCamera.SetBoolParam(ArtCameraBattleMap.IS_HOME_FIELD, isHomeField);
                    _artCamera.SetTrigger(ArtCameraBattleMap.USER_OUT_TRIGGER);
                    _artCamera.UpdateAnimator();
                }

                int param = isHomeField ? 0 : 1;
                for (int i = 0; i < _animationList.Count; ++i)
                {
                    var animation = _animationList[i];
                    if (animation.type == CLIP_TYPE_USER_OUT)
                    {
                        if (animation.param == param && animation.isSpecial == isSpecial && animation.clip != null)
                        {
                            var animationName = animation.clip.name;
                            Diagnostic.Log("[OnFirstRankFoucs] Camera Animation =>> " + animationName);
                            PlayAnimation(animationName, animation.courtType, animation.needRadialBlur, animation.fixAnimView, animation.blendTime);
#if !OUTSOURCE
                            if (LoginManager.Instance.IsInGame)
                                _currentAnimationName = string.Empty;       // 吃鸡动画是最后一个动画了，不需要监听play end了
#endif
                            break;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 是否拥有特殊的吃鸡镜头动画
        /// </summary>
        /// <returns></returns>
        public bool HashFirstRankSpecialCameraEffects(ref float playTime)
        {
            if (_cameraAnimation != null)
            {
                for (int i = 0; i < _animationList.Count; ++i)
                {
                    var animation = _animationList[i];
                    if (animation.type == CLIP_TYPE_USER_OUT && animation.isSpecial)
                    {
                        if (animation.clip != null)
                        {
                            playTime = animation.clip.length;
                            return true;
                        }
                    }
                }

                if (_artCamera != null)
                {
                    playTime = _artCamera.GetFirstRankStateTime();
                    if (playTime > 0)
                        return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 是否有相机动画在播放中
        /// </summary>
        /// <returns></returns>
        private bool HasCamAnimPlaying(bool containsBlend)
        {
            if (containsBlend)
                return !string.IsNullOrEmpty(_currentAnimationName);
            else
                return Time.time - _currentAnimationStartTime < _currentAnimationTotalTime || (_cameraAnimation != null && _cameraAnimation.isPlaying);
        }

        private void Update()
        {
            if (HasCamAnimPlaying(true))
            {
                if (HasCamAnimPlaying(false))
                {
                    if (OnAnimationUpdate != null)
                    {
                        OnAnimationUpdate(_currentAnimationName, _isPause);
                    }
                }
                else if (_needRevertCamPosAndRot && (_blendStartTime < 0 || _blendStartTime + _cameraBlendTime > Time.time) && !_isPause)       // 暂停阶段忽略掉这个blend的过程，直接end就好了
                {
                    if (_blendStartTime < 0)
                    {
                        _blendStartTime = Time.time;
                    }
                    else
                    {
                        if (_cameraTrans == null)
                        {
                            var cam = _cameraAnimation.GetComponentInChildren<Camera>();
                            if (cam != null)
                                _cameraTrans = cam.transform;
                        }

                        if (_cameraTrans != null)
                        {
                            float percent = (Time.time - _blendStartTime) / _cameraBlendTime;
                            _cameraTrans.localPosition = Vector3.Slerp(_cameraTrans.localPosition, _cameraInitPosition, percent);
                            _cameraTrans.localRotation = Quaternion.Slerp(_cameraTrans.localRotation, _cameraInitRotation, percent);
                        }
                    }
                }
                else
                {
                    OnAnimationEnd();
                }
            }
        }

        public void AddClip(AnimationClip animationClip, string clipName)
        {
            _cameraAnimation.AddClip(animationClip, clipName);
        }

        public void PlayAnimationClip(string clipName)
        {
            _cameraAnimation.Play(clipName);
        }

        private static bool needReopenOutline;
        private void SetCamEnv(bool isPlaying)
        {
            // isPlaying为true的时候要关描边，false的时候要开描边
            // --bug=117273621 【#897】【局内】【竞技场】狂风绝息之地+设置开启角色描边会有显示问题
            if (GameLOD.Instance.Outline)
            {
                if (isPlaying)
                {
                    GameLOD.Instance.ChangeOutline(false);
                    needReopenOutline = true;
                }
            }
            else
            {
                if (!isPlaying && needReopenOutline)
                {
                    GameLOD.Instance.ChangeOutline(true);
                    needReopenOutline = false;
                }
            }

            if (_radialBlur)
            {
                SetRadialBlur(isPlaying);
            }
            else
            {
                SetRadialBlur(false);
            }

            if (_cameraControl != null
#if !OUTSOURCE
                && LoginManager.Instance.IsInGame
#endif
                )
            {
                // 游戏结束则不重新启用这个组件了，因为结束的动画不需要恢复到原来的位置
#if !OUTSOURCE
                var gameState = ChessModelManager.Instance.GetBattleModel().GetCurPlayerModel().GetBattleTurnModel().CurGameState;
                if (gameState == ZGameClient.TAC_GameState.TAC_GameState_Finish)
                    _cameraControl.enabled = false;
                else
#endif
                    _cameraControl.enabled = !isPlaying;

                if (_needRevertCamPosAndRot)
                {
                    if (isPlaying)
                    {
                        _cameraControl.RevertInitCamParam();
                        _cameraControl.ResetToOriginEanble = false;
                    }
                    else
                    {
                        _cameraControl.ResetToOriginEanble = true;
                        _cameraControl.RevertOriginParam();
                    }
                }
            }
#if !OUTSOURCE
            var buim = ChessBattleGlobal.Instance.BattleUnitInfoMgr;
            if (buim != null)
            {
                buim.SetActive(!isPlaying);
            }
#endif

            if (!isPlaying)
            {
                if (BattleMapManager.Instance != null)
                    BattleMapManager.Instance.OnCameraPositionChanged();
            }
        }

        public void PauseAnim()
        {
            PauseArtCamera();
            PauseAnimV1();
        }

        private void PauseAnimV1()
        {
            if (!HasCamAnimPlaying(true))
                return;

            if (!_isPause)
            {
                _isPause = true;

                CameraParentResetPosition();
                SetCamEnv(false);
            }
        }

        private void PauseArtCamera()
        {
            if (_artCamera != null)
            {
                _artCamera.SetPause(true);

                if (_artCamera.IsPlaying())
                {
                    CameraParentResetPosition();
                    SetCamEnv(false);
                }
            }
        }

        public void ResumeAnim()
        {
            ResumeArtCamera();
            ResumeAnimV1();
        }

        private void ResumeAnimV1()
        {
            if (!HasCamAnimPlaying(true))
                return;

            if (_isPause)
            {
                _isPause = false;

                CameraParentResetPosition();
                SetCamEnv(true);

                if (!string.IsNullOrEmpty(_currentAnimationName) && Time.time - _currentAnimationStartTime < _currentAnimationTotalTime)
                {
                    if (m_clicDict.TryGetValue(_currentAnimationName, out AnimationClip clip))
                    {
                        if (_cameraAnimation.GetClip(_currentAnimationName) == null)
                            _cameraAnimation.AddClip(clip, _currentAnimationName);
                        _cameraAnimation.clip = clip;
                    }

                    _cameraAnimation[_currentAnimationName].time = Time.time - _currentAnimationStartTime;

                    bool success = _cameraAnimation.Play(_currentAnimationName);
                    if (!success)
                        Diagnostic.Warn("ResumeAnim : " + _currentAnimationName + " faild!");
                }
            }
        }

        private void ResumeArtCamera()
        {
            if (_artCamera != null)
            {
                _artCamera.SetPause(false);

                if (_artCamera.IsPlaying())
                {
                    CameraParentResetPosition();
                    SetCamEnv(true);
                }
            }
        }

        public void StopAnim()
        {
            StopArtCamera();
            StopAnimV1();
        }

        private void StopArtCamera()
        {
            _artCamera?.StopCameraAnimation();
        }

        private void StopAnimV1()
        {
            if (!HasCamAnimPlaying(true))
                return;

            if (!_isPause)
            {
                CameraParentResetPosition();
                SetCamEnv(false);
            }

            _currentAnimationName = string.Empty;
            _isPause = false;
        }
    }
}
