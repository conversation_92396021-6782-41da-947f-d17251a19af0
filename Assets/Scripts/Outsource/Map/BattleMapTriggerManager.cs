using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using TKFrame;
using TKPlugins;
using UnityEngine;
using ZGame;
using ZGameClient;
using Coroutine = UnityEngine.Coroutine;
using E_CT = ZGameChess.BattleMapTriggerAminationConfig.E_CheckType;
using E_ET = ZGameChess.BattleMapTriggerAminationConfig.E_ExtraType;
using E_MODT = ZGameChess.BattleMapTriggerAminationConfig.E_MapModifyType;
using ED = ZGameChess.BattleMapTriggerAminationConfig.ExtraData;

namespace ZGameChess
{
    /// <summary>
    /// 棋盘效果触发管理器
    /// Author : shinychen
    /// Date : 2020.10.27
    /// </summary>
    public class BattleMapTriggerManager
    {
        private List<BattleMapTriggerAminationConfig> m_triggerAnimList;
        private TKDictionary<string, List<BattleMapTriggerAminationConfig>> m_eventAnimDic;
        private List<BattleMapTriggerAminationConfig> m_cacheAnim = new List<BattleMapTriggerAminationConfig>();
        private HashSet<int> m_rankReachHash = new HashSet<int>();
        private GameObject root;
        private BattleMapManager m_mapMgr;
        private int mapId;
        private List<ChessMapLogicEvent> m_eventLogicList;
        private Collider[] m_eventTriggerColliders;
        private List<int> m_tempListForLogicEvent;
        private List<int> m_resultListForLogicEvent;
        private List<int> m_lastListForLogicEvent;
        private HashSet<int> m_CfgTempSet;
        private GameObject[] m_onlyHomeShowGos;
        private GameObject[] m_onlyNotHomeShowGos;
        private Coroutine m_reachTimeTriggerCoroutine;
        private YieldInstruction m_reachTimeTriggerCD;

        private class CDInfo
        {
            public TKDictionary<BattleMapTriggerAminationConfig, float> animConfigToLastTimeDic;
            public HashSet<int> fetterDataSet;
            public HashSet<int> heroPromotionDataSet;
            public int lastFetterCfgCount;
            public int lastHeroPromotionCfgCount;
        }
        private TKDictionary<int, CDInfo> m_dicMapOwnerIDToCDInfoDic;

        private class FlyEffectInfo
        {
            public Vector3 sourcePos = Vector3.zero;
            public UnityEngine.Coroutine coroutine;
        }
        private TKDictionary<BattleMapTriggerAminationConfig, FlyEffectInfo> m_animConfigToFlyEffectInfoDic;
        private bool m_flyEffectSwitchedPlayer;

        private class ReachTimeTriggerInfo
        {
            public bool triggered;
            public double leftSeconds;
            public BattleMapTriggerAminationConfig config;
            public DateTime initTime;
        }
        private List<ReachTimeTriggerInfo> m_reachTimeTriggerInfoList;
        private int m_leftReachTimeTriggerCount;
        private List<BattleMapTriggerAminationConfig> m_needTriggerCfgs;
        private BattleMapConfig m_mapConfig;
        private ChessPlayerUnit m_previewSelfUnit;
        private Transform m_fakeEnemyTrans;
        private GameObject m_sourceInterest;
        private GameObject m_sourceInterest2;
        private TKDictionary<string, string> m_overrideSoundInfoDic;

        public void Init(BattleMapManager mapMgr, BattleMapConfig config, int mapId)
        {
            m_mapMgr = mapMgr;

            this.mapId = mapId;
            this.m_cacheAnim.Clear();

            UpdateConfig(config);

            m_sourceInterest = m_mapMgr.TryGetSourceInterest(config.InterestTemplate, true, mapId);
            m_sourceInterest2 = m_mapMgr.TryGetSourceInterest(config.InterestTemplate2, false, mapId);
            if (m_sourceInterest != null) m_mapConfig.InterestTemplate = m_sourceInterest;
            if (m_sourceInterest2 != null) m_mapConfig.InterestTemplate2 = m_sourceInterest2;

            if (config.m_eventAnimationList != null)
            {
                if (m_eventAnimDic != null && m_eventAnimDic.Count > 0)
                {
                    RegistEvents();
                }

                TriggerWhenMapLoaded();
            }

            TrySaveEffectRefInBlockSlots();
        }

        public void UpdateConfig(BattleMapConfig config)
        {
            this.m_triggerAnimList = config.m_triggerAnimationList;
            this.m_eventLogicList = config.m_eventLogicList;
            this.m_eventTriggerColliders = config.m_eventTriggerColliders;
            this.m_onlyHomeShowGos = config.m_onlyHomeShowGos;
            this.m_onlyNotHomeShowGos = config.m_onlyNotHomeShowGos;
            this.m_cacheAnim.Clear();
            this.m_mapConfig = config;
            this.root = config.gameObject;

            if (config.m_eventAnimationList != null)
            {
                foreach (var animConfig in config.m_eventAnimationList)
                {
                    animConfig.Init();
                }

                //测试============================================
                //var debugCfg = new BattleMapTriggerAminationConfig();
                //debugCfg.m_enterAnimationName = "test";

                //var debugData = new BattleMapTriggerAminationConfig.ConfigData();
                //debugData.type = E_CT.REACH_TIME;
                //debugData.checkValue = 0;
                //debugData.extraMap = new TKDictionary<string, BattleMapTriggerAminationConfig.ExtraData>();
                //var debugEXData = new BattleMapTriggerAminationConfig.ExtraData();
                //debugEXData.stringParam = "11:20:30";
                //debugData.extraMap.Add(E_ET.SERVER_TIME, debugEXData);

                //var debugExCDData = new BattleMapTriggerAminationConfig.ExtraData();
                //debugExCDData.floatParam = 20;
                //debugData.extraMap.Add(E_ET.CD_TIME, debugExCDData);

                //debugCfg.DebugSetEnterAnimData(debugData);

                //config.m_eventAnimationList.Add(debugCfg);
                //测试============================================

                m_eventAnimDic = new TKDictionary<string, List<BattleMapTriggerAminationConfig>>();
                for (int i = 0, n = config.m_eventAnimationList.Count; i < n; i++)
                {
                    var eventConfig = config.m_eventAnimationList[i];
                    var enterData = eventConfig.GetEnterAnimData();
                    if (enterData != null)
                    {
                        string type = enterData.type;
                        List<BattleMapTriggerAminationConfig> list = null;
                        if (!m_eventAnimDic.TryGetValue(type, out list))
                        {
                            list = new List<BattleMapTriggerAminationConfig>();
                            m_eventAnimDic[type] = list;
                        }

                        list.Add(eventConfig);
                    }
                }
            }
        }

        private void TrySaveEffectRefInBlockSlots()
        {
            if (m_mapConfig == null)
            {
                return;
            }

            var dynamicObjectConfigs = m_mapConfig.m_DynamicObj;

            if (dynamicObjectConfigs == null || dynamicObjectConfigs.Count == 0)
            {
                return;
            }

            if (m_triggerAnimList != null)
            {
                foreach (var animConfig in m_triggerAnimList)
                {
                    animConfig.SaveObjectNameList(dynamicObjectConfigs);
                }
            }

            if (m_eventAnimDic != null)
            {
                foreach (var kvp in m_eventAnimDic)
                {
                    if (kvp.Value != null)
                    {
                        foreach (var animConfig in kvp.Value)
                        {
                            animConfig.SaveObjectNameList(dynamicObjectConfigs);
                        }
                    }
                }
            }

            if (m_eventLogicList != null)
            {
                foreach (var logicConfig in m_eventLogicList)
                {
                    if (logicConfig.triggerAnimCfgList != null)
                    {
                        foreach (var animConfig in logicConfig.triggerAnimCfgList)
                        {
                            animConfig.SaveObjectNameList(dynamicObjectConfigs);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 把新的动态物体，还原到动效中所有之前的引用中
        /// 例如之前的动效，引用了旧的animator，这里把animator指向新的动态物体里的animator
        /// </summary>
        public void RevertSlotObjectReference(Transform blockTrans)
        {
            if (m_triggerAnimList != null)
            {
                foreach (var animConfig in m_triggerAnimList)
                {
                    animConfig.RevertObjectReference(blockTrans);
                }
            }

            if (m_eventAnimDic != null)
            {
                foreach (var kvp in m_eventAnimDic)
                {
                    if (kvp.Value != null)
                    {
                        foreach (var animConfig in kvp.Value)
                        {
                            animConfig.RevertObjectReference(blockTrans);
                        }
                    }
                }
            }

            if (m_eventLogicList != null)
            {
                foreach (var logicConfig in m_eventLogicList)
                {
                    if (logicConfig.triggerAnimCfgList != null)
                    {
                        foreach (var animConfig in logicConfig.triggerAnimCfgList)
                        {
                            animConfig.RevertObjectReference(blockTrans);
                        }
                    }
                }
            }
        }

        public void OverrideSoundInfos(int planID, int regionFrom1)
        {
            if (m_triggerAnimList != null)
            {
                foreach (var animConfig in m_triggerAnimList)
                {
                    OverrideSoundInfo(animConfig, planID, regionFrom1);
                }
            }

            if (m_eventAnimDic != null)
            {
                foreach (var kvp in m_eventAnimDic)
                {
                    if (kvp.Value != null)
                    {
                        foreach (var animConfig in kvp.Value)
                        {
                            OverrideSoundInfo(animConfig, planID, regionFrom1);
                        }
                    }
                }
            }

            if (m_eventLogicList != null)
            {
                foreach (var logicConfig in m_eventLogicList)
                {
                    if (logicConfig.triggerAnimCfgList != null)
                    {
                        foreach (var animConfig in logicConfig.triggerAnimCfgList)
                        {
                            OverrideSoundInfo(animConfig, planID, regionFrom1);
                        }
                    }
                }
            }
        }

        private void OverrideSoundInfo(BattleMapTriggerAminationConfig config, int planID, int regionFrom1)
        {
            string sourceBankName = "UI_Chessboard_DragonArena";
            string overrideBankName = "UI_Chessboard_DragonArena_" + planID;

            bool revertToSource = planID == 0;

            //以后改成在BattleMapConfig里配
            switch (regionFrom1)
            {
                case 1:
                    OverrideSoundConfig(config, sourceBankName, "Play_UI_Chessboard_DragonArena_Intro",
                        overrideBankName, "Play_UI_Chessboard_DragonArena_Intro_" + planID + "_" + regionFrom1, revertToSource);

                    OverrideSoundConfig(config, sourceBankName, "Play_UI_Chessboard_DragonArena_Win_Dragon",
                        overrideBankName, "Play_UI_Chessboard_DragonArena_Win_Dragon_" + planID + "_" + regionFrom1, revertToSource);

                    OverrideSoundConfig(config, sourceBankName, "Play_UI_Chessboard_DragonArena_Ending",
                        overrideBankName, "Play_UI_Chessboard_DragonArena_Ending_" + planID + "_" + regionFrom1, revertToSource);

                    break;

                case 2:
                    OverrideSoundConfig(config, sourceBankName, "Play_UI_Chessboard_DragonArena_BackChessboard",
                        overrideBankName, "Play_UI_Chessboard_DragonArena_BackChessboard_" + planID + "_" + regionFrom1, revertToSource);

                    OverrideSoundConfig(config, sourceBankName, "Play_UI_Chessboard_DragonArena_Lose_Box",
                        overrideBankName, "Play_UI_Chessboard_DragonArena_Lose_Box_" + planID + "_" + regionFrom1, revertToSource);

                    OverrideSoundConfig(config, sourceBankName, "Play_UI_Chessboard_DragonArena_Win_Box",
                        overrideBankName, "Play_UI_Chessboard_DragonArena_Win_Box_" + planID + "_" + regionFrom1, revertToSource);

                    OverrideSoundConfig(config, sourceBankName, "Play_UI_Chessboard_DragonArena_Interact_Box",
                        overrideBankName, "Play_UI_Chessboard_DragonArena_Interact_Box_" + planID + "_" + regionFrom1, revertToSource);

                    OverrideSoundConfig(config, sourceBankName, "Play_UI_Chessboard_DragonArena_WinVFX_Box",
                        overrideBankName, "Play_UI_Chessboard_DragonArena_WinVFX_Box_" + planID + "_" + regionFrom1, revertToSource);

                    OverrideSoundConfig(config, sourceBankName, "Play_UI_Chessboard_DragonArena_Interact_Box",
                        overrideBankName, "Play_UI_Chessboard_DragonArena_Interact_Box_" + planID + "_" + regionFrom1, revertToSource);
                    break;
                case 4:
                    OverrideSoundConfig(config, sourceBankName, "Play_UI_Chessboard_DragonArena",
                        overrideBankName, "Play_UI_Chessboard_DragonArena_" + planID + "_" + regionFrom1, revertToSource);

                    break;
            }
        }

        private void OverrideSoundConfig(BattleMapTriggerAminationConfig config, string sourceBank, string sourceSound,
            string overrideBank, string overrideSound, bool revertToSource)
        {
            if (config.m_playSound && config.m_bankName.Equals(sourceBank) && config.m_soundName.Equals(sourceSound))
            {
                DoOverrideSoundInfo(sourceBank, sourceSound, revertToSource? sourceBank:overrideBank, revertToSource ? sourceSound : overrideSound);
            }

            if (config.m_soundInfos != null)
            {
                foreach (var soundInfo in config.m_soundInfos)
                {
                    DoOverrideSoundInfo(soundInfo.soundBank, soundInfo.soundEvent, revertToSource ? sourceBank : overrideBank, revertToSource ? sourceSound : overrideSound);
                }
            }
        }

        private void DoOverrideSoundInfo(string sourceBank, string sourceSound, string overrideBank, string overrideSound)
        {
            if (m_overrideSoundInfoDic == null)
            {
                m_overrideSoundInfoDic = new TKDictionary<string, string>();
            }

            var source = sourceBank + "|" + sourceSound;
            if (m_overrideSoundInfoDic.ContainsKey(source))
            {
                m_overrideSoundInfoDic[source] = overrideBank+ "|" + overrideSound;
            }
            else
            {
                m_overrideSoundInfoDic.Add(source, overrideBank + "|" + overrideSound);
            }
        }

        private void RegistEvents()
        {
            ACGEventManager.Instance.AddEventListener(EventType_BattleView.AutoChess_Battle_RoundSelect, OnRoundSelect);

            ACGEventManager.Instance.AddEventListener(EventType_BattleView.AutoChess_Battle_TurnStart, OnTurnStart);
            ACGEventManager.Instance.AddEventListener(EventType_BattleView.AutoChess_Battle_TurnEnd, OnTurnEnd);
            ACGEventManager.Instance.AddEventListener(EventType_BattleView.AutoChess_Battle_Get_Fetter, DebugOnFetterGet);
            ACGEventManager.Instance.AddEventListener(EventType_BattleView.AutoChess_Battle_Reach_Hero_Star, DebugOnReachHeroLevel);
            ACGEventManager.Instance.AddEventListener(EventType_BattleView.AutoChess_Battle_UpdateLevel, OnUpdateCurrentPlayerLevel);
            ACGEventManager.Instance.AddEventListener(EventType_BattleView.Application_Pause_Focus_Back, OnApplicationPauseFocusBack);
            var battleModel = ChessModelManager.Instance.GetBattleModel();
            if (battleModel != null)
            {
                battleModel.ObserverList -= HandleBattleEvent;
                battleModel.ObserverList += HandleBattleEvent;
            }
        }

        private void RemoveEvents()
        {
            ACGEventManager.Instance.RemoveEventListener(EventType_BattleView.AutoChess_Battle_RoundSelect, OnRoundSelect);

            ACGEventManager.Instance.RemoveEventListener(EventType_BattleView.AutoChess_Battle_TurnStart, OnTurnStart);
            ACGEventManager.Instance.RemoveEventListener(EventType_BattleView.AutoChess_Battle_TurnEnd, OnTurnEnd);
            ACGEventManager.Instance.RemoveEventListener(EventType_BattleView.AutoChess_Battle_Get_Fetter, DebugOnFetterGet);
            ACGEventManager.Instance.RemoveEventListener(EventType_BattleView.AutoChess_Battle_Reach_Hero_Star, DebugOnReachHeroLevel);
            ACGEventManager.Instance.RemoveEventListener(EventType_BattleView.AutoChess_Battle_UpdateLevel, OnUpdateCurrentPlayerLevel);
            ACGEventManager.Instance.RemoveEventListener(EventType_BattleView.Application_Pause_Focus_Back, OnApplicationPauseFocusBack);

            var battleModel = ChessModelManager.Instance.GetBattleModel();
            if (battleModel != null)
            {
                battleModel.ObserverList -= HandleBattleEvent;
            }
        }

        public List<BattleMapTriggerAminationConfig> GetTriggerAniListByType(string type)
        {
            List<BattleMapTriggerAminationConfig> ret = null;
            if (m_eventAnimDic != null) 
            {
                m_eventAnimDic.TryGetValue(type, out ret);
            };
            return ret;
        }

        public void OnTurnStart(int turnCount)
        {
            //恢复播放缓存效果
            PlayCacheAnimEffects();

            if (m_eventAnimDic != null && m_eventAnimDic.Count > 0)
            {
                var model = ChessModelManager.Instance.GetBattleModel();
                if (turnCount == 2)
                {
                    if (!model.IsHundredMode())
                    {
                        //第一回合是轮抽，第二回合才正式作为游戏开始阶段
                        if (m_eventAnimDic.TryGetValue(E_CT.GAME_START, out List<BattleMapTriggerAminationConfig> list1))
                        {
                            foreach (var config in list1)
                            {
                                if (config != null)
                                {
                                    var enterData = config.GetEnterAnimData();
                                    if (enterData != null)
                                    {
                                        PlayTriggerEffect(config, enterData.animName);

                                        //游戏开始特殊处理
                                        ExecuteGameStart(config.m_animator, enterData);
                                    }
                                }
                            }
                        }

                        CheckGameStartTimeAnim(false);
                    }
                }

                if (m_eventAnimDic.TryGetValue(E_CT.TURN_COUNT, out List<BattleMapTriggerAminationConfig> list2))
                {
                    foreach (var config in list2)
                    {
                        if (config != null)
                        {
                            var enterData = config.GetEnterAnimData();
                            if (enterData != null)
                            {
                                bool isTrigger;
                                if (enterData.checkValue == 0)
                                {
                                    //配置是0，那么每回合触发，但是要排除轮抽和入场
                                    var battleModel = ChessModelManager.Instance.GetBattleModel();
                                    isTrigger = turnCount > 1 && battleModel != null && !battleModel.IsRoundSelect();
                                }
                                else
                                {
                                    isTrigger = enterData.checkValue == turnCount;
                                }

                                //达成某个回合阶段，播放动画
                                if (isTrigger)
                                {
                                    PlayTriggerEffect(config, enterData.animName);
                                }
                            }
                        }
                    }
                }

                if (m_eventAnimDic.TryGetValue(E_CT.BATTLE_STAGE, out List<BattleMapTriggerAminationConfig> list3))
                {
                    if (model.CurrentPlayerId != -1)
                    {
                        //局外预览的时候，没有局内数据，所以不触发
                        TTAC_Quest_Client quest = model.SoGameData_View.GetQuestData(model.CurrentTurnCount);
                        if (quest != null && quest.iRound == 1)
                        {
                            //阶段的第一回合，播放动效
                            int curStage = quest.iStage;
                            foreach (var config in list3)
                            {
                                if (config != null)
                                {
                                    var enterData = config.GetEnterAnimData();
                                    if (enterData != null && enterData.checkValue == curStage)
                                    {
                                        //达成某个战斗阶段，播放动效
                                        PlayTriggerEffect(config, enterData.animName);
                                        TryChangeInterest(config);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            #region Magic Code  大头棋盘（300031）开场动画属于 特性，先用特殊代码使之生效
            //前人的意思是，if (bmtm != this)，在回合开始时。触发在后台的其他棋盘的对应回合阶段动效，以此达到后台的棋盘也能响应回合阶段的目的

            //List<int> mapidlist = new List<int>() { 300031, 300030 };
            if (m_mapMgr != null)
            {
                List<int> mapidlist = m_mapMgr.GetAllMapIDList();
                for (int i = 0; i < mapidlist.Count; i++)
                {
                    int mapid = mapidlist[i];
                    BattleMap battleMap = m_mapMgr.GetMapDataById(mapid);
                    if (battleMap != null)
                    {
                        if (turnCount > 2)
                        {
                            BattleMapTriggerManager bmtm = battleMap.GetBattleMapTrggierManager();
                            if (bmtm != this)
                            {
                                bmtm.PlayTurnStartAnim();
                            }
                        }
                    }
                }
            }

            #endregion
        }

        /// <summary>
        /// 选秀回合开始
        /// </summary>
        /// <param name="e"></param>
        public void OnRoundSelect(GEvent e)
        {
            var model = ChessModelManager.Instance.GetBattleModel();
            if (model != null)
            {
                TTAC_Quest_Client quest = ChessModelManager.Instance.GetBattleModel().SoGameData_View.GetQuestData(model.CurrentTurnCount);
                if (quest != null)
                {
                    int stage = quest.iStage;

                    if (m_eventAnimDic != null && m_eventAnimDic.Count > 0)
                    {
                        if (m_eventAnimDic.TryGetValue(E_CT.ROUND_SELECT_START, out List<BattleMapTriggerAminationConfig> list2))
                        {
                            foreach (var config in list2)
                            {
                                var enterData = config.GetEnterAnimData();
                                if (enterData != null && enterData.checkValue == stage)
                                {
                                    //选秀开始，播放动画
                                    PlayTriggerEffect(config, enterData.animName);
                                }
                            }
                        }
                    }
                }
            }

        }

        /// <summary>
        /// 准备阶段开始
        /// </summary>
        
        public void OnTurnStart(GEvent e)
        {
            var model = ChessModelManager.Instance.GetBattleModel();
            if (model != null)
            {
                OnTurnStart(model.CurrentTurnCount);
            }

            var curMap = BattleMapManager.Instance.GetCurrentBattleMap();
            //KDA棋盘，热更特殊处理，未来换实现
            if (curMap != null && curMap.MapId == 300081)
            {
                //KDA比较特殊，需要在战斗阶段到达的时候，上升棋盘，这里不能重回，不然会直接到上升的最后一帧
                return;
            }

            //客场返回，会触发回合开始，此时尝试触发重回
                var playerCtrl = ChessBattleGlobal.Instance.ChessPlayerCtrl;
            if (playerCtrl != null)
            {
                playerCtrl.DoAction(UnitTriggerMapLogicArea);
            }
        }

        private void UnitTriggerMapLogicArea(int playerID, ChessPlayerUnit unit)
        {
            if (unit != null)
            {
                unit.TriggerMapLogic(true);

                var model = ChessModelManager.Instance.GetBattleModel();
                bool isGameStart = model != null && model.CurrentTurnCount == 2;

                if (!isGameStart)
                {
                    //这里由回合开始触发，分三种情况
                    //1、整场战斗开始
                    //2、客场战斗结束回主场
                    //3、主客场战斗阶段开始

                    //整场战斗开始的时候，不需要触发重回
                    unit.TriggerMapRegain();
                }
            }
        }

        public void PlayTurnStartAnim()
        {
            if (m_eventAnimDic != null && m_eventAnimDic.Count > 0)
            {
                if (m_eventAnimDic.TryGetValue(E_CT.TURN_COUNT, out List<BattleMapTriggerAminationConfig> list2))
                {
                    int turnCount = 0; 
                    var model = ChessModelManager.Instance.GetBattleModel();
                    if (model != null)
                    {
                        turnCount = model.CurrentTurnCount;
                    }

                    foreach (var config in list2)
                    {
                        var enterData = config.GetEnterAnimData();
                        if (enterData != null && enterData.checkValue == turnCount)
                        {
                            //达成某个回合阶段，播放动画
                            PlayTriggerEffect(config, enterData.animName);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 回合结束
        /// </summary>
        /// <param name="e"></param>
        private void OnTurnEnd(GEvent e)
        {
            if (m_eventAnimDic == null || m_eventAnimDic.Count == 0)
            {
                return;
            }

            var battleModel = ChessModelManager.Instance.GetBattleModel();
#if !OUTSOURCE
            var turnModel = battleModel.GetMyPlayerModel().GetBattleTurnModel();
            if (turnModel.IsMonsterLevel(battleModel.CurrentTurnCount))
            {
                //野怪回合不触发胜利效果
                return;
            }
#endif

            AutoChessBattle_RoundResult resultData = (AutoChessBattle_RoundResult)e.objData;

            PlayerModel pm = battleModel.GetCurPlayerModel();
            if (pm != null && (pm.PlayerId == resultData.BattleIndex || pm.PlayerId == resultData.Opponent_BattleIndex))
            {
                //只有在当前被观战者视角才能触发效果
                if (pm.IsHomeCourt())
                {
                    //观战玩家在主场
                    if (resultData.RoundResult == AutoChessBattle_RoundResult_Enum.HOME_WIN)
                    {
                        CheckWinCount(pm.iConWinCount);
                    }
                    else
                    {
                        CheckLoseCount(pm.iConLoseCount);
                    }
                }
                else
                {
                    var targetModel = battleModel.GetPlayerModel(pm.EnemyPlayerID);
                    if (targetModel != null)
                    {
                        //播放客场玩家胜利动画
                        if (resultData.RoundResult == AutoChessBattle_RoundResult_Enum.HOME_WIN)
                        {
                            CheckWinCount(targetModel.iConWinCount);
                        }
                        else
                        {
                            CheckLoseCount(targetModel.iConLoseCount);
                        }
                    }
                }

                CheckWinRank();
            }

            //找到自己的result，发出去
            if (resultData.BattleIndex == battleModel.MyPlayerId)
            {
                TryTriggerWinCountAndTime(resultData.Home_ConWinCount);
            }
            else if (resultData.Opponent_BattleIndex == battleModel.MyPlayerId)
            {
                TryTriggerWinCountAndTime(resultData.Opponent_ConWinCount);
            }
        }

        /// <summary>
        /// 某英雄达成某星级
        /// </summary>
        /// <param name="e"></param>
        private void DebugOnReachHeroLevel(GEvent e)
        {
            List<BattleMapTriggerAminationConfig> list = GetTriggerAniListByType(E_CT.HERO_STAR_MATCH);
            if (list == null)
                return;

            int ret = e.intData;
            UpdateHeroLevelByIDAndCnt(ret / 100, ret % 100);
        }

        /// <summary>
        /// 达成某个羁绊
        /// </summary>
        /// <param name="e"></param>
        private void DebugOnFetterGet(GEvent e)
        {
            int ret = e.intData;
            UpdateFetterByIDAndCnt(ret / 100, ret % 100);
        }

        /// <summary>
        /// 触发器进入逻辑
        /// </summary>
        public void TriggerEnter(string modelName, string triggerName, bool isHomeCourt, bool isMyMapTrigger)
        {
            if (m_triggerAnimList != null)
            {
                // 小小英雄重载配置
                BattleMapTriggerModelOverrideConfig overrideConfig = null;
                foreach (var config in m_triggerAnimList)
                {
                    if (config.m_collider != null && config.m_collider.name == triggerName)
                    {
                        // 根据白名单检测是否能触发
                        if (config.CheckWhiteList(modelName, out overrideConfig) && config.CheckHomeCourt(isHomeCourt, true, overrideConfig))
                        {
                            string enterAnimName = overrideConfig != null && !overrideConfig.overrideEnterAnimName.IsNullOrEmpty() ? overrideConfig.overrideEnterAnimName : config.m_enterAnimationName;
                            if (!enterAnimName.IsNullOrEmpty())
                            {
                                PlayTriggerEffect(config, enterAnimName, overrideConfig: overrideConfig, useBlend : true);
                            }
                        }
                    }
                }
            }

            //棋盘逻辑事件——触发区域
            if (isMyMapTrigger && m_eventTriggerColliders != null)
            {
                for (int i = 0; i < m_eventTriggerColliders.Length; i++)
                {
                    Collider col = m_eventTriggerColliders[i];
                    if (col != null && col.name == triggerName)
                    {
                        bool canExcute = false;
                        if (m_eventLogicList != null)
                        {
                            BattleMapTriggerModelOverrideConfig overrideConfig = null;
                            foreach (var eventLogic in m_eventLogicList)
                            {
                                if (eventLogic.triggerAnimCfgList != null && eventLogic.triggerLogicData.ColliderID == i)
                                {
                                    foreach (var config in eventLogic.triggerAnimCfgList)
                                    {
                                        if (config.CheckWhiteList(modelName, out overrideConfig))
                                        {
                                            canExcute = true;
                                            break;
                                        }
                                    }
                                }
                            }
                        }

                        if (canExcute)
                        {
                            //请求触发了逻辑触发区域
                            TriggerHaveStateEvent(HaveStateEventTypeEnum.TriggerLogicArea, i);
                        }

                        //同一name的，只触发一次
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 带状态 的逻辑事件类型
        /// </summary>
        private enum HaveStateEventTypeEnum
        {
            None,
            TriggerLogicArea,
            Fetter,
            HeroPromotion,
            WinCountAndTime,
        }

        /// <summary>
        /// 触发 带状态 的逻辑事件
        /// 带状态，是指会进行同步和重回，不是一次性触发的，比较复杂的
        /// </summary>
        private void TriggerHaveStateEvent(HaveStateEventTypeEnum type, int data)
        {
            var list = new List<byte>();
            list.Add((byte)type);
            list.AddRange(UtilTools.intToByteList(data, 4));

            bool isEditorPreview = BattleMapPreviewEditorController.IsEditorPreviewBattleMap;

#if !OUTSOURCE
            if (!isEditorPreview)
            {
                ChessOperationInput.WriteInput((byte)ChessOperationType.OPT_TRIGGER_MAP_LOGIC, list);
            }
#endif

            if (isEditorPreview)
            {
                //棋盘预览模式
                var battleModel = ChessModelManager.Instance.GetBattleModel();
                ChessPlayerController ctrl = ChessBattleGlobal.Instance.ChessPlayerCtrl;
                if (ctrl != null)
                {
                    ChessPlayerUnit unit = ctrl.GetPlayer(battleModel.MyPlayerId);
                    TryTriggerMapLogic(unit, false, AddMapLogicTriggerLogic((int)type, data));
                }
            }
        }

        private LinkedHashMap<int, List<int>> m_mapLogicTriggerDatas;
        private TKDictionary<int, List<int>> AddMapLogicTriggerLogic(int type, int data)
        {
            if (m_mapLogicTriggerDatas == null)
            {
                m_mapLogicTriggerDatas = new LinkedHashMap<int, List<int>>();
            }

            if (m_mapLogicTriggerDatas.ContainsKey(type))
            {
                if (type == (int)HaveStateEventTypeEnum.WinCountAndTime)
                {
                    //改变了才加入
                    var list = m_mapLogicTriggerDatas[type];
                    if (list[list.Count - 1] != data)
                    {
                        m_mapLogicTriggerDatas[type].Add(data);
                    }
                }
                else
                {
                    m_mapLogicTriggerDatas[type].Add(data);
                }
            }
            else
            {
                var dataList = new List<int> { data };
                m_mapLogicTriggerDatas.Add(type, dataList);
            }

            TKDictionary<int, List<int>> resultDic = new TKDictionary<int, List<int>>();
            foreach (var item in m_mapLogicTriggerDatas)
            {
                resultDic.Add(item.Key, new List<int>(item.Value));
            }

            return resultDic;
        }

        /// <summary>
        /// 触发器退出逻辑
        /// </summary>
        public void TriggerExit(string modelName, string triggerName, bool isHomeCourt, bool isMyMapTrigger)
        {
            if (m_triggerAnimList != null)
            {
                // 小小英雄重载配置
                BattleMapTriggerModelOverrideConfig overrideConfig = null;
                foreach (var config in m_triggerAnimList)
                {
                    if (config.m_collider != null && config.m_collider.name == triggerName)
                    {
                        // 根据白名单检测是否能触发
                        if (config.CheckWhiteList(modelName, out overrideConfig) && config.CheckHomeCourt(isHomeCourt, false, overrideConfig))
                        {
                            string exitAnimName = overrideConfig != null && !overrideConfig.overrideExitAnimName.IsNullOrEmpty() ? overrideConfig.overrideExitAnimName : config.m_exitAnimationName;
                            if (!exitAnimName.IsNullOrEmpty())
                            {
                                PlayTriggerEffect(config, exitAnimName, true, false, overrideConfig: overrideConfig, useBlend : true);
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 切换观战目标时候刷新
        /// </summary>
        public void SwitchPlayer()
        {
            //刷新当前阶段特效
            //RefreshCurrentRoundEffect();

            //切换观战触发场景效果
            CheckChangeObserveEffects();

            //恢复播放缓存效果
            PlayCacheAnimEffects();

            m_flyEffectSwitchedPlayer = true;
        }

        /// <summary>
        /// 客场小小英雄跳入棋盘触发
        /// </summary>
        public void JumpToBattleMap(string modelname ="")
        {
            if (m_eventAnimDic.TryGetValue(E_CT.JUMP_TO_MAP, out List<BattleMapTriggerAminationConfig> list2))
            {
                // 小小英雄重载配置
                BattleMapTriggerModelOverrideConfig overrideConfig = null;
                foreach (var config in list2)
                {
                    // 根据白名单检测是否能触发
                    if (config.CheckWhiteList(modelname, out overrideConfig))
                    {
                        var enterData = config.GetEnterAnimData();
                        if (enterData != null)
                        {
                            //客场小小英雄跳入棋盘触发动画
                            PlayTriggerEffect(config, enterData.animName);
                        }
                    }
                }
            }
        }

        private void RefreshCurrentRoundEffect()
        {
            if (m_eventAnimDic != null && m_eventAnimDic.Count > 0)
            {
                var model = ChessModelManager.Instance.GetBattleModel();
                if (model != null)
                {
                    int turnCount = model.CurrentTurnCount;
                    int tempRound = 0;
                    BattleMapTriggerAminationConfig tempConfig = null;
                    if (m_eventAnimDic.TryGetValue(E_CT.TURN_COUNT, out List<BattleMapTriggerAminationConfig> list))
                    {
                        foreach (var config in list)
                        {
                            var enterData = config.GetEnterAnimData();
                            if (enterData != null && turnCount >= enterData.checkValue)
                            {
                                if (config.m_animator != null)
                                {
                                    if (enterData.checkValue > tempRound)
                                    {
                                        tempRound = enterData.checkValue;
                                        tempConfig = config;
                                    }
                                }
                            }
                        }
                    }

                    if (tempConfig != null)
                    {
                        var animData = tempConfig.GetEnterAnimData();
                        if (animData != null)
                        {
                            PlayTriggerEffect(tempConfig, animData.animName);
                        }
                    }
                }
            }
        }

        private void CheckChangeObserveEffects()
        {
            if (m_eventAnimDic != null && m_eventAnimDic.Count > 0)
            {
                if (m_eventAnimDic.TryGetValue(E_CT.CHANGE_OBSERVE, out List<BattleMapTriggerAminationConfig> list))
                {
                    foreach (var config in list)
                    {
                        var enterData = config.GetEnterAnimData();
                        if (enterData != null)
                        {
                            PlayTriggerEffect(config, enterData.animName);
                        }
                    }
                }
            }
        }

        public bool PlayCacheAnimEffects()
        {
            bool hasPlay = false;
            bool playCam = false;
            for (int i = 0, n = m_cacheAnim.Count; i < n; i++)
            {
                var config = m_cacheAnim[i];
                var enterData = config.GetEnterAnimData();
                if (enterData != null)
                {
                    if (enterData.extraMap != null && enterData.extraMap.TryGetValue(
                        E_ET.HANG_POINT_TYPE, out ED value))
                    {
                        if (value.boolParam && value.floatParam > 0 && value.stringParam.Filled())
                        {
                            if (m_mapMgr != null)
                            {
                                var curMap = m_mapMgr.GetCurrentBattleMap();
                                if (curMap != null && curMap.MapId == mapId)
                                {
                                    //小小英雄切换挂点
                                    var effect = root.TryGetComponent<ChangeHangPointEffect>();
                                    effect.isLock = true;
                                    effect.playTime = value.floatParam;
                                    effect.hangPointName = value.stringParam;
                                }
                            }
                        }
                    }

                    if (enterData.type == E_CT.RANK_REACH && enterData.checkValue == 1 && !playCam)
                    {
                        //播放吃鸡镜头动画
                        if (m_mapMgr != null)
                        {
                            m_mapMgr.OnFirstRankFoucs(true, true);
                            playCam = true;
                        }
                    }

                    PlayTriggerEffect(config, enterData.animName, false);
                    hasPlay = true;
                }
            }

            m_cacheAnim.Clear();

            return hasPlay;
        }

        private void CheckWinCount(int winCount)
        {

            bool isMutex = false;
            if (winCount > 1)
            {
                if (m_eventAnimDic.TryGetValue(E_CT.TURN_WIN_COUNT, out List<BattleMapTriggerAminationConfig> conWinList))
                {
                    foreach (var config in conWinList)
                    {
                        var enterData = config.GetEnterAnimData();
                        if (enterData != null && winCount >= enterData.checkValue && CheckServerTime(enterData))
                        {
                            //达成指定连胜次数，播放动画
                            PlayTriggerEffect(config, enterData.animName);
                            if (config.IsMutex)
                            {
                                isMutex = true;
                            }
                        }
                    }
                }
            }

            if (!isMutex)
            {
                if (m_eventAnimDic.TryGetValue(E_CT.TURN_WIN, out List<BattleMapTriggerAminationConfig> list))
                {
                    foreach (var config in list)
                    {
                        //胜利播放动画
                        var enterData = config.GetEnterAnimData();
                        if (enterData != null && CheckServerTime(enterData))
                        {
                            PlayTriggerEffect(config, enterData.animName);
                        }
                    }
                }
            }
        }

        private void TryTriggerWinCountAndTime(int winCount)
        {
            if (m_eventLogicList == null || m_eventLogicList.Count == 0)
            {
                return;
            }

            //当前关注的棋盘，是玩家自己的棋盘，才触发
            //一种是回合结束直接触发，一种是重回自己的棋盘
            bool haveWin = false;
            foreach (var eventLogic in m_eventLogicList)
            {
                if (eventLogic.condition == ChessMapCondition.WinCountAndTime &&
                    eventLogic.winCountAndTimeData != null)
                {
                    if (IsServerTimeInRange(eventLogic.winCountAndTimeData.TimeStart,
                            eventLogic.winCountAndTimeData.TimeEnd)
                        && eventLogic.winCountAndTimeData.WinCount != 0 /* ==0 代表复位 */
                       )
                    {
                        if (winCount >= eventLogic.winCountAndTimeData.WinCount)
                        {
                            //达成连胜条件
                            TriggerHaveStateEvent(HaveStateEventTypeEnum.WinCountAndTime,
                                eventLogic.winCountAndTimeData.WinCount);
                            haveWin = true;
                        }
                    }
                }
            }

            if (!haveWin && winCount == 0)
            {
                //没有触发连胜，需要重置
                //winCount大于0，但是没有达到连胜条件，需要播单次胜利的动画，这个时候不能复位
                TriggerHaveStateEvent(HaveStateEventTypeEnum.WinCountAndTime, 0);
            }
        }

        private bool CheckServerTime(BattleMapTriggerAminationConfig.ConfigData enterData)
        {
            if (enterData.extraMap != null
                && enterData.extraMap.TryGetValue(E_ET.SERVER_TIME_START, out ED serverTimeStart)
                && enterData.extraMap.TryGetValue(E_ET.SERVER_TIME_END, out ED serverTimeEnd)
               )
            {
                if (IsServerTimeInRange(serverTimeStart.stringParam, serverTimeEnd.stringParam))
                {
                    return true;
                }
            }
            else
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 判断服务器时间，是否在一个时间范围内，例如 6:00:00 到 14:00:00，当天的时间
        /// </summary>
        private bool IsServerTimeInRange(string cfgStartTime, string cfgEndTime)
        {
            if (string.IsNullOrEmpty(cfgStartTime) || string.IsNullOrEmpty(cfgEndTime))
            {
                return true;
            }

            DateTime serverNow = GetCurrServerTime();
            
            if (DateTime.TryParseExact(cfgStartTime, "HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None,
                    out var startTime)
                && DateTime.TryParseExact(cfgEndTime, "HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None,
                    out var endTime)
                )
            {
                var serverStartTime = serverNow.Date.Add(startTime.TimeOfDay);
                var serverEndTime = serverNow.Date.Add(endTime.TimeOfDay);

                return serverNow.Subtract(serverStartTime).TotalSeconds >= 0 && serverEndTime.Subtract(serverNow).TotalSeconds >= 0;
            }

            return true;
        }

        private void CheckWinRank()
        {
            //热更写死，香水宝石宫
            if (mapId != 300063) return;

            var curMap = m_mapMgr.GetCurrentBattleMap();
            if (curMap == null || curMap.MapId != mapId)
            {
                return;
            }

            ChessBattleModel battleModel = ChessModelManager.Instance.GetBattleModel();
            if (battleModel == null) return;

            int aliveCount = battleModel.GetLivePlayerModelCount();

            //小于等于4人存活，那么所有人肯定是前四的，触发棋盘特殊的前四动效
            if (aliveCount <= 4)
            {
                //达成TOP4之后，触发不同之前的回合胜利机制
                Animator targetAnimator = null;
                if (root != null)
                {
                    Component[] animators = root.GetComponentsInChildren(typeof(Animator));
                    //退出对局是低频操作，可以采用这个方式
                    if (animators != null)
                    {
                        foreach (var animator in animators)
                        {
                            if (animator.gameObject.name.Equals("effects") &&
                                animator is Animator)
                            {
                                targetAnimator = animator as Animator;
                                break;
                            }
                        }
                    }
                }

                if (targetAnimator != null)
                {
                    EnsureAnimParentActive(targetAnimator);

                    PlayAnim(targetAnimator, 0.3f, "s8_tft_destino_effects_top4_will", true);

                    var go = targetAnimator.gameObject;
                    if (go != null)
                    {
                        var soundPlay = go.TryGetComponent<Chess_WwisePlaySound>();
                        soundPlay.bankName = "UI_Chessboard_PerfumeMaid";
                        soundPlay.soundName = "Play_UI_Chessboard_PerfumeMaid_FourWin";
                        soundPlay.soundType = Chess_WwisePlaySound.E_SoundType.EFFECT;
                        soundPlay.disableStopSound = true;
                        soundPlay.playCompeleteDisable = true;
                        soundPlay.enabled = false;
                        soundPlay.enabled = true;
                    }
                }
            }
        }

        private void CheckLoseCount(int loseCount)
        {

            bool isMutex = false;
            if (loseCount > 1)
            {
                if (m_eventAnimDic.TryGetValue(E_CT.TURN_LOSE_COUNT, out List<BattleMapTriggerAminationConfig> conLoseList))
                {
                    foreach (var config in conLoseList)
                    {
                        var enterData = config.GetEnterAnimData();
                        if (enterData != null && loseCount >= enterData.checkValue)
                        {
                            //达成指定连败次数，播放动画
                            PlayTriggerEffect(config, enterData.animName);
                            if (config.IsMutex)
                            {
                                isMutex = true;
                            }
                        }
                    }
                }
            }
            if( !isMutex)
            {
                if (m_eventAnimDic.TryGetValue(E_CT.TURN_LOSE, out List<BattleMapTriggerAminationConfig> list))
                {
                    foreach (var config in list)
                    {
                        //失败播放动画
                        var enterData = config.GetEnterAnimData();
                        if (enterData != null)
                        {
                            PlayTriggerEffect(config, enterData.animName);
                        }
                    }
                }
            }
        }

        #region 羁绊特效

        /// <summary>
        /// 羁绊更新
        /// </summary>
        public void UpdateFetter(int mapOwnerID, List<HeroFetterInfo> fetterList)
        {
            if (m_eventAnimDic == null || m_eventAnimDic.Count <= 0) return;
            if (!m_eventAnimDic.TryGetValue(E_CT.FETTER_REACH, out List<BattleMapTriggerAminationConfig> list)) return;
            
            float nowTime = Time.realtimeSinceStartup;
            foreach (var config in list)
            {
                var enterData = config.GetEnterAnimData();

                if (enterData == null || enterData.extraMap == null ||
                    !enterData.extraMap.TryGetValue(E_ET.FETTER_COUNT, out ED value) ||
                    !enterData.extraMap.TryGetValue(E_ET.CD_TIME, out ED cdTimeED)) continue;

                CDInfo cdInfo = GetCDInfo(mapOwnerID);
                float lastTriggerTime = GetLastTriggerTime(cdInfo, config);

                bool CDisOK = value.boolParam || GetCDFinish(cdTimeED.floatParam, lastTriggerTime, nowTime);
                if (!CDisOK) continue;

                foreach (var fetterInfo in fetterList)
                {
                    bool typeOK = enterData.checkValue == 0 ||
                                  enterData.checkValue == fetterInfo.typeID;
                    bool countOK = (int)value.floatParam == 0
                        ? fetterInfo.count == fetterInfo.maxLevelCount
                        : fetterInfo.count == (int)value.floatParam;

                    if (typeOK && countOK)
                    {
                        int fetterData = GetTriggerFetterData(fetterInfo.typeID, fetterInfo.count, fetterInfo.maxLevelCount);

                        if (value.boolParam && cdInfo.fetterDataSet != null && cdInfo.fetterDataSet.Contains(fetterData))
                        {
                            //不重复触发的，如果set里有，就跳过
                            continue;
                        }

                        //达成配置的羁绊和数量，同步出去
                        TriggerHaveStateEvent(HaveStateEventTypeEnum.Fetter, fetterData);

                        //Diagnostic.Error("Trigger !!!!!!!!! " + mapOwnerID + " UpdateFetter anim " + enterData.animName);
                        UpdateCDTime(cdInfo, config, nowTime);
                    }
                }
            }
        }

        private int GetTriggerFetterData(int fetterType, int fetterLevel, int fetterMaxLevel)
        {
            // 5位羁绊ID + 2位羁绊等级 + 2位最高等级
            // 12345 | 67 | 89
            return fetterType * 10000 + fetterLevel * 100 + fetterMaxLevel;
        }

        private void FindTriggerFetterData(int cfgData, out int fetterType, out int fetterLevel, out int fetterMaxLevel)
        {
            // 5位羁绊ID + 2位羁绊等级 + 2位最高等级
            // 12345 | 67 | 89
            fetterType = cfgData / 10000;
            fetterLevel = cfgData / 100 % 100;
            fetterMaxLevel = cfgData % 100;
        }

        /// <summary>
        /// 更新指定羁绊和数量
        /// </summary>
        /// <param name="ID"></param>
        /// <param name="count"></param>
        public void UpdateFetterByIDAndCnt(int ID, int count)
        {
            List<BattleMapTriggerAminationConfig> list = GetTriggerAniListByType(E_CT.FETTER_REACH);
            if (list == null)
            {
                return;
            }
            foreach (var config in list)
            {
                var enterData = config.GetEnterAnimData();
                if (enterData != null && enterData.extraMap != null &&
                    enterData.extraMap.TryGetValue(E_ET.FETTER_COUNT, out ED value))
                {
                    if (enterData.checkValue == ID && count == (int)value.floatParam)
                    {
                        //达成配置的羁绊和数量
                        PlayTriggerEffect(config, enterData.animName);
                    }
                }
            }
        }

        #endregion

        #region 英雄升星特效

        /// <summary>
        /// 羁绊更新
        /// </summary>
        public void UpdateHeroPromotion(int mapOwnerID, List<TAC_WaitHeroPromotionInfo> heroPromotionList)
        {
            if (m_eventAnimDic == null || m_eventAnimDic.Count <= 0) return;
            if (!m_eventAnimDic.TryGetValue(E_CT.HERO_STAR_MATCH, out List<BattleMapTriggerAminationConfig> list)) return;

            float nowTime = Time.realtimeSinceStartup;
            foreach (var config in list)
            {
                var enterData = config.GetEnterAnimData();

                if (enterData == null || enterData.extraMap == null ||
                    !enterData.extraMap.TryGetValue(E_ET.HERO_STAR_COUNT, out ED value) ||
                    !enterData.extraMap.TryGetValue(E_ET.CD_TIME, out ED cdTimeED)) continue;

                CDInfo cdInfo = GetCDInfo(mapOwnerID);
                float lastTriggerTime = GetLastTriggerTime(cdInfo, config);

                bool CDisOK = value.boolParam || GetCDFinish(cdTimeED.floatParam, lastTriggerTime, nowTime);
                if (!CDisOK) continue;

                foreach (var HeroPromotionInfo in heroPromotionList)
                {
                    int heroCfgID = HeroPromotionInfo.stNewHero.stHeroEntity.iHeroConfID;

                    CheckHeroPromotionTrigger(enterData, heroCfgID, value, out var typeOK, out var countOK);

                    if (typeOK && countOK)
                    {
                        if (value.boolParam && cdInfo.heroPromotionDataSet != null && cdInfo.heroPromotionDataSet.Contains(heroCfgID))
                        {
                            //不重复触发的，如果set里有，就跳过
                            continue;
                        }

                        //达成配置的羁绊和数量，同步出去
                        TriggerHaveStateEvent(HaveStateEventTypeEnum.HeroPromotion, heroCfgID);

                        //Diagnostic.Error("Trigger !!!!!!!!! " + mapOwnerID + " UpdateHeroPromotion anim " + enterData.animName);
                        UpdateCDTime(cdInfo, config, nowTime);
                    }
                }
            }
        }

        private void CheckHeroPromotionTrigger(BattleMapTriggerAminationConfig.ConfigData enterData, int heroCfgID, ED value, out bool typeOK,
            out bool countOK)
        {
            typeOK = enterData.checkValue == 0;
            if (!typeOK)
            {
                var conf = ChessModelManager.Instance.GetBattleModel().SoGameData_View;
                //配置里都填一星的，所以需要把过来的数据，处理成一星的，对比
                if (conf != null)
                {
                    var oneStarCfg = conf.GetACGHeroByStar(heroCfgID, 1);
                    typeOK = enterData.checkValue == oneStarCfg.iID;
                }
            }

            countOK = (int) value.floatParam == 0;
            if (!countOK)
            {
                //不是任意星级触发，那么需要获取英雄星级，根据配置ID
                var cfg = DataBaseManager.Instance.SearchACGHero(heroCfgID);
                if (cfg != null)
                {
                    countOK = cfg.iStar == (int) value.floatParam;
                }
            }
        }

        /// <summary>
        /// 更新指定英雄ID和星级
        /// </summary>
        /// <param name="ID"></param>
        /// <param name="count"></param>
        public void UpdateHeroLevelByIDAndCnt(int ID, int count)
        {
            List<BattleMapTriggerAminationConfig> list = GetTriggerAniListByType(E_CT.HERO_STAR_MATCH);
            if (list == null)
                return;
            foreach (var config in list)
            {
                var enterData = config.GetEnterAnimData();
                if (enterData != null && enterData.extraMap != null &&
                    enterData.extraMap.TryGetValue(E_ET.HERO_STAR_COUNT, out ED value))
                {
                    if (enterData.checkValue == ID && count == (int)value.floatParam)
                    {
                        //达成配置的羁绊和数量
                        PlayTriggerEffect(config, enterData.animName);
                        break;
                    }
                }
            }
        }

        #endregion

        /// <summary>
        /// 操作指令回包
        /// </summary>
        /// <param name="msg"></param>
        private void HandleBattleEvent(ObservableMessage msg)
        {
            switch (msg.ID)
            {
                case (int)TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_USER_OUT:
                {
                    TAC_UserOutReq stUserOut = msg.CastValue<TAC_UserOutReq>();
                    if (stUserOut != null)
                    {
                        //玩家死亡出局
                        var model = ChessModelManager.Instance.GetBattleModel();
                        int selfPlayerId = model.MyPlayerId;
                        int aliveCount = 0;
                        if (model.IsTeamLeaderEditor())
                        {
                            //小队长编辑场景
                            aliveCount = model.TestAliveCount;
                            m_rankReachHash.Clear();
                        }
                        else
                        {
                            aliveCount = model.GetLivePlayerModelCount();
                        }

                        //双人对局本玩家胜利：1对1单挑赢了，或者2个同队的赢了
                        bool doublePlayWin = false;
                        if (aliveCount <= 2) 
                        {
                            //doublePlayWin = model.IsDualPlayWin();
                        }

                        if (selfPlayerId != stUserOut.iCharId)
                        {
                            //存活的玩家数
                            CheckRankReach(aliveCount, doublePlayWin);

                            //检查玩家出局触发效果
                            CheckUserOut(stUserOut.iCharId, selfPlayerId);
                        }
                        else
                        {
                            //玩家自己出局
                            //对于双人对局，如果aliveCount > 1，自己被淘汰，那么一定不会是自己队获胜结束了
                            //因为自己队获胜，自己又死了，队友一定活着，但aliveCount > 1说明有别的队活着，说明比赛没有结束
                            //所以这里aliveCount > 1就不特殊处理
                            if (aliveCount > 1)
                            {
                                CheckPlayerOut(aliveCount);
                            }
                            else
                            {
                                if (stUserOut.GetRank(selfPlayerId) == 1)
                                {
                                    //自己吃鸡
                                    CheckRankReach(1);
                                }
                                //这里aliveCount == 1，自己出局，场上剩最后一个人
                                //双人对局结束，如果最后一个人是队友，说明赢了，播放成功动画
                                //如果最后一个人不是队友，说明输了，失败处理
                                //else if (model.IsDualPlayMode())
                                //{
                                //    if (doublePlayWin)
                                //    {
                                //        CheckRankReach(1, true);
                                //    }
                                //    else
                                //    {
                                //        CheckPlayerOut(aliveCount);
                                //    }
                                //}
                            }
                        }
                    }

                    break;
                    }
            }
        }

        /// <summary>
        /// 目前关注的是当前地图
        /// 注意，每张地图仅有一个地图实体和数据，所以关注的地图可以是使用这个地图的任何一名玩家的
        /// </summary>
        private bool IsThisMapFocus()
        {
            if (m_mapMgr == null)
            {
                //还没初始化
                return false;
            }

            var curMap = m_mapMgr.GetCurrentBattleMap();
            if (curMap == null || curMap.MapId != mapId)
            {
                //跟当前观战场景不一致
                return false;
            }

            return true;
        }

        private bool NeedCheckPlayerLevel()
        {
            if (m_eventAnimDic == null || m_eventAnimDic.Count <= 0) return false;
            if (!m_eventAnimDic.ContainsKey(E_CT.PLAYER_LEVEL)) return false;
            return true;
        }

        private void OnUpdateCurrentPlayerLevel(GEvent e)
        {
            if (!IsThisMapFocus())
            {
                //当前没有关注的地图，不接受这个事件
                return;
            }
            //当前关注的玩家，触发了升级
            //这里只需要接收关注的玩家的升级，而不是全部玩家的，因为只需要在关注的棋盘上，触发玩家升级动效，未关注的不用触发
            var battleModel = ChessModelManager.Instance.GetBattleModel();
            if (battleModel != null)
            {
                var playerModel = battleModel.GetPlayerModel(battleModel.CurrentPlayerId);
                if (playerModel != null)
                {
                    //var turnModel = playerModel.GetBattleTurnModel();
                    //if (turnModel != null && turnModel.IsLevelChange)
                    //{
                    //    //直接触发的，不是重回，所以是false
                    //    OnUpdateCurrentPlayerLevel(false);
                    //}
                }
            }
        }

        public void UpdatePlayerLevel(int level, bool isRegain)
        {
            if (!m_eventAnimDic.TryGetValue(E_CT.PLAYER_LEVEL, out List<BattleMapTriggerAminationConfig> list)) return;

            foreach (var config in list)
            {
                var enterData = config.GetEnterAnimData();

                if (enterData == null || enterData.extraMap == null ||
                    !enterData.extraMap.TryGetValue(E_ET.COMPARE_TYPE, out ED value)) continue;

                bool needTrigger = false;
                switch ((BattleMapTriggerAminationConfig.E_CompareType)value.intParam)
                {
                    case BattleMapTriggerAminationConfig.E_CompareType.Equal:
                        needTrigger = level == enterData.checkValue;
                        break;
                    case BattleMapTriggerAminationConfig.E_CompareType.Greater:
                        needTrigger = level > enterData.checkValue;
                        break;
                    case BattleMapTriggerAminationConfig.E_CompareType.Less:
                        needTrigger = level < enterData.checkValue;
                        break;
                }

                if (needTrigger)
                {
                    //纯数据类的，一般不需要判断小小英雄是什么，overrideConfig
                    PlayTriggerEffect(config, enterData.animName, directToEnd: isRegain /*重回数据，直接播到最后*/);
                }
            }
        }

        private void OnApplicationPauseFocusBack(GEvent e)
        {
            //todo：高风险，先hold住
            //if (e != null && e.boolData)
            //{
            //    //home出去回来
            //    //如果正在播入场动画，那么直接播到终点
            //    m_mapMgr.StopSceneCameraAnimation(true);
            //}
        }

        /// <summary>
        /// 触发地图重回，用于切换地图时，触发棋盘逻辑
        /// </summary>
        public void TriggerMapRegain(ChessPlayerUnit unit)
        {
            var battleModel = ChessModelManager.Instance.GetBattleModel();
            if (battleModel != null && unit != null && unit.PlayerData != null &&
                unit.PlayerData.ChessPlayerId == battleModel.CurrentPlayerId)
            {
                //仅对当前关注玩家的逻辑，可以加在这里

                //这里是纯表现的，直接用当前数据，而不需要记录重回数据的事件
                //当前玩家等级
                OnUpdateCurrentPlayerLevel(true);
                //特殊处理，当前排名
                UpdateRegainRankReachEffect();
                //战斗阶段动效
                CheckBattleStageRegain();
                //达成排名重回动效
                CheckRankReachRegain();

                if (m_eventAnimDic != null && m_eventAnimDic.Count > 0)
                {
                    if (m_eventAnimDic.TryGetValue(E_CT.TINY_HERO_VISIT, out List<BattleMapTriggerAminationConfig> list))
                    {
                        foreach (var config in list)
                        {
                            var enterData = config.GetEnterAnimData();
                            if (enterData != null)
                            {
                                PlayTriggerEffect(config, enterData.animName, true, true, null, false, true);
                            }
                        }
                    }
                }
            }
        }

        private void OnUpdateCurrentPlayerLevel(bool isRegain)
        {
            if (NeedCheckPlayerLevel())
            {
                //需要对玩家升级做处理
                var battleModel = ChessModelManager.Instance.GetBattleModel();
                if (battleModel != null)
                {
                    //当前关注的棋盘，获取玩家等级
                    var playerModel = battleModel.GetPlayerModel(battleModel.CurrentPlayerId);
                    if (playerModel != null)
                    {
                        //var turnModel = playerModel.GetBattleTurnModel();
                        //if (turnModel != null)
                        //{
                        //    //重回，把已经点亮的特效展示出来
                        //    UpdatePlayerLevel(turnModel.MessangerLevel.iLevel, isRegain);
                        //}
                    }
                }
            }
        }

        private void CheckBattleStageRegain()
        {
            //重回的时候，切换到战斗阶段动效的终点
            var model = ChessModelManager.Instance.GetBattleModel();
            if (m_eventAnimDic.TryGetValue(E_CT.BATTLE_STAGE, out List<BattleMapTriggerAminationConfig> list3))
            {
                TTAC_Quest_Client quest = model.SoGameData_View.GetQuestData(model.CurrentTurnCount);
                if (quest != null)
                {
                    int curStage = quest.iStage;
                    foreach (var config in list3)
                    {
                        if (config != null)
                        {
                            var enterData = config.GetEnterAnimData();
                            if (enterData != null && curStage >= enterData.checkValue)
                            {
                                //当前战斗阶段大于的动效，都要触发
                                PlayTriggerEffect(config, enterData.animName, directToEnd: true);
                                TryChangeInterest(config);
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 尝试切换利球
        /// </summary>
        /// <param name="config"></param>
        private void TryChangeInterest(BattleMapTriggerAminationConfig config)
        {
            var enterData = config.GetEnterAnimData();
            if (enterData == null || enterData.type != E_CT.BATTLE_STAGE)
            {
                //目前只支持战斗阶段切换
                return;
            }

            //目前的设计，替换利息球事不可逆的，所以可以直接替换原数据
            if (config.SwitchInterestTemplate != null) m_mapConfig.InterestTemplate = config.SwitchInterestTemplate;
            if (config.SwitchInterestTemplate2 != null) m_mapConfig.InterestTemplate2 = config.SwitchInterestTemplate2;

            //替换完，刷新一下
            ACGEventManager.Instance.Send(EventType_BattleView.AutoChess_Battle_Refresh_Interest_Art);
        }

        private void RevertInterest(BattleMapTriggerAminationConfig config)
        {
            //目前的设计，替换利息球事不可逆的，所以可以直接替换原数据
            if (config.SwitchInterestTemplate != null && m_sourceInterest != null) m_mapConfig.InterestTemplate = m_sourceInterest;
            if (config.SwitchInterestTemplate2 != null && m_sourceInterest2 != null) m_mapConfig.InterestTemplate2 = m_sourceInterest2;

            //替换完，刷新一下
            ACGEventManager.Instance.Send(EventType_BattleView.AutoChess_Battle_Refresh_Interest_Art);
        }

        private void CheckRankReachRegain()
        {
            List<BattleMapTriggerAminationConfig> list = GetTriggerAniListByType(E_CT.RANK_REACH);
            if (list == null || list.Count == 0)
                return;

            ChessBattleModel model = ChessModelManager.Instance.GetBattleModel();
            if (model == null) return;

            int aliveCount = model.GetLivePlayerModelCount();

            //双人对局本玩家胜利：1对1单挑赢了，或者2个同队的赢了
            bool doublePlayWin = false;
            if (aliveCount <= 2)
            {
                //doublePlayWin = model.IsDualPlayWin();
            }
            if (aliveCount == 1 || doublePlayWin)
            {
                var myPlayer = ChessModelManager.Instance.GetBattleModel().GetMyPlayerModel();
                if (myPlayer != null)
                {
                    int mayMapId = myPlayer.Playerinfo.stTAC_GameTinyData.iMapId;
                    if (mayMapId != mapId || !myPlayer.IsHomeCourt())
                    {
                        //当前棋盘不是玩家所在棋盘
                        //playVisitFieldRankReach();
                        return;
                    }
                }
            }
            
            foreach (var config in list)
            {
                var enterData = config.GetEnterAnimData();
                if (enterData == null || config.m_animator == null)
                    continue;
                
                //达到指定排名以前，播放动画
                int configRank = enterData.checkValue;

                //KDA棋盘，热更特殊处理，未来换实现
                if (mapId == 300081)
                {
                    //KDA棋盘，3阶段之前禁止播排行大于1的动效
                    if (configRank > 1 && model.CurrentPlayerId != -1 && model.SoGameData_View != null)
                    {
                        TTAC_Quest_Client quest = model.SoGameData_View.GetQuestData(model.CurrentTurnCount);
                        if (quest != null)
                        {
                            if (quest.iStage < 3)
                            {
                                continue;
                            }
                        }
                    }
                }

                bool reachRank = EnsureReachRank(configRank >= aliveCount, configRank, aliveCount, doublePlayWin);

                if (reachRank)
                {
                    if (enterData.extraMap != null && enterData.extraMap.TryGetValue(
                            E_ET.STAY_STATE, out ED value))
                    {
                        if (value.boolParam)
                        {
                            //保持达成后状态
                            PlayTriggerEffect(config, enterData.animName, directToEnd: true);
                        }
                    }
                }
            }
        }

        private void UpdateRegainRankReachEffect()
        {
            //重回时，判断是否已经前四了，如果是，那么直接跳到前四后的动效
            //热更写死，香水宝石宫
            if (mapId != 300063) return;

            var model = ChessModelManager.Instance.GetBattleModel();

            if (model == null) return;

            int aliveCount = model.GetLivePlayerModelCount();

            //双人对局本玩家胜利：1对1单挑赢了，或者2个同队的赢了
            bool doublePlayWin = false;
            if (aliveCount <= 2)
            {
                //doublePlayWin = model.IsDualPlayWin();
            }
            if (aliveCount == 1 || doublePlayWin)
            {
                var myPlayer = ChessModelManager.Instance.GetBattleModel().GetMyPlayerModel();
                if (myPlayer != null)
                {
                    int mayMapId = myPlayer.Playerinfo.stTAC_GameTinyData.iMapId;
                    if (mayMapId != mapId || !myPlayer.IsHomeCourt())
                    {
                        //当前棋盘不是玩家所在棋盘
                        //playVisitFieldRankReach();
                        return;
                    }
                }
            }

            SpecialRankReachTemp(aliveCount, doublePlayWin, true);
        }

        private void CheckRankReach(int aliveCount, bool doublePlayWin = false)
        {
            if (aliveCount == 1 || doublePlayWin)
            {
                var myPlayer = ChessModelManager.Instance.GetBattleModel().GetMyPlayerModel();
                if (myPlayer != null)
                {
                    int mayMapId = myPlayer.Playerinfo.stTAC_GameTinyData.iMapId;
                    if (mayMapId != mapId || !myPlayer.IsHomeCourt())
                    {
                        //当前棋盘不是玩家所在棋盘
                        //playVisitFieldRankReach();
                        return;
                    }
                }
            }

            //香水宝石宫，临时特殊机制
            SpecialRankReachTemp(aliveCount, doublePlayWin, false);

            List<BattleMapTriggerAminationConfig> list = GetTriggerAniListByType(E_CT.RANK_REACH);
            if (list == null || list.Count == 0)
                return;

            var cache = new HashSet<int>();
            foreach (var config in list)
            {
                var enterData = config.GetEnterAnimData();
                if (enterData == null || config.m_animator == null)
                    continue;

                //达到指定排名以前，播放动画
                int configRank = enterData.checkValue;
                bool reachRank = configRank >= aliveCount;
                if (doublePlayWin && configRank == 1)
                {
                    //双人对局获胜，那么播放动画
                    reachRank = true;
                }

                //KDA棋盘，热更特殊处理，未来换实现
                if (mapId == 300081)
                {
                    ChessBattleModel model = ChessModelManager.Instance.GetBattleModel();

                    //KDA棋盘，3阶段之前禁止播排行大于1的动效
                    if (configRank > 1 && model != null && model.CurrentPlayerId != -1 && model.SoGameData_View != null)
                    {
                        TTAC_Quest_Client quest = model.SoGameData_View.GetQuestData(model.CurrentTurnCount);
                        if (quest != null)
                        {
                            if (quest.iStage < 3)
                            {
                                continue;
                            }
                        }
                    }
                }

                reachRank = EnsureReachRank(reachRank, configRank, aliveCount, doublePlayWin);
                if (!m_rankReachHash.Contains(configRank) && reachRank)
                {
                    if (!cache.Contains(configRank))
                    {
                        cache.Add(configRank);
                    }

                    if (configRank == 1)
                    {
                        //吃鸡效果先缓存起来，等待后面再播放
                        m_cacheAnim.Add(config);
                    }
                    else
                    {
                        if (enterData.extraMap != null && enterData.extraMap.TryGetValue(
                        E_ET.HANG_POINT_TYPE, out ED value))
                        {
                            if (value.boolParam && value.floatParam > 0 && value.stringParam.Filled())
                            {
                                if (m_mapMgr != null)
                                {
                                    var curMap = m_mapMgr.GetCurrentBattleMap();
                                    if (curMap != null && curMap.MapId == mapId)
                                    {
                                        //小小英雄切换挂点
                                        var effect = root.TryGetComponent<ChangeHangPointEffect>();
                                        effect.isLock = true;
                                        effect.playTime = value.floatParam;
                                        effect.hangPointName = value.stringParam;
                                    }
                                }
                            }
                        }

                        PlayTriggerEffect(config, enterData.animName);
                    }
                }
            }

            //播放完动画才添加到缓存队列
            foreach (int rank in cache)
            {
                m_rankReachHash.Add(rank);
            }
        }

        public void SpecialRankReachTemp(int aliveCount, bool doublePlayWin, bool isRegain)
        {
            //热更写死，香水宝石宫
            if (mapId != 300063) return;

            ChessBattleModel battleModel = ChessModelManager.Instance.GetBattleModel();
            if (battleModel == null) return;

            bool reachTop4 = EnsureReachRank(4 >= aliveCount, 4, aliveCount, doublePlayWin);

            if (!isRegain && !reachTop4) return;
            //重回一定触发，用于重置动画状态打前四或不是前四的情况
            //如果不是重回，那么只有到达前四触发，没有到前四，不触发

            PlayerModel playerModel = battleModel.GetCurPlayerModel(); //当前关注的player
            ChessPlayerController ctrl = ChessBattleGlobal.Instance.ChessPlayerCtrl;
            if (ctrl != null)
            {
                ChessPlayerUnit unit = ctrl.GetPlayer(playerModel.PlayerId);
                if (unit != null)
                {
                    //方法一开始的逻辑，保证了是在玩家自己的棋盘，因为前X动效仅在自己棋盘可以看到
                    //这里再判断自己的小小英雄，是不是特定的
                    string unitModelName = "";
                    if (unit.PlayerData != null && unit.PlayerData.TinyData != null)
                    {
                        int tinyID = unit.PlayerData.TinyData.iTinyId;
                        var heroCfg = DataBaseManager.Instance.SearchACGItem(tinyID);
                        if (heroCfg != null)
                        {
                            unitModelName = heroCfg.sPreviewResource;
                        }
                    }

                    Animator targetAnimator = null;
                    if (root != null)
                    {
                        Component[] animators = root.GetComponentsInChildren(typeof(Animator));
                        //退出对局是低频操作，可以采用这个方式
                        if (animators != null)
                        {
                            foreach (var animator in animators)
                            {
                                if (animator.gameObject.name.Equals("s8_tft_destino_water") &&
                                    animator is Animator)
                                {
                                    targetAnimator = animator as Animator;
                                    break;
                                }
                            }
                        }
                    }

                    if (targetAnimator != null)
                    {
                        if (unitModelName.StartsWith("t_oriannamaid_1_show"))
                        {
                            //奥利安娜
                            SpecialPlayAnimTemp(targetAnimator,
                                reachTop4 ? "s8_tft_destino_water_normal_oriannamaid_top4"
                                          : "s8_tft_destino_water_normal_oriannamaid", isRegain);
                        }
                        else if (unitModelName.StartsWith("t_jannamaid_1_show") ||
                                 unitModelName.StartsWith("t_jannamaidgold_1_show"))
                        {
                            //风女
                            SpecialPlayAnimTemp(targetAnimator,
                                reachTop4 ? "s8_tft_destino_water_normal_jannamaid_top4"
                                          : "s8_tft_destino_water_normal_jannamaid", isRegain);
                        }
                        else
                        {
                            //其他英雄
                            SpecialPlayAnimTemp(targetAnimator,
                                reachTop4 ? "s8_tft_destino_water_normal_top4"
                                    : "s8_tft_destino_water_normal", isRegain);
                        }
                    }
                }
            }
        }

        private void SpecialPlayAnimTemp(Animator targetAnimator, string animName, bool directToEnd)
        {
            EnsureAnimParentActive(targetAnimator);

            if (directToEnd)
            {
                targetAnimator.Play(animName, 0, 1f);
            }
            else
            {
                PlayAnim(targetAnimator, 0.3f, animName, true);
            }
        }

        private bool EnsureReachRank(bool reachRank,int configRank, int aliveCount, bool doublePlayWin)
        {
            if (!doublePlayWin && reachRank)
            {
                var myPlayer = ChessModelManager.Instance.GetBattleModel().GetMyPlayerModel();
                if (myPlayer != null)
                {
                    //if (myPlayer.bUserOut)
                    //{
                    //    //别人出局剩3个，但自己已经出局了，这个时候不应该认为自己是前四
                    //    return configRank >= myPlayer.MatchRank;
                    //}
                }
            }
            return reachRank;
        }

        #region 客场播放 我的吃鸡 动画
        /// <summary>
        /// 播放客场吃鸡动画
        /// 逻辑：
        ///     1   去自己棋盘找到客鸡动画
        ///     2   无动画，播默认的则不用管了。
        ///     3   有动画，则将棋盘的客鸡动画节点信息先保存；
        ///     4   将客鸡动画节点，剪切到客场棋盘来；
        ///     5   播放客鸡动画。
        /// </summary>
        private bool playVisitFieldRankReach()
        {
            PlayerModel myPlayer = ChessModelManager.Instance.GetBattleModel().GetMyPlayerModel();
            if (myPlayer == null)
                return false;

            int myMapID = myPlayer.Playerinfo.stTAC_GameTinyData.iMapId;
            if (myMapID == mapId)
                return false;

            BattleMap myMap = BattleMapManager.Instance.GetMapDataById(myMapID);
            if (myMap == null)
                return false;

            BattleMapTriggerManager bMTM = myMap.GetBattleMapTrggierManager();

            List<BattleMapTriggerAminationConfig> anis = bMTM.GetTriggerAniListByType(E_CT.VISITFIELD_RANK_FIRST);
            if (anis == null || anis.Count == 0)
            {
                return false;
            }

            for (int idx = 0; idx < anis.Count; idx++)
            {
                GameObject go = anis[idx].m_animator.gameObject;
                if(anis[idx].AniVisitFirstGO != null)
                {
                    go = anis[idx].AniVisitFirstGO;
                }
                Vector3 orgPos = go.transform.localPosition;
                go.transform.parent = BattleMapManager.Instance.GetCurrentBattleMap().Config.transform;
                go.transform.parent.localPosition = orgPos;

                BattleMapTriggerAminationConfig.ConfigData enterData = anis[idx].GetEnterAnimData();
                
                if (enterData != null)
                {
                    PlayTriggerEffect(anis[idx], enterData.animName);
                }
            }
            return true;
        }

        #endregion
        private void CheckPlayerOut(int aliveCount)
        {
            var myPlayer = ChessModelManager.Instance.GetBattleModel().GetMyPlayerModel();
            if (myPlayer != null)
            {
                int mayMapId = myPlayer.Playerinfo.stTAC_GameTinyData.iMapId;
                if (mayMapId != mapId)
                {
                    //当前棋盘不是玩家所在棋盘
                    return;
                }
            }

            if (m_eventAnimDic != null && m_eventAnimDic.Count > 0)
            {
                if (m_eventAnimDic.TryGetValue(E_CT.PLAYER_OUT, out List<BattleMapTriggerAminationConfig> list))
                {
                    foreach (var config in list)
                    {
                        var enterData = config.GetEnterAnimData();
                        if (enterData != null && config.m_animator != null)
                        {
                            //玩家在多少名外
                            if (enterData.checkValue > aliveCount)
                            {
                                PlayTriggerEffect(config, enterData.animName, false);
                                break;
                            }
                        }
                    }
                }
            }
        }

        private void CheckUserOut(int outPlayerId, int selfPlayerId)
        {
            if (m_eventAnimDic.TryGetValue(E_CT.USER_OUT, out List<BattleMapTriggerAminationConfig> list))
            {
                var enemyPlayerID = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(outPlayerId).EnemyPlayerID;
                if (enemyPlayerID == selfPlayerId)
                {
                    //玩家击败对手出局
                    foreach (var config in list)
                    {
                        //播放对手出局动画
                        var enterData = config.GetEnterAnimData();
                        if (enterData != null)
                        {
                            PlayTriggerEffect(config, enterData.animName);
                        }
                    }
                }
            }
        }

        private void AddCacheAnimationConfig(BattleMapTriggerAminationConfig config)
        {
            // 回合动画缓存，仅播放最后一个回合的
            if(config.GetEnterAnimData().type == E_CT.TURN_COUNT)
            {
                int before = m_cacheAnim.Count;
                int turnCount = config.GetEnterAnimData().checkValue;
                m_cacheAnim.RemoveAll(x => (x.GetEnterAnimData().type == E_CT.TURN_COUNT && x.GetEnterAnimData().checkValue < turnCount));
                int after = m_cacheAnim.Count;
                if(before > after)
                {
                    TKFrame.Diagnostic.Warn(string.Format("AddCacheAnimationConfig,Last Turn:{0},Remove TurnCount Cache Animations count,before:{1},after:{2}",turnCount, before, after));
                }
            }

            m_cacheAnim.Add(config);
        }

        public void PlayTriggerEffect(BattleMapTriggerAminationConfig config, string animName,
            bool checkMap = true, 
            bool inPlaySound = true, 
            BattleMapTriggerModelOverrideConfig overrideConfig = null, 
            bool useBlend = false,
            bool directToEnd = false
            )
        {
            bool needCache = config.m_needCache;
            float blendTime = config.m_blendTime;
            bool playSound = config.m_playSound;
            string bankName = config.m_bankName;
            string soundName = config.m_soundName;
            List<SoundInfo> soundInfos = config.m_soundInfos;
            GameObject soundObj = config.m_soundObj;
            bool setSwitch = config.m_setSwitch;
            bool isSetState = config.m_isSetState;
            bool isSetRTPC = config.m_isSetRTPC;
            GameObject switchObj = config.m_switchObj;
            string switchGroup = config.m_switchGroup;
            string switchKey = config.m_switchKey;

            if (overrideConfig != null)
            {
                // 使用重载数据
                needCache = overrideConfig.overrideNeedCache;
                blendTime = overrideConfig.overrideBlendTime;
                playSound = overrideConfig.overridePlaySound;
                bankName = overrideConfig.overrideBankName;
                soundName = overrideConfig.overrideSoundName;
                soundObj = overrideConfig.overrideSoundObj;
                soundInfos = overrideConfig.overrideSoundInfos;

                setSwitch = overrideConfig.overrideSetSwitch;
                isSetState = overrideConfig.overrideIsSetState;
                isSetRTPC = overrideConfig.overrideIsSetRTPC;
                switchObj = overrideConfig.overrideSwitchObj;
                switchGroup = overrideConfig.overrideSwitchGroup;
                switchKey = overrideConfig.overrideSwitchKey;
            }

            if (!MicroMgr.Instance.InBattle)
            {
                //不在对局比赛场景内
                checkMap = false;
            }

            if (checkMap)
            {
                if (m_mapMgr == null)
                {
                    //还没初始化
                    return;
                }

                var curMap = m_mapMgr.GetCurrentBattleMap();
                if (curMap == null)
                {
                    return;
                }

                if (curMap.MapId != mapId)
                {
                    //跟当前观战场景不一致
                    if (needCache)
                    {
                        AddCacheAnimationConfig(config);
                    }
                    
                    return;
                }
            }

            var anim = config.m_animator;
            if (anim != null)
            {
                //anim.enabled = false;
                anim.enabled = true;

                if (config.IsFlyEffect)
                {
                    //飞行特效
                    if (directToEnd)
                    {
                        StopFlyEffect(config, true);
                    }
                    else
                    {
                        StartFlyEffect(config, animName, useBlend);
                    }
                }
                else
                {
                    EnsureAnimParentActive(anim);

                    if (directToEnd)
                    {
                        //跳转到animName的Translation的终点，触发
                        //因为有些配置的动作，是有多个AnimatorState顺序走完的
                        //这里使用预生成的数据，代表此anim的动画终点，也就是自然crossFade的（没有conditions锁住的），最后一个节点的animName
                        if (!string.IsNullOrEmpty(config.m_finalAnimName))
                        {
                            anim.Play(config.m_finalAnimName, 0, 1f);
                        }
                        else
                        {
                            anim.Play(animName, 0, 1f);
                        }
                    }
                    else
                    {
                        PlayAnim(anim, blendTime, animName, useBlend);
                    }
                }

                if (config.m_mapModifyType == E_MODT.Modifiy)
                {
                    ACGEventManager.Instance.Send(EventType_BattleView.AutoChess_Battle_BeforeAnimationModifyScene, mapId);
                }
                else if (config.m_mapModifyType == E_MODT.Revert)
                {
                    var trigger = anim.gameObject.TryGetComponent<BattleMapAnimationEventTrigger>();
                    trigger.AnimationClipName = animName;
                    trigger.Callback = SendRevert;
                }
            }

            if (inPlaySound && playSound && !directToEnd)
            {
                //directToEnd，直接到最后的状态，不播声音
                var curMap = m_mapMgr.GetCurrentBattleMap();
                if (curMap != null && curMap.MapId == mapId)
                {
                    if (bankName.Filled() && soundName.Filled())
                    {
                        GameObject go = null;
                        if (soundObj != null)
                        {
                            go = soundObj;
                        }
                        else if (anim != null)
                        {
                            go = anim.gameObject;
                        }
                        else if (config.m_collider != null)
                        {
                            go = config.m_collider.gameObject;
                        }

                        if (go != null)
                        {
                            var soundPlay = go.TryGetComponent<Chess_WwisePlaySound>();
                            string overrideSoundInfo = GetOverrideSoundInfo(bankName, soundName);
                            if (string.IsNullOrEmpty(overrideSoundInfo))
                            {
                                soundPlay.bankName = bankName;
                                soundPlay.soundName = soundName;
                            }
                            else
                            {
                                var splitInfo = overrideSoundInfo.BeginSplit('|');
                                if (splitInfo != null && splitInfo.Length == 2)
                                {
                                    soundPlay.bankName = splitInfo[0];
                                    soundPlay.soundName = splitInfo[1];
                                }
                                splitInfo.EndSplit();
                            }
                            soundPlay.soundType = Chess_WwisePlaySound.E_SoundType.EFFECT;
                            soundPlay.disableStopSound = true;
                            soundPlay.playCompeleteDisable = true;
                            soundPlay.enabled = false;
                            soundPlay.enabled = true;
                            //ChessUtil.PlayWwiseBankByPath(config.m_bankName, config.m_soundName, anim.gameObject);
                        }
                    }

                    for (int i = 0; i < soundInfos.Count; ++i)
                    {
                        if (soundInfos[i].soundBank.Filled() && soundInfos[i].soundEvent.Filled())
                        {
                            GameObject go = null;
                            if (soundInfos[i].soundObj != null)
                            {
                                go = soundInfos[i].soundObj;
                            }
                            else if (anim != null)
                            {
                                go = anim.gameObject;
                            }
                            else if (config.m_collider != null)
                            {
                                go = config.m_collider.gameObject;
                            }

                            if (go != null)
                            {
                                var soundPlay = go.TryGetComponent<Chess_WwisePlaySound>();
                                string overrideSoundInfo = GetOverrideSoundInfo(soundInfos[i].soundBank, soundInfos[i].soundEvent);
                                if (string.IsNullOrEmpty(overrideSoundInfo))
                                {
                                    soundPlay.bankName = soundInfos[i].soundBank;
                                    soundPlay.soundName = soundInfos[i].soundEvent;
                                }
                                else
                                {
                                    var splitInfo = overrideSoundInfo.BeginSplit('|');
                                    if (splitInfo != null && splitInfo.Length == 2)
                                    {
                                        soundPlay.bankName = splitInfo[0];
                                        soundPlay.soundName = splitInfo[1];
                                    }
                                    splitInfo.EndSplit();
                                }
                                soundPlay.soundType = Chess_WwisePlaySound.E_SoundType.EFFECT;
                                soundPlay.disableStopSound = true;
                                soundPlay.playCompeleteDisable = true;
                                soundPlay.enabled = false;
                                soundPlay.enabled = true;
                                //ChessUtil.PlayWwiseBankByPath(config.m_bankName, config.m_soundName, anim.gameObject);
                            }
                        }
                    }
                }
            }

            if (setSwitch)
            {
                if (switchGroup.Filled() && switchGroup.Filled())
                {
#if !OUTSOURCE
                    if (isSetState)
                        AkSoundEngine.SetState(switchGroup, switchKey);
                    else if (isSetRTPC && int.TryParse(switchKey, out int tempValue))
                        AkSoundEngine.SetRTPCValue(switchGroup, tempValue);
                    else if (switchObj != null)
                        AkSoundEngine.SetSwitch(switchGroup, switchKey, switchObj);
#endif
                }
            }
        }
        
        private string GetOverrideSoundInfo(string bankName, string soundName)
        {
            if (m_overrideSoundInfoDic != null
                && m_overrideSoundInfoDic.TryGetValue(bankName + "|" + soundName, out string overrideInfo))
            {
                return overrideInfo;
            }
            return "";
        }

        /// <summary>
        /// 预览播动效，额外的操作
        /// </summary>
        public void PreviewPlayEffectAddition(BattleMapTriggerAminationConfig config)
        {
            if (config.SwitchInterestTemplate != null || config.SwitchInterestTemplate != null)
            {
                TryChangeInterest(config);
            }
        }

        private void SendRevert()
        {
            ACGEventManager.Instance.Send(EventType_BattleView.AutoChess_Battle_AfterAnimationRevertScene, mapId);
        }

        private void ExecuteGameStart(Animator anim, BattleMapTriggerAminationConfig.ConfigData config)
        {
            if (!IsThisMapFocus())
            {
                return;
            }

            if (root == null)
            {
                return;
            }

#if ACGGAME_CLIENT
            if (anim != null)
            {
                if (config.extraMap != null)
                {
                    if (config.extraMap.TryGetValue(E_ET.GROUND_TYPE, out ED value))
                    {
                        if (value.boolParam && value.floatParam > 0)
                        {
                            //游戏开始阶段，英雄和特效贴地处理
                            var effect = root.TryGetComponent<StickingGroundEffect>();
                            effect.root = anim.transform.Find("dimian_group");
                            effect.playTime = value.floatParam;
                        }
                    }

                    if (config.extraMap.TryGetValue(E_ET.HANG_POINT_TYPE, out ED value2))
                    {
                        if (value2.boolParam && value2.floatParam > 0 && value2.stringParam.Filled())
                        {
                            //游戏开始阶段，小小英雄切换挂点
                            var effect = root.TryGetComponent<ChangeHangPointEffect>();
                            effect.isLock = true;
                            effect.jumpActionByEnd = true;
                            effect.playTime = value2.floatParam;
                            effect.hangPointName = value2.stringParam;
                        }
                    }
                }
            }
#endif
        }

        public void TryTriggerMapLogic(ChessPlayerUnit unit, bool isRegain, TKDictionary<int, List<int>> triggerDataList)
        {
            if (triggerDataList == null)
            {
                //触发区域比较特殊，空的时候也要判断，用于特效的复位
                TriggerMapTriggerArea(unit, isRegain, null);

                //连胜时间，同样也需要复位
                TriggerWinCountAndTime(unit, isRegain, 0);
                return;
            }

            foreach (var key in triggerDataList.Keys)
            {
                var dataList = triggerDataList[key];

                switch ((HaveStateEventTypeEnum)key)
                {
                    case HaveStateEventTypeEnum.None:
                        break;
                    case HaveStateEventTypeEnum.TriggerLogicArea:
                        TriggerMapTriggerArea(unit, isRegain, triggerDataList[key]);
                        break;
                    case HaveStateEventTypeEnum.Fetter:
                        TriggerFetter(unit, isRegain, triggerDataList[key]);
                        break;
                    case HaveStateEventTypeEnum.HeroPromotion:
                        TriggerHeroPromotion(unit, isRegain, triggerDataList[key]);
                        break;
                    case HaveStateEventTypeEnum.WinCountAndTime:
                        int triggerWinCount = 0;
                        if (dataList != null && dataList.Count > 0)
                        {
                            triggerWinCount = dataList[dataList.Count - 1];
                        }
                        TriggerWinCountAndTime(unit, isRegain, triggerWinCount);
                        break;
                }
            }

            if (!triggerDataList.ContainsKey((int)HaveStateEventTypeEnum.TriggerLogicArea))
            {
                //触发区域比较特殊，空的时候也要判断，用于特效的复位
                TriggerMapTriggerArea(unit, isRegain, null);
            }
        }

        public void SetHomeVisible(bool isHome)
        {
            //只在主场显示的
            if (m_onlyHomeShowGos != null)
            {
                foreach (var go in m_onlyHomeShowGos)
                {
                    if (go != null) go.SetActive(isHome);
                }
            }

            //只在客场显示的
            if (m_onlyNotHomeShowGos != null)
            {
                foreach (var go in m_onlyNotHomeShowGos)
                {
                    if (go != null) go.SetActive(!isHome);
                }
            }
        }

        private void TriggerMapTriggerArea(ChessPlayerUnit unit, bool isRegain, List<int> triggerIDList)
        {
            if (m_eventTriggerColliders == null || m_eventTriggerColliders.Length == 0)
            {
                return;
            }

            //每个map的TriggerManager都会收到这个消息，但消息里带了玩家id
            int colliderLength = m_eventTriggerColliders.Length;

            //上一次的
            GetNoDuplicateResult(triggerIDList, colliderLength, false);

            bool needExcute = false;
            if (isRegain)
            {
                //重回，就要触发
                needExcute = true;
            }
            else
            {
                //不是重回，那么判断是否有改变
                if (triggerIDList == null || triggerIDList.Count <= 1)
                {
                    //当前为空，或者只有一个元素，都要触发
                    needExcute = true;
                }
                else
                {
                    //判断上次的列表，是否和本次相同
                    GetNoDuplicateResult(triggerIDList, colliderLength, true);
                    needExcute = m_lastListForLogicEvent.Count != m_resultListForLogicEvent.Count;
                }
            }

            ////test log
            //if (triggerIDList == null)
            //{
            //    Diagnostic.Error(unit.PlayerData.ChessPlayerId + " NOTIFY_MAP_TRIGGER_AREA  null "
            //                                                   + " isRegain? " + isRegain
            //                                                   + " needExcute? " + needExcute
            //    );
            //}
            //else
            //{
            //    string str = "";
            //    for (int i = 0; i < triggerIDList.Count; i++)
            //    {
            //        str += (" [" + i + "]" + triggerIDList[i]);
            //    }
            //    Diagnostic.Error(unit.PlayerData.ChessPlayerId + " NOTIFY_MAP_TRIGGER_AREA " + triggerIDList.Count
            //                     + " isRegain? " + isRegain
            //                     + " needExcute? " + needExcute
            //                     + "\n" + str);

            //    if (m_resultListForLogicEvent != null)
            //    {
            //        string strResult = "";
            //        for (int i = 0; i < m_resultListForLogicEvent.Count; i++)
            //        {
            //            strResult += (" [" + i + "]" + m_resultListForLogicEvent[i]);
            //        }
            //        Diagnostic.Error(unit.PlayerData.ChessPlayerId + " RESULT LIST " + m_resultListForLogicEvent.Count
            //                         + " isRegain? " + isRegain
            //                         + " needExcute? " + needExcute
            //                         + "\n" + strResult);
            //    }
            //}

            if (needExcute && m_eventLogicList != null)
            {
                foreach (var cfg in m_eventLogicList)
                {
                    if (cfg.CheckTriggerArea(ChessMapCondition.TriggerArea,
                        m_resultListForLogicEvent,
                        m_eventLogicList,
                        isRegain,
                        colliderLength
                    ))
                    {
                        cfg.ExceViewEvent(unit, isRegain);
                    }
                }
            }
        }

        private void TriggerFetter(ChessPlayerUnit unit, bool isRegain, List<int> fetterDataList)
        {
            if (m_eventAnimDic == null || m_eventAnimDic.Count <= 0) return;
            if (!m_eventAnimDic.TryGetValue(E_CT.FETTER_REACH, out List<BattleMapTriggerAminationConfig> list)) return;

            CDInfo cdInfo = GetCDInfo(unit.PlayerData.ChessPlayerId);
            if (cdInfo.fetterDataSet == null)
            {
                cdInfo.fetterDataSet = new HashSet<int>();
            }

            //var debugStr = "";
            //for (int i = 0; i < fetterDataList.Count; i++)
            //{
            //    debugStr += fetterDataList[i] + (i == fetterDataList.Count - 1 ? "" : ",");
            //}
            //Diagnostic.Error("Receive Fetter !!!!!!!!! isRegain " + isRegain + " list "+ debugStr);

            if (m_CfgTempSet == null)
            {
                m_CfgTempSet = new HashSet<int>();
            }
            if (isRegain)
            {
                //这个set表示当前记录过的所有触发过的集合
                m_CfgTempSet.Clear();
                foreach (var item in fetterDataList)
                {
                    m_CfgTempSet.Add(item);
                }

                //重回，需要把不重复触发的，播到最终状态，重复触发的不播
                foreach (var fetterCfg in m_CfgTempSet)
                {
                    FindTriggerFetterData(fetterCfg, out int fetterType, out int fetterLevel, out int fetterMaxLevel);
                    foreach (var config in list)
                    {
                        var enterData = config.GetEnterAnimData();
                        if (enterData == null || enterData.extraMap == null ||
                            !enterData.extraMap.TryGetValue(E_ET.FETTER_COUNT, out ED value)) continue;

                        if (value.boolParam)
                        {
                            //不重复触发的
                            bool typeOK = enterData.checkValue == 0 ||
                                          enterData.checkValue == fetterType;
                            bool countOK = (int)value.floatParam == 0
                                ? fetterLevel == fetterMaxLevel
                                : fetterLevel == (int)value.floatParam;

                            if (typeOK && countOK)
                            {
                                TriggerAnimAndEffects(unit, true, config);
                            }
                        }
                    }
                }
            }
            else
            {
                //这个set表示本次新触发的集合
                m_CfgTempSet.Clear();
                if (fetterDataList.Count > cdInfo.lastFetterCfgCount && cdInfo.lastFetterCfgCount >= 0)
                {
                    for (int i = cdInfo.lastFetterCfgCount; i < fetterDataList.Count; i++)
                    {
                        m_CfgTempSet.Add(fetterDataList[i]);
                    }
                }

                foreach (var fetterCfg in m_CfgTempSet)
                {
                    FindTriggerFetterData(fetterCfg, out int fetterType, out int fetterLevel, out int fetterMaxLevel);
                    foreach (var config in list)
                    {
                        var enterData = config.GetEnterAnimData();
                        if (enterData == null || enterData.extraMap == null ||
                            !enterData.extraMap.TryGetValue(E_ET.FETTER_COUNT, out ED value)) continue;

                        bool typeOK = enterData.checkValue == 0 ||
                                      enterData.checkValue == fetterType;
                        bool countOK = (int)value.floatParam == 0
                            ? fetterLevel == fetterMaxLevel
                            : fetterLevel == (int)value.floatParam;

                        if (typeOK && countOK)
                        {
                            if (value.boolParam)
                            {
                                //不重回，新的不重复触发的，就触发一次
                                if (!cdInfo.fetterDataSet.Contains(fetterCfg))
                                {
                                    TriggerAnimAndEffects(unit, false, config);
                                }
                            }
                            else
                            {
                                //不重回，新的可重复触发的，直接触发
                                TriggerAnimAndEffects(unit, false, config);
                            }
                        }
                    }
                }
            }

            foreach (var fetterCfg in fetterDataList)
            {
                cdInfo.fetterDataSet.Add(fetterCfg);
            }
            cdInfo.lastFetterCfgCount = fetterDataList.Count;
        }

        private void TriggerHeroPromotion(ChessPlayerUnit unit, bool isRegain, List<int> heroPromotionDataList)
        {
            if (m_eventAnimDic == null || m_eventAnimDic.Count <= 0) return;
            if (!m_eventAnimDic.TryGetValue(E_CT.HERO_STAR_MATCH, out List<BattleMapTriggerAminationConfig> list)) return;

            CDInfo cdInfo = GetCDInfo(unit.PlayerData.ChessPlayerId);
            if (cdInfo.heroPromotionDataSet == null)
            {
                cdInfo.heroPromotionDataSet = new HashSet<int>();
            }

            //var debugStr = "";
            //for (int i = 0; i < heroPromotionDataList.Count; i++)
            //{
            //    debugStr += heroPromotionDataList[i] + (i == heroPromotionDataList.Count - 1 ? "" : ",");
            //}
            //Diagnostic.Error("Receive HeroPromotion !!!!!!!!! isRegain " + isRegain + " list "+ debugStr);

            if (m_CfgTempSet == null)
            {
                m_CfgTempSet = new HashSet<int>();
            }
            if (isRegain)
            {
                //这个set表示当前记录过的所有触发过的集合
                m_CfgTempSet.Clear();
                foreach (var item in heroPromotionDataList)
                {
                    m_CfgTempSet.Add(item);
                }

                //重回，需要把不重复触发的，播到最终状态，重复触发的不播
                foreach (var heroCfgID in m_CfgTempSet)
                {
                    foreach (var config in list)
                    {
                        var enterData = config.GetEnterAnimData();
                        if (enterData == null || enterData.extraMap == null ||
                            !enterData.extraMap.TryGetValue(E_ET.HERO_STAR_COUNT, out ED value)) continue;

                        if (value.boolParam)
                        {
                            CheckHeroPromotionTrigger(enterData, heroCfgID, value, out var typeOK, out var countOK);

                            //不重复触发的
                            if (typeOK && countOK)
                            {
                                TriggerAnimAndEffects(unit, true, config);
                            }
                        }
                    }
                }
            }
            else
            {
                //这个set表示本次新触发的集合
                m_CfgTempSet.Clear();
                if (heroPromotionDataList.Count > cdInfo.lastHeroPromotionCfgCount && cdInfo.lastHeroPromotionCfgCount >= 0)
                {
                    for (int i = cdInfo.lastHeroPromotionCfgCount; i < heroPromotionDataList.Count; i++)
                    {
                        m_CfgTempSet.Add(heroPromotionDataList[i]);
                    }
                }

                foreach (var heroCfgID in m_CfgTempSet)
                {
                    foreach (var config in list)
                    {
                        var enterData = config.GetEnterAnimData();
                        if (enterData == null || enterData.extraMap == null ||
                            !enterData.extraMap.TryGetValue(E_ET.HERO_STAR_COUNT, out ED value)) continue;

                        CheckHeroPromotionTrigger(enterData, heroCfgID, value, out var typeOK, out var countOK);

                        if (typeOK && countOK)
                        {
                            if (value.boolParam)
                            {
                                //不重回，新的不重复触发的，就触发一次
                                if (!cdInfo.heroPromotionDataSet.Contains(heroCfgID))
                                {
                                    TriggerAnimAndEffects(unit, false, config);
                                }
                            }
                            else
                            {
                                //不重回，新的可重复触发的，直接触发
                                TriggerAnimAndEffects(unit, false, config);
                            }
                        }
                    }
                }
            }

            foreach (var heroCfgID in heroPromotionDataList)
            {
                cdInfo.heroPromotionDataSet.Add(heroCfgID);
            }
            cdInfo.lastHeroPromotionCfgCount = heroPromotionDataList.Count;
        }

        private void GetNoDuplicateResult(List<int> triggerIDList, int colliderLength, bool getLastList)
        {
            if (getLastList)
            {
                if (m_lastListForLogicEvent == null)
                {
                    m_lastListForLogicEvent = new List<int>();
                }
                m_lastListForLogicEvent.Clear();
            }
            else
            {
                if (m_resultListForLogicEvent == null)
                {
                    m_resultListForLogicEvent = new List<int>();
                }
                m_resultListForLogicEvent.Clear();
            }

            //按照collider个数，去重
            //比如 112242311 = 1243 1
            if (triggerIDList != null)
            {
                if (m_tempListForLogicEvent == null)
                {
                    m_tempListForLogicEvent = new List<int>();
                }
                m_tempListForLogicEvent.Clear();
                for (int i = 0; i < triggerIDList.Count - (getLastList ? 1 : 0); i++)
                {
                    int id = triggerIDList[i];
                    if (!m_tempListForLogicEvent.Contains(id))
                    {
                        m_tempListForLogicEvent.Add(id);
                    }

                    if (m_tempListForLogicEvent.Count >= colliderLength)
                    {
                        if (getLastList)
                        {
                            m_lastListForLogicEvent.AddRange(m_tempListForLogicEvent);
                        }
                        else
                        {
                            m_resultListForLogicEvent.AddRange(m_tempListForLogicEvent);
                        }
                        m_tempListForLogicEvent.Clear();
                    }
                }

                //残余的，加入
                if (m_tempListForLogicEvent.Count > 0)
                {
                    if (getLastList)
                    {
                        m_lastListForLogicEvent.AddRange(m_tempListForLogicEvent);
                    }
                    else
                    {
                        m_resultListForLogicEvent.AddRange(m_tempListForLogicEvent);
                    }
                }
            }
        }

        private void TriggerWinCountAndTime(ChessPlayerUnit unit, bool isRegain, int triggerWinCount)
        {
            if (m_eventLogicList != null)
            {
                foreach (var cfg in m_eventLogicList)
                {
                    if (cfg.condition == ChessMapCondition.WinCountAndTime && cfg.winCountAndTimeData != null)
                    {
                        if (triggerWinCount == 0)
                        {
                            //复位，需要按照时间去复位，因为时间是全局值，所以传递过来之后，依然可以判断，即使断线重连
                            if (IsServerTimeInRange(cfg.winCountAndTimeData.TimeStart, cfg.winCountAndTimeData.TimeEnd))
                            {
                                cfg.ExceViewEvent(unit, isRegain);
                            }
                        }
                        else if (cfg.winCountAndTimeData.WinCount == triggerWinCount)
                        {
                            cfg.ExceViewEvent(unit, isRegain);
                        }
                    }
                }
            }
        }

        public void TriggerAnimAndEffects(ChessPlayerUnit unit, bool isRegain, BattleMapTriggerAminationConfig cfg)
        {
            if (cfg != null)
            {
                //Diagnostic.Error("TriggerAnimAndEffects isRegain " + isRegain);

                // 根据白名单检测是否能触发
                // 不能用unit.model_name判断，因为有时候是空
                string resultModelName = "";
                if (unit.PlayerData != null && unit.PlayerData.TinyData != null)
                {
                    int tinyID = unit.PlayerData.TinyData.iTinyId;
                    var heroCfg = DataBaseManager.Instance.SearchACGItem(tinyID);
                    if (heroCfg != null)
                    {
                        resultModelName = heroCfg.sPreviewResource;
                    }
                }

                if (cfg.CheckWhiteList(resultModelName, out var overrideConfig))
                {
                    string enterAnimName;
                    if (overrideConfig != null)
                    {
                        enterAnimName = overrideConfig.overrideEnterAnimName;
                    }
                    else
                    {
                        var enterAnimData = cfg.GetEnterAnimData();
                        if (enterAnimData != null)
                        {
                            enterAnimName = enterAnimData.animName;
                        }
                        else
                        {
                            enterAnimName = cfg.m_enterAnimationName;
                        }
                    }

                    if (!string.IsNullOrEmpty(enterAnimName))
                    {
                        PlayTriggerEffect(cfg, enterAnimName, overrideConfig: overrideConfig,
                            directToEnd: isRegain /*重回数据，直接播到最后*/);
                    }
                }
            }
        }

        public void DirectToGameStartAnimEnd()
        {
            //KDA棋盘，热更特殊处理，未来换实现
            if (mapId == 300081)
            {
                //KDA比较特殊，在战斗阶段3之后，需要保持上升棋盘的状态，此时重置相机，就不能再把游戏开始，播到最后了
                var model = ChessModelManager.Instance.GetBattleModel();
                if (model != null && model.CurrentPlayerId != -1 && model.SoGameData_View != null)
                {
                    TTAC_Quest_Client quest = model.SoGameData_View.GetQuestData(model.CurrentTurnCount);
                    if (quest != null)
                    {
                        if (quest.iStage >= 3)
                        {
                            return;
                        }
                    }
                }
            }

            if (m_eventAnimDic != null && m_eventAnimDic.Count > 0)
            {
                if (m_eventAnimDic.TryGetValue(E_CT.GAME_START, out List<BattleMapTriggerAminationConfig> list1))
                {
                    foreach (var config in list1)
                    {
                        var enterData = config.GetEnterAnimData();
                        if (enterData != null)
                        {
                            PlayTriggerEffect(config, enterData.animName, directToEnd: true);

                            //这个接口，用于把动效切换到idle状态
                            //之前没有处理挂点动画的情况，因为之前只在局外用，所以看不出来，现在局内用，就有问题，但是局内其实不需要挂点动画，所以去掉，并且给comp加上容错
                            //游戏开始特殊处理
                            //ExecuteGameStart(config.m_animator, enterData);
                        }
                    }

                    CheckGameStartTimeAnim(true);
                }
            }
        }

        private void CheckGameStartTimeAnim(bool directToEnd)
        {
            //带到达时间配置的事件触发动画，需要找到最后一个已经到达的时间点，显示动效

            if (m_eventAnimDic == null || m_eventAnimDic.Count <= 0) return;
            if (!m_eventAnimDic.TryGetValue(E_CT.REACH_TIME, out List<BattleMapTriggerAminationConfig> list)) return;

            DateTime serverNow = GetCurrServerTime();

            //一天有86400秒
            double minPastSeconds = 86400;

            if (m_needTriggerCfgs == null)
            {
                m_needTriggerCfgs = new List<BattleMapTriggerAminationConfig>();
            }
            m_needTriggerCfgs.Clear();

            foreach (var config in list)
            {
                var enterData = config.GetEnterAnimData();

                if (enterData == null || enterData.extraMap == null ||
                    !enterData.extraMap.TryGetValue(E_ET.SERVER_TIME, out ED value)) continue;

                if (!value.stringParam.StartsWith("state"))
                {
                    //代表需要保持状态用的anim，比如6-8点保持黑夜
                    continue;
                }
                int stateStringLength = 5;
                string resultTime = value.stringParam.Substring(stateStringLength, value.stringParam.Length - stateStringLength);

                DateTime triggerTime;
                if (DateTime.TryParseExact(resultTime, "HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out triggerTime))
                {
                    DateTime serverTriggerTime = serverNow.Date.Add(triggerTime.TimeOfDay);
                    double pastSeconds = serverNow.Subtract(serverTriggerTime).TotalSeconds;
                    if (pastSeconds >= 0)
                    {
                        //已经到达的时间，选择过了时间最短的
                        if (pastSeconds < minPastSeconds)
                        {
                            //找到了更小的，清空之前的列表
                            m_needTriggerCfgs.Clear();
                            m_needTriggerCfgs.Add(config);
                            
                            minPastSeconds = pastSeconds;
                        }
                        else if (pastSeconds == minPastSeconds)
                        {
                            m_needTriggerCfgs.Add(config);
                        }
                    }
                }
            }

            foreach (var cfg in m_needTriggerCfgs)
            {
                if (cfg != null)
                {
                    var enterData = cfg.GetEnterAnimData();
                    if (enterData != null)
                    {
                        PlayTriggerEffect(cfg, enterData.animName, directToEnd: directToEnd);
                    }
                }
            }
        }

        public void ResetSpecialEffects()
        {
            //热更写死，后续改为配置
            if (mapId == 300062 && root != null)
            {
                //可爱房间
                var sub_s8_fx_spinecamera_opening02 = GameUtil.FindTransform(root.transform, "sub_s8_fx_spinecamera_opening02");
                if (sub_s8_fx_spinecamera_opening02 != null)
                {
                    sub_s8_fx_spinecamera_opening02.gameObject.SetActive(false);
                }
                var spinecamera = GameUtil.FindTransform(root.transform, "spinecamera");
                if (spinecamera != null)
                {
                    spinecamera.gameObject.SetActive(false);
                }
            }
        }

        /// <summary>
        /// 吃鸡动画，有小小英雄挂点动效
        /// </summary>
        public bool HaveRankOneHangHeroAnim()
        {
            if (m_eventAnimDic == null)
            {
                return false;
            }

            foreach (var kvp in m_eventAnimDic)
            {
                List<BattleMapTriggerAminationConfig> cfgList = kvp.Value;
                if (cfgList == null)
                {
                    continue;
                }

                foreach (var config in cfgList)
                {
                    if (config == null)
                    {
                        continue;
                    }

                    var enterData = config.GetEnterAnimData();
                    if (enterData != null && enterData.type == E_CT.RANK_REACH && enterData.checkValue == 1)
                    {
                        //吃鸡动画
                        if (enterData.extraMap != null && enterData.extraMap.TryGetValue(
                                E_ET.HANG_POINT_TYPE, out ED value))
                        {
                            if (value.boolParam && value.floatParam > 0 && value.stringParam.Filled())
                            {
                                //小小英雄切换挂点
                                return true;
                            }
                        }
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 敌方观战
        /// </summary>
        public void EnemyVisit(int mapOwnerID)
        {
            //Diagnostic.Error("visit !!!!!!!!! "+ mapOwnerID);
            if (m_eventAnimDic != null && m_eventAnimDic.Count > 0)
            {
                //调试用
                //if (!m_eventAnimDic.ContainsKey(E_CT.TINY_HERO_VISIT))
                //{
                //    List<BattleMapTriggerAminationConfig> debugList = new List<BattleMapTriggerAminationConfig>();
                //    BattleMapTriggerAminationConfig config1 = new BattleMapTriggerAminationConfig();
                //    BattleMapTriggerAminationConfig config2 = new BattleMapTriggerAminationConfig();
                //    config1.m_enterAnimationName = "TINY_HERO_VISIT:10:anim1";
                //    config2.m_enterAnimationName = "TINY_HERO_VISIT:15:anim2";
                //    config1.Init();
                //    config2.Init();
                //    debugList.Add(config1);
                //    debugList.Add(config2);
                //    m_eventAnimDic.Add(E_CT.TINY_HERO_VISIT, debugList);
                //}
                if (m_eventAnimDic.TryGetValue(E_CT.TINY_HERO_VISIT, out List<BattleMapTriggerAminationConfig> list))
                {
                    float nowTime = Time.realtimeSinceStartup;

                    foreach (var config in list)
                    {
                        var enterData = config.GetEnterAnimData();
                        CDInfo cdInfo = GetCDInfo(mapOwnerID);
                        float lastTriggerTime = GetLastTriggerTime(cdInfo, config);

                        if (enterData != null)
                        {
                            bool canTrigger = GetCDFinish(enterData.checkValue, lastTriggerTime, nowTime);
                            if (canTrigger)
                            {
                                //Diagnostic.Error("Trigger !!!!!!!!! " + mapOwnerID + " anim " + enterData.animName);
                                PlayTriggerEffect(config, enterData.animName);

                                UpdateCDTime(cdInfo, config, nowTime);
                            }
                        }
                    }
                }
            }
        }


        private static void UpdateCDTime(CDInfo cdInfo, BattleMapTriggerAminationConfig config, float nowTime)
        {
            if (cdInfo != null && cdInfo.animConfigToLastTimeDic != null
                               && cdInfo.animConfigToLastTimeDic.ContainsKey(config))
            {
                cdInfo.animConfigToLastTimeDic[config] = nowTime;
            }
        }

        private static bool GetCDFinish(float configCD, float lastTriggerTime, float nowTime)
        {
            bool cdFinish = false;
            if (configCD <= 0 || lastTriggerTime < 0)
            {
                cdFinish = true;
            }
            else if (lastTriggerTime >= 0)
            {
                //有上次trigger的数据，那么需要看是否cd完成
                cdFinish = nowTime - lastTriggerTime >= configCD;
            }

            return cdFinish;
        }

        private static float GetLastTriggerTime(CDInfo cdInfo, BattleMapTriggerAminationConfig config)
        {
            float lastTriggerTime = -1;
            if (cdInfo != null && cdInfo.animConfigToLastTimeDic != null)
            {
                if (!cdInfo.animConfigToLastTimeDic.TryGetValue(config, out lastTriggerTime))
                {
                    cdInfo.animConfigToLastTimeDic.Add(config, lastTriggerTime);
                }
            }

            return lastTriggerTime;
        }

        private CDInfo GetCDInfo(int mapOwnerID)
        {
            if (m_dicMapOwnerIDToCDInfoDic == null)
            {
                m_dicMapOwnerIDToCDInfoDic = new TKDictionary<int, CDInfo>();
            }
            CDInfo cdInfo;
            if (!m_dicMapOwnerIDToCDInfoDic.TryGetValue(mapOwnerID, out cdInfo))
            {
                cdInfo = new CDInfo();
                cdInfo.animConfigToLastTimeDic = new TKDictionary<BattleMapTriggerAminationConfig, float>();
                m_dicMapOwnerIDToCDInfoDic.Add(mapOwnerID, cdInfo);
            }

            return cdInfo;
        }
        private void StartFlyEffect(BattleMapTriggerAminationConfig config, string animName, bool useBlend)
        {
            Transform targetTrans = FindOwnerOrEnemyTrans(config.FlyEffectTargetType ==
                                                          BattleMapTriggerAminationConfig.E_MAP_FLY_EFFECT_TARGET_TYPE
                                                              .SELF_HERO);
            if (targetTrans == null)
            {
                //此时可能是局外预览，没有对战环境，那么读取局外预览用到的unit
                switch (config.FlyEffectTargetType)
                {
                    case BattleMapTriggerAminationConfig.E_MAP_FLY_EFFECT_TARGET_TYPE.SELF_HERO:
                    {
                        if (m_previewSelfUnit != null)
                        {
                            targetTrans = ChessPlayerUnit.GetHangPoint(m_previewSelfUnit, EffectHangPoint.Body);
                        }
                        break;
                        }
                    case BattleMapTriggerAminationConfig.E_MAP_FLY_EFFECT_TARGET_TYPE.ENEMY_HERO:
                        if (m_fakeEnemyTrans == null)
                        {
                            var fakeEnemy = new GameObject("FakeEnemy");
                            m_fakeEnemyTrans = fakeEnemy.transform;
                            m_fakeEnemyTrans.parent = root.transform;
                            m_fakeEnemyTrans.localPosition = new Vector3(9.5f, 0.6f, 6.5f);
                            m_fakeEnemyTrans.localRotation = Quaternion.identity;
                            m_fakeEnemyTrans.localScale = Vector3.one;
                        }
                        targetTrans = m_fakeEnemyTrans;
                        break;
                }

                if (targetTrans == null)
                {
                    return;
                }
            }

            config.m_animator.gameObject.SetActive(true);

            m_flyEffectSwitchedPlayer = false;

            EnsureAnimParentActive(config.m_animator);
            PlayAnim(config.m_animator, config.m_blendTime, animName, useBlend);

            if (m_animConfigToFlyEffectInfoDic == null)
            {
                m_animConfigToFlyEffectInfoDic = new TKDictionary<BattleMapTriggerAminationConfig, FlyEffectInfo>();
            }

            if (!m_animConfigToFlyEffectInfoDic.TryGetValue(config, out var info))
            {
                //没有，那么创建，并开始动画
                info = new FlyEffectInfo();
                if (info.sourcePos == Vector3.zero)
                {
                    //只记录一次，因为后面m_animator的位置会变
                    info.sourcePos = config.m_animator.transform.position;
                }
                else
                {
                    //局外预览的时候要复位
                    config.m_animator.transform.position = info.sourcePos;
                }

                info.coroutine = CoroutineUtil.Instance.StartCoroutine(DoFlyEffect(config, targetTrans));
                m_animConfigToFlyEffectInfoDic.Add(config, info);
            }
            else
            {
                //存在，说明可能是别人同样的图触发过的，因为同一个地图的控制器是一个，即使属于不同玩家
                //需要停止特效，归位，然后播放
                StopFlyEffect(config, false);
                info.coroutine = CoroutineUtil.Instance.StartCoroutine(DoFlyEffect(config, targetTrans));
            }
        }

        private void StopFlyEffect(BattleMapTriggerAminationConfig config, bool removeFromDic)
        {
            if (m_animConfigToFlyEffectInfoDic == null)
            {
                return;
            }

            if (m_animConfigToFlyEffectInfoDic.TryGetValue(config, out var info))
            {
                CoroutineUtil.Instance.StopCoroutine(info.coroutine);
                if (config.m_animator != null)
                {
                    config.m_animator.gameObject.SetActive(false);
                    config.m_animator.transform.position = info.sourcePos;
                }

                if (removeFromDic)
                {
                    m_animConfigToFlyEffectInfoDic.Remove(config);
                }
            }
        }

        private IEnumerator DoFlyEffect(BattleMapTriggerAminationConfig config, Transform targetTrans)
        {
            if (config.m_animator == null || config.FlyEffectAnimator == null)
            {
                yield break;
            }

            Transform animTrans = config.m_animator.transform;

            while (!m_flyEffectSwitchedPlayer && targetTrans != null && Vector3.Distance(animTrans.position, targetTrans.position) >= 1f)
            {
                animTrans.position = Vector3.MoveTowards(animTrans.position, targetTrans.position, config.FlySpeed * Time.deltaTime);
                yield return null;
            }

            if (m_flyEffectSwitchedPlayer && targetTrans != null)
            {
                //切了地图，那么目标位置就不要更新了
                Vector3 staticTargetPos = targetTrans.position;
                while (Vector3.Distance(animTrans.position, staticTargetPos) >= 1f)
                {
                    animTrans.position = Vector3.MoveTowards(animTrans.position, staticTargetPos, config.FlySpeed * Time.deltaTime);
                    yield return null;
                }
            }

            EnsureAnimParentActive(config.FlyEffectAnimator);
            //播放击中特效
            PlayAnim(config.FlyEffectAnimator, 0, config.FlyEffectAnimName, false);

            float delayTime = 3f;
            if (config.FlyEffectAnimator.runtimeAnimatorController != null)
            {
                AnimationClip[] clips = config.FlyEffectAnimator.runtimeAnimatorController.animationClips;
                if (clips != null)
                {
                    foreach (var clip in clips)
                    {
                        if (clip.name.Equals(config.FlyEffectAnimName))
                        {
                            delayTime = clip.length;
                        }
                    }
                }
            }

            while ((delayTime -= Time.deltaTime) >= 0)
            {
                yield return null;
            }

            StopFlyEffect(config, true);
        }

        private static void PlayAnim(Animator anim, float blendTime, string animName, bool useBlend)
        {
            if (useBlend)
            {
                float normalizedDuration = Mathf.Max(0.0f, blendTime) /
                                           Mathf.Max(0.1f, anim.GetCurrentAnimatorStateInfo(0).length);
                anim.CrossFade(animName, normalizedDuration, 0);
            }
            else
            {
                anim.Play(animName, 0, 0f);
            }
        }

        /// <summary>
        /// 解决有些情况，anim需要播，但是所在的GameObject还没active的问题
        /// </summary>
        private void EnsureAnimParentActive(Animator anim)
        {
            if (anim == null)
            {
                return;
            }

            Transform trans = anim.transform;
            while (trans != null && !trans.gameObject.activeInHierarchy)
            {
                trans.gameObject.SetActive(true);
                trans = trans.parent;
                if (trans == null || trans == this.root)
                {
                    break;
                }
            }
        }

        private Transform FindOwnerOrEnemyTrans(bool owner)
        {
            ChessBattleModel battleModel = ChessModelManager.Instance.GetBattleModel();
            PlayerModel playerModel = battleModel.GetCurPlayerModel();//正在观察的的player

            if (playerModel.BattleFiledVsPlayerPair.Key != null && playerModel.BattleFiledVsPlayerPair.Value != null)
            {
                int mapOwnerID = -1;
                int enemyID = -1;
                if (playerModel.BattleFiledVsPlayerPair.Key.isHomeCourt)
                {
                    mapOwnerID = playerModel.BattleFiledVsPlayerPair.Key.iChairid;
                    enemyID = playerModel.BattleFiledVsPlayerPair.Value.iChairid;
                }
                else if (playerModel.BattleFiledVsPlayerPair.Value.isHomeCourt)
                {
                    mapOwnerID = playerModel.BattleFiledVsPlayerPair.Value.iChairid;
                    enemyID = playerModel.BattleFiledVsPlayerPair.Key.iChairid;
                }

                if (mapOwnerID != -1 && enemyID != -1)
                {
                    ChessPlayerController ctrl = ChessBattleGlobal.Instance.ChessPlayerCtrl;
                    ChessPlayerUnit unit = ctrl.GetPlayer(owner ? mapOwnerID : enemyID);
                    if (unit != null)
                    {
                        return ChessPlayerUnit.GetHangPoint(unit, EffectHangPoint.Body);
                    }
                }
            }

            return null;
        }

        private void TriggerWhenMapLoaded()
        {
            TriggerReachTimeAnim();
        }

        private void TriggerReachTimeAnim()
        {
            if (m_eventAnimDic == null || m_eventAnimDic.Count <= 0) return;
            if (!m_eventAnimDic.TryGetValue(E_CT.REACH_TIME, out List<BattleMapTriggerAminationConfig> list)) return;

            DateTime serverNow = GetCurrServerTime();

            foreach (var config in list)
            {
                var enterData = config.GetEnterAnimData();

                if (enterData == null || enterData.extraMap == null ||
                    !enterData.extraMap.TryGetValue(E_ET.SERVER_TIME, out ED value)) continue;

                string resultTime = value.stringParam;
                if (value.stringParam.StartsWith("state"))
                {
                    int stateStringLength = 5;
                    resultTime = value.stringParam.Substring(stateStringLength, value.stringParam.Length - stateStringLength);
                }

                DateTime triggerTime;
                if (DateTime.TryParseExact(resultTime, "HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out triggerTime))
                {
                    var serverTriggerTime = serverNow.Date.Add(triggerTime.TimeOfDay);
                    var leftSeconds = serverTriggerTime.Subtract(serverNow).TotalSeconds;
                    if (leftSeconds >= 0)
                    {
                        //还没有到时间
                        if (m_reachTimeTriggerInfoList == null)
                        {
                            m_reachTimeTriggerInfoList = new List<ReachTimeTriggerInfo>();
                        }

                        bool alreadyHave = false;
                        foreach (var info in m_reachTimeTriggerInfoList)
                        {
                            if (info.config == config)
                            {
                                alreadyHave = true;
                                break;
                            }
                        }

                        if (!alreadyHave)
                        {
                            m_reachTimeTriggerInfoList.Add(new ReachTimeTriggerInfo()
                            {
                                config = config,
                                leftSeconds = leftSeconds,
                                triggered = false,
                                initTime = serverNow,
                            });
                        }
                    }
                }
            }

            if (m_reachTimeTriggerInfoList != null && m_reachTimeTriggerInfoList.Count > 0 && m_reachTimeTriggerCoroutine == null)
            {
                m_reachTimeTriggerCoroutine = CoroutineUtil.Instance.StartCoroutine(ReachTimeTriggerCoroutine());
            }
        }

        private IEnumerator ReachTimeTriggerCoroutine()
        {
            if (m_reachTimeTriggerInfoList == null)
            {
                yield break;
            }

            m_leftReachTimeTriggerCount = m_reachTimeTriggerInfoList.Count;

            while (m_leftReachTimeTriggerCount > 0)
            {
                if (m_reachTimeTriggerCD == null)
                {
                    m_reachTimeTriggerCD = TKFrame.CoroutineWait.GetWaitForSeconds(1);
                }

                //没必要这么精确，降帧
                yield return m_reachTimeTriggerCD;

                //var battleModel = ChessModelManager.Instance.GetBattleModel();
                //if (battleModel == null || battleModel.IsInBattle())
                //{
                //    //战斗中，不可以触发
                //    yield return null;
                //}

                DateTime serverNow = GetCurrServerTime();

                //触发服务器时间事件
                foreach (var info in m_reachTimeTriggerInfoList)
                {
                    if (!info.triggered)
                    {
                        var pastSeconds = serverNow.Subtract(info.initTime).TotalSeconds;
                        if (pastSeconds >= info.leftSeconds)
                        {
                            //流逝的时间，比剩余时间长，说明到达时间了
                            var enterData = info.config.GetEnterAnimData();
                            if (enterData != null)
                            {
                                //超过准点60秒，直接显示结果，否则触发动效
                                PlayTriggerEffect(info.config, enterData.animName,
                                    directToEnd: pastSeconds - info.leftSeconds > 60);
                            }

                            info.triggered = true;
                            m_leftReachTimeTriggerCount--;
                        }
                    }
                }
            }
        }

        private DateTime GetCurrServerTime()
        {
            return TimeUtil.Converttimestamp(GameUtil.CurrServiceTime / 1000d);
            //return new DateTime(2023, 8, 18, 19, 0, 0);
        }

        public void DisableAllPlaySound()
        {
            if (root == null)
            {
                return;
            }

            var soundCompArr = root.GetComponentsInChildren<Chess_WwisePlaySound>(true);
            if (soundCompArr != null)
            {
                foreach (var soundComp in soundCompArr)
                {
                    if (soundComp != null)
                    {
                        soundComp.enabled = false;
                    }
                }
            }
        }

        #region 局外单独触发棋盘逻辑，局内不要进行调用
        public void PreviewMapTrigger(string triggerName)
        {
            //KDA棋盘，热更特殊处理，未来换实现
            if (mapId == 300081)
            {
                if (triggerName == E_CT.TURN_WIN)
                {
                    if (m_eventAnimDic.TryGetValue(E_CT.BATTLE_STAGE, out List<BattleMapTriggerAminationConfig> list))
                    {
                        foreach (var config in list)
                        {
                            var enterData = config.GetEnterAnimData();
                            if (enterData != null && enterData.checkValue == 3)
                            {
                                PlayTriggerEffect(config, enterData.animName, directToEnd: true);
                                TryChangeInterest(config);
                            }
                        }
                    }
                }
            }

            if (triggerName == E_CT.GAME_START || triggerName == E_CT.TURN_WIN || triggerName == E_CT.TURN_LOSE || triggerName == E_CT.RANK_REACH)
            {
                if (triggerName == E_CT.GAME_START)
                {
                    if (m_mapMgr != null)
                    {
                        var curMap = m_mapMgr.GetCurrentBattleMap();
                        if (curMap != null)
                        {
                            var itemCfg = DataBaseManager.Instance.SearchACGItem(curMap.MapId);
                            if (itemCfg != null && itemCfg.iMapShowType != 0)
                            {
                                m_mapMgr.OnTurnStart(2, true, itemCfg.iMapShowType);
                            }
                        }
                    }
                }
                else if (triggerName == E_CT.RANK_REACH)
                {
                    //播放吃鸡镜头动画
                    if (m_mapMgr != null)
                    {
                        m_mapMgr.OnFirstRankFoucs(true, true);
                    }
                }

                if (m_eventAnimDic.TryGetValue(triggerName, out List<BattleMapTriggerAminationConfig> list))
                {
                    foreach (var config in list)
                    {
                        var enterData = config.GetEnterAnimData();
                        if (enterData != null && CheckServerTime(enterData))
                        {
                            if (triggerName == E_CT.GAME_START)
                            {
                                //开场的，前面处理了
                                continue;;
                            }
                            else if (triggerName == E_CT.RANK_REACH && enterData.checkValue != 1)
                            {
                                //吃鸡的，只播达成排名第一的动效
                                continue;
                            }
                            PlayTriggerEffect(config, enterData.animName);
                        }
                    }
                }
            }

            //KDA棋盘，热更特殊处理，未来换实现
            if (mapId == 300081 && triggerName == E_CT.BATTLE_STAGE)
            {
                if (m_eventAnimDic.TryGetValue(E_CT.BATTLE_STAGE, out List<BattleMapTriggerAminationConfig> list3))
                {
                    foreach (var config in list3)
                    {
                        if (config != null)
                        {
                            var enterData = config.GetEnterAnimData();
                            if (enterData != null && enterData.checkValue == 3)
                            {
                                PlayTriggerEffect(config, enterData.animName);
                                TryChangeInterest(config);
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 动画直接播到终点
        /// </summary>
        /// <param name="triggerName"></param>
        public void PreviewDirectToEnd(string triggerName)
        {
            if (m_eventAnimDic.TryGetValue(triggerName, out List<BattleMapTriggerAminationConfig> list))
            {
                foreach (var config in list)
                {
                    var enterData = config.GetEnterAnimData();
                    if (enterData != null && CheckServerTime(enterData))
                    {
                        if (triggerName == E_CT.RANK_REACH)
                        {
                            //吃鸡镜头动画终点，屏蔽名次不是1的
                            if (enterData.checkValue != 1)
                            {
                                continue;
                            }

                            if (mapId == 300080)
                            {
                                //热更特殊处理：2023世界赛 棋盘
                                //吃鸡播放视频，要关掉
                                Transform videoCamera = GameUtil.FindTransform(root.transform, "VideoCamera");
                                if (videoCamera != null)
                                {
                                    videoCamera.SetActive(false);
                                }
                            }
                        }
                        else if (mapId == 300081 && triggerName == E_CT.BATTLE_STAGE)
                        {
                            //KDA棋盘，热更特殊处理，未来换实现
                            if (enterData.checkValue == 3)
                            {
                                if (enterData.animName.Contains("Changescene"))
                                {
                                    PlayTriggerEffect(config, "Changescene_wait", directToEnd: true);
                                    //利息球恢复
                                    RevertInterest(config);
                                    continue;//不用后面的通用PlayTriggerEffect
                                }
                            }
                            else
                            {
                                continue;//只特殊处理=3的情况
                            }
                        }

                        PlayTriggerEffect(config, enterData.animName, directToEnd: true);
                    }
                }
            }
        }

        public bool CheckTriggerActive(string triggerName)
        {
            if (triggerName == E_CT.GAME_START)
            {
              
                if (m_eventAnimDic.TryGetValue(E_CT.GAME_START, out List<BattleMapTriggerAminationConfig> list))
                {
                    return list.Count > 0;
                }
            }
            else if (triggerName == E_CT.TURN_WIN)
            {
                if (m_eventAnimDic.TryGetValue(E_CT.TURN_WIN, out List<BattleMapTriggerAminationConfig> list))
                {
                    return list.Count > 0;
                }
            }
            else if (triggerName == E_CT.RANK_REACH)
            {
                List<BattleMapTriggerAminationConfig> list = GetTriggerAniListByType(E_CT.RANK_REACH);
                if (list == null || list.Count == 0)
                    return false;

                return true;
            }
            else if (triggerName == E_CT.TURN_LOSE)
            {
                if (m_eventAnimDic.TryGetValue(E_CT.TURN_LOSE, out List<BattleMapTriggerAminationConfig> list))
                {
                    return list.Count > 0;
                }
            }

            return false;
        }

        /// <summary>
        /// 设置预览用的Unit
        /// </summary>
        public void SetPreviewSelfUnit(ChessPlayerUnit selfUnit)
        {
            m_previewSelfUnit = selfUnit;
        }

        #endregion

        public void Dispose()
        {
            RemoveEvents();

            if (m_animConfigToFlyEffectInfoDic != null)
            {
                foreach (var kvp in m_animConfigToFlyEffectInfoDic)
                {
                    var config = kvp.Key;
                    var info = kvp.Value;
                    if (info.coroutine != null)
                    {
                        CoroutineUtil.Instance.StopCoroutine(info.coroutine);
                    }
                    if (config.m_animator != null)
                    {
                        config.m_animator.transform.position = info.sourcePos;
                    }
                }
                m_animConfigToFlyEffectInfoDic.Clear();
            }

            m_rankReachHash.Clear();
            m_cacheAnim.Clear();
            m_triggerAnimList = null;
            m_eventAnimDic = null;
            root = null;
            m_eventLogicList = null;
            m_eventTriggerColliders = null;
            m_onlyHomeShowGos = null;
            m_onlyNotHomeShowGos = null;

            if (m_reachTimeTriggerInfoList != null)
            {
                m_reachTimeTriggerInfoList.Clear();
            }
            m_reachTimeTriggerInfoList = null;

            if (m_reachTimeTriggerCoroutine != null)
            {
                CoroutineUtil.Instance.StopCoroutine(m_reachTimeTriggerCoroutine);
                m_reachTimeTriggerCoroutine = null;
            }
        }
    }
}