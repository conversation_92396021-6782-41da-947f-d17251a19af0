using PBRTools;
using System.Collections;
using TKFrame;
using TKPlugins;
using UnityEngine;
using ZGame;
using ZGame.Stage;
using ZGameChess;

public class LobbyPreviewController : SystemManager
{

    public CustomLightingTool lightingTool;
    public GameObject hero_root;
    public int tinyId;

    private int m_currentTinyId = -1;
    private GameObject goTinyHero_Cur;
    private UnityEngine.Coroutine m_loadCoroutine = null;
    private bool m_inited = false;

    private void InitDS()
    {
#if !OUTSOURCE
        DeviceSettingPerformer gs;
        if (DeviceSettingPerformer.Instance == null)
        {
            gs = new DeviceSettingPerformer();
            Services.AddService<DeviceSettingPerformer>(gs);

            GameingSettingModel gsm = ChessModelManager.Instance.GetGameingSettingModel();
            EDevicePower displayMode = (EDevicePower)gsm.GetIntValue(GameingSettingModel.ParamNames.DisplayMode);
            var fpsMode = gsm.GetIntValue(GameingSettingModel.ParamNames.FramePerSecondSetting);
            var resolutionSetting = gsm.GetIntValue(GameingSettingModel.ParamNames.ResolutionSetting);
            gs.InitGameDisplayMode(ref displayMode, ref resolutionSetting, ref fpsMode, gsm.GetBoolValue("m_BPowerSaving"), gsm.GetBoolValue("m_Outline"), gsm.GetIntValue("m_LodConfigVersion"));
        }
#else
        DeviceSettingPerformer gs;
        if (DeviceSettingPerformer.Instance == null)
        {
            gs = new DeviceSettingPerformer();
            Services.AddService<DeviceSettingPerformer>(gs);

            EDevicePower displayMode = EDevicePower.EDP_Ultra;
            var fpsMode = 3;
            var resolutionSetting = 0;
            gs.InitGameDisplayMode(ref displayMode, ref resolutionSetting, ref fpsMode, false, false, 0);
        }
#endif
    }
    public void InitNetService()
    {
#if !OUTSOURCE
        NetBehaviour _Network = Services.GetService<NetBehaviour>();
        if (_Network == null)
        {
            _Network = gameObject.AddComponent<NetBehaviour>();
            Services.AddService<NetBehaviour>(_Network);
        }
#endif
    }

    protected override IEnumerator Initialize()
    {
        DataBaseManager.Instance.Initialize();

        ChessUtil.MakeTransformIdentity(transform);

        TKFrame.NotchSizeImp.InitNotchSize();

        yield return base.Initialize();

        ACG.Core.Core.Setup();
        var WwiseListener = new GameObject("WwiseListerner");
        var akAudioListener = WwiseListener.AddComponent<AkAudioListener>();
        //var akAudioListener = this.gameObject.AddComponent<AkAudioListener>();

#if !OUTSOURCE
        MonitorBehaviour.audioLog = true;
#endif

        var akTerminator = gameObject.AddComponent<AkTerminator>();
        gameObject.AddComponent<TKAudioPlayManager>();

        TKAudioPlayManager.instance.canPlayEffectMusic = true;
        TKAudioPlayManager.instance.canPlayBackMusic = true;

        ZGameChessClientSwitch.IsOpenWwiseStream = true;

        Chess_WwisePlayManager.instance.SetBackVolume(100);
        Chess_WwisePlayManager.instance.SetEffectVolume(100);

        //ע��TKFrame���Žӿڵ�TKPluginAudioPlayDelegate��
        TKPluginAudioPlayDelegate.PreLoadAudioClipFunc = TKAudioPlayManager.instance.PreLoadAudioClip;
        TKPluginAudioPlayDelegate.StopAudioFuncByClip = TKAudioPlayManager.instance.Stop;
        TKPluginAudioPlayDelegate.StopAudioFuncByName = TKAudioPlayManager.instance.Stop;
        TKPluginAudioPlayDelegate.PlayAudioClipBackMusicFunc = TKAudioPlayManager.instance.PlayBackMusic;
        TKPluginAudioPlayDelegate.PlayAssetBackMusicFunc = TKAudioPlayManager.instance.PlayBackMusic;
        TKPluginAudioPlayDelegate.PlayAudioClipFunc = TKAudioPlayManager.instance.Play;
        TKPluginAudioPlayDelegate.PlayAssetAudioFunc = TKAudioPlayManager.instance.Play;

        TKPluginAudioPlayDelegate.PlayWwiseBankFunc = Chess_WwisePlayManager.instance.PlayWwiseBank_TKFrame;
        TKPluginAudioPlayDelegate.StopPlayWwiseBankFunc = Chess_WwisePlayManager.instance.StopMusic;

        COSUpdate.COSPackageContainer.GetBussinessAudioDownloader((downloader) =>
        {
            if (downloader != null)
            {
                downloader.AddFinishedCallback((_, success) =>
                {
                    AkBasePathGetter.AddCOSBusAudioPath();
                });
                downloader.Download();
            }
            else
            {
                AkBasePathGetter.AddCOSBusAudioPath();
            }
        });

        InitDS();
        InitNetService();

        gameObject.AddComponent<KeyEventManager>();

        //���ɽ����л�
        StageRunner stageRunner = Services.GetService<IStageRunner>() as StageRunner;

        stageRunner.Transition = new Transition();

        //�ȸ���������Ƶ��Դ
        yield return Chess_WwisePlayManager.instance.ReloadBnk();

        DataBaseManager.Instance.Initialize();
        SceneCameraManager.InitCamera();
        CameraConfigManager.Instance.GenerateCameraConfigs();

        //var mapConfig = GameObject.FindObjectOfType<BattleMapConfig>();
        //if (mapConfig != null)
        //{
        //    var sdfConfig = mapConfig.MapPathConfig;
        //    if (sdfConfig != null)
        //        SDFPath = new SDFPathConfig(sdfConfig.bytes);
        //}
        m_inited = true;
    }

    public override void Update()
    {
        base.Update();

        if (m_inited)
        {
            if (m_currentTinyId != tinyId)
            {
                m_currentTinyId = tinyId;
                LoadTinyHero();
            }
        }
    }

    private void LoadTinyHero()
    {
        DataBaseManager.Instance.Initialize();
        if (m_loadCoroutine != null)
            SystemManager.getInstance().StopCoroutine(m_loadCoroutine);

        bool fromCos = ChessPlayerBodyCache.GetBodyResNameLobby(tinyId, out string model_path, out string model_name);
        m_loadCoroutine = SystemManager.getInstance().StartCoroutine(ChessPlayerLoader.Load(model_path, model_name, Callback_LoadedBodyAsset_Local, this));
    }

    void Callback_LoadedBodyAsset_Local(LoadedAsset loadAsset, LittleLegendCfg cfg)
    {
        GameObject tmpGo = loadAsset.GetAsset<GameObject>();

        Diagnostic.Log($"LobbyTinyHeroReplace::Callback_LoadedBodyAsset with: {loadAsset.BundlePath} {loadAsset.AssetName}, tmpGo is null:{tmpGo == null}");

        Callback_LoadedBodyAsset(tmpGo, cfg);
    }

    void Callback_LoadedBodyAsset(GameObject tmpGo, LittleLegendCfg cfg)
    {
        //GameObject tmpGo = loadAsset.GetAsset<GameObject>();
        if (null == tmpGo)
        {
            return;
        }
        //
        GameObject tmpGoTarget = GameObject.Instantiate<GameObject>(tmpGo);

        if (null != tmpGoTarget)
        {
            ToolKit.SetLayer(tmpGoTarget, LayerMask.NameToLayer("Level"));
            var tmpCompAnim = tmpGoTarget.GetComponentInChildren<LobbyAnimationFrameAudioEventHandlerV2>();
            if (null != tmpCompAnim)
            {
                tmpCompAnim.EmHandler_Scene_Cur = LobbyAnimationFrameAudioEventHandlerV2.EmHandler_Scene.em_Lobby;
            }
            tmpGoTarget.transform.SetParent(hero_root.transform);
            tmpGoTarget.transform.localPosition = Vector3.zero;
            tmpGoTarget.transform.localRotation = tmpGo.transform.localRotation;//Quaternion.identity;
            tmpGoTarget.transform.localScale = tmpGo.transform.localScale;//Vector3.one;
            //�滻Animator
            Animator tmpComponent = tmpGoTarget.GetComponent<Animator>();
            string strPlayInfoClipName = "idle_UI";//"UI_Normal_enter";
            Diagnostic.Log("LobbyTinyHeroReplace::IntendToPlay:" + strPlayInfoClipName);
            if (null != tmpComponent)
            {
                tmpComponent.Play(strPlayInfoClipName);
            }

            //
            var tmpComp = tmpGoTarget.GetComponentInChildren<BoxCollider>(true);
            if (null != tmpComp)
            {
                var tmpArrTargetComp = tmpComp.gameObject.GetComponents<LobbySceneCharacterAnimEntry>();
                if (null == tmpArrTargetComp)
                {
                    //

                }
                else
                {
                    //
                    if (tmpArrTargetComp.Length < 2)
                    {
                        for (int i = 2 - tmpArrTargetComp.Length; i > 0; i--)
                        {
                            tmpComp.gameObject.AddComponent<LobbySceneCharacterAnimEntry>();
                        }

                        tmpArrTargetComp = tmpComp.gameObject.GetComponents<LobbySceneCharacterAnimEntry>();
                    }
                    else
                    {
                        //
                    }

                    //
                    tmpArrTargetComp[0].emSceneObj2SysVal = LobbySceneCharacterAnimEntry.EmCharacterAnimatorObj2System.em_CharacterAnimatorObj2Sys_Role_Hero;
                    //tmpArrTargetComp[0].objValParam = null;
                    tmpArrTargetComp[1].emSceneObj2SysVal = LobbySceneCharacterAnimEntry.EmCharacterAnimatorObj2System.em_CharacterAnimatorObj2Sys_None;
                }

            }


            var tmpCharacterHangPoint = tmpGoTarget.GetComponent<CharacterHangPoint>();
            if (null != tmpCharacterHangPoint)
            {
                tmpCharacterHangPoint.SetLittleLegendCfg(cfg);
                //
                var tmpTinyHero = DataBaseManager.Instance.SearchTinyHero(tinyId);
                if (null != tmpTinyHero && tmpTinyHero.sEffectLayerScene == "1")//��Ч��ӰӰ�쵽��С�����������⴦����ʾ.
                {
                    ChessUtil.CheckInitEffectDataList(tmpGoTarget, tmpCharacterHangPoint, GameObjectLayer.BattleEffect);
                }
                else
                {
                    ChessUtil.CheckInitEffectDataList(tmpGoTarget, tmpCharacterHangPoint, GameObjectLayer.Scene);
                }
            }

            lightingTool.UpdateLightingInternal();
            //�����Ч������
            var tmpObjComp = tmpGoTarget.TryGetComponent<ChessPlayerEffectEvent>();
            if (tmpObjComp != null)
            {
                tmpObjComp.CustomLight = lightingTool;
            }

            //��Todo���滻ССӢ��
            if (null != goTinyHero_Cur)
            {
                var tmpCompAudioFrameHandler = goTinyHero_Cur.GetComponent<LobbyAnimationFrameAudioEventHandlerV2>();
                if (null != tmpCompAudioFrameHandler)
                {
                    tmpCompAudioFrameHandler.DisposeReleaseList();
                }
                GameObject.Destroy(goTinyHero_Cur);
            }
            goTinyHero_Cur = tmpGoTarget;
        }
    }
}