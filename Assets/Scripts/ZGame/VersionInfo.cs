using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Text.RegularExpressions;
using TKFrame;
using UnityEngine;
using UnityEngine.Rendering;
#if !OUTSOURCE
using GCloud.MSDK;
#endif
using ZGame;
using System.Linq;
#if UNITY_EDITOR
using UnityEditor;
using UnityEditor.Callbacks;
#endif


namespace TKPlugins
{
    /// <summary>
    /// 与安装包绑定的版本信息，从streamAssets/version.ini中读取，不允许运行时修改
    /// </summary>
    public class VersionInfo
    {
        /// <summary>
        /// 游戏版本号
        /// </summary>
        public static int PackageVersion
        {
            get
            {
#if !OUTSOURCE
#if UNITY_IPHONE
                return VersionConfig.GetValue("IOSBuildNumber", 0, false);
                // return GetInt("IOSBuildNumber");
#elif UNITY_ANDROID
                // return GetInt("AndroidVersionCode");
                return VersionConfig.GetValue("AndroidVersionCode", 0, false);
#else
                return 99999;
#endif
#else
                return 99999;
#endif
            }
        }

        /// <summary>
        /// 安装包内资源版本号(与游戏中实际的资源版本号可能不同) 该字段用途不明
        /// </summary>
        public static int ResourceVersion
        {
            get
            {
#if !OUTSOURCE
                // return GetInt("ResourceVersion");
                return VersionConfig.GetValue("ResourceVersion", 0, false);
#else
                return 0;
#endif
            }
        }
        
        /// <summary>
        /// 构建时，保存的当时的资源版本，格式为四段式
        /// </summary>
        public static string ResourceVersionReal
        {
            get
            {
                string resourceVersion = GetString("ResourceVersionReal");
                if (string.IsNullOrEmpty(resourceVersion)) 
                    return "0.0.0.0";
                return resourceVersion;
            }
        }
        
        public static string ResourceVersionReal_Internal
        {
            get
            {
                string resourceVersion = GetString("ResourceVersionReal", true);
                if (string.IsNullOrEmpty(resourceVersion)) 
                    return "0.0.0.0";
                return resourceVersion;
            }
        }


        /// <summary>
        /// 打包需要配置的版本字符串，只做展示用途
        /// </summary>
        public static string BundleVersion
        {
            get
            {
#if !OUTSOURCE
                // return GetString("BundleVersion");
                return VersionConfig.GetValue("BundleVersion", "0", false);
#else
                return "0";
#endif
            }
        }
        /// <summary>
        /// 游戏内战斗逻辑版本号，不同战斗逻辑不能同玩
        /// </summary>
        private static int FightLogicVersion
        {
            get
            {
                return GetInt("FightLogicVersion");
            }
        }
        
        /// <summary>
        /// 游戏内通用逻辑版本号，不同逻辑会分配到不同的服务器
        /// </summary>
        public static int LogicVersion
        {
            get
            {
                return GetInt("LogicVersion");
            }
        }


        private static int isPreRelease = -1;
        private static int isCloseDebug2 = -1;
        /// <summary>
        /// 注意！！！！！！！！！！不止debug和release
        /// --默认debug，按照发布需求调整：debug，audit，exper，prerelease，release
        /// </summary>
        public static string VersionMode
        {
            get
            {
#if !OUTSOURCE
                if(isCloseDebug2 >= 1) return TKPlugins.VersionType.closedebug2.ToString();
                if (isPreRelease >= 1) return TKPlugins.VersionType.prerelease.ToString();
                
                string verMode = VersionConfig.GetValue("VersionMode", "debug", false);
                if (isPreRelease < 0)
                {
                    if (verMode == TKPlugins.VersionType.release.ToString() && PreBackDoorOpen)
                    {
                        isPreRelease = 1;
                        Diagnostic.Log("prerelease backdoor open!!!!!");
                        return TKPlugins.VersionType.prerelease.ToString();
                    }
                    isPreRelease = 0;
                }

                if (isCloseDebug2 < 0)
                {
                    if (verMode == TKPlugins.VersionType.closedebug.ToString() && PreBackDoorOpen)
                    {
                        isCloseDebug2 = 1;
                        Diagnostic.Log("isCloseDebug2 backdoor open!!!!!");
                        return TKPlugins.VersionType.closedebug2.ToString();
                    }

                    isCloseDebug2 = 0;
                }
                
                return verMode;
#else
                return "debug";
#endif
            }
        }

        // private static int HasBackDoor = 0;
        /*public static bool PreBackDoorOpen
        {
            get
            {
                if (HasBackDoor == 0)
                {
#if !UNITY_EDITOR && UNITY_ANDROID
                    HasBackDoor =  MSDKTools.IsAppInstalled("com.tencent.jkchesstikibackdoor") ? 1: -1;
#endif

#if !UNITY_EDITOR && UNITY_IOS
                    HasBackDoor =  IOSCanOpenURL.CheckUrl("jkchesstikibackdoor://") ? 1: -1;
#endif
                }
                return HasBackDoor == 1;
            }
        }*/
#if !OUTSOURCE
        public static bool IsReleaseVersion
        {
            get
            {
                return VersionMode.Equals(TKPlugins.VersionType.release.ToString(), StringComparison.OrdinalIgnoreCase);
            }
        }
        
        /// <summary>
        /// debug版
        /// </summary>
        public static bool IsDebugVersion
        {
            get
            {
                return VersionMode.Equals(TKPlugins.VersionType.debug.ToString(), StringComparison.OrdinalIgnoreCase);
            }
        }

        /// <summary>
        /// debug版
        /// </summary>
        public static bool IsCloseDebugVersion
        {
            get
            {
                return VersionMode.Equals(TKPlugins.VersionType.closedebug.ToString(), StringComparison.OrdinalIgnoreCase);
            }
        }

        /// <summary>
        /// 抢先版
        /// </summary>
        public static bool IsExperVersion
        {
            get
            {
                return VersionMode.Equals(TKPlugins.VersionType.exper.ToString(), StringComparison.OrdinalIgnoreCase);
            }
        }

        public static string SetList
        {
            get
            {
                return GetString("SetList");
            }
        }
        
        public static bool FrameProfile
        {
            get
            {
                return GetString("FrameProfile") == "true";
            }
        }
        
        /// <summary>
        /// Gcloud日志级别
        /// </summary>
        public static string GCloudLogType
        {
            get
            {
                return GetString("GCloudLogType");
            }
        }
#endif
        /// <summary>
        /// Build号，soda打包时生成的编号
        /// </summary>
        public static int SodaBuildNumber
        {
            get
            {
#if UNITY_EDITOR
                return 9999999;//编辑器下返回长长久久
#else
                return GetInt("SodaBuildNumber");
#endif
            }
        }

        private static string EditorRuntimeSVNVersion = string.Empty;
        /// <summary>
        /// SVN版本号，当前安装包对应的svn提交号
        /// </summary>
        public static string SVNRevNumber
        {
            get
            {
                if (Application.isEditor)
                {
                    if (string.IsNullOrEmpty(EditorRuntimeSVNVersion))
                    {
                        EditorRuntimeSVNVersion = DoEditorFetchSVNRevNumberByCommandLine();
                    }

                    return EditorRuntimeSVNVersion;
                }
                else
                    return GetString("SVNRevNumber");
            }
        }

#if UNITY_EDITOR
        // Clear svn rev number after scripts has recompiled.
        // Note: if with flag "-quit" in batchmode, this method won't be invoked.
        [DidReloadScripts]
        private static void OnScriptsReloaded()
        {
            EditorRuntimeSVNVersion = String.Empty;
        }
#endif

        private static string DoEditorFetchSVNRevNumberByCommandLine()
        {
            try
            {
#if UNITY_EDITOR_WIN
                using (var p = new System.Diagnostics.Process())
                {
                    p.StartInfo.UseShellExecute = false;
                    p.StartInfo.RedirectStandardOutput = true;
                    p.StartInfo.FileName = "svnversion";
                    p.StartInfo.Arguments = "--no-newline";
                    p.StartInfo.CreateNoWindow = true;
                    p.Start();
                    string output = p.StandardOutput.ReadToEnd();
                    p.WaitForExit();
                    var indexOfColon = output.IndexOf(':');
                    if (indexOfColon != -1)
                        return output.Substring(0, indexOfColon);
                    else
                        return output.Trim();
                }
#elif UNITY_EDITOR_OSX || UNITY_EDITOR_LINUX
                using (var p = new System.Diagnostics.Process())
                {
                    p.StartInfo.FileName = "/bin/bash";
                    p.StartInfo.UseShellExecute = false;
                    p.StartInfo.RedirectStandardInput = true;
                    p.StartInfo.RedirectStandardOutput = true;
                    var args = "svnversion --no-newline";
                    p.Start();
                    p.StandardInput.WriteLine(args);
                    p.StandardInput.WriteLine("exit");
                    p.StandardInput.Flush();
                    string output = p.StandardOutput.ReadToEnd();
                    p.WaitForExit();
                    var indexOfColon = output.IndexOf(':');
                    if (indexOfColon != -1)
                        return output.Substring(0, indexOfColon);
                    else
                        return output.Trim();
                }
#else
                return "1";
#endif
            }
            catch (System.Exception)
            {
                TKFrame.Diagnostic.Error("VersionInfo: 无法找到svn命令行，请安装svn命令行工具");
                return "1";
            }
        }

        /// <summary>
        /// SVN版本号，如果拿不到版本号（比如本地打包），则返回0。编辑器环境，返回1
        /// </summary>
        private static int SVNRevNumberInt
        {
            get
            { 
                if (Application.isEditor)
                    return 1;
                // remove non-number characters
                if (int.TryParse(Regex.Replace(SVNRevNumber, @"[\D]", "", RegexOptions.None), out int svnNum))
                    return svnNum;
                else
                    return 0;
            }
        }

        // /// <summary>
        // /// 是否是体验版本
        // /// </summary>
        // public static bool IsPreview
        // {
        //     get
        //     {
        //         return GetString("IsPreview") == "true";
        //     }
        // }

        private static bool AuditVersion = false;
        #if !OUTSOURCE
        /// <summary>
        /// 是否是审核版本
        /// </summary>
        public static bool IsAuditVersion
        {
            get
            {
#if !JK_RELEASE
                int localAuditFlag = PlayerPrefs.GetInt("LocalAuditFlag", 0);
                if (localAuditFlag != 0)
                    return true;
#endif
                // return GetString("AuditVersion") == "true" || VersionMode.Equals(TKPlugins.VersionType.audit.ToString(), StringComparison.OrdinalIgnoreCase);
                return AuditVersion || VersionMode.Equals(TKPlugins.VersionType.audit.ToString(), StringComparison.OrdinalIgnoreCase);
            }

            set
            {
                AuditVersion = value;
            }
        }
#endif
        private static bool AuditVersionBeforeLogin = false;
        //因为IsAuditVersion 这个标记需要在拉取到区服之后才能设置，在这之前的需要审核屏蔽的内容用AuditVersionBeforeLogin，热更新之后再打开，例如扫码登录等
        public static bool IsAuditVersionBeforeLogin()
        {
            return AuditVersionBeforeLogin;
        }
        public static void SetAuditVersionBeforeLogin(bool value)
        {
            AuditVersionBeforeLogin = value;
        }

        public static bool IsUSVersion
        {
            get
            {
                return GetString("IsUSVersion") == "true";
            }
        }

        public static bool IsXianYou
        {
            get
            {
                return GetString("IsXianYou") == "true";
            }
        }

        /// <summary>
        /// 是否是预发布版本
        /// </summary>
// public static bool IsPreRelease
// {
//     get
//     {
//         return VersionMode.Equals(TKPlugins.VersionType.prerelease.ToString(), StringComparison.OrdinalIgnoreCase);;
//     }
// }
#if !OUTSOURCE
        public static bool IsInternationalRelease
        {
            get
            {
                return VersionMode.Equals(TKPlugins.VersionType.intlrelease.ToString(), StringComparison.OrdinalIgnoreCase);
            }
        }
#endif
        public static bool IsHasLoginFlag
        {
            get
            {
                return GetString("loginFlag") == "true";
            }
        }

        public static bool IsHasMoa
        {
            get
            {
                return GetString("moaFlag") == "true";
            }
        }

        public static bool IsEnableLog
        {
            get
            {
                return GetString("EnableLog") == "true";
            }
        }

        public static bool IsEnableRuntimeInspector
        {
            get
            {
                return GetString("EnableRuntimeInspector") == "true";
            }
        }

        /// <summary>
        /// 废弃
        /// </summary>
        public static bool IsOpenErrorTip
        {
            get
            {
                return GetString("ErrorTip") == "true";
            }
        }
        
        public static bool UseCustomTexture
        {
            get
            {
                return GetString("UseCustomTextures") == "true";
            }
        }
#if !OUTSOURCE
        /// <summary>
        /// 版本类型
        /// Full全量版本 不支持增新版本(默认测试时发布的版本)
        /// First首包版本 带全量资源，支持增量更新
        /// Lite 轻量版本 首次进入游戏后会从CDN上拉取全量资源，支持增量更新(暂时不用)
        /// </summary>
        public static string VersionType
        {
            get
            {
                // return GetString("VersionType");
                return VersionConfig.GetValue("VersionType", "Full", false);
            }
        }
        
        public static bool IsFullVersionType()
        {
            return VersionType == "Full" || VersionType == "";
        }

        public static int DolphinChannelID
        {
            get
            {
                return VersionConfig.GetValue("DolphinChannelID", 0, false);
            }
        }
        
        public static int DolphinChannelID_low
        {
            get
            {
                return VersionConfig.GetValue("DolphinChannelID_low", 0, false);
            }
        }
        
        public static int DolphinChannelID_high
        {
            get
            {
                return VersionConfig.GetValue("DolphinChannelID_high", 0, false);
            }
        }

        public static int PufferChannelID
        {
            get
            {
                // return GetUInt("PufferChannelID");
                return VersionConfig.GetValue("PufferChannelID", 0, false);
            }
        }

        /// <summary>
        /// 当VersionType为First或Lite时
        /// 根据此接口来判断是否为预发布资源版本
        /// </summary>
        /// <returns></returns>
        public static bool IsReleaseUpdateURL
        {
            get
            {
                if (isPreRelease >= 1) return false;
                if (isCloseDebug2 >= 1) return false;
                // return GetString("IsReleaseUpdateURL", false) == "true";
                return VersionConfig.GetValue("IsReleaseUpdateURL", false, false);
            }
        }
        
        /// <summary>
        /// 构建号（唯一id）
        /// </summary>
        public static string Build_UUID
        {
            get
            {
                return VersionConfig.GetValue("build_UUID", "", false);
            }
        }
        
        /// <summary>
        /// 拳头版本对齐标识
        /// </summary>
        public static string VersionHead
        {
            get
            {
                return  GetString("VersionHead");
            }
        }

        public static string COSEnv
        {
            get
            {
                var env = GetString("COSEnv");

                if (Application.isEditor && string.IsNullOrEmpty(env))
                {
                    env = TKFrame.COSResource.COSGlobalDefine.GetCOSEnvFormSVN();
                }

                return env;
            }
        }
#endif
        public static bool GrayVersion { get; set; }

        private static Dictionary<string, string> default_config = new Dictionary<string, string>();
        private static Dictionary<string, string> internal_config = new Dictionary<string, string>();
        private static bool _initialized = false;
        public static readonly string FILE_NAME = "version.ini";
        public static readonly string PATH = Application.streamingAssetsPath + "/" + FILE_NAME;
        public const string PATTERN = @"\s*(.+?)\s*=\s*(.+?)\s*$";
        //包外还是包内配置
        private static bool isEXTERNAL = false;
        public static IEnumerator Initialize()
        {
            if (_initialized)
            {
                yield break;
            }
#if !OUTSOURCE
            // isPreRelease = -1;
            // isCloseDebug2 = -1;
            
            string external_AllContent = string.Empty;
            string externalDataPath = GameVersionModel.VersionDataPath + "/"+ FILE_NAME;
            // if (!VersionInfo.IsFullVersionType())
            {
                if (File.Exists(externalDataPath))
                {
                    external_AllContent = System.IO.File.ReadAllText(externalDataPath);
                    if (IsEncrypted(external_AllContent))
                    {
                        try
                        {
                            external_AllContent = Decrypt(external_AllContent);//解密
                            Diagnostic.Log("VersionInfo 加载外部文件 " + externalDataPath);
                        }
                        catch (Exception e)
                        {
                            external_AllContent = "";
                            Diagnostic.Log("VersionInfo 外部文件解密报错（可疑） " + externalDataPath + e.StackTrace);
                        }
                    }
                    else
                    {
                        external_AllContent = "";
                        Diagnostic.Log("VersionInfo 外部文件未加密（可疑） " + externalDataPath);
                    }
                }
                else
                {
                    Diagnostic.Log("VersionInfo 外部文件不存在 " + externalDataPath);
                }
            }
            Regex regex = new Regex(PATTERN, RegexOptions.Multiline);
            MatchCollection collection;
            
            default_config.Clear();
            internal_config.Clear();
            if (!string.IsNullOrEmpty(external_AllContent))
            {
                isEXTERNAL = true;
                collection = regex.Matches(external_AllContent);
                for (int i = 0; i < collection.Count; ++i)
                {
                    default_config[collection[i].Groups[1].Value] = collection[i].Groups[2].Value;
                }
            }
            string allContent = string.Empty;
            // if (string.IsNullOrEmpty(allContent))
            {
                //读取配置文件
#if UNITY_ANDROID
                var path = PATH;
#else
                var path = "file://" + PATH;
#endif
                Diagnostic.Log("VersionInfo 加载内部文件 " + path);

#if UNITY_EDITOR_OSX
                allContent = File.ReadAllText(PATH);
#else
                using (WWW reader = new WWW(path))
                {
                    yield return reader;
                    allContent = reader.text;
                }
#endif
                Diagnostic.Log("VersionInfo 加载内部文件2 " + path);

                allContent = Decrypt(allContent);

            }
            if (!string.IsNullOrEmpty(allContent))
            {
                collection = regex.Matches(allContent);
                bool has_config = default_config.Count > 0;
                if(!has_config) isEXTERNAL = false;
                for (int i = 0; i < collection.Count; ++i)
                {
                    internal_config[collection[i].Groups[1].Value] = collection[i].Groups[2].Value;
                    if(!has_config)
                        default_config[collection[i].Groups[1].Value] = collection[i].Groups[2].Value;
                }
            }

            CheckIsNewVersion();
#endif
            _initialized = true;
            Diagnostic.Log("VersionInfo 加载完成 fightVersion: " + FightLogicVersion);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public static IEnumerator Reload()
        {
            _initialized = false;
            yield return Initialize();
            // VersionConfig.Reload();
        }
#if !OUTSOURCE
        private static void CheckIsNewVersion()
        {
            string newVersion = string.Empty;
            if (IsFullVersionType())
            {
                newVersion = GetFightLogicVersion() + BundleVersion + "." + PackageVersion + " ";
            }
            else
            {
                newVersion = BundleVersion + "." + PackageVersion + " " + ResourceVersionReal + " " + ResourceVersionReal_Internal;
            }
            string oldVersion = PlayerPrefs.GetString("OldVersionStr", string.Empty);
            Diagnostic.Log("Check Is New " + newVersion + " " + oldVersion);
            if (newVersion != oldVersion)
            {
                Diagnostic.Log("Is New");
                FileManager.Instance.RenameTmpDir();
                PlayerPrefs.SetString("OldVersionStr", newVersion);
            }
        }
#endif

        /// <summary>
        /// 修改或添加配置信息，只在Editor下调用
        /// </summary>
        public static void Set(string key, string value)
        {
#if UNITY_EDITOR
            if (!_initialized)
                EditInitialize();

            default_config[key] = value;

            if (File.Exists(PATH))
            {
                File.Delete(PATH);
            }

            using (StreamWriter sw = new StreamWriter(PATH))
            {
                sw.WriteLine("[Default]");
                foreach (var pair in default_config)
                {
                    sw.WriteLine(pair.Key + " = " + pair.Value);
                }
            }
#endif
        }

        public static List<string> GetAllKey(string prefix, bool internalData = false)
        {
            if (!_initialized)
                EditInitialize();

            List<string> keys = new List<string>();
            var config = internalData ? internal_config : default_config;
            foreach (var key in config.Keys)
            {
                if (key.StartsWith(prefix))
                    keys.Add(key);
            }
            return keys;
        }

        /// <summary>
        /// 读取内容，区分包内和包外，大部分内容读取包外（尤其战斗版本号），但是app版本读取包内
        /// </summary>
        /// <param name="key"></param>
        /// <param name="external">一般优先读包外，如果包外没有，就使用包内</param>
        /// <returns></returns>
        public static string GetString(string key, bool internalData = false)
        {
            if (!_initialized)
                EditInitialize();
            var config = internalData ? internal_config : default_config;
            if (config.ContainsKey(key))
                return config[key];
            return "";
        }

        private static int GetInt(string key, bool internalData = false)
        {
            if (!_initialized)
                EditInitialize();

            int ret;
            if (int.TryParse(GetString(key, internalData), out ret))
                return ret;
            else
                return 0;
        }

        private static uint GetUInt(string key, bool internalData = false)
        {
            if (!_initialized)
                EditInitialize();

            uint ret;
            if (uint.TryParse(GetString(key, internalData), out ret))
                return ret;
            else
                return 0;
        }

        /// <summary>
        /// Editor下的初始化接口
        /// </summary>
        private static void EditInitialize()
        {
#if UNITY_EDITOR
            default_config.Clear();

            using (StreamReader sr = new StreamReader(PATH))
            {
                Regex regex = new Regex(PATTERN, RegexOptions.Multiline);
                string allContent = sr.ReadToEnd();
                allContent = Decrypt(allContent);
                MatchCollection collection = regex.Matches(allContent);
                for (int i = 0; i < collection.Count; ++i)
                {
                    default_config[collection[i].Groups[1].Value] = collection[i].Groups[2].Value;
                }
            }
#endif
        }

        /*
        //内存中的战斗版本号
        private static int FightLogicVersion_MC = 0;

        public static void SetFightLogicVersion(int version)
        {
            FightLogicVersion_MC = version;
        }
        */
#if !OUTSOURCE
        public static int GetFightLogicVersion()
        {
            // return 246552;
            //if (FightLogicVersion_MC != 0) return FightLogicVersion_MC;
            //VersionConfig配置中，是否使用svn版本号作为战斗版本号
            if (VersionConfig.GetValue("FightVersionCodeSvnSwitch", true, false))
            {
                return SVNRevNumberInt;
            }
            return FightLogicVersion;
        }
#endif

        //当前的Set资源版本
        public static int ResourceSet
        {
            get
            {
                int rs = 0;
                int.TryParse(GetString("ResourceSet"), out rs);
                return rs;
            }
        }
        
        //包含了几个Set资源（从当前版本往前数几个版本）
        public static int ResourceSetCount
        {
            get
            {
                int rsNum = 0;
                int.TryParse(GetString("ResourceSetCount"), out rsNum);
                return rsNum;
            }
        }

        /// <summary>
        /// 是否有该set资源，从1开始
        /// </summary>
        /// <param name="setIndex"></param>
        /// <returns></returns>
        public static bool HasResouceSet(int setIndex)
        {
            if (ResourceSet <= 0) return false;
            if (ResourceSetCount <= 0) return false;

            if (setIndex <= ResourceSet && setIndex >= ResourceSet - ResourceSetCount + 1)
                return true;
            return false;
        }

        private const string Encrypt_Keyword = "JkVersion:";
        //构建时，对version进行加密
#if !OUTSOURCE
        public static void Encrypt(string path = "")
        {
            if (string.IsNullOrEmpty(path))
                path = PATH;
            string allContent = File.ReadAllText(path);
            if (allContent.StartsWith(Encrypt_Keyword))
            {
                Debug.Log(path + " is already Encrypted!");
                return;
            }
            
            allContent = FileTools.Encrypt(allContent);
            allContent = Encrypt_Keyword + allContent;
            File.WriteAllText(path, allContent);
        }
#endif
        public static bool IsEncrypted(string data)
        {
            return data.StartsWith(Encrypt_Keyword);
        }
        
        public static string Decrypt(string data)
        {
#if !OUTSOURCE
            if (data.StartsWith(Encrypt_Keyword))
                data = FileTools.Decrypt(data.Substring(Encrypt_Keyword.Length));
            else
            {
                if (!Application.isEditor)
                    Debug.Log("原文件未加密，无需解密");
            }
            
#endif
            return data;
        }
#if !OUTSOURCE
       //海外标识 
        public static string SeaVersionType
        {
            get
            {
                return VersionConfig.GetValue("SeaType", "", false);
            }
        }

        
        //是否是海外版本
        public static bool IsSeaVersion()
        {
            string seaType = SeaVersionType;
            if (string.IsNullOrEmpty(seaType) || seaType == SeaType.cn.ToString())
            {
                return false;
            }

            return true;
        }
#endif


#if UNITY_EDITOR && !OUTSOURCE

        [MenuItem("Tools/VersionInfo/Version.ini->加密")]
        public static void EncryptVersionInfo()
        {
            string path = EditorUtility.OpenFilePanel("选择version.ini", PATH, "ini");
            if (!string.IsNullOrEmpty(path) && !path.EndsWith(".meta"))
            {
                if (Path.GetFileName(path) == "version.ini")
                {
                    Encrypt(path);
                    Debug.Log("加密成功");
                    return;
                }
            }

            EditorUtility.DisplayDialog("Error", "检查文件是否选择的为version.ini", "ok");
        }
        
        [MenuItem("Tools/VersionInfo/Version.ini->解密")]
        public static void DecryptVersionInfo()
        {
            string path = EditorUtility.OpenFilePanel("选择version.ini", PATH, "ini");
            if (!string.IsNullOrEmpty(path) && !path.EndsWith(".meta"))
            {
                if (Path.GetFileName(path) == "version.ini")
                {
                    string allContent = File.ReadAllText(path);
                    if (allContent.StartsWith(Encrypt_Keyword))
                    {
                        allContent = FileTools.Decrypt(allContent.Substring(Encrypt_Keyword.Length));
                        File.WriteAllText(path, allContent);
                        Debug.Log("解密后的内容：" + allContent);
                    }
                    else
                    {
                        Debug.Log("原文件未加密，无需解密 " + allContent);
                    }
                    return;
                }
            }

            EditorUtility.DisplayDialog("Error", "检查文件是否选择的为version.ini", "ok");
        }
#endif
    }

#if UNITY_EDITOR

    /// <summary>
    /// 提示更新引擎的工具类。
    /// 调用CheckEngineUpdate即可。
    /// </summary>
    public static class EngineForceUpdate
    {
        public struct EngineUpdateInfo
        {
            /// <summary>
            /// 引擎更新的日期，早于这个日期的引擎提示更新。
            /// </summary>
            internal DateTime EngineBuildDate;
            /// <summary>
            /// 容许的最晚更新日期，晚于这个日期会要求退出Unity。
            /// </summary>
            internal DateTime ForceUpdateDeadline;
            /// <summary>
            /// 版本更新消息
            /// </summary>
            internal string UpdateNote;
        }
        public static readonly EngineUpdateInfo ASTCExhaustiveSupported = new EngineUpdateInfo()
        {
            EngineBuildDate = new DateTime(2021, 7, 29),
            ForceUpdateDeadline = new DateTime(2021, 8, 2),
            UpdateNote = "为了保证能够在Editor模式下运行AB【请尽快执行引擎更新，否则无法在Editor模式下运行AB模式】"
        };

        /// <summary>
        /// 引擎的版本号，非此版本不允许运行。
        /// </summary>
        private static readonly string EngineVersion = "2018.4.25f1";
        private static volatile bool engineCheck = false;
        private static volatile bool engineValid = true;
        public static bool CheckEngineUpdate(EngineUpdateInfo updateInfo, bool forceUpdate = false)
        {
#if !OUTSOURCE
            if (!engineCheck)
            {
                engineCheck = true;
                var version = Application.unityVersion;
                var buildInfo = Application.unityBuildInfo;
                DateTime date = Convert.ToDateTime(buildInfo.Split(' ')[2]);
                Debug.Log("Unity engine version: " + version + Environment.NewLine
                          + "Unity engine build info:" + buildInfo);
                if (version != EngineVersion)
                {
                    var msg = "Unity引擎非2018.4.25f1，请用自编引擎运行。当前引擎为：" + version + "[" + date.ToShortDateString() + "]";
                    Debug.LogError(msg);
                    EditorUtility.DisplayDialog("引擎版本需要更新", msg, "关闭Unity");
                    EditorApplication.Exit(999);
                    engineValid = false;
                }
                if (date < updateInfo.EngineBuildDate)
                {
                    var msg = "引擎版本太旧，请更新引擎！！！"
                              + Environment.NewLine + "新引擎支持特性：" + updateInfo.UpdateNote + "。";
                    Debug.LogError(msg);
                    if (DateTime.Now >= updateInfo.ForceUpdateDeadline || forceUpdate)
                    {
                        EditorUtility.DisplayDialog("引擎版本需要更新", msg, "关闭Unity");
                        EditorApplication.Exit(999);
                    }
                    else
                    {
                        msg += Environment.NewLine + "请于" + updateInfo.ForceUpdateDeadline.ToShortDateString() + "之前完成更新。";
                        if (EditorUtility.DisplayDialog("引擎版本需要更新", msg, "关闭Unity",
                            "继续使用至" + updateInfo.ForceUpdateDeadline.ToShortDateString()))
                        {
                            EditorApplication.Exit(999);
                        }
                    }
                    engineValid = false;
                }
            }
#endif
            return engineValid;
        }

        private static volatile bool buildSettingCheck = false;
        private static volatile bool buildSettingValid = true;
        public static bool CheckBuildSetting()
        {
            if(!buildSettingCheck)
            {
                buildSettingCheck = true;
                var target = EditorUserBuildSettings.activeBuildTarget;
                if (target != BuildTarget.Android && target != BuildTarget.iOS)
                {
                    if (EditorUtility.DisplayDialog("构建平台有误", "当前平台不为Android或者ios", "改为Android", "改为iOS"))
                    {
                        EditorUserBuildSettings.SwitchActiveBuildTarget(BuildTargetGroup.Android, BuildTarget.Android);
                    }
                    else
                    {
                        EditorUserBuildSettings.SwitchActiveBuildTarget(BuildTargetGroup.iOS, BuildTarget.Android);
                    }
                }
                else if (target == BuildTarget.Android)
                {
                    var apis = PlayerSettings.GetGraphicsAPIs(BuildTarget.Android);
                    var vulkanCorrect = apis.Length > 0 && apis[0] == GraphicsDeviceType.Vulkan;
                    var glesCorrect = apis.Length == 2 && apis.Contains(GraphicsDeviceType.OpenGLES3) && apis.Contains(GraphicsDeviceType.OpenGLES3);
                    var apisCorrect = vulkanCorrect || glesCorrect;
                    if (EditorUserBuildSettings.androidBuildSubtarget != MobileTextureSubtarget.Generic
                        || EditorUserBuildSettings.androidETC2Fallback != AndroidETC2Fallback.Quality32Bit
                        || !apisCorrect)
                    {
                        if (EditorUtility.DisplayDialog("Build Setting有误", "当前平台为Android，但图形API或压缩格式设置有误", "帮我修改设置", "退出Unity"))
                        {
                            EditorUserBuildSettings.androidBuildSubtarget = MobileTextureSubtarget.Generic;
                            EditorUserBuildSettings.androidETC2Fallback = AndroidETC2Fallback.Quality32Bit;
                            PlayerSettings.SetGraphicsAPIs(BuildTarget.Android, new[] { GraphicsDeviceType.OpenGLES2, GraphicsDeviceType.OpenGLES3 });
                        }
                        else
                        {
                            EditorApplication.Exit(999);
                            buildSettingValid = false;
                        }
                    }
                }
                else if (target == BuildTarget.iOS)
                {
                    var apis = PlayerSettings.GetGraphicsAPIs(BuildTarget.iOS);
                    var apisCorrect = apis.Length == 2 && ((apis[0] == GraphicsDeviceType.OpenGLES2 && apis[1] == GraphicsDeviceType.Metal)
                        || (apis[0] == GraphicsDeviceType.Metal && apis[1] == GraphicsDeviceType.OpenGLES2));
                    if (!apisCorrect)
                    {
                        Debug.LogError("API 不正确，当前为数量：" + apis.Length);
                        foreach (var api in apis)
                        {
                            Debug.LogError("API ：" + api);
                        }
                        buildSettingValid = false;
                        //if (EditorUtility.DisplayDialog("Build Setting有误", "当前平台为ios，但图形API设置有误", "帮我修改设置", "退出Unity"))
                        //{
                        //    PlayerSettings.SetGraphicsAPIs(BuildTarget.iOS, new[] { GraphicsDeviceType.Metal, GraphicsDeviceType.OpenGLES2 });
                        //}
                        //else
                        //{
                        //    EditorApplication.Exit(999);
                        //}
                    }
                }
            }
            return buildSettingValid;
        }
        private static volatile bool cacheServerCheck = false;
        private static volatile bool cacheServerValid = true;
        public static bool CheckCacheServer()
        {
             if(!cacheServerCheck)
             {
                cacheServerCheck = true;
                if (!UnityEditorInternal.InternalEditorUtility.CanConnectToCacheServer())
                {
                    Debug.LogError("Check CacheServer Connect Error！");
                    if (Application.isBatchMode)
                    {
                        EditorApplication.Exit(999);
                    }
                    else
                    {
                        if (!EditorUtility.DisplayDialog("Cache Server 有误"
                            , "请检查Cache Server连接，检查是否在办公网。" +
                            "项目大量资源依赖于Cache Server数据共享，连接不上会导致大量资源被重新设置，需要大量时间刷新资源。" +
                            "美术同学找  @TA  协助解决。策划和其他同学 找 @qinjiawang（王钦佳）协助解决"
                            , "仍要继续", "退出Unity"))
                        {
                            EditorApplication.Exit(999);
                        }
                    }
                    cacheServerValid = false;
                }
                else
                {
                    Debug.Log("CacheServer Valid.");
                }
             }
             return cacheServerValid;
        }
    }

#endif
}
