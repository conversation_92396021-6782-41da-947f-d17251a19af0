// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ReportBCRoguelikeResultRsp : Wup.Jce.JceStruct
    {
        public int err = 0;

        public int gameModuleID = 0;

        public int levelID = 0;

        public bool isSucc = false;

        public int challengeDuration = 0;

        public string currentLineup = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(err, 0);
            _os.Write(gameModuleID, 1);
            _os.Write(levelID, 2);
            _os.Write(isSucc, 3);
            _os.Write(challengeDuration, 4);
            _os.Write(currentLineup, 5);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            err = (int) _is.Read(err, 0, false);

            gameModuleID = (int) _is.Read(gameModuleID, 1, false);

            levelID = (int) _is.Read(levelID, 2, false);

            isSucc = (bool) _is.Read(isSucc, 3, false);

            challengeDuration = (int) _is.Read(challengeDuration, 4, false);

            currentLineup = (string) _is.Read(currentLineup, 5, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(err, "err");
            _ds.Display(gameModuleID, "gameModuleID");
            _ds.Display(levelID, "levelID");
            _ds.Display(isSucc, "isSucc");
            _ds.Display(challengeDuration, "challengeDuration");
            _ds.Display(currentLineup, "currentLineup");
        }

        public override void Clear()
        {
            err = 0;
            gameModuleID = 0;
            levelID = 0;
            isSucc = false;
            challengeDuration = 0;
            currentLineup = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ReportBCRoguelikeResultRsp();
            copied.err = this.err;
            copied.gameModuleID = this.gameModuleID;
            copied.levelID = this.levelID;
            copied.isSucc = this.isSucc;
            copied.challengeDuration = this.challengeDuration;
            copied.currentLineup = this.currentLineup;
            return copied;
        }
    }
}

