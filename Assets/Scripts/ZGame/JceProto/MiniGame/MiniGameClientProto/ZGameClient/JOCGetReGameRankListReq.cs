// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class JOCGetReGameRankListReq : Wup.Jce.JceStruct
    {
        /// <summary>
        /// 榜单类型
        /// </summary>
        public int rankType = 0;

        /// <summary>
        /// 榜单子ID
        /// </summary>
        public int subRankID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(rankType, 0);
            _os.Write(subRankID, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            rankType = (int) _is.Read(rankType, 0, false);

            subRankID = (int) _is.Read(subRankID, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(rankType, "rankType");
            _ds.Display(subRankID, "subRankID");
        }

        public override void Clear()
        {
            rankType = 0;
            subRankID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new JOCGetReGameRankListReq();
            copied.rankType = this.rankType;
            copied.subRankID = this.subRankID;
            return copied;
        }
    }
}

