// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class CardCollectCheckRedReq : Wup.Jce.JceStruct
    {
        public int reserved = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(reserved, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            reserved = (int) _is.Read(reserved, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(reserved, "reserved");
        }

        public override void Clear()
        {
            reserved = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new CardCollectCheckRedReq();
            copied.reserved = this.reserved;
            return copied;
        }
    }
}

