// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class JOCManualLotteryHistoryRsp : Wup.Jce.JceStruct
    {
        public int ret = 0;

        public System.Collections.Generic.List<JOCManualClientLotteryRecord> lotteryHistory;

        public System.Collections.Generic.List<JOCManualClientLotteryRecord> surpriseHistory;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(ret, 0);
            _os.Write(lotteryHistory, 1);
            _os.Write(surpriseHistory, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            ret = (int) _is.Read(ret, 0, false);

            lotteryHistory = (System.Collections.Generic.List<JOCManualClientLotteryRecord>) _is.Read(lotteryHistory, 1, false);

            surpriseHistory = (System.Collections.Generic.List<JOCManualClientLotteryRecord>) _is.Read(surpriseHistory, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(ret, "ret");
            _ds.Display(lotteryHistory, "lotteryHistory");
            _ds.Display(surpriseHistory, "surpriseHistory");
        }

        public override void Clear()
        {
            ret = 0;
            lotteryHistory = null;
            surpriseHistory = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new JOCManualLotteryHistoryRsp();
            copied.ret = this.ret;
            copied.lotteryHistory = (System.Collections.Generic.List<JOCManualClientLotteryRecord>)JceUtil.DeepClone(this.lotteryHistory);
            copied.surpriseHistory = (System.Collections.Generic.List<JOCManualClientLotteryRecord>)JceUtil.DeepClone(this.surpriseHistory);
            return copied;
        }
    }
}

