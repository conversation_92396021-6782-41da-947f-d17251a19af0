// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GiftCenterSetting : Wup.Jce.JceStruct
    {
        /// <summary>
        /// 是否开启二级密码
        /// </summary>
        public bool isOpenPassword = false;

        /// <summary>
        /// 申请强制关闭二级密码等待结束时间，非0表示已提交强制申请
        /// </summary>
        public long forceClosePasswordEndTime = 0;

        /// <summary>
        /// 免密操作结束时间，表示过期时间，0表示已过期
        /// </summary>
        public long passwordFreeEndTime = 0;

        /// <summary>
        /// 是否开启免密开关，开启免密开关的情况下passwordFreeEndTime才有效
        /// </summary>
        public bool isOpenPasswordFree = false;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(isOpenPassword, 0);
            _os.Write(forceClosePasswordEndTime, 1);
            _os.Write(passwordFreeEndTime, 2);
            _os.Write(isOpenPasswordFree, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            isOpenPassword = (bool) _is.Read(isOpenPassword, 0, false);

            forceClosePasswordEndTime = (long) _is.Read(forceClosePasswordEndTime, 1, false);

            passwordFreeEndTime = (long) _is.Read(passwordFreeEndTime, 2, false);

            isOpenPasswordFree = (bool) _is.Read(isOpenPasswordFree, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(isOpenPassword, "isOpenPassword");
            _ds.Display(forceClosePasswordEndTime, "forceClosePasswordEndTime");
            _ds.Display(passwordFreeEndTime, "passwordFreeEndTime");
            _ds.Display(isOpenPasswordFree, "isOpenPasswordFree");
        }

        public override void Clear()
        {
            isOpenPassword = false;
            forceClosePasswordEndTime = 0;
            passwordFreeEndTime = 0;
            isOpenPasswordFree = false;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GiftCenterSetting();
            copied.isOpenPassword = this.isOpenPassword;
            copied.forceClosePasswordEndTime = this.forceClosePasswordEndTime;
            copied.passwordFreeEndTime = this.passwordFreeEndTime;
            copied.isOpenPasswordFree = this.isOpenPasswordFree;
            return copied;
        }
    }
}

