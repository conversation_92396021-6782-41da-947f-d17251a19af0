// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TDeviceFPS_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sDeviceModel = "";

        public int iRefreshRate = 0;

        public string sDeviceName = "";

        public string sVendor = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sDeviceModel, 1);
            _os.Write(iRefreshRate, 2);
            _os.Write(sDeviceName, 3);
            _os.Write(sVendor, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sDeviceModel = (string) _is.Read(sDeviceModel, 1, false);

            iRefreshRate = (int) _is.Read(iRefreshRate, 2, false);

            sDeviceName = (string) _is.Read(sDeviceName, 3, false);

            sVendor = (string) _is.Read(sVendor, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sDeviceModel, "sDeviceModel");
            _ds.Display(iRefreshRate, "iRefreshRate");
            _ds.Display(sDeviceName, "sDeviceName");
            _ds.Display(sVendor, "sVendor");
        }

        public override void Clear()
        {
            iID = 0;
            sDeviceModel = "";
            iRefreshRate = 0;
            sDeviceName = "";
            sVendor = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TDeviceFPS_Client();
            copied.iID = this.iID;
            copied.sDeviceModel = this.sDeviceModel;
            copied.iRefreshRate = this.iRefreshRate;
            copied.sDeviceName = this.sDeviceName;
            copied.sVendor = this.sVendor;
            return copied;
        }
    }
}

