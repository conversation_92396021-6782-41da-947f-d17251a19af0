//所在的Excel 【ACG_Match.xlsm】
//*********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TMatchRejectReason_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sContent = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sContent, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sContent = (string) _is.Read(sContent, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sContent, "sContent");
        }

        public override void Clear()
        {
            iID = 0;
            sContent = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TMatchRejectReason_Client();
            copied.iID = this.iID;
            copied.sContent = this.sContent;
            return copied;
        }
    }
}

