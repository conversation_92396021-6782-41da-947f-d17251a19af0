// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class InGameButtonDataClient : Wup.Jce.JceStruct
    {
        /// <summary>
        /// 非0表示当前使用的局内按钮ID
        /// </summary>
        public int currentID = 0;

        public TKFrame.TKDictionary<int, InGameButtonSingleClient> data;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(currentID, 0);
            _os.Write(data, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            currentID = (int) _is.Read(currentID, 0, false);

            data = (TKFrame.TKDictionary<int, InGameButtonSingleClient>) _is.Read(data, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(currentID, "currentID");
            _ds.Display(data, "data");
        }

        public override void Clear()
        {
            currentID = 0;
            data = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new InGameButtonDataClient();
            copied.currentID = this.currentID;
            copied.data = (TKFrame.TKDictionary<int, InGameButtonSingleClient>)JceUtil.DeepClone(this.data);
            return copied;
        }
    }
}

