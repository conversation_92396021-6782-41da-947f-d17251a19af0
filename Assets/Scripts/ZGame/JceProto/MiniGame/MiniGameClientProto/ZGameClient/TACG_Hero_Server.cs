// **********************************************************************
// This file was generated by a TAF parser!
// Generated from `SGameDBConfigProto.jce'
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_Hero_Server : Wup.Jce.JceStruct
    {
        int _iID = 0;
        public int iID
        {
            get
            {
                 return _iID;
            }
            set
            {
                _iID = value; 
            }
        }

        string _sName = "";
        public string sName
        {
            get
            {
                 return _sName;
            }
            set
            {
                _sName = value; 
            }
        }

        string _sAIName = "";
        public string sAIName
        {
            get
            {
                 return _sAIName;
            }
            set
            {
                _sAIName = value; 
            }
        }

        int _iDelayLeadTime = 0;
        public int iDelayLeadTime
        {
            get
            {
                 return _iDelayLeadTime;
            }
            set
            {
                _iDelayLeadTime = value; 
            }
        }

        int _iMinDelayFrame = 0;
        public int iMinDelayFrame
        {
            get
            {
                 return _iMinDelayFrame;
            }
            set
            {
                _iMinDelayFrame = value; 
            }
        }

        int _iMaxDelayFrame = 0;
        public int iMaxDelayFrame
        {
            get
            {
                 return _iMaxDelayFrame;
            }
            set
            {
                _iMaxDelayFrame = value; 
            }
        }

        int _iStar = 0;
        public int iStar
        {
            get
            {
                 return _iStar;
            }
            set
            {
                _iStar = value; 
            }
        }

        int _iQuality = 0;
        public int iQuality
        {
            get
            {
                 return _iQuality;
            }
            set
            {
                _iQuality = value; 
            }
        }

        string _sDesc = "";
        public string sDesc
        {
            get
            {
                 return _sDesc;
            }
            set
            {
                _sDesc = value; 
            }
        }

        string _sSpec = "";
        public string sSpec
        {
            get
            {
                 return _sSpec;
            }
            set
            {
                _sSpec = value; 
            }
        }

        string _sClass = "";
        public string sClass
        {
            get
            {
                 return _sClass;
            }
            set
            {
                _sClass = value; 
            }
        }

        string _sSkill = "";
        public string sSkill
        {
            get
            {
                 return _sSkill;
            }
            set
            {
                _sSkill = value; 
            }
        }

        int _iCost = 0;
        public int iCost
        {
            get
            {
                 return _iCost;
            }
            set
            {
                _iCost = value; 
            }
        }

        int _iPrice = 0;
        public int iPrice
        {
            get
            {
                 return _iPrice;
            }
            set
            {
                _iPrice = value; 
            }
        }

        string _sPrefabShowID = "";
        public string sPrefabShowID
        {
            get
            {
                 return _sPrefabShowID;
            }
            set
            {
                _sPrefabShowID = value; 
            }
        }

        string _sPrefabID = "";
        public string sPrefabID
        {
            get
            {
                 return _sPrefabID;
            }
            set
            {
                _sPrefabID = value; 
            }
        }

        int _iProperty1 = 0;
        public int iProperty1
        {
            get
            {
                 return _iProperty1;
            }
            set
            {
                _iProperty1 = value; 
            }
        }

        int _iProperty2 = 0;
        public int iProperty2
        {
            get
            {
                 return _iProperty2;
            }
            set
            {
                _iProperty2 = value; 
            }
        }

        int _iProperty3 = 0;
        public int iProperty3
        {
            get
            {
                 return _iProperty3;
            }
            set
            {
                _iProperty3 = value; 
            }
        }

        int _iProperty4 = 0;
        public int iProperty4
        {
            get
            {
                 return _iProperty4;
            }
            set
            {
                _iProperty4 = value; 
            }
        }

        int _iProperty5 = 0;
        public int iProperty5
        {
            get
            {
                 return _iProperty5;
            }
            set
            {
                _iProperty5 = value; 
            }
        }

        int _iProperty6 = 0;
        public int iProperty6
        {
            get
            {
                 return _iProperty6;
            }
            set
            {
                _iProperty6 = value; 
            }
        }

        int _iProperty7 = 0;
        public int iProperty7
        {
            get
            {
                 return _iProperty7;
            }
            set
            {
                _iProperty7 = value; 
            }
        }

        int _iProperty8 = 0;
        public int iProperty8
        {
            get
            {
                 return _iProperty8;
            }
            set
            {
                _iProperty8 = value; 
            }
        }

        int _iProperty9 = 0;
        public int iProperty9
        {
            get
            {
                 return _iProperty9;
            }
            set
            {
                _iProperty9 = value; 
            }
        }

        int _iProperty10 = 0;
        public int iProperty10
        {
            get
            {
                 return _iProperty10;
            }
            set
            {
                _iProperty10 = value; 
            }
        }

        int _iProperty11 = 0;
        public int iProperty11
        {
            get
            {
                 return _iProperty11;
            }
            set
            {
                _iProperty11 = value; 
            }
        }

        int _iProperty12 = 0;
        public int iProperty12
        {
            get
            {
                 return _iProperty12;
            }
            set
            {
                _iProperty12 = value; 
            }
        }

        int _iProperty13 = 0;
        public int iProperty13
        {
            get
            {
                 return _iProperty13;
            }
            set
            {
                _iProperty13 = value; 
            }
        }

        int _iNextHeroID = 0;
        public int iNextHeroID
        {
            get
            {
                 return _iNextHeroID;
            }
            set
            {
                _iNextHeroID = value; 
            }
        }

        int _iSpec1 = 0;
        public int iSpec1
        {
            get
            {
                 return _iSpec1;
            }
            set
            {
                _iSpec1 = value; 
            }
        }

        int _iProperty15 = 0;
        public int iProperty15
        {
            get
            {
                 return _iProperty15;
            }
            set
            {
                _iProperty15 = value; 
            }
        }

        int _iProperty16 = 0;
        public int iProperty16
        {
            get
            {
                 return _iProperty16;
            }
            set
            {
                _iProperty16 = value; 
            }
        }

        int _iProperty19 = 0;
        public int iProperty19
        {
            get
            {
                 return _iProperty19;
            }
            set
            {
                _iProperty19 = value; 
            }
        }

        int _iProperty20 = 0;
        public int iProperty20
        {
            get
            {
                 return _iProperty20;
            }
            set
            {
                _iProperty20 = value; 
            }
        }

        string _sFetterSkill = "";
        public string sFetterSkill
        {
            get
            {
                 return _sFetterSkill;
            }
            set
            {
                _sFetterSkill = value; 
            }
        }

        string _sSpecFetterSkill = "";
        public string sSpecFetterSkill
        {
            get
            {
                 return _sSpecFetterSkill;
            }
            set
            {
                _sSpecFetterSkill = value; 
            }
        }

        string _sClassFetterSkill = "";
        public string sClassFetterSkill
        {
            get
            {
                 return _sClassFetterSkill;
            }
            set
            {
                _sClassFetterSkill = value; 
            }
        }

        int _iGroup = 0;
        public int iGroup
        {
            get
            {
                 return _iGroup;
            }
            set
            {
                _iGroup = value; 
            }
        }

        int _iTimeNode = 0;
        public int iTimeNode
        {
            get
            {
                 return _iTimeNode;
            }
            set
            {
                _iTimeNode = value; 
            }
        }

        int _iHiddenSkill = 0;
        public int iHiddenSkill
        {
            get
            {
                 return _iHiddenSkill;
            }
            set
            {
                _iHiddenSkill = value; 
            }
        }

        string _shiteffect = "";
        public string shiteffect
        {
            get
            {
                 return _shiteffect;
            }
            set
            {
                _shiteffect = value; 
            }
        }

        string _sAnimationAttackPointList = "";
        public string sAnimationAttackPointList
        {
            get
            {
                 return _sAnimationAttackPointList;
            }
            set
            {
                _sAnimationAttackPointList = value; 
            }
        }

        string _sAttackIdlePoint = "";
        public string sAttackIdlePoint
        {
            get
            {
                 return _sAttackIdlePoint;
            }
            set
            {
                _sAttackIdlePoint = value; 
            }
        }

        int _ireadytimeforjump = 0;
        public int ireadytimeforjump
        {
            get
            {
                 return _ireadytimeforjump;
            }
            set
            {
                _ireadytimeforjump = value; 
            }
        }

        int _iShowherotag = 0;
        public int iShowherotag
        {
            get
            {
                 return _iShowherotag;
            }
            set
            {
                _iShowherotag = value; 
            }
        }

        string _sShifaAttackPointList = "";
        public string sShifaAttackPointList
        {
            get
            {
                 return _sShifaAttackPointList;
            }
            set
            {
                _sShifaAttackPointList = value; 
            }
        }

        int _iHeroType = 0;
        public int iHeroType
        {
            get
            {
                 return _iHeroType;
            }
            set
            {
                _iHeroType = value; 
            }
        }

        int _iTag = 0;
        public int iTag
        {
            get
            {
                 return _iTag;
            }
            set
            {
                _iTag = value; 
            }
        }

        string _sHeroPaint = "";
        public string sHeroPaint
        {
            get
            {
                 return _sHeroPaint;
            }
            set
            {
                _sHeroPaint = value; 
            }
        }

        string _sHeroPaintSmall = "";
        public string sHeroPaintSmall
        {
            get
            {
                 return _sHeroPaintSmall;
            }
            set
            {
                _sHeroPaintSmall = value; 
            }
        }

        string _sEnterBuff = "";
        public string sEnterBuff
        {
            get
            {
                 return _sEnterBuff;
            }
            set
            {
                _sEnterBuff = value; 
            }
        }

        int _iModelScale = 0;
        public int iModelScale
        {
            get
            {
                 return _iModelScale;
            }
            set
            {
                _iModelScale = value; 
            }
        }

        int _iAttachOriginID = 0;
        public int iAttachOriginID
        {
            get
            {
                 return _iAttachOriginID;
            }
            set
            {
                _iAttachOriginID = value; 
            }
        }

        int _iHeroMapTag = 0;
        public int iHeroMapTag
        {
            get
            {
                 return _iHeroMapTag;
            }
            set
            {
                _iHeroMapTag = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sName, 1);
            _os.Write(sAIName, 2);
            _os.Write(iDelayLeadTime, 3);
            _os.Write(iMinDelayFrame, 4);
            _os.Write(iMaxDelayFrame, 5);
            _os.Write(iStar, 6);
            _os.Write(iQuality, 7);
            _os.Write(sDesc, 8);
            _os.Write(sSpec, 9);
            _os.Write(sClass, 10);
            _os.Write(sSkill, 11);
            _os.Write(iCost, 12);
            _os.Write(iPrice, 13);
            _os.Write(sPrefabShowID, 14);
            _os.Write(sPrefabID, 15);
            _os.Write(iProperty1, 16);
            _os.Write(iProperty2, 17);
            _os.Write(iProperty3, 18);
            _os.Write(iProperty4, 19);
            _os.Write(iProperty5, 20);
            _os.Write(iProperty6, 21);
            _os.Write(iProperty7, 22);
            _os.Write(iProperty8, 23);
            _os.Write(iProperty9, 24);
            _os.Write(iProperty10, 25);
            _os.Write(iProperty11, 26);
            _os.Write(iProperty12, 27);
            _os.Write(iProperty13, 28);
            _os.Write(iNextHeroID, 29);
            _os.Write(iSpec1, 30);
            _os.Write(iProperty15, 31);
            _os.Write(iProperty16, 32);
            _os.Write(iProperty19, 33);
            _os.Write(iProperty20, 34);
            _os.Write(sFetterSkill, 35);
            _os.Write(sSpecFetterSkill, 36);
            _os.Write(sClassFetterSkill, 37);
            _os.Write(iGroup, 38);
            _os.Write(iTimeNode, 39);
            _os.Write(iHiddenSkill, 40);
            _os.Write(shiteffect, 41);
            _os.Write(sAnimationAttackPointList, 42);
            _os.Write(sAttackIdlePoint, 43);
            _os.Write(ireadytimeforjump, 44);
            _os.Write(iShowherotag, 45);
            _os.Write(sShifaAttackPointList, 46);
            _os.Write(iHeroType, 47);
            _os.Write(iTag, 48);
            _os.Write(sHeroPaint, 49);
            _os.Write(sHeroPaintSmall, 50);
            _os.Write(sEnterBuff, 51);
            _os.Write(iModelScale, 52);
            _os.Write(iAttachOriginID, 53);
            _os.Write(iHeroMapTag, 54);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sName = (string) _is.Read(sName, 1, false);

            sAIName = (string) _is.Read(sAIName, 2, false);

            iDelayLeadTime = (int) _is.Read(iDelayLeadTime, 3, false);

            iMinDelayFrame = (int) _is.Read(iMinDelayFrame, 4, false);

            iMaxDelayFrame = (int) _is.Read(iMaxDelayFrame, 5, false);

            iStar = (int) _is.Read(iStar, 6, false);

            iQuality = (int) _is.Read(iQuality, 7, false);

            sDesc = (string) _is.Read(sDesc, 8, false);

            sSpec = (string) _is.Read(sSpec, 9, false);

            sClass = (string) _is.Read(sClass, 10, false);

            sSkill = (string) _is.Read(sSkill, 11, false);

            iCost = (int) _is.Read(iCost, 12, false);

            iPrice = (int) _is.Read(iPrice, 13, false);

            sPrefabShowID = (string) _is.Read(sPrefabShowID, 14, false);

            sPrefabID = (string) _is.Read(sPrefabID, 15, false);

            iProperty1 = (int) _is.Read(iProperty1, 16, false);

            iProperty2 = (int) _is.Read(iProperty2, 17, false);

            iProperty3 = (int) _is.Read(iProperty3, 18, false);

            iProperty4 = (int) _is.Read(iProperty4, 19, false);

            iProperty5 = (int) _is.Read(iProperty5, 20, false);

            iProperty6 = (int) _is.Read(iProperty6, 21, false);

            iProperty7 = (int) _is.Read(iProperty7, 22, false);

            iProperty8 = (int) _is.Read(iProperty8, 23, false);

            iProperty9 = (int) _is.Read(iProperty9, 24, false);

            iProperty10 = (int) _is.Read(iProperty10, 25, false);

            iProperty11 = (int) _is.Read(iProperty11, 26, false);

            iProperty12 = (int) _is.Read(iProperty12, 27, false);

            iProperty13 = (int) _is.Read(iProperty13, 28, false);

            iNextHeroID = (int) _is.Read(iNextHeroID, 29, false);

            iSpec1 = (int) _is.Read(iSpec1, 30, false);

            iProperty15 = (int) _is.Read(iProperty15, 31, false);

            iProperty16 = (int) _is.Read(iProperty16, 32, false);

            iProperty19 = (int) _is.Read(iProperty19, 33, false);

            iProperty20 = (int) _is.Read(iProperty20, 34, false);

            sFetterSkill = (string) _is.Read(sFetterSkill, 35, false);

            sSpecFetterSkill = (string) _is.Read(sSpecFetterSkill, 36, false);

            sClassFetterSkill = (string) _is.Read(sClassFetterSkill, 37, false);

            iGroup = (int) _is.Read(iGroup, 38, false);

            iTimeNode = (int) _is.Read(iTimeNode, 39, false);

            iHiddenSkill = (int) _is.Read(iHiddenSkill, 40, false);

            shiteffect = (string) _is.Read(shiteffect, 41, false);

            sAnimationAttackPointList = (string) _is.Read(sAnimationAttackPointList, 42, false);

            sAttackIdlePoint = (string) _is.Read(sAttackIdlePoint, 43, false);

            ireadytimeforjump = (int) _is.Read(ireadytimeforjump, 44, false);

            iShowherotag = (int) _is.Read(iShowherotag, 45, false);

            sShifaAttackPointList = (string) _is.Read(sShifaAttackPointList, 46, false);

            iHeroType = (int) _is.Read(iHeroType, 47, false);

            iTag = (int) _is.Read(iTag, 48, false);

            sHeroPaint = (string) _is.Read(sHeroPaint, 49, false);

            sHeroPaintSmall = (string) _is.Read(sHeroPaintSmall, 50, false);

            sEnterBuff = (string) _is.Read(sEnterBuff, 51, false);

            iModelScale = (int) _is.Read(iModelScale, 52, false);

            iAttachOriginID = (int) _is.Read(iAttachOriginID, 53, false);

            iHeroMapTag = (int) _is.Read(iHeroMapTag, 54, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sName, "sName");
            _ds.Display(sAIName, "sAIName");
            _ds.Display(iDelayLeadTime, "iDelayLeadTime");
            _ds.Display(iMinDelayFrame, "iMinDelayFrame");
            _ds.Display(iMaxDelayFrame, "iMaxDelayFrame");
            _ds.Display(iStar, "iStar");
            _ds.Display(iQuality, "iQuality");
            _ds.Display(sDesc, "sDesc");
            _ds.Display(sSpec, "sSpec");
            _ds.Display(sClass, "sClass");
            _ds.Display(sSkill, "sSkill");
            _ds.Display(iCost, "iCost");
            _ds.Display(iPrice, "iPrice");
            _ds.Display(sPrefabShowID, "sPrefabShowID");
            _ds.Display(sPrefabID, "sPrefabID");
            _ds.Display(iProperty1, "iProperty1");
            _ds.Display(iProperty2, "iProperty2");
            _ds.Display(iProperty3, "iProperty3");
            _ds.Display(iProperty4, "iProperty4");
            _ds.Display(iProperty5, "iProperty5");
            _ds.Display(iProperty6, "iProperty6");
            _ds.Display(iProperty7, "iProperty7");
            _ds.Display(iProperty8, "iProperty8");
            _ds.Display(iProperty9, "iProperty9");
            _ds.Display(iProperty10, "iProperty10");
            _ds.Display(iProperty11, "iProperty11");
            _ds.Display(iProperty12, "iProperty12");
            _ds.Display(iProperty13, "iProperty13");
            _ds.Display(iNextHeroID, "iNextHeroID");
            _ds.Display(iSpec1, "iSpec1");
            _ds.Display(iProperty15, "iProperty15");
            _ds.Display(iProperty16, "iProperty16");
            _ds.Display(iProperty19, "iProperty19");
            _ds.Display(iProperty20, "iProperty20");
            _ds.Display(sFetterSkill, "sFetterSkill");
            _ds.Display(sSpecFetterSkill, "sSpecFetterSkill");
            _ds.Display(sClassFetterSkill, "sClassFetterSkill");
            _ds.Display(iGroup, "iGroup");
            _ds.Display(iTimeNode, "iTimeNode");
            _ds.Display(iHiddenSkill, "iHiddenSkill");
            _ds.Display(shiteffect, "shiteffect");
            _ds.Display(sAnimationAttackPointList, "sAnimationAttackPointList");
            _ds.Display(sAttackIdlePoint, "sAttackIdlePoint");
            _ds.Display(ireadytimeforjump, "ireadytimeforjump");
            _ds.Display(iShowherotag, "iShowherotag");
            _ds.Display(sShifaAttackPointList, "sShifaAttackPointList");
            _ds.Display(iHeroType, "iHeroType");
            _ds.Display(iTag, "iTag");
            _ds.Display(sHeroPaint, "sHeroPaint");
            _ds.Display(sHeroPaintSmall, "sHeroPaintSmall");
            _ds.Display(sEnterBuff, "sEnterBuff");
            _ds.Display(iModelScale, "iModelScale");
            _ds.Display(iAttachOriginID, "iAttachOriginID");
            _ds.Display(iHeroMapTag, "iHeroMapTag");
        }

    }
}

