// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 3.2.1.7 by WSRD Tencent.
// Generated from `./Common/ClientProto/JceGameMsgDef.jce'
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TCmdS2CNotifyRemoveHero : Wup.Jce.JceStruct
    {
        public int i8ChairID = 0;
        public int battleIndex = 0;
        public int iEntityID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(i8ChairID, 0);
            _os.Write(battleIndex, 1);
            _os.Write(iEntityID, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            i8ChairID = (int) _is.Read(i8ChairID, 0, false);
            battleIndex = (int)_is.Read(battleIndex, 1, false);
            iEntityID = (int)_is.Read(iEntityID, 2, false);
        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(i8ChairID, "i8ChairID");
            _ds.Display(iEntityID, "iEntityID");
            _ds.Display(battleIndex, "battleIndex");
        }

        public override void Clear()
        {
            i8ChairID = 0;
            iEntityID = 0;
            battleIndex = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_TCmdS2CNotifyRemoveHero();
            copied.i8ChairID = this.i8ChairID;
            copied.iEntityID = this.iEntityID;
            copied.battleIndex = this.battleIndex;
            return copied;
        }
    }
}

