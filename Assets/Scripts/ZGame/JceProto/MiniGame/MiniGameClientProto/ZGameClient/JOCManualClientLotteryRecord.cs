// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class JOCManualClientLotteryRecord : Wup.Jce.JceStruct
    {
        public int drawType = 0;

        public long drawTimestamp = 0;

        public System.Collections.Generic.List<TItemInfo> rewards;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(drawType, 0);
            _os.Write(drawTimestamp, 1);
            _os.Write(rewards, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            drawType = (int) _is.Read(drawType, 0, false);

            drawTimestamp = (long) _is.Read(drawTimestamp, 1, false);

            rewards = (System.Collections.Generic.List<TItemInfo>) _is.Read(rewards, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(drawType, "drawType");
            _ds.Display(drawTimestamp, "drawTimestamp");
            _ds.Display(rewards, "rewards");
        }

        public override void Clear()
        {
            drawType = 0;
            drawTimestamp = 0;
            rewards = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new JOCManualClientLotteryRecord();
            copied.drawType = this.drawType;
            copied.drawTimestamp = this.drawTimestamp;
            copied.rewards = (System.Collections.Generic.List<TItemInfo>)JceUtil.DeepClone(this.rewards);
            return copied;
        }
    }
}

