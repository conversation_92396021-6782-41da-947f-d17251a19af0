// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TCardCollectSeasonShow_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sBeginTime = "";

        public string sEndTime = "";

        public string sImageName = "";

        public string sTitle = "";

        public string sJumpParam = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sBeginTime, 1);
            _os.Write(sEndTime, 2);
            _os.Write(sImageName, 3);
            _os.Write(sTitle, 4);
            _os.Write(sJumpParam, 5);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sBeginTime = (string) _is.Read(sBeginTime, 1, false);

            sEndTime = (string) _is.Read(sEndTime, 2, false);

            sImageName = (string) _is.Read(sImageName, 3, false);

            sTitle = (string) _is.Read(sTitle, 4, false);

            sJumpParam = (string) _is.Read(sJumpParam, 5, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sBeginTime, "sBeginTime");
            _ds.Display(sEndTime, "sEndTime");
            _ds.Display(sImageName, "sImageName");
            _ds.Display(sTitle, "sTitle");
            _ds.Display(sJumpParam, "sJumpParam");
        }

        public override void Clear()
        {
            iID = 0;
            sBeginTime = "";
            sEndTime = "";
            sImageName = "";
            sTitle = "";
            sJumpParam = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TCardCollectSeasonShow_Client();
            copied.iID = this.iID;
            copied.sBeginTime = this.sBeginTime;
            copied.sEndTime = this.sEndTime;
            copied.sImageName = this.sImageName;
            copied.sTitle = this.sTitle;
            copied.sJumpParam = this.sJumpParam;
            return copied;
        }
    }
}

