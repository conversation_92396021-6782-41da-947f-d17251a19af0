// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class HonorDetail : Wup.Jce.JceStruct
    {
        public THonorSystem_Server honorSystemConfig;

        /// <summary>
        /// key: level
        /// </summary>
        public TKFrame.TKDictionary<int, HonorLevelBasicData> honorLevelMap;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(honorSystemConfig, 0);
            _os.Write(honorLevelMap, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            honorSystemConfig = (THonorSystem_Server) _is.Read(honorSystemConfig, 0, false);

            honorLevelMap = (TKFrame.TKDictionary<int, HonorLevelBasicData>) _is.Read(honorLevelMap, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(honorSystemConfig, "honorSystemConfig");
            _ds.Display(honorLevelMap, "honorLevelMap");
        }

        public override void Clear()
        {
            honorSystemConfig = null;
            honorLevelMap = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new HonorDetail();
            copied.honorSystemConfig = (THonorSystem_Server)JceUtil.DeepClone(this.honorSystemConfig);
            copied.honorLevelMap = (TKFrame.TKDictionary<int, HonorLevelBasicData>)JceUtil.DeepClone(this.honorLevelMap);
            return copied;
        }
    }
}

