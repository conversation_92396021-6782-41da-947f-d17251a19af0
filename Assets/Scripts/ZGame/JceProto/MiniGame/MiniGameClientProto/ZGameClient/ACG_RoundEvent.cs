// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_RoundEvent : Wup.Jce.JceStruct
    {
        public int iEventType = 0;

        public int iEventParam = 0;

        public int iEventParam2 = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iEventType, 0);
            _os.Write(iEventParam, 1);
            _os.Write(iEventParam2, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iEventType = (int) _is.Read(iEventType, 0, false);

            iEventParam = (int) _is.Read(iEventParam, 1, false);

            iEventParam2 = (int) _is.Read(iEventParam2, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iEventType, "iEventType");
            _ds.Display(iEventParam, "iEventParam");
            _ds.Display(iEventParam2, "iEventParam2");
        }

        public override void Clear()
        {
            iEventType = 0;
            iEventParam = 0;
            iEventParam2 = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_RoundEvent();
            copied.iEventType = this.iEventType;
            copied.iEventParam = this.iEventParam;
            copied.iEventParam2 = this.iEventParam2;
            return copied;
        }
    }
}

