//所在的Excel 【ACG_Hero.xlsm】
//**********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_HeroStoreConfig_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iGroup = 0;

        public int iWeight = 0;

        public int iHeroCfgA = 0;

        public int iEquipCfgA = 0;

        public int iQualityA = 0;

        public int iHeroCfgB = 0;

        public int iEquipCfgB = 0;

        public int iQualityB = 0;

        public int iHeroCfgC = 0;

        public int iEquipCfgC = 0;

        public int iQualityC = 0;

        public int iHeroCfgD = 0;

        public int iEquipCfgD = 0;

        public int iQualityD = 0;

        public int iCoinA = 0;

        public int iCoinB = 0;

        public int iCoinC = 0;

        public int iCoinD = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iGroup, 1);
            _os.Write(iWeight, 2);
            _os.Write(iHeroCfgA, 3);
            _os.Write(iEquipCfgA, 4);
            _os.Write(iQualityA, 5);
            _os.Write(iHeroCfgB, 6);
            _os.Write(iEquipCfgB, 7);
            _os.Write(iQualityB, 8);
            _os.Write(iHeroCfgC, 9);
            _os.Write(iEquipCfgC, 10);
            _os.Write(iQualityC, 11);
            _os.Write(iHeroCfgD, 12);
            _os.Write(iEquipCfgD, 13);
            _os.Write(iQualityD, 14);
            _os.Write(iCoinA, 15);
            _os.Write(iCoinB, 16);
            _os.Write(iCoinC, 17);
            _os.Write(iCoinD, 18);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iGroup = (int) _is.Read(iGroup, 1, false);

            iWeight = (int) _is.Read(iWeight, 2, false);

            iHeroCfgA = (int) _is.Read(iHeroCfgA, 3, false);

            iEquipCfgA = (int) _is.Read(iEquipCfgA, 4, false);

            iQualityA = (int) _is.Read(iQualityA, 5, false);

            iHeroCfgB = (int) _is.Read(iHeroCfgB, 6, false);

            iEquipCfgB = (int) _is.Read(iEquipCfgB, 7, false);

            iQualityB = (int) _is.Read(iQualityB, 8, false);

            iHeroCfgC = (int) _is.Read(iHeroCfgC, 9, false);

            iEquipCfgC = (int) _is.Read(iEquipCfgC, 10, false);

            iQualityC = (int) _is.Read(iQualityC, 11, false);

            iHeroCfgD = (int) _is.Read(iHeroCfgD, 12, false);

            iEquipCfgD = (int) _is.Read(iEquipCfgD, 13, false);

            iQualityD = (int) _is.Read(iQualityD, 14, false);

            iCoinA = (int) _is.Read(iCoinA, 15, false);

            iCoinB = (int) _is.Read(iCoinB, 16, false);

            iCoinC = (int) _is.Read(iCoinC, 17, false);

            iCoinD = (int) _is.Read(iCoinD, 18, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iGroup, "iGroup");
            _ds.Display(iWeight, "iWeight");
            _ds.Display(iHeroCfgA, "iHeroCfgA");
            _ds.Display(iEquipCfgA, "iEquipCfgA");
            _ds.Display(iQualityA, "iQualityA");
            _ds.Display(iHeroCfgB, "iHeroCfgB");
            _ds.Display(iEquipCfgB, "iEquipCfgB");
            _ds.Display(iQualityB, "iQualityB");
            _ds.Display(iHeroCfgC, "iHeroCfgC");
            _ds.Display(iEquipCfgC, "iEquipCfgC");
            _ds.Display(iQualityC, "iQualityC");
            _ds.Display(iHeroCfgD, "iHeroCfgD");
            _ds.Display(iEquipCfgD, "iEquipCfgD");
            _ds.Display(iQualityD, "iQualityD");
            _ds.Display(iCoinA, "iCoinA");
            _ds.Display(iCoinB, "iCoinB");
            _ds.Display(iCoinC, "iCoinC");
            _ds.Display(iCoinD, "iCoinD");
        }

        public override void Clear()
        {
            iID = 0;
            iGroup = 0;
            iWeight = 0;
            iHeroCfgA = 0;
            iEquipCfgA = 0;
            iQualityA = 0;
            iHeroCfgB = 0;
            iEquipCfgB = 0;
            iQualityB = 0;
            iHeroCfgC = 0;
            iEquipCfgC = 0;
            iQualityC = 0;
            iHeroCfgD = 0;
            iEquipCfgD = 0;
            iQualityD = 0;
            iCoinA = 0;
            iCoinB = 0;
            iCoinC = 0;
            iCoinD = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_HeroStoreConfig_Client();
            copied.iID = this.iID;
            copied.iGroup = this.iGroup;
            copied.iWeight = this.iWeight;
            copied.iHeroCfgA = this.iHeroCfgA;
            copied.iEquipCfgA = this.iEquipCfgA;
            copied.iQualityA = this.iQualityA;
            copied.iHeroCfgB = this.iHeroCfgB;
            copied.iEquipCfgB = this.iEquipCfgB;
            copied.iQualityB = this.iQualityB;
            copied.iHeroCfgC = this.iHeroCfgC;
            copied.iEquipCfgC = this.iEquipCfgC;
            copied.iQualityC = this.iQualityC;
            copied.iHeroCfgD = this.iHeroCfgD;
            copied.iEquipCfgD = this.iEquipCfgD;
            copied.iQualityD = this.iQualityD;
            copied.iCoinA = this.iCoinA;
            copied.iCoinB = this.iCoinB;
            copied.iCoinC = this.iCoinC;
            copied.iCoinD = this.iCoinD;
            return copied;
        }
    }
}

