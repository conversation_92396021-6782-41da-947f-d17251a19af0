// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class HeroStatisticsData : Wup.Jce.JceStruct
    {
        public System.Collections.Generic.List<int> vecEquipment;

        public int physicalDamage = 0;

        public int magicDamage = 0;

        public int realDamage = 0;

        public TAC_BattleGroundHeroPos pos;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(vecEquipment, 0);
            _os.Write(physicalDamage, 1);
            _os.Write(magicDamage, 2);
            _os.Write(realDamage, 3);
            _os.Write(pos, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            vecEquipment = (System.Collections.Generic.List<int>) _is.Read(vecEquipment, 0, false);

            physicalDamage = (int) _is.Read(physicalDamage, 1, false);

            magicDamage = (int) _is.Read(magicDamage, 2, false);

            realDamage = (int) _is.Read(realDamage, 3, false);

            pos = (TAC_BattleGroundHeroPos) _is.Read(pos, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(vecEquipment, "vecEquipment");
            _ds.Display(physicalDamage, "physicalDamage");
            _ds.Display(magicDamage, "magicDamage");
            _ds.Display(realDamage, "realDamage");
            _ds.Display(pos, "pos");
        }

        public override void Clear()
        {
            vecEquipment = null;
            physicalDamage = 0;
            magicDamage = 0;
            realDamage = 0;
            pos = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new HeroStatisticsData();
            copied.vecEquipment = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.vecEquipment);
            copied.physicalDamage = this.physicalDamage;
            copied.magicDamage = this.magicDamage;
            copied.realDamage = this.realDamage;
            copied.pos = (TAC_BattleGroundHeroPos)JceUtil.DeepClone(this.pos);
            return copied;
        }
    }
}

