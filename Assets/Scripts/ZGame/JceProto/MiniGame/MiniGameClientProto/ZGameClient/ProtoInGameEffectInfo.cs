// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ProtoInGameEffectInfo : Wup.Jce.JceStruct
    {
        int _iType = 0;
        public int iType
        {
            get
            {
                 return _iType;
            }
            set
            {
                _iType = value; 
            }
        }

        int _iValue = 0;
        public int iValue
        {
            get
            {
                 return _iValue;
            }
            set
            {
                _iValue = value; 
            }
        }

        int _iHeroGroup = 0;
        public int iHeroGroup
        {
            get
            {
                 return _iHeroGroup;
            }
            set
            {
                _iHeroGroup = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iType, 0);
            _os.Write(iValue, 1);
            _os.Write(iHeroGroup, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iType = (int) _is.Read(iType, 0, false);

            iValue = (int) _is.Read(iValue, 1, false);

            iHeroGroup = (int) _is.Read(iHeroGroup, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iType, "iType");
            _ds.Display(iValue, "iValue");
            _ds.Display(iHeroGroup, "iHeroGroup");
        }

        public override void Clear()
        {
            iType = 0;
            iValue = 0;
            iHeroGroup = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ProtoInGameEffectInfo();
            copied.iType = this.iType;
            copied.iValue = this.iValue;
            copied.iHeroGroup = this.iHeroGroup;
            return copied;
        }
    }
}

