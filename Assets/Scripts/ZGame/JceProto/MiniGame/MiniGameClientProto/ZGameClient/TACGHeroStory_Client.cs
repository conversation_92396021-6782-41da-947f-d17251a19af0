//所在的Excel 【ACG_HeroStory.xlsm】
//*****************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACGHeroStory_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sName2 = "";

        public string sRecommendEquips = "";

        public string sStory = "";

        public string sShowTurnDesc = "";

        public string sPoster = "";

        public string sPosterMovie = "";

        public int iAnchorVerticalType = 0;

        public int iAnchorHorizontalType = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sName2, 1);
            _os.Write(sRecommendEquips, 2);
            _os.Write(sStory, 3);
            _os.Write(sShowTurnDesc, 4);
            _os.Write(sPoster, 5);
            _os.Write(sPosterMovie, 6);
            _os.Write(iAnchorVerticalType, 7);
            _os.Write(iAnchorHorizontalType, 8);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sName2 = (string) _is.Read(sName2, 1, false);

            sRecommendEquips = (string) _is.Read(sRecommendEquips, 2, false);

            sStory = (string) _is.Read(sStory, 3, false);

            sShowTurnDesc = (string) _is.Read(sShowTurnDesc, 4, false);

            sPoster = (string) _is.Read(sPoster, 5, false);

            sPosterMovie = (string) _is.Read(sPosterMovie, 6, false);

            iAnchorVerticalType = (int) _is.Read(iAnchorVerticalType, 7, false);

            iAnchorHorizontalType = (int) _is.Read(iAnchorHorizontalType, 8, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sName2, "sName2");
            _ds.Display(sRecommendEquips, "sRecommendEquips");
            _ds.Display(sStory, "sStory");
            _ds.Display(sShowTurnDesc, "sShowTurnDesc");
            _ds.Display(sPoster, "sPoster");
            _ds.Display(sPosterMovie, "sPosterMovie");
            _ds.Display(iAnchorVerticalType, "iAnchorVerticalType");
            _ds.Display(iAnchorHorizontalType, "iAnchorHorizontalType");
        }

        public override void Clear()
        {
            iID = 0;
            sName2 = "";
            sRecommendEquips = "";
            sStory = "";
            sShowTurnDesc = "";
            sPoster = "";
            sPosterMovie = "";
            iAnchorVerticalType = 0;
            iAnchorHorizontalType = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACGHeroStory_Client();
            copied.iID = this.iID;
            copied.sName2 = this.sName2;
            copied.sRecommendEquips = this.sRecommendEquips;
            copied.sStory = this.sStory;
            copied.sShowTurnDesc = this.sShowTurnDesc;
            copied.sPoster = this.sPoster;
            copied.sPosterMovie = this.sPosterMovie;
            copied.iAnchorVerticalType = this.iAnchorVerticalType;
            copied.iAnchorHorizontalType = this.iAnchorHorizontalType;
            return copied;
        }
    }
}

