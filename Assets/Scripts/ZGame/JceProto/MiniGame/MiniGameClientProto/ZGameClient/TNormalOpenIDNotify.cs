// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TNormalOpenIDNotify : Wup.Jce.JceStruct
    {
        public string sNormalOpenID = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(sNormalOpenID, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            sNormalOpenID = (string) _is.Read(sNormalOpenID, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(sNormalOpenID, "sNormalOpenID");
        }

        public override void Clear()
        {
            sNormalOpenID = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TNormalOpenIDNotify();
            copied.sNormalOpenID = this.sNormalOpenID;
            return copied;
        }
    }
}

