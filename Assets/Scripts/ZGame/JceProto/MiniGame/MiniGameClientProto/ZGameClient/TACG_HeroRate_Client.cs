//所在的Excel 【ACG_Hero.xlsm】
//m】
//***************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_HeroRate_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iHeroRate1 = 0;

        public int iHeroRate2 = 0;

        public int iHeroRate3 = 0;

        public int iHeroRate4 = 0;

        public int iHeroRate5 = 0;

        public string sHeroNum1 = "";

        public string sHeroNum2 = "";

        public string sHeroNum3 = "";

        public string sHeroNum4 = "";

        public string sHeroNum5 = "";

        public int iTableID = 0;

        public int iPlanID = 0;

        public string sHeroStar1 = "";

        public string sHeroStar2 = "";

        public string sHeroStar3 = "";

        public string sHeroStar4 = "";

        public string sHeroStar5 = "";

        public int iExtraRule = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iHeroRate1, 1);
            _os.Write(iHeroRate2, 2);
            _os.Write(iHeroRate3, 3);
            _os.Write(iHeroRate4, 4);
            _os.Write(iHeroRate5, 5);
            _os.Write(sHeroNum1, 6);
            _os.Write(sHeroNum2, 7);
            _os.Write(sHeroNum3, 8);
            _os.Write(sHeroNum4, 9);
            _os.Write(sHeroNum5, 10);
            _os.Write(iTableID, 11);
            _os.Write(iPlanID, 12);
            _os.Write(sHeroStar1, 13);
            _os.Write(sHeroStar2, 14);
            _os.Write(sHeroStar3, 15);
            _os.Write(sHeroStar4, 16);
            _os.Write(sHeroStar5, 17);
            _os.Write(iExtraRule, 18);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iHeroRate1 = (int) _is.Read(iHeroRate1, 1, false);

            iHeroRate2 = (int) _is.Read(iHeroRate2, 2, false);

            iHeroRate3 = (int) _is.Read(iHeroRate3, 3, false);

            iHeroRate4 = (int) _is.Read(iHeroRate4, 4, false);

            iHeroRate5 = (int) _is.Read(iHeroRate5, 5, false);

            sHeroNum1 = (string) _is.Read(sHeroNum1, 6, false);

            sHeroNum2 = (string) _is.Read(sHeroNum2, 7, false);

            sHeroNum3 = (string) _is.Read(sHeroNum3, 8, false);

            sHeroNum4 = (string) _is.Read(sHeroNum4, 9, false);

            sHeroNum5 = (string) _is.Read(sHeroNum5, 10, false);

            iTableID = (int) _is.Read(iTableID, 11, false);

            iPlanID = (int) _is.Read(iPlanID, 12, false);

            sHeroStar1 = (string) _is.Read(sHeroStar1, 13, false);

            sHeroStar2 = (string) _is.Read(sHeroStar2, 14, false);

            sHeroStar3 = (string) _is.Read(sHeroStar3, 15, false);

            sHeroStar4 = (string) _is.Read(sHeroStar4, 16, false);

            sHeroStar5 = (string) _is.Read(sHeroStar5, 17, false);

            iExtraRule = (int) _is.Read(iExtraRule, 18, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iHeroRate1, "iHeroRate1");
            _ds.Display(iHeroRate2, "iHeroRate2");
            _ds.Display(iHeroRate3, "iHeroRate3");
            _ds.Display(iHeroRate4, "iHeroRate4");
            _ds.Display(iHeroRate5, "iHeroRate5");
            _ds.Display(sHeroNum1, "sHeroNum1");
            _ds.Display(sHeroNum2, "sHeroNum2");
            _ds.Display(sHeroNum3, "sHeroNum3");
            _ds.Display(sHeroNum4, "sHeroNum4");
            _ds.Display(sHeroNum5, "sHeroNum5");
            _ds.Display(iTableID, "iTableID");
            _ds.Display(iPlanID, "iPlanID");
            _ds.Display(sHeroStar1, "sHeroStar1");
            _ds.Display(sHeroStar2, "sHeroStar2");
            _ds.Display(sHeroStar3, "sHeroStar3");
            _ds.Display(sHeroStar4, "sHeroStar4");
            _ds.Display(sHeroStar5, "sHeroStar5");
            _ds.Display(iExtraRule, "iExtraRule");
        }

        public override void Clear()
        {
            iID = 0;
            iHeroRate1 = 0;
            iHeroRate2 = 0;
            iHeroRate3 = 0;
            iHeroRate4 = 0;
            iHeroRate5 = 0;
            sHeroNum1 = "";
            sHeroNum2 = "";
            sHeroNum3 = "";
            sHeroNum4 = "";
            sHeroNum5 = "";
            iTableID = 0;
            iPlanID = 0;
            sHeroStar1 = "";
            sHeroStar2 = "";
            sHeroStar3 = "";
            sHeroStar4 = "";
            sHeroStar5 = "";
            iExtraRule = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_HeroRate_Client();
            copied.iID = this.iID;
            copied.iHeroRate1 = this.iHeroRate1;
            copied.iHeroRate2 = this.iHeroRate2;
            copied.iHeroRate3 = this.iHeroRate3;
            copied.iHeroRate4 = this.iHeroRate4;
            copied.iHeroRate5 = this.iHeroRate5;
            copied.sHeroNum1 = this.sHeroNum1;
            copied.sHeroNum2 = this.sHeroNum2;
            copied.sHeroNum3 = this.sHeroNum3;
            copied.sHeroNum4 = this.sHeroNum4;
            copied.sHeroNum5 = this.sHeroNum5;
            copied.iTableID = this.iTableID;
            copied.iPlanID = this.iPlanID;
            copied.sHeroStar1 = this.sHeroStar1;
            copied.sHeroStar2 = this.sHeroStar2;
            copied.sHeroStar3 = this.sHeroStar3;
            copied.sHeroStar4 = this.sHeroStar4;
            copied.sHeroStar5 = this.sHeroStar5;
            copied.iExtraRule = this.iExtraRule;
            return copied;
        }
    }
}

