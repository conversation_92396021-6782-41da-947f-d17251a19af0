//所在的Excel 【ACG_ElementTerrain.xlsm】
//************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_ElementTerrain_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iEquipment = 0;

        public string sIcon = "";

        public string sRoundSelectRes = "";

        public string sBirthRes = "";

        public string sStayRes = "";

        public string sSurroundRes = "";

        public string sAppearTurnCount = "";

        public string sHeroGroupId = "";

        public int iSpecId = 0;

        public string sElmentCenter = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iEquipment, 1);
            _os.Write(sIcon, 2);
            _os.Write(sRoundSelectRes, 3);
            _os.Write(sBirthRes, 4);
            _os.Write(sStayRes, 5);
            _os.Write(sSurroundRes, 6);
            _os.Write(sAppearTurnCount, 7);
            _os.Write(sHeroGroupId, 8);
            _os.Write(iSpecId, 9);
            _os.Write(sElmentCenter, 10);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iEquipment = (int) _is.Read(iEquipment, 1, false);

            sIcon = (string) _is.Read(sIcon, 2, false);

            sRoundSelectRes = (string) _is.Read(sRoundSelectRes, 3, false);

            sBirthRes = (string) _is.Read(sBirthRes, 4, false);

            sStayRes = (string) _is.Read(sStayRes, 5, false);

            sSurroundRes = (string) _is.Read(sSurroundRes, 6, false);

            sAppearTurnCount = (string) _is.Read(sAppearTurnCount, 7, false);

            sHeroGroupId = (string) _is.Read(sHeroGroupId, 8, false);

            iSpecId = (int) _is.Read(iSpecId, 9, false);

            sElmentCenter = (string) _is.Read(sElmentCenter, 10, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iEquipment, "iEquipment");
            _ds.Display(sIcon, "sIcon");
            _ds.Display(sRoundSelectRes, "sRoundSelectRes");
            _ds.Display(sBirthRes, "sBirthRes");
            _ds.Display(sStayRes, "sStayRes");
            _ds.Display(sSurroundRes, "sSurroundRes");
            _ds.Display(sAppearTurnCount, "sAppearTurnCount");
            _ds.Display(sHeroGroupId, "sHeroGroupId");
            _ds.Display(iSpecId, "iSpecId");
            _ds.Display(sElmentCenter, "sElmentCenter");
        }

        public override void Clear()
        {
            iID = 0;
            iEquipment = 0;
            sIcon = "";
            sRoundSelectRes = "";
            sBirthRes = "";
            sStayRes = "";
            sSurroundRes = "";
            sAppearTurnCount = "";
            sHeroGroupId = "";
            iSpecId = 0;
            sElmentCenter = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_ElementTerrain_Client();
            copied.iID = this.iID;
            copied.iEquipment = this.iEquipment;
            copied.sIcon = this.sIcon;
            copied.sRoundSelectRes = this.sRoundSelectRes;
            copied.sBirthRes = this.sBirthRes;
            copied.sStayRes = this.sStayRes;
            copied.sSurroundRes = this.sSurroundRes;
            copied.sAppearTurnCount = this.sAppearTurnCount;
            copied.sHeroGroupId = this.sHeroGroupId;
            copied.iSpecId = this.iSpecId;
            copied.sElmentCenter = this.sElmentCenter;
            return copied;
        }
    }
}

