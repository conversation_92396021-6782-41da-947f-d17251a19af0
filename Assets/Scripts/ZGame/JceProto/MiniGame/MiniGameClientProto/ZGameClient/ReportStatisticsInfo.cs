// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ReportStatisticsInfo : Wup.Jce.JceStruct
    {
        public int iOpenPlatType = 0;

        public string sLog = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iOpenPlatType, 0);
            _os.Write(sLog, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iOpenPlatType = (int) _is.Read(iOpenPlatType, 0, false);

            sLog = (string) _is.Read(sLog, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iOpenPlatType, "iOpenPlatType");
            _ds.Display(sLog, "sLog");
        }

        public override void Clear()
        {
            iOpenPlatType = 0;
            sLog = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ReportStatisticsInfo();
            copied.iOpenPlatType = this.iOpenPlatType;
            copied.sLog = this.sLog;
            return copied;
        }
    }
}

