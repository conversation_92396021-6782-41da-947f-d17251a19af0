// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_BattleSimpleInfo : Wup.Jce.JceStruct
    {
        int _iPlayerID = 0;
        public int iPlayerID
        {
            get
            {
                 return _iPlayerID;
            }
            set
            {
                _iPlayerID = value; 
            }
        }

        int _iEnemyPlayerID = 0;
        public int iEnemyPlayerID
        {
            get
            {
                 return _iEnemyPlayerID;
            }
            set
            {
                _iEnemyPlayerID = value; 
            }
        }

        int _iWinnerID = 0;
        public int iWinnerID
        {
            get
            {
                 return _iWinnerID;
            }
            set
            {
                _iWinnerID = value; 
            }
        }

        int _iBossNPCHeroID = 0;
        public int iBossNPCHeroID
        {
            get
            {
                 return _iBossNPCHeroID;
            }
            set
            {
                _iBossNPCHeroID = value; 
            }
        }

        int _iLastFrameID = 0;
        public int iLastFrameID
        {
            get
            {
                 return _iLastFrameID;
            }
            set
            {
                _iLastFrameID = value; 
            }
        }

        public TAC_BattleResult stBattleResult {get; set;} 

        public TAC_AllFetterSkillInfo stAllFetterSkillInfo {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iPlayerID, 0);
            _os.Write(iEnemyPlayerID, 1);
            _os.Write(iWinnerID, 2);
            _os.Write(iBossNPCHeroID, 3);
            _os.Write(iLastFrameID, 4);
            _os.Write(stBattleResult, 5);
            _os.Write(stAllFetterSkillInfo, 6);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iPlayerID = (int) _is.Read(iPlayerID, 0, false);

            iEnemyPlayerID = (int) _is.Read(iEnemyPlayerID, 1, false);

            iWinnerID = (int) _is.Read(iWinnerID, 2, false);

            iBossNPCHeroID = (int) _is.Read(iBossNPCHeroID, 3, false);

            iLastFrameID = (int) _is.Read(iLastFrameID, 4, false);

            stBattleResult = (TAC_BattleResult) _is.Read(stBattleResult, 5, false);

            stAllFetterSkillInfo = (TAC_AllFetterSkillInfo) _is.Read(stAllFetterSkillInfo, 6, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iPlayerID, "iPlayerID");
            _ds.Display(iEnemyPlayerID, "iEnemyPlayerID");
            _ds.Display(iWinnerID, "iWinnerID");
            _ds.Display(iBossNPCHeroID, "iBossNPCHeroID");
            _ds.Display(iLastFrameID, "iLastFrameID");
            _ds.Display(stBattleResult, "stBattleResult");
            _ds.Display(stAllFetterSkillInfo, "stAllFetterSkillInfo");
        }

        public override void Clear()
        {
            iPlayerID = 0;
            iEnemyPlayerID = 0;
            iWinnerID = 0;
            iBossNPCHeroID = 0;
            iLastFrameID = 0;
            stBattleResult = null;
            stAllFetterSkillInfo = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_BattleSimpleInfo();
            copied.iPlayerID = this.iPlayerID;
            copied.iEnemyPlayerID = this.iEnemyPlayerID;
            copied.iWinnerID = this.iWinnerID;
            copied.iBossNPCHeroID = this.iBossNPCHeroID;
            copied.iLastFrameID = this.iLastFrameID;
            copied.stBattleResult = (TAC_BattleResult)JceUtil.DeepClone(this.stBattleResult);
            copied.stAllFetterSkillInfo = (TAC_AllFetterSkillInfo)JceUtil.DeepClone(this.stAllFetterSkillInfo);
            return copied;
        }
    }
}

