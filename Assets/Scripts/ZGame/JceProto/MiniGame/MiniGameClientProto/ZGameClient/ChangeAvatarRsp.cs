// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ChangeAvatarRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public int id = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(id, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            id = (int) _is.Read(id, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(id, "id");
        }

        public override void Clear()
        {
            iRet = 0;
            id = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ChangeAvatarRsp();
            copied.iRet = this.iRet;
            copied.id = this.id;
            return copied;
        }
    }
}

