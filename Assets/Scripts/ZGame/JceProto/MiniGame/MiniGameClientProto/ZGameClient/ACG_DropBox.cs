// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_DropBox : Wup.Jce.JceStruct
    {
        int _turnCount = 0;
        public int turnCount
        {
            get
            {
                 return _turnCount;
            }
            set
            {
                _turnCount = value; 
            }
        }

        int _itemID = 0;
        public int itemID
        {
            get
            {
                 return _itemID;
            }
            set
            {
                _itemID = value; 
            }
        }

        int _iNum = 0;
        public int iNum
        {
            get
            {
                 return _iNum;
            }
            set
            {
                _iNum = value; 
            }
        }

        public System.Collections.Generic.List<int> vecEquipList {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(turnCount, 0);
            _os.Write(itemID, 1);
            _os.Write(iNum, 2);
            _os.Write(vecEquipList, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            turnCount = (int) _is.Read(turnCount, 0, false);

            itemID = (int) _is.Read(itemID, 1, false);

            iNum = (int) _is.Read(iNum, 2, false);

            vecEquipList = (System.Collections.Generic.List<int>) _is.Read(vecEquipList, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(turnCount, "turnCount");
            _ds.Display(itemID, "itemID");
            _ds.Display(iNum, "iNum");
            _ds.Display(vecEquipList, "vecEquipList");
        }

        public override void Clear()
        {
            turnCount = 0;
            itemID = 0;
            iNum = 0;
            vecEquipList = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_DropBox();
            copied.turnCount = this.turnCount;
            copied.itemID = this.itemID;
            copied.iNum = this.iNum;
            copied.vecEquipList = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.vecEquipList);
            return copied;
        }
    }
}

