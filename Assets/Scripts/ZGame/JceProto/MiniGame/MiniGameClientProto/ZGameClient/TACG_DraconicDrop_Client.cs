//所在的Excel 【ACG_Drop.xlsm】
//**********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_DraconicDrop_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iEggLevel = 0;

        public int iEggTurn = 0;

        public string sDropParams = "";

        public int iWeight = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iEggLevel, 1);
            _os.Write(iEggTurn, 2);
            _os.Write(sDropParams, 3);
            _os.Write(iWeight, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iEggLevel = (int) _is.Read(iEggLevel, 1, false);

            iEggTurn = (int) _is.Read(iEggTurn, 2, false);

            sDropParams = (string) _is.Read(sDropParams, 3, false);

            iWeight = (int) _is.Read(iWeight, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iEggLevel, "iEggLevel");
            _ds.Display(iEggTurn, "iEggTurn");
            _ds.Display(sDropParams, "sDropParams");
            _ds.Display(iWeight, "iWeight");
        }

        public override void Clear()
        {
            iID = 0;
            iEggLevel = 0;
            iEggTurn = 0;
            sDropParams = "";
            iWeight = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_DraconicDrop_Client();
            copied.iID = this.iID;
            copied.iEggLevel = this.iEggLevel;
            copied.iEggTurn = this.iEggTurn;
            copied.sDropParams = this.sDropParams;
            copied.iWeight = this.iWeight;
            return copied;
        }
    }
}

