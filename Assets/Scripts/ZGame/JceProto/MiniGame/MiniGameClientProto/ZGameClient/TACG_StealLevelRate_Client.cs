//所在的Excel 【ACG_Equipment.xlsm】
//*****************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_StealLevelRate_Client : Wup.Jce.JceStruct
    {
        public int iLevel = 0;

        public int iStealLevelRate1 = 0;

        public int iStealLevelRate2 = 0;

        public int iStealLevelRate3 = 0;

        public int iStealLevelRate4 = 0;

        public int iID = 0;

        public int iPlanID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iLevel, 0);
            _os.Write(iStealLevelRate1, 1);
            _os.Write(iStealLevelRate2, 2);
            _os.Write(iStealLevelRate3, 3);
            _os.Write(iStealLevelRate4, 4);
            _os.Write(iID, 5);
            _os.Write(iPlanID, 7);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iLevel = (int) _is.Read(iLevel, 0, false);

            iStealLevelRate1 = (int) _is.Read(iStealLevelRate1, 1, false);

            iStealLevelRate2 = (int) _is.Read(iStealLevelRate2, 2, false);

            iStealLevelRate3 = (int) _is.Read(iStealLevelRate3, 3, false);

            iStealLevelRate4 = (int) _is.Read(iStealLevelRate4, 4, false);

            iID = (int) _is.Read(iID, 5, false);

            iPlanID = (int) _is.Read(iPlanID, 7, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iLevel, "iLevel");
            _ds.Display(iStealLevelRate1, "iStealLevelRate1");
            _ds.Display(iStealLevelRate2, "iStealLevelRate2");
            _ds.Display(iStealLevelRate3, "iStealLevelRate3");
            _ds.Display(iStealLevelRate4, "iStealLevelRate4");
            _ds.Display(iID, "iID");
            _ds.Display(iPlanID, "iPlanID");
        }

        public override void Clear()
        {
            iLevel = 0;
            iStealLevelRate1 = 0;
            iStealLevelRate2 = 0;
            iStealLevelRate3 = 0;
            iStealLevelRate4 = 0;
            iID = 0;
            iPlanID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_StealLevelRate_Client();
            copied.iLevel = this.iLevel;
            copied.iStealLevelRate1 = this.iStealLevelRate1;
            copied.iStealLevelRate2 = this.iStealLevelRate2;
            copied.iStealLevelRate3 = this.iStealLevelRate3;
            copied.iStealLevelRate4 = this.iStealLevelRate4;
            copied.iID = this.iID;
            copied.iPlanID = this.iPlanID;
            return copied;
        }
    }
}

