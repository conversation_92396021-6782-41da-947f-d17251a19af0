// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GuildFeedsRoomMember : Wup.Jce.JceStruct
    {
        public TUserID uid;

        public int tier = 0;

        public THeadIconID headIconID;

        public int iconBoxID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(uid, 0);
            _os.Write(tier, 1);
            _os.Write(headIconID, 2);
            _os.Write(iconBoxID, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            uid = (TUserID) _is.Read(uid, 0, false);

            tier = (int) _is.Read(tier, 1, false);

            headIconID = (THeadIconID) _is.Read(headIconID, 2, false);

            iconBoxID = (int) _is.Read(iconBoxID, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(uid, "uid");
            _ds.Display(tier, "tier");
            _ds.Display(headIconID, "headIconID");
            _ds.Display(iconBoxID, "iconBoxID");
        }

        public override void Clear()
        {
            uid = null;
            tier = 0;
            headIconID = null;
            iconBoxID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GuildFeedsRoomMember();
            copied.uid = (TUserID)JceUtil.DeepClone(this.uid);
            copied.tier = this.tier;
            copied.headIconID = (THeadIconID)JceUtil.DeepClone(this.headIconID);
            copied.iconBoxID = this.iconBoxID;
            return copied;
        }
    }
}

