// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TMatchInviteWithoutRoomRsp : Wup.Jce.JceStruct
    {
        public TPlayerId stLauncher;

        public TPlayerId stBeInvited;

        public int iRet = 0;

        public int iLeftBanSeconds = 0;

        public TClientForbidInfo stForbidInfo;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(st<PERSON>auncher, 0);
            _os.Write(stBeInvited, 1);
            _os.Write(iRet, 2);
            _os.Write(iLeftBanSeconds, 3);
            _os.Write(stForbidInfo, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            stLauncher = (TPlayerId) _is.Read(stLauncher, 0, false);

            stBeInvited = (TPlayerId) _is.Read(stBeInvited, 1, false);

            iRet = (int) _is.Read(iRet, 2, false);

            iLeftBanSeconds = (int) _is.Read(iLeftBanSeconds, 3, false);

            stForbidInfo = (TClientForbidInfo) _is.Read(stForbidInfo, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(stLauncher, "stLauncher");
            _ds.Display(stBeInvited, "stBeInvited");
            _ds.Display(iRet, "iRet");
            _ds.Display(iLeftBanSeconds, "iLeftBanSeconds");
            _ds.Display(stForbidInfo, "stForbidInfo");
        }

        public override void Clear()
        {
            stLauncher = null;
            stBeInvited = null;
            iRet = 0;
            iLeftBanSeconds = 0;
            stForbidInfo = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TMatchInviteWithoutRoomRsp();
            copied.stLauncher = (TPlayerId)JceUtil.DeepClone(this.stLauncher);
            copied.stBeInvited = (TPlayerId)JceUtil.DeepClone(this.stBeInvited);
            copied.iRet = this.iRet;
            copied.iLeftBanSeconds = this.iLeftBanSeconds;
            copied.stForbidInfo = (TClientForbidInfo)JceUtil.DeepClone(this.stForbidInfo);
            return copied;
        }
    }
}

