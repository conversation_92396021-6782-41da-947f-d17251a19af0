// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class BattleHeroInfo : Wup.Jce.JceStruct
    {
        public int iHeroID = 0;

        public System.Collections.Generic.List<int> vecEquipment;

        public int iHeroConfigID = 0;

        public TAC_BattleGroundHeroPos pos;

        public int iGroupID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iHeroID, 0);
            _os.Write(vecEquipment, 1);
            _os.Write(iHeroConfigID, 2);
            _os.Write(pos, 3);
            _os.Write(iGroupID, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iHeroID = (int) _is.Read(iHeroID, 0, false);

            vecEquipment = (System.Collections.Generic.List<int>) _is.Read(vecEquipment, 1, false);

            iHeroConfigID = (int) _is.Read(iHeroConfigID, 2, false);

            pos = (TAC_BattleGroundHeroPos) _is.Read(pos, 3, false);

            iGroupID = (int) _is.Read(iGroupID, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iHeroID, "iHeroID");
            _ds.Display(vecEquipment, "vecEquipment");
            _ds.Display(iHeroConfigID, "iHeroConfigID");
            _ds.Display(pos, "pos");
            _ds.Display(iGroupID, "iGroupID");
        }

        public override void Clear()
        {
            iHeroID = 0;
            vecEquipment = null;
            iHeroConfigID = 0;
            pos = null;
            iGroupID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new BattleHeroInfo();
            copied.iHeroID = this.iHeroID;
            copied.vecEquipment = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.vecEquipment);
            copied.iHeroConfigID = this.iHeroConfigID;
            copied.pos = (TAC_BattleGroundHeroPos)JceUtil.DeepClone(this.pos);
            copied.iGroupID = this.iGroupID;
            return copied;
        }
    }
}

