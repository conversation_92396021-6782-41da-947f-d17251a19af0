// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GuildChatRecord : Wup.Jce.JceStruct
    {
        public TUserID uid;

        public string talkText = "";

        public long id = 0;

        public GuildExtraUserInfoForClient extraInfo;

        public SendAudio audioData;

        public string subText = "";

        public int chatBubbleID = 0;

        public int iContentType = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(uid, 0);
            _os.Write(talkText, 2);
            _os.Write(id, 3);
            _os.Write(extraInfo, 4);
            _os.Write(audioData, 5);
            _os.Write(subText, 6);
            _os.Write(chatBubbleID, 7);
            _os.Write(iContentType, 8);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            uid = (TUserID) _is.Read(uid, 0, false);

            talkText = (string) _is.Read(talkText, 2, false);

            id = (long) _is.Read(id, 3, false);

            extraInfo = (GuildExtraUserInfoForClient) _is.Read(extraInfo, 4, false);

            audioData = (SendAudio) _is.Read(audioData, 5, false);

            subText = (string) _is.Read(subText, 6, false);

            chatBubbleID = (int) _is.Read(chatBubbleID, 7, false);

            iContentType = (int) _is.Read(iContentType, 8, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(uid, "uid");
            _ds.Display(talkText, "talkText");
            _ds.Display(id, "id");
            _ds.Display(extraInfo, "extraInfo");
            _ds.Display(audioData, "audioData");
            _ds.Display(subText, "subText");
            _ds.Display(chatBubbleID, "chatBubbleID");
            _ds.Display(iContentType, "iContentType");
        }

        public override void Clear()
        {
            uid = null;
            talkText = "";
            id = 0;
            extraInfo = null;
            audioData = null;
            subText = "";
            chatBubbleID = 0;
            iContentType = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GuildChatRecord();
            copied.uid = (TUserID)JceUtil.DeepClone(this.uid);
            copied.talkText = this.talkText;
            copied.id = this.id;
            copied.extraInfo = (GuildExtraUserInfoForClient)JceUtil.DeepClone(this.extraInfo);
            copied.audioData = (SendAudio)JceUtil.DeepClone(this.audioData);
            copied.subText = this.subText;
            copied.chatBubbleID = this.chatBubbleID;
            copied.iContentType = this.iContentType;
            return copied;
        }
    }
}

