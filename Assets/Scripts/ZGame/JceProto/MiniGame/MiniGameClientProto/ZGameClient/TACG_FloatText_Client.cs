// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_FloatText_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iItemID = 0;

        public string sName = "";

        public int iType = 0;

        public string sABPath = "";

        public string sAssetName = "";

        public int iIsAdd = 0;

        public int iIsTween = 0;

        public int iIsHide = 0;

        public int iIsOverlay = 0;

        public int iTweenDuration = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iItemID, 1);
            _os.Write(sName, 2);
            _os.Write(iType, 3);
            _os.Write(sABPath, 4);
            _os.Write(sAssetName, 5);
            _os.Write(iIsAdd, 6);
            _os.Write(iIsTween, 7);
            _os.Write(iIsHide, 8);
            _os.Write(iIsOverlay, 9);
            _os.Write(iTweenDuration, 10);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iItemID = (int) _is.Read(iItemID, 1, false);

            sName = (string) _is.Read(sName, 2, false);

            iType = (int) _is.Read(iType, 3, false);

            sABPath = (string) _is.Read(sABPath, 4, false);

            sAssetName = (string) _is.Read(sAssetName, 5, false);

            iIsAdd = (int) _is.Read(iIsAdd, 6, false);

            iIsTween = (int) _is.Read(iIsTween, 7, false);

            iIsHide = (int) _is.Read(iIsHide, 8, false);

            iIsOverlay = (int) _is.Read(iIsOverlay, 9, false);

            iTweenDuration = (int) _is.Read(iTweenDuration, 10, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iItemID, "iItemID");
            _ds.Display(sName, "sName");
            _ds.Display(iType, "iType");
            _ds.Display(sABPath, "sABPath");
            _ds.Display(sAssetName, "sAssetName");
            _ds.Display(iIsAdd, "iIsAdd");
            _ds.Display(iIsTween, "iIsTween");
            _ds.Display(iIsHide, "iIsHide");
            _ds.Display(iIsOverlay, "iIsOverlay");
            _ds.Display(iTweenDuration, "iTweenDuration");
        }

        public override void Clear()
        {
            iID = 0;
            iItemID = 0;
            sName = "";
            iType = 0;
            sABPath = "";
            sAssetName = "";
            iIsAdd = 0;
            iIsTween = 0;
            iIsHide = 0;
            iIsOverlay = 0;
            iTweenDuration = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_FloatText_Client();
            copied.iID = this.iID;
            copied.iItemID = this.iItemID;
            copied.sName = this.sName;
            copied.iType = this.iType;
            copied.sABPath = this.sABPath;
            copied.sAssetName = this.sAssetName;
            copied.iIsAdd = this.iIsAdd;
            copied.iIsTween = this.iIsTween;
            copied.iIsHide = this.iIsHide;
            copied.iIsOverlay = this.iIsOverlay;
            copied.iTweenDuration = this.iTweenDuration;
            return copied;
        }
    }
}

