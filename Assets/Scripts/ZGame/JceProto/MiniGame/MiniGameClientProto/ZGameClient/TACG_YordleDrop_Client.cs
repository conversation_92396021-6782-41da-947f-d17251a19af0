// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_YordleDrop_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iLevel = 0;

        public string sHeroPool = "";

        public string sEquipment = "";

        public int iWeight = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iLevel, 1);
            _os.Write(sHeroPool, 2);
            _os.Write(sEquipment, 3);
            _os.Write(iWeight, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iLevel = (int) _is.Read(iLevel, 1, false);

            sHeroPool = (string) _is.Read(sHeroPool, 2, false);

            sEquipment = (string) _is.Read(sEquipment, 3, false);

            iWeight = (int) _is.Read(iWeight, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iLevel, "iLevel");
            _ds.Display(sHeroPool, "sHeroPool");
            _ds.Display(sEquipment, "sEquipment");
            _ds.Display(iWeight, "iWeight");
        }

        public override void Clear()
        {
            iID = 0;
            iLevel = 0;
            sHeroPool = "";
            sEquipment = "";
            iWeight = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_YordleDrop_Client();
            copied.iID = this.iID;
            copied.iLevel = this.iLevel;
            copied.sHeroPool = this.sHeroPool;
            copied.sEquipment = this.sEquipment;
            copied.iWeight = this.iWeight;
            return copied;
        }
    }
}

