// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TPackageRule_Client : Wup.Jce.JceStruct
    {
        /// <summary>
        /// ID
        /// </summary>
        public int iID = 0;

        /// <summary>
        /// 资源类型
        /// </summary>
        public int iResourceType = 0;

        /// <summary>
        /// ID段
        /// </summary>
        public string sItemRange = "";

        /// <summary>
        /// 道具资源类型
        /// </summary>
        public int iItemResType = 0;

        /// <summary>
        /// 赛季ID
        /// </summary>
        public int iSetID = 0;

        /// <summary>
        /// 资源匹配规则
        /// </summary>
        public string sResourceMatchRule = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iResourceType, 1);
            _os.Write(sItemRange, 2);
            _os.Write(iItemResType, 3);
            _os.Write(iSetID, 8);
            _os.Write(sResourceMatchRule, 9);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int)_is.Read(iID, 0, false);

            iResourceType = (int)_is.Read(iResourceType, 1, false);

            sItemRange = (string)_is.Read(sItemRange, 2, false);

            iItemResType = (int)_is.Read(iItemResType, 3, false);

            iSetID = (int)_is.Read(iSetID, 8, false);

            sResourceMatchRule = (string)_is.Read(sResourceMatchRule, 9, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iResourceType, "iResourceType");
            _ds.Display(sItemRange, "sItemRange");
            _ds.Display(iItemResType, "iItemResType");
            _ds.Display(iSetID, "iSetID");
            _ds.Display(sResourceMatchRule, "sResourceMatchRule");
        }

        public override void Clear()
        {
            iID = 0;
            iResourceType = 0;
            sItemRange = "";
            iItemResType = 0;
            iSetID = 0;
            sResourceMatchRule = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TPackageRule_Client();
            copied.iID = this.iID;
            copied.iResourceType = this.iResourceType;
            copied.sItemRange = this.sItemRange;
            copied.iItemResType = this.iItemResType;
            copied.iSetID = this.iSetID;
            copied.sResourceMatchRule = this.sResourceMatchRule;
            return copied;
        }
    }
}

