// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ShareRoomMessageReq : Wup.Jce.JceStruct
    {
        public string password = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(password, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            password = (string) _is.Read(password, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(password, "password");
        }

        public override void Clear()
        {
            password = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ShareRoomMessageReq();
            copied.password = this.password;
            return copied;
        }
    }
}

