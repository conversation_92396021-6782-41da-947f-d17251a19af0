//所在的Excel 【ACG_NoviceInfo.xlsm】
//****************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_NoviceReward_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sItemId = "";

        public string sItemNum = "";

        public string sIcon = "";

        public int iRewardType = 0;

        public int iParentId = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sItemId, 1);
            _os.Write(sItemNum, 2);
            _os.Write(sIcon, 3);
            _os.Write(iRewardType, 4);
            _os.Write(iParentId, 5);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sItemId = (string) _is.Read(sItemId, 1, false);

            sItemNum = (string) _is.Read(sItemNum, 2, false);

            sIcon = (string) _is.Read(sIcon, 3, false);

            iRewardType = (int) _is.Read(iRewardType, 4, false);

            iParentId = (int) _is.Read(iParentId, 5, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sItemId, "sItemId");
            _ds.Display(sItemNum, "sItemNum");
            _ds.Display(sIcon, "sIcon");
            _ds.Display(iRewardType, "iRewardType");
            _ds.Display(iParentId, "iParentId");
        }

        public override void Clear()
        {
            iID = 0;
            sItemId = "";
            sItemNum = "";
            sIcon = "";
            iRewardType = 0;
            iParentId = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_NoviceReward_Client();
            copied.iID = this.iID;
            copied.sItemId = this.sItemId;
            copied.sItemNum = this.sItemNum;
            copied.sIcon = this.sIcon;
            copied.iRewardType = this.iRewardType;
            copied.iParentId = this.iParentId;
            return copied;
        }
    }
}

