// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

	[Serializable]
    public sealed class ACG_DropPosInfo : Wup.Jce.JceStruct
    {
        int _iSrcCol = 0;
        public int iSrcCol
        {
            get
            {
                 return _iSrcCol;
            }
            set
            {
                _iSrcCol = value; 
            }
        }

        int _iSrcRow = 0;
        public int iSrcRow
        {
            get
            {
                 return _iSrcRow;
            }
            set
            {
                _iSrcRow = value; 
            }
        }

        int _iXPercent = 0;
        public int iXPercent
        {
            get
            {
                 return _iXPercent;
            }
            set
            {
                _iXPercent = value; 
            }
        }

        int _iYPercent = 0;
        public int iYPercent
        {
            get
            {
                 return _iYPercent;
            }
            set
            {
                _iYPercent = value; 
            }
        }

        int _iCoinDegree = 0;
        public int iCoinDegree
        {
            get
            {
                 return _iCoinDegree;
            }
            set
            {
                _iCoinDegree = value; 
            }
        }

        int _dropCount = 0;
        public int dropCount
        {
            get
            {
                 return _dropCount;
            }
            set
            {
                _dropCount = value; 
            }
        }

        int _iEntityID = 0;
        public int iEntityID
        {
            get
            {
                 return _iEntityID;
            }
            set
            {
                _iEntityID = value; 
            }
        }

        int _iCoinSource = 0;
        public int iCoinSource
        {
            get
            {
                 return _iCoinSource;
            }
            set
            {
                _iCoinSource = value; 
            }
        }

        int _iDropType = 0;
        public int iDropType
        {
            get
            {
                 return _iDropType;
            }
            set
            {
                _iDropType = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iSrcCol, 0);
            _os.Write(iSrcRow, 1);
            _os.Write(iXPercent, 2);
            _os.Write(iYPercent, 3);
            _os.Write(iCoinDegree, 4);
            _os.Write(dropCount, 5);
            _os.Write(iEntityID, 6);
            _os.Write(iCoinSource, 7);
            _os.Write(iDropType, 8);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iSrcCol = (int) _is.Read(iSrcCol, 0, false);

            iSrcRow = (int) _is.Read(iSrcRow, 1, false);

            iXPercent = (int) _is.Read(iXPercent, 2, false);

            iYPercent = (int) _is.Read(iYPercent, 3, false);

            iCoinDegree = (int) _is.Read(iCoinDegree, 4, false);

            dropCount = (int) _is.Read(dropCount, 5, false);

            iEntityID = (int) _is.Read(iEntityID, 6, false);

            iCoinSource = (int) _is.Read(iCoinSource, 7, false);

            iDropType = (int) _is.Read(iDropType, 8, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iSrcCol, "iSrcCol");
            _ds.Display(iSrcRow, "iSrcRow");
            _ds.Display(iXPercent, "iXPercent");
            _ds.Display(iYPercent, "iYPercent");
            _ds.Display(iCoinDegree, "iCoinDegree");
            _ds.Display(dropCount, "dropCount");
            _ds.Display(iEntityID, "iEntityID");
            _ds.Display(iCoinSource, "iCoinSource");
            _ds.Display(iDropType, "iDropType");
        }

        public override void Clear()
        {
            iSrcCol = 0;
            iSrcRow = 0;
            iXPercent = 0;
            iYPercent = 0;
            iCoinDegree = 0;
            dropCount = 0;
            iEntityID = 0;
            iCoinSource = 0;
            iDropType = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_DropPosInfo();
            copied.iSrcCol = this.iSrcCol;
            copied.iSrcRow = this.iSrcRow;
            copied.iXPercent = this.iXPercent;
            copied.iYPercent = this.iYPercent;
            copied.iCoinDegree = this.iCoinDegree;
            copied.dropCount = this.dropCount;
            copied.iEntityID = this.iEntityID;
            copied.iCoinSource = this.iCoinSource;
            copied.iDropType = this.iDropType;
            return copied;
        }
    }
}

