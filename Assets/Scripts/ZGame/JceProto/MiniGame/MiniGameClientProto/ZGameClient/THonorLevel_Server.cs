// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class THonorLevel_Server : Wup.Jce.JceStruct
    {
        /// <summary>
        /// ID
        /// </summary>
        public int iID = 0;

        /// <summary>
        /// 等级
        /// </summary>
        public int iLevel = 0;

        /// <summary>
        /// 达到等级需要的荣誉点
        /// </summary>
        public int iHonorPoint = 0;

        /// <summary>
        /// 是否允许资源分享
        /// </summary>
        public int iAllowShare = 0;

        /// <summary>
        /// 可允许分享次数
        /// </summary>
        public int iacceptShareTimes = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iLevel, 1);
            _os.Write(iHonorPoint, 2);
            _os.Write(iAllowShare, 3);
            _os.Write(iacceptShareTimes, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iLevel = (int) _is.Read(iLevel, 1, false);

            iHonorPoint = (int) _is.Read(iHonorPoint, 2, false);

            iAllowShare = (int) _is.Read(iAllowShare, 3, false);

            iacceptShareTimes = (int) _is.Read(iacceptShareTimes, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iLevel, "iLevel");
            _ds.Display(iHonorPoint, "iHonorPoint");
            _ds.Display(iAllowShare, "iAllowShare");
            _ds.Display(iacceptShareTimes, "iacceptShareTimes");
        }

        public override void Clear()
        {
            iID = 0;
            iLevel = 0;
            iHonorPoint = 0;
            iAllowShare = 0;
            iacceptShareTimes = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new THonorLevel_Server();
            copied.iID = this.iID;
            copied.iLevel = this.iLevel;
            copied.iHonorPoint = this.iHonorPoint;
            copied.iAllowShare = this.iAllowShare;
            copied.iacceptShareTimes = this.iacceptShareTimes;
            return copied;
        }
    }
}

