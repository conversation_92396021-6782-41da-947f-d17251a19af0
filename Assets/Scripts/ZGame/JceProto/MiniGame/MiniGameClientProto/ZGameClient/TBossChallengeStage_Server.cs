// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TBossChallengeStage_Server : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sName = "";

        public string sUnlockTime = "";

        public int iUnlockTime = 0;

        public string sUnlockTips = "";

        public string sLineUpIDs = "";

        public int iGameModuleID = 0;

        public string sTitleDesc = "";

        public string sBriefDesc = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sName, 1);
            _os.Write(sUnlockTime, 2);
            _os.Write(iUnlockTime, 3);
            _os.Write(sUnlockTips, 4);
            _os.Write(sLineUpIDs, 5);
            _os.Write(iGameModuleID, 6);
            _os.Write(sTitleDesc, 7);
            _os.Write(sBriefDesc, 8);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sName = (string) _is.Read(sName, 1, false);

            sUnlockTime = (string) _is.Read(sUnlockTime, 2, false);

            iUnlockTime = (int) _is.Read(iUnlockTime, 3, false);

            sUnlockTips = (string) _is.Read(sUnlockTips, 4, false);

            sLineUpIDs = (string) _is.Read(sLineUpIDs, 5, false);

            iGameModuleID = (int) _is.Read(iGameModuleID, 6, false);

            sTitleDesc = (string) _is.Read(sTitleDesc, 7, false);

            sBriefDesc = (string) _is.Read(sBriefDesc, 8, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sName, "sName");
            _ds.Display(sUnlockTime, "sUnlockTime");
            _ds.Display(iUnlockTime, "iUnlockTime");
            _ds.Display(sUnlockTips, "sUnlockTips");
            _ds.Display(sLineUpIDs, "sLineUpIDs");
            _ds.Display(iGameModuleID, "iGameModuleID");
            _ds.Display(sTitleDesc, "sTitleDesc");
            _ds.Display(sBriefDesc, "sBriefDesc");
        }

        public override void Clear()
        {
            iID = 0;
            sName = "";
            sUnlockTime = "";
            iUnlockTime = 0;
            sUnlockTips = "";
            sLineUpIDs = "";
            iGameModuleID = 0;
            sTitleDesc = "";
            sBriefDesc = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TBossChallengeStage_Server();
            copied.iID = this.iID;
            copied.sName = this.sName;
            copied.sUnlockTime = this.sUnlockTime;
            copied.iUnlockTime = this.iUnlockTime;
            copied.sUnlockTips = this.sUnlockTips;
            copied.sLineUpIDs = this.sLineUpIDs;
            copied.iGameModuleID = this.iGameModuleID;
            copied.sTitleDesc = this.sTitleDesc;
            copied.sBriefDesc = this.sBriefDesc;
            return copied;
        }
    }
}

