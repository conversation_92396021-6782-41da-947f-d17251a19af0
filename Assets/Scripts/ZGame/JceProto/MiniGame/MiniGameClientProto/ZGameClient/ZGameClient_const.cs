// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{
    public class Const 
    {
        public const int FRIEND_RANK_INIT_COUNT = 15;
        public const int FRIEND_RANK_PAGE_COUNT_MAX = 20;
        public const int FRIEND_RECENT_PLAYER_MAX = 20;
        public const int HEAD_ICON_MAX_NUM = 20;
        public const int FRIEND_NEARBY_COUNT_MAX = 20;
        public const int FRIEND_STATE_COUNT_MAX = 20;
        public const int TAC_RANK_TYPE_BASE = 1000000;
        public const int ZONE_RANK_PAGE_COUNT_MAX = 20;
        public const int TAC_DROP_WEIGHT_BASE = 1000000;
        public const int TAC_Tiny_InitiativeExpressionNum = 6;
        public const int TAC_Tiny_PassiveExpressionType_Num = 6;
        public const int TAC_TINY_DEFAULT_ID = 100001;
        public const int TAC_TINY_EXPRESSION_DEFAULT_ID = 200000;
        public const int TAC_TINY_MAP_DEFAULT_ID = 300001;
        public const int TAC_TINY_DAMAGE_DEFAULT_ID = 700001;
        public const int DROP_ACTIVITY_PROBABILITY_BASE = 10000;
        public const int MIDAS_RMB_TO_COUPON_RATE = 100;
        public const int MIDAS_QDot_TO_COUPON_RATE = 10;
        public const int SEC_BAN_RANK_FOR_ALL_TYPE = 99;
    }
    public enum ESceneType
    {
        SceneType_ZGame_PVP_AutoFight_RELAX_SINGLE = 25,
        SceneType_ZGame_PVP_AutoFight_MMR_RANK = 31,
        SceneType_ZGame_PVE_AutoFight_PLAYER_VS_AI = 32,
        SceneType_ZGame_PVE_AutoFight_BOUNTY = 33,
        SceneType_ZGame_PVE_AutoFight_CUSTOM = 34,
        SceneType_ZGame_PVE_AutoFight_100_PLAYER = 35,
        SceneType_ZGame_PVP_AutoFight_DUAL_PLAY = 36,
        SceneType_ZGame_PVP_AutoFight_TURBO = 37,
        SceneType_ZGame_PVP_AutoFight_BOUNTY_NORMAL = 38,
        SceneType_ZGame_PVP_AutoFight_BOUNTY_TURBO = 39,
        SceneType_ZGame_PVP_AutoFight_SET_X = 40,
        SceneType_ZGame_PVP_AutoFight_EQUIPMENT_CARNIVAL = 41,
        SceneType_ZGame_PVP_AutoFight_LINE_UP_CHALLENGE = 42,
        SceneType_ZGame_PVP_AutoFight_RELAX_SINGLE_MINOR = 43,
        SceneType_ZGame_PVP_AutoFight_MMR_RANK_MINOR = 44,
        SceneType_ZGame_PVE_AutoFight_NOVICE_GUIDE = 45,
        SceneType_ZGame_PVE_AutoFight_NINE_BOSS = 46,
        SceneType_ZGame_PVP_AutoFight_RELAX_SET_505 = 47,
        SceneType_ZGame_PVP_AutoFight_RELAX_SET_4 = 48,
        SceneType_ZGame_PVP_AutoFight_DUAL_PLAY_MMR = 49,
        SceneType_ZGame_PVP_AutoFight_CONTEST_RANDOM = 50,
        SceneType_ZGame_PVP_AutoFight_CONTEST_MMR_RANK = 51,
        SceneType_ZGame_PVP_AutoFight_THE_CHOSEN_CHARACTER = 52,
        SceneType_ZGame_PVE_AutoFight_BOSS_CHALLENGE_ROGUELIKE = 53,
        SceneType_ZGame_PVP_AutoFight_NEW_DUAL_PLAY_MMR = 54,
        SceneType_ZGame_PVP_AutoFight_NEW_DUAL_PLAY_RELAX = 55,
        SceneType_ZGame_PVP_AutoFight_TEAM_4V4_RELAX = 56,
        SceneType_ZGame_PVP_AutoFight_TEAM_4V4_MMR = 57,
        SceneType_ZGame_PVE_AutoFight_BOSS_CHALLENGE_PVE = 58,
        SceneType_ZGame_PVP_AutoFight_FORTUNE_FAVOR = 59,
        SceneType_ZGame_PVP_AutoFight_FIRST_QUALIFIER = 60,
        SceneType_ZGame_PVP_AutoFight_SECOND_QUALIFIER = 61,
        SceneType_ZGame_PVP_AutoFight_JOC_RANK = 62,
        SceneType_ZGame_PVP_AutoFight_MAJOR_OB = 63,
        SceneType_ZGame_PVP_AutoFight_Mode_64 = 64,
        SceneType_ZGame_PVP_AutoFight_Mode_65 = 65,
        SceneType_ZGame_PVP_AutoFight_Mode_66 = 66,
        SceneType_ZGame_PVP_AutoFight_Mode_67 = 67,
        SceneType_ZGame_PVP_AutoFight_Mode_68 = 68,
        SceneType_ZGame_PVP_AutoFight_Mode_69 = 69,
        SceneType_ZGame_PVP_AutoFight_Mode_70 = 70,
        SceneType_ZGame_PVP_AutoFight_Mode_71 = 71,
        SceneType_ZGame_PVP_AutoFight_Mode_72 = 72,

        SceneType_ZGame_Limit = 100,
        SceneType_ZGame_PVP_AutoFight_TURBO1 = 137,
        SceneType_ZGame_PVP_ONLY_RUN_CLINET = 777,
        SceneType_ZGame_PVP_NO_MATCH_AI = 888,
        SceneType_ZGame_PVP_Special_Unknown_Mode_BEGIN = 950,
        SceneType_ZGame_PVP_Special_Unknown_Mode = 999,
        SceneType_TimeLimit_Relax_Begin = 1001,
        SceneType_TimeLimit_Relax_Galaxy = 1002,
        SceneType_TimeLimit_Relax_Dual_Galaxy = 1202,
        SceneType_TimeLimit_Relax_End = 1999,
        SceneType_TimeLimit_MMR_Begin = 2001,
        SceneType_TimeLimit_MMR_Galaxy = 2002,
        SceneType_TimeLimit_MMR_Dual_Galaxy = 2202,
        SceneType_TimeLimit_MMR_End = 2999,
        SceneType_Contest_MMR_Begin = 100001,
        SceneType_Contest_MMR_End = 199999,
        SceneType_Contest_Random_Begin = 200001,
        SceneType_Contest_Random_End = 299999,
        SceneType_ZGame_Collecte_Achivement = 300001,
        SceneType_ZGame_Magic_Expression_Celebrity = 300002,
        SceneType_ZGame_Magic_Expression_Popularity = 300003,
        SceneType_COUNT,
    }
    public enum E_TAC_PLAY_MATCH_TYPE
    {
        E_TAC_PLAY_MATCH_TYPE_NOMAL = 0,
        E_TAC_PLAY_MATCH_TYPE_FRIEND = 1,
        E_TAC_PLAY_MATCH_TYPE_FRIEND_TEAM = 2,
        E_TAC_PLAY_MATCH_TYPE_MANY_PEOPLE = 3,
        E_TAC_PLAY_MATCH_TYPE_TEAM_SHARELIFE = 4,
        E_TAC_PLAY_MATCH_TYPE_RANKING_SINGLE = 5,
        E_TAC_PLAY_MATCH_TYPE_RANKING_TEAM = 6,
        E_TAC_PLAY_MATCH_TYPE_NEW = 7,
        E_TAC_PLAY_MATCH_TYPE_CUSTOM = 8,
        E_TAC_PLAY_MATCH_TYPE_100_PLAYERS = 9,
        E_TAC_PLAY_MATCH_TYPE_DUAL_PLAY = 10,
        E_TAC_PLAY_MATCH_TYPE_TURBO = 11,
        E_TAC_PLAY_MATCH_TYPE_TURBO1 = 111,
        E_TAC_PLAY_MATCH_TYPE_Special_Unknown_Mode = 999,
    }
    public enum KICK_REASON
    {
        KICK_UPGRADE = 0,
        KICK_MULTILOGIN = 1,
        KICK_DULL = 2,
        KICK_BY_PLAYER = 4,
        KICK_STOP_SVC = 5,
        KICK_BY_ADMIN = 6,
        KICK_MIDAS_TOKEN_EXPIRED = 7,
        KICK_CHECK_ACCESS_TOKEN_FAILED = 8,
        KICK_FORBID_USER_LOGIN = 100,
        KICK_UIN_OTHER = 134,
    }
    public enum SITDOWN_MODE
    {
        SITMODE_NOMAL = 1,
        SITMODE_REPLAY = 2,
        SITMODE_SWITCHTABLE = 3,
    }
    public enum E_TABLE_STATUS
    {
        ST_TABLE_FREE = 0,
        ST_TABLE_INGAME = 1,
    }
    public enum E_GAME_TYPE
    {
        E_GAME_TYPE_PVP = 0,
        E_GAME_TYPE_PVE = 1,
    }
    public enum E_OPEN_PLAT_TYPE
    {
        E_OPEN_PLAT_TYPE_WECHAT = 1,
        E_OPEN_PLAT_TYPE_QQ = 2,
        E_OPEN_PLAT_TYPE_GUEST = 3,
    }
    public enum E_MOBILE_PLAT_TYPE
    {
        E_MOBILE_PLAT_TYPE_IOS = 0,
        E_MOBILE_PLAT_TYPE_ANDROID = 1,
    }
    public enum E_MSG_TYPE
    {
        E_MSG_TYPE_REQUEST = 0,
        E_MSG_TYPE_RESPONSE,
        E_MSG_TYPE_NOTIFY,
    }
    public enum E_SVR_MSG_ID
    {
        E_SVR_MSG_ID_HELLO = 1,
        E_SVR_MSG_ID_LOGIN = 2,
        E_SVR_MSG_ID_LOGOUT = 3,
        E_SVR_MSG_ID_RELOGIN = 4,
        E_SVR_MSG_ID_KICKEDOUT = 5,
        E_SVR_MSG_ID_REGISTER = 6,
        E_SVR_MSG_ID_PUSH_MSG_HEAD_PARAM_ERROR = 7,
        E_SVR_MSG_ID_PUSH_INVALID_OPENID = 8,
        E_SVR_MSG_ID_BEFORE_LOGIN_HELLO = 9,
        E_SVR_MSG_ID_OFFSET = 10,
        E_SVR_MSG_ID_NEW_VERSION_NOTIFY = 11,
        E_SVR_MSG_ID_MOD_USERINFO = 30,
        E_SVR_MSG_ID_RELOADCFG = 38,
        E_SVR_MSG_ID_NOTIFY_USERSTATE = 46,
        E_SVR_MSG_ID_GET_USERSTATE = 47,
        E_SVR_MSG_ID_GET_LAST_GAME_OVER = 48,
        E_SVR_MSG_ID_LOCK_STEP_SECOND_RESULT = 49,
        E_SVR_MSG_ID_GAMESVR_TOO_BUSY = 103,
        E_SVR_MSG_ID_NOTIFY_LEVEL_UPGRADE = 157,
        E_SVR_MSG_ID_REPORT_START_GAME_FAILED = 160,
        E_SVR_MSG_ID_RELOAD_LOG_OPENID = 213,
        E_SVR_MSG_ID_NOTIFY_MATCH_EXIT_PLAYER = 220,
        E_SVR_MSG_ID_NOTIFY_MATCH_ADD_PLAYER = 221,
        E_SVR_MSG_ID_NOTIFY_MATCH_ADD_PLAYER_REPLAY = 222,
        E_SVR_MSG_ID_AUTO_CHESS_NOTIFY_MATCH = 223,
        E_SVR_MSG_ID_AUTO_CHESS_NOTIFY_PROGRESSMATCH = 224,
        E_SVR_MSG_ID_NOTIFY_DEBUG_INFO_100PLAYER = 225,
        E_SVR_MSG_ID_TAC_GET_MAIL_LIST = 226,
        E_SVR_MSG_ID_TAC_SET_MAIL_READ_FLAG = 227,
        E_SVR_MSG_ID_TAC_FETCH_MAIL_ITEM = 228,
        E_SVR_MSG_ID_TAC_DEL_MAIL = 229,
        E_SVR_MSG_ID_TAC_FETCH_ALL_MAIL_ITEM = 231,
        E_SVR_MSG_ID_NOTIFY_NEW_MAIL = 232,
        E_SVR_MSG_ID_TAC_UPDATE_MAIL_ADDRESS = 233,
        E_SVR_MSG_ID_TAC_INVITE_FRIEND_ONLINE = 252,
        E_SVR_MSG_ID_SEC_MODIFY_LINEUP_CONTENTS = 259,
        E_SVR_MSG_ID_SEC_BAN = 260,
        E_SVR_MSG_ID_SEC_AUDIO_STATE = 261,
        E_SVR_MSG_ID_SEC_AUDIO = 262,
        E_SVR_MSG_ID_SEC_CLEAR_POST = 263,
        E_SVR_MSG_ID_SEC_UPDATE_USER_SESSION = 264,
        E_SVR_MSG_ID_SEC_UNBAN_RANKING = 265,
        E_SVR_MSG_ID_SEC_BAN_RANKING = 266,
        E_SVR_MSG_ID_SEC_RENAME = 267,
        E_SVR_MSG_ID_FORBID_RENAME = 268,
        E_SVR_MSG_ID_UNDO_FORBID_RENAME = 269,
        E_SVR_MSG_ID_FORBID_USER_LOGIN = 270,
        E_SVR_MSG_ID_UNDO_FORBID_USER_LOGIN = 271,
        E_SVR_MSG_ID_IDIP_MODIFY_ITEM = 272,
        E_SVR_MSG_ID_SEND_BAN_HEAD_URL_MAIL = 273,
        E_SVR_MSG_ID_FORBID_USER_POST = 274,
        E_SVR_MSG_ID_UNDO_FORBID_USER_POST = 275,
        E_SVR_MSG_ID_FORBID_ADD_FRIEND = 276,
        E_SVR_MSG_ID_UNDO_ADD_FRIEND = 277,
        E_SVR_MSG_ID_SILENCE = 278,
        E_SVR_MSG_ID_UNDO_SILENCE = 279,
        E_SVR_MSG_ID_TAC_REPORT_TASKREPORT_EVENT = 280,
        E_SVR_MSG_ID_TAC_REPORT_USER_SHARE = 281,
        E_SVR_MSG_ID_TAC_REPORT_USER_VIEW = 282,
        E_SVR_MSG_ID_TAC_SEGMENT_RATING_INFO = 290,
        E_SVR_MSG_ID_LOGIN_ANNOUNCEMENT_NOTIFY = 292,
        E_SVR_MSG_ID_PANDORA_NOTIFY = 294,
        E_SVR_MSG_ID_GET_ACTIVITY_PANEL = 295,
        E_SVR_MSG_ID_GET_ADD_PLAYER_REJECT_COUNT = 296,
        E_SVR_MSG_ID_PUSH_NORMAL_OPENID = 297,
        E_SVR_MSG_ID_PUSH_ZK_INSTRUCTION = 299,
        E_SVR_MSG_ID_GET_EVENTS = 300,
        E_SVR_MSG_ID_GET_MODULE_CONTROL_CONF = 310,
        E_SVR_MSG_ID_GET_SEASON_INFO = 400,
        E_SVR_MSG_ID_GET_SEASON_TASK_INFO = 403,
        E_SVR_MSG_ID_GET_SEASON_TASK_REWARD = 404,
        E_SVR_MSG_ID_NOTIFY_REFRESH_SEASON = 406,
        E_SVR_MSG_ID_NOTIFY_CLIENT_VER_IFNO = 407,
        E_SVR_MSG_ID_GET_ALL_RANK_HISTORY = 408,
        E_SVR_MSG_ID_GET_OWNER_TIER_INFO = 409,
        E_SVR_MSG_ID_GET_TURBO_LP_INFO = 410,
        E_SVR_MSG_ID_GET_TURBO_TASK_INFO = 411,
        E_SVR_MSG_ID_GET_TURBO_TASK_REWARD = 412,
        E_SVR_MSG_ID_GET_SET_VIA_SEASON = 413,
        E_SVR_MSG_ID_GET_DUAL_TASK_INFO = 414,
        E_SVR_MSG_ID_GET_DUAL_TASK_REWARD = 415,
        E_SVR_MSG_ID_GET_TURBO_PROVISIONALS_INFO = 416,
        E_SVR_MSG_ID_GET_CASUAL_TASK_INFO = 417,
        E_SVR_MSG_ID_GET_CASUAL_TASK_REWARD = 418,
        E_SVR_MSG_ID_GET_ZONE_RANK = 421,
        E_SVR_MSG_ID_GET_ZONE_RANK_USER_INFO = 422,
        E_SVR_MSG_ID_GET_RANK_TYPE = 423,
        E_SVR_MSG_ID_MATCH_OWNER_KICKOFF = 1201,
        E_SVR_MSG_ID_MATCH_OWNER_BEGIN = 1203,
        E_SVR_MSG_ID_MATCH_OWNER_BE_KICKOFF = 1204,
        E_SVR_MSG_ID_MATCH_CANCEL_MATCH = 1207,
        E_SVR_MSG_ID_MATCH_CREATE_OR_JOIN = 1208,
        E_SVR_MSG_ID_MATCH_EXIT_ALLMATCH = 1210,
        E_SVR_MSG_ID_MATCH_START_CONFIRM = 1211,
        E_SVR_MSG_ID_MATCH_CREATE_ROOM = 1212,
        E_SVR_MSG_ID_MATCH_JOIN_ROOM = 1213,
        E_SVR_MSG_ID_MATCH_CREATE_ROOM_AND_BEGIN = 1214,
        E_SVR_MSG_ID_MATCH_START_REJECT = 1215,
        E_SVR_MSG_ID_MATCH_ROOM_HELLO = 1216,
        E_SVR_MSG_ID_MATCH_GET_USER_BANINFO = 1217,
        E_SVR_MSG_ID_MATCH_USERAPPLY_JOIN = 1220,
        E_SVR_MSG_ID_MATCH_USERGOT_INVITED_JOIN_NOTIFY = 1221,
        E_SVR_MSG_ID_MATCH_IGOT_USERAPPLY_JOIN_NOTIFY = 1222,
        E_SVR_MSG_ID_MATCH_IAGREE_USERAPPLY_JOIN = 1223,
        E_SVR_MSG_ID_MATCH_USERGOT_AGREED_JOIN_NOTIFY = 1224,
        E_SVR_MSG_ID_MATCH_MATCHING_TO_INVITE = 1225,
        E_SVR_MSG_ID_MATCH_USER_REJECT_INVITED_JOIN = 1226,
        E_SVR_MSG_ID_MATCH_USER_REJECT_INVITED_JOIN_NOTIFY = 1227,
        E_SVR_MSG_ID_MATCH_FIGHT_VER_NOTIFY = 1228,
        E_SVR_MSG_ID_MATCH_FIGHT_VER_MSG = 1229,
        E_SVR_MSG_ID_GET_CUSTOM_SET_CFG = 1230,
        E_SVR_MSG_ID_OBROOM_GET_AI_LIST = 1231,
        E_SVR_MSG_ID_OBROOM_SET_AI = 1232,
        E_SVR_MSG_ID_OBROOM_SWITCH_SEAT = 1233,
        E_SVR_MSG_ID_MATCH_INVITE_WITHOUT_ROOM = 1234,
        E_SVR_MSG_ID_MATCH_INVITE_WITHOUT_ROOM_NOTIFY = 1235,
        E_SVR_MSG_ID_MATCH_INVITE_WITHOUT_ROOM_ANSWER = 1236,
        E_SVR_MSG_ID_MATCH_INVITE_WITHOUT_ROOM_ANSWER_NOTIFY = 1237,
        E_SVR_MSG_ID_MATCH_WITHOUT_ROOM = 1238,
        E_SVR_MSG_ID_NOTIFY_REJECT_MATCH = 1239,
        E_SVR_MSG_ID_NOTIFY_CANCEL_MATCH = 1240,
        E_SVR_MSG_ID_MATCH_DUAL_PLAY_SWITCH_SEAT = 1241,
        E_SVR_MSG_ID_MATCH_DUAL_PLAY_SWITCH_SEAT_NOTIFY = 1242,
        E_SVR_MSG_ID_MATCH_DUAL_PLAY_CONFIRM_SWITCH_SEAT = 1243,
        E_SVR_MSG_ID_MATCH_DUAL_PLAY_REJECT_SWITCH_SEAT_NOTIFY = 1244,
        E_SVR_MSG_ID_NOTIFY_PLAYER_DIFF_VERSION = 1245,
        E_SVR_MSG_ID_MATCH_COMMON_NOTIFY = 1246,
        E_SVR_MSG_ID_NOT_IN_MATCH_ROOM_NOTIFY = 1260,
        E_SVR_MSG_ID_MATCH_REJECT_INVITE_NOTIFY = 1261,
        E_SVR_MSG_ID_MATCH_PLAY_AGAIN_ENTER_ROOM = 1270,
        E_SVR_MSG_ID_MATCH_PLAY_AGAIN_ROOM_NOTIFY = 1271,
        E_SVR_MSG_ID_MATCH_REFUSE_PLAY_AGAIN = 1272,
        E_SVR_MSG_ID_NOTIFY_FIX_MEAN_MMR = 1281,
        E_TAC_SVR_MSG_ID_DAILY_ACTIVE_NOTIFY_RED = 1304,
        E_SVR_MSG_ID_REPORT_RTVOICE_CREDIT_LIMIT = 1340,
        E_TAC_SVR_MSG_ID_CHAT_SEND_MSG = 1351,
        E_TAC_SVR_MSG_ID_CHAT_GET_MSG = 1352,
        E_TAC_SVR_MSG_ID_CHAT_PUSH_MSG = 1353,
        E_TAC_SVR_MSG_ID_CHAT_SEND_SYS_MSG = 1355,
        E_TAC_SVR_MSG_ID_RTVOICE = 1356,
        E_TAC_SVR_MSG_ID_CHAT_SHARE_ROOM_MSG = 1357,
        E_TAC_SVR_MSG_ID_GENERAL_SENSITIVE_WORD_CHECK_MSG = 1358,
        E_TAC_SVR_MSG_ID_SEC_CLEAR_POST = 1359,
        E_TAC_SVR_MSG_ID_PAOMADENG_MSG = 1360,
        E_TAC_SVR_MSG_ID_MARQUEE_CONFIG_MSG = 1361,
        E_SVR_MSG_ID_GET_MARQUEE_CONFIG = 1362,
        E_TAC_SVR_MSG_ID_GET_CLIENT_SETTING = 1450,
        E_TAC_SVR_MSG_ID_UPDATE_CLIENT_SETTING = 1451,
        E_TAC_SVR_MSG_ID_UPDATE_GIFTCENTER_SETTING = 1452,
        E_TAC_SVR_MSG_ID_REPORT_FEATURE = 1458,
        E_TAC_SVR_MSG_ID_GET_ALL_CLIENT_SETTING_BY_KEY = 1459,
        E_TAC_SVR_MSG_ID_GET_BATTLE_PASS_DATA = 1500,
        E_TAC_SVR_MSG_ID_BUY_BATTLE_PASS = 1501,
        E_TAC_SVR_MSG_ID_GET_BATTLE_PASS_LV_REWARD = 1503,
        E_TAC_SVR_MSG_ID_GET_DAILY_TASK = 1504,
        E_TAC_SVR_MSG_ID_GET_DAILY_TASK_REWARD = 1505,
        E_TAC_SVR_MSG_ID_BATTLE_PASS_GM = 1506,
        E_TAC_SVR_MSG_ID_GET_WEEKLY_TASK = 1507,
        E_TAC_SVR_MSG_ID_GET_WEEKLY_TASK_REWARD = 1508,
        E_SVR_MSG_ID_GET_ENLIGHTENMENT_DATA = 1509,
        E_SVR_MSG_ID_GET_ENLIGHTENMENT_REWARD = 1510,
        E_SVR_MSG_ID_GET_BP_MALL = 1511,
        E_SVR_MSG_ID_BP_MALL_BUY = 1512,
        E_SVR_MSG_ID_REFRESH_BP_MALL = 1513,
        E_TAC_SVR_MSG_ID_GET_ALL_BP_LEVEL_REWARD = 1514,
        E_TAC_SVR_MSG_ID_GET_BP_HISTORY = 1515,
        E_TAC_SVR_MSG_ID_MALL_LIST = 1611,
        E_TAC_SVR_MSG_ID_MALL_BUY = 1612,
        E_TAC_SVR_MSG_ID_PREFERENTIAL_MALL_LIST = 1613,
        E_TAC_SVR_MSG_ID_MALL_BUY_EGG = 1614,
        E_TAC_SVR_MSG_ID_MALL_EGG_INFO = 1615,
        E_TAC_SVR_MSG_ID_MALL_PAGE_INFO = 1616,
        E_TAC_SVR_MSG_ID_MALL_PRE_DIRECT_BUY = 1617,
        E_TAC_SVR_MSG_ID_MALL_FAST_LIST = 1618,
        E_TAC_SVR_MSG_ID_MALL_BUY_CHEST = 1619,
        E_TAC_SVR_MSG_ID_MALL_CHEST_INFO = 1620,
        E_TAC_SVR_MSG_ID_USE_MALL_EGG = 1621,
        E_TAC_SVR_MSG_ID_MALL_MAIN_HALL = 1622,
        E_TAC_SVR_MSG_ID_MALL_SINGLE_GOODS = 1627,
        E_TAC_SVR_MSG_ID_MALL_QUERY_DIRECT_BUY = 1628,
        E_TAC_SVR_MSG_ID_MALL_REQ_JTECHNICAL_CHEST_INFO = 1623,
        E_TAC_SVR_MSG_ID_MALL_BUY_JTECHNICAL_CHEST = 1624,
        E_TAC_SVR_MSG_ID_MALL_REQ_JTECHNICAL_BUY_TASK_REWARD_INFO = 1625,
        E_TAC_SVR_MSG_ID_MALL_GET_JTECHNICAL_BUY_TASK_REWARD = 1626,
        E_TAC_SVR_MSG_ID_TINY_LIST_REQ = 1690,
        E_TAC_SVR_MSG_ID_SET_TINY_REQ = 1691,
        E_TAC_SVR_MSG_ID_READ_TINY_SKIN_REQ = 1692,
        E_TAC_SVR_MSG_ID_SET_TINY_EXPRESSION_DATA_REQ = 1693,
        E_TAC_SVR_MSG_ID_SET_TINY_EXPRESSION_REQ = 1694,
        E_TAC_SVR_MSG_ID_TINY_MAP_REQ = 1695,
        E_TAC_SVR_MSG_ID_SET_TINY_MAP_REQ = 1696,
        E_TAC_SVR_MSG_ID_TINY_HERO_CHANGE = 1697,
        E_TAC_SVR_MSG_ID_TINY_EXPRESSION_CHANGE = 1698,
        E_TAC_SVR_MSG_ID_TINY_MAP_CHANGE = 1699,
        E_TAC_SVR_MSG_ID_GET_USER_PROFILE_INFO = 1701,
        E_TAC_SVR_MSG_ID_CHANGE_AVATAR_BOX = 1702,
        E_TAC_SVR_MSG_ID_ADD_AVATAR_BOX_GM = 1703,
        E_TAC_SVR_MSG_ID_GET_RECORD_LIST = 1704,
        E_TAC_SVR_MSG_ID_GET_GAME_RECORD = 1707,
        E_TAC_SVR_MSG_ID_GET_HERO_USED_RECORD = 1708,
        E_TAC_SVR_MSG_ID_GET_FETTER_USED_RECORD = 1709,
        E_TAC_SVR_MSG_ID_GET_WIN_FETTER_TEAM_RECORD = 1710,
        E_TAC_SVR_MSG_ID_LOCK_STEP_RESULT = 1711,
        E_TAC_SVR_MSG_ID_LOCK_STEP_GAME_END = 1713,
        E_SVR_MSG_ID_GET_AVATAR_LIST = 1714,
        E_SVR_MSG_ID_GET_AVATAR_BOX_LIST = 1715,
        E_TAC_SVR_MSG_ID_GET_FETTER_RECORD = 1716,
        E_SVR_MSG_ID_AVATAR_BOX_AUTO_CHANGE_NOTIFY = 1717,
        E_SVR_MSG_ID_CHANGE_AVATAR = 1718,
        E_SVR_MSG_ID_AVATAR_AUTO_CHANGE_NOTIFY = 1719,
        E_TAC_SVR_MSG_ID_REPORT_STATISTICS_INFO = 1720,
        E_TAC_SVR_MSG_ID_TEST_LOCK_STEP_RESULT = 1721,
        E_TAC_SVR_MSG_ID_GET_TURN_BATTLE_INFO = 1722,
        E_TAC_SVR_MSG_ID_REPORT_USER_BATTLE_INFO = 1723,
        E_TAC_SVR_MSG_ID_GET_USER_BATTLE_INFO = 1724,
        E_TAC_SVR_MSG_ID_DEL_TURN_BATTLE_INFO = 1726,
        E_TAC_SVR_MSG_ID_MODIFY_USER_BATTLE_NAME = 1727,
        E_TAC_SVR_MSG_ID_GET_USER_DEACTIVATE_STATE_BATCH = 1728,
        E_TAC_SVR_MSG_ID_GET_NOVICE_GUIDE_GIFT_STATUS = 1730,
        E_TAC_SVR_MSG_ID_NOVICE_GUIDE_REPORT_MISSION_COMPLETE = 1731,
        E_TAC_SVR_MSG_ID_USER_GET_NOVICE_GUIDE_REWARD = 1732,
        E_TAC_SVR_MSG_ID_REPORT_NOVICE_COMPLETE = 1734,
        E_SVR_MSG_ID_REPORT_NOVICE_TASK_CHOOSE = 1736,
        E_SVR_MSG_ID_GET_NOVICE_TASK_STATUS = 1737,
        E_SVR_MSG_ID_REPORT_RECOMMEND_CHOOSE = 1738,
        E_SVR_MSG_ID_GET_RECOMMEND_CHOOSE = 1739,
        E_SVR_MSG_ID_REPORT_NOVICE_COMPLETE = 1740,
        E_SVR_MSG_ID_GET_K6_PLAYER_DATA = 1741,
        E_SVR_MSG_ID_REPORT_NEWBEE_MISSION_COMPLETE = 1742,
        E_SVR_MSG_ID_TAC_STOP_WATCH_GAME_PLAY = 1749,
        E_SVR_MSG_ID_TAC_WATCH_GAME_PLAY = 1750,
        E_TAC_SVR_MSG_ItemChangeNotify = 1751,
        E_TAC_SVR_MSG_RedPointNotify = 1752,
        E_TAC_SVR_MSG_ItemRead = 1753,
        E_TAC_SVR_MSG_ItemUse = 1755,
        E_TAC_SVR_MSG_ItemSell = 1756,
        E_TAC_SVR_MSG_OPEN_CHEST_NOTIFY = 1757,
        E_TAC_SVR_MSG_GIVE_ITEM_BY_DIRECT_BUY = 1758,
        E_TAC_SVR_MSG_LAUNCH_DIRECT_BUY = 1759,
        E_TAC_SVR_MSG_ItemGet = 1760,
        E_TAC_SVR_MSG_CouponGet = 1761,
        E_TAC_SVR_MSG_GIFT_PACK_USE = 1762,
        E_TAC_SVR_MSG_ITEM_EXPIRE_NOTIFY = 1763,
        E_TAC_SVR_MSG_ITEM_MANUAL_DECOMPOSE = 1764,
        E_TAC_SVR_MSG_CHECK_ITEM_NUMBER = 1765,
        E_TAC_SVR_MSG_ItemSynthesize = 1766,
        E_TAC_SVR_MSG_QUERY_ITEM_MAX_USE_NUM = 1767,
        E_TAC_SVR_MSG_ID_CANCEL_TINY_EQUIPMENT = 1774,
        E_TAC_SVR_MSG_ID_TINY_HERO_UPGRADE = 1775,
        E_TAC_SVR_MSG_ID_TINY_FETTERS_NOTIFY = 1776,
        E_TAC_SVR_MSG_ID_TINY_FETTERS_GET_INFO = 1777,
        E_SVR_MSG_ID_TINY_FETTERS_CRAFT_EMBLEM = 1778,
        E_TAC_SVR_MSG_ID_TINY_EXPIRE_TIPS_NOTIFY = 1779,
        E_TAC_SVR_MSG_ID_MAP_EXPIRE_TIPS_NOTIFY = 1780,
        E_TAC_SVR_MSG_ID_GET_DROP_GROUP_CFG = 1781,
        E_SVR_MSG_ID_FRIEND_MSG_INGAME = 1800,
        E_SVR_MSG_ID_FRIEND_MSG_RELATION_CHAIN = 1801,
        E_SVR_MSG_ID_FRIEND_MSG_APPLY = 1802,
        E_SVR_MSG_ID_FRIEND_MSG_APPLY_LIST = 1803,
        E_SVR_MSG_ID_FRIEND_MSG_AGREE = 1804,
        E_SVR_MSG_ID_FRIEND_MSG_DEL = 1805,
        E_SVR_MSG_ID_FRIEND_MSG_ADD_BLACK = 1806,
        E_SVR_MSG_ID_FRIEND_MSG_REMOVE_BLACK = 1807,
        E_SVR_MSG_ID_FRIEND_MSG_BLACK_LIST = 1808,
        E_SVR_MSG_ID_FRIEND_MSG_NEARBY = 1809,
        E_SVR_MSG_ID_FRIEND_MSG_NOTIFY_RED_POINT = 1810,
        E_SVR_MSG_ID_FRIEND_MSG_NOTIFY_BLACK_LIST = 1811,
        E_SVR_MSG_ID_FRIEND_MSG_SEARCH = 1812,
        E_SVR_MSG_ID_FRIEND_MSG_NOTIFY_AGREE = 1813,
        E_SVR_MSG_ID_FRIEND_MSG_RANK_INIT = 1814,
        E_SVR_MSG_ID_FRIEND_MSG_RANK_PAGE = 1815,
        E_SVR_MSG_ID_FRIEND_MSG_RANK_SELF = 1816,
        E_SVR_MSG_ID_FRIEND_MSG_RECENT_PLAYER = 1817,
        E_SVR_MSG_ID_FRIEND_MSG_STATE = 1818,
        E_SVR_MSG_ID_FRIEND_MSG_OFFLINE_MSG = 1819,
        E_SVR_MSG_ID_FRIEND_MSG_APPLY_MSG = 1820,
        E_SVR_MSG_ID_FRIEND_MSG_REFRESH_REALTIME = 1821,
        E_SVR_MSG_ID_FRIEND_MSG_GET_RECOMMENDING_PLAYER = 1822,
        E_SVR_MSG_ID_FRIEND_MSG_COMPANION_RED_DOT = 1823,
        E_SVR_MSG_ID_GET_RENAME_STATUS = 1831,
        E_SVR_MSG_ID_RENAME = 1832,
        E_SVR_MSG_ID_GET_BUTLER_ACTIVITY_LIST = 1850,
        E_SVR_MSG_ID_GET_BUTLER_ACTIVITY_DETAIL = 1851,
        E_SVR_MSG_ID_GET_BUTLER_ACTIVITY_REWARD = 1852,
        E_SVR_MSG_ID_EXCHANGE_BUTLER_ACTIVITY_ITEMS = 1853,
        E_SVR_MSG_ID_UPDATE_BUTLER_EXCHANGE_REMIND_SETTING = 1854,
        E_SVR_MSG_ID_QUIZ_EVENT_INFO = 1902,
        E_SVR_MSG_ID_QUIZ_EVENT_SUBMIT = 1903,
        E_SVR_MSG_ID_QUIZ_EVENT_CHEAT = 1904,
        E_SVR_MSG_ID_CONTEST_GET_CONTEST_LIST = 2000,
        E_SVR_MSG_ID_CONTEST_GET_USER_CONTEST_SIGNINFO = 2001,
        E_SVR_MSG_ID_CONTEST_SIGN = 2002,
        E_SVR_MSG_ID_CONTEST_RESET_SIGN = 2003,
        E_SVR_MSG_ID_CONTEST_GET_USER_HISTORY_CONTEST_RECORD = 2004,
        E_SVR_MSG_ID_CONTEST_GET_CONTEST_REWARDINFO = 2005,
        E_SVR_MSG_ID_CONTEST_RESET_AND_SIGN = 2006,
        E_SVR_MSG_ID_CONTEST_NOTIFY_PROFRESS_REWARD = 2007,
        E_SVR_MSG_ID_CONTEST_GET_PERIOD_REWARD = 2008,
        E_SVR_MSG_ID_CONTEST_CFG = 2009,
        E_SVR_MSG_ID_CONTEST_BUY_TICKET = 2010,
        E_SVR_MSG_ID_CONTEST_GET_FREE_TICKET = 2011,
        E_SVR_MSG_ID_CONTEST_EXCHANGE_TICKET = 2012,
        E_SVR_MSG_ID_CONTEST_USER_TICKET_INFO = 2013,
        E_SVR_MSG_ID_CONTEST_MATCH_FAIL_100PLAYER = 2014,
        E_SVR_MSG_ID_GET_ACTIVITY_CONF = 2100,
        E_SVR_MSG_ID_QUERY_ACCUMULATE_CHARGE = 2104,
        E_SVR_MSG_ID_FETCH_ACCUMULATE_CHARGE = 2105,
        E_SVR_MSG_ID_QUERY_FIRST_CHARGE = 2106,
        E_SVR_MSG_ID_FETCH_FIRST_CHARGE = 2107,
        E_SVR_MSG_ID_MIDAS_GIFT_PACK_DELIVER = 2108,
        E_SVR_MSG_ID_GET_CONSECUTIVE_DAY_CHALLENGE_DETAIL = 2140,
        E_SVR_MSG_ID_GET_CONSECUTIVE_DAY_CHALLENGE_REWARD = 2141,
        E_SVR_MSG_ID_GET_LINE_UP_CHALLENGE_DETAIL = 2160,
        E_SVR_MSG_ID_BEGIN_LINE_UP_CHALLENGE = 2161,
        E_SVR_MSG_ID_REPORT_LINE_UP_CHALLENGE_RESULT = 2162,
        E_SVR_MSG_ID_GM_NOTIFY_LINE_UP_REFRESH_RESULT = 2163,
        E_SVR_MSG_ID_GET_DAILY_LOGIN_DETAIL = 2180,
        E_SVR_MSG_ID_SIGNIN_DAILY_LOGIN = 2181,
        E_SVR_MSG_ID_GET_DAILY_LOGIN_WEEKLY_REWARD = 2182,
        E_SVR_MSG_ID_GET_CONSECUTIVE_DAY_TASK_DETAIL = 2190,
        E_SVR_MSG_ID_GET_CONSECUTIVE_DAY_TASK_REWARD = 2191,
        E_SVR_MSG_ID_GET_SKYRIM_TRANING_DETAIL = 2201,
        E_SVR_MSG_ID_GET_SKYRIM_TRANING_REWARD = 2202,
        E_SVR_MSG_ID_GET_SKYRIM_TRANING_ACC_REWARD = 2203,
        E_SVR_MSG_ID_GET_WEEKEND_PARTY_DETAIL = 2221,
        E_SVR_MSG_ID_GET_WEEKEND_PARTY_REWARD = 2222,
        E_SVR_MSG_ID_FEAST_OF_WIND_GET_EXCHANGE_STATE = 2231,
        E_SVR_MSG_ID_FEAST_OF_WIND_DO_EXCHANGE = 2232,
        E_SVR_MSG_ID_REPAIR_SPACETIME_GET_DETAIL = 2241,
        E_SVR_MSG_ID_REPAIR_SPACETIME_GET_REWARD = 2242,
        E_SVR_MSG_ID_COMMON_LOGIN_GET_DETAIL = 2250,
        E_SVR_MSG_ID_COMMON_LOGIN_GET_REWARD = 2251,
        E_SVR_MSG_ID_GET_COMMON_TASK_ACTIVITY_DETAIL = 2261,
        E_SVR_MSG_ID_GET_COMMON_TASK_ACTIVITY_REWARD = 2262,
        E_SVR_MSG_ID_GET_FEATHER_KNIGHT_TASK_DETAIL = 2271,
        E_SVR_MSG_ID_GET_FEATHER_KNIGHT_TASK_REWARD = 2272,
        E_SVR_MSG_ID_GET_STAR_DRAGON_DETAIL = 2280,
        E_SVR_MSG_ID_GET_STAR_DRAGON_REWARD = 2281,
        E_SVR_MSG_ID_GET_STAR_DRAGON_ACC_REWARD = 2282,
        E_SVR_MSG_ID_GET_RECHARGE_DETAIL = 2290,
        E_SVR_MSG_ID_GET_SMALL_RECHARGE_DETAIL = 2295,
        E_SVR_MSG_ID_GET_SMALL_RECHARGE_REWARD = 2296,
        E_SVR_MSG_ID_GET_SMALL_RECHARGE_DIRECT_BUY_ITEM = 2297,
        E_SVR_MSG_ID_GET_BUTLER_ACTIVITY_CONF = 2310,
        E_SVR_MSG_ID_FRIEND_APPOINTMENT_LAUNCH = 2321,
        E_SVR_MSG_ID_FRIEND_APPOINTMENT_LAUNCH_NOTIFY = 2322,
        E_SVR_MSG_ID_FRIEND_APPOINTMENT_ANSWER = 2323,
        E_SVR_MSG_ID_FRIEND_APPOINTMENT_ANSWER_NOTIFY = 2324,
        E_SVR_MSG_ID_FRIEND_APPOINTMENT_GAME_END_NOTIFY = 2325,
        E_SVR_MSG_ID_FRIEND_APPOINTMENT_SET_CURRENT = 2326,
        E_SVR_MSG_ID_FRIEND_APPOINTMENT_LAUNCH_NOTIFY_SERVER = 2327,
        E_SVR_MSG_ID_REPORT_DATA_MORE_RETURN_USER = 2320,
        E_SVR_MSG_ID_GET_PLAY_GROUND_INFO = 2340,
        E_SVR_MSG_ID_GET_GAME_MODULE_INFO = 2341,
        E_SVR_MSG_ID_GET_SHARE_SWITCH = 2342,
        E_SVR_MSG_ID_GET_RESOURCES_CONTROL = 2343,
        E_SVR_MSG_ID_SET_USER_HAS_SHOW = 2344,
        E_SVR_MSG_ID_GET_SCHEDULE = 2400,
        E_SVR_MSG_ID_GET_ACTIVITY_CENTER_CONF = 2600,
        E_SVR_MSG_ID_GET_BANNER_CONF = 2601,
        E_SVR_MSG_ID_CHECK_USER_CONDITION = 2701,
        E_SVR_MSG_ID_DELETE_USER_ACCOUNT = 2702,
        E_SVR_MSG_ID_SEASON_LOTTERY_GET_ON_SALE_LIST = 2711,
        E_SVR_MSG_ID_SEASON_LOTTERY_GET_USER_DATA = 2712,
        E_SVR_MSG_ID_SEASON_LOTTERY_DO_LOTTERY = 2713,
        E_SVR_MSG_ID_SEASON_LOTTERY_PRESENT_COUPON = 2714,
        E_SVR_MSG_ID_SET_LINEUP = 2731,
        E_SVR_MSG_ID_GET_LINEUP = 2732,
        E_SVR_MSG_ID_SET_LINEUP_IDIP = 2733,
        E_SVR_MSG_ID_GET_LINEUP_IDIP = 2734,
        E_SVR_MSG_ID_GIFT_CENTER_GET_DETAIL = 2741,
        E_SVR_MSG_ID_GIFT_CENTER_GIVE_GIFT = 2742,
        E_SVR_MSG_ID_GIFT_CENTER_DEL_GIVE_GIFT_RECORD = 2743,
        E_SVR_MSG_ID_GIFT_CENTER_GET_GIFT = 2744,
        E_SVR_MSG_ID_GIFT_CENTER_DEL_RECV_GIFT_RECORD = 2745,
        E_SVR_MSG_ID_GIFT_CENTER_WANT_GIFT = 2746,
        E_SVR_MSG_ID_GIFT_CENTER_DEL_WANT_GIFT_RECORD = 2747,
        E_SVR_MSG_ID_GIFT_CENTER_READ_RECV_GIFT = 2748,
        E_SVR_MSG_ID_GIFT_CENTER_READ_WANT_GIFT = 2749,
        E_SVR_MSG_ID_GIFT_CENTER_GET_CAN_GIVE_GIFT_LIST = 2750,
        E_SVR_MSG_ID_GIFT_CENTER_RECEVIER_RECORD_RECV_GIFT = 2770,
        E_SVR_MSG_ID_GIFT_CENTER_RECEVIER_RECORD_WANT_GIFT = 2771,
        E_SVR_MSG_ID_GIFT_CENTER_RECEVIER_CHECK_CAN_GIVE_GIFT = 2772,
        E_SVR_MSG_ID_GIFT_CENTER_RECEVIER_GET_CAN_GIVE_GIFT_LIST = 2773,
        E_TAC_SVR_MSG_ID_MALL_REQ_JTECHNICAL_CHEST_INFO_V2 = 2781,
        E_TAC_SVR_MSG_ID_MALL_BUY_JTECHNICAL_CHEST_V2 = 2782,
        E_TAC_SVR_MSG_ID_MALL_REQ_JTECHNICAL_BUY_TASK_REWARD_INFO_V2 = 2783,
        E_TAC_SVR_MSG_ID_MALL_GET_JTECHNICAL_BUY_TASK_REWARD_V2 = 2784,
        E_TAC_SVR_MSG_ID_MALL_PUSH_JTECHNICAL_USER_STATE = 2785,
        E_SVR_MSG_ID_QUERY_INNERBLESSBAG = 2801,
        E_SVR_MSG_ID_JTECHNICAL_COMICS_GET_DETAIL = 2811,
        E_SVR_MSG_ID_JTECHNICAL_COMICS_GET_REWARD = 2812,
        E_SVR_MSG_ID_JTECHNICAL_COMICS_ENABLE_TASK_REQ = 2813,
        E_SVR_MSG_ID_JTECHNICAL_COMICS_NOTIFY_UNLOCK_STEP = 2814,
        E_SVR_MSG_ID_GET_BOSS_CHALLENGE_DETAIL = 2830,
        E_SVR_MSG_ID_BEGIN_BOSS_CHALLENGE = 2831,
        E_SVR_MSG_ID_REPORT_BOSS_CHALLENGE_RESULT = 2832,
        E_SVR_MSG_ID_GET_BOSS_CHALLENGE_ROGUELIKE_SELECTION = 2833,
        E_SVR_MSG_ID_BEGIN_BOSS_CHALLENGE_ROGUELIKE = 2834,
        E_SVR_MSG_ID_GIVEUP_BOSS_CHALLENGE_ROGUELIKE = 2835,
        E_SVR_MSG_ID_REPORT_BOSS_CHALLENGE_ROGUELIKE_RESULT = 2836,
        E_SVR_MSG_ID_GET_BOSS_CHALLENGE_ROGUELIKE_BOSS_ATLAS = 2837,
        E_SVR_MSG_ID_GET_BOSS_CHALLENGE_ROGUELIKE_ATLAS_TASK = 2838,
        E_SVR_MSG_ID_GET_BOSS_CHALLENGE_ROGUELIKE_ATLAS_TASK_REWARD = 2839,
        E_SVR_MSG_ID_RANDOM_BOSS_CHALLENGE_ROGUELIKE_OPTION = 2840,
        E_SVR_MSG_ID_NOTIFY_BOSS_CHALLENGE_ROGUELIKE_DEFEATALL = 2841,
        E_SVR_MSG_ID_GET_BOSS_CHALLENGE_PVE_DETAIL = 2850,
        E_SVR_MSG_ID_BEGIN_BOSS_CHALLENGE_PVE = 2851,
        E_SVR_MSG_ID_REPORT_BOSS_CHALLENGE_PVE_RESULT = 2852,
        E_SVR_MSG_ID_GIVEUP_BOSS_CHALLENGE_PVE = 2853,
        E_SVR_MSG_ID_GET_BOSS_CHALLENGE_PVE_REWARD = 2854,
        E_SVR_MSG_ID_NOTIFY_BOSS_CHALLENGE_PVE_PASS = 2855,
        E_SVR_MSG_ID_GET_CHAT_BUBBLE_LIST = 3201,
        E_SVR_MSG_ID_CHANGE_CHAT_BUBBLE = 3202,
        E_SVR_MSG_ID_CHAT_BUBBLE_AUTO_CHANGE_NOTIFY = 3203,
        E_SVR_MSG_ID_TRIAL_OF_CYAN_DRAGON_GET_CONFIG = 3301,
        E_SVR_MSG_ID_TRIAL_OF_CYAN_DRAGON_GET_USER_DATA = 3302,
        E_SVR_MSG_ID_TRIAL_OF_CYAN_DRAGON_GET_TASK_REWARD = 3303,
        E_SVR_MSG_ID_TRIAL_OF_CYAN_DRAGON_GET_BD_REWARD = 3304,
        E_SVR_MSG_ID_TRIAL_OF_CYAN_DRAGON_ENABLE_TRIAL = 3305,
        E_SVR_MSG_ID_TRIAL_OF_CYAN_DRAGON_GET_ALL_BD_REWARD = 3306,
        E_SVR_MSG_ID_GO_TWO_CITIES_GET_INFO = 3321,
        E_SVR_MSG_ID_GO_TWO_CITIES_CHOOSE = 3322,
        E_SVR_MSG_ID_GO_TWO_CITIES_RECEIVE_TASK_REWARD = 3323,
        E_SVR_MSG_ID_CARD_COLLECT_GET_CURRENT_SET_COLLECTION = 3350,
        E_SVR_MSG_ID_CARD_COLLECT_UPGRADE_CARD = 3351,
        E_SVR_MSG_ID_CARD_COLLECT_DISMANTLE_CARD = 3352,
        E_SVR_MSG_ID_CARD_COLLECT_CLAIM_TASK_REWARD = 3353,
        E_SVR_MSG_ID_CARD_COLLECT_OPEN_PACK = 3354,
        E_SVR_MSG_ID_CARD_COLLECT_OPEN_PACK_CHOOSE_CARD = 3355,
        E_SVR_MSG_ID_CARD_COLLECT_USE_IN_GAME_EFFECT = 3356,
        E_SVR_MSG_ID_CARD_COLLECT_CRAFT_CARD = 3357,
        E_SVR_MSG_ID_CARD_COLLECT_CHECK_RED = 3358,
        E_SVR_MSG_ID_CARD_COLLECT_GET_TASK = 3359,
        E_SVR_MSG_ID_CARD_COLLECT_BATCH_UPGRADE_CARD = 3360,
        E_SVR_MSG_ID_CARD_COLLECT_BATCH_DISMANTLE_CARD = 3361,
        E_SVR_MSG_ID_CARD_COLLECT_GET_SET_BRIEF_INFO = 3362,
        E_SVR_MSG_ID_CARD_COLLECT_GET_CURRENT_SET_COLLECTION_BY_SET_ID = 3363,
        E_SVR_MSG_ID_CARD_COLLECT_BATCH_USE_IN_GAME_EFFECT = 3364,
        E_SVR_MSG_ID_HERO_SKIN_GET_SET_CONF = 3365,
        E_SVR_MSG_ID_HERO_SKIN_GET_SET_COLLECTION_BY_SET_ID = 3366,
        E_SVR_MSG_ID_HERO_SKIN_GET_TASK = 3367,
        E_SVR_MSG_ID_HERO_SKIN_CLAIM_TASK_REWARD = 3368,
        E_SVR_MSG_ID_HERO_SKIN_GET_ALL_SKIN = 3369,
        E_TAC_SVR_MSG_ID_MAIN_HALL_GET_DATA = 3401,
        E_TAC_SVR_MSG_ID_MAIN_HALL_GET_TIPS = 3402,
        E_TAC_SVR_MSG_ID_MAIN_HALL_GET_BANNER = 3403,
        E_TAC_SVR_MSG_ID_GET_AFK_COUNTDOWN_CONFIG = 3404,
        E_TAC_SVR_MSG_ID_MAIN_HALL_GET_TOP_ENTRANCE = 3406,
        E_TAC_SVR_MSG_ID_MAIN_HALL_GET_LEFT_RECOMMEND = 3407, // 拉取大厅左屏推荐配置
        E_SVR_MSG_ID_FETTER_TWO_CITIES_GET_INFO = 3460,
        E_SVR_MSG_ID_FETTER_TWO_CITIES_OPEN_HEX_TASK = 3461,
        E_SVR_MSG_ID_FETTER_TWO_CITIES_GET_REWARD = 3462,
        E_TAC_SVR_MSG_GIVE_ITEM_BY_PROMO_CODE = 3470,
        E_SVR_MSG_ID_COMM_ACCUMULATE_GET_INFO = 3480,
        E_SVR_MSG_ID_COMM_ACCUMULATE_GET_REWARD = 3481,
        E_SVR_MSG_ID_JOC_GET_QUALIFIER = 3502,
        E_SVR_MSG_ID_JOC_GET_BRIEF_INFO = 3503,
        E_SVR_MSG_ID_JOC_GET_TASK = 3504,
        E_SVR_MSG_ID_JOC_CLAIM_TASK_REWARD = 3505,
        E_SVR_MSG_ID_JOC_GET_REGAME_PLAYERS = 3509,
        E_SVR_MSG_ID_PRIVILEGE_CARD_DETAIL = 3551,
        E_SVR_MSG_ID_COMMON_BP_GET_DATA = 3571,
        E_SVR_MSG_ID_COMMON_BP_GET_LEVEL_REWARD = 3572,
        E_SVR_MSG_ID_COMMON_BP_GET_TASK_REWARD = 3573,
        E_SVR_MSG_ID_COMMON_BP_REFRESH_TASK = 3574,
        E_SVR_MSG_ID_COMMON_MULTI_STEP_ACTIVITY_GET_DETAIL = 3600,
        E_SVR_MSG_ID_COMMON_MULTI_STEP_ACTIVITY_GET_REWARD = 3601,
        E_SVR_MSG_ID_COMMON_MULTI_STEP_ACTIVITY_NOTIFY_UNLOCK_STEP = 3602,
        E_SVR_MSG_ID_GET_QUICK_CHAT_DATA = 3630,
        E_SVR_MSG_ID_SET_QUICK_CHAT_DATA = 3631,
        E_SVR_MSG_ID_GET_DEFAULT_QUICK_CHAT_CFG = 3632,
        E_SVR_MSG_ID_COMMON_MALL_LIST = 3650,
        E_SVR_MSG_ID_COMMON_MALL_BUY = 3651,
        E_SVR_MSG_ID_COMMON_MALL_PAGE_INFO = 3652,
        E_SVR_MSG_ID_COMMON_MALL_GET_BUY_RECORDS = 3653,
        E_SVR_MSG_ID_FORTUNE_BOX_GET_DETAIL = 3680,
        E_SVR_MSG_ID_FORTUNE_BOX_RECEIVE_REWARD = 3681,
        E_SVR_MSG_ID_FORTUNE_BOX_SERIES_PANEL = 3682,
        E_SVR_MSG_ID_ENLIGHTENED_TALE_INFO = 3700,
        E_SVR_MSG_ID_ENLIGHTENED_TALE_LOTTERY = 3701,
        E_SVR_MSG_ID_ENLIGHTENED_TALE_OPEN_BAG = 3702,
        E_SVR_MSG_ID_CHECK_TOKEN_IMMEDIATELY = 3711,
        E_SVR_MSG_ID_NOTIFY_APEX_TIER_CHANGE = 3712,    // 通知顶尖段位变化
        E_SVR_MSG_ID_GET_LOTTERY_CONFIG = 3800,
        E_SVR_MSG_ID_GET_NO_REPLACEMENT_CONFIG = 3801,
        E_SVR_MSG_ID_DRAW = 3802,
        E_SVR_MSG_ID_GET_LOTTERY_DETAILS_CONFIG = 3803,
        E_SVR_MSG_ID_BLUEPRINT_OPEN_BAG = 3804,
        E_SVR_MSG_ID_BLUEPRINT_PRESENT_COUPON = 3805,
        E_SVR_MSG_ID_BUSINESS_CARD_GET_SHOW_INFO = 3851,
        E_SVR_MSG_ID_BUSINESS_CARD_GET_DATA = 3852,
        E_SVR_MSG_ID_BUSINESS_CARD_CHANGE_BACKGROUND = 3853,
        E_SVR_MSG_ID_BUSINESS_CARD_CHANGE_FRAME = 3854,
        E_SVR_MSG_ID_GET_BATTLE_EMOTION_DATA = 3901,        // 拉取魔法表情装配数据
        E_SVR_MSG_ID_SET_BATTLE_EMOTION_DATA = 3902,
        E_SVR_MSG_ID_GET_DIRECT_BUY_ACTIVITY_DETAIL = 3921,
        E_SVR_MSG_ID_GET_DIRECT_BUY_ACTIVITY_ITEM = 3922,
        E_SVR_MSG_ID_GET_DIRECT_BUY_ACTIVITY_CONF = 3923,
        E_SVR_MSG_ID_GET_BETTING_LOTTERY_INFO = 3931,   
        E_SVR_MSG_ID_GET_BETTING_LOTTERY_REWARD = 3932,
        E_SVR_MSG_ID_GET_BETTING_LOTTERY_FIRST_PRIZE_HISTORY = 3933,
        E_SVR_MSG_ID_GET_BETTING_LOTTERY_USER_RECORDS = 3934,
        E_SVR_MSG_ID_GET_TIME_STORE_DETAIL = 3980,
        E_SVR_MSG_ID_BETTING_LOTTERY_BUY = 3935,
        E_SVR_MSG_ID_GET_BETTING_LOTTERY_GLOBAL_STATE_INFO = 3936,
        E_TAC_SVR_MSG_ID_TINY_MAP_SET_DIY_COLLOATION = 4000,
        E_TAC_SVR_MSG_ID_TINY_MAP_USE_DIY_COLLOATION = 4001,
        E_TAC_SVR_MSG_ID_TINY_MAP_CLEAR_DIY_COLLOATION = 4002,
        E_TAC_SVR_MSG_ID_TINY_MAP_RENAME_DIY_COLLOATION = 4003,
        E_TAC_SVR_MSG_ID_GET_CHESS_CELEBRATE_EFFECT_DATA = 4050,
        E_TAC_SVR_MSG_ID_UPDATE_CURRENT_CHESS_CELEBRATE_EFFECT = 4051,
        E_TAC_SVR_MSG_ID_SET_CHESS_CELEBRATE_EFFECT_READ = 4052,
        E_TAC_SVR_MSG_ID_JOC_MANUAL_GET_BRIEF = 4111,
        E_TAC_SVR_MSG_ID_JOC_MANUAL_GET_BULLET_SCREEN = 4112,
        E_TAC_SVR_MSG_ID_JOC_MANUAL_SEND_BULLET_SCREEN = 4113,
        E_TAC_SVR_MSG_ID_JOC_MANUAL_SELF_VOTE = 4114,
        E_TAC_SVR_MSG_ID_JOC_MANUAL_CREATE_VOTE_ASSIST = 4115,
        E_TAC_SVR_MSG_ID_JOC_MANUAL_ASSIST_VOTE = 4116,
        E_TAC_SVR_MSG_ID_JOC_MANUAL_GET_VOTE_ASSIST = 4117,
        E_TAC_SVR_MSG_ID_JOC_MANUAL_GET_FRIEND_VOTE_ASSIST_LIST = 4118,
        E_TAC_SVR_MSG_ID_JOC_MANUAL_NOTIFY_FRIEND_VOTE_ASSIST = 4119,
        E_TAC_SVR_MSG_ID_JOC_MANUAL_GET_VOTE_HISTORY = 4120,
        E_TAC_SVR_MSG_ID_JOC_MANUAL_GET_VOTE_REWARD = 4121,
        E_TAC_SVR_MSG_ID_JOC_MANUAL_GET_QUIZ_DATA = 4122,
        E_TAC_SVR_MSG_ID_JOC_MANUAL_COMMIT_QUIZ_ANSWER = 4123,
        E_TAC_SVR_MSG_ID_JOC_MANUAL_GET_QUIZ_TASK_REWARD = 4124,
        E_TAC_SVR_MSG_ID_JOC_MANUAL_QUERY_WORLD_VOTE_ASSIST = 4125,
        E_TAC_SVR_MSG_ID_JOC_MANUAL_GET_SEND_VOTE_ASSIST_FRIEND = 4126, 
        E_TAC_SVR_MSG_ID_JOC_MANUAL_LOTTERY_GET = 4127, 
        E_TAC_SVR_MSG_ID_JOC_MANUAL_LOTTERY_DRAW = 4128, 
        E_TAC_SVR_MSG_ID_JOC_MANUAL_LOTTERY_HISTORY = 4129, 
        E_TAC_SVR_MSG_ID_JOC_MANUAL_LOTTERY_PRESENT_COUPON = 4131, 
        E_SVR_MSG_ID_PLAYBOOK_GET_CURRENT = 5200,
        E_SVR_MSG_ID_PLAYBOOK_UPDATE_CURRENT = 5201,
        E_SVR_MSG_ID_GET_GENDER_STATUS = 5210,
        E_SVR_MSG_ID_MODIFY_GENDER = 5211,
        E_SVR_MSG_ID_LBS_GET_GEO_ADDRESS = 5231, // 根据经纬度获取行政区
        E_SVR_MSG_ID_GUILD_GET_CHANNEL_DATA = 5023,
        E_SVR_MSG_ID_GUILD_CHAT_MESSAGE = 5024,
        E_SVR_MSG_ID_GET_ACTIVITY_STATION = 5221, // 获取活动站活动列表信息 GetActivityStationReq GetActivityStationResp
        E_SVR_MSG_ID_GUILD_MEMBER_OPERATION_MESSAGE = 5029,
        E_SVR_MSG_ID_GUILD_CHANGE_GEO_ADDRESS = 5037,
        E_SVR_MSG_ID_GET_INGAME_BUTTON_DATA = 5241, // 获取拥有的局内按钮列表
        E_SVR_MSG_ID_SET_INGAME_BUTTON = 5242, // 设置当前使用局内按钮
        E_SVR_MSG_ID_HONOR_SYSTEM_GET_DETAIL = 5250, // 获取荣誉系统所有配置
        E_SVR_MSG_ID_HONOR_SYSTEM_GET_ALL_TINY_DATA = 5251, // 获取所有的房间内小小英雄和棋盘
        E_SVR_MSG_ID_HONOR_SYSTEM_SET_MODULE_STATE = 5252, // 设置模块状态
        E_SVR_MSG_ID_SITDOWN = 10001,
        E_SVR_MSG_ID_STANDUP = 10002,
        E_SVR_MSG_ID_TAC_ROOM_REPORT_VOICE_ID = 10025,
        E_SVR_MSG_ID_TAC_PUSH_LOCKSTEP_ROOMINFO = 10032,
        E_SVR_MSG_ID_TAC_REPORT_BALANCE = 10033,
        E_SVR_MSG_ID_TAC_REPORT_GAMEOVER = 10034,
        E_SVR_MSG_ID_TAC_REPORT_PLAY_STATE = 10035,
        E_SVR_MSG_ID_NOTIFY_INCONSISTENT = 10036,
        E_SVR_MSG_ID_LOGIN_AT_GAMESVR = 10037,
        E_SVR_MSG_ID_GET_LATEST_PLAY_STATE = 10038,
        E_SVR_MSG_ID_REPORT_JOIN_GVOICE_DONE = 10039,
        E_SVR_MSG_ID_NOTIFY_JOIN_GVOICE_DONE = 10040,
        E_TAC_SVR_MSG_ID_REPORT_TURN_BATTLE_INFO = 10041,
        E_SVR_MSG_ID_NOTIFY_VOTE_NUM_NOT_ENOUGH = 10042,
        E_SVR_MSG_ID_REPORT_BATTLE_DATA = 10043,
        E_SVR_MSG_ID_REPORT_ROUND_RESULT = 10044,
        E_SVR_MSG_ID_GET_BRGAME_DATA = 10045,
        E_SVR_MSG_ID_NOTIFY_NEW_ROUND_BATTLE_DATA = 10046,
        E_SVR_MSG_ID_NOTIFY_WATCH_GAME_NUM = 10047,
        E_SVR_MSG_ID_GET_WATCH_GAME_PLAYERS = 10048,
        E_SVR_MSG_ID_DUAL_PLAY_TEAM_TALK = 10049,
        E_SVR_MSG_ID_DUAL_PLAY_TEAM_TALK_NOTIFY = 10050,
        E_SVR_MSG_ID_GAME_PLAY_SEND_MSG = 10051,
        E_SVR_MSG_ID_GAME_PLAY_SEND_MSG_NOTIFY = 10052,
        E_SVR_MSG_ID_PUSH_DUAL_SECOND_RESULT_DATA = 10053,
        E_SVR_MSG_ID_NOTIFY_PLAYER_RANK = 10054,
        E_SVR_MSG_ID_GET_RSA_PUK = 20001,
        E_SVR_MSG_ID_GET_SIG = 20002,
        E_SVR_MSG_ID_GET_FRIEND_BEFORE_LOGIN = 30001,
        E_SVR_MSG_ID_NOTIFY_REPORT_LOG = 30101,
        E_SVR_MSG_ID_CHECK_REGISTER = 30002,
        E_SVR_MSG_ID_GCLOUD_MODULE_SWITCH = 30003,
        E_SVR_MSG_ID_GET_COS_SIGNATURE = 30004,
        E_SVR_MSG_ID_GET_RECOMMEND_NAMES = 31001,
        E_MSG_ID_GAME_CLIENT_RUN = 40001,
        E_MSG_ID_BRGAME_CLIENT_RUN = 40002,
        E_MSG_ID_IDLE_GAME_SERVER_CALCULATION = 40003,
        E_MSG_ID_NOTIFY_GC_COMPLETED = 40100,
        E_MSG_ID_CLIENT_PROPERTY_REPORT = 50001,
        E_MSG_ID_CLIENT_OLAP_REPORT = 50002,
        E_SVR_MSG_ID_SET_ITRE_SHOW = 70001,
        E_SVR_MSG_ID_SET_ITRE_SHOW_BATCH = 70002,
        E_SVR_MSG_ID_SET_ITRE_SHOW_BATCHV2 = 70003,
        E_SVR_MSG_ID_CLEAR_USER_RANKING_DATA = 70004,
        E_SVR_MSG_ID_TSS_SDK_DATA = 80001,
        E_MSG_ID_PUSH_SIG_DECRYPT_ERROR = 110001,
        E_MSG_ID_PUSH_SIG_OVER_DATE = 110003,
        E_MSG_ID_PUSH_DECRYPT_FAILED = 110004,
        E_MSG_ID_PUSH_NO_GAME_SERVER_AVAILABLE = 110005,
        E_MSG_ID_GM_MSG_MAIN_CMD = 200001,
    }
    public enum E_SVR_RET
    {
        E_SVR_RET_SUCC = 0,
        E_SVR_RET_FAILED = 1,
        E_SVR_RET_VERIFY_FAILED = 2,
        E_SVR_RET_NEED_REGISTER = 3,
        E_SVR_RET_GETUSERDATA_FAILED = 4,
        E_SVR_RET_NOT_TEST_WHITEUIN = 5,
        E_SVR_RET_REGISTER_SYS_ERROR = 6,
        E_SVR_RET_WHITELIST_DISABLED = 7,
        E_SVR_RET_NOT_FIND_USER_SESSION = 14,
        E_SVR_RET_SERVER_INVALID = 28,
        E_SVR_RET_REQ_PARAM_ERROR = 29,
        E_SVR_RET_INVALID_ZONE_ID = 30,
        E_SVR_RET_GET_USER_STATE_FAILED = 34,
        E_SVR_RET_NOT_ENOUGH_COUPONS = 50,
        E_SVR_RET_QUEUE_INFO_NOT_VALID = 51,
        E_SVR_RET_MSDK_ENVIRONMENT_NOT_MATCH = 52,
        E_SVR_RET_GET_GAME_OVER_GUID_NOT_MATCH = 53,
        E_SVR_RET_PLAYBOOK_DISABLED = 74,
        E_SVR_RET_USER_CLIENT_VERSION_SMALL_MAX_LOGIN_VERSION = 75,
        E_SVR_RET_CLIENT_VERSION_HIGH = 76,
        E_SVR_RET_CLIENT_VERSION_ERROR = 77,
        E_SVR_RET_TALK_TEXT_EMPTY = 78,
        E_SVR_RET_TALK_TEXT_TOO_LONG = 79,
        E_SVR_RET_TALK_REEQUENCY_TOO_FAST = 80,
        E_SVR_RET_NO_TALK_TO_GAME_FRIEND = 81,
        E_SVR_RET_NO_TALK_TO_SELF = 82,
        E_SVR_RET_GET_FRIEND_TALK_RET = 83,
        E_SVR_RET_TALK_TRUMPET_QUEUE_FULL = 84,
        E_SVR_RET_TALK_REQ_PARAM_ERROR = 85,
        E_SVR_RET_TALK_EMOTION_NOT_IN_GAME = 86,
        E_SVR_RET_TALK_OVER_FREQUENCY = 87,
        E_SVR_RET_TALK_TYPE_CLOSED = 88,
        E_SVR_RET_TALK_QUICK_CHAT_NOT_IN_GAME = 89,
        E_SVR_RET_TALK_BATTLE_EMOTION_NOT_EQUIPED = 90,
        E_SVR_RET_TALK_TEXT_CONTAINS_EVIL = 104,
        E_SVR_RET_VOICE_CONTAINS_SENSITIVE = 105,
        E_SVR_RET_SHARE_ROOM_IN_MATCHING = 106,
        E_SVR_RET_BE_FORBID_POST = 107,
        E_SVR_RET_BE_FORBID_AUDIO = 108,
        E_SVR_RET_BE_FORBID_SHARE_ROOM = 109,
        E_SVR_RET_CLIENT_FIGHT_LOGIC_VERSION_ERROR = 112,
        E_SVR_RET_MAIL_CANNOT_DELETE_MANUALLY = 127,
        E_SVR_RET_KICK_OUT_UIN_OTHER = 134,
        E_SVR_RET_LOGIN_OVERLOAD = 135,
        E_SVR_RET_ITEM_EXPIRE = 159,
        E_SVR_RET_ITEM_NOT_EXIST = 163,
        E_SVR_RET_MALL_GOODS_INVALID_TIME = 165,
        E_SVR_RET_MALL_GOODS_NOT_EXIST = 166,
        E_SVR_RET_MALL_GOODS_COUNT_LIMIT = 167,
        E_SVR_RET_BE_FORBID_LOGIN = 168,
        E_SVR_RET_MALL_CURRENCY_NOT_ENOUGH = 170,
        E_SVR_RET_MALL_BUY_ITEM_NOT_ENOUGH = 171,
        E_SVR_RET_TALK_WORLD_UNDER_LEVEL = 172,
        E_SVR_RET_TALK_TRUMPET_INSUFFICIENT = 173,
        E_SVR_RET_USER_NOT_ONLINE = 174,
        E_SVR_RET_INVALID_ARG = 175,
        E_SVR_RET_USER_NOT_EXIST = 176,
        E_SVR_RET_DEVICE_NOT_PERMITTED = 177,
        E_SVR_RET_DEVICE_BIND_CHECK_FAILED = 178,
        E_SVR_RET_LOGIN_TIME_NOT_PERMITTED = 179,
        E_SVR_RET_TALK_AS_GM_CMD = 180,
        E_SVR_RET_MALL_PAGE_INFO_NOT_EXIST = 181,
        E_SVR_RET_TALK_EMOTION_INSUFFICIENT = 183,
        E_SVR_RET_QUIZ_CHEAT_ITEM_INSUFFICIENT = 184,
        E_SVR_RET_USER_DEACTIVATE_ACCOUNT = 185,
        E_SVR_RET_RESULT_RECORD_IS_EXPIRED = 186,
        E_SVR_RET_GAME_RECORD_IS_EXPIRED = 187,
        E_SVR_RET_BUTLER_ACTIVITY_NO_CACHED_DATA = 190,
        E_SVR_RET_BUTLER_ACTIVITY_NOT_FOUND = 191,
        E_SVR_RET_BUTLER_ACTIVITY_DATA_NOT_FOUND = 192,
        E_SVR_RET_BUTLER_ACTIVITY_TASK_NOT_FOUND = 193,
        E_SVR_RET_BUTLER_ACTIVITY_TASK_DATA_NOT_FOUND = 194,
        E_SVR_RET_BUTLER_ACTIVITY_TASK_RECEIVED_REWARD = 195,
        E_SVR_RET_BUTLER_ACTIVITY_TASK_INCOMPLETED = 196,
        E_SVR_RET_BUTLER_ACTIVITY_INDEX_NOT_EXCHANGE = 198,
        E_SVR_RET_BUTLER_ACTIVITY_MAX_EXCHANGE_TIMES = 199,
        E_SVR_RET_BUTLER_ACTIVITY_TASK_EXPIRED = 200,
        E_SVR_RET_BUTLER_ACTIVITY_TASK_NOT_IN_REPORT_TIME_RANGE = 201,
        E_SVR_RET_SERIAL_HAS_BEEN_DEALT = 248,
        E_SVR_RET_ITEM_DIRECT_BUY_REFUSE = 249,
        E_SVR_RET_ITEM_COUNT_NOT_ENOUGH = 250,
        E_SVR_RET_ITEM_PARAM_ERROR = 251,
        E_SVR_RET_ITEM_ADD_EXP_ERROR = 252,
        E_SVR_RET_ITEM_USE_TYPE_ERROR = 253,
        E_SVR_RET_ITEM_USE_CHEST_CFG_NOT_FOUND = 254,
        E_SVR_RET_ITEM_USE_CHEST_PARAM_ERROR = 255,
        E_SVR_RET_ITEM_USE_CHEST_KEY_NOT_ENOUGH = 256,
        E_SVR_RET_ITEM_USE_CHEST_DROP_ID_NOT_FOUND = 257,
        E_SVR_RET_ITEM_USE_CHEST_DROP_FAILED = 258,
        E_SVR_RET_ITEM_USE_CHEST_DEL_FAILED = 259,
        E_SVR_RET_ITEM_USE_CHEST_ADD_ITEM_FAILED = 260,
        E_SVR_RET_ITEM_USE_NORMAL_DEL_FAILED = 261,
        E_SVR_RET_ITEM_CONSUME_ITEM_TYPE_ERROR = 263,
        E_SVR_RET_ITEM_CAN_NOT_SELL = 264,
        E_SVR_RET_ITEM_ADD_COUPON_OVERFLOW = 266,
        E_SVR_RET_ITEM_ADD_COUPON_ERROR = 267,
        E_SVR_RET_ITEM_COUNT_OVERFLOW = 268,
        E_SVR_RET_ITEM_DIRECT_BUY_SESSION_NOT_FOUND = 272,
        E_SVR_RET_ITEM_DIRECT_BUY_PARAM_ERROR = 273,
        E_SVR_RET_ITEM_DROP_ERROR = 276,
        E_SVR_RET_ITEM_CFG_NOT_FOUND = 277,
        E_SVR_RET_ITEM_GIFT_PACK_CFG_ERROR = 278,
        E_SVR_RET_ITEM_GIFT_PACK_CHOOSE_ERROR = 279,
        E_SVR_RET_ITEM_GIFT_PACK_CONSUME_ERROR = 280,
        E_SVR_RET_ITEM_GIFT_PACK_ADD_ERROR = 281,
        E_SVR_RET_ITEM_GIFT_PACK_LEVEL_ERROR = 282,
        E_SVR_RET_ITEM_DIRECT_BUY_PRICE_ERROR = 283,
        E_SVR_RET_ITEM_USE_ITEM_ADD_DAY = 284,
        E_SVR_RET_ITEM_EXPIRED = 285,
        E_SVR_RET_ITEM_IS_MSDK_MOCKER = 286,
        E_SVR_RET_ITEM_GET_COUPON_FAILED = 287,
        E_SVR_RET_ITEM_HISTORY_CHARGE_NOT_CHANGE = 289,
        E_SVR_RET_ITEM_FILL_MIDAS_PARAM_ERROR = 290,
        E_SVR_RET_ITEM_CAN_NOT_BE_USED = 291,
        E_SVR_RET_ITEM_USE_ITEM_TYPE_ERROR = 292,
        E_SVR_RET_ITEM_DATA_NOT_FOUND = 293,
        E_SVR_RET_ITEM_USE_NUM_TOO_MANY = 294,
        E_SVR_RET_EXPR_ITEM_USE_CFG_ERROR = 295,
        E_SVR_RET_EXPR_ITEM_TYPE_ERROR = 296,
        E_SVR_RET_MODIFY_TICKET_PARAM_ERROR = 297,
        E_SVR_RET_ITEM_USE_DROP_CONFIG_BY_USE_COUNT_ERROR = 298,
        E_SVR_RET_ITEM_UNIMPLEMENTED_PROTO = 299,
        E_SVR_RET_MALL_GOODS_ITEM_CFG_NOT_EXIST = 300,
        E_SVR_RET_MALL_NOT_IN_PAGE_BUY_TIME = 302,
        E_SVR_RET_MALL_ITEM_NUM_CFG_ERROR = 303,
        E_SVR_RET_MALL_QUERY_DIRECT_BUY_IS_IN_CD = 304,
        E_SVR_RET_ORANGE_GEM_EXCHANGE_RATE_ERROR = 306,
        E_SVR_RET_ORANGE_GEM_EXCHANGE_CALC_ERROR = 307,
        E_SVR_RET_MALL_BUY_TICKET_NOT_ENOUGH = 308,
        E_SVR_RET_MALL_BUY_CONSUME_ERROR = 309,
        E_SVR_RET_MALL_CHEST_CFG_NOT_FOUND = 310,
        E_SVR_RET_MALL_PAGE_CFG_NOT_FOUND = 311,
        E_SVR_RET_MALL_CHEST_DROP_CFG_NOT_FOUND = 312,
        E_SVR_RET_MALL_CHEST_NOT_ON_SALE = 313,
        E_SVR_RET_MALL_CHEST_CURRENCY_CFG_NOT_FOUND = 314,
        E_SVR_RET_MALL_CHEST_OPEN_CFG_ERROR = 315,
        E_SVR_RET_MALL_CHEST_DROP_ERROR = 316,
        E_SVR_RET_MALL_CHEST_GIVE_ERROR = 317,
        E_SVR_RET_MALL_BUY_CURRENCY_ERROR = 318,
        E_SVR_RET_MALL_BUY_LIMIT_SIZE_OVERFLOW = 319,
        E_SVR_RET_MALL_QUERY_DIRECT_BUY_NOT_FINISHED = 320,
        E_SVR_RET_MALL_QUERY_DIRECT_BUY_NOT_FOUND = 321,
        E_SVR_RET_MALL_BUY_EXCHANGE_PARAM_ERROR = 322,
        E_SVR_RET_MALL_BUY_PARAM_ERROR = 323,
        E_SVR_RET_MALL_EGG_CFG_NOT_EXIST = 324,
        E_SVR_RET_MALL_EGG_DROP_ERROR = 325,
        E_SVR_RET_MALL_EGG_CONFIRM_TRANSFORM = 326,
        E_SVR_RET_MALL_EGG_CAN_NOT_DROP = 327,
        E_SVR_RET_MALL_CONDITION_HAVE_ITEM_NOT_OK = 328,
        E_SVR_RET_MALL_ALREADY_HAVE_PERMANENT_ITEM = 329,
        E_SVR_RET_MALL_SHOULD_HIDE = 330,
        E_SVR_RET_DROP_CFG_NOT_FOUND = 331,
        E_SVR_RET_DROP_TYPE_PROBABILITY_NOT_SUPPORTED = 332,
        E_SVR_RET_DROP_PROBABILITY_ERROR = 333,
        E_SVR_RET_DROP_NO_RETURN_DROP_EMPTY = 334,
        E_SVR_RET_DROP_GIFT_PACK_EMPTY = 335,
        E_SVR_RET_DROP_CHEST_EMPTY = 336,
        E_SVR_RET_DROP_EGG_EMPTY = 337,
        E_SVR_RET_DROP_CFG_BY_ITEM_NOT_SUPPORTED = 338,
        E_SVR_RET_DROP_CFG_BY_ITEM_NOT_FOUND = 339,
        E_SVR_RET_JTECHNICAL_NO_FOUND_TASK = 341,
        E_SVR_RET_JTECHNICAL_HAVE_ALREADY_RECEIVED_REWARD = 342,
        E_SVR_RET_JTECHNICAL_REQUIREMENTS_ERROR = 343,
        E_SVR_RET_JTECHNICAL_CONF_ERROR = 344,
        E_SVR_RET_JTECHNICAL_BUY_COUNT_ERROR = 345,
        E_SVR_RET_JTECHNICAL_CHEST_CFG_NOT_FOUND = 346,
        E_SVR_RET_JTECHNICAL_SEND_REWARD_ERROR = 347,
        E_SVR_RET_JTECHNICAL_LOTTERY_COUNT_LIMIT = 348,
        E_SVR_RET_JTECHNICAL_NO_FOUND_CHEST_CONF = 349,
        E_SVR_RET_JTECHNICAL_NOT_ON_SALE = 350,
        E_SVR_RET_SEASON_LOTTERY_NO_FOUND_CONF = 361,
        E_SVR_RET_SEASON_LOTTERY_REQ_PARAM_ERROR = 362,
        E_SVR_RET_SEASON_LOTTERY_ITEM_NOT_ON_SALE = 363,
        E_SVR_RET_SEASON_LOTTERY_CONSUME_ITEM_NOT_ENOUGH = 364,
        E_SVR_RET_SEASON_LOTTERY_SEND_REWARD_ERROR = 365,
        E_SVR_RET_SEASON_LOTTERY_CALC_REWARD_LEVEL_ERROR = 366,
        E_SVR_RET_SEASON_LOTTERY_CREATE_REWARD_LEVEL_PRIZEL_POOL_ERROR = 367,
        E_SVR_RET_SEASON_LOTTERY_CREATE_REWARD_ITEM_PRIZEL_POOL_ERROR = 368,
        E_SVR_RET_SEASON_LOTTERY_CALC_REWARD_ITEM_ERROR = 369,
        E_SVR_RET_SEASON_LOTTERY_DRAW_ERROR = 370,
        E_SVR_RET_SEASON_LOTTERY_CALC_CONSUME_ERROR = 371,
        E_SVR_RET_SEASON_LOTTERY_LOTTERY_COUNT_LIMIT = 372,
        E_SVR_RET_MALL_DIRECT_BUY_GOODS_META_TOO_LONG = 391,
        E_SVR_RET_MALL_QUERY_DIRECT_BUY_IS_IN_AFTER_PAY_CD = 392,
        E_SVR_RET_INVALID_ROOMID = 501,
        E_SVR_RET_NOT_FIND_PLAYER = 504,
        E_SVR_RET_SIT_AREA_ROOM_NOT_EXIST = 514,
        E_SVR_RET_SITDOWN_BRGAME_FORBID_OB = 523,
        E_SVR_RET_NO_FRAMES_IN_RANGE = 601,
        E_SVR_RET_NO_FRAME_DATA_FOUND = 602,
        E_SVR_RET_WATCH_FORBIDDEN = 603,
        E_SVR_RET_NO_PLAY_STATE_YET = 604,
        E_SVR_RET_GAME_WATCHER_NUM_LIMIT = 605,
        E_SVR_RET_PLAYER_NOT_FOUND = 606,
        E_SVR_RET_PLAY_STATE_REPORTED = 607,
        E_SVR_RET_NOT_BRGAME = 608,
        E_SVR_RET_BRGAME_NO_ROUND_GROUP_DATA = 610,
        E_SVR_RET_PLAYER_ALREADY_BALANCED = 611,
        E_SVR_RET_PLAYER_FIGHT_LOGIC_VERSION_NOT_MATCH = 612,
        E_SVR_RET_WATCH_GAME_ENDED = 613,
        E_SVR_RET_CLIENT_PLAYSTATE_UP_TO_DATE = 614,
        E_SVR_RET_GET_RSARPIVATE_KEY_FAILED = 1001,
        E_SVR_RET_RSA_DECRYPT_ERROR = 1002,
        E_SVR_RET_PROTOSECRETKEY_ENCRYPT_ERROR = 1003,
        E_SVR_RET_SIG_ENCRYPT_ERROR = 1004,
        E_SVR_RET_FRIEND_MATCH_SYS_FAIL = 4100,
        E_SVR_RET_FRIEND_MATCH_PASSWORD_INVALID = 4101,
        E_SVR_RET_FRIEND_MATCH_BE_OWNER_KICKOFF = 4102,
        E_SVR_RET_FRIEND_MATCH_BE_TIMEOUT_DISBAND = 4104,
        E_SVR_RET_FRIEND_MATCH_BE_FORBID = 4105,
        E_SVR_RET_FRIEND_MATCH_FRIEND_EXIST_INCONSISTENT = 4107,
        E_SVR_RET_FRIEND_MATCH_BE_ALREADY_BEGIN = 4108,
        E_SVR_RET_FRIEND_MATCH_PARAM_ERROR = 4109,
        E_SVR_RET_TURBO_MATCH_CLOSED = 4112,
        E_SVR_RET_MATCH_NEED_COMPLETE_SEGMENT_RATING = 4111,
        E_SVR_RET_ELO_MATCH_CLOSED = 4113,
        E_SVR_RET_SEGMENT_DIRTY_READ = 4114,
        E_SVR_RET_MATCH_BEBAN = 4115,
        E_SVR_RET_MATCH_FIGHTLOGICVERSION_LOW = 4116,
        E_SVR_RET_MATCH_FIGHTLOGICVERSION_HIGH = 4117,
        E_SVR_RET_MATCH_RANK_USER_LEVEL_CONFLICT = 4118,
        E_SVR_RET_MATCH_ROOM_MEMBER_COUNT_LIMIT = 4119,
        E_SVR_RET_MATCH_FIGHT_LOGIC_VERSION_CONFLICT = 4120,
        E_SVR_RET_MATCH_FIGHT_LOGIC_VERSION_NOT_MATCH = 4121,
        E_SVR_RET_FRIEND_MATCH_CROSS_ZONE = 4123,
        E_SVR_RET_FRIEND_MATCH_USER_BE_FORBID = 4125,
        E_SVR_RET_FRIEND_MATCH_OBSERVER_FORBID = 4127,
        E_SVR_RET_FRIEND_MATCH_NOT_ENOUGH_PLAYER = 4128,
        E_SVR_RET_MATCH_INVITE_NO_ROOM_HOST_NOT_MATCH_CONDITION = 4129,
        E_SVR_RET_MATCH_INVITE_NO_ROOM_SELF_IN_ROOM = 4130,
        E_SVR_RET_MATCH_INVITE_NO_ROOM_TARGET_IN_ROOM = 4132,
        E_SVR_RET_MATCH_INVITE_NO_ROOM_PARAM_ERROR = 4133,
        E_SVR_RET_MATCH_DESERIALIZE = 4134,
        E_SVR_RET_MATCH_INVITE_NO_ROOM_ANSWER_PARAM_ERROR = 4135,
        E_SVR_RET_MATCH_INVITE_NO_ROOM_INVITE_TIMEOUT = 4136,
        E_SVR_RET_MATCH_NOT_IN_ROOM = 4139,
        E_SVR_RET_MATCH_SCENE_TYPE_MISMATCH = 4141,
        E_SVR_RET_MATCH_INVALID_SEAT_ID = 4142,
        E_SVR_RET_MATCH_WAIT_FOR_SWITCH_SEAT_CONFIRMATION = 4144,
        E_SVR_RET_MATCH_PENDING_REQUEST_NOT_FOUND = 4146,
        E_SVR_RET_MATCH_SEAT_CHANGED = 4147,
        E_SVR_RET_MATCH_UPDATE_CACHE_FAILED = 4148,
        E_SVR_RET_MATCH_SWITCH_TO_OWN_SEAT = 4149,
        E_SVR_RET_MATCH_PENDING_SWITCH_SEAT_REQUEST_BY_SELF = 4150,
        E_SVR_RET_MATCH_PENDING_SWITCH_SEAT_REQUEST_BY_OTHERS = 4151,
        E_SVR_RET_MATCH_SWITCH_SEAT_REQUEST_HANDLED = 4152,
        E_SVR_RET_MATCH_SWITCH_SEAT_REQUEST_EXPIRED = 4153,
        E_SVR_RET_MATCH_TARGET_HAS_PENDING_SWITCH_SEAT_REQUEST = 4154,
        E_SVR_RET_MATCH_SWITCH_SEAT_CD = 4155,
        E_SVR_RET_MATCH_IN_OTHER_MATCH_ROOM = 4156,
        E_SVR_RET_MATCH_END_WAITING_STARTGAME = 4157,
        E_SVR_RET_MATCH_SHARE_ROOM_TOO_FAST = 4158,
        E_SVR_RET_MATCH_TIER_MEMBER_TOO_MANY = 4159,
        E_SVR_RET_MATCH_PLAYER_REJECT_MATCH = 4160,
        E_SVR_RET_MATCH_PLAYER_CANCEL = 4161,
        E_SVR_RET_MATCH_PLAYER_DIFF_VERSION = 4162,
        E_SVR_RET_MATCH_TIMEOUT_BACKROOM = 4163,
        E_SVR_RET_MATCH_BE_INVITED_BEBAN = 4165,
        E_SVR_RET_MATCH_IN_OTHER_ROOM = 4166,
        E_SVR_RET_MATCH_TURBO_LP_CONFLICT = 4168,
        E_SVR_RET_MATCH_INVALID_SCENE_TYPE = 4169,
        E_SVR_RET_MATCH_IN_MATCH_INVITE_IS_CD = 4170,
        E_SVR_RET_MATCH_IN_MATCH_INVITE_IS_NOT_FRIEND = 4171,
        E_SVR_RET_MATCH_IN_MATCH_INVITE_TIMEOUT = 4172,
        E_SVR_RET_MATCH_RANK_DATA_SEASON_CONFLICT = 4173,
        E_SVR_RET_MATCH_IN_MATCH_INVITE_CHECK_CLUB_RELATION_FAIL = 4174,
        E_SVR_RET_MATCH_IN_MATCH_INVITE_RELATION_TYPE_INVALID = 4175,
        E_SVR_RET_MATCH_SET_INDEX_CONFLICT = 4176,
        E_SVR_RET_MATCH_PLAY_AGAIN_ENTER_ROOM_FAIL = 4177,
        E_SVR_RET_MATCH_REFUSE_PLAY_AGAIN_FAIL = 4178,
        E_SVR_RET_OWNER_BEGIN_PLAYER_OFFLINE = 4179,
        E_SVR_RET_OWNER_BEGIN_PLAYER_NOT_IN_MATCH_ROOM = 4180,
        E_SVR_RET_FRIEND_SYS_SELF_FRIEND_LIMIT = 4201,
        E_SVR_RET_FRIEND_SYS_TARGET_FRIEND_LIMIT = 4202,
        E_SVR_RET_FRIEND_SYS_AGREE_PROTOCOL_ERROR = 4203,
        E_SVR_RET_FRIEND_SYS_AGREE_ALL_SELF_FRIEND_LIMIT = 4204,
        E_SVR_RET_FRIEND_SYS_AGREE_ALL_APPLICANT_FRIEND_LIMIT = 4205,
        E_SVR_RET_FRIEND_SYS_APPLY_IS_ALREADY_FRIEND = 4206,
        E_SVR_RET_FRIEND_SYS_AGREE_SINGLE_SELF_FRIEND_LIMIT = 4207,
        E_SVR_RET_FRIEND_SYS_AGREE_SINGLE_APPLICANT_FRIEND_LIMIT = 4208,
        E_SVR_RET_FRIEND_SYS_DEL_IS_NOT_FRIEND = 4209,
        E_SVR_RET_FRIEND_SYS_ADD_BLACK_IS_NOT_FRIEND = 4210,
        E_SVR_RET_FRIEND_SYS_ADD_BLACK_IS_ALREADY_BLACK = 4211,
        E_SVR_RET_FRIEND_SYS_ADD_BLACK_IS_FULL = 4212,
        E_SVR_RET_FRIEND_SYS_REMOVE_BLACK_IS_NOT_BLACK = 4213,
        E_SVR_RET_FRIEND_SYS_REMOVE_BLACK_IS_ALREADY_FRIEND = 4214,
        E_SVR_RET_FRIEND_SYS_REMOVE_BLACK_IS_FULL = 4215,
        E_SVR_RET_FRIEND_SYS_SEARCH_NOT_FOUND = 4217,
        E_SVR_RET_FRIEND_SYS_INVALID_RANK_TYPE = 4219,
        E_SVR_RET_FRIEND_SYS_RANK_PAGE_TOO_MANY = 4220,
        E_SVR_RET_FRIEND_SYS_ERROR = 4221,
        E_SVR_RET_FRIEND_SYS_APPLY_IS_SELF = 4222,
        E_SVR_RET_FRIEND_SYS_APPLY_IS_IN_SELF_BLACK = 4223,
        E_SVR_RET_FRIEND_SYS_SESSION_NOT_FOUND = 4224,
        E_SVR_RET_FRIEND_SYS_REFRESH_UID_IS_NOT_FRIEND = 4225,
        E_SVR_RET_FRIEND_SYS_SEARCH_NAME_DIRTY = 4226,
        E_SVR_RET_BE_FORBID_ADD_FRIEND = 4229,
        E_SVR_RET_FRIEND_SYS_APPLY_LIST_EMPTY = 4230,
        E_SVR_RET_FRIEND_NO_FRIEND_RELATION = 4231,
        E_SVR_RET_FRIEND_SYS_BE_FORBID_RANK = 4232,
        E_SVR_RET_MAIL_GET_MAIL_LIST_ERROR = 4254,
        E_SVR_RET_MAIL_GET_SINGLE_MAIL_ERROR = 4255,
        E_SVR_RET_MAIL_UPDATE_SINGLE_MAIL_ERROR = 4256,
        E_SVR_RET_MAIL_DEL_MAIL_ERROR_ATTACH_NOT_GET = 4258,
        E_SVR_RET_MAIL_DEL_MAIL_ERROR = 4259,
        E_SVR_RET_MAIL_GET_ALL_MAIL_ITEM_ERROR = 4260,
        E_SVR_RET_MAIL_IS_EXPIRE = 4261,
        E_SVR_RET_MAIL_IS_ALREADY_FETCH = 4262,
        E_SVR_RET_MAIL_CREATE_MAIL_ITEM_ERROR = 4263,
        E_SVR_RET_FILL_MAIL_CONFIG_ERROR = 4264,
        E_SVR_RET_MAIL_ADDRESS_DIRTY_ERROR = 4265,
        E_TAC_SVR_RET_RECEIVE_DAILY_ACTIVE_TASK_INCOMPLETE = 4301,
        E_TAC_SVR_RET_RECEIVE_DAILY_ACTIVE_TASK_RECEIVED = 4302,
        E_TAC_SVR_RET_RECEIVE_DAILY_ACTIVE_REWARD_NOTFOUND = 4307,
        E_TAC_SVR_RET_NOVICE_GUIDE_GIFT_ALREADY_GET = 4401,
        E_TAC_SVR_RET_NOVICE_GUIDE_GIFT_NOT_CONFIG = 4402,
        E_TAC_SVR_RET_NOVICE_GUIDE_MISSION_NOT_PASS = 4404,
        E_TAC_SVR_RET_GET_BATTLEPASS_PAY_CONFIG_ERROR = 4501,
        E_TAC_SVR_RET_BATTLEPASS_HAVE_PAY_PASS = 4502,
        E_TAC_SVR_RET_BATTLEPASS_NO_PAY_PASS = 4503,
        E_TAC_SVR_RET_BATTLEPASS_LVREWARD_CONFIG_ERROR = 4504,
        E_TAC_SVR_RET_BATTLEPASS_LVREWARD_RECEIVED = 4505,
        E_TAC_SVR_RET_BATTLEPASS_SEASON_NOT_IN_OPEN_TIME = 4506,
        E_TAC_SVR_RET_BP_REFRESH_SEASON_ERROR = 4511,
        E_TAC_SVR_RET_BP_TASK_CFG_NOT_FOUND = 4513,
        E_TAC_SVR_RET_BP_CFG_ERROR = 4514,
        E_TAC_SVR_RET_BP_MALL_CFG_NOT_FOUND = 4515,
        E_TAC_SVR_RET_BP_MALL_PARAM_ERROR = 4516,
        E_TAC_SVR_RET_BP_MALL_CFG_ERROR = 4517,
        E_TAC_SVR_RET_BP_MALL_NOT_ON_SALE = 4518,
        E_TAC_SVR_RET_BP_MALL_BUY_LIMIT = 4519,
        E_TAC_SVR_RET_BP_MALL_NEED_ADVANCED_BP = 4520,
        E_TAC_SVR_RET_BP_MALL_LEVEL_NOT_FULL = 4521,
        E_TAC_SVR_RET_BP_MALL_EXP_NOT_ENOUGH = 4522,
        E_TAC_SVR_RET_BP_MODULE_NOT_OPEN = 4523,
        E_TAC_SVR_RET_BP_MALL_LEVEL_IS_FULL = 4524,
        E_TAC_SVR_RET_BP_LEVEL_CFG_NOT_FOUND = 4525,
        E_TAC_SVR_RET_BP_BUY_LEVEL_EXCEED_MAX = 4526,
        E_TAC_SVR_RET_BP_BUY_LEVEL_CALC_FAILED = 4527,
        E_TAC_SVR_RET_BP_BUY_LEVEL_CFG_ERROR = 4528,
        E_TAC_SVR_RET_SEASON_TASK_REWARD_RECEIVED = 4670,
        E_TAC_SVR_RET_SEASON_TASK_INCOMPLETE = 4671,
        E_SVR_RET_TURBO_SEASON_TASK_NOT_EXIST = 4680,
        E_SVR_RET_TURBO_SEASON_TASK_REWARD_RECEIVED = 4681,
        E_SVR_RET_TURBO_SEASON_TASK_INCOMPLETE = 4682,
        E_SVR_RET_NICKNAME_CONTAINS_SPACE = 4699,
        E_TAC_SVR_RET_MOD_USERINFO_NICKNAME_LIMIT = 4700,
        E_TAC_SVR_RET_MOD_USERINFO_NICKNAME_DIRTY = 4701,
        E_TAC_SVR_RET_MOD_USERINFO_NICKNAME_TOO_SHORT = 4702,
        E_TAC_SVR_RET_MOD_USERINFO_NICKNAME_TOO_LONG = 4703,
        E_TAC_SVR_RET_RENAME_COIN_NOT_ENOUGH = 4705,
        E_TAC_SVR_RET_RENAME_TIME_LIMIT = 4706,
        E_TAC_SVR_RET_RENAME_NAME_IS_SAME = 4707,
        E_SVR_RET_NICKNAME_NOT_UTF8 = 4708,
        E_SVR_RET_FORBID_MODIFY_NICKNAME = 4709,
        E_SVR_RET_NAME_CARD_NOT_ENOUGH = 4710,
        E_TAC_SVR_RET_AI_USER_CANNOT_LOOK = 4711,
        E_TAC_SVR_RET_NOT_HAVE_AVATAR = 4712,
        E_TAC_SVR_RET_AVATAR_DEL_ERROR = 4713,
        E_SVR_RET_AVATAR_BOX_CONFIG_NOT_EXIST = 4714,
        E_SVR_RET_AVATAR_NOT_EXIST = 4715,
        E_SVR_RET_AVATAR_CONFIG_NOT_EXIST = 4716,
        E_TAC_SVR_RET_GM_CMD_NOT_FOUND = 4743,
        E_TAC_SVR_RET_GM_SU_NOT_INIT = 4744,
        E_TAC_SVR_RET_GM_SU_FAILED = 4745,
        E_TAC_SVR_RET_NOVICE_TASK_CONFIG_ERROR = 4754,
        E_TAC_SVR_RET_MODIFY_USERBATTLE_NAME = 4761,
        E_TAC_SVR_RET_GET_ZONE_RANK_BE_FORBID = 4773,
        E_TAC_SVR_RET_GET_RANK_TYPE_SEASON_NOT_FOUND = 4774,
        E_TAC_SVR_RET_TINY_SESSION_NOT_FOUND = 4900,
        E_TAC_SVR_RET_TINY_SET_REQ_HERO_NOT_FOUND = 4903,
        E_TAC_SVR_RET_TINY_SET_EXPRESSION_REQ_PARAM_ERROR = 4912,
        E_TAC_SVR_RET_TINY_SET_EXPRESSION_REQ_NOT_FOUND = 4913,
        E_TAC_SVR_RET_TINY_SET_MAP_REQ_MAP_NOT_FOUND = 4914,
        E_TAC_SVR_RET_TINY_SET_MAP_REQ_DOOR_NOT_FOUND = 4915,
        E_TAC_SVR_RET_TINY_SET_MAP_REQ_DAMAGE_NOT_FOUND = 4916,
        E_TAC_SVR_RET_TINY_SET_MAP_REQ_FOOT_NOT_FOUND = 4917,
        E_TAC_SVR_RET_TINY_SET_MAP_REQ_ID_ERROR = 4918,
        E_TAC_SVR_RET_TINY_HERO_CFG_NOT_FOUND = 4921,
        E_TAC_SVR_RET_TINY_EXPIRED = 4932,
        E_TAC_SVR_RET_ADD_TINY_HERO_LEVEL_ERROR = 4933,
        E_TAC_SVR_RET_ADD_TINY_EXPRESSION_PARAM_ERROR = 4934,
        E_TAC_SVR_RET_ADD_TINY_EQUIP_PARAM_ERROR = 4935,
        E_TAC_SVR_RET_TINY_DEL_ERROR = 4936,
        E_TAC_SVR_RET_TINY_HERO_NOT_FOUND = 4937,
        E_TAC_SVR_RET_EXPR_TINY_HERO_CAN_NOT_UPGRADE = 4938,
        E_TAC_SVR_RET_TINY_HERO_NEXT_LEVEL_CFG_NOT_FOUND = 4939,
        E_TAC_SVR_RET_TINY_HERO_NEXT_LEVEL_ALREADY_HAVE = 4940,
        E_TAC_SVR_RET_TINY_HERO_UPGRADE_ITEM_NOT_MATCH = 4941,
        E_TAC_SVR_RET_TINY_HERO_CAN_NOT_UPGRADE_BY_ITEM = 4942,
        E_TAC_SVR_RET_TINY_DAMAGE_CFG_NOT_FOUND = 4943,
        E_TAC_SVR_RET_TINY_NEED_USE_EXCLUSIVE_HERO = 4944,
        E_TAC_SVR_RET_TINY_SET_TINY_ELIMINATE_NOT_FOUND = 4945,
        E_TAC_SVR_RET_TINY_SET_TINY_ELIMINATE_ALREADY_USE = 4946,
        E_TAC_SVR_RET_TINY_ELIMINATE_NEED_USE_EXCLUSIVE_HERO = 4947,
        E_TAC_SVR_RET_TINY_SET_MAP_REQ_ITEM_NOT_FOUND = 4948,
        E_TAC_SVR_RET_TINY_FETTER_CANNOT_CRAFT_EMBLEM = 4955,
        E_SVR_RET_ACTIVITY_CONFIG_NOT_FOUND = 5003,
        E_SVR_RET_ACTIVITY_NOT_OPEN = 5004,
        E_SVR_RET_ACTIVITY_EXPIRED = 5005,
        E_SVR_RET_ACTIVITY_LEVEL_LIMIT = 5006,
        E_SVR_RET_ACTIVITY_TASK_CONFIG_NOT_FOUND = 5007,
        E_SVR_RET_ACTIVITY_TASK_STATUS_NOT_FOUND = 5008,
        E_SVR_RET_ACTIVITY_TASK_RECEIVED_REWARD = 5009,
        E_SVR_RET_ACTIVITY_TASK_INCOMPLETED = 5010,
        E_SVR_RET_ACTIVITY_INVALID_ACTIVITY_ID = 5011,
        E_SVR_RET_ACTIVITY_TASK_LOCKED = 5012,
        E_SVR_RET_ACTIVITY_PLAT_NOT_MATCH = 5013,
        E_SVR_RET_ACTIVITY_MOBILE_NOT_MATCH = 5014,
        E_SVR_RET_ACCUMULATE_CHARGE_CFG_NOT_FOUND = 5043,
        E_SVR_RET_ACCUMULATE_CHARGE_NOT_ACTIVATE = 5044,
        E_SVR_RET_ACCUMULATE_CHARGE_PARAM_ERROR = 5045,
        E_SVR_RET_ACCUMULATE_CHARGE_NOT_FINISHED = 5046,
        E_SVR_RET_ACCUMULATE_CHARGE_ALREADY_ACHIEVED = 5047,
        E_SVR_RET_MIDAS_GET_PAY_SERVICE_FAIL = 5061,
        E_SVR_RET_MIDAS_GET_QUERY_REQ_FAIL = 5062,
        E_SVR_RET_MIDAS_GET_PAY_REQ_FAIL = 5063,
        E_SVR_RET_MIDAS_GET_PRESENT_REQ_FAIL = 5064,
        E_SVR_RET_MIDAS_GET_CANCEL_REQ_FAIL = 5065,
        E_SVR_RET_MIDAS_GET_BUY_GOODS_REQ_FAIL = 5066,
        E_SVR_RET_MIDAS_GET_ITEM_PARAM_ERROR = 5068,
        E_SVR_RET_MIDAS_GET_USER_STATE_ERROR = 5069,
        E_SVR_RET_MIDAS_GIVE_ITEM_ERROR = 5070,
        E_SVR_RET_MIDAS_GET_DISTRIBUTE_ANSWER_FAIL = 5071,
        E_SVR_RET_MIDAS_APOLLO_TIMEOUT = 5075,
        E_SVR_RET_MIDAS_APOLLO_COMMON_ERROR = 5076,
        E_SVR_RET_MIDAS_TOKEN_EXPIRED = 5077,
        E_SVR_RET_MIDAS_PARAM_ERROR = 5079,
        E_SVR_RET_MIDAS_SHOULD_RETRY = 5080,
        E_SVR_RET_MIDAS_SHOULD_RETRY_AND_IS_OK = 5081,
        E_SVR_RET_MIDAS_QUERY_COMMON_ERROR = 5082,
        E_SVR_RET_MIDAS_PAY_COMMON_ERROR = 5083,
        E_SVR_RET_MIDAS_PRESENT_COMMON_ERROR = 5084,
        E_SVR_RET_MIDAS_CANCEL_COMMON_ERROR = 5085,
        E_SVR_RET_MIDAS_BUY_GOODS_ORDER_COMMON_ERROR = 5086,
        E_SVR_RET_MIDAS_COMMON_ERROR = 5087,
        E_SVR_RET_MIDAS_TICKET_NOT_ENOUGH = 5088,
        E_SVR_RET_MIDAS_RPC_FAIL = 5089,
        E_SVR_RET_MIDAS_CAN_RETRY_QUERY = 5090,
        E_SVR_RET_MIDAS_TOKEN_EXPIRED_NO_ALARM = 5091,
        E_SVR_RET_MIDAS_BUY_GOODS_SYSTEM_ERROR = 5092,
        E_SVR_RET_SCORE_TOO_SMALL_NOT_TALK = 6001,
        E_SVR_RET_SCORE_TOO_SMALL_NOT_ADD_FRIEND = 6002,
        E_SVR_RET_SCORE_TOO_SMALL_NOT_BE_ADD_FRIEND = 6004,
        E_SVR_RET_SCORE_TOO_SMALL_NOT_RENAME = 6005,
        E_SVR_RET_SCORE_TOO_SMALL_NOT_SHARE_ROOM = 6006,
        E_SVR_RET_SCORE_TOO_SMALL_NOT_CREATE_CLUB = 6007,
        E_SVR_RET_SCORE_TOO_SMALL_NOT_SHARE_CLUB = 6008,
        E_SVR_RET_FRIEND_APPOINTMENT_ALREADY_GLOBAL_REFUSE = 6031,
        E_SVR_RET_FRIEND_APPOINTMENT_SYS_ERROR = 6032,
        E_SVR_RET_FRIEND_APPOINTMENT_REFUSE_NOT_IN_GAME = 6033,
        E_SVR_RET_FRIEND_APPOINTMENT_NOT_ONLINE = 6034,
        E_SVR_RET_FRIEND_APPOINTMENT_IS_NOT_FRIEND = 6035,
        E_SVR_RET_FRIEND_APPOINTMENT_TARGET_NOT_IN_GAME = 6036,
        E_SVR_RET_FRIEND_APPOINTMENT_SEND_MSG_ERROR = 6037,
        E_SVR_RET_FRIEND_APPOINTMENT_IS_REFUSE = 6038,
        E_SVR_RET_FRIEND_APPOINTMENT_GAME_IS_ALREADY_END = 6039,
        E_SVR_RET_CONSECUTIVE_DAY_CHALLENGE_NOT_FOUND = 6060,
        E_SVR_RET_CONSECUTIVE_DAY_CHALLENGE_RECEIVED_REWARD = 6061,
        E_SVR_RET_CONSECUTIVE_DAY_CHALLENGE_INCOMPLETED = 6062,
        E_SVR_RET_CONSECUTIVE_DAY_CHALLENGE_NO_CONFIG = 6066,
        E_SVR_RET_CONSECUTIVE_DAY_CHALLENGE_NOT_OPENED = 6067,
        E_SVR_RET_LINE_UP_CHALLENGE_NOT_OPEN = 6081,
        E_SVR_RET_LINE_UP_CHALLENGE_CONFIG_ERROR = 6082,
        E_SVR_RET_LINE_UP_CHALLENGE_LEVEL_LIMIT = 6083,
        E_SVR_RET_LINE_UP_CHALLENGE_LINE_UP_NOT_EXIST = 6084,
        E_SVR_RET_LINE_UP_CHALLENGE_DATA_EXPIRED = 6085,
        E_SVR_RET_LINE_UP_CHALLENGE_LINE_UP_IS_LOCK = 6086,
        E_SVR_RET_ENTRANCE_NOT_OPEN = 6141,
        E_SVR_RET_ENTRANCE_TODAY_TIMES_LIMIT = 6142,
        E_SVR_RET_LOTTERY_MAX_DRAW_NUM_REACHED = 6161,
        E_SVR_RET_LOTTERY_MAX_DRAW_NUM_PER_DAY_REACHED = 6162,
        E_SVR_RET_LOTTERY_TOKEN_NOT_ENOUGH = 6163,
        E_SVR_RET_LOTTERY_TOKEN_CONSUME_ERROR = 6164,
        E_SVR_RET_LOTTERY_DRAW_ERROR = 6165,
        E_SVR_RET_LOTTERY_CONFIG_NOT_FOUND = 6166,
        E_SVR_RET_ALL_TOP_REWARDS_ACQUIRED = 6167,
        E_SVR_RET_DAILY_LOGIN_CONFIG_ERROR = 6181,
        E_SVR_RET_DAILY_LOGIN_LEVEL_LIMIT = 6182,
        E_SVR_RET_DAILY_LOGIN_REQ_PARAM_ERROR = 6183,
        E_SVR_RET_DAILY_LOGIN_MAKE_UP_TIMES_NOT_ENOUGH = 6184,
        E_SVR_RET_DAILY_LOGIN_MAKE_UP_ITEM_NOT_ENOUGH = 6185,
        E_SVR_RET_DAILY_LOGIN_WEEKLY_REWARD_RECEIVED = 6186,
        E_SVR_RET_DAILY_LOGIN_NOT_OPEN = 6187,
        E_SVR_RET_DAILY_LOGIN_WEEKLY_REWARD_TIME_ERROR = 6188,
        E_SVR_RET_DAILY_LOGIN_CLAIM_WEEKLY_REWARD_LOGIN_NOT_ENOUGH = 6189,
        E_SVR_RET_CONSECUTIVE_DAY_TASK_NOT_FOUND = 6201,
        E_SVR_RET_CONSECUTIVE_DAY_TASK_RECEIVED_REWARD = 6202,
        E_SVR_RET_CONSECUTIVE_DAY_TASK_INCOMPLETED = 6203,
        E_SVR_RET_CONSECUTIVE_DAY_TASK_NO_CONFIG = 6207,
        E_SVR_RET_CONSECUTIVE_DAY_TASK_NOT_OPENED = 6208,
        E_SVR_RET_CONSECUTIVE_DAY_TASK_GET_REWARD_TIME_ERROR = 6209,
        E_SVR_RET_SKYRIM_TRANING_ACC_REWARD_NOT_FOUND = 6226,
        E_SVR_RET_SKYRIM_TRANING_REWARD_ACTSCORE_NOT_ENOUGH = 6227,
        E_SVR_RET_DAILY_FIRST_WIN_NO_CONFIG = 6241,
        E_SVR_RET_DAILY_FIRST_WIN_RECEIVED_REWARD = 6243,
        E_SVR_RET_DAILY_FIRST_WIN_TASK_INCOMPLETED = 6244,
        E_SVR_RET_ZONE_REGISTER_NUM_LIMIT = 6261,
        E_SVR_RET_CHANNEL_REGISTER_NUM_LIMIT = 6262,
        E_SVR_RET_ACTIVITY_CENTER_NOT_OPEN = 6271,
        E_SVR_RET_BANNER_NOT_VALILD_CONFIG = 6272,
        E_SVR_RET_WEEKEND_PARTY_NOT_FOUND = 6281,
        E_SVR_RET_WEEKEND_PARTY_RECEIVED_REWARD = 6282,
        E_SVR_RET_WEEKEND_PARTY_INCOMPLETED = 6283,
        E_SVR_RET_WEEKEND_PARTY_GET_REWARD_TIME_ERROR = 6289,
        E_SVR_RET_BOUNTY_SOMEONE_TICKET_NOT_ENOUGH = 6301,
        E_SVR_RET_BOUNTY_OWNER_TICKET_NOT_ENOUGH = 6302,
        E_SVR_RET_COMMON_LOGIN_CONFIG_NOT_FOUND = 6421,
        E_SVR_RET_COMMON_LOGIN_INIT_ACTIVITY_DATA_FAILED = 6422,
        E_SVR_RET_COMMON_TASK_ACTIVITY_RECEIVED_REWARD = 6441,
        E_SVR_RET_COMMON_TASK_ACTIVITY_INCOMPLETED = 6442,
        E_SVR_RET_NAME_RECOMMEND_DB_ERROR = 6480,
        E_SVR_RET_RECHARGE_CONFIG_NOT_FOUND = 6500,
        E_SVR_RET_FEAST_OF_WIND_NO_FOUND_CONF = 6510,
        E_SVR_RET_FEAST_OF_WIND_COUNT_LIMIT_TOTAL = 6511,
        E_SVR_RET_FEAST_OF_WIND_COUNT_LIMIT_TODAY = 6512,
        E_SVR_RET_FEAST_OF_WIND_CONSUME_ITEM_ERROR = 6513,
        E_SVR_RET_FEAST_OF_WIND_NOT_IN_OPEN_TIME = 6514,
        E_SVR_RET_FEAST_OF_WIND_CONSUME_ITEM_NOT_ENOUGH = 6515,
        E_SVR_RET_FEAST_OF_WIND_MSG_PARAM_ERROR = 6516,
        E_SVR_RET_INVALID_LINEUP = 6530,
        E_SVR_RET_DIRTY_TITLE = 6531,
        E_SVR_RET_LINEUP_NOT_EXIST = 6532,
        E_SVR_RET_BE_FORBID_MODIFY_LINEUP = 6533,
        E_SVR_RET_LINEUP_INDEX_INVALID = 6534,
        E_SVR_RET_LINEUP_CONTENT_SIZE_INVALID = 6535,
        E_SVR_RET_MALL_ALREADY_HAVE_IN_GIFT_CENTER = 6540,
        E_SVR_RET_MALL_BUY_EXCEED_MAX_HAVE_LIMIT = 6541,
        E_SVR_RET_MALL_BUY_NOT_MEET_CLUB_REQUIREMENTS = 6542,
        E_SVR_RET_GIFT_CENTER_NOT_OPEN = 6900,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_PARAM_ERROR = 6901,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_NOT_ALLOW_GIVE_SELF = 6902,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_MOBILE_TYPE_NOT_EQUAL = 6903,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_GIVER_LEVEL_TOO_LOW = 6904,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_NOT_FRIEND = 6905,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_RECEIVER_LEVEL_TOO_LOW = 6906,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_MESSAGE_TOO_LONG = 6907,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_GOODS_NOT_EXIST = 6908,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_GOODS_NOT_ALLOW_GIVE = 6909,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_GOODS_NOT_ALLOW_FORCE_GIVE = 6910,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_CURRENCY_ID_NOT_EQUAL = 6911,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_RECEIVER_EXCEED_MAX_NUMBER = 6912,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_RECEIVER_EXCEED_MAX_BUY_NUM = 6913,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_GIVER_BAN_LOGIN = 6914,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_GIVER_BAN_LOGIN_CD = 6915,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_GIVER_BAN_GIFT_CENTER = 6916,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_RECEIVER_BAN_LOGIN = 6917,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_RECEIVER_BAN_LOGIN_CD = 6918,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_RECEIVER_BAN_GIFT_CENTER = 6919,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_RECEIVER_ALREADY_HAVE = 6920,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_RECEIVER_GOODS_NOT_EXIST = 6921,
        E_SVR_RET_GIFT_CENTER_DEL_GIVE_RECORD_NOT_FOUND = 6922,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_GIVER_NOT_HAVE_ITEM = 6923,
        E_SVR_RET_GIFT_CENTER_GET_GIFT_RECORD_NOT_FOUND = 6926,
        E_SVR_RET_GIFT_CENTER_GET_GIFT_ALREADY_GET = 6927,
        E_SVR_RET_GIFT_CENTER_DEL_RECV_RECORD_NOT_FOUND = 6930,
        E_SVR_RET_GIFT_CENTER_DEL_RECV_RECORD_GIFT_NOT_GET = 6931,
        E_SVR_RET_GIFT_CENTER_READ_RECV_RECORD_NOT_FOUND = 6932,
        E_SVR_RET_GIFT_CENTER_WANT_GIFT_PARAM_ERROR = 6935,
        E_SVR_RET_GIFT_CENTER_WANT_GIFT_MESSAGE_TOO_LONG = 6936,
        E_SVR_RET_GIFT_CENTER_WANT_GIFT_NOT_ALLOW_GIVE_SELF = 6937,
        E_SVR_RET_GIFT_CENTER_WANT_GIFT_MOBILE_TYPE_NOT_EQUAL = 6938,
        E_SVR_RET_GIFT_CENTER_WANT_GIFT_GOODS_NOT_EXIST = 6939,
        E_SVR_RET_GIFT_CENTER_WANT_GIFT_GOODS_NOT_ALLOW_GIVE = 6940,
        E_SVR_RET_GIFT_CENTER_WANT_GIFT_GIVER_LEVEL_TOO_LOW = 6941,
        E_SVR_RET_GIFT_CENTER_WANT_GIFT_NOT_FRIEND = 6942,
        E_SVR_RET_GIFT_CENTER_WANT_GIFT_USER_LEVEL_TOO_LOW = 6943,
        E_SVR_RET_GIFT_CENTER_WANT_GIFT_USER_BAN_LOGIN = 6944,
        E_SVR_RET_GIFT_CENTER_WANT_GIFT_USER_BAN_LOGIN_CD = 6945,
        E_SVR_RET_GIFT_CENTER_WANT_GIFT_USER_BAN_GIFT_CENTER = 6946,
        E_SVR_RET_GIFT_CENTER_WANT_GIFT_USER_EXCEED_MAX_NUMBER = 6947,
        E_SVR_RET_GIFT_CENTER_WANT_GIFT_MESSAGE_DIRTY = 6948,
        E_SVR_RET_GIFT_CENTER_DEL_WANT_RECORD_NOT_FOUND = 6950,
        E_SVR_RET_GIFT_CENTER_READ_WANT_RECORD_NOT_FOUND = 6951,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_EXCEED_MAX_NUMBER_ONE_DAY = 6960,
        E_SVR_RET_GIFT_CENTER_RECV_GIFT_EXCEED_MAX_NUMBER_ONE_DAY = 6961,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_CREDIT_TOO_LOW = 6962,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_CREDIT_TAG_BLACK = 6963,
        E_SVR_RET_GIFT_CENTER_RECV_GIFT_CREDIT_TOO_LOW = 6964,
        E_SVR_RET_GIFT_CENTER_RECV_GIFT_CREDIT_TAG_BLACK = 6965,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_BE_FRIEND_TOO_SHORT = 6966,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_MESSAGE_DIRTY = 6967,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_EFFECT_TEAM_PLAY_COUNT_NOT_ENOUGH = 6968,
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_RECEIVER_HAVE_KEY_REWARD = 6969, // 收礼人拥有信物可兑换奖品
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_RECEIVER_HAVE_TOKEN = 6970, // 收礼人拥有不可赠送信物
        E_SVR_RET_GIFT_CENTER_GIVE_GIFT_RECEIVER_HAVE_PRESENT_TOKEN = 6971, // 收礼人拥有可赠送信物
        E_SVR_RET_INNERBLESSBAG_QUERY_ERROR = 7200,
        E_SVR_RET_INNERBLESSBAG_DECODE_HTTPRESPOND_ERROR = 7201,
        E_SVR_RET_INNERBLESSBAG_PARSE_FAILDED = 7202,
        E_SVR_RET_INNERBLESSBAG_ARGS_ERROR = 7203,
        E_SVR_RET_INNERBLESSBAG_SIGN_ERROR = 7204,
        E_SVR_RET_INNERBLESSBAG_BAG_CREATE_ERROR = 7205,
        E_SVR_RET_CHAT_BUBBLE_NOT_FOUND_ERROR = 7210,
        E_SVR_RET_CLUB_OPERATOR_MEMBER_NOT_FOUND = 8001,
        E_SVR_RET_CLUB_TARGET_MEMBER_NOT_FOUND = 8002,
        E_SVR_RET_CLUB_OPERATION_REQUIRE_HIGHER_RANKING = 8003,
        E_SVR_RET_CLUB_NOT_IN_THIS_CLUB = 8005,
        E_SVR_RET_CLUB_ALREADY_IN_CLUB = 8006,
        E_SVR_RET_CLUB_SAME_ACCOUNT_IN_CLUB = 8007,
        E_SVR_RET_CLUB_NOT_JOINABLE = 8008,
        E_SVR_RET_CLUB_MEMBER_NUM_REACH_LIMIT = 8009,
        E_SVR_RET_CLUB_RANKING_MEMBER_NUM_REACH_LIMIT = 8010,
        E_SVR_RET_CLUB_ALREADY_IN_APPLICATION_LIST = 8011,
        E_SVR_RET_CLUB_SAME_ACCOUNT_IN_APPLICATION_LIST = 8012,
        E_SVR_RET_CLUB_NOT_IN_APPLICATION_LIST = 8013,
        E_SVR_RET_CLUB_REQUIRES_APPLY_BEFORE_JOIN = 8014,
        E_SVR_RET_CLUB_RANKING_NOT_CHANGED = 8015,
        E_SVR_RET_CLUB_NOT_IN_ANY_CLUB = 8016,
        E_SVR_RET_CLUB_APPLICATION_LIST_NUM_REACH_LIMIT = 8017,
        E_SVR_RET_CLUB_INVALID_RANKING = 8018,
        E_SVR_RET_CLUB_NAME_DUPLICATED = 8019,
        E_SVR_RET_CLUB_NOT_FOUND = 8020,
        E_SVR_RET_CLUB_DISMISSED = 8021,
        E_SVR_RET_CLUB_DONOT_NEED_APPROVAL = 8022,
        E_SVR_RET_CLUB_CANNOT_KICKOUT_SELF = 8023,
        E_SVR_RET_CLUB_NAME_DIRTY = 8024,
        E_SVR_RET_CLUB_DECLARATION_DIRTY = 8025,
        E_SVR_RET_CLUB_SELF_INTRODUCTION_DIRTY = 8026,
        E_SVR_RET_CLUB_CHAT_DIRTY = 8027,
        E_SVR_RET_CLUB_WORLD_RECRUIT_MESSAGE_FREQUENCY_LIMIT = 8028,
        E_SVR_RET_CLUB_ALREADY_DRAWED_WEEKLY_ACTIVE_REWARD = 8029,
        E_SVR_RET_CLUB_WEEKLY_ACTIVE_REWARD_NOT_AVAILABLE = 8030,
        E_SVR_RET_CLUB_SELF_WEEKLY_ACTIVE_SCORE_NOT_ENOUGH = 8031,
        E_SVR_RET_CLUB_RECOMMENDING_NOT_AVAILABLE = 8032,
        E_SVR_RET_CLUB_NOT_MEET_LEVEL_REQUIREMENTS = 8033,
        E_SVR_RET_CLUB_NOT_MEET_TIER_REQUIREMENTS = 8034,
        E_SVR_RET_CLUB_CLUB_WEEKLY_ACTIVE_SCORE_NOT_ENOUGH = 8035,
        E_SVR_RET_CLUB_CHAT_MESSAGE_FREQUENCY_LIMIT = 8036,
        E_SVR_RET_CLUB_INVALID_TASK_ID = 8037,
        E_SVR_RET_CLUB_ALREADY_RECEIVED_REWARD = 8038,
        E_SVR_RET_CLUB_TASK_NOT_COMPLETED = 8039,
        E_SVR_RET_CLUB_CHIEF_NOT_FOUND = 8040,
        E_SVR_RET_CLUB_ITEM_CONFIG_NOT_EXIST = 8041,
        E_SVR_RET_CLUB_GOODS_BUY_LIMITED = 8042,
        E_SVR_RET_CLUB_CURRENCY_NOT_ENOUGH = 8043,
        E_SVR_RET_CLUB_INVITED_USER_OFFLINE = 8044,
        E_SVR_RET_CLUB_INVITE_FREQUENCY_LIMIT = 8045,
        E_SVR_RET_CLUB_CREATE_FREQUENCY_LIMIT = 8046,
        E_SVR_RET_CLUB_JOIN_FREQUENCY_LIMIT = 8047,
        E_SVR_RET_CLUB_RECRUIT_DECLARATION_DIRTY = 8048,
        E_SVR_RET_CLUB_NAME_LEN_INVALID = 8049,
        E_SVR_RET_CLUB_DECLARATION_LEN_INVALID = 8050,
        E_SVR_RET_CLUB_APPLY_INTRODUCTION_LEN_INVALID = 8051,
        E_SVR_RET_CLUB_RECRUIT_DECLARATION_LEN_INVALID = 8052,
        E_SVR_RET_CLUB_INTRODUCTION_LEN_INVALID = 8053,
        E_SVR_RET_CLUB_INTRODUCTION_DIRTY = 8054,
        E_SVR_RET_CLUB_SEARCH_KEYWORDS_DIRTY = 8055,
        E_SVR_RET_CLUB_ALREADY_IN_OTHER_CLUB = 8056,
        E_SVR_RET_CLUB_NETWORK_CONNECTION_FAIL = 8057,
        E_SVR_RET_CLUB_FORBIT_MODIFY_CONTENTS = 8058,
        E_SVR_RET_CLUB_FORBIT_BEHAVIOR = 8059,
        E_SVR_RET_CLUB_OPENPLAT_TYPE_UNMATCH = 8060,
        E_SVR_RET_CLUB_CANNOT_MANAGE_SAME_ACCOUNT = 8061,
        E_SVR_RET_CLUB_INVITATION_NUM_REACH_LIMIT = 8062,
        E_SVR_RET_CLUB_RENAME_LIMIT = 8063,
        E_SVR_RET_CLUB_ALREADY_RECEIVED_ACTIVE_RANK_REWARD = 8064,
        E_SVR_RET_REPAIR_SPACETIME_NO_FOUND_ACTIVITY_CONF = 8101,
        E_SVR_RET_REPAIR_SPACETIME_NO_FOUND_TASK_CONF = 8102,
        E_SVR_RET_REPAIR_SPACETIME_NO_FOUND_TASK_COND_CONF = 8103,
        E_SVR_RET_REPAIR_SPACETIME_NO_FOUND_USER_ACTIVITY_DATA = 8104,
        E_SVR_RET_REPAIR_SPACETIME_NO_FOUND_USER_TASK_DATA = 8105,
        E_SVR_RET_REPAIR_SPACETIME_TASK_UN_COMPLETED = 8106,
        E_SVR_RET_REPAIR_SPACETIME_TASK_RECEIVED_REWARD = 8107,
        E_SVR_RET_REPAIR_SPACETIME_ACTIVITY_NOT_OPEN = 8108,
        E_SVR_RET_JTECHNICAL_COMICS_NO_FOUND_ACTIVITY_CONF = 8130,
        E_SVR_RET_JTECHNICAL_COMICS_NO_FOUND_TASK_CONF = 8131,
        E_SVR_RET_JTECHNICAL_COMICS_NO_FOUND_TASK_COND_CONF = 8132,
        E_SVR_RET_JTECHNICAL_COMICS_NO_FOUND_USER_ACTIVITY_DATA = 8133,
        E_SVR_RET_JTECHNICAL_COMICS_NO_FOUND_USER_TASK_DATA = 8134,
        E_SVR_RET_JTECHNICAL_COMICS_TASK_UN_COMPLETED = 8135,
        E_SVR_RET_JTECHNICAL_COMICS_TASK_RECEIVED_REWARD = 8136,
        E_SVR_RET_JTECHNICAL_COMICS_ACTIVITY_NOT_OPEN = 8137,
        E_SVR_RET_JTECHNICAL_COMICS_TASK_ALREADY_ENABLE = 8138,
        E_SVR_RET_JTECHNICAL_COMICS_SWITCH_STEP_ERROR = 8139,
        E_SVR_RET_BOSS_CHALLENGE_REQ_LINE_UP_NOT_EXIST = 8160,
        E_SVR_RET_BOSS_CHALLENGE_REQ_LINE_UP_IS_LOCK = 8161,
        E_SVR_RET_BOSS_CHALLENGE_ROGUELIKE_LEVEL_PASSED = 8162,
        E_SVR_RET_BOSS_CHALLENGE_ROGUELIKE_LEVEL_DISORDER = 8163,
        E_SVR_RET_BOSS_CHALLENGE_ROGUELIKE_LEVEL_NON_CURRENT = 8164,
        E_SVR_RET_BOSS_CHALLENGE_ROGUELIKE_TASK_NON_COMPLETED = 8165,
        E_SVR_RET_BOSS_CHALLENGE_ROGUELIKE_TASK_REWARD_RECEIVED = 8166,
        E_SVR_RET_BOSS_CHALLENGE_RANDOM_COUNT_LIMIT = 8167,
        E_SVR_RET_BOSS_CHALLENGE_PVE_LEVEL_PASSED = 8171,
        E_SVR_RET_BOSS_CHALLENGE_PVE_LEVEL_DISORDER = 8172,
        E_SVR_RET_BOSS_CHALLENGE_PVE_LEVEL_NON_CURRENT = 8173,
        E_SVR_RET_BOSS_CHALLENGE_PVE_TASK_NON_COMPLETED = 8174,
        E_SVR_RET_BOSS_CHALLENGE_PVE_TASK_REWARD_RECEIVED = 8175,
        E_SVR_RET_SMALL_RECHARGE_ALREADY_GET_ITEM = 8180,
        E_SVR_RET_TRIAL_OF_CYAN_DRAGON_ACTIVITY_NOT_OPEN = 8190,
        E_SVR_RET_TRIAL_OF_CYAN_DRAGON_NO_FOUND_CONF = 8191,
        E_SVR_RET_TRIAL_OF_CYAN_DRAGON_NOT_ENABLE = 8192,
        E_SVR_RET_TRIAL_OF_CYAN_DRAGON_HAS_ENABLE = 8193,
        E_SVR_RET_TRIAL_OF_CYAN_DRAGON_TASK_UN_COMPLETED = 8194,
        E_SVR_RET_TRIAL_OF_CYAN_DRAGON_TASK_RECEIVED_REWARD = 8195,
        E_SVR_RET_TRIAL_OF_CYAN_DRAGON_TASK_OUTDATED = 8196,
        E_SVR_RET_TRIAL_OF_CYAN_DRAGON_BP_UN_COMPLETED = 8197,
        E_SVR_RET_TRIAL_OF_CYAN_DRAGON_BP_RECEIVED_REWARD = 8198,
        E_SVR_RET_TRIAL_OF_CYAN_DRAGON_INIT_TASK_ERROR = 8199,
        E_SVR_RET_TRIAL_OF_CYAN_DRAGON_NO_FOUND_BP_REWARD_CONF = 8200,
        E_SVR_RET_TRIAL_OF_CYAN_DRAGON_OUT_OF_EABLE_DEADLINE = 8201,
        E_SVR_RET_TRIAL_OF_CYAN_DRAGON_NOT_CHARGEABLE_BP = 8202,
        E_SVR_RET_FETTER_OF_TWO_CITIES_NOT_OPEN = 8220,
        E_SVR_RET_FETTER_OF_TWO_CITIES_NOT_FOUND_CONF = 8221,
        E_SVR_RET_FETTER_OF_TWO_CITIES_ALREADY_OPEN_HEX = 8222,
        E_SVR_RET_FETTER_OF_TWO_CITIES_NOT_FOUND_DATA = 8223,
        E_SVR_RET_FETTER_OF_TWO_CITIES_RECEIVE_REWARD = 8224,
        E_SVR_RET_FETTER_OF_TWO_CITIES_TASK_NOT_COMPLETED = 8225,
        E_SVR_RET_COMM_ACC_NOT_OPEN = 8240,
        E_SVR_RET_COMM_ACC_NOT_FIND_CONF = 8241,
        E_SVR_RET_COMM_ACC_NOT_FIND_DATA = 8242,
        E_SVR_RET_COMM_ACC_CANT_GET_REWARD = 8243,
        E_SVR_RET_ENLIGHTENED_TALE_NOT_FIND_CONF = 8280,
        E_SVR_RET_ENLIGHTENED_TALE_PARAM_ERROR = 8281,
        E_SVR_RET_ENLIGHTENED_TALE_CONF_ERROR = 8282,
        E_SVR_RET_ENLIGHTENED_TALE_LOTTERY_ERROR = 8283,
        E_SVR_RET_ENLIGHTENED_TALE_LOTTERY_NOT_ENOUGH_COIN = 8284,
        E_SVR_RET_CARD_COLLECT_SYSTEM_ERROR = 8300,
        E_SVR_RET_CARD_COLLECT_CARD_CONFIG_ERROR = 8301,
        E_SVR_RET_CARD_COLLECT_CARD_NOT_ENOUGH = 8302,
        E_SVR_RET_CARD_COLLECT_CARD_ALREADY_MAXIMUM_STAR = 8303,
        E_SVR_RET_CARD_COLLECT_CARD_UPGRADE_ERROR = 8304,
        E_SVR_RET_CARD_COLLECT_CARD_EFFECT_NOT_OWNED = 8305,
        E_SVR_RET_CARD_COLLECT_NEED_TO_CHOOSE_ONE_CARD = 8306,
        E_SVR_RET_CARD_COLLECT_CARD_PARAM_ERROR = 8307,
        E_SVR_RET_CARD_COLLECT_CARD_NOT_OPEN = 8308,
        E_SVR_RET_CARD_COLLECT_NO_CARDS_TO_UPGRADE = 8309,
        E_SVR_RET_CARD_COLLECT_NO_CARDS_TO_DISMANTLE = 8310,
        E_SVR_RET_CARD_COLLECT_TOO_MANY_CARDS_TO_DISMANTLE = 8311,
        E_SVR_RET_CARD_COLLECT_CARD_PACK_NOT_EXIST = 8312,
        E_SVR_RET_GO_TWO_CITIES_CONFIG_ERROR = 8401,
        E_SVR_RET_GO_TWO_CITIES_NOT_OPEN = 8402,
        E_SVR_RET_GO_TWO_CITIES_PARAM_ERROR = 8403,
        E_SVR_RET_GO_TWO_CITIES_TASK_NOT_COMPLETE = 8404,
        E_SVR_RET_GO_TWO_CITIES_TASK_RECEIVED = 8405,
        E_SVR_RET_GO_TWO_CITIES_ACTIVITY_FINISHED = 8406,
        E_SVR_RET_GO_TWO_CITIES_ACTIVITY_NOT_FINISH = 8407,
        E_SVR_RET_FORTUNE_BOX_CONFIG_ERROR = 8410,
        E_SVR_RET_FORTUNE_BOX_NOT_OPEN = 8411,
        E_SVR_RET_FORTUNE_BOX_PARAM_ERROR = 8412,
        E_SVR_RET_FORTUNE_BOX_TASK_NOT_COMPLETE = 8413,
        E_SVR_RET_FORTUNE_BOX_TASK_RECEIVED = 8414,
        E_SVR_RET_FORTUNE_BOX_NOT_FINISH_UNLOCK_TASK = 8415,
        E_SVR_RET_CONTEST_CANNOT_TEAMUP_ERROR = 8501,
        E_SVR_RET_CONTEST_TICKET_NOT_ENOUGH = 8502,
        E_SVR_RET_CONTEST_SUB_CONTEST_NOT_OPEN = 8503,
        E_SVR_RET_CONTEST_CONFIG_ERROR = 8504,
        E_SVR_RET_CONTEST_BONUS_CARD_ALREADY_USED = 8505,
        E_SVR_RET_CONTEST_BONUS_CARD_CAN_ONLY_BE_USED_IN_RANKED_CONTEST = 8506,
        E_SVR_RET_CONTEST_NETWORK_ERROR = 8507,
        E_SVR_RET_CONTEST_CONTEST_NOT_OPEN = 8508,
        E_SVR_RET_CONTEST_CONTEST_ROOM_NOT_OPEN = 8509,
        E_SVR_RET_CONTEST_JOIN_COUNT_LIMIT = 8510,
        E_SVR_RET_CONTEST_SUBCONTEST_CHANGED = 8511,
        E_SVR_RET_CONTEST_NOT_IN_OPEN_TIME = 8512,
        E_SVR_RET_CONTEST_IS_STOP = 8513,
        E_SVR_RET_PRIVILEGE_CONFIG_NOT_FOUND = 8601,
        E_SVR_RET_PRIVILEGE_EXPIRE = 8602,
        E_SVR_RET_COMMON_BP_NOT_BEGIN = 8700,
        E_SVR_RET_COMMON_BP_IS_END = 8701,
        E_SVR_RET_COMMON_BP_CONFIG_NOT_FOUND = 8702,
        E_SVR_RET_COMMON_BP_WEEK_CONFIG_NOT_FOUND = 8703,
        E_SVR_RET_COMMON_BP_LEVEL_NO_REWARDS = 8704,
        E_SVR_RET_COMMON_BP_LEVEL_CONFIG_NOT_FOUND = 8705,
        E_SVR_RET_COMMON_BP_LEVEL_RECEIVED_REWARD = 8706,
        E_SVR_RET_COMMON_BP_LEVEL_TOO_LOW = 8707,
        E_SVR_RET_COMMON_BP_PAY_TYPE_CHECK_FAILED = 8708,
        E_SVR_RET_COMMON_BP_PAY_TYPE_1_NOT_MATCH = 8709,
        E_SVR_RET_COMMON_BP_PAY_TYPE_2_NOT_MATCH = 8710,
        E_SVR_RET_COMMON_BP_TASK_CONFIG_NOT_FOUND = 8711,
        E_SVR_RET_COMMON_BP_TASK_STATUS_NOT_FOUND = 8712,
        E_SVR_RET_COMMON_BP_TASK_INCOMPLETED = 8713,
        E_SVR_RET_COMMON_BP_TASK_RECEIVED_REWARD = 8714,
        E_SVR_RET_COMMON_BP_IS_NOT_OPEN = 8719,
        E_SVR_RET_COMMON_BP_TASK_REFRESH_CONFIG_NOT_FOUND = 8720,
        E_SVR_RET_COMMON_BP_TASK_REFRESH_EXCEED_MAX_LIMIT = 8721,
        E_SVR_RET_COMMON_BP_TASK_REFRESH_NOT_ALL_RECEIVED = 8722,
        E_SVR_RET_COMMON_BP_TASK_REFRESH_CURRENCY_NOT_ENOUGH = 8723,
        E_SVR_RET_COMMON_BP_TASK_REFRESH_CONSUME_ERROR = 8724,
        E_SVR_RET_CLIENT_HIDE_ONLINE_USED_UP = 8800,
        E_SVR_RET_ITEM_USE_NOT_IN_VALID_TIME = 8900,
        E_SVR_RET_QUICK_CHAT_INVALID_POS = 9010,
        E_SVR_RET_QUICK_CHAT_DO_NOT_HAVE_ID = 9011,
        E_SVR_RET_QUICK_CHAT_CFG_NOT_FOUND = 9012,
        E_SVR_RET_QUICK_CHAT_NOT_EQUIPED = 9013,
        E_SVR_RET_QUICK_CHAT_IS_FORBID = 9014,
        E_SVR_RET_QUICK_CHAT_FOUND_FORBID_ID = 9015,
        E_SVR_RET_QUICK_CHAT_SET_EXPIRED_ID = 9016,
        E_SVR_RET_QUICK_CHAT_CAN_NOT_SET_IN_GAME = 9017,
        E_SVR_RET_COMMON_MULTI_STEP_ACTIVITY_NO_FOUND_ACTIVITY_CONF = 9050,
        E_SVR_RET_COMMON_MULTI_STEP_ACTIVITY_NO_FOUND_TASK_CONF = 9051,
        E_SVR_RET_COMMON_MULTI_STEP_ACTIVITY_NO_FOUND_TASK_COND_CONF = 9052,
        E_SVR_RET_COMMON_MULTI_STEP_ACTIVITY_NO_FOUND_USER_ACTIVITY_DATA = 9053,
        E_SVR_RET_COMMON_MULTI_STEP_ACTIVITY_NO_FOUND_USER_TASK_DATA = 9054,
        E_SVR_RET_COMMON_MULTI_STEP_ACTIVITY_TASK_UN_COMPLETED = 9055,
        E_SVR_RET_COMMON_MULTI_STEP_ACTIVITY_TASK_RECEIVED_REWARD = 9056,
        E_SVR_RET_COMMON_MULTI_STEP_ACTIVITY_ACTIVITY_NOT_OPEN = 9057,
        E_SVR_RET_COMMON_MULTI_STEP_ACTIVITY_TASK_ALREADY_ENABLE = 9058,
        E_SVR_RET_COMMON_MULTI_STEP_ACTIVITY_SWITCH_STEP_ERROR = 9059,
        E_SVR_RET_COMMON_MALL_NON_EXISTENT = 9080,
        E_SVR_RET_COMMON_MALL_NOT_OPEN = 9081,
        E_SVR_RET_COMMON_MALL_NOT_IN_OPERATION = 9082,
        E_SVR_RET_LOTTERY_SYSTEM_CONFIG_NOT_FOUND = 9130,
        E_SVR_RET_LOTTERY_SYSTEM_INDEX_CONFIG_NOT_FOUND = 9131,
        E_SVR_RET_LOTTERY_SYSTEM_LOTTERY_END = 9132,
        E_SVR_RET_LOTTERY_SYSTEM_LOTTERY_CHIP_NOT_ENOUGH = 9133,
        E_SVR_RET_LOTTERY_SYSTEM_DRAW_ERROR = 9134,
        E_SVR_RET_LOTTERY_SYSTEM_DRAW_TYPE_ERROR = 9135,
        E_SVR_RET_LOTTERY_SYSTEM_DROP_CONFIG_NOT_FOUND = 9136,
        E_SVR_RET_LOTTERY_SYSTEM_CALCULATE_CHIP_ERROR = 9137,
        E_SVR_RET_LOTTERY_SYSTEM_NO_REPLACEMENT_CONFIG_NOT_FOUND = 9138,
        E_SVR_RET_LOTTERY_SYSTEM_NO_REPLACEMENT_UNABLE_TO_DRAW = 9139,
        E_SVR_RET_LOTTERY_SYSTEM_NO_REPLACEMENT_DRAW_FAILED = 9140,
        E_SVR_RET_LOTTERY_SYSTEM_NO_REPLACEMENT_DRAW_FINISH = 9141,
        E_SVR_RET_LOTTERY_SYSTEM_NO_REPLACEMENT_NO_CONDITION_ITEM = 9142,
        E_SVR_RET_LOTTERY_SYSTEM_NO_REPLACEMENT_DRAWTYPE_ERROR = 9143,
        E_SVR_RET_LOTTERY_SYSTEM_BLUEPRINT_SURPRISE_PRIZE_NOT_EXISTS = 9144,
        E_SVR_RET_LOTTERY_SYSTEM_BLUEPRINT_CONFIG_NOT_FOUND = 9145,
        E_SVR_RET_LOTTERY_SYSTEM_BLUEPRINT_PARAM_ERROR = 9146,
        E_SVR_RET_LOTTERY_SYSTEM_BLUEPRINT_PRESENT_COUPON_NOT_ENOUGH = 9147,
        E_SVR_RET_ITEM_COUPON_OP_NOT_SUPPORT = 9160,
        E_SVR_RET_BUSINESS_CARD_RESOURCE_NOT_FOUND_ERROR = 9251,
        E_SVR_RET_BATTLE_EMOTION_CAN_NOT_SET_IN_GAME = 9311,
        E_SVR_RET_BATTLE_EMOTION_INVALID_POS = 9312,
        E_SVR_RET_BATTLE_EMOTION_INVALID_ITEM_ID = 9313,
        E_SVR_RET_BATTLE_EMOTION_NO_TIMES_GIVE_INGMAE = 9316,
        E_SVR_RET_BATTLE_EMOTION_NO_TIMES_GIVE_OUTGMAE = 9317,
        E_SVR_RET_BATTLE_EMOTION_NO_TIMES_RECV_INGMAE = 9318,
        E_SVR_RET_BATTLE_EMOTION_NO_TIMES_RECV_OUTGMAE = 9319,
        E_SVR_RET_BATTLE_EMOTION_TALKTYPE_ERROR = 9320,
        E_SVR_RET_BATTLE_EMOTION_SELECT_SOCIABILTY_FAIL = 9321,
        E_SVR_RET_BATTLE_EMOTION_UPDATE_SOCIABILTY_FAIL = 9322,
        E_SVR_RET_BATTLE_EMOTION_NO_OPEN = 9323,
        E_SVR_RET_BATTLE_EMOTION_ID_NOT_EXIST = 9324,
        E_SVR_RET_BATTLE_EMOTION_REPORT_SOCIAL_RANK_FAIL = 9325,
        E_SVR_RET_MATCH_BEBAN_FROM_AFK = 9341,
        E_SVR_RET_DROP_NO_RETURN_DROP_USE_TOO_MANY = 9351,
        E_SVR_RET_BETTING_LOTTERY_NOT_INIT = 9401,
        E_SVR_RET_BETTING_LOTTERY_BETTING_COUNT_POOL_LIMIT = 9402,
        E_SVR_RET_BETTING_LOTTERY_BETTING_COUNT_PLAYER_LIMIT = 9403,
        E_SVR_RET_BETTING_LOTTERY_HAVE_UNACCEPT_REWARD = 9404,
        E_SVR_RET_BETTING_LOTTERY_ALREAD_HAVE_FIRST_PRIZE = 9405,
        E_SVR_RET_BETTING_LOTTERY_COIN_NOT_ENOUGH = 9406,
        E_SVR_RET_BETTING_LOTTERY_INVALID_ARGS = 9407,
        E_SVR_RET_BETTING_LOTTERY_ALREAD_ACCEPT_REWARD = 9408,
        E_SVR_RET_BETTING_LOTTERY_HAVE_NOT_ACCEPT_CONDITION = 9409,
        E_SVR_RET_BETTING_LOTTERY_HAVE_NOT_BETTING_RECORD = 9410,
        E_SVR_RET_BETTING_LOTTERY_CACULATING_TRY_AGAIN_LATER = 9411,
        E_SVR_RET_BETTING_LOTTERY_ACEEPT_FAIL_TRY_AGAIN_LATER = 9412,
        E_SVR_RET_BETTING_LOTTERY_QUERY_FAIL_TRY_AGAIN_LATER = 9413,
        E_SVR_RET_BETTING_LOTTERY_CONFIG_NOT_FOUND = 9414,
        E_SVR_RET_JOC_MANUAL_NOT_OPEN = 9751,
        E_SVR_RET_JOC_MANUAL_SEND_BULLET_SCREEN_IN_CD = 9752,
        E_SVR_RET_JOC_MANUAL_BULLET_SCREEN_CFG_NOT_FOUND = 9753,
        E_SVR_RET_JOC_MANUAL_VOTE_CFG_NOT_FOUND = 9754,
        E_SVR_RET_JOC_MANUAL_SELF_VOTE_ITEM_NOT_ENOUGH = 9755,
        E_SVR_RET_JOC_MANUAL_VOTE_INCREASE_ERROR = 9756,
        E_SVR_RET_JOC_MANUAL_VOTE_ASSIST_ITEM_NOT_ENOUGH = 9757,
        E_SVR_RET_JOC_MANUAL_VOTE_ASSIST_CREATE_ERROR = 9758,
        E_SVR_RET_JOC_MANUAL_VOTE_ASSIST_TYPE_ERROR = 9759,
        E_SVR_RET_JOC_MANUAL_VOTE_ASSIST_SEND_FRIEND_ERROR = 9760,
        E_SVR_RET_JOC_MANUAL_VOTE_ASSIST_IS_NOT_FRIEND = 9761,
        E_SVR_RET_JOC_MANUAL_VOTE_ASSIST_ALREADY_SEND_TO_FRIEND_TODAY = 9762,
        E_SVR_RET_JOC_MANUAL_VOTE_ASSIST_IS_INVALID = 9763,
        E_SVR_RET_JOC_MANUAL_VOTE_ASSIST_INFO_QUERY_FAILED = 9764,
        E_SVR_RET_JOC_MANUAL_VOTE_ASSIST_IS_ALREADY_ASSISTED = 9765,
        E_SVR_RET_JOC_MANUAL_VOTE_ASSIST_FAILED = 9716,
        E_SVR_RET_JOC_MANUAL_VOTE_ASSIST_TYPE_INCONSISTENT = 9767,
        E_SVR_RET_JOC_MANUAL_VOTE_ASSIST_QUERY_DATA_FAILED = 9768,
        E_SVR_RET_JOC_MANUAL_CAN_NOT_ASSIST_SELF = 9769,
        E_SVR_RET_JOC_MANUAL_VOTE_ASSIST_GET_FRIEND_FAILED = 9770,
        E_SVR_RET_JOC_MANUAL_VOTE_ASSIST_FRIEND_UID_ERROR = 9771,
        E_SVR_RET_JOC_MANUAL_VOTE_COUNT_ERROR = 9772,
        E_SVR_RET_JOC_MANUAL_VOTE_REWARD_CFG_NOT_FOUND = 9773,
        E_SVR_RET_JOC_MANUAL_VOTE_REWARD_PROGRESS_NOT_ENOUGH = 9774,
        E_SVR_RET_JOC_MANUAL_VOTE_REWARD_ALREADY_GET = 9775,
        E_SVR_RET_JOC_MANUAL_QUIZ_QUESTION_CFG_NOT_FOUND = 9776,
        E_SVR_RET_JOC_MANUAL_QUIZ_ANSWER_COUNT_NOT_MATCH = 9777,
        E_SVR_RET_JOC_MANUAL_QUIZ_NOT_OPEN = 9778,
        E_SVR_RET_JOC_MANUAL_QUIZ_ANSWER_CFG_NOT_FOUND = 9779,
        E_SVR_RET_JOC_MANUAL_QUIZ_IS_ALREADY_ANSWERED = 9780,
        E_SVR_RET_JOC_MANUAL_QUIZ_TASK_CFG_NOT_FOUND = 9781,
        E_SVR_RET_JOC_MANUAL_QUIZ_TASK_IS_SPECIAL = 9782,
        E_SVR_RET_JOC_MANUAL_QUIZ_TASK_DATA_NOT_FOUND = 9783,
        E_SVR_RET_JOC_MANUAL_QUIZ_TASK_ALREADY_GET_REWARD = 9784,
        E_SVR_RET_JOC_MANUAL_QUIZ_TASK_NOT_FINISHED = 9785,
        E_SVR_RET_JOC_MANUAL_QUIZ_ANSWER_ID_REPEATED = 9786,
        E_SVR_RET_JOC_MANUAL_VOTE_ASSIST_IS_EXPIRED = 9787,
        E_SVR_RET_JOC_MANUAL_VOTE_ASSIST_TARGET_INCONSISTENT = 9788,
        E_SVR_RET_JOC_MANUAL_VOTE_ASSIST_WORLD_LIMIT = 9789,
        E_SVR_RET_JOC_MANUAL_VOTE_ASSIST_CAN_NOT_SEND_TO_SELF = 9790,
        E_SVR_RET_JOC_MANUAL_QUIZ_CAN_NOT_COMMIT_DIFFERENT_PAGE = 9791,
        E_SVR_RET_JOC_MANUAL_QUIZ_QUESTION_NUM_NOT_OK = 9792,
        E_SVR_RET_JOC_MANUAL_QUIZ_QUESTION_ID_REPEATED = 9793,
        E_SVR_RET_JOC_MANUAL_QUIZ_ANSWER_ID_NOT_MATCH_QUESTION = 9794,
        E_SVR_RET_JOC_MANUAL_QUIZ_ANSWER_IS_EMPTY = 9795,
        E_SVR_RET_JOC_MANUAL_VOTE_NOT_OPEN = 9796,
        E_SVR_RET_JOC_MANUAL_MALL_CONSUME_ITEM_NOT_ENOUGH = 9797,
        E_SVR_RET_JOC_MANUAL_MALL_CONSUME_IS_NOT_JOC = 9798,
        E_SVR_RET_JOC_MANUAL_LOTTERY_CURRENCY_NOT_ENOUGH = 9861, // 您的抽奖代币不足
        E_SVR_RET_JOC_MANUAL_LOTTERY_CURRENCY_CALC_ERROR = 9862, // 抽奖代币消耗计算异常
        E_SVR_RET_JOC_MANUAL_LOTTERY_DRAW_TYPE_ERROR = 9863, // 抽奖类型错误
        E_SVR_RET_JOC_MANUAL_LOTTERY_CONSUME_CURRENCY_ERROR = 9864, // 抽奖扣除代币失败
        E_SVR_RET_JOC_MANUAL_LOTTERY_CONSUME_COUPON_ERROR = 9865, // 抽奖扣除代币失败
        E_SVR_RET_JOC_MANUAL_LOTTERY_BUILD_POOL_EMPTY = 9866, // 构建奖池失败，找不到可掉落项
        E_SVR_RET_JOC_MANUAL_LOTTERY_POOL_RANDOM_ERROR = 9867, // 奖池随机失败，找不到可掉落项
        E_SVR_RET_JOC_MANUAL_LOTTERY_BUILD_REWARD_CANDIDATE_EMPTY = 9868, // 构建掉落候选项失败，找不到可掉落项
        E_SVR_RET_JOC_MANUAL_LOTTERY_REWARD_CANDIDATE_RANDOM_ERROR = 9869, // 掉落候选项随机失败，找不到可掉落项
        E_SVR_RET_JOC_MANUAL_LOTTERY_LIGHT_UP_COUNT_CFG_ERROR = 9870, // 灯光点亮数量配置错误
        E_SVR_RET_JOC_MANUAL_LOTTERY_LIGHT_UP_RANDOM_ERROR = 9871, // 灯光点亮随机失败
        E_SVR_RET_JOC_MANUAL_LOTTERY_CREATE_LIGHT_UP_REWARD_ERROR = 9872, // 创建灯光奖励失败
        E_SVR_RET_JOC_MANUAL_LOTTERY_PRESENT_MAIL_EMPTY = 9873, // 赠送邮件配置为空
        E_SVR_RET_JOC_MANUAL_LOTTERY_PRESENT_PARAM_ERROR = 9874, // 赠送参数错误
        E_SVR_RET_JOC_MANUAL_LOTTERY_PRESENT_COUPON_NOT_ENOUGH = 9875, // 赠送券数量不足
        E_SVR_RET_JOC_MANUAL_LOTTERY_PRESENT_SEND_MAIL_ERROR = 9876, // 发送赠送邮件失败
        E_SVR_RET_NO_RESPONSE = 99999,
    }
    public enum ENETWORK_TYPE
    {
        NETWORK_TYPE_2G = 1,
        NETWORK_TYPE_3G = 2,
        NETWORK_TYPE_4G = 3,
        NETWORK_TYPE_WIFI = 4,
        NETWORK_TYPE_5G = 5,
    }
    public enum E_ITEM_SEGMENT
    {
        E_ITEM_SEGMENT_GAME_COUPON = 1,
        E_ITEM_SEGMENT_RARE_GEM = 2,
        E_ITEM_SEGMENT_BLUE_ESSENCE = 3,
        E_ITEM_SEGMENT_ORANGE_ESSENCE = 4,
        E_ITEM_SEGMENT_JTECH_JEM = 5,
        E_ITEM_SEGMENT_BP_EXP = 6,
        E_ITEM_SEGMENT_BP_COIN = 7,
        E_ITEM_SEGMENT_PLAYRE_EXP = 8,
        E_ITEM_SEGMENT_CONTEST_COIN = 9,
        E_ITEM_SEGMENT_SIGN_IN_EXP = 10,
        E_ITEM_SEGMENT_TINY_HERO_COIN = 11,
        E_ITEM_SEGMENT_TINY_HERO_FRAGMENT = 12,
        E_ITEM_SEGMENT_DIRECT_BUY = 16,
        E_ITEM_SEGMENT_CARD_COLLECT_SCRAP = 17,
        E_ITEM_SEGMENT_CLUB_CURRENCY = 18,
        E_ITEM_SEGMENT_LUCKYCOIN = 20,
        E_ITEM_SEGMENT_ZHIZHEN_COIN_BEGIN = 4500,
        E_ITEM_SEGMENT_ZHIZHEN_COIN_END = 4999,
        E_ITEM_SEGMENT_SPECIAL_CURRENTCY_TYPE_BEGIN = 5000,
        E_ITEM_SEGMENT_SKYRIM_TRANING_ACTSCORE = 5001,
        E_ITEM_SEGMENT_FAT_DRAGON_COOKING = 5002,
        E_ITEM_SEGMENT_FAT_DRAGON_ACTSCORE = 5003,
        E_ITEM_SEGMENT_NORMAL_BEGIN = 10000,
        E_ITEM_SEGMENT_DOUBLE_ADD_EXP = 10001,
        E_ITEM_SEGMENT_TRUMPET = 10012,
        E_ITEM_SEGMENT_RENAME_CARD = 10013,
        E_ITEM_SEGMENT_REFRESH_BALL = 10018,
        E_ITEM_SEGMENT_DOUBLE_ADD_EXP_USING = 10030,
        E_ITEM_SEGMENT_WIN_DOUBLE_ADD_EXP = 10031,
        E_ITEM_SEGMENT_WIN_DOUBLE_ADD_EXP_USING = 10032,
        E_ITEM_SEGMENT_JTECH_GAMBLING_COIN = 10034,
        E_ITEM_SEGMENT_SILVER_CARD = 10046,
        E_ITEM_SEGMENT_GOLDEN_CARD = 10047,
        E_ITEM_SEGMENT_CLUB_RENAME_CARD = 10048,
        E_ITEM_SEGMENT_COIN_ACTIVITY_PROJECT = 11000,
        E_ITEM_SEGMENT_COIN_ACTIVITY_WORLD_CHAMPIONSHIP = 11001,
        E_ITEM_SEGMENT_COIN_ACTIVITY_CARNIVAL = 11002,
        E_ITEM_SEGMENT_BOUNTY_TICKET = 12001,
        E_ITEM_SEGMENT_RANK_PROTECTION = 14000,
        E_ITEM_SEGMENT_LUCKY_TICKET = 16002,
        E_ITEM_SEGMENT_COIN_JINGNENG_COST = 2600018,
        E_ITEM_SEGMENT_COIN_JINGNENG_FREE = 2600019,
        E_ITEM_SEGMENT_NORMAL_END = 99999,
        E_ITEM_SEGMENT_TINY_BEGIN = 100000,
        E_ITEM_SEGMENT_TINY_END = 199999,
        E_ITEM_SEGMENT_CHATBUBBLE_EXPCARD_BEGIN = 1400001,
        E_ITEM_SEGMENT_CHATBUBBLE_EXPCARD_END =  1410000,
        E_ITEM_SEGMENT_TINY_EXPRESSION_BEGIN = 200000,
        E_ITEM_SEGMENT_TINY_EXPRESSION_END = 299999,
        E_ITEM_SEGMENT_TINY_MAP_BEGIN = 300000,
        E_ITEM_SEGMENT_TINY_MAP_END = 399999,
        E_ITEM_SEGMENT_DROP_ID_BEGIN = 400000,
        E_ITEM_SEGMENT_DROP_ID_END = 499999,
        E_ITEM_SEGMENT_HEAD_FRAME_BEGIN = 500000,
        E_ITEM_SEGMENT_HEAD_FRAME_DEFAULT = 510000,
        E_ITEM_SEGMENT_HEAD_FRAME_END = 599999,
        E_ITEM_SEGMENT_FOOT_BEGIN = 600000,
        E_ITEM_SEGMENT_FOOT_END = 699999,
        E_ITEM_SEGMENT_HURTEFFECT_BEGIN = 700000,
        E_ITEM_SEGMENT_HURTEFFECT_END = 799999,
        E_ITEM_SEGMENT_TRANSFER_BEGIN = 800000,
        E_ITEM_SEGMENT_TRANSFER_END = 899999,
        E_ITEM_SEGMENT_CHEST_BEGIN = 900000,
        E_ITEM_SEGMENT_CHEST_END = 999999,
        E_ITEM_SEGMENT_CHEST_KEY_BEGIN = 1000000,
        E_ITEM_SEGMENT_CHEST_KEY_END = 1099999,
        E_ITEM_SEGMENT_INTERACT_EXPRESSION_BEGIN = 1100000,
        E_ITEM_SEGMENT_INTERACT_EXPRESSION_END = 1199999,
        E_ITEM_SEGMENT_GIFT_PACK_BEGIN = 1200000,
        E_ITEM_SEGMENT_GIFT_PACK_END = 1299999,
        E_ITEM_SEGMENT_HIDE_BEGIN = 1300000,
        E_ITEM_SEGMENT_HIDE_END = 1399999,
        E_ITEM_EXPERIENCE_BEGIN = 1400000,
        E_ITEM_EXPERIENCE_END = 1499999,
        E_ITEM_ACTIVITY_CURRENCY_BEGIN = 1500000,
        E_ITEM_ACTIVITY_CURRENCY_END = 1599999,
        E_ITEM_ACTIVITY_LOTTERY_DROP_GROUP_BEGIN = 1600000,
        E_ITEM_ACTIVITY_LOTTERY_DROP_GROUP_END = 1699999,
        E_ITEM_MALL_EGG_BEGIN = 1700000,
        E_ITEM_MALL_EGG_END = 1799999,
        E_ITEM_MALL_DROP_GROUP_CONFIG_BY_USE_COUNT_BEGIN = 1800000,
        E_ITEM_MALL_DROP_GROUP_CONFIG_BY_USE_COUNT_END = 1899999,
        E_ITEM_ADVANCED_BP_BEGIN = 1900000,
        E_ITEM_ADVANCED_BP_END = 1999999,
        E_ITEM_AVATAR_BEGIN = 2000000,
        E_ITEM_AVATAR_END = 2099999,
        E_ITEM_SEGMENT_ZHIZHEN_ITEM_BEGIN = 2100000,
        E_ITEM_SEGMENT_ZHIZHEN_ITEM_END = 2199999,
        E_ITEM_SEGMENT_ACTIVITY_SCORE_ITEM_BEGIN = 2200000,
        E_ITEM_SEGMENT_ACTIVITY_SCORE_ITEM_END = 2299999,
        E_ITEM_JTECHNICAL_CHEST_BEGIN = 2300000,
        E_ITEM_JTECHNICAL_CHEST_END = 2399999,
        E_ITEM_DROP_GROUP_JTECHNICAL_CHEST_BEGIN = 2400000,
        E_ITEM_DROP_GROUP_JTECHNICAL_CHEST_END = 2499999,
        E_ITEM_SEGMENT_ACTIVITY_SHOW_ITEM_BEGIN = 2500000,
        E_ITEM_SEGMENT_ACTIVITY_SHOW_ITEM_END = 2599999,
        E_ITEM_SEGMENT_CARD_BOX_BEGIN = 2510000,
        E_ITEM_SEGMENT_CARD_BOX_END = 2519999,
        E_ITEM_SEGMENT_ACTIVITY_HIDE_ITEM_BEGIN = 2600000,
        E_ITEM_SEGMENT_ENERGYSTONE = 2600018,
        E_ITEM_SEGMENT_ACTIVITY_HIDE_ITEM_END = 2699999,
        E_ITEM_SEASON_LOTTORY_MALL_ITEM_BEGIN = 2700000,
        E_ITEM_SEASON_LOTTORY_MALL_ITEM_END = 2799999,
        E_ITEM_SEASON_LOTTORY_DROP_GROUP_BEGIN = 2800000,
        E_ITEM_SEASON_LOTTORY_DROP_GROUP_END = 2899999,
        E_ITEM_SEGMENT_MYTH_SUMMON_COIN = 2900000,
        E_ITEM_SEGMENT_FIGHT_SUMMON_COIN = 2900005,
        E_ITEM_SEASON_LOTTORY_CONSUME_ITEM_END = 2999999,
        E_ITEM_SEGMENT_CHAT_BUBBLE_BEGIN = 3000000,
        E_ITEM_SEGMENT_CHAT_BUBBLE_DEFAULT = 3000001,
        E_ITEM_SEGMENT_CHAT_BUBBLE_END = 3099999,
        E_ITEM_SEGMENT_COIN_BEGIN = 1,
        E_ITEM_SEGMENT_COIN_END = 9999,
        E_ITEM_SEGMENT_TINY_ELIMINATE_BEGIN = 3100000,
        E_ITEM_SEGMENT_TINY_ELIMINATE_END = 3199999,
        E_ITEM_SEGMENT_CLUB_ITEM_BEGIN = 3200000,
        E_ITEM_SEGMENT_CLUB_ACTIVE_RANK_TITLE_BEGIN = 3200001,
        E_ITEM_SEGMENT_CLUB_ACTIVE_RANK_TITLE_END = 3200150,
        E_ITEM_SEGMENT_CLUB_MALL_SALES_TITLE_BEGIN = 3200151,
        E_ITEM_SEGMENT_CLUB_MALL_SALES_TITLE_END = 3200300,
        E_ITEM_SEGMENT_CLUB_ITEM_END = 3299999,
        E_ITEM_SEGMENT_CARD_COLLECT_CARD_BEGIN = 3300000,
        E_ITEM_SEGMENT_CARD_COLLECT_CARD_END = 3399999,
        E_ITEM_SEGMENT_HERO_IN_GAME_EFFECT_HERO_SKIN_BEGIN = 3400000,
        E_ITEM_SEGMENT_HERO_IN_GAME_EFFECT_HERO_SKIN_END = 3499999,
        E_ITEM_SEGMENT_HERO_IN_GAME_EFFECT_NORMAL_BROADCAST_BEGIN = 3500000,
        E_ITEM_SEGMENT_HERO_IN_GAME_EFFECT_NORMAL_BROADCAST_END = 3509999,
        E_ITEM_SEGMENT_HERO_IN_GAME_EFFECT_ADVANCED_BROADCAST_BEGIN = 3510000,
        E_ITEM_SEGMENT_HERO_IN_GAME_EFFECT_ADVANCED_BROADCAST_END = 3519999,
        E_ITEM_SEGMENT_HERO_IN_GAME_EFFECT_DOMINATING_BROADCAST_BEGIN = 3520000,
        E_ITEM_SEGMENT_HERO_IN_GAME_EFFECT_DOMINATING_BROADCAST_END = 3529999,
        E_ITEM_SEGMENT_HERO_IN_GAME_EFFECT_CARD_SCORE_BROADCAST = 3530000,
        E_ITEM_SEGMENT_HERO_IN_GAME_EFFECT_CARD_SCORE_BROADCAST_END = 3539999,
        E_ITEM_SEGMENT_HERO_IN_GAME_EFFECT_BROADCAST_END = 3599999,
        E_ITEM_SEGMENT_VARIABLE_PRICE_GIFT_PACK_BEGIN = 3600000,
        E_ITEM_SEGMENT_VARIABLE_PRICE_GIFT_PACK_END = 3699999,
        E_ITEM_SEGMENT_TINY_SHARED_DRAFT_BORDER_BEGIN = 3700000,
        E_ITEM_SEGMENT_TINY_SHARED_DRAFT_BORDER_END = 3799999,
        E_ITEM_SEGMENT_TINY_WIN_STREAK_BEGIN = 3800000,
        E_ITEM_SEGMENT_TINY_WIN_STREAK_END = 3899999,
        E_ITEM_SEGMENT_TINY_DAMAGE_NUM_BEGIN = 3900000,
        E_ITEM_SEGMENT_TINY_DAMAGE_NUM_END = 3999999,
        E_ITEM_SEGMENT_TINY_CHAMPION_DAMAGE_BEGIN = 4000000,
        E_ITEM_SEGMENT_TINY_CHAMPION_DAMAGE_END = 4099999,
        E_ITEM_SEGMENT_COMMON_BP_EXP_BEGIN = 4100000,
        E_ITEM_SEGMENT_COMMON_BP_EXP_END = 4199999,
        E_ITEM_SEGMENT_COMMON_BP_PAY_ITEM_BEGIN = 4200000,
        E_ITEM_SEGMENT_COMMON_BP_PAY_ITEM_END = 4299999,
        E_ITEM_SEGMENT_QUICK_CHAT_BEGIN = 4300000,
        E_ITEM_SEGMENT_QUICK_CHAT_END = 4399999,
        E_ITEM_SEGMENT_TINY_MAP_DIY_PART_BEGIN = 4400000,
        E_ITEM_SEGMENT_TINY_MAP_DIY_PART_END = 4499999,
        E_ITEM_SEGMENT_SPECIAL_DATA_MODIFY_BEGIN = 4500000,
        E_ITEM_SEGMENT_SPECIAL_DATA_MODIFY_END = 4599999,
        E_ITEM_SEGMENT_BUSINESS_CARD_FRAME_BEGIN = 4600000,
        E_ITEM_SEGMENT_BUSINESS_CARD_FRAME_DEFAULT = 4600001,
        E_ITEM_SEGMENT_BUSINESS_CARD_FRAME_END = 4699999,
        E_ITEM_SEGMENT_BUSINESS_CARD_BACKGROUND_BEGIN = 4700000,
        E_ITEM_SEGMENT_BUSINESS_CARD_BACKGROUND_DEFAULT = 4700001,
        E_ITEM_SEGMENT_BUSINESS_CARD_BACKGROUND_END = 4799999,
        E_ITEM_SEGMENT_HERO_IN_GAME_EFFECT_CELEBRATE_BEGIN = 4800000,
        E_ITEM_SEGMENT_HERO_IN_GAME_EFFECT_CELEBRATE_END = 4899999,
        E_ITEM_SEGMENT_CHESS_TRANSPORT_EFFECT_BEGIN = 4900000,
        E_ITEM_SEGMENT_CHESS_TRANSPORT_EFFECT_DEFAULT = 4900001,
        E_ITEM_SEGMENT_CHESS_TRANSPORT_EFFECT_END = 4999999,
        E_ITEM_SEGMENT_COUPON_ITEM_BEGIN = 5000000,
        E_ITEM_SEGMENT_COUPON_ITEM_END = 5099999,
        E_ITEM_SEGMENT_DROP_GROUP_PACK_BEGIN = 5100000,
        E_ITEM_SEGMENT_DROP_GROUP_PACK_END = 5199999,
        E_ITEM_SEGMENT_CELEBRATE_EFFECT_BEGIN = 5200000,
        E_ITEM_SEGMENT_CELEBRATE_EFFECT_END = 5299999,
        E_ITEM_SEGMENT_GUILD_ICON_BEGIN = 5300000,
        E_ITEM_SEGMENT_GUILD_ICON_END = 5399999,

        E_ITEM_SEGMENT_GUILD_ICONBOX_BEGIN = 5400000,
        E_ITEM_SEGMENT_GUILD_ICONBOX_END = 5499999,

        E_ITEM_SEGMENT_MALL_DEDUCT_BEGIN = 5500000,
        E_ITEM_SEGMENT_MALL_DEDUCT_END = 5599999,
        E_ITEM_SEGMENT_MALL_COUPON_BEGIN = 5600000,
        E_ITEM_SEGMENT_MALL_COUPON_END = 5699999,
        E_ITEM_SEGMENT_INGAME_BUTTON_BEGIN = 5700000,
        E_ITEM_SEGMENT_INGAME_BUTTON_DEFAULT = 5700001, // 默认个性化按钮
        E_ITEM_SEGMENT_INGAME_BUTTON_END = 5799999,
        E_ITEM_SEGMENT_FAKE_ITEM_BEGIN = 5800000,
        E_ITEM_SEGMENT_FAKE_ITEM_END = 5899999,
        E_ITEM_TYPE_BATTLE_GRAFFITI_BEGIN = 5900000,
        E_ITEM_TYPE_BATTLE_GRAFFITI_END = 5999999,
    }
    public enum E_ITEM_TYPE
    {
        E_ITEM_TYPE_NORMAL = 0,
        E_ITEM_TYPE_TINY_HERO = 1,
        E_ITEM_TYPE_TINY_EXPRESSION = 2,
        E_ITEM_TYPE_TINY_MAP = 3,
        E_ITEM_TYPE_DROP_ID = 4,
        E_ITEM_TYPE_HEAD_FRAME = 5,
        E_ITEM_TYPE_TINY_FOOT = 6,
        E_ITEM_TYPE_TINY_DAMAGE = 7,
        E_ITEM_TYPE_TINY_DOOR = 8,
        E_ITEM_TYPE_CHEST = 9,
        E_ITEM_TYPE_CHEST_KEY = 10,
        E_ITEM_TYPE_INTERACT_EXPRESSION = 11,
        E_ITEM_TYPE_GIFT_PACK = 12,
        E_ITEM_TYPE_HIDE = 13,
        E_ITEM_TYPE_EXPERIENCE = 14,
        E_ITEM_TYPE_ACTIVITY_CURRENCY = 15,
        E_ITEM_ACTIVITY_LOTTERY_DROP_GROUP = 16,
        E_ITEM_TYPE_MALL_EGG = 17,
        E_ITEM_TYPE_DROP_GROUP_CONFIG_BY_USE_COUNT = 18,
        E_ITEM_TYPE_ADVANCED_BP = 19,
        E_ITEM_TYPE_AVATAR = 20,
        E_ITEM_TYPE_ZHIZHEN_ITEM = 21,
        E_ITEM_TYPE_ACTIVITY_SCORE_ITEM = 22,
        E_ITEM_TYPE_JTECHNICAL_CHEST = 23,
        E_ITEM_TYPE_JTECHNICAL_DROP_ID = 24,
        E_ITEM_TYPE_ACTIVITY_SHOW_ITEM = 25,
        E_ITEM_TYPE_ACTIVITY_HIDE_ITEM = 26,
        E_ITEM_TYPE_SEASON_LOTTORY_MALL_ITEM = 27,
        E_ITEM_TYPE_SEASON_LOTTORY_DROP_GROUP = 28,
        E_ITEM_TYPE_SEASON_LOTTORY_CONSUME_ITEM = 29,
        E_ITEM_TYPE_CHAT_BUBBLE = 30,
        E_ITEM_TYPE_TINY_ELIMINATE = 31,
        E_ITEM_TYPE_CLUB_ITEM = 32,
        E_ITEM_TYPE_CARD_COLLECT_CARD = 33,
        E_ITEM_TYPE_HERO_IN_GAME_EFFECT_HERO_SKIN = 34,
        E_ITEM_TYPE_HERO_IN_GAME_EFFECT_BROADCAST = 35,
        E_ITEM_TYPE_VARIABLE_PRICE_GIFT_PACK = 36,
        E_ITEM_TYPE_TINY_SHARED_DRAFT_BORDER = 37,
        E_ITEM_TYPE_TINY_WIN_STREAK = 38,
        E_ITEM_TYPE_TINY_DAMAGE_NUM = 39,
        E_ITEM_TYPE_TINY_CHAMPION_DAMAGE = 40,
        E_ITEM_TYPE_COMMON_BP_EXP = 41,
        E_ITEM_TYPE_COMMON_BP_PAY_ITEM = 42,
        E_ITEM_TYPE_QUICK_CHAT = 43,
        E_ITEM_TYPE_TINY_MAP_DIY_PART = 44,
        E_ITEM_TYPE_SPECIAL_DATA_MODIFY = 45,
        E_ITEM_TYPE_BUSINESS_CARD_FRAME = 46,
        E_ITEM_TYPE_BUSINESS_CARD_BACKGROUND = 47,
        E_ITEM_SEGMENT_HERO_IN_GAME_EFFECT_CELEBRATE = 48,
        E_ITEM_TYPE_MAX,
    }
    public enum E_ITEM_USE_TYPE
    {
        E_ITEM_USE_ADD_ITEM = 1,
        E_ITEM_USE_DROP = 2,
        E_ITEM_USE_ADD_DAY = 3,
        E_ITEM_USE_DROP_CONFIG_BY_USE_COUNT = 4,
        E_ITEM_USE_CLUB = 5,
        E_ITEM_USE_DROP_NO_NOTIFY = 6,
        E_ITEM_USE_ADD_ROUND_COUNT = 7,
        E_ITEM_USE_CHANGE_LP = 8,
        E_ITEM_USE_COUPON = 9,
    }
    public enum E_TINY_READ_TYPE
    {
        E_TINY_READ_TYPE_NULL = 0,
        E_TINY_READ_TYPE_TINY = 1,
        E_TINY_READ_TYPE_EXPRESSION = 2,
        E_TINY_READ_TYPE_MAP = 3,
        E_TINY_READ_TYPE_MAX = 4,
    }
    public enum PROPFLOW_REASON
    {
        R_TAC_NOVICE_GUIDE = 2000,
        R_TAC_BATTLE_PASS_PAY = 2001,
        R_TAC_BATTLE_PASS_LEVEL_REWARD = 2002,
        R_TAC_MALL_BUY = 2003,
        R_TAC_ACTIVITY_TASK = 2004,
        R_TAC_LEVEL_TASK = 2005,
        R_TAC_ITEM_SELL = 2006,
        R_TAC_ITEM_USE = 2007,
        R_TAC_ACHIEVEMENT = 2008,
        R_TAC_ITEM_EXPIRE = 2009,
        R_TAC_ITEM_INIT = 2010,
        R_TAC_SEASON_REWARD = 2011,
        R_TAC_REFRESH_BP_MALL = 2012,
        R_TAC_DAILY_TASK_REWARD = 2013,
        R_TAC_WEEKLY_TASK_REWARD = 2014,
        R_TAC_ENLIGHTMENT_REWARD = 2015,
        R_TAC_BP_TMP_EXP = 2016,
        R_TAC_MALL_Consume_item = 2017,
        R_TAC_MALL_Mail_Item = 2018,
        R_TAC_LOCKSTER_DROPRESULT = 2019,
        R_TAC_PLAYRE_TRUMPET_TALK = 2020,
        R_TAC_USER_SHARE_REWARD = 2021,
        R_TAC_NOVICE_TASK_REWARD = 2022,
        R_TAC_OPEN_CHEST = 2023,
        R_TAC_ANSWER_QUESTION = 2024,
        R_TAC_BOUNTY_REWARD_5MATCH = 2025,
        R_TAC_CONTEST_COST = 2026,
        R_TAC_RENAME = 2027,
        R_TAC_BUTLER_ACTIVITY_REWARD = 2028,
        R_TAC_BUTLER_ACTIVITY_EXCHANGE = 2029,
        R_TAC_GM_CMD = 2030,
        R_TAC_DIRECT_BUY = 2031,
        R_TAC_QUIZ_EVENT_CHEAT = 2032,
        R_TAC_GIFT_PACK = 2033,
        R_TAC_QUIZ_EVENT_REWARD = 2034,
        R_TAC_CONTEST_PERIOD_REWARD = 2035,
        R_TAC_TINY_SINGLE_REWARD = 2039,
        R_TAC_TINY_ORIGIN_TASK_REWARD = 2040,
        R_TAC_IDIP = 2041,
        R_TAC_BALANCE_RANK_PROTECTION = 2042,
        R_TAC_CONTEST_BUY_TICKET = 2043,
        R_TAC_CONTEST_EXCHANGE_TICKET = 2044,
        R_TAC_CONTEST_GET_FREE_TICKET = 2045,
        R_TAC_BOUNTY_REWARD_100PLAYER = 2046,
        R_TAC_BOUNTY_MATCH_FAIL_100PLAYER = 2047,
        R_TAC_DIRECT_BUY_ORDER = 2048,
        R_TAC_ACCUMULATE_CHARGE_REWARD = 2049,
        R_TAC_FIRST_CHARGE_REWARD = 2050,
        R_TAC_MALL_OPEN_CHEST = 2054,
        R_TAC_BALANCE_WIN_DOUBLE_ADD_EXP = 2055,
        R_TAC_SEASON_TASK_REWARD = 2056,
        R_TAC_BATTLE_EMOTION = 2060,
        R_TAC_BP_AUTO_GET_REWARD_MAIL = 2061,
        R_TAC_NEW_PLAYER_MAIL = 2062,
        R_TAC_TINY_HERO_EXPIRE = 2063,
        R_TAC_TINY_MAP_EXPIRE = 2064,
        R_TAC_QQ_GAME_LOGIN_REWARD = 2065,
        R_TAC_AUTO_MAIL = 2066,
        R_TAC_WX_GAME_LOGIN_REWARD = 2067,
        R_TAC_DATA_MORE_RETURN_USER_FIRST_MATCH_REWARD = 2068,
        R_TAC_TINY_HERO_UPGRADE = 2069,
        R_TAC_AUTO_DECOMPOSE = 2070,
        R_TAC_ITEM_EXPIRE_MAIL = 2071,
        R_TAC_NOVICE_COMPLETE = 2072,
        R_TAC_BAN_HEAD_URL = 2073,
        R_TAC_TINY_HERO_UPGRADE_BY_ITEM = 2074,
        R_TAC_LINE_UP_CHALLENGE = 2075,
        R_TAC_CONSECUTIVE_DAY_CHALLENGE_REWARD = 2077,
        R_TAC_MALL_BUY_EGG = 2081,
        R_TAC_USER_DATA_INIT = 2082,
        R_TAC_USE_MALL_EGG = 2089,
        R_TAC_DAILY_FIRST_WIN_REWARD = 2090,
        R_TAC_BOUNTY_NEW_OPEN_REWARD_DROP = 2092,
        R_TAC_BOUNTY_CONSUME_TICKET = 2093,
        R_TAC_BP_MALL = 2100,
        R_TAC_MANUAL_DECOMPOSE = 2101,
        R_TAC_AVATAR_EXPIRE = 2104,
        R_TAC_JTECHNICAL_REWARD = 2105,
        R_TAC_EXCLUSIVE_DAMAGE_BY_HERO = 2106,
        R_TAC_JTECHNICAL_CONSUME = 2107,
        R_TAC_PAY_BP_EXP = 2108,
        R_TAC_TURBO_RANK_SEASON_REWARD = 2109,
        R_TAC_TURBO_FETCH_SEASON_TASK_REWARD = 2110,
        R_TAC_SEASON_LOTTERY_REWARD = 2111,
        R_TAC_SEASON_LOTTERY_CONSUME = 2112,
        R_TAC_GIFT_CENTER = 2113,
        R_TAC_CLUB_WEEKLY_ACTIVE_SCORE_REWARD = 2115,
        R_TAC_SKYRIM_TRANING_RESET_ACTIVITY_SCORE = 2116,
        R_TAC_ACTIVITY_TASK_REWARD = 2117,
        R_TAC_ACTIVITY_ACTIVITY_SCORE_REWARD = 2118,
        R_TAC_ACTIVITY_EXCHANGE_ITEM = 2119,
        R_TAC_ACTIVITY_UNCLAIMED_REWARD = 2120,
        R_TAC_ACTIVITY_REPAIR_SPACETIME = 2121,
        R_TAC_JTECHNICAL_DROP = 2122,
        R_TAC_SEASON_LOTTERY_DROP = 2123,
        R_TAC_CARD_COLLECT = 2124,
        R_TAC_CLUB_MALL_BUY_GOODS = 2125,
        R_TAC_CLUB_JOIN_MAIL = 2126,
        R_TAC_JTECHNICAL_NEW_USER_GUIDE = 2127,
        R_TAC_JTECHNICAL_TASK = 2128,
        R_TAC_JTECHNICAL_DRAW = 2129,
        R_TAC_CLUB_QUIT_MAIL = 2130,
        R_TAC_CLUB_CREATION = 2131,
        R_TAC_DUAL_SEASON_REWARD = 2132,
        R_TAC_DUAL_SEASON_TASK_REWARD = 2133,
        R_TAC_ACTIVITY_GO_TWO_CITIES = 2134,
        R_TAC_ACTIVITY_FETTER_TWO_CITIES = 2135,
        R_TAC_PROMO_CODE_REWARD = 2136,
        R_TAC_LOCKSTER_DUAL_RANK_SECOND_RESULT = 2137,
        R_TAC_CLUB_TASK_REWARD = 2138,
        R_TAC_CONTEST = 2139,
        R_TAC_PRIVILEGED_CARD_REWARD = 2140,
        R_TAC_COMMON_BP_TASK_REWARD = 2141,
        R_TAC_CONTEST_RANK_BALANCE_REWARD = 2142,
        R_TAC_DEL_EXCLUSIVE_DAMAGE_BY_HERO = 2143,
        R_TAC_DEL_EXCLUSIVE_ELIMINATE_BY_HERO = 2144,
        R_TAC_IDIP_MODIFY_CURRENCY = 2145,
        R_TAC_IDIP_MODIFY_ITEM = 2146,
        R_TAC_ACTIVITY_COMM_ACC_REWARD = 2147,
        R_TAC_USER_LEVEL_REWARD = 2148,
        R_TAC_CLUB_RENAME = 2149,
        R_TAC_COMMON_BP_DAILY_TASK_REWARD = 2150,
        R_TAC_COMMON_BP_STAGE_TASK_REWARD = 2151,
        R_TAC_COMMON_BP_LEVEL_REWARD = 2152,
        R_TAC_COMMON_MALL_BUY = 2153,
        R_TAC_BOSS_CHALLENGE_ROGUELIKE_TASK_REWARD = 2154,
        R_TAC_CONTEST_INIT_DATA = 2155,
        R_TAC_BOSS_CHALLENGE_PVE_REWARD = 2159,
        R_TAC_COMMON_BP_REFRESH_TASK = 2160,
        R_TAC_CHAT_BUBBLE_EXPIRE = 2200,
        R_TAC_EXCLUSIVE_ELIMINATE_BY_HERO = 2201,
        R_TAC_MALL_BUY_SOURCE_RECOMMEND = 2202,
        R_TAC_AVATAR_BOX_EXPIRE = 2221,
        R_TAC_TINY_FETTER_REWARD = 2222,
        R_TAC_QUICK_CHAT_EXPIRE = 2223,
        R_TAC_QUICK_CHAT_DEFAULT = 2224,
        R_TAC_ACTIVITY_FORTUNE_BOX = 2230,
        R_TAC_ACTIVITY_ENLIGHTENED_TALE = 2231,
        R_TAC_NO_REPLACEMENT_DRAW = 2232,
        R_TAC_SYNTHESIS = 2233,
        R_TAC_ACTIVITY_XAYAH_RAKAN_LOTTERY = 2234,
        R_TAC_ACTIVITY_XAYAH_RAKAN_PRESENT = 2235,
        R_TAC_ACTIVITY_XAYAH_RAKAN_RECV_PRESENT = 2236,
        R_TAC_BUSINESS_CARD_RESOURCE_EXPIRE = 2237,
        R_TAC_BUSINESS_CARD_RESOURCE_SHARE_REWARD = 2238,
        R_TAC_ACTIVITY_XAYAH_RAKAN_DECOMPOSE = 2239,
        R_TAC_ACHIVEMENT_REWARD = 2240,
        R_TAC_ACTIVITY_ENLIGHTENED_TALE_PRESENT = 2250,
        R_TAC_ACTIVITY_ENLIGHTENED_TALE_RECV_PRESENT = 2251,
        R_TAC_DUAL_MMR_AFK_BEHAVIOR_PUNISH = 2252,
        R_TAC_BLUE_PRINT = 2253,
        R_TAC_BLUE_PRINT_PRESENT = 2254,
        R_TAC_ACTIVITY_BLUE_PRINT_RECV_PRESENT = 2255,
        R_TAC_ACTIVITY_SEASON_LOTTERY_PRESENT = 2256,
        R_TAC_ACTIVITY_SEASON_LOTTERY_RECV_PRESENT = 2257,
        R_TAC_GIFT_PACK_TRANSFORM = 2059,
        R_TAC_BATTLE_EMOTION_DROP_GIVE = 2262,
        R_TAC_BATTLE_EMOTION_DROP_RECV = 2263,
        R_TAC_BIND_PHONE_REWARD = 100000,
    }
    public enum E_DROP_GROUP_FLOW_REASON
    {
        R_TAC_ITEM_USE_OPEN_DROP_GROUP_CONFIG_BY_USE_COUNT = 1000,
        R_TAC_BOUNTY_NEW_GAME_RESULT_REWARD_SCENE_TYPE_UNKNOWN = 2000,
        R_TAC_BOUNTY_NEW_GAME_RESULT_REWARD_SCENE_TYPE_38 = 2001,
        R_TAC_BOUNTY_NEW_GAME_RESULT_REWARD_SCENE_TYPE_39 = 2002,
    }
    public enum E_DROP_GROUP_SHOW_TYPE
    {
        E_DROP_GROUP_SHOW_TYPE_OLD = 0,
        E_DROP_GROUP_SHOW_TYPE_NEW = 1,
    }
    public enum E_DROP_GROUP_MALL_SHOW_TYPE
    {
        E_DROP_GROUP_MALL_SHOW_TYPE_NONE = 0,
        E_DROP_GROUP_MALL_SHOW_TYPE_OPEN = 1,
    }
    public enum EGlobalType
    {
        EGlobalType_WorldTalkFrequency = 47,
        EGlobalType_ClickOffset = 58,
        EGlobalType_NoEncryptProto = 83,
        EGlobalType_CrossDayTime = 103,
        EGlobalType_PerDayFreeTalkCount = 140,
        EGlobalType_MMR_DISTANCE_UNIT = 1001,
        EGlobalType_GAME_VARIANCE = 1002,
        EGlobalType_VAR_G = 1003,
        EGlobalType_VAR_LOBBY_BOND = 1004,
        EGlobalType_VAR_DECAY = 1005,
        EGlobalType_INIT_VAR = 1006,
        EGlobalType_LP_GAME_VAR = 1007,
        EGlobalType_BASE_LP = 1008,
        EGlobalType_MAX_MMR_DIFFERENCE = 1009,
        EGlobalType_CFG_PROVISIONALS_MUTIL = 1010,
        EGlobalType_TIER_ENDPROVISIONALS = 1011,
        EGlobalType_APEX_TIER = 1012,
        EGlobalType_LP_ALEVEL = 1013,
        EGlobalType_DEMOTION_MMR = 1014,
        EGlobalType_DEMOTION_AFTERLP = 1015,
        EGlobalType_APEX_NORMALIZATION_FACTOR = 1016,
        EGlobalType_NEW_PLAYER_INIT_MMR = 1017,
        EGlobalType_NEW_PLAYER_INIT_TIER = 1018,
        EGlobalType_NEW_PLAYER_SEASON_CHANGE_BASEMMR = 1019,
        EGlobalType_NEW_PLAYER_SEASON_CHANGE_MMRFACTOR = 1020,
        EGlobalType_MMRDECAY_LOGIN_INTER_DAYS = 1021,
        EGlobalType_MMRDECAY_MIN_VAR = 1022,
        EGlobalType_MMRDECAY_MMR_REDUCE = 1023,
        EGlobalType_MMRDECAY_MMR_REDUCE_MAX = 1024,
        EGlobalType_MMRDECAY_VAR_REDUCE_FACTOR = 1025,
        EGlobalType_MMRDECAY_VAR_REDUCE_MAX_WEEKS = 1026,
        EGlobalType_LPDECAY_TYPE1_MIN_TIER = 1027,
        EGlobalType_LPDECAY_TYPE2_MIN_TIER = 1028,
        EGlobalType_LPDECAY_TYPE1_MAX_NOLOGINDAYS = 1029,
        EGlobalType_LPDECAY_TYPE1_REDUCE_TIMEUNIT = 1030,
        EGlobalType_LPDECAY_TYPE1_REDUCE_LPS = 1031,
        EGlobalType_LPDECAY_TYPE2_MAX_BACKUP_GAMES = 1032,
        EGlobalType_LPDECAY_TYPE2_REDUCE_LPS = 1033,
        EGlobalType_PROVISIONALS_GAMES = 1034,
        EGlobalType_MALL_VIDEO_URL = 1035,
        EGlobalType_APEX2_TIER_ID = 1036,
        EGlobalType_APEX3_TIER_ID = 1037,
        EGlobalType_MIN_VAR = 1038,
        EGlobalType_CONTEST_REFRESH_COUNT_HOUR = 1039,
        EGlobalType_BATTLEPASS_VIDEO_URL = 1040,
        EGlobalType_CONTEST_INIT_SIGNNUM = 1041,
        EGlobalType_CONTEST_SIGNNUM_RATE = 1042,
        EGlobalType_CALCULATE_MAX_LP = 1043,
        EGlobalType_BATTLEPASS_REWARD1_VIDEO_URL = 1044,
        EGlobalType_BATTLEPASS_REWARD2_VIDEO_URL = 1045,
        EGlobalType_RELAX_MATCH_IS_PREMADE_COUNT = 1046,
        EGlobalType_TICKET_TO_ORANGE_GEM = 1047,
        EGlobalType_HISTORY_TOTAL_CHARGE_WARNING = 1048,
        EGlobalType_APEX_TIER_SWITCH_TIME = 1049,
        EGlobalType_BP_HISTORY_SEASON_NUM = 1050,
        EGlobalType_SEASON_SOFT_RESET_MRR = 1051,
        EGlobalType_MAIL_EXPIRE_SECONDS = 1052,
        EGlobalType_RETURN_USER_WARM_VALUE = 1053,
        EGlobalType_WARM_GAME_ROUND_COUNT = 1054,
        EGlobalType_WARM_MIN_LEFT_COIN_NUM = 1055,
        EGlobalType_WARM_MIN_NO_OPER_TRUN_COUNT = 1056,
        EGlobalType_WARM_COMPUTE_GAME_COUNT = 1057,
        EGlobalType_WARM_MIN_RANK_LEVEL = 1058,
        EGlobalType_RETURN_USER_NO_LOGIN_SECS = 1059,
        EGlobalType_WARM_REGISTER_SECS = 1060,
        EGlobalType_WARM_REGISTER_GAME_ROUND_NUM = 1061,
        EGlobalType_BP_DAILY_TASK_EXPIRE_DAYS = 1062,
        EGlobalType_BP_DAILY_TASK_REFRESH_NUM = 1063,
        EGlobalType_BP_TODO_TASK_NUM = 1064,
        EGlobalType_MALL_EGG_BATCH_COUNT = 1065,
        EGlobalType_BATCH_USE_ITEM_MAX_NUM = 1066,
        EGlobalType_MAX_RECENT_PLAYER_NUM = 1067,
        EGlobalType_INIT_LP = 1068,
        EGlobalType_MAX_INGAME_FRIEND_NUM = 1069,
        EGlobalType_MAX_INGAME_BLACK_NUM = 1070,
        EGlobalType_MAX_TOPNEXT_USER_NUM = 1071,
        EGlobalType_MAGIC_EXPRESSION_MALL_PAGE = 1072,
        EGlobalType_DEFAULT_TINY_HERO_ID = 1074,
        EGlobalType_DEFAULT_TINY_MAP_ID = 1075,
        EGlobalType_DEFAULT_TINY_EXPRESSION_ID = 1076,
        EGlobalType_AUTO_GET_BP_TASK_REWARD_SCENE_TYPE = 1082,
        EGlobalType_MAX_TOPNEXT_USER_NUM_FOR_TURBO = 1083,
        EGlobalType_DEFAULT_TINY_DAMAGE_ID = 1084,
        EGlobalType_MALL_PAGE_ZHIZHEN = 1085,
        EGlobalType_MALL_PAGE_JTECH = 1086,
        EGlobalType_DEFAULT_TINY_ELIMINATE_ID = 1087,
        EGlobalType_MISS_TINY_ID = 1088,
        EGlobalType_RANK_MAX_LESS_TARGET_MMR = 1089,
        EGlobalType_TURBO_INIT_MMR = 1090,
        EGlobalType_TURBO_INIT_VAR = 1091,
        EGlobalType_TURBO_MMR_DISTANCE_UNIT = 1092,
        EGlobalType_TURBO_GAME_VARIANCE = 1093,
        EGlobalType_TURBO_VAR = 1094,
        EGlobalType_TURBO_VAR_LOBBY_BOND = 1095,
        EGlobalType_TURBO_VAR_DECAY = 1096,
        EGlobalType_TURBO_LP_GAME_VAR = 1097,
        EGlobalType_TURBO_BASE_LP = 1098,
        EGlobalType_TURBO_MAX_MMR_DIFF = 1099,
        EGlobalType_TURBO_MAX_MMR = 1100,
        EGlobalType_TURBO_NORMAL_FACTOR = 1101,
        EGlobalType_TURBO_MIN_VAR = 1102,
        EGlobalType_TURBO_CAL_MAX_LP = 1103,
        EGlobalType_TURBO_CAL_MIN_LP = 1104,
        EGlobalType_TURBO_SEASON_SWITCH_MMR_BASE = 1105,
        EGlobalType_TURBO_SEASON_SWITCH_MMR_ACTOR = 1106,
        EGlobalType_TURBO_SEASON_SOFT_RESET_MMR = 1107,
        EGlobalType_TURBO_SEASON_SWITCH_LP_BASE = 1108,
        EGlobalType_TURBO_SEASON_SWITCH_AFTER_LP = 1109,
        EGlobalType_TURBO_SEASON_SWITCH_DEFAULT_AFTER_LP = 1110,
        EGlobalType_TURBO_INIT_TURBO_LP = 1111,
        EGlobalType_TURBO_PROVISIONALS_LP_MULTI = 1112,
        EGlobalType_TURBO_PROVISIONALS_LP_END = 1113,
        EGlobalType_TURBO_PROVISIONALS_GAMES = 1114,
        EGlobalType_GIFT_CENTER_GIVER_MIN_LEVEL = 1151,
        EGlobalType_GIFT_CENTER_RECEIVER_MIN_LEVEL = 1152,
        EGlobalType_WARM_FLAG_LIMIT_TIER = 1160,
        EGlobalType_WARM_FLAG_LIMIT_PLAY_COUNT = 1161,
        EGlobalType_WARM_FLAG_INIT_FLAG = 1162,
        EGlobalType_WARM_FLAG_MAX_FLAG = 1163,
        EGlobalType_WARM_FLAG_RECORVERY_LIMIT_VALUE = 1164,
        EGlobalType_WARM_FLAG_RECORVERY_WARM_FLAG = 1165,
        EGlobalType_WARM_FLAG_RECORVERY_SWITCH = 1166,
        EGlobalType_WARM_FLAG_RETURN_USER_WARM_FLAG_VALUE = 1167,
        EGlobalType_WARM_FLAG_RETURN_USER_RECORVERY_WARM_FLAG = 1168,
        EGlobalType_WARM_FLAG_RETURN_USER_WARM_FLAG_SWITCH = 1169,
        EGlobalType_WARM_FLAG_RETURN_USER_DAY_NUM = 1170,
        EGlobalType_CONTEST_INIT_MMR = 1200,
        EGlobalType_CONTEST_INIT_VAR = 1201,
        EGlobalType_CONTEST_MMR_DISTANCE_UNIT = 1202,
        EGlobalType_CONTEST_GAME_VARIANCE = 1203,
        EGlobalType_CONTEST_VAR = 1204,
        EGlobalType_CONTEST_VAR_LOBBY_BOND = 1205,
        EGlobalType_CONTEST_VAR_DECAY = 1206,
        EGlobalType_CONTEST_LP_GAME_VAR = 1207,
        EGlobalType_CONTEST_BASE_LP = 1208,
        EGlobalType_CONTEST_MAX_MMR_DIFF = 1209,
        EGlobalType_CONTEST_MAX_MMR = 1210,
        EGlobalType_CONTEST_NORMAL_FACTOR = 1211,
        EGlobalType_CONTEST_MIN_VAR = 1212,
        EGlobalType_CONTEST_CAL_MAX_LP = 1213,
        EGlobalType_CONTEST_CAL_MIN_LP = 1214,
        EGlobalType_CONTEST_SEASON_SWITCH_MMR_BASE = 1215,
        EGlobalType_CONTEST_SEASON_SWITCH_MMR_ACTOR = 1216,
        EGlobalType_CONTEST_SEASON_SWITCH_LP_BASE = 1217,
        EGlobalType_CONTEST_SEASON_SWITCH_AFTER_LP = 1218,
        EGlobalType_CONTEST_SEASON_SWITCH_DEFAULT_AFTER_LP = 1219,
        EGlobalType_CONTEST_INIT_CONTEST_LP = 1220,
        EGlobalType_DEFAULT_TINY_DOOR_ID = 1241,
        EGlobalType_DEFAULT_TINY_WIN_STREAK_ID = 1242,
        EGlobalType_DEFAULT_TINY_DAMAGE_NUM_ID = 1243,
        EGlobalType_DEFAULT_TINY_SHARED_DRAFT_BORDER_ID = 1244,
        EGlobalType_DEFAULT_TINY_CHAMPION_DAMAGE_ID = 1245,
        EGlobalType_MAX_QUICK_CHAT_EQUIP_NUM = 1281,
        EGlobalType_MAX_BATTLE_EMOTION_EQUIP_NUM = 1282,
        EGlobalType_MINI_INVITE_PROGRAM = 2101,
        EGlobalType_MINI_RECORD_PROGRAM = 2102,
        EGlobalType_MINI_CHALLENGE_PROGRAM = 2103,
        EGlobalType_MINI_MAGICEXPRESSION_COOLDOWN = 3019,
        EGlobalType_FOOL_DAY_ACTIVITY_ID = 3700,
        EGlobalType_FOOL_DAY_RECOVER_ICON_TASK_ID = 3701,
        EGlobalType_CHECK_ASSIST_FUNC = 4001,
        EGlobalType_HERO_POS_INFO = 4002,
        EGlobalType_BOSS_CHANLLENGE_ROGUELIKE_RANDOM_LINEUP_COUNT = 4054,
        EGlobalType_BOSS_CHANLLENGE_ROGUELIKE_RANDOM_HEX_COUNT = 4055,
        CHAMPIONCONFIG = 4058,
    }

    public enum E_Share_Templete
    {
        E_Share_Tiny_Image = 2001,
        E_Share_Tiny_Video = 2002,
        E_Share_Map_Image = 2003,
        E_Share_Map_Video = 2004,
        E_Share_Set_End_Image = 2005,
        E_Share_Card_Collect = 2006,
        E_Share_Set_End_Rank_Image = 2007,
        E_Share_Tiny_End_Image = 2008,
        E_Share_Tranfer_Image = 2009,
        E_Share_Hero_Tranfer_Image = 2010,
        E_Share_Celebrate_Effect_Image = 2011,
    }

    public enum E_TALK_TYPE
    {
        E_TALK_TYPE_WORLD = 1,
        E_TALK_TYPE_FRIEND_1V1 = 2,
        E_TALK_TYPE_TEAM_INVITE = 3,
        E_TALK_TYPE_SYSTEM = 4,
        E_TALK_TYPE_ROOM = 5,
        E_TALK_TYPE_BATTLE = 6,
        E_TALK_TYPE_TRUMPET = 7,
        E_TALK_TYPE_BATTLE_TEAMMATE = 8,
        E_TALK_TYPE_BATTLE_EMOTION = 9,
        E_TALK_TYPE_NODRAWING_OCCUPY = 10,
        E_TALK_TYPE_DUAL_PLAY_BATTLE_TEAM = 11,
        E_TALK_TYPE_CLUB_CHAT = 12,
        E_TALK_TYPE_CLUB_RECRUIT = 13,
        E_TALK_TYPE_BATTLE_QUICK_CHAT = 14,
        E_TALK_TYPE_BATTLE_OPPONENT = 15,
        E_TALK_TYPE_MATCHING = 16,
        E_TALK_TYPE_GUILD_CHAT = 17,
        E_TALK_TYPE_JOC_MANUAL_VOTE_ASSIST_WORLD = 18,
        E_TALK_TYPE_JOC_MANUAL_VOTE_ASSIST_FRIEND = 19,
        E_TALK_TYPE_GUILD_ROOM = 20,
        E_TALK_TYPE_MAX,
    }
    public enum E_TALK_CONTENT_TYPE
    {
        E_TALK_CONTENT_TYPE_TEXT = 0,
        E_TALK_CONTENT_TYPE_CARD_GROUP = 1,
        E_TALK_CONTENT_TYPE_TEAM_INVITE = 2,
        E_TALK_CONTENT_TYPE_TEAM_SYS = 3,
        E_TALK_CONTENT_TYPE_AUDIO = 4,
        E_TALK_CONTENT_TYPE_TINY = 5,
        E_TALK_CONTENT_TYPE_AUTHORIZED = 6,
        E_TALK_CONTENT_TYPE_CLIENT_OFFLINE = 7,
        E_TALK_CONTENT_TYPE_FORBIDALL = 8,
        E_TALK_CONTENT_TYPE_FORBIDMEMBER = 9,
        E_TALK_CONTENT_TYPE_CLUB_RECRUIT = 10,
        E_TALK_CONTENT_TYPE_TEXT_AUDIO = 11,
        E_TALK_CONTENT_TYPE_LINEUP_SHARE = 12,
        E_TALK_CONTENT_TYPE_BUSINESS_CARD = 13,
        E_TALK_CONTENT_TYPE_BATTLE_EMOTION = 14,
        E_TALK_CONTENT_TYPE_EMOTION_DROP = 15,
        E_TALK_CONTENT_TYPE_SOCIAL_EMOTION  = 16,    // 百人总览页表情
        E_TALK_CONTENT_TYPE_SOCIAL_BROADCAST = 17,   // 百人总览页文本信息
        E_TALK_CONTENT_TYPE_JOC_MANUAL_VOTE_ASSIST_WORLD = 18,
        E_TALK_CONTENT_TYPE_JOC_MANUAL_VOTE_ASSIST_FRIEND = 19,
        E_TALK_CONTENT_TYPE_PANDORA_CONTENT = 20,
        E_TALK_CONTENT_TYPE_GUILD_SHARE = 21,
        E_TALK_CONTENT_TYPE_MAX,
    }
    public enum E_RELOGIN_TYPE
    {
        E_RELOGIN_TYPE_GAME = 0,
        E_RELOGIN_TYPE_ROOM = 1,
    }
    public enum E_TAC_RANK_TYPE
    {
        E_TAC_RANK_TYPE_NULL                    = 0,        // null
        E_TAC_RANK_TYPE_WIN_RELAX_SINGLE        = 3,        // 吃鸡榜                   (休闲匹配，只有好友榜，没有全区榜)
        E_TAC_RANK_TYPE_WIN_MMR_RANK            = 4,        // 吃鸡榜                   (排位匹配，只有好友榜，没有全区榜)
        E_TAC_RANK_TYPE_CONTEST_WIN             = 10,       // 赏金赛赛事通关榜         (暂时没用到，赏金赛系统目前处于屏蔽状态)
        E_TAC_RANK_TYPE_CONTEST_ALLWIN          = 11,       // 赏金赛赛事全胜榜         (暂时没用到，赏金赛系统目前处于屏蔽状态)
        E_TAC_RANK_TYPE_CONTEST_WINCAP          = 12,       // 赏金赛赛事冠军榜         (暂时没用到，赏金赛系统目前处于屏蔽状态)
        E_TAC_RANK_TYPE_SEASONCONTEST_WIN       = 13,       // 赏金赛赛季通关榜         (暂时没用到，赏金赛系统目前处于屏蔽状态)
        E_TAC_RANK_TYPE_SEASONCONTEST_ALLWIN    = 14,       // 赏金赛赛季全胜榜         (暂时没用到，赏金赛系统目前处于屏蔽状态)
        E_TAC_RANK_TYPE_SEASONCONTEST_WINCAP    = 15,       // 赏金赛赛季冠军榜         (暂时没用到，赏金赛系统目前处于屏蔽状态)
        E_TAC_RANK_TYPE_CONTEST_BOUNTY          = 16,       // 赏金币榜                 (暂时没用到，赏金赛系统目前处于屏蔽状态)
        E_TAC_RANK_TYPE_CONTEST_100FINALS       = 17,       // 赏金赛百人赛赛事入围榜   (暂时没用到，赏金赛系统目前处于屏蔽状态)
        E_TAC_RANK_TYPE_CONTEST_100WIN          = 18,       // 赏金赛百人赛赛事冠军榜   (暂时没用到，赏金赛系统目前处于屏蔽状态)
        E_TAC_RANK_TYPE_TOP_FOUR                = 20,       // 获胜榜                   (没有好友榜，只有全区榜)
        E_TAC_RANK_TYPE_SEGMENT_SHOW            = 22,       // 主排位段位榜             (外显，既有好友榜，又有全区榜)
        E_TAC_RANK_TYPE_SEGMENT_REALTIME        = 23,       // 主排位段位榜             (不用来显示，只用于全区榜顶级段位切换逻辑；使用了镜像，注意清理时实时、镜像数据都要清理！)
        E_TAC_RANK_TYPE_TURBO_SCORE             = 24,       // Turbo玩法积分            (没有好友榜，只有全区榜, 满足先游二/三，已废弃)
        E_TAC_RANK_TYPE_SEGMENT_SHOW_SET_5      = 25,       // Set5段位榜               (外显，既有好友榜，又有全区榜，只针对赛季2使用，后续勿用)
        E_TAC_RANK_TYPE_SEGMENT_REALTIME_SET_5  = 26,       // Set5段位榜               (不用来显示，只用于全区榜顶级段位切换逻辑；使用了镜像，注意清理时实时、镜像数据都要清理！只针对赛季2使用，后续勿用)
        E_TAC_RANK_TYPE_MAJOR_TIER_WX           = 27,       // 微信跨区段位榜-主排位     (没有好友榜，只有全区榜)
        E_TAC_RANK_TYPE_MAJOR_TIER_QQ           = 28,       // QQ跨区段位榜-主排位       (没有好友榜，只有全区榜)
        E_TAC_RANK_TYPE_MINOR_TIER_WX           = 29,       // 微信跨区段位榜-副排位      (没有好友榜，只有全区榜)
        E_TAC_RANK_TYPE_MINOR_TIER_QQ           = 30,       // QQ跨区段位榜-副排位       (没有好友榜，只有全区榜)
        E_TAC_RANK_TYPE_SEGMENT_SHOW_MINOR      = 31,       // 副排位段位榜             (外显，既有好友榜，又有全区榜)
        E_TAC_RANK_TYPE_SEGMENT_REALTIME_MINOR  = 32,       // 副排位段位榜             (不用来显示，只用于全区榜顶级段位切换逻辑；使用了镜像，注意清理时实时、镜像数据都要清理！)
        E_TAC_RANK_TYPE_SEGMENT_SHOW_DUAL       = 33,       // 双人模式段位榜           (外显，既有好友榜，又有全区榜)
        E_TAC_RANK_TYPE_SEGMENT_REALTIME_DUAL   = 34,       // 双人模式段位榜           (不用来显示，只用于全区榜顶级段位切换逻辑；使用了镜像，注意清理时实时、镜像数据都要清理！)
        E_TAC_RANK_TYPE_DUAL_TIER_WX            = 35,       // 微信跨区双人模式段位榜   (没有好友榜，只有全区榜)
        E_TAC_RANK_TYPE_DUAL_TIER_QQ            = 36,       // QQ跨区双人模式段位榜     (没有好友榜，只有全区榜)
        E_TAC_RANK_TYPE_CLUB_WEEKLY_ACTIVITY_WX = 37,       // 微信跨区俱乐部周活跃榜     (只有全区榜)
        E_TAC_RANK_TYPE_CLUB_WEEKLY_ACTIVITY_QQ = 38,       // QQ跨区俱乐部周活跃榜     (只有全区榜)
        E_TAC_RANK_TYPE_CLUB_SEASON_ACTIVITY_WX = 39,       // 微信跨区俱乐部赛季活跃榜     (只有全区榜)
        E_TAC_RANK_TYPE_CLUB_SEASON_ACTIVITY_QQ = 40,       // QQ跨区俱乐部赛季活跃榜     (只有全区榜)
        E_TAC_RANK_TYPE_CONTEST_SCORE_WX        = 41,       // 福星赛积分榜-微信跨区        (只有全区榜)
        E_TAC_RANK_TYPE_CONTEST_SCORE_QQ        = 42,       // 福星赛积分榜-QQ跨区          (只有全区榜)
        E_TAC_RANK_TYPE_CONTEST_BP_LEVEL_WX     = 43,       // 福星赛BP等级榜-微信跨区      (只有全区榜)
        E_TAC_RANK_TYPE_CONTEST_BP_LEVEL_QQ     = 44,       // 福星赛BP等级榜-QQ跨区        (只有全区榜)
        E_TAC_RANK_TYPE_SEGMENT_SHOW_TURBO      = 45,       // 段位榜-狂暴          (只有全区榜)
        E_TAC_RANK_TYPE_TURBO_WX                = 46,       // 微信跨区狂暴模式榜     (没有好友榜，只有全区榜)
        E_TAC_RANK_TYPE_TURBO_QQ                = 47,       // QQ跨区狂暴模式榜     (没有好友榜，只有全区榜)
        E_TAC_RANK_TYPE_CARDCOLLECT_CARD_WX_DEPRECATED = 48,  //废弃
        E_TAC_RANK_TYPE_CARDCOLLECT_CARD_QQ_DEPRECATED = 49,  //废弃
        E_TAC_RANK_TYPE_FRIEND_WIN_COUNT_MAJOR   = 54,       // 主排位好友登顶榜
        E_TAC_RANK_TYPE_FRIEND_WIN_COUNT_MINOR   = 55,       // 副排位好友登顶榜
        E_TAC_RANK_TYPE_FRIEND_WIN_COUNT_DUAL    = 56,       // 双人排位好友登顶榜
        E_TAC_RANK_TYPE_FRIEND_WIN_COUNT_TURBO   = 57,       // 狂暴排位好友登顶榜
        E_TAC_RANK_TYPE_FRIEND_WIN_COUNT_TIME_LIMIT = 58,   // 限时赛季排位好友登顶榜
        E_TAC_RANK_TYPE_FRIEND_TIER_MAJOR  = 59,            // 主排位好友段位榜
        E_TAC_RANK_TYPE_FRIEND_TIER_MINOR  = 60,            // 副排位好友段位榜
        E_TAC_RANK_TYPE_FRIEND_TIER_DUAL   = 61,            // 双人排位好友段位榜
        E_TAC_RANK_TYPE_FRIEND_TIER_TURBO  = 62,            // 狂暴排位好友段位榜
        E_TAC_RANK_TYPE_FRIEND_TIER_TIME_LIMIT = 63,        // 限时赛季排位好友段位榜
        E_TAC_RANK_TYPE_SEGMENT_SHOW_TIME_LIMIT = 50,       // 限时赛季段位榜
        E_TAC_RANK_TYPE_SEGMENT_REALTIME_TIME_LIMIT = 51,   // 限时赛季段位榜
        E_TAC_RANK_TYPE_TIME_LIMIT_TIER_WX      = 52,       // 微信跨区段位榜-限时赛季
        E_TAC_RANK_TYPE_TIME_LIMIT_TIER_QQ      = 53,       // QQ跨区段位榜-限时赛季
        E_TAC_RANK_TYPE_ACHIEVEMENT = 64,                   //成就小区榜单
        E_TAC_RANK_TYPE_ACHIEVEMENT_WX = 65,                //成就巅峰榜单wx
        E_TAC_RANK_TYPE_ACHIEVEMENT_QQ = 66,                //成就巅峰榜单qq
        E_TAC_RANK_TYPE_FRIEND_ACHIEVEMENT = 67,            // 成就好友榜                   (好友榜)
        E_TAC_RANK_TYPE_SOCIAL_CELEBRITY_WX = 68,           //微信名流值全区榜
        E_TAC_RANK_TYPE_SOCIAL_CELEBRITY_QQ = 69,           //QQ名流值全区榜
        E_TAC_RANK_TYPE_SOCIAL_POPULARITY_WX = 70,          //微信人气全区榜
        E_TAC_RANK_TYPE_SOCIAL_POPULARITY_QQ = 71,          //QQ人气全区榜
        E_TAC_RANK_TYPE_SOCIAL_CELEBRITY = 72,              //名流值大区榜
        E_TAC_RANK_TYPE_SOCIAL_POPULARITY = 73,             //人气值大区榜
        E_TAC_RANK_TYPE_FRIEND_SOCIAL_CELEBRITY = 74,       //名流值好友榜
        E_TAC_RANK_TYPE_FRIEND_SOCIAL_POPULARITY = 75,      //人气值好友榜
        E_TAC_RANK_TYPE_MAJOR_TIER_MASTER_BELOW = 76,
        E_TAC_RANK_TYPE_MAJOR_TIER_APEX2 = 77,
        E_TAC_RANK_TYPE_MAJOR_TIER_APEX3 = 78,
        E_TAC_RANK_TYPE_MINOR_TIER_MASTER_BELOW = 79,
        E_TAC_RANK_TYPE_MINOR_TIER_APEX2 = 80,
        E_TAC_RANK_TYPE_MINOR_TIER_APEX3 = 81,
        E_TAC_RANK_TYPE_DUAL_TIER_MASTER_BELOW = 82,
        E_TAC_RANK_TYPE_DUAL_TIER_APEX2 = 83,
        E_TAC_RANK_TYPE_DUAL_TIER_APEX3 = 84,
        E_TAC_RANK_TYPE_TIME_LIMIT_TIER_MASTER_BELOW = 85,
        E_TAC_RANK_TYPE_TIME_LIMIT_TIER_APEX2 = 86,
        E_TAC_RANK_TYPE_TIME_LIMIT_TIER_APEX3 = 87,
        E_TAC_RANK_TYPE_MAJOR_TIER_MASTER_BELOW_WX = 88,
        E_TAC_RANK_TYPE_MAJOR_TIER_APEX2_WX = 89,
        E_TAC_RANK_TYPE_MAJOR_TIER_APEX3_WX = 90,
        E_TAC_RANK_TYPE_MINOR_TIER_MASTER_BELOW_WX = 91,
        E_TAC_RANK_TYPE_MINOR_TIER_APEX2_WX = 92,
        E_TAC_RANK_TYPE_MINOR_TIER_APEX3_WX = 93,
        E_TAC_RANK_TYPE_DUAL_TIER_MASTER_BELOW_WX = 94,
        E_TAC_RANK_TYPE_DUAL_TIER_APEX2_WX = 95,
        E_TAC_RANK_TYPE_DUAL_TIER_APEX3_WX = 96,
        E_TAC_RANK_TYPE_TIME_LIMIT_TIER_MASTER_BELOW_WX = 97,
        E_TAC_RANK_TYPE_TIME_LIMIT_TIER_APEX2_WX = 98,
        E_TAC_RANK_TYPE_TIME_LIMIT_TIER_APEX3_WX = 99,
        E_TAC_RANK_TYPE_MAJOR_TIER_MASTER_BELOW_QQ = 100,
        E_TAC_RANK_TYPE_MAJOR_TIER_APEX2_QQ = 101,
        E_TAC_RANK_TYPE_MAJOR_TIER_APEX3_QQ = 102,
        E_TAC_RANK_TYPE_MINOR_TIER_MASTER_BELOW_QQ = 103,
        E_TAC_RANK_TYPE_MINOR_TIER_APEX2_QQ = 104,
        E_TAC_RANK_TYPE_MINOR_TIER_APEX3_QQ = 105,
        E_TAC_RANK_TYPE_DUAL_TIER_MASTER_BELOW_QQ = 106,
        E_TAC_RANK_TYPE_DUAL_TIER_APEX2_QQ = 107,
        E_TAC_RANK_TYPE_DUAL_TIER_APEX3_QQ = 108,
        E_TAC_RANK_TYPE_TIME_LIMIT_TIER_MASTER_BELOW_QQ = 109,
        E_TAC_RANK_TYPE_TIME_LIMIT_TIER_APEX2_QQ = 110,
        E_TAC_RANK_TYPE_TIME_LIMIT_TIER_APEX3_QQ = 111,
        E_TAC_RANK_TYPE_MAJOR_TIER_INNER_REALTIME = 112,
        E_TAC_RANK_TYPE_MINOR_TIER_INNER_REALTIME = 113,
        E_TAC_RANK_TYPE_DUAL_TIER_INNER_REALTIME = 114,
        E_TAC_RANK_TYPE_TIME_LIMIT_TIER_INNER_REALTIME = 115,
        E_TAC_RANK_TYPE_100_PLAYERS_LP_SEGMENT = 116,
        E_TAC_RANK_TYPE_100_PLAYERS_WIN_SEGMENT = 117,
        E_TAC_RANK_TYPE_100_PLAYERS_LP_QQ = 118,
        E_TAC_RANK_TYPE_100_PLAYERS_LP_WX = 119,
        E_TAC_RANK_TYPE_100_PLAYERS_WIN_QQ = 120,
        E_TAC_RANK_TYPE_100_PLAYERS_WIN_WX = 121,
        E_TAC_RANK_TYPE_100_PLAYERS_LP_FRIEND = 122,
        E_TAC_RANK_TYPE_100_PLAYERS_WIN_FRIEND = 123,
        E_TAC_RANK_TYPE_JOC_LP = 124,
        E_TAC_RANK_TYPE_SOUL_FIGHTER_LP_SEGMENT = 129,
        E_TAC_RANK_TYPE_SOUL_FIGHTER_WIN_SEGMENT = 130,
        E_TAC_RANK_TYPE_SOUL_FIGHTER_LP_QQ = 131,
        E_TAC_RANK_TYPE_SOUL_FIGHTER_LP_WX = 132,
        E_TAC_RANK_TYPE_SOUL_FIGHTER_WIN_QQ = 133,
        E_TAC_RANK_TYPE_SOUL_FIGHTER_WIN_WX = 134,
        E_TAC_RANK_TYPE_SOUL_FIGHTER_LP_FRIEND = 135,
        E_TAC_RANK_TYPE_SOUL_FIGHTER_WIN_FRIEND = 136,
        E_TAC_RANK_TYPE_CARDCOLLECT_CARD_WX = 160,
        E_TAC_RANK_TYPE_CARDCOLLECT_CARD_QQ = 161,
        E_TAC_RANK_TYPE_GXFC_LP_SEGMENT = 162,
        E_TAC_RANK_TYPE_GXFC_LP_QQ = 164,
        E_TAC_RANK_TYPE_GXFC_LP_WX = 165,
        E_TAC_RANK_TYPE_GONGXIFACHA = 168,
        E_TAC_RANK_TYPE_TOPNEXT_END = 9999,
        E_TAC_RANK_TYPE_TOPN_BEGIN = 10000,
        E_TAC_RANK_TYPE_MAX,
    }
    public enum E_RANK_DATA_TYPE
    {
        E_RANK_DATA_TYPE_NULL = 0,
        E_RANK_DATA_TYPE_USER = 1,
        E_RANK_DATA_TYPE_CLUB = 2,
        E_RANK_DATA_TYPE_CARD_COLLECT = 3,
        E_RANK_DATA_TYPE_MAX,
    }

    /// <summary>
    /// 存在：
    /// 【1】全区类型的榜单：
    ///     协议1+参数
    /// 【2】好友类型的榜单：
    ///     协议2+参数
    /// 
    /// 其中，【1】、【2】的参数 是 E_TAC_RANK_TYPE，会复用，
    /// 因此需要在客户端自己做区分。所以有了这个客户端用的枚举
    /// </summary>
    public enum E_RANK_TYPE_CLIENT
    {
        E_RANK_TYPE_CLIENT_TIER_ZONE            = 1,        // 段位榜—全区
        E_RANK_TYPE_CLIENT_TIER_ZONE_MINOR      = 2,        // 段位榜—全区_副排位
        E_RANK_TYPE_CLIENT_TIER_ZONE_DUAL       = 3,        // 段位榜—全区_双人模式                
        E_RANK_TYPE_CLIENT_TIER_ZONE_SET45      = 4,        // 段位榜—全区_SET45             
        E_RANK_TYPE_CLIENT_TIER_ZONE_TURBO      = 5,        // 段位榜—全区_狂暴模式
        
        E_RANK_TYPE_CLIENT_TIER_FRIEND          = 8,        // 段位榜—好友
        E_RANK_TYPE_CLIENT_TIER_FRIEND_MINOR    = 9,        // 段位榜—好友_副排位
        E_RANK_TYPE_CLIENT_TIER_FRIEND_DUAL     = 10,       // 段位榜—好友_双人模式
        E_RANK_TYPE_CLIENT_TIER_FRIEND_TURBO    = 11,       // 段位榜—好友_狂暴模式
        E_RANK_TYPE_CLIENT_TIER_FRIEND_SET45    = 12,       // 段位榜—好友_SET45

        E_RANK_TYPE_CLIENT_WIN_MMR_RANK         = 15,       // 登顶榜—好友排位模式
        E_RANK_TYPE_CLIENT_WIN_RELAX_SINGLE     = 16,       // 登顶榜—好友匹配模式
        // E_RANK_TYPE_CLIENT_WIN_MMR_RANK         = 15,       // 登顶榜—好友排位模式
        // E_RANK_TYPE_CLIENT_WIN_RELAX_SINGLE     = 16,       // 登顶榜—好友匹配模式
        E_RANK_TYPE_CLIENT_WIN_FRIEND          = 15,        // 登顶榜—好友
        E_RANK_TYPE_CLIENT_WIN_FRIEND_MINOR    = 16,        // 登顶榜—好友_副排位
        E_RANK_TYPE_CLIENT_WIN_FRIEND_DUAL     = 17,        // 登顶榜—好友_双人模式
        E_RANK_TYPE_CLIENT_WIN_FRIEND_TURBO    = 18,        // 登顶榜—好友_狂暴模式
        E_RANK_TYPE_CLIENT_WIN_FRIEND_SET45    = 19,        // 登顶榜—好友_SET45
                
        E_RANK_TYPE_CLIENT_WIX_QQ_TIER_MAJOR    = 20,       // 主排位-跨区
        E_RANK_TYPE_CLIENT_WIX_QQ_TIER_MINOR    = 21,       // 副排位-跨区
        E_RANK_TYPE_CLIENT_WIX_QQ_TIER_DUAL     = 22,       // 双人-跨区
        E_RANK_TYPE_CLIENT_WIX_QQ_SHOW_TURBO    = 23,       // 狂暴-跨区
        E_RANK_TYPE_CLIENT_WIX_QQ_TIER_MAJOR_MATCH_SET45    = 24,       // Set4.5主排位-跨区
        E_RANK_TYPE_CLIENT_WIX_QQ_TIER_MAJOR_RANK_SET45    = 25,       // Set4.5主排位-跨区
                
        E_RANK_TYPE_CLIENT_TOP_FOUR_ZONE        = 50,       // 获胜(前四)榜—全区
        E_RANK_TYPE_CLIENT_TOP_FOUR_FRIEND      = 51,       // 获胜(前四)榜—好友
        E_RANK_TYPE_CLIENT_TURBO_SCORE          = 60,       // Turbo玩法积分(临时满足先游二)
        E_RANK_TYPE_CLIENT_TIER_ZONE_SET_5      = 70,       // 段位榜—全区Set5
        E_RANK_TYPE_CLIENT_TIER_FRIEND_SET_5    = 71,       // 段位榜—好友Set5
        E_RANK_TYPE_CLIENT_SEGMENT_SHOW_TURBO   = 72,       // 狂暴榜单
        E_RANK_TYPE_CLIENT_ACHIEVEMENT_ZONE = 73,               //成就本区榜
        E_RANK_TYPE_CLIENT_ACHIEVEMENT_FRIEND = 74,          //成就好友榜
        E_RANK_TYPE_CLIENT_ACHIEVEMENT_CROSS_ZONE = 75,         //成就全区榜
        E_RANK_TYPE_CLIENT_CELEBRITY_ZONE = 76,             //名流本区榜单
        E_RANK_TYPE_CLIENT_CELEBRITY_FRIEND = 77,           //名流值好友榜
        E_RANK_TYPE_CLIENT_CELEBRITY_CROSS_ZONE  = 78,      //名流值全区榜
        E_RANK_TYPE_CLIENT_POPULARITY_ZONE  =   79,         //人气值本区榜
        E_RANK_TYPE_CLIENT_POPULARITY_FRIEND = 80,          //人气值好友榜
        E_RANK_TYPE_CLIENT_POPULARITY_CROSS_ZONE = 81,      //人气值全区榜
        
        E_RANK_TYPE_CLIENT_TIER_MASTER_BELOW = 100,         //主排位段位榜（大师及以下）
        E_RANK_TYPE_CLIENT_TIER_MASTER_APEX2 = 101,         //主排位段位榜（宗师）
        E_RANK_TYPE_CLIENT_TIER_MASTER_APEX3 = 102,         //主排位段位榜（王者）
        E_RANK_TYPE_CLIENT_TIER_MINOR_BELOW = 103,         //主排位段位榜（大师及以下）
        E_RANK_TYPE_CLIENT_TIER_MINOR_APEX2 = 104,         //主排位段位榜（宗师）
        E_RANK_TYPE_CLIENT_TIER_MINOR_APEX3 = 105,         //主排位段位榜（王者）
        E_RANK_TYPE_CLIENT_TIER_DUAL_BELOW = 106,         //主排位段位榜（大师及以下）
        E_RANK_TYPE_CLIENT_TIER_DUAL_APEX2 = 107,         //主排位段位榜（宗师）
        E_RANK_TYPE_CLIENT_TIER_DUAL_APEX3 = 108,         //主排位段位榜（王者）
        E_RANK_TYPE_CLIENT_TIER_TIME_LIMIT_BELOW = 109,         //主排位段位榜（大师及以下）
        E_RANK_TYPE_CLIENT_TIER_TIME_LIMIT_APEX2 = 110,         //主排位段位榜（宗师）
        E_RANK_TYPE_CLIENT_TIER_TIME_LIMIT_APEX3 = 111,         //主排位段位榜（王者）
        
        // E_RANK_TYPE_CLIENT_TIER_MASTER_BELOW_WX_QQ = 120,         //主排位段位榜（大师及以下）
        // E_RANK_TYPE_CLIENT_TIER_MASTER_APEX2_WX_QQ = 121,         //主排位段位榜（宗师）
        // E_RANK_TYPE_CLIENT_TIER_MASTER_APEX3_WX_QQ = 122,         //主排位段位榜（王者）
        // E_RANK_TYPE_CLIENT_TIER_MINOR_BELOW_WX_QQ = 123,         //主排位段位榜（大师及以下）
        // E_RANK_TYPE_CLIENT_TIER_MINOR_APEX2_WX_QQ = 124,         //主排位段位榜（宗师）
        // E_RANK_TYPE_CLIENT_TIER_MINOR_APEX3_WX_QQ = 125,         //主排位段位榜（王者）
        // E_RANK_TYPE_CLIENT_TIER_DUAL_BELOW_WX_QQ = 126,         //主排位段位榜（大师及以下）
        // E_RANK_TYPE_CLIENT_TIER_DUAL_APEX2_WX_QQ = 127,         //主排位段位榜（宗师）
        // E_RANK_TYPE_CLIENT_TIER_DUAL_APEX3_WX_QQ = 128,         //主排位段位榜（王者）
        // E_RANK_TYPE_CLIENT_TIER_TIME_LIMIT_BELOW_WX_QQ = 129,         //主排位段位榜（大师及以下）
        // E_RANK_TYPE_CLIENT_TIER_TIME_LIMIT_APEX2_WX_QQ = 130,         //主排位段位榜（宗师）
        // E_RANK_TYPE_CLIENT_TIER_TIME_LIMIT_APEX3_WX_QQ = 131,         //主排位段位榜（王者）
        
        E_RANK_TYPE_CLIENT_100_ZONE_TIER_QQ_WX = 120,
        E_RANK_TYPE_CLIENT_100_ZONE_TIER = 121,
        E_RANK_TYPE_CLIENT_100_TIER_FRIEND = 122,
        E_RANK_TYPE_CLIENT_100_ZONE_WINCOUNT_QQ_WX = 123,
        E_RANK_TYPE_CLIENT_100_ZONE_WINCOUNT_TIER = 124,
        E_RANK_TYPE_CLIENT_100_WINCOUNT_FRIEND = 125,

        E_RANK_TYPE_CLIENT_JOC_ZONE_TIER = 130,
        
        E_RANK_TYPE_CLIENT_SOUL_FIGHTER_TIER_QQ_WX = 140,
        E_RANK_TYPE_CLIENT_SOUL_FIGHTER_ZONE_TIER = 141,
        E_RANK_TYPE_CLIENT_SOUL_FIGHTER_TIER_FRIEND = 142,
        E_RANK_TYPE_CLIENT_SOUL_FIGHTER_WINCOUNT_QQ_WX = 143,
        E_RANK_TYPE_CLIENT_SOUL_FIGHTER_ZONE_WINCOUNT = 144,
        E_RANK_TYPE_CLIENT_SOUL_FIGHTER_WINCOUNT_FRIEND = 145,

        E_RANK_TYPE_CLIENT_TIME_LIMIT_TIER_QQ_WX_DUAL = 150,
        E_RANK_TYPE_CLIENT_TIME_LIMIT_ZONE_TIER_DUAL = 151,
        E_RANK_TYPE_CLIENT_TIME_LIMIT_TIER_FRIEND_DUAL = 152,
        E_RANK_TYPE_CLIENT_TIME_LIMIT_WIN_FRIEND_DUAL = 153,
        E_RANK_TYPE_CLIENT_TIER_TIME_LIMIT_BELOW_DUAL = 154,         //主排位段位榜（大师及以下）
        E_RANK_TYPE_CLIENT_TIER_TIME_LIMIT_APEX2_DUAL = 155,         //主排位段位榜（宗师）
        E_RANK_TYPE_CLIENT_TIER_TIME_LIMIT_APEX3_DUAL = 156,         //主排位段位榜（王者）
        
        E_RANK_TYPE_CLIENT_CONTEST              = 100000,   // 赛事排行榜(动态生成, 区间在100000-199999)
        E_RANK_TYPE_CLIENT_CARDCOLLECT_ZONE     = 10000000, // 魔典徽章全区排行榜(根据卡牌ID动态生成, 区间在10000000-19999999)
        E_RANK_TYPE_CLIENT_CARDCOLLECT_FRIEND   = 20000000, // 魔典徽章好友排行榜(根据卡牌ID动态生成, 区间在20000000-29999999)

        E_RANK_TYPE_CLIENT_NONE                 = 999999999,   // 错误处理
    }
    public enum E_RANK_TYPE_SHOWTYPE_CLIENT
    {
        E_RANK_SHOWTYPE_CLIENT_TIER = 1,
        E_RANK_SHOWTYPE_CLIENT_WIN = 2,
        E_RANK_SHOWTYPE_CLIENT_TOPFOUR = 3,
        E_RANK_SHOWTYPE_CLIENT_WEALTH = 4,
        E_RANK_SHOWTYPE_CLIENT_TURBOSCORE = 5,
        E_RANK_SHOWTYPE_CLIENT_KILLCOUNT = 6,
        E_RANK_SHOWTYPE_CLIENT_CONTEST = 7,
        E_RANK_SHOWTYPE_CLIENT_SEGMENTTURBO = 8,
        E_RANK_SHOWTYPE_CLIENT_CONTEST_LEVEL = 9,
        E_RANK_SHOWTYPE_CLIENT_CARDSCORE = 10,
        E_RANK_SHOWTYPE_CLIENT_ACHIEVEMENT = 11,
        E_RANK_SHOWTYPE_CLIENT_CELEBRARITY = 12,
        E_RANK_SHOWTYPE_CLIENT_POPULARYITY = 13,
        E_RANK_SHOWTYPE_CLIENT_JOC = 14,
        E_RANK_SHOWTYPE_CLIENT_JOCS10 = 15,
        E_RANK_SHOWTYPE_CLIENT_JOCS10_32 = 16,
    }
    public enum E_PLAYER_ROOM_ROLE_TYPE
    {
        E_PLAYER_ROOM_ROLE_TYPE_MEMBER = 1,
        E_PLAYER_ROOM_ROLE_TYPE_HOST = 2,
    }
    public enum E_PLAYER_ROOM_STATUS
    {
        E_PLAYER_ROOM_STATUS_IDLE = 1,
        E_PLAYER_ROOM_ROLE_TYPE_IN_GAME = 2,
    }
    public enum E_AUTOFIGHT_ROOMTYPE
    {
        E_AUTOFIGHT_ROOMTYPE_FRIEND = 2,
        E_AUTOFIGHT_ROOMTYPE_OBSERVER = 3,
    }
    public enum E_TAC_DAILY_ACTIVE_TASK_STATUS
    {
        E_TAC_DAILY_ACTIVE_RECEIVED = 1,
        E_TAC_DAILY_ACTIVE_INCOMPLETE = 2,
        E_TAC_DAILY_ACTIVE_COMPLETED = 3,
        E_TAC_DAILY_ACTIVE_LOCKED = 4,
    }
    public enum E_SITDOWN_JOIN_SCEN
    {
        E_SITDOWN_JOIN_SCEN_COMM = 0,
        E_SITDOWN_JOIN_SCEN_FRIEND_HOMEOWNER_JOIN = 1,
        E_SITDOWN_JOIN_SCEN_FRIEND_INVITE = 2,
        E_SITDOWN_JOIN_SCEN_FRIEND_HOMEOWNER_JOIN_AND_BEGIN = 4,
    }
    public enum E_JOIN_ROOM_TYPE
    {
        E_JOIN_ROOM_TYPE_UNKOWN = 0,
        E_JOIN_ROOM_TYPE_APPLE = 1,
        E_JOIN_ROOM_TYPE_INVITE = 2,
        E_JOIN_ROOM_TYPE_WLORD = 3,
        E_JOIN_ROOM_TYPE_WX_MINIAPP = 4,
        E_JOIN_ROOM_TYPE_PLAY_AGAIN = 5,
        E_JOIN_ROOM_TYPE_GUILD = 6,               // 公会玩法加入
    }
    public enum E_AUTOFIGHT_MATCH_STATE
    {
        E_AUTOFIGHT_MATCH_STATE_MATCHING = 0,
        E_AUTOFIGHT_MATCH_STATE_FINISH = 1,
        E_AUTOFIGHT_MATCH_STATE_FRIEND_HOSTOWNER_WAITING = 2,
        E_AUTOFIGHT_MATCH_STATE_OPENGAMEFAIL = 3,
        E_AUTOFIGHT_MATCH_STATE_MATCHFAIL = 4,
        E_AUTOFIGHT_MATCH_RANKING_MATCH_TIMEOUT_FAIL = 5,
        E_AUTOFIGHT_MATCH_STATE_PRESTART = 6,
    }
    public enum E_SYS_FRIEND_GAME_STATUS
    {
        E_SYS_FRIEND_GAME_STATUS_FREE = 0,
        E_SYS_FRIEND_GAME_STATUS_PLAYING = 1,
        E_SYS_FRIEND_GAME_STATUS_OFFLINE = 2,
        E_SYS_FRIEND_GAME_STATUS_MATCHING = 3,
    }
    public enum E_FRIEND_APPROVAL_OPT
    {
        E_FRIEND_APPROVAL_OPT_AGREE = 0,
        E_FRIEND_APPROVAL_OPT_IGNORE = 1,
    }
    public enum E_RANK_GAME_STATUS
    {
        E_RANK_GAME_STATUS_FREE = 0,
        E_RANK_GAME_STATUS_PLAYING = 1,
        E_RANK_GAME_STATUS_OFFLINE = 2,
        E_RANK_GAME_STATUS_MATCHING = 3,
    }
    public enum E_TAC_BATTLE_PASS_PAY_TYPE
    {
        E_TAC_BATTLE_PASS_PAY_PASS = 1,
        E_TAC_BATTLE_PASS_PAY_PASS_AND_LV = 2,
        E_TAC_BATTLE_PASS_PAY_1LV = 3,
        E_TAC_BATTLE_PASS_PAY_EXP = 4,
    }
    public enum E_HELLO_SCENE_ID
    {
        E_HELLO_SCENE_ID_MAIN = 1,
        E_HELLO_SCENE_ID_MATCH_OPENROOM = 2,
        E_HELLO_SCENE_ID_MATCH_MATCHQUEUE = 3,
        E_HELLO_SCENE_ID_MATCH_FINISHMATCH = 4,
        E_HELLO_SCENE_ID_MATCH_FIGHT = 5,
        E_HELLO_SCENE_ID_MATCH_PRESTART = 6,
    }
    public enum E_TAC_DROP_GROUP_STRATEGY
    {
        E_TAC_DROP_GROUP_STRATEGY_NULL = 0,
        E_TAC_DROP_GROUP_STRATEGY_WHOLE = 1,
        E_TAC_DROP_GROUP_STRATEGY_EACH = 2,
        E_TAC_DROP_GROUP_STRATEGY_NO_RETURN = 3,
        E_TAC_DROP_GROUP_STRATEGY_EGG = 4,
        E_TAC_DROP_GROUP_STRATEGY_SELF_CHOOSE = 6,
        E_TAC_DROP_GROUP_STRATEGY_TINY_HERO = 7,
        E_TAC_DROP_GROUP_STRATEGY_NO_RETURN_2 = 8,
        E_TAC_DROP_GROUP_STRATEGY_VARIABLE_PRICE = 9,
        E_TAC_DROP_GROUP_STRATEGY_NO_RETURN_WITH_UNIQUE_ID = 10,
        E_TAC_DROP_GROUP_STRATEGY_MAX,
    }
    public enum E_TAC_DROP_GROUP_TRAVERSE
    {
        E_TAC_DROP_GROUP_TRAVERSE_NULL = 0,
        E_TAC_DROP_GROUP_TRAVERSE_ASCEND = 1,
        E_TAC_DROP_GROUP_TRAVERSE_DESCEND = 2,
        E_TAC_DROP_GROUP_TRAVERSE_MAX = 3,
    }
    public enum E_TAC_DROP_GROUP_ENSURE
    {
        E_TAC_DROP_GROUP_ENSURE_NULL = 0,
        E_TAC_DROP_GROUP_ENSURE_ONCE = 1,
        E_TAC_DROP_GROUP_ENSURE_RESET = 2,
        E_TAC_DROP_GROUP_ENSURE_MOD = 3,
        E_TAC_DROP_GROUP_ENSURE_TINY_HERO = 4,
        E_TAC_DROP_GROUP_ENSURE_MOD_RANDOM = 5,
        E_TAC_DROP_GROUP_ENSURE_ONCE_NO_DROP = 6,
        E_TAC_DROP_GROUP_ENSURE_MAX = 7,
    }
    public enum E_TAC_USER_SHARE_TYPE
    {
        E_TAC_USER_SHARE_TYPE_GAME_RESULT = 1,
        E_TAC_USER_SHARE_TYPE_CAPTAIN = 2,
        E_TAC_USER_SHARE_TYPE_GAME_BEGIN = 3,
        E_TAC_USER_SHARE_TYPE_UP_TIER = 4,
        E_TAC_USER_SHARE_TYPE_MVP_HERO = 5,
        E_TAC_USER_SHARE_TYPE_RANK_CHANGE = 6,
        E_TAC_USER_SHARE_TYPE_PICTURE = 7,
        E_TAC_USER_SHARE_TYPE_USERINFO = 8,
        E_TAC_USER_SHARE_TYPE_RANK_FIRST = 9,
        E_TAC_USER_SHARE_TYPE_GET_TINY = 10,
        E_TAC_USER_SHARE_TYPE_MACTH_MINI_GAME = 11,
        E_TAC_USER_SHARE_TYPE_MATCH_ARK = 12,
        E_TAC_USER_SHARE_TYPE_MATCH_RECORD_MINI_GAME = 13,
        E_TAC_USER_SHARE_TYPE_BOSS_CHALLENGE_MINI_GAME = 14,
        E_TAC_USER_SHARE_TYPE_GET_MAP = 15,
        E_TAC_USER_SHARE_TYPE_WAREHOUSE_TINY = 16,
        E_TAC_USER_SHARE_TYPE_WAREHOUSE_MAP = 17,
        E_TAC_USER_SHARE_TYPE_INNERBLESS_BAG = 18,
        E_TAC_USER_SHARE_TYPE_GAME_RECORD = 19,
        E_TAC_USER_SHARE_TYPE_PICTURE2 = 20,
        E_TAC_USER_SHARE_TYPE_GLOBAL = 21,
        E_TAC_USER_SHARE_TYPE_SHOT_SCREEN = 22,
        E_TAC_USER_SHARE_TYPE_CARD_COLLECT_CARD_DETAILS = 24,
        E_TAC_USER_SHARE_TYPE_CARD_COLLECT_CARD_RANK = 25,
        E_TAC_USER_SHARE_TYPE_CARD_COLLECT_OPEN_PACK = 26,
        E_TAC_USER_SHARE_TYPE_TEAMRECOMMEND = 27,
        E_TAC_USER_SHARE_TYPE_ACHIEVEMENT = 28,
        E_TAC_USER_SHARE_TYPE_WARECOLLECTION = 29,
        E_TAC_USER_SHARE_TYPE_SKIN_COLLECT = 30,
        E_TAC_USER_SHARE_TYPE_HEROES_UNITE = 31,
        E_TAC_USER_SHARE_USER_RANK = 32,
        E_TAC_USER_SHARE_WORLD_RANK = 33,
        E_TAC_USER_SHARE_LUCK_DRAW = 34,
        E_TAC_USER_SHARE_TYPE_RANKSYS = 37,
        E_TAC_USER_SHARE_TYPE_WEEKLY_SHARE = 38,
    }
    public enum E_TAC_USER_SHARE_SUB_TYPE
    {
        E_TAC_USER_SHARE_SUB_TYPE_CLICK = 1,
        E_TAC_USER_SHARE_SUB_TYPE_WECHAT = 2,
        E_TAC_USER_SHARE_SUB_TYPE_WECHAT_PUBLIC = 3,
        E_TAC_USER_SHARE_SUB_TYPE_QQ = 4,
        E_TAC_USER_SHARE_SUB_TYPE_QQ_PUBLIC = 5,
        E_TAC_USER_SHARE_SUB_TYPE_ZM = 6,
        E_TAC_USER_SHARE_SUB_TYPE_CLUB = 7, 
        E_TAC_USER_SHARE_SUB_TYPE_GAMEFRIEND = 8,
        E_TAC_USER_SHARE_SUB_TYPE_MANUAL_TRIGGER = 9,
        E_TAC_USER_SHARE_SUB_TYPE_AUTO_TRIGGER = 10,
        E_TAC_USER_SHARE_SUB_TYPE_XHS = 11,
    }
    public enum E_TAC_USER_VIEW_TYPE
    {
        E_TAC_USER_VIEW_TYPE_LEADERBOARD_RANKED = 1,
        E_TAC_USER_VIEW_TYPE_HEADLINE = 11,
        E_TAC_USER_VIEW_TYPE_REPLAY = 12,
    }
    public enum E_SHARE_TRIGGER_TYPE
    {
        E_SHARE_TRIGGER_DAILY = 0,
        E_SHARE_TRIGGER_UP_TIER = 1,
    }
    public enum TAC_TinyPassiveExpressionType
    {
        TAC_TinyPassiveExpressionType_Start = 0,
        TAC_TinyPassiveExpressionType_Win = 1,
        TAC_TinyPassiveExpressionType_ContinueWin = 2,
        TAC_TinyPassiveExpressionType_CompuseThreeStar = 3,
        TAC_TinyPassiveExpressionType_First = 4,
        TAC_TinyPassiveExpressionType_EndContinue = 5,
    }
    public enum E_SVR_MALL_GOODS_TAG
    {
        E_SVR_TAG_HOT = 1,
        E_SVR_TAG_NEW = 2,
        E_SVR_TAG_DISCOUNT = 3,
        E_SVR_TAG_LIMIT_TIME = 4,
        E_SVR_TAG_LIMIT_NUM = 5,
    }
    public enum E_SVR_MALL_BUY_EGGS_TYPE
    {
        E_SVR_EGG_BUY_TYPE_SINGLE = 1,
        E_SVR_EGG_BUY_TYPE_BATCH = 2,
    }
    public enum E_PAGE_PANEL_TYPE
    {
        E_PAGE_PANEL_TYPE_RECOMMAND = 100,
        E_PAGE_PANEL_TYPE_CAPSULE = 200,
        E_PAGE_PANEL_TYPE_MAPSPINE = 301,
        E_PAGE_PANEL_TYPE_EXPRESSION = 302,
        E_PAGE_PANEL_TYPE_PORTAL = 303,
        E_PAGE_PANEL_TYPE_ATTACKEFFECT = 304,
        E_PAGE_PANEL_TYPE_FOOTPRINT = 305,
        E_PAGE_PANEL_TYPE_NORMALITEM = 400,
        E_PAGE_PANEL_TYPE_TOKENSHOP = 500,
        E_PAGE_PANEL_TYPE_TOKEN_PROJECT = 501,
        E_PAGE_PANEL_TYPE_TOKEN_WORLD_CHAMPIONSHIP = 502,
        E_PAGE_PANEL_TYPE_TOKEN_CARNIVAL = 503,
        E_PAGE_PANEL_TYPE_DIRECT_BUY = 1000,
    }
    public enum E_SVR_BPMALL_REFRESH_TYPE
    {
        E_SVR_BPMALL_REFRESH_TYPE_NORMAL = 1,
        E_SVR_BPMALL_REFRESH_TYPE_SUPER = 2,
    }
    public enum E_MAIL_ID
    {
        E_MAIL_ID_NEW_PLAYER_OFFICIAL = 1,
        E_MAIL_ID_BATTLEPASS_COMPENSATE = 2,
        E_MAIL_ID_NEW_PLAYER_SYSTEM = 3,
        E_MAIL_ID_DELAY_BATTLE_RESULT = 7,
        E_MAIL_ID_NOVICE_TASK_COMPENSATE = 13,
        E_MAIL_ID_BOUNTY_GAME_SINGLE_REWARD = 14,
        E_MAIL_ID_BOUNTY_GAME_PASS_MISSION_REWARD = 15,
        E_MAIL_ID_BOUNTY_GAME_PASS_ALL_MISSION_ONETIME_REWARD = 16,
        E_MAIL_ID_GIVE_ITEM_BY_DIRECT_BUY = 21,
        E_MAIL_ID_BOUNTY_GAME_100PLAYER_REWARD = 22,
        E_MAIL_ID_EXPERIENCE_TINY_EXPIRED = 23,
        E_MAIL_ID_EXPERIENCE_TINY_MAP_EXPIRED = 24,
        E_MAIL_ID_QQ_GAME_PER_DAY_LOGIN_REWARD = 29,
        E_MAIL_ID_WX_GAME_PER_DAY_LOGIN_REWARD = 30,
        E_MAIL_ID_DATA_MORE_RETURN_USER_FIRST_MATCH_REWARD = 31,
        E_MAIL_ID_ITEM_EXPIRE_AND_RETURN_ITEM = 32,
        E_MAIL_ID_BAN_HEAD_URL = 34,
        E_MAIL_ID_CONSECUTIVE_DAY_CHALLENGE_UNCLAIMED_REWARD = 35,
        E_MAIL_ID_CONSECUTIVE_DAY_TASK_UNCLAIMED_REWARD = 37,
        E_MAIL_ID_EXPERIENCE_AVATAR_EXPIRED = 38,
        E_MAIL_ID_RANK_SEASON_REWARD = 39,
        E_MAIL_ID_EXPERIENCE_CHAT_BUBBLE_EXPIRED = 41,
        E_MAIL_ID_ACTIVITY_UNCLAIMED_REWARD_SKYRIMTRANING = 42,
        E_MAIL_ID_TURBO_RANK_SEASON_REWARD = 43,
        E_MAIL_ID_EXPERIENCE_AVATAR_BOX_EXPIRED = 44,
        E_MAIL_ID_SHARE_DROP = 45,
        E_MAIL_ID_USER_JOIN_CLUB = 46,
        E_MAIL_ID_USER_QUIT_CLUB = 47,
        E_MAIL_ID_DUAL_SEASON_REWARD = 48,
        E_MAIL_ID_PROMO_CODE_REWARD = 49,
        E_MAIL_ID_DUAL_RANK_SECOND_RESULT = 50,
        E_MAIL_ID_PRIVILEGED_CARD_DAILY_REWARD = 53,
        E_MAIL_ID_PRIVILEGED_CARD_EXPIRE = 54,
        E_MAIL_ID_EXPERIENCE_QUICK_CHAT_EXPIRED = 55,
        E_MAIL_ID_ITEM_EXPIRE_AND_NO_RETURN_ITEM = 56,
        E_MAIL_ID_BATTLE_EMOTION_DROP = 78,
    }
    public enum E_MAIL_FORMAT_ARG_COUNT
    {
        E_MAIL_FORMAT_ARG_COUNT_BATTLE_RESULT = 2,
        E_MAIL_FORMAT_ARG_COUNT_EXPERIENCE_TINY = 1,
    }
    public enum E_MAIL_FORMAT_TYPE
    {
        E_MAIL_FORMAT_TYPE_NULL = 0,
        E_MAIL_FORMAT_TYPE_DATE_YMD_HMS = 1,
        E_MAIL_FORMAT_TYPE_MATCH_TYPE = 2,
        E_MAIL_FORMAT_TYPE_MAX = 3,
    }
    public enum E_MAIL_TYPE
    {
        E_MAIL_TYPE_SYS = 1,
        E_MAIL_TYPE_IDIP = 2,
        TAC_E_MAIL_TYPE_SYS = 11,
        TAC_E_MAIL_TYPE_FRIEND = 12,
    }
    public enum TAC_MAIL_SCENE
    {
        TAC_MAIL_SCENE_DEFAULT = 0,
        TAC_MAIL_SCENE_COMPUTE_RESULT = 1,
        TAC_MAIL_SCENE_FRIEND_GIFT = 2,
        TAC_MAIL_SCENE_NEW_USER_GIFT = 3,
        TAC_MAIL_SCENE_DAILY_GIFT = 4,
    }
    public enum MAIL_DEL_TYPE
    {
        MAIL_DEL_TYPE_EXPIRED = 0,
        MAIL_DEL_TYPE_READED = 1,
        MAIL_DEL_TYPE_FETCHED_GIFT = 2,
    }
    public enum TAC_MAIL_DEL_ACTION
    {
        TAC_MAIL_DEL_ACTION_CURRECT_MAIL = 1,
        TAC_MAIL_DEL_ACTION_CURRECT_TAG_HAVE_READ = 2,
    }
    public enum E_GM_CMD
    {
        E_GM_CMD_SU = 1,
        E_GM_CMD_TIME = 2,
        E_GM_CMD_SETTIERANDLP = 3,
        E_GM_CMD_SETMMRANDVAR = 4,
        E_GM_CMD_CLEAR_NOVICE_STATE = 5,
        E_GM_CMD_CLEAR_AUTO_MAIL = 6,
        E_GM_CMD_SEND_CONFIG_MAIL = 9,
        E_GM_CMD_BATTLE_PASS = 11,
        E_GM_CMD_ADD_ITEM = 12,
        E_GM_CMD_CALC_APEX_TIER_RANK = 17,
        E_GM_CMD_SET_TIME = 18,
        E_GM_CMD_SET_WARM_VALUE = 19,
        E_GM_CMD_CLEAR_SHARE_RECORD = 20,
        E_GM_CMD_CLEAR_SEASON_STATISTICS = 21,
        E_GM_CMD_REFRESH_NOVICE_TASK = 22,
        E_GM_CMD_SET_DROP_ENSURE_GROUP = 23,
        E_GM_CMD_SET_DROP_ENSURE_ITEM = 24,
        E_GM_CMD_SEND_ITEM_MAIL = 27,
        E_GM_CMD_REPORT_RANK = 28,
        E_GM_CMD_GET_RANK = 29,
        E_GM_CMD_INCREASE_RANK_VALUE = 30,
        E_GM_CMD_CLEAR_BATTLE_RECORD = 31,
        E_GM_CMD_FRIEND_RANK_INIT = 32,
        E_GM_CMD_SET_MASTER_GUIDE = 33,
        E_GM_CMD_DEL_RANK_INFO = 35,
        E_GM_CMD_SET_CONTEST_LIVES = 36,
        E_GM_CMD_SET_CONTEST_PROGRESS = 37,
        E_GM_CMD_CLEAR_CONTEST_RANK = 38,
        E_GM_CMD_GET_USER_WARM_VALUE = 39,
        E_GM_CMD_CANCEL_MIDAS_PAY = 40,
        E_GM_CMD_TEST_DROP_ID = 41,
        E_GM_CMD_CLEAR_USER_DATA = 42,
        E_GM_CMD_SET_LEVEL = 43,
        E_GM_CMD_RESET_BUTLER_ACTIVITY_TASK = 44,
        E_GM_CMD_UPDATE_BUTLER_ACTIVITY_TASK_PROGRESS = 45,
        E_GM_CMD_RESET_BUTLER_ACTIVITY_EXCHANGE = 46,
        E_GM_CMD_TEST_MARQUEE = 49,
        E_GM_CMD_RESET_SEASON_TASK = 50,
        E_GM_CMD_SIMULATE_ZK_KICKOUT = 51,
        E_GM_CMD_RESET_SEASONID_GAMETIME = 52,
        E_GM_CMD_GET_USER_RANK_DATA = 53,
        E_GM_CMD_TEST_DROP_ACTIVITY = 54,
        E_GM_CMD_GET_MAP_ELIMINATE_NUM = 55,
        E_GM_CMD_GET_ITEM_TYPE_COUNT = 56,
        E_GM_CMD_BATCH_SEND_TRUMPET = 59,
        E_GM_CMD_SET_DATA_MORE_RETURN_USER = 60,
        E_GM_CMD_DELETE_ACCOUNT = 61,
        E_GM_CMD_RECV_BP_TASK = 62,
        E_GM_CMD_ADD_MOCK_FRIEND_FOR_BP_TEST = 63,
        E_GM_CMD_GET_RANK_SEANSON_TIME = 64,
        E_GM_CMD_GET_RAW_USERRANKINGDATA = 65,
        E_GM_CMD_CLEAR_RANKMATCH_RECORD = 66,
        E_GM_CMD_CLEAR_USER_RANKING_DATA = 67,
        E_GM_CMD_UPDATE_CONSECUTIVE_DAY_CHALLENGE_PROGRESS = 69,
        E_GM_CMD_TRIGGER_TASK_EVENT = 70,
        E_GM_CMD_SET_PLAY_GROUND_COUNT = 74,
        E_GM_CMD_REFRESH_LINE_UP_CHALLENGE = 76,
        E_GM_CMD_FINISH_LINE_UP_CHALLENGE = 77,
        E_GM_CMD_TEST_DRAW_LOTTERY = 78,
        E_GM_CMD_TEST_DAILY_LOGIN = 79,
        E_GM_CMD_TEST_CONSECUTIVE_DAY_CHALLENGE = 80,
        E_GM_CMD_TRANSFORM_TOPNEXT_DATA = 81,
        E_GM_CMD_INSERT_FAKE_TOPNEXT_DATA = 82,
        E_GM_CMD_RESET_SKYRIMTRANING = 83,
        E_GM_CMD_SCHEDULE = 84,
        E_GM_CMD_SETLPBACKUPGAMES = 85,
        E_GM_CMD_TEST_REGISTER_LIMIT = 86,
        E_GM_CMD_UPDATE_CONSECUTIVE_DAY_TASK_PROGRESS = 87,
        E_GM_CMD_UPDATE_WEEKEND_PARTY_PROGRESS = 88,
        E_GM_CMD_GAME_MODULE = 89,
        E_GM_CMD_SET_BP_TASK_DATA = 90,
        E_GM_CMD_RESET_RANKING_DATA = 91,
        E_GM_CMD_RESTART_SERVER = 92,
        E_GM_CMD_TEST_COMMON_TASK_ACTIVITY = 93,
        E_GM_CMD_TEST_FEATHER_KNIGHT_TASK_ACTIVITY = 94,
        E_GM_CMD_TEST_JTECHNICAL = 95,
        E_GM_CMD_RECHARGE = 96,
        E_GM_CMD_TEST_FEAST_OF_WIND = 97,
        E_GM_CMD_SET_MATCH_RECORD = 98,
        E_GM_CMD_TEST_TURBO_RANK = 99,
        E_GM_CMD_TEST_SEASON_LOTTERY = 100,
        E_GM_CMD_CLEAR_LAST_TALK_TIME = 101,
        E_GM_CMD_TEST_NAME_RECOMMEND = 102,
        E_GM_CMD_SET_PRIVI = 103,
        E_GM_CMD_TEST_REPAIR_SPACETIME = 104,
        E_GM_CMD_TEST_JTECHNICAL_COMICS = 105,
        E_GM_CMD_TEST_WARM_FLAG = 106,
        E_GM_CMD_TEST_BOSS_CHALLENGE = 107,
        E_GM_CMD_AUTO_TEST_FOR_MMR_VAR = 108,
        E_GM_CMD_TRIAL_OF_CYAN_DRAGON = 109,
        E_GM_CMD_CARD_COLLECT = 110,
        E_GM_CMD_GO_TWO_CITIES = 111,
        E_GM_CMD_FETTER_TWO_CITIES = 112,
        E_GM_CMD_TINY_HERO_EGG_INFO = 113,
        E_GM_CMD_TEST_CLUB = 114,
        E_GM_CMD_COMPLETE_SEASON_TASK = 115,
        E_GM_CMD_TEST_CONTEST = 116,
        E_GM_CMD_COMM_ACCUMULATE = 117,
        E_GM_CMD_NEW_CLIENT_VERSION_NOTIFY = 118,
        E_GM_CMD_TINY_FETTER = 119,
        E_GM_CMD_COMMON_MULTI_STEP_ACTIVITY = 120,
        E_GM_CMD_CONTEST_CHANGE_TOPNEXT_DATA = 121,
        E_GM_CMD_TEST_BOSS_CHALLENGE_ROGELIKE = 122,
        E_GM_CMD_CONTEST_SET_JOIN_COUNT = 123,
        E_GM_CMD_ENLIGHTENED_TALE = 124,
        E_GM_CMD_FORTUNE_BOX = 125,
        E_GM_CMD_PLAY_AGAIN = 126,
        E_GM_CMD_LOTTERY = 127,
        E_GM_CMD_SYNTHESIS = 129,
        E_GM_CMD_MOCK_RANK_BALANCE = 141,
    }
    public enum E_MOUDLE_ID
    {
        E_MOUDLE_ID_NORMAL_MATCH = 1,
        E_MOUDLE_ID_RANK_MATCH = 2,
    }
    public enum E_PMD_TYPE
    {
        E_PMD_TYPE_HALL = 1,
        E_PMD_TYPE_MODULE = 2,
        E_PMD_TYPE_FIGHT = 3,
        E_PMD_TYPE_SYSTEM = 4,
    }
    public enum E_MARQUEE_CONF_TYPE
    {
        E_MARQUEE_CONF_TYPE_SYSTEM = 1,
        E_MARQUEE_CONF_TYPE_TIME_EXCEL = 2,
        E_MARQUEE_CONF_TYPE_TIME_BUTLER = 3,
    }
    public enum E_MARQUEE_ID
    {
        E_MARQUEE_ID_SEND_GIFT = 3,
    }
    public enum E_WARM_MATCH_TYPE
    {
        E_WARM_MATCH_TYPE_NORMAL = 0,
        E_WARM_MATCH_TYPE_SMALL = 1,
        E_WARM_MATCH_TYPE_BIG = 2,
        E_WARM_MATCH_TYPE_NEW_TASK_TYPE_SINGLE = 3,
        E_WARM_MATCH_TYPE_NEW_PLYAER_SMALL = 4,
        E_WARM_MATCH_TYPE_NEW_PLAYER_RELAX = 5,
        E_WARM_MATCH_TYPE_NEW_PLAYER_RANK = 6,
    }
    public enum TActivitySubPanelTag
    {
        TActivitySubPanelTag_Hot = 1,
        TActivitySubPanelTag_Discount = 2,
    }
    public enum E_AQ_STATE
    {
        E_AQ_STATE_NOTANSWER = 0,
        E_AQ_STATE_ANSWERED_NOREWARD,
        E_AQ_STATE_ANSWERED_REWARD,
    }
    public enum E_SIGN_STATE
    {
        E_SIGN_STATE_NOSIGN = 0,
        E_SIGN_STATE_SIGN = 1,
    }
    public enum E_VOTE_TYPE
    {
        E_VOTE_TYPE_PLAY_STATE = 1,
        E_VOTE_TYPE_BALANCE = 2,
    }
    public enum E_ZK_INSTRUCTION_TYPE
    {
        E_ZK_INSTRUCTION_TYPE_TIPS = 1,
        E_ZK_INSTRUCTION_TYPE_OFFLINE = 2,
        E_ZK_INSTRUCTION_TYPE_OPEN_URL = 3,
        E_ZK_INSTRUCTION_TYPE_CUSTOMIZE = 4,
        E_ZK_INSTRUCTION_TYPE_EARNING_NO_TIPS = 5,
        E_ZK_INSTRUCTION_TYPE_EARNING_TIPS = 6,
        E_ZK_INSTRUCTION_TYPE_STOP = 7,
        E_ZK_INSTRUCTION_TYPE_COUNTDOWN = 8,
    }
    public enum E_BUTLER_ACTIVITY_TASK_REFRESH
    {
        E_BUTLER_ACTIVITY_TASK_REFRESH_DAILY = 1,
        E_BUTLER_ACTIVITY_TASK_REFRESH_WEEKLY = 2,
        E_BUTLER_ACTIVITY_TASK_REFRESH_ONCE = 3,
    }
    public enum FrameCompressMethod
    {
        E_FrameCompressMethod_None = 0,
        E_FrameCompressMethod_Snappy = 1,
        E_FrameCompressMethod_GZip = 2,
    }
    public enum E_CHARGE_ACTIVITY_REWARD_STATUS
    {
        E_CHARGE_ACTIVITY_REWARD_INVALID = 0,
        E_CHARGE_ACTIVITY_REWARD_VALID = 1,
        E_CHARGE_ACTIVITY_REWARD_ACHIEVED = 2,
    }
    public enum E_QUIZ_EVENT_PARA_TYPE
    {
        E_QUIZ_EVENT_PARA_RESULT = 1,
    }
    public enum E_MIDAS_OP
    {
        E_MIDAS_OP_NULL = 0,
        E_MIDAS_OP_QUERY = 1,
        E_MIDAS_OP_PAY = 2,
        E_MIDAS_OP_PRESENT = 3,
        E_MIDAS_OP_CANCEL = 4,
        E_MIDAS_OP_BUY_GOODS_ORDER = 5,
        E_MIDAS_OP_BUY_GOODS_GIVE = 6,
        E_MIDAS_OP_BUY_GOODS_ANSWER = 7,
        E_MIDAS_OP_HISTORY_TOTAL_CHARGE = 8,
        E_MIDAS_OP_MAX = 9,
    }
    public enum E_MIDAS_LOG_REQ_RSP
    {
        E_MIDAS_LOG_REQ = 0,
        E_MIDAS_LOG_RSP = 1,
    }
    public enum E_MIDAS_LOG_ADD_REDUCE
    {
        E_MIDAS_LOG_ADD = 0,
        E_MIDAS_LOG_REDUCE = 1,
    }

    public enum TAC_MatchReportType
    {
        TAC_MatchReportType_RefreshTimes = 0,
        TAC_MatchReportType_TotalCoinNum = 1,
        TAC_MatchReportType_KillEnemyTimes = 2,
        TAC_MatchReportType_MaxPopulation = 3,
        TAC_MatchReportType_HeroStars = 4,
        TAC_MatchReportType_BuyHeroRank = 5,
        TAC_MatchReportType_MaxContinueWin = 6,
        TAC_MatchReportType_TotalWin = 7,
        TAC_MatchReportType_MaxInterest = 8,
        TAC_MatchReportType_MaxFetter = 9,
        TAC_MatchReportType_MonsterNum = 10,
        TAC_MatchReportType_MagicHurt = 11,
        TAC_MatchReportType_PhyHurt = 12,
        TAC_MatchReportType_TotalHurt = 13,
        TAC_MatchReportType_ComposeEquip = 14,
        TAC_MatchReportType_EquipHero = 15,
        TAC_MatchReportType_CurRound = 16,
        TAC_MatchReportType_TotalSpecNum = 17,
        TAC_MatchReportType_TotalClassNum = 18,
        TAC_MatchReportType_SpecNum = 19,
        TAC_MatchReportType_ClassNum = 20,
        TAC_MatchReportType_RoundLevel = 21,
        TAC_MatchReportType_MaxCoin = 22,
        TAC_MatchReportType_RoundBuySameHero = 23,
        TAC_MatchReportType_EquipAttr = 24,
        TAC_MatchReportType_RoundKill = 25,
        TAC_MatchReportType_SkillKill = 26,
        TAC_MatchReportType_RoundSkill = 27,
        TAC_MatchReportType_RoundHeroHurt = 28,
        TAC_MatchReportType_TotalHeroHurt = 29,
        TAC_MatchReportType_TotalBuffCount = 30,
        TAC_MatchReportType_FullRelation = 31,
        TAC_MatchReportType_SellHeroCoin = 32,
        TAC_MatchReportType_WinCoin = 33,
        TAC_MatchReportType_LoseCoin = 34,
        TAC_MatchReportType_BuyExpCoin = 35,
        TAC_MatchReportType_NoDeathRound = 36,
        TAC_MatchReportType_HeroSkillMove = 37,
        TAC_MatchReportType_ChangeHeroType = 38,
        TAC_MatchReportType_Relive = 39,
        TAC_MatchReportType_LoseRound = 40,
        TAC_MatchReportType_SellHeroEquip = 41,
        TAC_MatchReportType_UnusedEquip = 42,
        TAC_MatchReportType_SoloKill = 44,
        TAC_MatchReportType_MaxContinueLose = 45,
        TAC_MatchReportType_HeroTotalKill = 46,
        TAC_MatchReportType_HeroTotalSkill = 47,
        TAC_MatchReportType_Attr = 48,
        TAC_MatchReportType_TotalHurtMap = 49,
        TAC_MatchReportType_TotalIncome = 50,
        TAC_MatchReportType_MostValuableHero = 51,
        TAC_MatchReportType_LeftCoinNum = 52,
        TAC_MatchReportType_NoOperationTurnNum = 53,
        TAC_MatchReportType_RoundLoseLife = 54,
        TAC_MatchReportType_MaxEquipmentsCountInOneHero = 55,
        TAC_MatchReportType_GetEquipmentsCount = 56,
        TAC_MatchReportType_LeftLifeWhenWin = 57,
        TAC_MatchReportType_TakeOtherPlayerLeftMax = 58,
        TAC_MatchReportType_UsingCountsOfMagicEmoji = 59,
        TAC_MatchReportType_SellHeroCounts = 61,
        TAC_MatchReportType_MaxLevel = 62,
        TAC_MatchReportType_UpgradeHeroCounts = 63,
        TAC_MatchReportType_BuyHeroCounts = 64,
        TAC_MatchReportType_ConsumeCoin = 65,
        TAC_MatchReportType_BaseIncomeCoin = 66,
        TAC_MatchReportType_InterestIncomeCoin = 67,
        TAC_MatchReportType_WinIncomeCoin = 68,
        TAC_MatchReportType_BuyHeroCoin = 69,
        TAC_MatchReportType_RefreshCoin = 70,
        TAC_MatchReportType_BuySpecHero = 71,
        TAC_MatchReportType_BuyClassHero = 72,
        TAC_MatchReportType_UsingCountsOfEmoji = 73,
        TAC_MatchReportType_UsingCountsOfTinyAct = 74,
        TAC_MatchReportType_UsingCountsOfMagicExp = 75,
        TAC_MatchReportType_ResultSpecNum = 76,
        TAC_MatchReportType_ResultClassNum = 77,
        TAC_MatchReportType_ComposeEquip_OnHero = 78,
        TAC_MatchReportType_ComposeEquip_OnEquipPanel = 79,
        TAC_MatchReportType_BuyExpCount = 80,
        TAC_MatchReportType_SwitchPlayer = 81,
        TAC_MatchReportType_PickUpDropBox = 82,
        TAC_MatchReportType_SendHeroTimes = 83,
        TAC_MatchReportType_SupportTeammateTimes = 84,
        TAC_MatchReportType_SupportHeroCost2MaxStar = 85,
        TAC_MatchReportType_ResultStarNum = 90,
        TAC_MatchReportType_ResultCostMaxStarNum = 91,
        TAC_MatchReportType_WinMaxConWinOpponent = 96,
        TAC_MatchReportType_RefreshSameHeroMax = 97,
        TAC_MatchReportType_RefreshStarHero2MaxNum = 98,
        TAC_MatchReportType_Compose3StarHeroEarliestRound = 100,
        TAC_MatchReportType_MaxFetterOfSingleHero = 101,
        TAC_MatchReportType_DefeatOpponentNumLastRound = 102,
        TAC_MatchReportType_WinPeopleNumDiffOpponent = 103,
        TAC_MatchReportType_WinPeopleNumDiffLastRound = 104,
        TAC_MatchReportType_RecastMaxNumInSingleOp = 106,
        TAC_MatchReportType_BattleUnitMaxNum = 111,
        TAC_MatchReportType_AllHeroSatisfiedType = 112,
        TAC_MatchReportType_ZephyrHeroWithThreeItemsContinuously = 116,
        TAC_MatchReportType_OneLeftForThreeHeroes = 117,
        TAC_MatchReportType_FiveCostThreeStar = 118,
        TAC_MatchReportType_HolyDrop = 119,
        TAC_MatchReportType_WatchPlayerInfo = 120,
        TAC_MatchReportType_WearEquipmentHeroNum = 121,
        TAC_MatchReportType_PlaceHeroCostNum = 122,
        TAC_MatchReportType_SoulEnergyNum = 123, // 获取到的灵魂能量个数   主干放开
        TAC_MatchReportType_SoulEngineEquipNum = 124, //使用能量增幅器的次数  主干放开枚举
        TAC_MatchReportType_GalaxyVote = 130, //玩家地区投票ID
        TAC_MatchReportType_HexSingleRefresh = 131, // 玩家海克斯刷新次数
        TAC_MatchReportType_RoundLevelExt = 132,
        TAC_MatchReportType_Stage = 133,
        TAC_MatchReportType_HexBatteryDragonLayer = 134, // S9海克斯电龙层数
        E_TASK_CT_FORTUNE_FAVOR_KEY_CARD_THREESTAR_ROUND = 135,
        E_TASK_CT_HEAVEN_CHOOSER_MAX_STAR = 136, // 天选英雄对局中达到的最高星级 <品质, 星级>
        E_TASK_CT_HEARTSTEEL_FETTER_EFFECTED_MAX_LEVEL = 137, // 心之钢羁绊礼物盒达到并成功售卖的最高等级
        TAC_MatchReportType_ResultDataScore = 10000,
        
    }

    public enum GameStatisticsType
    {
        E_GameStatisticsType_LiveRound = 1,
        E_GameStatisticsType_LiveTime = 2,
        E_GameStatisticsType_PeopleNum = 3,
        E_GameStatisticsType_ChessWorth = 4,
        E_GameStatisticsType_WinCount = 5,
        E_GameStatisticsType_LoseCount = 6,
        E_GameStatisticsType_MaxConWinCount = 7,
        E_GameStatisticsType_MaxConLoseCount = 8,
        E_GameStatisticsType_ToPlayerDamageNum = 9,
        E_GameStatisticsType_MostValuableHeroId = 10,
        E_GameStatisticsType_MostValuableHeroKillNum = 11,
        E_GameStatisticsType_MostValuableHeroDeathNum = 12,
        E_GameStatisticsType_MostValuableHeroAssistNum = 13,
        E_GameStatisticsType_TotalCoinNum = 14,
        E_GameStatisticsType_TotalKillHeroNum = 15,
        E_GameStatisticsType_TotalStarLevelOnBalance = 16,
        E_GameStatisticsType_KillEnemyTimes = 17,
        E_GameStatisticsType_WinPeopleNumDiffLastRound = 18,
    }
    public enum RoundStatisticsType
    {
        E_RoundStatisticsType_IsWin = 1,
        E_RoundStatisticsType_LoseLifeNum = 2,
        E_RoundStatisticsType_Life = 3,
        E_RoundStatisticsType_CoinNum = 4,
        E_RoundStatisticsType_CoinBasicAdd = 5,
        E_RoundStatisticsType_CoinInterestAdd = 6,
        E_RoundStatisticsType_CoinConWinAdd = 7,
        E_RoundStatisticsType_CoinConLoseAdd = 8,
    }
    public enum OtherInfosType
    {
        E_OtherInfosType_Champion_Fetter_ID,
        E_OtherInfosType_Champion_Fetter_Type,
    }
    public enum BossChallengeStatisticInGame
    {
        E_BossChallengeStatisticInGame_HurtTotal = 1,
        E_BossChallengeStatisticInGame_HealTotal = 2,
        E_BossChallengeStatisticInGame_LiveTime = 3,
        E_BossChallengeStatisticInGame_PassTime = 4,
    }
    public enum ActivityType
    {
        E_ActivityType_Accumulate_Charge = 2,
        E_ActivityType_First_Charge = 3,
        E_ActivityType_Drop = 6,
        E_ActivityType_Consecutive_Day_Challenge = 8,
        E_ActivityType_LineUp_Challenge = 10,
        E_ActivityType_Daily_Login = 13,
        E_ActivityType_Consecutive_Day_Task = 14,
        E_ActivityType_Skyrim_Traning = 15,
        E_ActivityType_Daily_First_Win = 16,
        E_ActivityType_Weekend_Party = 17,
        E_ActivityType_Banner = 18,
        E_ActivityType_Feast_Of_Wind = 20,
        E_ActivityType_Common_Login = 21,
        E_ActivityType_Common_Task = 22,
        E_ActivityType_Feather_Knight_Task = 23,
        E_ActivityType_Star_Dragon = 24,
        E_ActivityType_Recharge = 25,
        E_ActivityType_Feast_Of_Wind_Exchange_Item = 26,
        E_ActivityType_Feather_Knight = 27,
        E_ActivityType_Pandora = 28,
        E_ActivityType_Version_Headlines = 29,
        E_ActivityType_Small_Recharge = 30,
        E_ActivityType_Innerblessbag = 31,
        E_ActivityType_MainRepairSpacetime = 32,
        E_ActivityType_RepairSpacetimeUnLockVido = 33,
        E_ActivityType_JTechnicalComics = 34,
        E_ActivityType_TrialOfCyanDragon = 35,
        E_ActivityType_MagicPenPanda = 36,
        E_ActivityType_FetterOfTwoCities = 37,
        E_ActivityType_SourceOfTwoCities = 38,
        E_ActivityType_AppointmentTwoCities = 39,
        E_ActivityType_GoTwoCities = 40,
        E_ActivityType_CommAccumulate = 41,
        E_ActivityType_CommonMultiStepTask = 42,
        E_ActivityType_FortuneBox = 43,
        E_ActivityType_EnlightenedTale = 44,
    }
    public enum BRGameStatisticsType
    {
        E_BRGameStatisticsType_Coin_Num = 1,
        E_BRGameStatisticsType_Equip_Num = 2,
        E_BRGameStatisticsType_Kill_Num = 3,
        E_BRGameStatisticsType_Mvp_Hero = 4,
        E_BRGameStatisticsType_Fetter = 5,
        E_BRGameStatisticsType_Level = 6,
        E_BRGameStatisticsType_HeroDps = 7,
        E_BRGameStatisticsType_HeroInfo = 8,
        E_BRGameStatisticsType_HexRule = 9,
        E_BRGameStatisticsType_ConWinCount = 10,
        E_BRGameStatisticsType_HeroStarCount = 11,
        E_BRGameStatisticsType_EndFrame = 12,
    }
    public enum E_MODULE_CONTROL_SERVER
    {
        E_MODULE_CONTROL_SERVER_SCENE = 1,
        E_MODULE_CONTROL_SERVER_TEAM = 2,
        E_MODULE_CONTROL_SERVER_MALL = 3,
        E_MODULE_CONTROL_SERVER_BP = 4,
    }
    public enum E_ADD_DAY_TYPE
    {
        E_ADD_DAY_TYPE_DEFAULT = 0,
        E_ADD_DAY_TYPE_MERGE = 1,
        E_ADD_DAY_TYPE_MERGE_FROM_ZERO = 2,
    }
    public enum E_TINY_EQUIPMENT_TYPE
    {
        E_TINY_EQUIPMENT_TYPE_MAP = 0,
        E_TINY_EQUIPMENT_TYPE_DAMAGE = 1,
        E_TINY_EQUIPMENT_TYPE_DOOR = 2,
        E_TINY_EQUIPMENT_TYPE_FOOT = 3,
        E_TINY_EQUIPMENT_TYPE_ELIMINATE = 4,
    }
    public enum E_LOGIN_SOURCE
    {
        E_LOGIN_SOURCE_COMMON = 0,
        E_LOGIN_SOURCE_WECHAR_GAME = 1,
        E_LOGIN_SOURCE_QQ_GAME = 2,
    }
    public enum E_PRIVI_FLAG
    {
        E_PRIVI_FLAG_NONE = 0,
        E_PRIVI_FLAG_WX_EXPIRE = 1,
        E_PRIVI_FLAG_WX_VALID = 2,
        E_PRIVI_FLAG_QQ_EXPIRE = 4,
        E_PRIVI_FLAG_QQ_VALID = 8,
    }
    public enum emWeeklyLoginSignStatus
    {
        emWeeklyLoginSignStatus_NotFinished = 0,
        emWeeklyLoginSignStatus_FinishNotFetched = 1,
        emWeeklyLoginSignStatus_CanMakeup = 2,
        emWeeklyLoginSignStatus_Fetched = 3,
    }
    public enum emDropActivityLimitType
    {
        emDropActivityLimitType_NULL = 0,
        emDropActivityLimitType_Daily = 1,
        emDropActivityLimitType_Total = 2,
    }
    public enum emDropActivityProbabilityMode
    {
        emDropActivityProbabilityMode_Independent = 1,
        emDropActivityProbabilityMode_Accumulate = 2,
    }
    public enum E_NOVICE_LABEL
    {
        E_NOVICE_LABEL_ROOKIE = 1,
        E_NOVICE_LABEL_EXPERIENCED = 2,
        E_NOVICE_LABEL_MASTER = 3,
    }
    public enum E_WARM_USER_FLAG
    {
        E_WARM_USER_FLAG_COMMON = 0,
        E_WARM_USER_FLAG_RETURN = 1,
        E_WARM_USER_FLAG_NO_TFT_NEW = 2,
        E_WARM_USER_FLAG_TFT_NEW = 3,
    }
    public enum E_BAN_TYPE
    {
        E_BAN_TYPE_LOGIN = 1,
        E_BAN_TYPE_POST = 2,
        E_BAN_TYPE_SILENCE = 3,
        E_BAN_TYPE_RANKING_LIST = 4,
        E_BAN_TYPE_ADD_FRIEND = 5,
        E_BAN_TYPE_MODIFY_PLAYER_INFO = 6,
        E_BAN_TYPE_HEAD_URL_SECURITY = 7,
        E_BAN_TYPE_CLEAR_POST = 8,
        E_BAN_TYPE_GIFT_CENTER = 9,
        E_BAN_TYPE_AUDIO = 10,
        E_BAN_TYPE_CLUB_MODIFY_CONTENTS = 11,
        E_BAN_TYPE_CLUB_BEHAVIOR = 12,
        E_BAN_TYPE_MODIFY_LINEUP = 13,
        E_BAN_TYPE_WORLD_SHARE_ROOM_SILENCE = 14,
        E_BAN_TYPE_WORLD_SHARE_ROOM_FORBID = 15,
    }
    public enum E_CONFIG_SOURCE_TYPE
    {
        E_CONFIG_SOURCE_TYPE_EXCEL = 1,
        E_CONFIG_SOURCE_TYPE_BUTLER = 2,
    }
    public enum E_PLAY_GROUND_ENTRANCE
    {
        E_PLAY_GROUND_ENTRANCE_TURBO = 1,
        E_PLAY_GROUND_ENTRANCE_LINEUP = 2,
    }
    public enum E_USER_TAG
    {
        E_USER_TAG_NONE = 0,
        E_USER_TAG_NOVICE = 1,
        E_USER_TAG_MASTER = 2,
    }
    public enum ClubDataType
    {
        BasicData = 1,
        ApplicationData = 2,
        ChannelData = 3,
        BasicDataChiefMemberOnly = 4,
        BasicDataChiefActiveMembers = 5,
        ModeEntrance = 6,
    }
    public enum ClubMemberRanking
    {
        Chief = 1000,
        DeputyChief = 800,
        Admin = 600,
        Member = 0,
    }
    public enum ClubTaskType
    {
        Daily = 1,
        Weekly = 2,
    }
    public enum ClubModifyUserDataType
    {
        ModBasicData = 0,
        ModInvitedData = 1,
    }
    public enum MemberOperationRecordKind
    {
        Invalid = 0,
        JoinDirectly = 1,
        JoinByApproval = 2,
        BeenKickedOut = 3,
        Promote = 4,
        Demote = 5,
        Create = 6,
        Quit = 7,
        Inherit = 8,
        InviteToJoin = 9,
        RejectApplication = 10,
    }
    public enum ClubRankListType
    {
        ClubRankListType_Weekly = 0,
        ClubRankListType_Season = 1,
        ClubRankListType_Max,
    }
    public enum ClubDynamicsType
    {
        ClubDynamicsType_PromoteTier = 0,
        ClubDynamicsType_Win = 1,
        ClubDynamicsType_OrganizeTeam = 2,
        ClubDynamicsType_Max,
    }
    public enum ClubGoodsType
    {
        InvalidClubGoodsType = 0,
        Expression = 1,
        TinyHero = 2,
        HurtEffect = 3,
        ChatBubble = 4,
        ClubTitle = 5,
    }
    public enum JTECHNICAL_COMICS_STEP
    {
        JTECHNICAL_COMICS_STEP_DISABLE = 0,
        JTECHNICAL_COMICS_STEP_GUN = 1,
        JTECHNICAL_COMICS_STEP_HAMMER = 2,
        JTECHNICAL_COMICS_STEP_SHIELD = 3,
        JTECHNICAL_COMICS_STEP_ALL_COMPLETED = 100,
        JTECHNICAL_COMICS_STEP_ENABLE = 101,
        JTECHNICAL_COMICS_STEP_ERROR = 1001,
    }
    public enum SEASON_LOTTERY_ROLL_COUNT
    {
        SEASON_LOTTERY_ROLL_COUNT_ONE = 1,
        SEASON_LOTTERY_ROLL_COUNT_TEN = 10,
    }
    public enum SEASON_LOTTERY_DISCOUNT_REFRESH_TYPE
    {
        SEASON_LOTTERY_DISCOUNT_REFRESH_TYPE_NONE = 0,
        SEASON_LOTTERY_DISCOUNT_REFRESH_TYPE_DAY = 1,
        SEASON_LOTTERY_DISCOUNT_REFRESH_TYPE_WEEK = 2,
        SEASON_LOTTERY_DISCOUNT_REFRESH_TYPE_MONTH = 3,
    }
    public enum J_TECHNICAL_ROLL_COUNT
    {
        J_TECHNICAL_ROLL_COUNT_ONE = 1,
        J_TECHNICAL_ROLL_COUNT_TEN = 10,
    }
    public enum ABTestType
    {
        ABTestType_Warm_Flag = 1,
    }
    public enum AddExpType
    {
        AddExpInfo_DoubleExp = 1,
        AddExpInfo_WinDoubleExp = 2,
        AddExpInfo_LimitTimeExpBonus = 3,
        AddExpInfo_QQPrivilege = 4,
        AddExpInfo_WXPrivilege = 5,
        AddExpInfo_OriginExp = 6,
        AddExpInfo_OriginBPExp = 7,
    }
    public enum E_HERO_IN_GAME_EFFECT_STATUS
    {
        E_HERO_IN_GAME_EFFECT_OWNED = 1,
        E_HERO_IN_GAME_EFFECT_IN_USE = 2,
    }
    public enum HERO_IN_GAME_STATISTICS_TYPE
    {
        HERO_IN_GAME_STATISTICS_TYPE_USE_COUNT = 1,
        HERO_IN_GAME_STATISTICS_TYPE_WIN_COUNT = 2,
        HERO_IN_GAME_STATISTICS_TYPE_KILL_COUNT = 3,
        HERO_IN_GAME_STATISTICS_TYPE_ASSIST_COUNT = 4,
        HERO_IN_GAME_STATISTICS_TYPE_PURCHASE_COUNT = 5,
        HERO_IN_GAME_STATISTICS_TYPE_HIGHEST_DAMAGE = 6,
        HERO_IN_GAME_STATISTICS_TYPE_HIGHEST_DAMAGE_TAKEN = 7,
        HERO_IN_GAME_STATISTICS_TYPE_HIGHEST_HEAL = 8,
        HERO_IN_GAME_STATISTICS_TYPE_HIGHEST_SCORE = 9,
        HERO_IN_GAME_STATISTICS_TYPE_THREE_STAR_COUNT = 10, // 三星次数
        HERO_IN_GAME_STATISTICS_TYPE_CONTROL = 11, // 累计控制时间
        HERO_IN_GAME_STATISTICS_TYPE_ACCUMULATE_DAMAGE = 12, // 累计伤害
        HERO_IN_GAME_STATISTICS_TYPE_ACCUMULATE_DAMAGE_TAKEN = 13, // 累计承伤
        HERO_IN_GAME_STATISTICS_TYPE_ACCUMULATE_HEAL = 14, // 累计治疗
        HERO_IN_GAME_STATISTICS_TYPE_ADVANCED_SCORE = 15, // 累计分数
    }
    public enum MAIN_HALL_TIPS_TYPE
    {
        MAIN_HALL_TIPS_TYPE_CONDITION = 1,
        MAIN_HALL_TIPS_TYPE_GAME_ANNOUNCEMENT = 2,
        MAIN_HALL_TIPS_TYPE_INNERBLESS_BAG = 3,
        MAIN_HALL_TIPS_TYPE_RANK_REWARD = 4,
        MAIN_HALL_TIPS_TYPE_MAIL = 5,
        MAIN_HALL_TIPS_TYPE_TURBO_RANK_REWARD = 6,
        MAIN_HALL_TIPS_TYPE_APEX_TIER_PROMPT = 7, // 顶尖段位降段提示
        MAIN_HALL_TIPS_TYPE_LOOT = 8, //战利品红点提示
        MAIN_HALL_TIPS_TYPE_ACTIVITY = 9
    }
    public enum MAIN_HALL_BANNER_CHANNEL_LIST_TYPE
    {
        MAIN_HALL_BANNER_CHANNEL_LIST_TYPE_WHITE = 0,
        MAIN_HALL_BANNER_CHANNEL_LIST_TYPE_BLACK = 1,
        MAIN_HALL_BANNER_CHANNEL_LIST_TYPE_FILTER_ALL_CHANNEL = 2,
    }
    public enum MAIN_HALL_TOP_ENTRANCE_TYPE
    {
        ENTRANCE_TYPE_NORMAL = 0,
        ENTRANCE_TYPE_ACTIVITY = 1,
        ENTRANCE_TYPE_PRIVILEGED_CARD = 2,
        ENTRANCE_TYPE_TIME_STORE = 3,
        ENTRANCE_TYPE_COMMON_BP = 4,
    }
    public enum GIFT_CENTER_GOODS_CONDITION_TYPE
    {
        GIFT_CENTER_GOODS_CONDITION_TYPE_GIVER_HAVE_ITEM = 1,
    }
    public enum E_TAC_PLAYER_RELATION_TYPE
    {
        E_RELATION_TYPE_FRIEND = 0,
        E_RELATION_TYPE_SAME_CLUB = 1,
    }
    public enum CLUB_WORDING_MOD_TYPE
    {
        CLUB_WORDING_MOD_TYPE_NAME = 1,
        CLUB_WORDING_MOD_TYPE_INTRODUCTION = 2,
        CLUB_WORDING_MOD_TYPE_DECLARATION = 3,
        CLUB_WORDING_MOD_TYPE_RECRUIT_DECLARATION = 4,
    }
    public enum BC_ROGUELIKE_BOSS_STATE_TYPE
    {
        BC_ROGUELIKE_BOSS_STATE_TYPE_NON_START = 1,
        BC_ROGUELIKE_BOSS_STATE_TYPE_CHALLENGING = 2,
        BC_ROGUELIKE_BOSS_STATE_TYPE_LOCK = 3,
        BC_ROGUELIKE_BOSS_STATE_TYPE_EMPTY = 4,
    }
    public enum BOSS_CHALLENGE_PVE_STATE_TYPE
    {
        BOSS_CHALLENGE_PVE_STATE_TYPE_NON_START = 1,
        BOSS_CHALLENGE_PVE_STATE_TYPE_CHALLENGING = 2,
        BOSS_CHALLENGE_PVE_STATE_TYPE_EMPTY = 3,
    }
    public enum E_QUICK_CHAT_OP
    {
        E_QUICK_CHAT_OP_NULL = 0,
        E_QUICK_CHAT_OP_USE = 1,
        E_QUICK_CHAT_OP_EQUIP = 2,
        E_QUICK_CHAT_OP_UNEQUIP = 3,
    }

    public enum EmBluePrintActionType
    {
        EmBluePrintBuyType_Lottery = 1,
        EmBluePrintBuyType_Bag = 2,
        EmBluePrintBuyType_Exchange = 3,
        EmBluePrintBuyType_Present = 4,
        EmBluePrintBuyType_Surprise = 5,
    }

    public enum EmEnlightenedTaleActionType
    {
        EmEnlightenedTaleBuyType_Buy = 0,
        EmEnlightenedTaleBuyType_Lottery = 1,
        EmEnlightenedTaleBuyType_Bag = 2,
        EmEnlightenedTaleBuyType_Exchange = 3,
    }
    public enum EmEnlightenedTaleBagStatus
    {
        EmEnlightenedTaleBagStatus_CannotGet = 0,
        EmEnlightenedTaleBagStatus_CanGet = 1,
        EmEnlightenedTaleBagStatus_Received = 2,
    }

    public enum EmSeasonLotteryActionType
    {
        EmSeasonLotteryActionType_Lottery = 1,
        EmSeasonLotteryActionType_Present = 2,
        EmSeasonLotteryActionType_Surprise = 3,
    }

    public enum TinyMapDIYPartStatus
    {
        TinyMapDIYPartStatus_None = 0,
        TinyMapDIYPartStatus_CanUse = 1,
        TinyMapDIYPartStatus_InUse = 2,
        TinyMapDIYPartStatus_Max,
    }
    public enum TinyMapDIYCollocationType
    {
        TinyMapDIYCollocationType_Recommend = 0,
        TinyMapDIYCollocationType_Custom = 1,
        TinyMapDIYCollocationType_Max,
    }
    public enum E_TASK_PROGRESS_STAT_TYPE
    {
        E_TASK_PROGRESS_STAT_TYPE_ONCE = 0,
        E_TASK_PROGRESS_STAT_TYPE_MULTI = 1,
    }

    public enum CommonLotteryType
    {
        CommonLotteryType_Lottery = 1,
        CommonLotteryType_SeasonLottery = 2,
        CommonLotteryType_JTech = 3,
        CommonLotteryType_XayahRakan = 4,
        CommonLotteryType_EnlightenedTale = 5,
    }

    enum E_CLIENT_ONLINE_SETTING_TYPE
    {
        E_CLIENT_ONLINE_SETTING_TYPE_ONLINE      = 0, // 在线
        E_CLIENT_ONLINE_SETTING_TYPE_HIDE        = 1, // 隐身
        E_CLIENT_ONLINE_SETTING_TYPE_NO_DISTURB  = 2, // 勿扰，暂不实现
    };

    public enum DrawType
    {
        DrawType_Once,
        DrawType_All,
    }
    public enum FreeChipType
    {
        FreeChipType_Day = 1,
        FreeChipType_Week = 2,
        FreeChipType_Once = 3,
    }
    public enum ResetType
    {
        ResetType_Reset_Count = 1,
    }
    public enum EGetUserProfileInfoType
    {
        EGetUserProfileInfoType_Career = 0,
        EGetUserProfileInfoType_BusinessCard = 1,
    }
    public enum AFK_ADD_COUNTDOWN_ACTION_TYPE
    {
        AFK_ADD_COUNTDOWN_ACTION_TYPE_GAME_START = 1,
        AFK_ADD_COUNTDOWN_ACTION_TYPE_PICK_UNIT_IN_SHARED_DRAFT = 2,
        AFK_ADD_COUNTDOWN_ACTION_TYPE_UNIT_STAR_UP = 3,
        AFK_ADD_COUNTDOWN_ACTION_TYPE_STAR_UNIT_COUNT = 4,
    }
    public enum ClientFrameEventType
    {
        ClientFrameEventType_CELLUAR_TO_WIFI = 1,
        ClientFrameEventType_WIFI_TO_CELLUAR,
        ClientFrameEventType_LARGE_FRAME_INTERVAL_TOP_N,
    }
    public enum ClientFrameMetricType
    {
        ClientFrameMetric_AverageFrameInterval = 1,
        ClientFrameMetric_HelloReqRTT,
        ClientFrameMetric_InputLossNum,
        ClientFrameMetric_FrameLossNum,
        ClientFrameMetric_GameDurationMS,
    }
    public enum ClientOLAPReportType
    {
        ClientOLAPReportType_ClientFrameReport = 1,
    }
    public enum CARD_COLLECT_USE_IN_GAME_EFFECT_USE_TYPE
    {
        UNUSE = 0,
        USE = 1,
        USE_FOR_ALL_WITHIN_SET = 2,
    }

    public enum MagicExpressionUseType
    {
        Initiative = 0,     //主动使用
        BeObserved = 1,    //被观战
        Oberserve = 2,     //观战别人
        EndingTheWinningStreak = 3,//终结连胜
        Win = 4,           //胜利
        Fialed = 5,        //失败
        RecieveGift = 6,   //收到礼物
    }
    public enum E_COMMON_BP_TASK_TYPE
    {
        E_COMMON_BP_TASK_TYPE_STAGE_FIXED = 0,
        E_COMMON_BP_TASK_TYPE_DAILY_RAND = 1,
        E_COMMON_BP_TASK_TYPE_TIME_LIMIT = 2,
        E_COMMON_BP_TASK_TYPE_SPECIAL = 3,
    }
    public enum emBPMallLimitType
    {
        emBPMallLimitType_None = 0,
        emBPMallLimitType_Daily = 1,
        emBPMallLimitType_Weekly = 2,
        emBPMallLimitType_Forever = 3,
        emBPMallLimitType_Max = 4,
    }
    public enum E_COMMON_BP_PAY_TYPE
    {
        E_COMMON_BP_PAY_TYPE_FREE = 0,
        E_COMMON_BP_PAY_TYPE_1 = 1,
        E_COMMON_BP_PAY_TYPE_2 = 2,
    }
    public enum E_COMMON_BP_TASK_REFRESH_LIMIT_TYPE
    {
        E_COMMON_BP_TASK_REFRESH_LIMIT_TYPE_NO = 0,
        E_COMMON_BP_TASK_REFRESH_LIMIT_TYPE_DAY = 1,
        E_COMMON_BP_TASK_REFRESH_LIMIT_TYPE_WEEK = 2,
    }
    public enum GuildMemberRanking
    {
        GChief = 1000,
        GDeputyChief = 800,
        GAdmin = 600,
        GMember = 0,
    }
    public enum GuildChannelItemType
    {
        Chat = 0,
        Operation = 1,
        Feeds = 2,
    }
    public enum GuildDataType
    {
        GBasicData = 1,
        GApplicationData = 2,
        GChannelData = 3,
        GBasicDataChiefMemberOnly = 4,
        GModeEntrance = 5,
        GIconInfo = 6,
    }
    public enum GuidIconType
    {
        IconID = 0,
        AvatarBoxID = 1,
    }
    public enum GuildFeedsType
    {
        GuildFeedsType_Undefined = 0,
        GuildFeedsType_Room = 1,
        GuildFeedsType_Item = 2,
        GuildFeedsType_Tier = 3,
        GuildFeedsType_Winner = 4,
        GuildFeedsType_Level = 5,    // 等级提升
        GuildFeedsType_Max,
    }

    public enum JOCDayQuaifierStatus
    {
        JOCDayQuaifierStatus_No_Qualify = 0,
        JOCDayQuaifierStatus_Fail = 1,
        JOCDayQuaifierStatus_Succ = 2,
        JOCDayQuaifierStatus_Done = 3,
        JOCDayQuaifierStatus_Doing = 4,
        JOCDayQuaifierStatus_No_Start = 5,
    }

    public enum JOCTaskType
    {
        JOCTaskType_None = 0,
        JOCTaskType_Qualifier = 1,
        JOCTaskType_Rank = 2,
    }
    
    public enum JOCManualVoteAssistType
    {
        JOCManualVoteAssistType_None = 0,
        JOCManualVoteAssistType_World = 1,
        JOCManualVoteAssistType_Friend = 2,
        JOCManualVoteAssistType_Max,
    }
    public enum JOCManualCreateVoteAssistStatus
    {
        JOCManualCreateVoteAssistStatus_None = 0,
        JOCManualCreateVoteAssistStatus_IsAssisted = 1,
        JOCManualCreateVoteAssistStatus_IsExpired = 2,
    }
    public enum JOCManualHistoryRecordType
    {
        JOCManualHistoryRecordType_SelfVote = 1,
        JOCManualHistoryRecordType_AssistOther = 2,
        JOCManualHistoryRecordType_CreateVoteAssist = 3,
    }
    
    // 公会房间状态
    public enum GuildFeedsRoomStatus
    {
        GuildRoom_CanJoin = 0,                  // 可以加入
        GuildRoom_CannotJoin = 1,               // 房间解散或者开始匹配，不可加入
    }

    public enum USER_MATCH_DATA_EXT
    {
        USER_MATCH_DATA_EXT_SOUL_ENERGY = 0,
    }
    public enum PatFaceType
    {
        
        PatFaceTypeNone = 0, // 未知类型

        // 系统 100-199
        PatFaceTypeSeasonBegin          = 100, // 赛季开屏，原生
        PatFaceTypePrivilegeCardRenewal = 101, // 月卡续期提醒，原生
        PatFaceTypePrivilegeCardReward  = 102, // 月卡发奖提醒，原生
        PatFaceTypeInnerblessbag        = 103, // 登顶福袋，原生
        PatFaceTypeAchievement          = 104, // 成就奖励，原生
        PatFaceTypeGuildInvite          = 105, // 俱乐部邀请，原生，俱乐部社交能力弹窗，玩家通过微信消息拉起才能触发
        PatFaceTypeTinyHeroTutorial     = 106, // 小小英雄教程，原生，玩家获得第2只小小英雄回到主界面弹出
        PatFaceTypeTierUp               = 107, // 升段弹窗，原生
        PatFaceTypeLevelUp              = 108, // 升级弹窗，原生
        PatFaceTypeClientSetting        = 109, // 辅助功能开关(主要是隐私选项)，原生，一个账号仅触发一次
        PatFaceTypeTGALive              = 110, // TGA直播拍脸，原生
        PatFaceTypeUniverseLink         = 111, // universe link，原生
        PatFaceTypeGuildApplyAgree      = 112, // 俱乐部申请同意弹窗，原生，玩家申请俱乐部同意后弹出提示

        // 拍脸宣传-原生 200-299
        PatFaceTypeDynamicNative = 200, // 动态拍脸，原生

        // 拍脸宣传-潘多拉 300-399
        PatFaceTypeDynamicPandora   = 300, // 动态拍脸，潘多拉+灵犀
        PatFaceTypeThemeStation     = 301, // 活动主题站，潘多拉
        PatFaceTypePromotionalImage = 302, // 宣传图，潘多拉

        // 活动-原生 400-499
        PatFaceTypeDirectBuyActivity = 400, // 云端弈宝，原生

        // 活动-潘多拉 500-599
        PatFaceTypeLingXiActivity   = 500, // 灵犀活动，潘多拉（灵犀）
        PatFaceTypeRecommendedStore = 501, // 精准推荐商店，潘多拉（灵犀）
        PatFaceTypeUserReturn       = 502, // 重度回流活动，潘多拉
        PatFaceTypeUserReturnLight  = 503, // 轻度回流，潘多拉
    }

    public enum GiftCenterSettingOpType
    {
        GiftCenterSettingOpTypeNone = 0,
        GiftCenterSettingOpTypeSetPassword = 1,
        GiftCenterSettingOpTypeModifyPassword = 2,
        GiftCenterSettingOpTypeClosePassword = 3,
        GiftCenterSettingOpTypeForceClosePassword = 4,
        GiftCenterSettingOpTypeCancelForceClosePassword = 5,
        GiftCenterSettingOpTypeOpenPasswordFree = 6,
        GiftCenterSettingOpTypeClosePasswordFree = 7,
    }
    
    public enum JOCManualLotteryPossessCondition
    {
        JOCManualLotteryPossessCondition_TinyHeroInBag = 1,
        JOCManualLotteryPossessCondition_GiveableItemInBag = 2,
        JOCManualLotteryPossessCondition_GiveableItemInMail = 3,
        JOCManualLotteryPossessCondition_GiveableItemInGiftCenter = 4,
    }
    
    public enum ModuleHasShowType
    {
        ModuleHasShowType_Innerblessbag, // 登顶福袋
        ModuleHasShowType_PrivilegeSystem, // 月卡
    }
    
    public enum SpecialEffectType
    {
        SpecialEffectType_LevelBadge = 0, // 徽章
        SpecialEffectType_FriendsListColor = 1, // 好友列表特殊颜色
        SpecialEffectType_RankListColor = 2, // 排行榜列表特殊颜色
        SpecialEffectType_Max,
    }
}
