// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TCasual_LP_Cacualte_Config_Client : Wup.Jce.JceStruct
    {
        /// <summary>
        /// 唯一ID
        /// </summary>
        public int iID = 0;

        /// <summary>
        /// 玩法类型
        /// </summary>
        public int iSceneType = 0;

        /// <summary>
        /// 段位ID
        /// </summary>
        public int iRankID = 0;

        /// <summary>
        /// 段位名
        /// </summary>
        public string sName = "";

        /// <summary>
        /// 最低的分数(闭区间)
        /// </summary>
        public int iMinLP = 0;

        /// <summary>
        /// 最高的分数(开区间)
        /// </summary>
        public int iMaxLP = 0;

        /// <summary>
        /// 段位图标
        /// </summary>
        public string sIcon = "";

        /// <summary>
        /// 段位小图标
        /// </summary>
        public string sminiIcon = "";

        /// <summary>
        /// 大段位ID
        /// </summary>
        public int iBigRankID = 0;

        /// <summary>
        /// 段位名
        /// </summary>
        public string sShowName = "";

        /// <summary>
        /// 徽章变化视频
        /// </summary>
        public string sLevelUpMovie = "";

        /// <summary>
        /// 徽章变化特效
        /// </summary>
        public string sLevelUpAudio = "";

        /// <summary>
        /// 段位图标AB
        /// </summary>
        public string sIconAB = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iSceneType, 1);
            _os.Write(iRankID, 2);
            _os.Write(sName, 3);
            _os.Write(iMinLP, 4);
            _os.Write(iMaxLP, 5);
            _os.Write(sIcon, 6);
            _os.Write(sminiIcon, 7);
            _os.Write(iBigRankID, 8);
            _os.Write(sShowName, 9);
            _os.Write(sLevelUpMovie, 10);
            _os.Write(sLevelUpAudio, 11);
            _os.Write(sIconAB, 12);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iSceneType = (int) _is.Read(iSceneType, 1, false);

            iRankID = (int) _is.Read(iRankID, 2, false);

            sName = (string) _is.Read(sName, 3, false);

            iMinLP = (int) _is.Read(iMinLP, 4, false);

            iMaxLP = (int) _is.Read(iMaxLP, 5, false);

            sIcon = (string) _is.Read(sIcon, 6, false);

            sminiIcon = (string) _is.Read(sminiIcon, 7, false);

            iBigRankID = (int) _is.Read(iBigRankID, 8, false);

            sShowName = (string) _is.Read(sShowName, 9, false);

            sLevelUpMovie = (string) _is.Read(sLevelUpMovie, 10, false);

            sLevelUpAudio = (string) _is.Read(sLevelUpAudio, 11, false);

            sIconAB = (string) _is.Read(sIconAB, 12, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iSceneType, "iSceneType");
            _ds.Display(iRankID, "iRankID");
            _ds.Display(sName, "sName");
            _ds.Display(iMinLP, "iMinLP");
            _ds.Display(iMaxLP, "iMaxLP");
            _ds.Display(sIcon, "sIcon");
            _ds.Display(sminiIcon, "sminiIcon");
            _ds.Display(iBigRankID, "iBigRankID");
            _ds.Display(sShowName, "sShowName");
            _ds.Display(sLevelUpMovie, "sLevelUpMovie");
            _ds.Display(sLevelUpAudio, "sLevelUpAudio");
            _ds.Display(sIconAB, "sIconAB");
        }

        public override void Clear()
        {
            iID = 0;
            iSceneType = 0;
            iRankID = 0;
            sName = "";
            iMinLP = 0;
            iMaxLP = 0;
            sIcon = "";
            sminiIcon = "";
            iBigRankID = 0;
            sShowName = "";
            sLevelUpMovie = "";
            sLevelUpAudio = "";
            sIconAB = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TCasual_LP_Cacualte_Config_Client();
            copied.iID = this.iID;
            copied.iSceneType = this.iSceneType;
            copied.iRankID = this.iRankID;
            copied.sName = this.sName;
            copied.iMinLP = this.iMinLP;
            copied.iMaxLP = this.iMaxLP;
            copied.sIcon = this.sIcon;
            copied.sminiIcon = this.sminiIcon;
            copied.iBigRankID = this.iBigRankID;
            copied.sShowName = this.sShowName;
            copied.sLevelUpMovie = this.sLevelUpMovie;
            copied.sLevelUpAudio = this.sLevelUpAudio;
            copied.sIconAB = this.sIconAB;
            return copied;
        }
    }
}

