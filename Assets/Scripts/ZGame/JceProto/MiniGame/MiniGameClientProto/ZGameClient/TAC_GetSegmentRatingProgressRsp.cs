// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_GetSegmentRatingProgressRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public TKFrame.TKDictionary<int, SegmentRatingData> modeToRankingData;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(modeToRankingData, 8);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            modeToRankingData = (TKFrame.TKDictionary<int, SegmentRatingData>) _is.Read(modeToRankingData, 8, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(modeToRankingData, "modeToRankingData");
        }

        public override void Clear()
        {
            iRet = 0;
            modeToRankingData = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_GetSegmentRatingProgressRsp();
            copied.iRet = this.iRet;
            copied.modeToRankingData = (TKFrame.TKDictionary<int, SegmentRatingData>)JceUtil.DeepClone(this.modeToRankingData);
            return copied;
        }
    }
}

