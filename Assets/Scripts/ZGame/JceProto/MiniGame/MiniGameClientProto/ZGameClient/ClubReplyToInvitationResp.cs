// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ClubReplyToInvitationResp : Wup.Jce.JceStruct
    {
        public int err = 0;

        public bool approved = false;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(err, 0);
            _os.Write(approved, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            err = (int) _is.Read(err, 0, false);

            approved = (bool) _is.Read(approved, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(err, "err");
            _ds.Display(approved, "approved");
        }

        public override void Clear()
        {
            err = 0;
            approved = false;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ClubReplyToInvitationResp();
            copied.err = this.err;
            copied.approved = this.approved;
            return copied;
        }
    }
}

