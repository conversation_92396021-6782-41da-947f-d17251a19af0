//所在的Excel 【ACG_DropGroup.xlsm】
//*****************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_DropGroup_Client : Wup.Jce.JceStruct
    {
        int _iIndex = 0;
        public int iIndex
        {
            get
            {
                 return _iIndex;
            }
            set
            {
                _iIndex = value; 
            }
        }

        int _iID = 0;
        public int iID
        {
            get
            {
                 return _iID;
            }
            set
            {
                _iID = value; 
            }
        }

        int _iDropType = 0;
        public int iDropType
        {
            get
            {
                 return _iDropType;
            }
            set
            {
                _iDropType = value; 
            }
        }

        int _iMin = 0;
        public int iMin
        {
            get
            {
                 return _iMin;
            }
            set
            {
                _iMin = value; 
            }
        }

        int _iMax = 0;
        public int iMax
        {
            get
            {
                 return _iMax;
            }
            set
            {
                _iMax = value; 
            }
        }

        int _iType1 = 0;
        public int iType1
        {
            get
            {
                 return _iType1;
            }
            set
            {
                _iType1 = value; 
            }
        }

        int _iNum1 = 0;
        public int iNum1
        {
            get
            {
                 return _iNum1;
            }
            set
            {
                _iNum1 = value; 
            }
        }

        int _iProbability1 = 0;
        public int iProbability1
        {
            get
            {
                 return _iProbability1;
            }
            set
            {
                _iProbability1 = value; 
            }
        }

        int _iType2 = 0;
        public int iType2
        {
            get
            {
                 return _iType2;
            }
            set
            {
                _iType2 = value; 
            }
        }

        int _iNum2 = 0;
        public int iNum2
        {
            get
            {
                 return _iNum2;
            }
            set
            {
                _iNum2 = value; 
            }
        }

        int _iProbability2 = 0;
        public int iProbability2
        {
            get
            {
                 return _iProbability2;
            }
            set
            {
                _iProbability2 = value; 
            }
        }

        int _iType3 = 0;
        public int iType3
        {
            get
            {
                 return _iType3;
            }
            set
            {
                _iType3 = value; 
            }
        }

        int _iNum3 = 0;
        public int iNum3
        {
            get
            {
                 return _iNum3;
            }
            set
            {
                _iNum3 = value; 
            }
        }

        int _iProbability3 = 0;
        public int iProbability3
        {
            get
            {
                 return _iProbability3;
            }
            set
            {
                _iProbability3 = value; 
            }
        }

        int _iType4 = 0;
        public int iType4
        {
            get
            {
                 return _iType4;
            }
            set
            {
                _iType4 = value; 
            }
        }

        int _iNum4 = 0;
        public int iNum4
        {
            get
            {
                 return _iNum4;
            }
            set
            {
                _iNum4 = value; 
            }
        }

        int _iProbability4 = 0;
        public int iProbability4
        {
            get
            {
                 return _iProbability4;
            }
            set
            {
                _iProbability4 = value; 
            }
        }

        int _iType5 = 0;
        public int iType5
        {
            get
            {
                 return _iType5;
            }
            set
            {
                _iType5 = value; 
            }
        }

        int _iNum5 = 0;
        public int iNum5
        {
            get
            {
                 return _iNum5;
            }
            set
            {
                _iNum5 = value; 
            }
        }

        int _iProbability5 = 0;
        public int iProbability5
        {
            get
            {
                 return _iProbability5;
            }
            set
            {
                _iProbability5 = value; 
            }
        }

        int _iType6 = 0;
        public int iType6
        {
            get
            {
                 return _iType6;
            }
            set
            {
                _iType6 = value; 
            }
        }

        int _iNum6 = 0;
        public int iNum6
        {
            get
            {
                 return _iNum6;
            }
            set
            {
                _iNum6 = value; 
            }
        }

        int _iProbability6 = 0;
        public int iProbability6
        {
            get
            {
                 return _iProbability6;
            }
            set
            {
                _iProbability6 = value; 
            }
        }

        int _iTraverse = 0;
        public int iTraverse
        {
            get
            {
                 return _iTraverse;
            }
            set
            {
                _iTraverse = value; 
            }
        }

        int _iEnsureType = 0;
        public int iEnsureType
        {
            get
            {
                 return _iEnsureType;
            }
            set
            {
                _iEnsureType = value; 
            }
        }

        string _sEnsure1 = "";
        public string sEnsure1
        {
            get
            {
                 return _sEnsure1;
            }
            set
            {
                _sEnsure1 = value; 
            }
        }

        string _sEnsure2 = "";
        public string sEnsure2
        {
            get
            {
                 return _sEnsure2;
            }
            set
            {
                _sEnsure2 = value; 
            }
        }

        string _sEnsure3 = "";
        public string sEnsure3
        {
            get
            {
                 return _sEnsure3;
            }
            set
            {
                _sEnsure3 = value; 
            }
        }

        string _sEnsure4 = "";
        public string sEnsure4
        {
            get
            {
                 return _sEnsure4;
            }
            set
            {
                _sEnsure4 = value; 
            }
        }

        string _sEnsure5 = "";
        public string sEnsure5
        {
            get
            {
                 return _sEnsure5;
            }
            set
            {
                _sEnsure5 = value; 
            }
        }

        string _sEnsure6 = "";
        public string sEnsure6
        {
            get
            {
                 return _sEnsure6;
            }
            set
            {
                _sEnsure6 = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iIndex, 0);
            _os.Write(iID, 1);
            _os.Write(iDropType, 2);
            _os.Write(iMin, 3);
            _os.Write(iMax, 4);
            _os.Write(iType1, 5);
            _os.Write(iNum1, 6);
            _os.Write(iProbability1, 7);
            _os.Write(iType2, 9);
            _os.Write(iNum2, 10);
            _os.Write(iProbability2, 11);
            _os.Write(iType3, 13);
            _os.Write(iNum3, 14);
            _os.Write(iProbability3, 15);
            _os.Write(iType4, 17);
            _os.Write(iNum4, 18);
            _os.Write(iProbability4, 19);
            _os.Write(iType5, 21);
            _os.Write(iNum5, 22);
            _os.Write(iProbability5, 23);
            _os.Write(iType6, 25);
            _os.Write(iNum6, 26);
            _os.Write(iProbability6, 27);
            _os.Write(iTraverse, 29);
            _os.Write(iEnsureType, 30);
            _os.Write(sEnsure1, 37);
            _os.Write(sEnsure2, 38);
            _os.Write(sEnsure3, 39);
            _os.Write(sEnsure4, 40);
            _os.Write(sEnsure5, 41);
            _os.Write(sEnsure6, 42);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iIndex = (int) _is.Read(iIndex, 0, false);

            iID = (int) _is.Read(iID, 1, false);

            iDropType = (int) _is.Read(iDropType, 2, false);

            iMin = (int) _is.Read(iMin, 3, false);

            iMax = (int) _is.Read(iMax, 4, false);

            iType1 = (int) _is.Read(iType1, 5, false);

            iNum1 = (int) _is.Read(iNum1, 6, false);

            iProbability1 = (int) _is.Read(iProbability1, 7, false);

            iType2 = (int) _is.Read(iType2, 9, false);

            iNum2 = (int) _is.Read(iNum2, 10, false);

            iProbability2 = (int) _is.Read(iProbability2, 11, false);

            iType3 = (int) _is.Read(iType3, 13, false);

            iNum3 = (int) _is.Read(iNum3, 14, false);

            iProbability3 = (int) _is.Read(iProbability3, 15, false);

            iType4 = (int) _is.Read(iType4, 17, false);

            iNum4 = (int) _is.Read(iNum4, 18, false);

            iProbability4 = (int) _is.Read(iProbability4, 19, false);

            iType5 = (int) _is.Read(iType5, 21, false);

            iNum5 = (int) _is.Read(iNum5, 22, false);

            iProbability5 = (int) _is.Read(iProbability5, 23, false);

            iType6 = (int) _is.Read(iType6, 25, false);

            iNum6 = (int) _is.Read(iNum6, 26, false);

            iProbability6 = (int) _is.Read(iProbability6, 27, false);

            iTraverse = (int) _is.Read(iTraverse, 29, false);

            iEnsureType = (int) _is.Read(iEnsureType, 30, false);

            sEnsure1 = (string) _is.Read(sEnsure1, 37, false);

            sEnsure2 = (string) _is.Read(sEnsure2, 38, false);

            sEnsure3 = (string) _is.Read(sEnsure3, 39, false);

            sEnsure4 = (string) _is.Read(sEnsure4, 40, false);

            sEnsure5 = (string) _is.Read(sEnsure5, 41, false);

            sEnsure6 = (string) _is.Read(sEnsure6, 42, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iIndex, "iIndex");
            _ds.Display(iID, "iID");
            _ds.Display(iDropType, "iDropType");
            _ds.Display(iMin, "iMin");
            _ds.Display(iMax, "iMax");
            _ds.Display(iType1, "iType1");
            _ds.Display(iNum1, "iNum1");
            _ds.Display(iProbability1, "iProbability1");
            _ds.Display(iType2, "iType2");
            _ds.Display(iNum2, "iNum2");
            _ds.Display(iProbability2, "iProbability2");
            _ds.Display(iType3, "iType3");
            _ds.Display(iNum3, "iNum3");
            _ds.Display(iProbability3, "iProbability3");
            _ds.Display(iType4, "iType4");
            _ds.Display(iNum4, "iNum4");
            _ds.Display(iProbability4, "iProbability4");
            _ds.Display(iType5, "iType5");
            _ds.Display(iNum5, "iNum5");
            _ds.Display(iProbability5, "iProbability5");
            _ds.Display(iType6, "iType6");
            _ds.Display(iNum6, "iNum6");
            _ds.Display(iProbability6, "iProbability6");
            _ds.Display(iTraverse, "iTraverse");
            _ds.Display(iEnsureType, "iEnsureType");
            _ds.Display(sEnsure1, "sEnsure1");
            _ds.Display(sEnsure2, "sEnsure2");
            _ds.Display(sEnsure3, "sEnsure3");
            _ds.Display(sEnsure4, "sEnsure4");
            _ds.Display(sEnsure5, "sEnsure5");
            _ds.Display(sEnsure6, "sEnsure6");
        }

        public override void Clear()
        {
            iIndex = 0;
            iID = 0;
            iDropType = 0;
            iMin = 0;
            iMax = 0;
            iType1 = 0;
            iNum1 = 0;
            iProbability1 = 0;
            iType2 = 0;
            iNum2 = 0;
            iProbability2 = 0;
            iType3 = 0;
            iNum3 = 0;
            iProbability3 = 0;
            iType4 = 0;
            iNum4 = 0;
            iProbability4 = 0;
            iType5 = 0;
            iNum5 = 0;
            iProbability5 = 0;
            iType6 = 0;
            iNum6 = 0;
            iProbability6 = 0;
            iTraverse = 0;
            iEnsureType = 0;
            sEnsure1 = "";
            sEnsure2 = "";
            sEnsure3 = "";
            sEnsure4 = "";
            sEnsure5 = "";
            sEnsure6 = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_DropGroup_Client();
            copied.iIndex = this.iIndex;
            copied.iID = this.iID;
            copied.iDropType = this.iDropType;
            copied.iMin = this.iMin;
            copied.iMax = this.iMax;
            copied.iType1 = this.iType1;
            copied.iNum1 = this.iNum1;
            copied.iProbability1 = this.iProbability1;
            copied.iType2 = this.iType2;
            copied.iNum2 = this.iNum2;
            copied.iProbability2 = this.iProbability2;
            copied.iType3 = this.iType3;
            copied.iNum3 = this.iNum3;
            copied.iProbability3 = this.iProbability3;
            copied.iType4 = this.iType4;
            copied.iNum4 = this.iNum4;
            copied.iProbability4 = this.iProbability4;
            copied.iType5 = this.iType5;
            copied.iNum5 = this.iNum5;
            copied.iProbability5 = this.iProbability5;
            copied.iType6 = this.iType6;
            copied.iNum6 = this.iNum6;
            copied.iProbability6 = this.iProbability6;
            copied.iTraverse = this.iTraverse;
            copied.iEnsureType = this.iEnsureType;
            copied.sEnsure1 = this.sEnsure1;
            copied.sEnsure2 = this.sEnsure2;
            copied.sEnsure3 = this.sEnsure3;
            copied.sEnsure4 = this.sEnsure4;
            copied.sEnsure5 = this.sEnsure5;
            copied.sEnsure6 = this.sEnsure6;
            return copied;
        }
    }
}

