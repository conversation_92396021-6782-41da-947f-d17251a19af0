// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class PveRewardDetail : Wup.Jce.JceStruct
    {
        public bool isReceived = true;

        public THeroesUniteReward_Server rewardConf;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(isReceived, 1);
            _os.Write(rewardConf, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            isReceived = (bool) _is.Read(isReceived, 1, false);

            rewardConf = (THeroesUniteReward_Server) _is.Read(rewardConf, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(isReceived, "isReceived");
            _ds.Display(rewardConf, "rewardConf");
        }

        public override void Clear()
        {
            isReceived = true;
            rewardConf = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new PveRewardDetail();
            copied.isReceived = this.isReceived;
            copied.rewardConf = (THeroesUniteReward_Server)JceUtil.DeepClone(this.rewardConf);
            return copied;
        }
    }
}

