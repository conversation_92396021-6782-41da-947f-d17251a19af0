//所在的Excel 【TAC_AITree.xlsm】
//********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_Tag_V2_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iSetID = 0;

        public int iTag = 0;

        public int iPeopleNum = 0;

        public int iHeroID1 = 0;

        public string sHeroPos1 = "";

        public string sHeroEquip1 = "";

        public int iHeroID2 = 0;

        public string sHeroPos2 = "";

        public string sHeroEquip2 = "";

        public int iHeroID3 = 0;

        public string sHeroPos3 = "";

        public string sHeroEquip3 = "";

        public int iHeroID4 = 0;

        public string sHeroPos4 = "";

        public int iHeroID5 = 0;

        public string sHeroPos5 = "";

        public int iHeroID6 = 0;

        public string sHeroPos6 = "";

        public int iHeroID7 = 0;

        public string sHeroPos7 = "";

        public int iHeroID8 = 0;

        public string sHeroPos8 = "";

        public int iHeroID9 = 0;

        public string sHeroPos9 = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iSetID, 1);
            _os.Write(iTag, 2);
            _os.Write(iPeopleNum, 3);
            _os.Write(iHeroID1, 4);
            _os.Write(sHeroPos1, 5);
            _os.Write(sHeroEquip1, 6);
            _os.Write(iHeroID2, 7);
            _os.Write(sHeroPos2, 8);
            _os.Write(sHeroEquip2, 9);
            _os.Write(iHeroID3, 10);
            _os.Write(sHeroPos3, 11);
            _os.Write(sHeroEquip3, 12);
            _os.Write(iHeroID4, 13);
            _os.Write(sHeroPos4, 14);
            _os.Write(iHeroID5, 15);
            _os.Write(sHeroPos5, 16);
            _os.Write(iHeroID6, 17);
            _os.Write(sHeroPos6, 18);
            _os.Write(iHeroID7, 19);
            _os.Write(sHeroPos7, 20);
            _os.Write(iHeroID8, 21);
            _os.Write(sHeroPos8, 22);
            _os.Write(iHeroID9, 23);
            _os.Write(sHeroPos9, 24);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iSetID = (int) _is.Read(iSetID, 1, false);

            iTag = (int) _is.Read(iTag, 2, false);

            iPeopleNum = (int) _is.Read(iPeopleNum, 3, false);

            iHeroID1 = (int) _is.Read(iHeroID1, 4, false);

            sHeroPos1 = (string) _is.Read(sHeroPos1, 5, false);

            sHeroEquip1 = (string) _is.Read(sHeroEquip1, 6, false);

            iHeroID2 = (int) _is.Read(iHeroID2, 7, false);

            sHeroPos2 = (string) _is.Read(sHeroPos2, 8, false);

            sHeroEquip2 = (string) _is.Read(sHeroEquip2, 9, false);

            iHeroID3 = (int) _is.Read(iHeroID3, 10, false);

            sHeroPos3 = (string) _is.Read(sHeroPos3, 11, false);

            sHeroEquip3 = (string) _is.Read(sHeroEquip3, 12, false);

            iHeroID4 = (int) _is.Read(iHeroID4, 13, false);

            sHeroPos4 = (string) _is.Read(sHeroPos4, 14, false);

            iHeroID5 = (int) _is.Read(iHeroID5, 15, false);

            sHeroPos5 = (string) _is.Read(sHeroPos5, 16, false);

            iHeroID6 = (int) _is.Read(iHeroID6, 17, false);

            sHeroPos6 = (string) _is.Read(sHeroPos6, 18, false);

            iHeroID7 = (int) _is.Read(iHeroID7, 19, false);

            sHeroPos7 = (string) _is.Read(sHeroPos7, 20, false);

            iHeroID8 = (int) _is.Read(iHeroID8, 21, false);

            sHeroPos8 = (string) _is.Read(sHeroPos8, 22, false);

            iHeroID9 = (int) _is.Read(iHeroID9, 23, false);

            sHeroPos9 = (string) _is.Read(sHeroPos9, 24, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iSetID, "iSetID");
            _ds.Display(iTag, "iTag");
            _ds.Display(iPeopleNum, "iPeopleNum");
            _ds.Display(iHeroID1, "iHeroID1");
            _ds.Display(sHeroPos1, "sHeroPos1");
            _ds.Display(sHeroEquip1, "sHeroEquip1");
            _ds.Display(iHeroID2, "iHeroID2");
            _ds.Display(sHeroPos2, "sHeroPos2");
            _ds.Display(sHeroEquip2, "sHeroEquip2");
            _ds.Display(iHeroID3, "iHeroID3");
            _ds.Display(sHeroPos3, "sHeroPos3");
            _ds.Display(sHeroEquip3, "sHeroEquip3");
            _ds.Display(iHeroID4, "iHeroID4");
            _ds.Display(sHeroPos4, "sHeroPos4");
            _ds.Display(iHeroID5, "iHeroID5");
            _ds.Display(sHeroPos5, "sHeroPos5");
            _ds.Display(iHeroID6, "iHeroID6");
            _ds.Display(sHeroPos6, "sHeroPos6");
            _ds.Display(iHeroID7, "iHeroID7");
            _ds.Display(sHeroPos7, "sHeroPos7");
            _ds.Display(iHeroID8, "iHeroID8");
            _ds.Display(sHeroPos8, "sHeroPos8");
            _ds.Display(iHeroID9, "iHeroID9");
            _ds.Display(sHeroPos9, "sHeroPos9");
        }

        public override void Clear()
        {
            iID = 0;
            iSetID = 0;
            iTag = 0;
            iPeopleNum = 0;
            iHeroID1 = 0;
            sHeroPos1 = "";
            sHeroEquip1 = "";
            iHeroID2 = 0;
            sHeroPos2 = "";
            sHeroEquip2 = "";
            iHeroID3 = 0;
            sHeroPos3 = "";
            sHeroEquip3 = "";
            iHeroID4 = 0;
            sHeroPos4 = "";
            iHeroID5 = 0;
            sHeroPos5 = "";
            iHeroID6 = 0;
            sHeroPos6 = "";
            iHeroID7 = 0;
            sHeroPos7 = "";
            iHeroID8 = 0;
            sHeroPos8 = "";
            iHeroID9 = 0;
            sHeroPos9 = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TTAC_Tag_V2_Client();
            copied.iID = this.iID;
            copied.iSetID = this.iSetID;
            copied.iTag = this.iTag;
            copied.iPeopleNum = this.iPeopleNum;
            copied.iHeroID1 = this.iHeroID1;
            copied.sHeroPos1 = this.sHeroPos1;
            copied.sHeroEquip1 = this.sHeroEquip1;
            copied.iHeroID2 = this.iHeroID2;
            copied.sHeroPos2 = this.sHeroPos2;
            copied.sHeroEquip2 = this.sHeroEquip2;
            copied.iHeroID3 = this.iHeroID3;
            copied.sHeroPos3 = this.sHeroPos3;
            copied.sHeroEquip3 = this.sHeroEquip3;
            copied.iHeroID4 = this.iHeroID4;
            copied.sHeroPos4 = this.sHeroPos4;
            copied.iHeroID5 = this.iHeroID5;
            copied.sHeroPos5 = this.sHeroPos5;
            copied.iHeroID6 = this.iHeroID6;
            copied.sHeroPos6 = this.sHeroPos6;
            copied.iHeroID7 = this.iHeroID7;
            copied.sHeroPos7 = this.sHeroPos7;
            copied.iHeroID8 = this.iHeroID8;
            copied.sHeroPos8 = this.sHeroPos8;
            copied.iHeroID9 = this.iHeroID9;
            copied.sHeroPos9 = this.sHeroPos9;
            return copied;
        }
    }
}

