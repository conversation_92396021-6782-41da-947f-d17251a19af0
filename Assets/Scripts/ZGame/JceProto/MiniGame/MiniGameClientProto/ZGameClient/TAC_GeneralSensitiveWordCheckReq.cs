// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_GeneralSensitiveWordCheckReq : Wup.Jce.JceStruct
    {
        public int iKey = 0;

        public string sMessage = "";

        public string sSubKey = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iKey, 0);
            _os.Write(sMessage, 1);
            _os.Write(sSubKey, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iKey = (int) _is.Read(iKey, 0, false);

            sMessage = (string) _is.Read(sMessage, 1, false);

            sSubKey = (string) _is.Read(sSubKey, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iKey, "iKey");
            _ds.Display(sMessage, "sMessage");
            _ds.Display(sSubKey, "sSubKey");
        }

        public override void Clear()
        {
            iKey = 0;
            sMessage = "";
            sSubKey = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_GeneralSensitiveWordCheckReq();
            copied.iKey = this.iKey;
            copied.sMessage = this.sMessage;
            copied.sSubKey = this.sSubKey;
            return copied;
        }
    }
}

