// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_ReadTinyRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public System.Collections.Generic.List<int> listId;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 2);
            _os.Write(listId, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 2, false);

            listId = (System.Collections.Generic.List<int>) _is.Read(listId, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(listId, "listId");
        }

        public override void Clear()
        {
            iRet = 0;
            listId = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_ReadTinyRsp();
            copied.iRet = this.iRet;
            copied.listId = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.listId);
            return copied;
        }
    }
}

