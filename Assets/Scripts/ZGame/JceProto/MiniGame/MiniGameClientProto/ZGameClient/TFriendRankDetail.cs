// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TFriendRankDetail : Wup.Jce.JceStruct
    {
        public TUserID uid;

        public TFriendRankPlayer player;

        public TFriendRankOrder order;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(uid, 0);
            _os.Write(player, 1);
            _os.Write(order, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            uid = (TUserID) _is.Read(uid, 0, false);

            player = (TFriendRankPlayer) _is.Read(player, 1, false);

            order = (TFriendRankOrder) _is.Read(order, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(uid, "uid");
            _ds.Display(player, "player");
            _ds.Display(order, "order");
        }

        public override void Clear()
        {
            uid = null;
            player = null;
            order = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TFriendRankDetail();
            copied.uid = (TUserID)JceUtil.DeepClone(this.uid);
            copied.player = (TFriendRankPlayer)JceUtil.DeepClone(this.player);
            copied.order = (TFriendRankOrder)JceUtil.DeepClone(this.order);
            return copied;
        }
    }
}

