// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGetButlerActivityDetailRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public ButlerActivity activity;

        public TButlerActivityData activityData;

        public TKFrame.TKDictionary<int, int> items;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(activity, 1);
            _os.Write(activityData, 3);
            _os.Write(items, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            activity = (ButlerActivity) _is.Read(activity, 1, false);

            activityData = (TButlerActivityData) _is.Read(activityData, 3, false);

            items = (TKFrame.TKDictionary<int, int>) _is.Read(items, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(activity, "activity");
            _ds.Display(activityData, "activityData");
            _ds.Display(items, "items");
        }

        public override void Clear()
        {
            iRet = 0;
            activity = null;
            activityData = null;
            items = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGetButlerActivityDetailRsp();
            copied.iRet = this.iRet;
            copied.activity = (ButlerActivity)JceUtil.DeepClone(this.activity);
            copied.activityData = (TButlerActivityData)JceUtil.DeepClone(this.activityData);
            copied.items = (TKFrame.TKDictionary<int, int>)JceUtil.DeepClone(this.items);
            return copied;
        }
    }
}

