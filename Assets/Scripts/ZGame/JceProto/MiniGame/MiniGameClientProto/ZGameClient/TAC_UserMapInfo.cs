// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_UserMapInfo : Wup.Jce.JceStruct
    {
        int _i8ChairID = 255;
        public int i8ChairID
        {
            get
            {
                 return _i8ChairID;
            }
            set
            {
                _i8ChairID = value; 
            }
        }

        int _iMapID = 0;
        public int iMapID
        {
            get
            {
                 return _iMapID;
            }
            set
            {
                _iMapID = value; 
            }
        }

        int _iInitLife = 0;
        public int iInitLife
        {
            get
            {
                 return _iInitLife;
            }
            set
            {
                _iInitLife = value; 
            }
        }

        int _iTeamID = 0;
        public int iTeamID
        {
            get
            {
                 return _iTeamID;
            }
            set
            {
                _iTeamID = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(i8ChairID, 0);
            _os.Write(iMapID, 1);
            _os.Write(iInitLife, 2);
            _os.Write(iTeamID, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            i8ChairID = (int) _is.Read(i8ChairID, 0, false);

            iMapID = (int) _is.Read(iMapID, 1, false);

            iInitLife = (int) _is.Read(iInitLife, 2, false);

            iTeamID = (int) _is.Read(iTeamID, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(i8ChairID, "i8ChairID");
            _ds.Display(iMapID, "iMapID");
            _ds.Display(iInitLife, "iInitLife");
            _ds.Display(iTeamID, "iTeamID");
        }

        public override void Clear()
        {
            i8ChairID = 255;
            iMapID = 0;
            iInitLife = 0;
            iTeamID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_UserMapInfo();
            copied.i8ChairID = this.i8ChairID;
            copied.iMapID = this.iMapID;
            copied.iInitLife = this.iInitLife;
            copied.iTeamID = this.iTeamID;
            return copied;
        }
    }
}

