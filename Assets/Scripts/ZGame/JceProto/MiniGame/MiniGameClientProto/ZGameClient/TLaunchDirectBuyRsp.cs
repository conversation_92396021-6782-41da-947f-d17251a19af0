// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TLaunchDirectBuyRsp : Wup.Jce.JceStruct
    {
        public int iResult = 0;

        public string sToken = "";

        public string sUrl = "";

        public string sBillNo = "";

        public int iCDSeconds = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iResult, 0);
            _os.Write(sToken, 1);
            _os.Write(sUrl, 2);
            _os.Write(sBillNo, 3);
            _os.Write(iCDSeconds, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iResult = (int) _is.Read(iResult, 0, false);

            sToken = (string) _is.Read(sToken, 1, false);

            sUrl = (string) _is.Read(sUrl, 2, false);

            sBillNo = (string) _is.Read(sBillNo, 3, false);

            iCDSeconds = (int) _is.Read(iCDSeconds, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iResult, "iResult");
            _ds.Display(sToken, "sToken");
            _ds.Display(sUrl, "sUrl");
            _ds.Display(sBillNo, "sBillNo");
            _ds.Display(iCDSeconds, "iCDSeconds");
        }

        public override void Clear()
        {
            iResult = 0;
            sToken = "";
            sUrl = "";
            sBillNo = "";
            iCDSeconds = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TLaunchDirectBuyRsp();
            copied.iResult = this.iResult;
            copied.sToken = this.sToken;
            copied.sUrl = this.sUrl;
            copied.sBillNo = this.sBillNo;
            copied.iCDSeconds = this.iCDSeconds;
            return copied;
        }
    }
}

