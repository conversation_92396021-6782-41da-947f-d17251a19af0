// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class JOCManualLotteryPresentCouponRsp : Wup.Jce.JceStruct
    {
        public int ret = 0;

        /// <summary>
        /// 透传
        /// </summary>
        public TUserID targetUID;

        /// <summary>
        /// 透传
        /// </summary>
        public int presentCount = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(ret, 0);
            _os.Write(targetUID, 1);
            _os.Write(presentCount, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            ret = (int) _is.Read(ret, 0, false);

            targetUID = (TUserID) _is.Read(targetUID, 1, false);

            presentCount = (int) _is.Read(presentCount, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(ret, "ret");
            _ds.Display(targetUID, "targetUID");
            _ds.Display(presentCount, "presentCount");
        }

        public override void Clear()
        {
            ret = 0;
            targetUID = null;
            presentCount = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new JOCManualLotteryPresentCouponRsp();
            copied.ret = this.ret;
            copied.targetUID = (TUserID)JceUtil.DeepClone(this.targetUID);
            copied.presentCount = this.presentCount;
            return copied;
        }
    }
}

