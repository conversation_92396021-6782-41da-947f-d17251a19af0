// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_ModuleSwitch : Wup.Jce.JceStruct
    {
        int _iModuleId = 0;
        public int iModuleId
        {
            get
            {
                 return _iModuleId;
            }
            set
            {
                _iModuleId = value; 
            }
        }

        int _iCloseBeginTime = 0;
        public int iCloseBeginTime
        {
            get
            {
                 return _iCloseBeginTime;
            }
            set
            {
                _iCloseBeginTime = value; 
            }
        }

        int _iCloseEndTime = 0;
        public int iCloseEndTime
        {
            get
            {
                 return _iCloseEndTime;
            }
            set
            {
                _iCloseEndTime = value; 
            }
        }

        int _iMinTier = 0;
        public int iMinTier
        {
            get
            {
                 return _iMinTier;
            }
            set
            {
                _iMinTier = value; 
            }
        }

        int _iMaxTier = 0;
        public int iMaxTier
        {
            get
            {
                 return _iMaxTier;
            }
            set
            {
                _iMaxTier = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iModuleId, 0);
            _os.Write(iCloseBeginTime, 1);
            _os.Write(iCloseEndTime, 2);
            _os.Write(iMinTier, 3);
            _os.Write(iMaxTier, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iModuleId = (int) _is.Read(iModuleId, 0, false);

            iCloseBeginTime = (int) _is.Read(iCloseBeginTime, 1, false);

            iCloseEndTime = (int) _is.Read(iCloseEndTime, 2, false);

            iMinTier = (int) _is.Read(iMinTier, 3, false);

            iMaxTier = (int) _is.Read(iMaxTier, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iModuleId, "iModuleId");
            _ds.Display(iCloseBeginTime, "iCloseBeginTime");
            _ds.Display(iCloseEndTime, "iCloseEndTime");
            _ds.Display(iMinTier, "iMinTier");
            _ds.Display(iMaxTier, "iMaxTier");
        }

        public override void Clear()
        {
            iModuleId = 0;
            iCloseBeginTime = 0;
            iCloseEndTime = 0;
            iMinTier = 0;
            iMaxTier = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_ModuleSwitch();
            copied.iModuleId = this.iModuleId;
            copied.iCloseBeginTime = this.iCloseBeginTime;
            copied.iCloseEndTime = this.iCloseEndTime;
            copied.iMinTier = this.iMinTier;
            copied.iMaxTier = this.iMaxTier;
            return copied;
        }
    }
}

