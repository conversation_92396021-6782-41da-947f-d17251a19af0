// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GiftCenterRecvGiftRecord : Wup.Jce.JceStruct
    {
        public GiftCenterGiftRecord stRecord;

        public TGameUserProfile stGiverProfile;

        public bool bIsRead = false;

        public int iGetTime = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(stRecord, 0);
            _os.Write(stGiverProfile, 1);
            _os.Write(bIsRead, 2);
            _os.Write(iGetTime, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            stRecord = (GiftCenterGiftRecord) _is.Read(stRecord, 0, false);

            stGiverProfile = (TGameUserProfile) _is.Read(stGiverProfile, 1, false);

            bIsRead = (bool) _is.Read(bIsRead, 2, false);

            iGetTime = (int) _is.Read(iGetTime, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(stRecord, "stRecord");
            _ds.Display(stGiverProfile, "stGiverProfile");
            _ds.Display(bIsRead, "bIsRead");
            _ds.Display(iGetTime, "iGetTime");
        }

        public override void Clear()
        {
            stRecord = null;
            stGiverProfile = null;
            bIsRead = false;
            iGetTime = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GiftCenterRecvGiftRecord();
            copied.stRecord = (GiftCenterGiftRecord)JceUtil.DeepClone(this.stRecord);
            copied.stGiverProfile = (TGameUserProfile)JceUtil.DeepClone(this.stGiverProfile);
            copied.bIsRead = this.bIsRead;
            copied.iGetTime = this.iGetTime;
            return copied;
        }
    }
}

