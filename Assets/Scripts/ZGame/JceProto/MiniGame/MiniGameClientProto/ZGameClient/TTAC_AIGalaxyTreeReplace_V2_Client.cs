// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_AIGalaxyTreeReplace_V2_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sMode = "";

        public string sGalaxyID = "";

        public string sOriDesicionBranch = "";

        public string sNewDesicionBranch = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sMode, 1);
            _os.Write(sGalaxyID, 2);
            _os.Write(sOriDesicionBranch, 3);
            _os.Write(sNewDesicionBranch, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sMode = (string) _is.Read(sMode, 1, false);

            sGalaxyID = (string) _is.Read(sGalaxyID, 2, false);

            sOriDesicionBranch = (string) _is.Read(sOriDesicionBranch, 3, false);

            sNewDesicionBranch = (string) _is.Read(sNewDesicionBranch, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sMode, "sMode");
            _ds.Display(sGalaxyID, "sGalaxyID");
            _ds.Display(sOriDesicionBranch, "sOriDesicionBranch");
            _ds.Display(sNewDesicionBranch, "sNewDesicionBranch");
        }

        public override void Clear()
        {
            iID = 0;
            sMode = "";
            sGalaxyID = "";
            sOriDesicionBranch = "";
            sNewDesicionBranch = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TTAC_AIGalaxyTreeReplace_V2_Client();
            copied.iID = this.iID;
            copied.sMode = this.sMode;
            copied.sGalaxyID = this.sGalaxyID;
            copied.sOriDesicionBranch = this.sOriDesicionBranch;
            copied.sNewDesicionBranch = this.sNewDesicionBranch;
            return copied;
        }
    }
}

