// **********************************************************************
// This file was generated by a TAF parser!
// Generated from `SGameDBConfigProto.jce'
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_MisBuff_Server : Wup.Jce.JceStruct
    {
        int _iBuffID = 0;
        public int iBuffID
        {
            get
            {
                 return _iBuffID;
            }
            set
            {
                _iBuffID = value; 
            }
        }

        string _sName = "";
        public string sName
        {
            get
            {
                 return _sName;
            }
            set
            {
                _sName = value; 
            }
        }

        string _sDesc = "";
        public string sDesc
        {
            get
            {
                 return _sDesc;
            }
            set
            {
                _sDesc = value; 
            }
        }

        int _iValue = 0;
        public int iValue
        {
            get
            {
                 return _iValue;
            }
            set
            {
                _iValue = value; 
            }
        }

        int _iBuffSkillID = 0;
        public int iBuffSkillID
        {
            get
            {
                 return _iBuffSkillID;
            }
            set
            {
                _iBuffSkillID = value; 
            }
        }

        string _sBuffIcon = "";
        public string sBuffIcon
        {
            get
            {
                 return _sBuffIcon;
            }
            set
            {
                _sBuffIcon = value; 
            }
        }

        string _sBrief = "";
        public string sBrief
        {
            get
            {
                 return _sBrief;
            }
            set
            {
                _sBrief = value; 
            }
        }

        int _iValueRound = 0;
        public int iValueRound
        {
            get
            {
                 return _iValueRound;
            }
            set
            {
                _iValueRound = value; 
            }
        }

        string _sValues = "";
        public string sValues
        {
            get
            {
                 return _sValues;
            }
            set
            {
                _sValues = value; 
            }
        }

        int _iBuffGroup = 0;
        public int iBuffGroup
        {
            get
            {
                 return _iBuffGroup;
            }
            set
            {
                _iBuffGroup = value; 
            }
        }

        int _iBuffKind = 0;
        public int iBuffKind
        {
            get
            {
                 return _iBuffKind;
            }
            set
            {
                _iBuffKind = value; 
            }
        }

        int _iBuffType = 0;
        public int iBuffType
        {
            get
            {
                 return _iBuffType;
            }
            set
            {
                _iBuffType = value; 
            }
        }

        int _iShapeKind = 0;
        public int iShapeKind
        {
            get
            {
                 return _iShapeKind;
            }
            set
            {
                _iShapeKind = value; 
            }
        }

        string _sLandFormEffect = "";
        public string sLandFormEffect
        {
            get
            {
                 return _sLandFormEffect;
            }
            set
            {
                _sLandFormEffect = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iBuffID, 0);
            _os.Write(sName, 1);
            _os.Write(sDesc, 2);
            _os.Write(iValue, 3);
            _os.Write(iBuffSkillID, 4);
            _os.Write(sBuffIcon, 5);
            _os.Write(sBrief, 6);
            _os.Write(iValueRound, 7);
            _os.Write(sValues, 8);
            _os.Write(iBuffGroup, 9);
            _os.Write(iBuffKind, 10);
            _os.Write(iBuffType, 11);
            _os.Write(iShapeKind, 12);
            _os.Write(sLandFormEffect, 13);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iBuffID = (int) _is.Read(iBuffID, 0, false);

            sName = (string) _is.Read(sName, 1, false);

            sDesc = (string) _is.Read(sDesc, 2, false);

            iValue = (int) _is.Read(iValue, 3, false);

            iBuffSkillID = (int) _is.Read(iBuffSkillID, 4, false);

            sBuffIcon = (string) _is.Read(sBuffIcon, 5, false);

            sBrief = (string) _is.Read(sBrief, 6, false);

            iValueRound = (int) _is.Read(iValueRound, 7, false);

            sValues = (string) _is.Read(sValues, 8, false);

            iBuffGroup = (int) _is.Read(iBuffGroup, 9, false);

            iBuffKind = (int) _is.Read(iBuffKind, 10, false);

            iBuffType = (int) _is.Read(iBuffType, 11, false);

            iShapeKind = (int) _is.Read(iShapeKind, 12, false);

            sLandFormEffect = (string) _is.Read(sLandFormEffect, 13, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iBuffID, "iBuffID");
            _ds.Display(sName, "sName");
            _ds.Display(sDesc, "sDesc");
            _ds.Display(iValue, "iValue");
            _ds.Display(iBuffSkillID, "iBuffSkillID");
            _ds.Display(sBuffIcon, "sBuffIcon");
            _ds.Display(sBrief, "sBrief");
            _ds.Display(iValueRound, "iValueRound");
            _ds.Display(sValues, "sValues");
            _ds.Display(iBuffGroup, "iBuffGroup");
            _ds.Display(iBuffKind, "iBuffKind");
            _ds.Display(iBuffType, "iBuffType");
            _ds.Display(iShapeKind, "iShapeKind");
            _ds.Display(sLandFormEffect, "sLandFormEffect");
        }

    }
}

