// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_TCmdS2CNotifyDaulTinyTransfer : Wup.Jce.JceStruct
    {
        int _iChairID = 0;
        public int iChairID
        {
            get
            {
                 return _iChairID;
            }
            set
            {
                _iChairID = value; 
            }
        }

        int _iSrcBattleId = 0;
        public int iSrcBattleId
        {
            get
            {
                 return _iSrcBattleId;
            }
            set
            {
                _iSrcBattleId = value; 
            }
        }

        int _iDstBattleId = 0;
        public int iDstBattleId
        {
            get
            {
                 return _iDstBattleId;
            }
            set
            {
                _iDstBattleId = value; 
            }
        }

        int _iState = 0;
        public int iState
        {
            get
            {
                 return _iState;
            }
            set
            {
                _iState = value; 
            }
        }

        int _iTime = 0;
        public int iTime
        {
            get
            {
                 return _iTime;
            }
            set
            {
                _iTime = value; 
            }
        }

        int _iTotalTime = 0;
        public int iTotalTime
        {
            get
            {
                 return _iTotalTime;
            }
            set
            {
                _iTotalTime = value; 
            }
        }

        int _iTransferPosX = 0;
        public int iTransferPosX
        {
            get
            {
                 return _iTransferPosX;
            }
            set
            {
                _iTransferPosX = value; 
            }
        }

        int _iTransferPosZ = 0;
        public int iTransferPosZ
        {
            get
            {
                 return _iTransferPosZ;
            }
            set
            {
                _iTransferPosZ = value; 
            }
        }

        bool _bSwitchPlayer = true;
        public bool bSwitchPlayer
        {
            get
            {
                 return _bSwitchPlayer;
            }
            set
            {
                _bSwitchPlayer = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iChairID, 0);
            _os.Write(iSrcBattleId, 1);
            _os.Write(iDstBattleId, 2);
            _os.Write(iState, 3);
            _os.Write(iTime, 4);
            _os.Write(iTotalTime, 5);
            _os.Write(iTransferPosX, 6);
            _os.Write(iTransferPosZ, 7);
            _os.Write(bSwitchPlayer, 8);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iChairID = (int) _is.Read(iChairID, 0, false);

            iSrcBattleId = (int) _is.Read(iSrcBattleId, 1, false);

            iDstBattleId = (int) _is.Read(iDstBattleId, 2, false);

            iState = (int) _is.Read(iState, 3, false);

            iTime = (int) _is.Read(iTime, 4, false);

            iTotalTime = (int) _is.Read(iTotalTime, 5, false);

            iTransferPosX = (int) _is.Read(iTransferPosX, 6, false);

            iTransferPosZ = (int) _is.Read(iTransferPosZ, 7, false);

            bSwitchPlayer = (bool) _is.Read(bSwitchPlayer, 8, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iChairID, "iChairID");
            _ds.Display(iSrcBattleId, "iSrcBattleId");
            _ds.Display(iDstBattleId, "iDstBattleId");
            _ds.Display(iState, "iState");
            _ds.Display(iTime, "iTime");
            _ds.Display(iTotalTime, "iTotalTime");
            _ds.Display(iTransferPosX, "iTransferPosX");
            _ds.Display(iTransferPosZ, "iTransferPosZ");
            _ds.Display(bSwitchPlayer, "bSwitchPlayer");
        }

        public override void Clear()
        {
            iChairID = 0;
            iSrcBattleId = 0;
            iDstBattleId = 0;
            iState = 0;
            iTime = 0;
            iTotalTime = 0;
            iTransferPosX = 0;
            iTransferPosZ = 0;
            bSwitchPlayer = true;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_TCmdS2CNotifyDaulTinyTransfer();
            copied.iChairID = this.iChairID;
            copied.iSrcBattleId = this.iSrcBattleId;
            copied.iDstBattleId = this.iDstBattleId;
            copied.iState = this.iState;
            copied.iTime = this.iTime;
            copied.iTotalTime = this.iTotalTime;
            copied.iTransferPosX = this.iTransferPosX;
            copied.iTransferPosZ = this.iTransferPosZ;
            copied.bSwitchPlayer = this.bSwitchPlayer;
            return copied;
        }
    }
}

