//所在的Excel 【TAC_DropAbility.xlsm】
//***************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_Drop_Client : Wup.Jce.JceStruct
    {
        int _iTurn = 0;
        public int iTurn
        {
            get
            {
                 return _iTurn;
            }
            set
            {
                _iTurn = value; 
            }
        }

        int _iPossibility = 0;
        public int iPossibility
        {
            get
            {
                 return _iPossibility;
            }
            set
            {
                _iPossibility = value; 
            }
        }

        string _sBuffID = "";
        public string sBuffID
        {
            get
            {
                 return _sBuffID;
            }
            set
            {
                _sBuffID = value; 
            }
        }

        string _sWeight = "";
        public string sWeight
        {
            get
            {
                 return _sWeight;
            }
            set
            {
                _sWeight = value; 
            }
        }

        int _iDropNum = 0;
        public int iDropNum
        {
            get
            {
                 return _iDropNum;
            }
            set
            {
                _iDropNum = value; 
            }
        }

        int _iLosePossibility = 0;
        public int iLosePossibility
        {
            get
            {
                 return _iLosePossibility;
            }
            set
            {
                _iLosePossibility = value; 
            }
        }

        string _sLoseBuffID = "";
        public string sLoseBuffID
        {
            get
            {
                 return _sLoseBuffID;
            }
            set
            {
                _sLoseBuffID = value; 
            }
        }

        string _sLoseWeight = "";
        public string sLoseWeight
        {
            get
            {
                 return _sLoseWeight;
            }
            set
            {
                _sLoseWeight = value; 
            }
        }

        int _iLoseDropNum = 0;
        public int iLoseDropNum
        {
            get
            {
                 return _iLoseDropNum;
            }
            set
            {
                _iLoseDropNum = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iTurn, 0);
            _os.Write(iPossibility, 1);
            _os.Write(sBuffID, 2);
            _os.Write(sWeight, 3);
            _os.Write(iDropNum, 4);
            _os.Write(iLosePossibility, 5);
            _os.Write(sLoseBuffID, 6);
            _os.Write(sLoseWeight, 7);
            _os.Write(iLoseDropNum, 8);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iTurn = (int) _is.Read(iTurn, 0, false);

            iPossibility = (int) _is.Read(iPossibility, 1, false);

            sBuffID = (string) _is.Read(sBuffID, 2, false);

            sWeight = (string) _is.Read(sWeight, 3, false);

            iDropNum = (int) _is.Read(iDropNum, 4, false);

            iLosePossibility = (int) _is.Read(iLosePossibility, 5, false);

            sLoseBuffID = (string) _is.Read(sLoseBuffID, 6, false);

            sLoseWeight = (string) _is.Read(sLoseWeight, 7, false);

            iLoseDropNum = (int) _is.Read(iLoseDropNum, 8, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iTurn, "iTurn");
            _ds.Display(iPossibility, "iPossibility");
            _ds.Display(sBuffID, "sBuffID");
            _ds.Display(sWeight, "sWeight");
            _ds.Display(iDropNum, "iDropNum");
            _ds.Display(iLosePossibility, "iLosePossibility");
            _ds.Display(sLoseBuffID, "sLoseBuffID");
            _ds.Display(sLoseWeight, "sLoseWeight");
            _ds.Display(iLoseDropNum, "iLoseDropNum");
        }

        public override void Clear()
        {
            iTurn = 0;
            iPossibility = 0;
            sBuffID = "";
            sWeight = "";
            iDropNum = 0;
            iLosePossibility = 0;
            sLoseBuffID = "";
            sLoseWeight = "";
            iLoseDropNum = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TTAC_Drop_Client();
            copied.iTurn = this.iTurn;
            copied.iPossibility = this.iPossibility;
            copied.sBuffID = this.sBuffID;
            copied.sWeight = this.sWeight;
            copied.iDropNum = this.iDropNum;
            copied.iLosePossibility = this.iLosePossibility;
            copied.sLoseBuffID = this.sLoseBuffID;
            copied.sLoseWeight = this.sLoseWeight;
            copied.iLoseDropNum = this.iLoseDropNum;
            return copied;
        }
    }
}

