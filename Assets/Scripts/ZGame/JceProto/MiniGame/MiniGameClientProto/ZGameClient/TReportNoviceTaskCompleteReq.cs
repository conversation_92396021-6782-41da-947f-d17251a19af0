// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TReportNoviceTaskCompleteReq : Wup.Jce.JceStruct
    {
        public int iTaskID = 0;

        public long id = 0;

        public TMidasTokenInfo stMidasToken;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iTaskID, 0);
            _os.Write(id, 1);
            _os.Write(stMidasToken, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iTaskID = (int) _is.Read(iTaskID, 0, false);

            id = (long) _is.Read(id, 1, false);

            stMidasToken = (TMidasTokenInfo) _is.Read(stMidasToken, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iTaskID, "iTaskID");
            _ds.Display(id, "id");
            _ds.Display(stMidasToken, "stMidasToken");
        }

        public override void Clear()
        {
            iTaskID = 0;
            id = 0;
            stMidasToken = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TReportNoviceTaskCompleteReq();
            copied.iTaskID = this.iTaskID;
            copied.id = this.id;
            copied.stMidasToken = (TMidasTokenInfo)JceUtil.DeepClone(this.stMidasToken);
            return copied;
        }
    }
}

