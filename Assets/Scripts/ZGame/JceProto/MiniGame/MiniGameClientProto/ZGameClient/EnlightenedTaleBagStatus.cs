// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class EnlightenedTaleBagStatus : Wup.Jce.JceStruct
    {
        public int iIndex = 0;

        public int iCount = 0;

        public int iPoolID = 0;

        public int iStatus = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iIndex, 0);
            _os.Write(iCount, 1);
            _os.Write(iPoolID, 2);
            _os.Write(iStatus, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iIndex = (int) _is.Read(iIndex, 0, false);

            iCount = (int) _is.Read(iCount, 1, false);

            iPoolID = (int) _is.Read(iPoolID, 2, false);

            iStatus = (int) _is.Read(iStatus, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iIndex, "iIndex");
            _ds.Display(iCount, "iCount");
            _ds.Display(iPoolID, "iPoolID");
            _ds.Display(iStatus, "iStatus");
        }

        public override void Clear()
        {
            iIndex = 0;
            iCount = 0;
            iPoolID = 0;
            iStatus = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new EnlightenedTaleBagStatus();
            copied.iIndex = this.iIndex;
            copied.iCount = this.iCount;
            copied.iPoolID = this.iPoolID;
            copied.iStatus = this.iStatus;
            return copied;
        }
    }
}

