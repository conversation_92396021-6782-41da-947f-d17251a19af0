// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GiveupBCRoguelikeRsp : Wup.Jce.JceStruct
    {
        public int err = 0;

        public int gameModuleID = 0;

        public int levelID = 0;

        public System.Collections.Generic.List<BCRoguelikeBossDetail> bossDetails;

        public System.Collections.Generic.List<PveRouteDetail> routeDetails;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(err, 0);
            _os.Write(gameModuleID, 1);
            _os.Write(levelID, 2);
            _os.Write(bossDetails, 3);
            _os.Write(routeDetails, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            err = (int)_is.Read(err, 0, false);

            gameModuleID = (int)_is.Read(gameModuleID, 1, false);

            levelID = (int)_is.Read(levelID, 2, false);

            bossDetails = (System.Collections.Generic.List<BCRoguelikeBossDetail>)_is.Read(bossDetails, 3, false);

            routeDetails = (System.Collections.Generic.List<PveRouteDetail>)_is.Read(routeDetails, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(err, "err");
            _ds.Display(gameModuleID, "gameModuleID");
            _ds.Display(levelID, "levelID");
            _ds.Display(bossDetails, "bossDetails");
            _ds.Display(routeDetails, "routeDetails");
        }

        public override void Clear()
        {
            err = 0;
            gameModuleID = 0;
            levelID = 0;
            bossDetails = null;
            routeDetails = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GiveupBCRoguelikeRsp();
            copied.err = this.err;
            copied.gameModuleID = this.gameModuleID;
            copied.levelID = this.levelID;
            copied.bossDetails = (System.Collections.Generic.List<BCRoguelikeBossDetail>)JceUtil.DeepClone(this.bossDetails);
            copied.routeDetails = (System.Collections.Generic.List<PveRouteDetail>)JceUtil.DeepClone(this.routeDetails);
            return copied;
        }
    }
}

