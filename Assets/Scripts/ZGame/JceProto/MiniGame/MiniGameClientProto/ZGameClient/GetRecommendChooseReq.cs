// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GetRecommendChooseReq : Wup.Jce.JceStruct
    {
        long _recordId = 0;
        public long recordId
        {
            get
            {
                 return _recordId;
            }
            set
            {
                _recordId = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(recordId, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            recordId = (long) _is.Read(recordId, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(recordId, "recordId");
        }

        public override void Clear()
        {
            recordId = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GetRecommendChooseReq();
            copied.recordId = this.recordId;
            return copied;
        }
    }
}

