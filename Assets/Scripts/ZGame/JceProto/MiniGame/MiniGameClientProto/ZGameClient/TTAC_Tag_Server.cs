// **********************************************************************
// This file was generated by a TAF parser!
// Generated from `SGameDBConfigProto.jce'
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_Tag_Server : Wup.Jce.JceStruct
    {
        int _iID = 0;
        public int iID
        {
            get
            {
                 return _iID;
            }
            set
            {
                _iID = value; 
            }
        }

        int _iTAG = 0;
        public int iTAG
        {
            get
            {
                 return _iTAG;
            }
            set
            {
                _iTAG = value; 
            }
        }

        int _iHero = 0;
        public int iHero
        {
            get
            {
                 return _iHero;
            }
            set
            {
                _iHero = value; 
            }
        }

        int _iPriority = 0;
        public int iPriority
        {
            get
            {
                 return _iPriority;
            }
            set
            {
                _iPriority = value; 
            }
        }

        int _iAdvancePriority = 0;
        public int iAdvancePriority
        {
            get
            {
                 return _iAdvancePriority;
            }
            set
            {
                _iAdvancePriority = value; 
            }
        }

        int _iBuildPriority = 0;
        public int iBuildPriority
        {
            get
            {
                 return _iBuildPriority;
            }
            set
            {
                _iBuildPriority = value; 
            }
        }

        int _iBuildGroup = 0;
        public int iBuildGroup
        {
            get
            {
                 return _iBuildGroup;
            }
            set
            {
                _iBuildGroup = value; 
            }
        }

        int _iBuildNumber = 0;
        public int iBuildNumber
        {
            get
            {
                 return _iBuildNumber;
            }
            set
            {
                _iBuildNumber = value; 
            }
        }

        int _iHeroGroup = 0;
        public int iHeroGroup
        {
            get
            {
                 return _iHeroGroup;
            }
            set
            {
                _iHeroGroup = value; 
            }
        }

        int _iSubstituteHero = 0;
        public int iSubstituteHero
        {
            get
            {
                 return _iSubstituteHero;
            }
            set
            {
                _iSubstituteHero = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iTAG, 1);
            _os.Write(iHero, 2);
            _os.Write(iPriority, 5);
            _os.Write(iAdvancePriority, 6);
            _os.Write(iBuildPriority, 9);
            _os.Write(iBuildGroup, 10);
            _os.Write(iBuildNumber, 11);
            _os.Write(iHeroGroup, 12);
            _os.Write(iSubstituteHero, 17);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iTAG = (int) _is.Read(iTAG, 1, false);

            iHero = (int) _is.Read(iHero, 2, false);

            iPriority = (int) _is.Read(iPriority, 5, false);

            iAdvancePriority = (int) _is.Read(iAdvancePriority, 6, false);

            iBuildPriority = (int) _is.Read(iBuildPriority, 9, false);

            iBuildGroup = (int) _is.Read(iBuildGroup, 10, false);

            iBuildNumber = (int) _is.Read(iBuildNumber, 11, false);

            iHeroGroup = (int) _is.Read(iHeroGroup, 12, false);

            iSubstituteHero = (int) _is.Read(iSubstituteHero, 17, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iTAG, "iTAG");
            _ds.Display(iHero, "iHero");
            _ds.Display(iPriority, "iPriority");
            _ds.Display(iAdvancePriority, "iAdvancePriority");
            _ds.Display(iBuildPriority, "iBuildPriority");
            _ds.Display(iBuildGroup, "iBuildGroup");
            _ds.Display(iBuildNumber, "iBuildNumber");
            _ds.Display(iHeroGroup, "iHeroGroup");
            _ds.Display(iSubstituteHero, "iSubstituteHero");
        }

    }
}

