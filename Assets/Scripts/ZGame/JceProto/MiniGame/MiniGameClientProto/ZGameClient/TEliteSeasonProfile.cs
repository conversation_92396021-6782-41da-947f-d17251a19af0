// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TEliteSeasonProfile : Wup.Jce.JceStruct
    {
        string _sCardGroupName = "";
        public string sCardGroupName
        {
            get
            {
                 return _sCardGroupName;
            }
            set
            {
                _sCardGroupName = value; 
            }
        }

        string _sRecommDesc = "";
        public string sRecommDesc
        {
            get
            {
                 return _sRecommDesc;
            }
            set
            {
                _sRecommDesc = value; 
            }
        }

        public TKFrame.TKDictionary<int, int> mapCardList {get; set;} 

        int _iClassID = 0;
        public int iClassID
        {
            get
            {
                 return _iClassID;
            }
            set
            {
                _iClassID = value; 
            }
        }

        int _iHighestStarCount = 0;
        public int iHighestStarCount
        {
            get
            {
                 return _iHighestStarCount;
            }
            set
            {
                _iHighestStarCount = value; 
            }
        }

        int _iTotalGame = 0;
        public int iTotalGame
        {
            get
            {
                 return _iTotalGame;
            }
            set
            {
                _iTotalGame = value; 
            }
        }

        int _iWinGame = 0;
        public int iWinGame
        {
            get
            {
                 return _iWinGame;
            }
            set
            {
                _iWinGame = value; 
            }
        }

        int _iMaxWinInRow = 0;
        public int iMaxWinInRow
        {
            get
            {
                 return _iMaxWinInRow;
            }
            set
            {
                _iMaxWinInRow = value; 
            }
        }

        int _iCardGroupType = 0;
        public int iCardGroupType
        {
            get
            {
                 return _iCardGroupType;
            }
            set
            {
                _iCardGroupType = value; 
            }
        }

        public System.Collections.Generic.List<int> vecCompetition {get; set;} 

        public TUserID stCardUserID {get; set;} 

        int _iRent = 0;
        public int iRent
        {
            get
            {
                 return _iRent;
            }
            set
            {
                _iRent = value; 
            }
        }

        int _iCardGroupID = 0;
        public int iCardGroupID
        {
            get
            {
                 return _iCardGroupID;
            }
            set
            {
                _iCardGroupID = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(sCardGroupName, 0);
            _os.Write(sRecommDesc, 1);
            _os.Write(mapCardList, 2);
            _os.Write(iClassID, 3);
            _os.Write(iHighestStarCount, 4);
            _os.Write(iTotalGame, 5);
            _os.Write(iWinGame, 6);
            _os.Write(iMaxWinInRow, 7);
            _os.Write(iCardGroupType, 8);
            _os.Write(vecCompetition, 9);
            _os.Write(stCardUserID, 10);
            _os.Write(iRent, 11);
            _os.Write(iCardGroupID, 12);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            sCardGroupName = (string) _is.Read(sCardGroupName, 0, false);

            sRecommDesc = (string) _is.Read(sRecommDesc, 1, false);

            mapCardList = (TKFrame.TKDictionary<int, int>) _is.Read(mapCardList, 2, false);

            iClassID = (int) _is.Read(iClassID, 3, false);

            iHighestStarCount = (int) _is.Read(iHighestStarCount, 4, false);

            iTotalGame = (int) _is.Read(iTotalGame, 5, false);

            iWinGame = (int) _is.Read(iWinGame, 6, false);

            iMaxWinInRow = (int) _is.Read(iMaxWinInRow, 7, false);

            iCardGroupType = (int) _is.Read(iCardGroupType, 8, false);

            vecCompetition = (System.Collections.Generic.List<int>) _is.Read(vecCompetition, 9, false);

            stCardUserID = (TUserID) _is.Read(stCardUserID, 10, false);

            iRent = (int) _is.Read(iRent, 11, false);

            iCardGroupID = (int) _is.Read(iCardGroupID, 12, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(sCardGroupName, "sCardGroupName");
            _ds.Display(sRecommDesc, "sRecommDesc");
            _ds.Display(mapCardList, "mapCardList");
            _ds.Display(iClassID, "iClassID");
            _ds.Display(iHighestStarCount, "iHighestStarCount");
            _ds.Display(iTotalGame, "iTotalGame");
            _ds.Display(iWinGame, "iWinGame");
            _ds.Display(iMaxWinInRow, "iMaxWinInRow");
            _ds.Display(iCardGroupType, "iCardGroupType");
            _ds.Display(vecCompetition, "vecCompetition");
            _ds.Display(stCardUserID, "stCardUserID");
            _ds.Display(iRent, "iRent");
            _ds.Display(iCardGroupID, "iCardGroupID");
        }

        public override void Clear()
        {
            sCardGroupName = "";
            sRecommDesc = "";
            mapCardList = null;
            iClassID = 0;
            iHighestStarCount = 0;
            iTotalGame = 0;
            iWinGame = 0;
            iMaxWinInRow = 0;
            iCardGroupType = 0;
            vecCompetition = null;
            stCardUserID = null;
            iRent = 0;
            iCardGroupID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TEliteSeasonProfile();
            copied.sCardGroupName = this.sCardGroupName;
            copied.sRecommDesc = this.sRecommDesc;
            copied.mapCardList = (TKFrame.TKDictionary<int, int>)JceUtil.DeepClone(this.mapCardList);
            copied.iClassID = this.iClassID;
            copied.iHighestStarCount = this.iHighestStarCount;
            copied.iTotalGame = this.iTotalGame;
            copied.iWinGame = this.iWinGame;
            copied.iMaxWinInRow = this.iMaxWinInRow;
            copied.iCardGroupType = this.iCardGroupType;
            copied.vecCompetition = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.vecCompetition);
            copied.stCardUserID = (TUserID)JceUtil.DeepClone(this.stCardUserID);
            copied.iRent = this.iRent;
            copied.iCardGroupID = this.iCardGroupID;
            return copied;
        }
    }
}

