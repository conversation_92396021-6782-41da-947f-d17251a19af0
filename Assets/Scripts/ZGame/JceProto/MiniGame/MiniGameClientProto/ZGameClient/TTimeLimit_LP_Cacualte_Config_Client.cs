// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTimeLimit_LP_Cacualte_Config_Client : Wup.Jce.JceStruct
    {
        /// <summary>
        /// 段位ID
        /// </summary>
        public int iRankID = 0;

        /// <summary>
        /// 段位名
        /// </summary>
        public string sName = "";

        /// <summary>
        /// 目标MMR
        /// </summary>
        public string sTarget_MMR = "";

        /// <summary>
        /// 最大技术系数 - 失去胜点
        /// </summary>
        public string sMaxSkillFactor_LPLoss = "";

        /// <summary>
        /// 最小技术系数 - 失去胜点
        /// </summary>
        public string sMinSkillFactor_LPLoss = "";

        /// <summary>
        /// 最大技术系数 - 得到胜点
        /// </summary>
        public string sMaxSkillFactor_LPGain = "";

        /// <summary>
        /// 最小技术系数 - 得到胜点
        /// </summary>
        public string sMinSkillFactor_LPGain = "";

        /// <summary>
        /// 段位边界标识(降大段)
        /// </summary>
        public int iTierEdgeFlag = 0;

        /// <summary>
        /// 段位图标
        /// </summary>
        public string sIcon = "";

        /// <summary>
        /// 段位小图标
        /// </summary>
        public string sminiIcon = "";

        /// <summary>
        /// 大段位ID
        /// </summary>
        public int iBigRankID = 0;

        /// <summary>
        /// 段位名
        /// </summary>
        public string sShowName = "";

        /// <summary>
        /// 升段动画视频
        /// </summary>
        public string sLevelUpMovie = "";

        /// <summary>
        /// 升段动画音频
        /// </summary>
        public string sLevelUpAudio = "";

        /// <summary>
        /// 段位ID
        /// </summary>
        public int iID = 0;

        /// <summary>
        /// 玩法类型
        /// </summary>
        public int iSceneType = 0;

        /// <summary>
        /// 段位图标AB
        /// </summary>
        public string sIconAB = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRankID, 0);
            _os.Write(sName, 1);
            _os.Write(sTarget_MMR, 2);
            _os.Write(sMaxSkillFactor_LPLoss, 3);
            _os.Write(sMinSkillFactor_LPLoss, 4);
            _os.Write(sMaxSkillFactor_LPGain, 5);
            _os.Write(sMinSkillFactor_LPGain, 6);
            _os.Write(iTierEdgeFlag, 7);
            _os.Write(sIcon, 8);
            _os.Write(sminiIcon, 9);
            _os.Write(iBigRankID, 10);
            _os.Write(sShowName, 11);
            _os.Write(sLevelUpMovie, 12);
            _os.Write(sLevelUpAudio, 13);
            _os.Write(iID, 14);
            _os.Write(iSceneType, 15);
            _os.Write(sIconAB, 16);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRankID = (int) _is.Read(iRankID, 0, false);

            sName = (string) _is.Read(sName, 1, false);

            sTarget_MMR = (string) _is.Read(sTarget_MMR, 2, false);

            sMaxSkillFactor_LPLoss = (string) _is.Read(sMaxSkillFactor_LPLoss, 3, false);

            sMinSkillFactor_LPLoss = (string) _is.Read(sMinSkillFactor_LPLoss, 4, false);

            sMaxSkillFactor_LPGain = (string) _is.Read(sMaxSkillFactor_LPGain, 5, false);

            sMinSkillFactor_LPGain = (string) _is.Read(sMinSkillFactor_LPGain, 6, false);

            iTierEdgeFlag = (int) _is.Read(iTierEdgeFlag, 7, false);

            sIcon = (string) _is.Read(sIcon, 8, false);

            sminiIcon = (string) _is.Read(sminiIcon, 9, false);

            iBigRankID = (int) _is.Read(iBigRankID, 10, false);

            sShowName = (string) _is.Read(sShowName, 11, false);

            sLevelUpMovie = (string) _is.Read(sLevelUpMovie, 12, false);

            sLevelUpAudio = (string) _is.Read(sLevelUpAudio, 13, false);

            iID = (int) _is.Read(iID, 14, false);

            iSceneType = (int) _is.Read(iSceneType, 15, false);

            sIconAB = (string) _is.Read(sIconAB, 16, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRankID, "iRankID");
            _ds.Display(sName, "sName");
            _ds.Display(sTarget_MMR, "sTarget_MMR");
            _ds.Display(sMaxSkillFactor_LPLoss, "sMaxSkillFactor_LPLoss");
            _ds.Display(sMinSkillFactor_LPLoss, "sMinSkillFactor_LPLoss");
            _ds.Display(sMaxSkillFactor_LPGain, "sMaxSkillFactor_LPGain");
            _ds.Display(sMinSkillFactor_LPGain, "sMinSkillFactor_LPGain");
            _ds.Display(iTierEdgeFlag, "iTierEdgeFlag");
            _ds.Display(sIcon, "sIcon");
            _ds.Display(sminiIcon, "sminiIcon");
            _ds.Display(iBigRankID, "iBigRankID");
            _ds.Display(sShowName, "sShowName");
            _ds.Display(sLevelUpMovie, "sLevelUpMovie");
            _ds.Display(sLevelUpAudio, "sLevelUpAudio");
            _ds.Display(iID, "iID");
            _ds.Display(iSceneType, "iSceneType");
            _ds.Display(sIconAB, "sIconAB");
        }

        public override void Clear()
        {
            iRankID = 0;
            sName = "";
            sTarget_MMR = "";
            sMaxSkillFactor_LPLoss = "";
            sMinSkillFactor_LPLoss = "";
            sMaxSkillFactor_LPGain = "";
            sMinSkillFactor_LPGain = "";
            iTierEdgeFlag = 0;
            sIcon = "";
            sminiIcon = "";
            iBigRankID = 0;
            sShowName = "";
            sLevelUpMovie = "";
            sLevelUpAudio = "";
            iID = 0;
            iSceneType = 0;
            sIconAB = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TTimeLimit_LP_Cacualte_Config_Client();
            copied.iRankID = this.iRankID;
            copied.sName = this.sName;
            copied.sTarget_MMR = this.sTarget_MMR;
            copied.sMaxSkillFactor_LPLoss = this.sMaxSkillFactor_LPLoss;
            copied.sMinSkillFactor_LPLoss = this.sMinSkillFactor_LPLoss;
            copied.sMaxSkillFactor_LPGain = this.sMaxSkillFactor_LPGain;
            copied.sMinSkillFactor_LPGain = this.sMinSkillFactor_LPGain;
            copied.iTierEdgeFlag = this.iTierEdgeFlag;
            copied.sIcon = this.sIcon;
            copied.sminiIcon = this.sminiIcon;
            copied.iBigRankID = this.iBigRankID;
            copied.sShowName = this.sShowName;
            copied.sLevelUpMovie = this.sLevelUpMovie;
            copied.sLevelUpAudio = this.sLevelUpAudio;
            copied.iID = this.iID;
            copied.iSceneType = this.iSceneType;
            copied.sIconAB = this.sIconAB;
            return copied;
        }
    }
}

