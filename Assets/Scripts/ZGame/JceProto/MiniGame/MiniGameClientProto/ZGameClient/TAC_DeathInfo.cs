// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_DeathInfo : Wup.Jce.JceStruct
    {
        public TAC_Entity entity {get; set;} 

        int _deathKind = 0;
        public int deathKind
        {
            get
            {
                 return _deathKind;
            }
            set
            {
                _deathKind = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(entity, 0);
            _os.Write(deathKind, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            entity = (TAC_Entity) _is.Read(entity, 0, false);

            deathKind = (int) _is.Read(deathKind, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(entity, "entity");
            _ds.Display(deathKind, "deathKind");
        }

        public override void Clear()
        {
            entity = null;
            deathKind = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_DeathInfo();
            copied.entity = (TAC_Entity)JceUtil.DeepClone(this.entity);
            copied.deathKind = this.deathKind;
            return copied;
        }
    }
}

