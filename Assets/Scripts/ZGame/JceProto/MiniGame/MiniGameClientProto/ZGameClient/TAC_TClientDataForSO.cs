// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TClientDataForSO : Wup.Jce.JceStruct
    {
        int _iAutoUpStatus = 0;
        public int iAutoUpStatus
        {
            get
            {
                 return _iAutoUpStatus;
            }
            set
            {
                _iAutoUpStatus = value; 
            }
        }

        int _iAutoWaitPromotionStatus = 0;
        public int iAutoWaitPromotionStatus
        {
            get
            {
                 return _iAutoWaitPromotionStatus;
            }
            set
            {
                _iAutoWaitPromotionStatus = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iAutoUpStatus, 0);
            _os.Write(iAutoWaitPromotionStatus, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iAutoUpStatus = (int) _is.Read(iAutoUpStatus, 0, false);

            iAutoWaitPromotionStatus = (int) _is.Read(iAutoWaitPromotionStatus, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iAutoUpStatus, "iAutoUpStatus");
            _ds.Display(iAutoWaitPromotionStatus, "iAutoWaitPromotionStatus");
        }

    }
}

