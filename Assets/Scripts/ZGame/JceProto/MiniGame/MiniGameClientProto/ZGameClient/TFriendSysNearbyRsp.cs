// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TFriendSysNearbyRsp : Wup.Jce.JceStruct
    {
        public int nResultID = 0;

        public System.Collections.Generic.List<TCommonFriendInfo> vec;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(nResultID, 0);
            _os.Write(vec, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            nResultID = (int) _is.Read(nResultID, 0, false);

            vec = (System.Collections.Generic.List<TCommonFriendInfo>) _is.Read(vec, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(nResultID, "nResultID");
            _ds.Display(vec, "vec");
        }

        public override void Clear()
        {
            nResultID = 0;
            vec = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TFriendSysNearbyRsp();
            copied.nResultID = this.nResultID;
            copied.vec = (System.Collections.Generic.List<TCommonFriendInfo>)JceUtil.DeepClone(this.vec);
            return copied;
        }
    }
}

