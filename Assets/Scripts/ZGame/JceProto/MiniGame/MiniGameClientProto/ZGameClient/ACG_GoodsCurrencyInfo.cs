// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_GoodsCurrencyInfo : Wup.Jce.JceStruct
    {
        public int iCurrencyId = 0;

        public int iCurrencyOldPrice = 0;

        public int iCurrencyNowPrice = 0;

        public int iCurrencyId2 = 0;

        public int iCurrencyOldPrice2 = 0;

        public int iCurrencyNowPrice2 = 0;

        public int iGiftCurrencyNowPrice = 0;

        public int iGiftCurrencyNowPrice2 = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iCurrencyId, 0);
            _os.Write(iCurrencyOldPrice, 1);
            _os.Write(iCurrencyNowPrice, 2);
            _os.Write(iCurrencyId2, 3);
            _os.Write(iCurrencyOldPrice2, 4);
            _os.Write(iCurrencyNowPrice2, 5);
            _os.Write(iGiftCurrencyNowPrice, 6);
            _os.Write(iGiftCurrencyNowPrice2, 7);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iCurrencyId = (int) _is.Read(iCurrencyId, 0, false);

            iCurrencyOldPrice = (int) _is.Read(iCurrencyOldPrice, 1, false);

            iCurrencyNowPrice = (int) _is.Read(iCurrencyNowPrice, 2, false);

            iCurrencyId2 = (int) _is.Read(iCurrencyId2, 3, false);

            iCurrencyOldPrice2 = (int) _is.Read(iCurrencyOldPrice2, 4, false);

            iCurrencyNowPrice2 = (int) _is.Read(iCurrencyNowPrice2, 5, false);

            iGiftCurrencyNowPrice = (int) _is.Read(iGiftCurrencyNowPrice, 6, false);

            iGiftCurrencyNowPrice2 = (int) _is.Read(iGiftCurrencyNowPrice2, 7, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iCurrencyId, "iCurrencyId");
            _ds.Display(iCurrencyOldPrice, "iCurrencyOldPrice");
            _ds.Display(iCurrencyNowPrice, "iCurrencyNowPrice");
            _ds.Display(iCurrencyId2, "iCurrencyId2");
            _ds.Display(iCurrencyOldPrice2, "iCurrencyOldPrice2");
            _ds.Display(iCurrencyNowPrice2, "iCurrencyNowPrice2");
            _ds.Display(iGiftCurrencyNowPrice, "iGiftCurrencyNowPrice");
            _ds.Display(iGiftCurrencyNowPrice2, "iGiftCurrencyNowPrice2");
        }

        public override void Clear()
        {
            iCurrencyId = 0;
            iCurrencyOldPrice = 0;
            iCurrencyNowPrice = 0;
            iCurrencyId2 = 0;
            iCurrencyOldPrice2 = 0;
            iCurrencyNowPrice2 = 0;
            iGiftCurrencyNowPrice = 0;
            iGiftCurrencyNowPrice2 = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_GoodsCurrencyInfo();
            copied.iCurrencyId = this.iCurrencyId;
            copied.iCurrencyOldPrice = this.iCurrencyOldPrice;
            copied.iCurrencyNowPrice = this.iCurrencyNowPrice;
            copied.iCurrencyId2 = this.iCurrencyId2;
            copied.iCurrencyOldPrice2 = this.iCurrencyOldPrice2;
            copied.iCurrencyNowPrice2 = this.iCurrencyNowPrice2;
            copied.iGiftCurrencyNowPrice = this.iGiftCurrencyNowPrice;
            copied.iGiftCurrencyNowPrice2 = this.iGiftCurrencyNowPrice2;
            return copied;
        }
    }
}

