// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class LockStepGameRecordDB : Wup.Jce.JceStruct
    {
        public long lRecordID = 0;

        public byte[] frames;

        public long frameIDEnd = 0;

        public TKFrame.TKDictionary<long, byte[]> savedSitdowns;

        public int fightLogicVersion = 0;

        public TKFrame.TKDictionary<long, long> roundToFrameID;

        public int compressMethod = 0;

        public TKFrame.TKDictionary<long, TKFrame.TKDictionary<int, TAC_MatchReportData>> reportData;

        public TKFrame.TKDictionary<long, string> roundToDataMd5;

        public TKFrame.TKDictionary<long, TAC_TLockstepPlayState> roundToPlayState;

        public bool containsGMOperation = false;

        public int gameLogicVersion = 0;

        public TKFrame.TKDictionary<int, BRGameResultRecord> brGameResultRecordMap;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(lRecordID, 0);
            _os.Write(frames, 1);
            _os.Write(frameIDEnd, 2);
            _os.Write(savedSitdowns, 3);
            _os.Write(fightLogicVersion, 4);
            _os.Write(roundToFrameID, 5);
            _os.Write(compressMethod, 6);
            _os.Write(reportData, 7);
            _os.Write(roundToDataMd5, 8);
            _os.Write(roundToPlayState, 9);
            _os.Write(containsGMOperation, 10);
            _os.Write(gameLogicVersion, 11);
            _os.Write(brGameResultRecordMap, 12);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            lRecordID = (long)_is.Read(lRecordID, 0, false);

            frames = (byte[])_is.Read(frames, 1, false);

            frameIDEnd = (long)_is.Read(frameIDEnd, 2, false);

            savedSitdowns = (TKFrame.TKDictionary<long, byte[]>)_is.Read(savedSitdowns, 3, false);

            fightLogicVersion = (int)_is.Read(fightLogicVersion, 4, false);

            roundToFrameID = (TKFrame.TKDictionary<long, long>)_is.Read(roundToFrameID, 5, false);

            compressMethod = (int)_is.Read(compressMethod, 6, false);

            reportData = (TKFrame.TKDictionary<long, TKFrame.TKDictionary<int, TAC_MatchReportData>>)_is.Read(reportData, 7, false);

            roundToDataMd5 = (TKFrame.TKDictionary<long, string>)_is.Read(roundToDataMd5, 8, false);

            roundToPlayState = (TKFrame.TKDictionary<long, TAC_TLockstepPlayState>)_is.Read(roundToPlayState, 9, false);

            containsGMOperation = (bool)_is.Read(containsGMOperation, 10, false);

            gameLogicVersion = (int)_is.Read(gameLogicVersion, 11, false);

            brGameResultRecordMap = (TKFrame.TKDictionary<int, BRGameResultRecord>)_is.Read(brGameResultRecordMap, 12, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(lRecordID, "lRecordID");
            _ds.Display(frames, "frames");
            _ds.Display(frameIDEnd, "frameIDEnd");
            _ds.Display(savedSitdowns, "savedSitdowns");
            _ds.Display(fightLogicVersion, "fightLogicVersion");
            _ds.Display(roundToFrameID, "roundToFrameID");
            _ds.Display(compressMethod, "compressMethod");
            _ds.Display(reportData, "reportData");
            _ds.Display(roundToDataMd5, "roundToDataMd5");
            _ds.Display(roundToPlayState, "roundToPlayState");
            _ds.Display(containsGMOperation, "containsGMOperation");
            _ds.Display(gameLogicVersion, "gameLogicVersion");
            _ds.Display(brGameResultRecordMap, "brGameResultRecordMap");
        }

        public override void Clear()
        {
            lRecordID = 0;
            frames = null;
            frameIDEnd = 0;
            savedSitdowns = null;
            fightLogicVersion = 0;
            roundToFrameID = null;
            compressMethod = 0;
            reportData = null;
            roundToDataMd5 = null;
            roundToPlayState = null;
            containsGMOperation = false;
            gameLogicVersion = 0;
            brGameResultRecordMap = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new LockStepGameRecordDB();
            copied.lRecordID = this.lRecordID;
            copied.frames = (byte[])JceUtil.DeepClone(this.frames);
            copied.frameIDEnd = this.frameIDEnd;
            copied.savedSitdowns = (TKFrame.TKDictionary<long, byte[]>)JceUtil.DeepClone(this.savedSitdowns);
            copied.fightLogicVersion = this.fightLogicVersion;
            copied.roundToFrameID = (TKFrame.TKDictionary<long, long>)JceUtil.DeepClone(this.roundToFrameID);
            copied.compressMethod = this.compressMethod;
            copied.reportData = (TKFrame.TKDictionary<long, TKFrame.TKDictionary<int, TAC_MatchReportData>>)JceUtil.DeepClone(this.reportData);
            copied.roundToDataMd5 = (TKFrame.TKDictionary<long, string>)JceUtil.DeepClone(this.roundToDataMd5);
            copied.roundToPlayState = (TKFrame.TKDictionary<long, TAC_TLockstepPlayState>)JceUtil.DeepClone(this.roundToPlayState);
            copied.containsGMOperation = this.containsGMOperation;
            copied.gameLogicVersion = this.gameLogicVersion;
            copied.brGameResultRecordMap = (TKFrame.TKDictionary<int, BRGameResultRecord>)JceUtil.DeepClone(this.brGameResultRecordMap);
            return copied;
        }
    }
}

