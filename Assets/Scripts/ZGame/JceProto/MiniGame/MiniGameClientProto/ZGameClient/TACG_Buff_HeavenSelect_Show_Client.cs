//所在的Excel 【ACG_HeavenSelectBuff.xlsm】
//**********************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_Buff_HeavenSelect_Show_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sName = "";

        public string sTitle = "";

        public string sDes = "";

        public string sPaint = "";

        public int iPrice = 0;

        public int iSellPrice = 0;

        public int iIsActiveShowPreviewEffect = 0;

        public int iPreviewEffectSelectTargetType = 0;

        public string sPreviewEffectSelectTargetParam = "";

        public int iPreviewEffectType = 0;

        public string sPreviewEffectParam = "";

        public int iPreviewEffectTime = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sName, 1);
            _os.Write(sTitle, 2);
            _os.Write(sDes, 3);
            _os.Write(sPaint, 4);
            _os.Write(iPrice, 5);
            _os.Write(iSellPrice, 6);
            _os.Write(iIsActiveShowPreviewEffect, 7);
            _os.Write(iPreviewEffectSelectTargetType, 8);
            _os.Write(sPreviewEffectSelectTargetParam, 9);
            _os.Write(iPreviewEffectType, 10);
            _os.Write(sPreviewEffectParam, 11);
            _os.Write(iPreviewEffectTime, 12);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sName = (string) _is.Read(sName, 1, false);

            sTitle = (string) _is.Read(sTitle, 2, false);

            sDes = (string) _is.Read(sDes, 3, false);

            sPaint = (string) _is.Read(sPaint, 4, false);

            iPrice = (int) _is.Read(iPrice, 5, false);

            iSellPrice = (int) _is.Read(iSellPrice, 6, false);

            iIsActiveShowPreviewEffect = (int) _is.Read(iIsActiveShowPreviewEffect, 7, false);

            iPreviewEffectSelectTargetType = (int) _is.Read(iPreviewEffectSelectTargetType, 8, false);

            sPreviewEffectSelectTargetParam = (string) _is.Read(sPreviewEffectSelectTargetParam, 9, false);

            iPreviewEffectType = (int) _is.Read(iPreviewEffectType, 10, false);

            sPreviewEffectParam = (string) _is.Read(sPreviewEffectParam, 11, false);

            iPreviewEffectTime = (int) _is.Read(iPreviewEffectTime, 12, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sName, "sName");
            _ds.Display(sTitle, "sTitle");
            _ds.Display(sDes, "sDes");
            _ds.Display(sPaint, "sPaint");
            _ds.Display(iPrice, "iPrice");
            _ds.Display(iSellPrice, "iSellPrice");
            _ds.Display(iIsActiveShowPreviewEffect, "iIsActiveShowPreviewEffect");
            _ds.Display(iPreviewEffectSelectTargetType, "iPreviewEffectSelectTargetType");
            _ds.Display(sPreviewEffectSelectTargetParam, "sPreviewEffectSelectTargetParam");
            _ds.Display(iPreviewEffectType, "iPreviewEffectType");
            _ds.Display(sPreviewEffectParam, "sPreviewEffectParam");
            _ds.Display(iPreviewEffectTime, "iPreviewEffectTime");
        }

        public override void Clear()
        {
            iID = 0;
            sName = "";
            sTitle = "";
            sDes = "";
            sPaint = "";
            iPrice = 0;
            iSellPrice = 0;
            iIsActiveShowPreviewEffect = 0;
            iPreviewEffectSelectTargetType = 0;
            sPreviewEffectSelectTargetParam = "";
            iPreviewEffectType = 0;
            sPreviewEffectParam = "";
            iPreviewEffectTime = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_Buff_HeavenSelect_Show_Client();
            copied.iID = this.iID;
            copied.sName = this.sName;
            copied.sTitle = this.sTitle;
            copied.sDes = this.sDes;
            copied.sPaint = this.sPaint;
            copied.iPrice = this.iPrice;
            copied.iSellPrice = this.iSellPrice;
            copied.iIsActiveShowPreviewEffect = this.iIsActiveShowPreviewEffect;
            copied.iPreviewEffectSelectTargetType = this.iPreviewEffectSelectTargetType;
            copied.sPreviewEffectSelectTargetParam = this.sPreviewEffectSelectTargetParam;
            copied.iPreviewEffectType = this.iPreviewEffectType;
            copied.sPreviewEffectParam = this.sPreviewEffectParam;
            copied.iPreviewEffectTime = this.iPreviewEffectTime;
            return copied;
        }
    }
}

