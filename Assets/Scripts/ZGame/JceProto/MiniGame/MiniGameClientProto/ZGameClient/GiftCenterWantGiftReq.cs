// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GiftCenterWantGiftReq : Wup.Jce.JceStruct
    {
        public TUserID stUserID;

        public int iGoodsID = 0;

        public int iGoodsNum = 1;

        public string sMessage = "";

        public string sMsdkToken = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(stUserID, 0);
            _os.Write(iGoodsID, 1);
            _os.Write(iGoodsNum, 2);
            _os.Write(sMessage, 3);
            _os.Write(sMsdkToken, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            stUserID = (TUserID) _is.Read(stUserID, 0, false);

            iGoodsID = (int) _is.Read(iGoodsID, 1, false);

            iGoodsNum = (int) _is.Read(iGoodsNum, 2, false);

            sMessage = (string) _is.Read(sMessage, 3, false);

            sMsdkToken = (string) _is.Read(sMsdkToken, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(stUserID, "stUserID");
            _ds.Display(iGoodsID, "iGoodsID");
            _ds.Display(iGoodsNum, "iGoodsNum");
            _ds.Display(sMessage, "sMessage");
            _ds.Display(sMsdkToken, "sMsdkToken");
        }

        public override void Clear()
        {
            stUserID = null;
            iGoodsID = 0;
            iGoodsNum = 1;
            sMessage = "";
            sMsdkToken = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GiftCenterWantGiftReq();
            copied.stUserID = (TUserID)JceUtil.DeepClone(this.stUserID);
            copied.iGoodsID = this.iGoodsID;
            copied.iGoodsNum = this.iGoodsNum;
            copied.sMessage = this.sMessage;
            copied.sMsdkToken = this.sMsdkToken;
            return copied;
        }
    }
}

