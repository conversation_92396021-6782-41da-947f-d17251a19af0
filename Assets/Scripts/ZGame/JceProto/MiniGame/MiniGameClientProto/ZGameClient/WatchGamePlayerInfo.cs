// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class WatchGamePlayerInfo : Wup.Jce.JceStruct
    {
        public TUserID uid;

        public string nickname = "";

        public string faceUrl = "";

        public int iconId = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(uid, 0);
            _os.Write(nickname, 1);
            _os.Write(faceUrl, 2);
            _os.Write(iconId, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            uid = (TUserID) _is.Read(uid, 0, false);

            nickname = (string) _is.Read(nickname, 1, false);

            faceUrl = (string) _is.Read(faceUrl, 2, false);

            iconId = (int) _is.Read(iconId, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(uid, "uid");
            _ds.Display(nickname, "nickname");
            _ds.Display(faceUrl, "faceUrl");
            _ds.Display(iconId, "iconId");
        }

        public override void Clear()
        {
            uid = null;
            nickname = "";
            faceUrl = "";
            iconId = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new WatchGamePlayerInfo();
            copied.uid = (TUserID)JceUtil.DeepClone(this.uid);
            copied.nickname = this.nickname;
            copied.faceUrl = this.faceUrl;
            copied.iconId = this.iconId;
            return copied;
        }
    }
}

