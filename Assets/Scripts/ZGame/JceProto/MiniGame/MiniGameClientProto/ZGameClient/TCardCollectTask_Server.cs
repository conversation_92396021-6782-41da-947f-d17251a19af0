// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TCardCollectTask_Server : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sTaskDesc = "";

        public string sCondition = "";

        public int iItemID1 = 0;

        public int iItemCount1 = 0;

        public int iItemID2 = 0;

        public int iItemCount2 = 0;

        public int iSetID = 0;

        public string sJumpParam = "";

        public string sProgressTarget = "";

        public int iTargetType = 0;

        public int iTaskTab = 0;

        public string sTitle = "";

        public int iItemDisplayWeight1 = 0;

        public int iItemDisplayWeight2 = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sTaskDesc, 2);
            _os.Write(sCondition, 3);
            _os.Write(iItemID1, 4);
            _os.Write(iItemCount1, 5);
            _os.Write(iItemID2, 6);
            _os.Write(iItemCount2, 7);
            _os.Write(iSetID, 8);
            _os.Write(sJumpParam, 9);
            _os.Write(sProgressTarget, 10);
            _os.Write(iTargetType, 11);
            _os.Write(iTaskTab, 12);
            _os.Write(sTitle, 13);
            _os.Write(iItemDisplayWeight1, 14);
            _os.Write(iItemDisplayWeight2, 15);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sTaskDesc = (string) _is.Read(sTaskDesc, 2, false);

            sCondition = (string) _is.Read(sCondition, 3, false);

            iItemID1 = (int) _is.Read(iItemID1, 4, false);

            iItemCount1 = (int) _is.Read(iItemCount1, 5, false);

            iItemID2 = (int) _is.Read(iItemID2, 6, false);

            iItemCount2 = (int) _is.Read(iItemCount2, 7, false);

            iSetID = (int) _is.Read(iSetID, 8, false);

            sJumpParam = (string) _is.Read(sJumpParam, 9, false);

            sProgressTarget = (string) _is.Read(sProgressTarget, 10, false);

            iTargetType = (int) _is.Read(iTargetType, 11, false);

            iTaskTab = (int) _is.Read(iTaskTab, 12, false);

            sTitle = (string) _is.Read(sTitle, 13, false);

            iItemDisplayWeight1 = (int) _is.Read(iItemDisplayWeight1, 14, false);

            iItemDisplayWeight2 = (int) _is.Read(iItemDisplayWeight2, 15, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sTaskDesc, "sTaskDesc");
            _ds.Display(sCondition, "sCondition");
            _ds.Display(iItemID1, "iItemID1");
            _ds.Display(iItemCount1, "iItemCount1");
            _ds.Display(iItemID2, "iItemID2");
            _ds.Display(iItemCount2, "iItemCount2");
            _ds.Display(iSetID, "iSetID");
            _ds.Display(sJumpParam, "sJumpParam");
            _ds.Display(sProgressTarget, "sProgressTarget");
            _ds.Display(iTargetType, "iTargetType");
            _ds.Display(iTaskTab, "iTaskTab");
            _ds.Display(sTitle, "sTitle");
            _ds.Display(iItemDisplayWeight1, "iItemDisplayWeight1");
            _ds.Display(iItemDisplayWeight2, "iItemDisplayWeight2");
        }

        public override void Clear()
        {
            iID = 0;
            sTaskDesc = "";
            sCondition = "";
            iItemID1 = 0;
            iItemCount1 = 0;
            iItemID2 = 0;
            iItemCount2 = 0;
            iSetID = 0;
            sJumpParam = "";
            sProgressTarget = "";
            iTargetType = 0;
            iTaskTab = 0;
            sTitle = "";
            iItemDisplayWeight1 = 0;
            iItemDisplayWeight2 = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TCardCollectTask_Server();
            copied.iID = this.iID;
            copied.sTaskDesc = this.sTaskDesc;
            copied.sCondition = this.sCondition;
            copied.iItemID1 = this.iItemID1;
            copied.iItemCount1 = this.iItemCount1;
            copied.iItemID2 = this.iItemID2;
            copied.iItemCount2 = this.iItemCount2;
            copied.iSetID = this.iSetID;
            copied.sJumpParam = this.sJumpParam;
            copied.sProgressTarget = this.sProgressTarget;
            copied.iTargetType = this.iTargetType;
            copied.iTaskTab = this.iTaskTab;
            copied.sTitle = this.sTitle;
            copied.iItemDisplayWeight1 = this.iItemDisplayWeight1;
            copied.iItemDisplayWeight2 = this.iItemDisplayWeight2;
            return copied;
        }
    }
}

