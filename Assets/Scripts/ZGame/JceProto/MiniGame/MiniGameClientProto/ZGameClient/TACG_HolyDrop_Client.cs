//所在的Excel 【ACG_HundredReward.xlsm】
//*************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_HolyDrop_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iColumn = 0;

        public int iRow = 0;

        public int iLifeLimited = 0;

        public int iTurnCountLimited = 0;

        public int iSameDrop = 0;

        public string sCondition = "";

        public string sDropPool = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iColumn, 1);
            _os.Write(iRow, 2);
            _os.Write(iLifeLimited, 3);
            _os.Write(iTurnCountLimited, 4);
            _os.Write(iSameDrop, 5);
            _os.Write(sCondition, 6);
            _os.Write(sDropPool, 7);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iColumn = (int) _is.Read(iColumn, 1, false);

            iRow = (int) _is.Read(iRow, 2, false);

            iLifeLimited = (int) _is.Read(iLifeLimited, 3, false);

            iTurnCountLimited = (int) _is.Read(iTurnCountLimited, 4, false);

            iSameDrop = (int) _is.Read(iSameDrop, 5, false);

            sCondition = (string) _is.Read(sCondition, 6, false);

            sDropPool = (string) _is.Read(sDropPool, 7, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iColumn, "iColumn");
            _ds.Display(iRow, "iRow");
            _ds.Display(iLifeLimited, "iLifeLimited");
            _ds.Display(iTurnCountLimited, "iTurnCountLimited");
            _ds.Display(iSameDrop, "iSameDrop");
            _ds.Display(sCondition, "sCondition");
            _ds.Display(sDropPool, "sDropPool");
        }

        public override void Clear()
        {
            iID = 0;
            iColumn = 0;
            iRow = 0;
            iLifeLimited = 0;
            iTurnCountLimited = 0;
            iSameDrop = 0;
            sCondition = "";
            sDropPool = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_HolyDrop_Client();
            copied.iID = this.iID;
            copied.iColumn = this.iColumn;
            copied.iRow = this.iRow;
            copied.iLifeLimited = this.iLifeLimited;
            copied.iTurnCountLimited = this.iTurnCountLimited;
            copied.iSameDrop = this.iSameDrop;
            copied.sCondition = this.sCondition;
            copied.sDropPool = this.sDropPool;
            return copied;
        }
    }
}

