// **********************************************************************
// This file was generated by a TAF parser!
// Generated from `SGameDBConfigProto.jce'
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_SegmentRatingBaseEvent_Server : Wup.Jce.JceStruct
    {
        int _iEventID = 0;
        public int iEventID
        {
            get
            {
                 return _iEventID;
            }
            set
            {
                _iEventID = value; 
            }
        }

        int _iCount = 0;
        public int iCount
        {
            get
            {
                 return _iCount;
            }
            set
            {
                _iCount = value; 
            }
        }

        int _iRating = 0;
        public int iRating
        {
            get
            {
                 return _iRating;
            }
            set
            {
                _iRating = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iEventID, 0);
            _os.Write(iCount, 1);
            _os.Write(iRating, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iEventID = (int) _is.Read(iEventID, 0, false);

            iCount = (int) _is.Read(iCount, 1, false);

            iRating = (int) _is.Read(iRating, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iEventID, "iEventID");
            _ds.Display(iCount, "iCount");
            _ds.Display(iRating, "iRating");
        }

    }
}

