// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_TMallPageInfoRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public System.Collections.Generic.List<ACG_MallPageInfo> vecMallPageInfo;

        public TKFrame.TKDictionary<int, TMallJump_Server> mapJumpInfo;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(vecMallPageInfo, 1);
            _os.Write(mapJumpInfo, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            vecMallPageInfo = (System.Collections.Generic.List<ACG_MallPageInfo>) _is.Read(vecMallPageInfo, 1, false);

            mapJumpInfo = (TKFrame.TKDictionary<int, TMallJump_Server>) _is.Read(mapJumpInfo, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(vecMallPageInfo, "vecMallPageInfo");
            _ds.Display(mapJumpInfo, "mapJumpInfo");
        }

        public override void Clear()
        {
            iRet = 0;
            vecMallPageInfo = null;
            mapJumpInfo = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_TMallPageInfoRsp();
            copied.iRet = this.iRet;
            copied.vecMallPageInfo = (System.Collections.Generic.List<ACG_MallPageInfo>)JceUtil.DeepClone(this.vecMallPageInfo);
            copied.mapJumpInfo = (TKFrame.TKDictionary<int, TMallJump_Server>)JceUtil.DeepClone(this.mapJumpInfo);
            return copied;
        }
    }
}

