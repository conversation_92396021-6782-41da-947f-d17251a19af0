// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_RoundInfoBestData : Wup.Jce.JceStruct
    {
        int _chairId = 0;
        public int chairId
        {
            get
            {
                 return _chairId;
            }
            set
            {
                _chairId = value; 
            }
        }

        int _num = 0;
        public int num
        {
            get
            {
                 return _num;
            }
            set
            {
                _num = value; 
            }
        }

        int _life = 0;
        public int life
        {
            get
            {
                 return _life;
            }
            set
            {
                _life = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(chairId, 0);
            _os.Write(num, 1);
            _os.Write(life, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            chairId = (int) _is.Read(chairId, 0, false);

            num = (int) _is.Read(num, 1, false);

            life = (int) _is.Read(life, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(chairId, "chairId");
            _ds.Display(num, "num");
            _ds.Display(life, "life");
        }

        public override void Clear()
        {
            chairId = 0;
            num = 0;
            life = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_RoundInfoBestData();
            copied.chairId = this.chairId;
            copied.num = this.num;
            copied.life = this.life;
            return copied;
        }
    }
}

