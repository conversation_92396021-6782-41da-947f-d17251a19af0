// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class PveLevelDetail : Wup.Jce.JceStruct
    {
        public TBCRoguelikeLevel_Server levelConf;

        public bool isSucc = false;

        public PveLineupDetail enemyLineup;

        public int levelID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(levelConf, 0);
            _os.Write(isSucc, 1);
            _os.Write(enemyLineup, 2);
            _os.Write(levelID, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            levelConf = (TBCRoguelikeLevel_Server) _is.Read(levelConf, 0, false);

            isSucc = (bool) _is.Read(isSucc, 1, false);

            enemyLineup = (PveLineupDetail) _is.Read(enemyLineup, 2, false);

            levelID = (int) _is.Read(levelID, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(levelConf, "levelConf");
            _ds.Display(isSucc, "isSucc");
            _ds.Display(enemyLineup, "enemyLineup");
            _ds.Display(levelID, "levelID");
        }

        public override void Clear()
        {
            levelConf = null;
            isSucc = false;
            enemyLineup = null;
            levelID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new PveLevelDetail();
            copied.levelConf = (TBCRoguelikeLevel_Server)JceUtil.DeepClone(this.levelConf);
            copied.isSucc = this.isSucc;
            copied.enemyLineup = (PveLineupDetail)JceUtil.DeepClone(this.enemyLineup);
            copied.levelID = this.levelID;
            return copied;
        }
    }
}

