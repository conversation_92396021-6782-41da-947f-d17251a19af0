// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_BGEditor_AddHero : Wup.Jce.JceStruct
    {
        int _heroID = 0;
        public int heroID
        {
            get
            {
                 return _heroID;
            }
            set
            {
                _heroID = value; 
            }
        }

        int _entityID = 0;
        public int entityID
        {
            get
            {
                 return _entityID;
            }
            set
            {
                _entityID = value; 
            }
        }

        public TAC_CellPos pos {get; set;} 

        int _team = 0;
        public int team
        {
            get
            {
                 return _team;
            }
            set
            {
                _team = value; 
            }
        }

        public System.Collections.Generic.List<int> disabledComponents {get; set;} 

        int _maxHP = 0;
        public int maxHP
        {
            get
            {
                 return _maxHP;
            }
            set
            {
                _maxHP = value; 
            }
        }

        public System.Collections.Generic.List<string> selectorParams {get; set;} 

        public System.Collections.Generic.List<string> triggerParams {get; set;} 

        public TKFrame.TKDictionary<int, System.Collections.Generic.List<string>> effectorParams {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(heroID, 0);
            _os.Write(entityID, 1);
            _os.Write(pos, 2);
            _os.Write(team, 3);
            _os.Write(disabledComponents, 4);
            _os.Write(maxHP, 5);
            _os.Write(selectorParams, 6);
            _os.Write(triggerParams, 7);
            _os.Write(effectorParams, 8);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            heroID = (int) _is.Read(heroID, 0, false);

            entityID = (int) _is.Read(entityID, 1, false);

            pos = (TAC_CellPos) _is.Read(pos, 2, false);

            team = (int) _is.Read(team, 3, false);

            disabledComponents = (System.Collections.Generic.List<int>) _is.Read(disabledComponents, 4, false);

            maxHP = (int) _is.Read(maxHP, 5, false);

            selectorParams = (System.Collections.Generic.List<string>) _is.Read(selectorParams, 6, false);

            triggerParams = (System.Collections.Generic.List<string>) _is.Read(triggerParams, 7, false);

            effectorParams = (TKFrame.TKDictionary<int, System.Collections.Generic.List<string>>) _is.Read(effectorParams, 8, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(heroID, "heroID");
            _ds.Display(entityID, "entityID");
            _ds.Display(pos, "pos");
            _ds.Display(team, "team");
            _ds.Display(disabledComponents, "disabledComponents");
            _ds.Display(maxHP, "maxHP");
            _ds.Display(selectorParams, "selectorParams");
            _ds.Display(triggerParams, "triggerParams");
            _ds.Display(effectorParams, "effectorParams");
        }

        public override void Clear()
        {
            heroID = 0;
            entityID = 0;
            pos = null;
            team = 0;
            disabledComponents = null;
            maxHP = 0;
            selectorParams = null;
            triggerParams = null;
            effectorParams = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_BGEditor_AddHero();
            copied.heroID = this.heroID;
            copied.entityID = this.entityID;
            copied.pos = (TAC_CellPos)JceUtil.DeepClone(this.pos);
            copied.team = this.team;
            copied.disabledComponents = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.disabledComponents);
            copied.maxHP = this.maxHP;
            copied.selectorParams = (System.Collections.Generic.List<string>)JceUtil.DeepClone(this.selectorParams);
            copied.triggerParams = (System.Collections.Generic.List<string>)JceUtil.DeepClone(this.triggerParams);
            copied.effectorParams = (TKFrame.TKDictionary<int, System.Collections.Generic.List<string>>)JceUtil.DeepClone(this.effectorParams);
            return copied;
        }
    }
}

