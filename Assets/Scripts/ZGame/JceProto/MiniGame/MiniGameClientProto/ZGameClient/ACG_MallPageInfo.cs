// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_MallPageInfo : Wup.Jce.JceStruct
    {
        public int iId = 0;

        public string sPageName = "";

        public int iParentId = 0;

        public int iDisplayStartUnixTime = 0;

        public int iDisplayEndUnixTime = 0;

        public int iPurchaseStartUnixTime = 0;

        public int iPurchaseEndUnixTime = 0;

        public int iTokenId = 0;

        public int iPanelType = 0;

        public string sCurrencyItem = "";

        public int iHideOnMainUI = 0;

        public int iSort = 0;

        public int iRedDotMark = 0;

        public int iHideOnGiftUI = 0;

        public int iBindRedPointValue = 0;

        public string sGiftCurrencyItem = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iId, 0);
            _os.Write(sPageName, 1);
            _os.Write(iParentId, 2);
            _os.Write(iDisplayStartUnixTime, 3);
            _os.Write(iDisplayEndUnixTime, 4);
            _os.Write(iPurchaseStartUnixTime, 5);
            _os.Write(iPurchaseEndUnixTime, 6);
            _os.Write(iTokenId, 7);
            _os.Write(iPanelType, 8);
            _os.Write(sCurrencyItem, 9);
            _os.Write(iHideOnMainUI, 10);
            _os.Write(iSort, 11);
            _os.Write(iRedDotMark, 12);
            _os.Write(iHideOnGiftUI, 13);
            _os.Write(iBindRedPointValue, 14);
            _os.Write(sGiftCurrencyItem, 15);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iId = (int) _is.Read(iId, 0, false);

            sPageName = (string) _is.Read(sPageName, 1, false);

            iParentId = (int) _is.Read(iParentId, 2, false);

            iDisplayStartUnixTime = (int) _is.Read(iDisplayStartUnixTime, 3, false);

            iDisplayEndUnixTime = (int) _is.Read(iDisplayEndUnixTime, 4, false);

            iPurchaseStartUnixTime = (int) _is.Read(iPurchaseStartUnixTime, 5, false);

            iPurchaseEndUnixTime = (int) _is.Read(iPurchaseEndUnixTime, 6, false);

            iTokenId = (int) _is.Read(iTokenId, 7, false);

            iPanelType = (int) _is.Read(iPanelType, 8, false);

            sCurrencyItem = (string) _is.Read(sCurrencyItem, 9, false);

            iHideOnMainUI = (int) _is.Read(iHideOnMainUI, 10, false);

            iSort = (int) _is.Read(iSort, 11, false);

            iRedDotMark = (int) _is.Read(iRedDotMark, 12, false);

            iHideOnGiftUI = (int) _is.Read(iHideOnGiftUI, 13, false);

            iBindRedPointValue = (int) _is.Read(iBindRedPointValue, 14, false);

            sGiftCurrencyItem = (string) _is.Read(sGiftCurrencyItem, 15, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iId, "iId");
            _ds.Display(sPageName, "sPageName");
            _ds.Display(iParentId, "iParentId");
            _ds.Display(iDisplayStartUnixTime, "iDisplayStartUnixTime");
            _ds.Display(iDisplayEndUnixTime, "iDisplayEndUnixTime");
            _ds.Display(iPurchaseStartUnixTime, "iPurchaseStartUnixTime");
            _ds.Display(iPurchaseEndUnixTime, "iPurchaseEndUnixTime");
            _ds.Display(iTokenId, "iTokenId");
            _ds.Display(iPanelType, "iPanelType");
            _ds.Display(sCurrencyItem, "sCurrencyItem");
            _ds.Display(iHideOnMainUI, "iHideOnMainUI");
            _ds.Display(iSort, "iSort");
            _ds.Display(iRedDotMark, "iRedDotMark");
            _ds.Display(iHideOnGiftUI, "iHideOnGiftUI");
            _ds.Display(iBindRedPointValue, "iBindRedPointValue");
            _ds.Display(sGiftCurrencyItem, "sGiftCurrencyItem");
        }

        public override void Clear()
        {
            iId = 0;
            sPageName = "";
            iParentId = 0;
            iDisplayStartUnixTime = 0;
            iDisplayEndUnixTime = 0;
            iPurchaseStartUnixTime = 0;
            iPurchaseEndUnixTime = 0;
            iTokenId = 0;
            iPanelType = 0;
            sCurrencyItem = "";
            iHideOnMainUI = 0;
            iSort = 0;
            iRedDotMark = 0;
            iHideOnGiftUI = 0;
            iBindRedPointValue = 0;
            sGiftCurrencyItem = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_MallPageInfo();
            copied.iId = this.iId;
            copied.sPageName = this.sPageName;
            copied.iParentId = this.iParentId;
            copied.iDisplayStartUnixTime = this.iDisplayStartUnixTime;
            copied.iDisplayEndUnixTime = this.iDisplayEndUnixTime;
            copied.iPurchaseStartUnixTime = this.iPurchaseStartUnixTime;
            copied.iPurchaseEndUnixTime = this.iPurchaseEndUnixTime;
            copied.iTokenId = this.iTokenId;
            copied.iPanelType = this.iPanelType;
            copied.sCurrencyItem = this.sCurrencyItem;
            copied.iHideOnMainUI = this.iHideOnMainUI;
            copied.iSort = this.iSort;
            copied.iRedDotMark = this.iRedDotMark;
            copied.iHideOnGiftUI = this.iHideOnGiftUI;
            copied.iBindRedPointValue = this.iBindRedPointValue;
            copied.sGiftCurrencyItem = this.sGiftCurrencyItem;
            return copied;
        }
    }
}

