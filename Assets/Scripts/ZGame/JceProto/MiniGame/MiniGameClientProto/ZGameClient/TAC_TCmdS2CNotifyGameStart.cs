// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TCmdS2CNotifyGameStart : Wup.Jce.JceStruct
    {
        int _i8ChairID = 0;
        public int i8ChairID
        {
            get
            {
                 return _i8ChairID;
            }
            set
            {
                _i8ChairID = value; 
            }
        }

        int _iGalaxyID = 0;
        public int iGalaxyID
        {
            get
            {
                 return _iGalaxyID;
            }
            set
            {
                _iGalaxyID = value; 
            }
        }

        public System.Collections.Generic.List<TAC_UserMapInfo> vecUserMapInfo {get; set;} 

        public System.Collections.Generic.List<int> vecRecruitPriceTimes {get; set;} 

        int _iAutoUpStatus = 0;
        public int iAutoUpStatus
        {
            get
            {
                 return _iAutoUpStatus;
            }
            set
            {
                _iAutoUpStatus = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(i8ChairID, 0);
            _os.Write(iGalaxyID, 1);
            _os.Write(vecUserMapInfo, 2);
            _os.Write(vecRecruitPriceTimes, 3);
            _os.Write(iAutoUpStatus, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            i8ChairID = (int) _is.Read(i8ChairID, 0, false);

            iGalaxyID = (int) _is.Read(iGalaxyID, 1, false);

            vecUserMapInfo = (System.Collections.Generic.List<TAC_UserMapInfo>) _is.Read(vecUserMapInfo, 2, false);

            vecRecruitPriceTimes = (System.Collections.Generic.List<int>) _is.Read(vecRecruitPriceTimes, 3, false);

            iAutoUpStatus = (int) _is.Read(iAutoUpStatus, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(i8ChairID, "i8ChairID");
            _ds.Display(iGalaxyID, "iGalaxyID");
            _ds.Display(vecUserMapInfo, "vecUserMapInfo");
            _ds.Display(vecRecruitPriceTimes, "vecRecruitPriceTimes");
            _ds.Display(iAutoUpStatus, "iAutoUpStatus");
        }

        public override void Clear()
        {
            i8ChairID = 0;
            iGalaxyID = 0;
            vecUserMapInfo = null;
            vecRecruitPriceTimes = null;
            iAutoUpStatus = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_TCmdS2CNotifyGameStart();
            copied.i8ChairID = this.i8ChairID;
            copied.iGalaxyID = this.iGalaxyID;
            copied.vecUserMapInfo = (System.Collections.Generic.List<TAC_UserMapInfo>)JceUtil.DeepClone(this.vecUserMapInfo);
            copied.vecRecruitPriceTimes = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.vecRecruitPriceTimes);
            copied.iAutoUpStatus = this.iAutoUpStatus;
            return copied;
        }
    }
}

