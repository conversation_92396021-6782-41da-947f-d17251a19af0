// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class JOCGetQualifierResp : Wup.Jce.JceStruct
    {
        public int ret = 0;

        public System.Collections.Generic.List<JOCQualifierData> qualifierData;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(ret, 0);
            _os.Write(qualifierData, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            ret = (int) _is.Read(ret, 0, false);

            qualifierData = (System.Collections.Generic.List<JOCQualifierData>) _is.Read(qualifierData, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(ret, "ret");
            _ds.Display(qualifierData, "qualifierData");
        }

        public override void Clear()
        {
            ret = 0;
            qualifierData = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new JOCGetQualifierResp();
            copied.ret = this.ret;
            copied.qualifierData = (System.Collections.Generic.List<JOCQualifierData>)JceUtil.DeepClone(this.qualifierData);
            return copied;
        }
    }
}

