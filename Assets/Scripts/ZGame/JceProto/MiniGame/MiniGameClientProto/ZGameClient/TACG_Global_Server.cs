// **********************************************************************
// This file was generated by a TAF parser!
// Generated from `SGameDBConfigProto.jce'
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_Global_Server : Wup.Jce.JceStruct
    {
        int _iID = 0;
        public int iID
        {
            get
            {
                 return _iID;
            }
            set
            {
                _iID = value; 
            }
        }

        string _sParam1 = "";
        public string sParam1
        {
            get
            {
                 return _sParam1;
            }
            set
            {
                _sParam1 = value; 
            }
        }

        string _sParam2 = "";
        public string sParam2
        {
            get
            {
                 return _sParam2;
            }
            set
            {
                _sParam2 = value; 
            }
        }

        string _sParam3 = "";
        public string sParam3
        {
            get
            {
                 return _sParam3;
            }
            set
            {
                _sParam3 = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sParam1, 1);
            _os.Write(sParam2, 2);
            _os.Write(sParam3, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sParam1 = (string) _is.Read(sParam1, 1, false);

            sParam2 = (string) _is.Read(sParam2, 2, false);

            sParam3 = (string) _is.Read(sParam3, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sParam1, "sParam1");
            _ds.Display(sParam2, "sParam2");
            _ds.Display(sParam3, "sParam3");
        }

    }
}

