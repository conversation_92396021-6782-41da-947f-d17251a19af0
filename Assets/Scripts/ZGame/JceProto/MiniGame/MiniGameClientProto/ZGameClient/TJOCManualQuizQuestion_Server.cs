// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TJOCManualQuizQuestion_Server : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iManualID = 0;

        public string sDesc = "";

        public int iPageID = 0;

        public string sPageDesc = "";

        public int iPageWeight = 0;

        public int iItemID = 0;

        public int iItemNum = 0;

        public int iIsNeedAnswerCfg = 0;

        public string sCorrectAnswerIDs = "";

        public int iCorrectAnswerCount = 0;

        public string sBeginTime = "";

        public string sEndTime = "";

        public string sAnnounceTime = "";

        public int iBeginTime = 0;

        public int iEndTime = 0;

        public int iAnnounceTime = 0;

        public int iMailID = 0;

        public int iMailParamDynamic = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iManualID, 1);
            _os.Write(sDesc, 2);
            _os.Write(iPageID, 3);
            _os.Write(sPageDesc, 4);
            _os.Write(iPageWeight, 5);
            _os.Write(iItemID, 6);
            _os.Write(iItemNum, 7);
            _os.Write(iIsNeedAnswerCfg, 8);
            _os.Write(sCorrectAnswerIDs, 9);
            _os.Write(iCorrectAnswerCount, 10);
            _os.Write(sBeginTime, 11);
            _os.Write(sEndTime, 12);
            _os.Write(sAnnounceTime, 13);
            _os.Write(iBeginTime, 14);
            _os.Write(iEndTime, 15);
            _os.Write(iAnnounceTime, 16);
            _os.Write(iMailID, 17);
            _os.Write(iMailParamDynamic, 18);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iManualID = (int) _is.Read(iManualID, 1, false);

            sDesc = (string) _is.Read(sDesc, 2, false);

            iPageID = (int) _is.Read(iPageID, 3, false);

            sPageDesc = (string) _is.Read(sPageDesc, 4, false);

            iPageWeight = (int) _is.Read(iPageWeight, 5, false);

            iItemID = (int) _is.Read(iItemID, 6, false);

            iItemNum = (int) _is.Read(iItemNum, 7, false);

            iIsNeedAnswerCfg = (int) _is.Read(iIsNeedAnswerCfg, 8, false);

            sCorrectAnswerIDs = (string) _is.Read(sCorrectAnswerIDs, 9, false);

            iCorrectAnswerCount = (int) _is.Read(iCorrectAnswerCount, 10, false);

            sBeginTime = (string) _is.Read(sBeginTime, 11, false);

            sEndTime = (string) _is.Read(sEndTime, 12, false);

            sAnnounceTime = (string) _is.Read(sAnnounceTime, 13, false);

            iBeginTime = (int) _is.Read(iBeginTime, 14, false);

            iEndTime = (int) _is.Read(iEndTime, 15, false);

            iAnnounceTime = (int) _is.Read(iAnnounceTime, 16, false);

            iMailID = (int) _is.Read(iMailID, 17, false);

            iMailParamDynamic = (int) _is.Read(iMailParamDynamic, 18, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iManualID, "iManualID");
            _ds.Display(sDesc, "sDesc");
            _ds.Display(iPageID, "iPageID");
            _ds.Display(sPageDesc, "sPageDesc");
            _ds.Display(iPageWeight, "iPageWeight");
            _ds.Display(iItemID, "iItemID");
            _ds.Display(iItemNum, "iItemNum");
            _ds.Display(iIsNeedAnswerCfg, "iIsNeedAnswerCfg");
            _ds.Display(sCorrectAnswerIDs, "sCorrectAnswerIDs");
            _ds.Display(iCorrectAnswerCount, "iCorrectAnswerCount");
            _ds.Display(sBeginTime, "sBeginTime");
            _ds.Display(sEndTime, "sEndTime");
            _ds.Display(sAnnounceTime, "sAnnounceTime");
            _ds.Display(iBeginTime, "iBeginTime");
            _ds.Display(iEndTime, "iEndTime");
            _ds.Display(iAnnounceTime, "iAnnounceTime");
            _ds.Display(iMailID, "iMailID");
            _ds.Display(iMailParamDynamic, "iMailParamDynamic");
        }

        public override void Clear()
        {
            iID = 0;
            iManualID = 0;
            sDesc = "";
            iPageID = 0;
            sPageDesc = "";
            iPageWeight = 0;
            iItemID = 0;
            iItemNum = 0;
            iIsNeedAnswerCfg = 0;
            sCorrectAnswerIDs = "";
            iCorrectAnswerCount = 0;
            sBeginTime = "";
            sEndTime = "";
            sAnnounceTime = "";
            iBeginTime = 0;
            iEndTime = 0;
            iAnnounceTime = 0;
            iMailID = 0;
            iMailParamDynamic = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TJOCManualQuizQuestion_Server();
            copied.iID = this.iID;
            copied.iManualID = this.iManualID;
            copied.sDesc = this.sDesc;
            copied.iPageID = this.iPageID;
            copied.sPageDesc = this.sPageDesc;
            copied.iPageWeight = this.iPageWeight;
            copied.iItemID = this.iItemID;
            copied.iItemNum = this.iItemNum;
            copied.iIsNeedAnswerCfg = this.iIsNeedAnswerCfg;
            copied.sCorrectAnswerIDs = this.sCorrectAnswerIDs;
            copied.iCorrectAnswerCount = this.iCorrectAnswerCount;
            copied.sBeginTime = this.sBeginTime;
            copied.sEndTime = this.sEndTime;
            copied.sAnnounceTime = this.sAnnounceTime;
            copied.iBeginTime = this.iBeginTime;
            copied.iEndTime = this.iEndTime;
            copied.iAnnounceTime = this.iAnnounceTime;
            copied.iMailID = this.iMailID;
            copied.iMailParamDynamic = this.iMailParamDynamic;
            return copied;
        }
    }
}

