// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class CheckUserConditionRsp : Wup.Jce.JceStruct
    {
        public bool isEnableDeactivateAccount = true;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(isEnableDeactivateAccount, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            isEnableDeactivateAccount = (bool) _is.Read(isEnableDeactivateAccount, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(isEnableDeactivateAccount, "isEnableDeactivateAccount");
        }

        public override void Clear()
        {
            isEnableDeactivateAccount = true;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new CheckUserConditionRsp();
            copied.isEnableDeactivateAccount = this.isEnableDeactivateAccount;
            return copied;
        }
    }
}

