// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class PrivilegeGoodInfo : Wup.Jce.JceStruct
    {
        public ACG_MallGoodsInfo goods;

        public int discount = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(goods, 0);
            _os.Write(discount, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            goods = (ACG_MallGoodsInfo) _is.Read(goods, 0, false);

            discount = (int) _is.Read(discount, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(goods, "goods");
            _ds.Display(discount, "discount");
        }

        public override void Clear()
        {
            goods = null;
            discount = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new PrivilegeGoodInfo();
            copied.goods = (ACG_MallGoodsInfo)JceUtil.DeepClone(this.goods);
            copied.discount = this.discount;
            return copied;
        }
    }
}

