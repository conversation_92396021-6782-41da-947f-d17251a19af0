// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGetAnswerQuestionRewardRsp : Wup.Jce.JceStruct
    {
        public int iResult = 0;

        public int iAQId = 0;

        public System.Collections.Generic.List<TItemInfo> vecItem;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iResult, 0);
            _os.Write(iAQId, 1);
            _os.Write(vecItem, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iResult = (int) _is.Read(iResult, 0, false);

            iAQId = (int) _is.Read(iAQId, 1, false);

            vecItem = (System.Collections.Generic.List<TItemInfo>) _is.Read(vecItem, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iResult, "iResult");
            _ds.Display(iAQId, "iAQId");
            _ds.Display(vecItem, "vecItem");
        }

        public override void Clear()
        {
            iResult = 0;
            iAQId = 0;
            vecItem = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGetAnswerQuestionRewardRsp();
            copied.iResult = this.iResult;
            copied.iAQId = this.iAQId;
            copied.vecItem = (System.Collections.Generic.List<TItemInfo>)JceUtil.DeepClone(this.vecItem);
            return copied;
        }
    }
}

