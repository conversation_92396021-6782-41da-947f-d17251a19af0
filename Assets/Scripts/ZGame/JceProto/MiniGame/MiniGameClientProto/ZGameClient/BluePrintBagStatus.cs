// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class BluePrintBagStatus : Wup.Jce.JceStruct
    {
        int _index = 0;
        public int index
        {
            get
            {
                 return _index;
            }
            set
            {
                _index = value; 
            }
        }

        int _count = 0;
        public int count
        {
            get
            {
                 return _count;
            }
            set
            {
                _count = value; 
            }
        }

        int _poolID = 0;
        public int poolID
        {
            get
            {
                 return _poolID;
            }
            set
            {
                _poolID = value; 
            }
        }

        int _status = 0;
        public int status
        {
            get
            {
                 return _status;
            }
            set
            {
                _status = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(index, 0);
            _os.Write(count, 1);
            _os.Write(poolID, 2);
            _os.Write(status, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            index = (int) _is.Read(index, 0, false);

            count = (int) _is.Read(count, 1, false);

            poolID = (int) _is.Read(poolID, 2, false);

            status = (int) _is.Read(status, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(index, "index");
            _ds.Display(count, "count");
            _ds.Display(poolID, "poolID");
            _ds.Display(status, "status");
        }

        public override void Clear()
        {
            index = 0;
            count = 0;
            poolID = 0;
            status = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new BluePrintBagStatus();
            copied.index = this.index;
            copied.count = this.count;
            copied.poolID = this.poolID;
            copied.status = this.status;
            return copied;
        }
    }
}

