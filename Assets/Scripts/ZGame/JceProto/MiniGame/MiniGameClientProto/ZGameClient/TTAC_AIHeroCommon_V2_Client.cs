// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_AIHeroCommon_V2_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iSetID = 0;

        public int iGroupID = 0;

        public int iPower = 0;

        public int iPos = 0;

        public int iPosType = 0;

        public string sType = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iSetID, 1);
            _os.Write(iGroupID, 2);
            _os.Write(iPower, 4);
            _os.Write(iPos, 5);
            _os.Write(iPosType, 6);
            _os.Write(sType, 7);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iSetID = (int) _is.Read(iSetID, 1, false);

            iGroupID = (int) _is.Read(iGroupID, 2, false);

            iPower = (int) _is.Read(iPower, 4, false);

            iPos = (int) _is.Read(iPos, 5, false);

            iPosType = (int) _is.Read(iPosType, 6, false);

            sType = (string) _is.Read(sType, 7, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iSetID, "iSetID");
            _ds.Display(iGroupID, "iGroupID");
            _ds.Display(iPower, "iPower");
            _ds.Display(iPos, "iPos");
            _ds.Display(iPosType, "iPosType");
            _ds.Display(sType, "sType");
        }

        public override void Clear()
        {
            iID = 0;
            iSetID = 0;
            iGroupID = 0;
            iPower = 0;
            iPos = 0;
            iPosType = 0;
            sType = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TTAC_AIHeroCommon_V2_Client();
            copied.iID = this.iID;
            copied.iSetID = this.iSetID;
            copied.iGroupID = this.iGroupID;
            copied.iPower = this.iPower;
            copied.iPos = this.iPos;
            copied.iPosType = this.iPosType;
            copied.sType = this.sType;
            return copied;
        }
    }
}

