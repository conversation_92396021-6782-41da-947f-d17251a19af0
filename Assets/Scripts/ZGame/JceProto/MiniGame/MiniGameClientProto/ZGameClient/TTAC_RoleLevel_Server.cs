// **********************************************************************
// This file was generated by a TAF parser!
// Generated from `SGameDBConfigProto.jce'
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_RoleLevel_Server : Wup.Jce.JceStruct
    {
        int _iLevel = 0;
        public int iLevel
        {
            get
            {
                 return _iLevel;
            }
            set
            {
                _iLevel = value; 
            }
        }

        int _iExp = 0;
        public int iExp
        {
            get
            {
                 return _iExp;
            }
            set
            {
                _iExp = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iLevel, 0);
            _os.Write(iExp, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iLevel = (int) _is.Read(iLevel, 0, false);

            iExp = (int) _is.Read(iExp, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iLevel, "iLevel");
            _ds.Display(iExp, "iExp");
        }

    }
}

