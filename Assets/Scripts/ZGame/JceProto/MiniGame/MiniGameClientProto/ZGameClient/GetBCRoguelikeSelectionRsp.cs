// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GetBCRoguelikeSelectionRsp : Wup.Jce.JceStruct
    {
        public int err = 0;

        public int gameModuleID = 0;

        public System.Collections.Generic.List<BCRoguelikeBossDetail> bossDetails;

        public TGameModule_Server gameModuleConfig;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(err, 0);
            _os.Write(gameModuleID, 1);
            _os.Write(bossDetails, 2);
            _os.Write(gameModuleConfig, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            err = (int) _is.Read(err, 0, false);

            gameModuleID = (int) _is.Read(gameModuleID, 1, false);

            bossDetails = (System.Collections.Generic.List<BCRoguelikeBossDetail>) _is.Read(bossDetails, 2, false);

            gameModuleConfig = (TGameModule_Server) _is.Read(gameModuleConfig, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(err, "err");
            _ds.Display(gameModuleID, "gameModuleID");
            _ds.Display(bossDetails, "bossDetails");
            _ds.Display(gameModuleConfig, "gameModuleConfig");
        }

        public override void Clear()
        {
            err = 0;
            gameModuleID = 0;
            bossDetails = null;
            gameModuleConfig = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GetBCRoguelikeSelectionRsp();
            copied.err = this.err;
            copied.gameModuleID = this.gameModuleID;
            copied.bossDetails = (System.Collections.Generic.List<BCRoguelikeBossDetail>)JceUtil.DeepClone(this.bossDetails);
            copied.gameModuleConfig = (TGameModule_Server)JceUtil.DeepClone(this.gameModuleConfig);
            return copied;
        }
    }
}

