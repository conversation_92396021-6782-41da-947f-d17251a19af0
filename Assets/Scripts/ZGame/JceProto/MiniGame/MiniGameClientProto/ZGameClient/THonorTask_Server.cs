// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class THonorTask_Server : Wup.Jce.JceStruct
    {
        /// <summary>
        /// ID
        /// </summary>
        public int iID = 0;

        /// <summary>
        /// 任务类型
        /// </summary>
        public int iType = 0;

        /// <summary>
        /// 任务目标次数
        /// </summary>
        public string sProgressTarget = "";

        /// <summary>
        /// 任务描述
        /// </summary>
        public string sTaskDes = "";

        /// <summary>
        /// 目标类型0普通1连续2累计3覆盖
        /// </summary>
        public int iTargetType = 0;

        /// <summary>
        /// 条件^参数1^参数2|条件^参数1^参数2^参数3
        /// </summary>
        public string sCondition = "";

        /// <summary>
        /// 道具ID
        /// </summary>
        public int iItemID1 = 0;

        /// <summary>
        /// 道具数量
        /// </summary>
        public int iItemCount1 = 0;

        /// <summary>
        /// 道具ID
        /// </summary>
        public int iItemID2 = 0;

        /// <summary>
        /// 道具数量
        /// </summary>
        public int iItemCount2 = 0;

        /// <summary>
        /// 道具ID
        /// </summary>
        public int iItemID3 = 0;

        /// <summary>
        /// 道具数量
        /// </summary>
        public int iItemCount3 = 0;

        /// <summary>
        /// 道具ID
        /// </summary>
        public int iItemID4 = 0;

        /// <summary>
        /// 道具数量
        /// </summary>
        public int iItemCount4 = 0;

        /// <summary>
        /// 任务参数
        /// </summary>
        public string sValue = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iType, 1);
            _os.Write(sProgressTarget, 3);
            _os.Write(sTaskDes, 4);
            _os.Write(iTargetType, 5);
            _os.Write(sCondition, 6);
            _os.Write(iItemID1, 7);
            _os.Write(iItemCount1, 8);
            _os.Write(iItemID2, 9);
            _os.Write(iItemCount2, 10);
            _os.Write(iItemID3, 11);
            _os.Write(iItemCount3, 12);
            _os.Write(iItemID4, 13);
            _os.Write(iItemCount4, 14);
            _os.Write(sValue, 15);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iType = (int) _is.Read(iType, 1, false);

            sProgressTarget = (string) _is.Read(sProgressTarget, 3, false);

            sTaskDes = (string) _is.Read(sTaskDes, 4, false);

            iTargetType = (int) _is.Read(iTargetType, 5, false);

            sCondition = (string) _is.Read(sCondition, 6, false);

            iItemID1 = (int) _is.Read(iItemID1, 7, false);

            iItemCount1 = (int) _is.Read(iItemCount1, 8, false);

            iItemID2 = (int) _is.Read(iItemID2, 9, false);

            iItemCount2 = (int) _is.Read(iItemCount2, 10, false);

            iItemID3 = (int) _is.Read(iItemID3, 11, false);

            iItemCount3 = (int) _is.Read(iItemCount3, 12, false);

            iItemID4 = (int) _is.Read(iItemID4, 13, false);

            iItemCount4 = (int) _is.Read(iItemCount4, 14, false);

            sValue = (string) _is.Read(sValue, 15, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iType, "iType");
            _ds.Display(sProgressTarget, "sProgressTarget");
            _ds.Display(sTaskDes, "sTaskDes");
            _ds.Display(iTargetType, "iTargetType");
            _ds.Display(sCondition, "sCondition");
            _ds.Display(iItemID1, "iItemID1");
            _ds.Display(iItemCount1, "iItemCount1");
            _ds.Display(iItemID2, "iItemID2");
            _ds.Display(iItemCount2, "iItemCount2");
            _ds.Display(iItemID3, "iItemID3");
            _ds.Display(iItemCount3, "iItemCount3");
            _ds.Display(iItemID4, "iItemID4");
            _ds.Display(iItemCount4, "iItemCount4");
            _ds.Display(sValue, "sValue");
        }

        public override void Clear()
        {
            iID = 0;
            iType = 0;
            sProgressTarget = "";
            sTaskDes = "";
            iTargetType = 0;
            sCondition = "";
            iItemID1 = 0;
            iItemCount1 = 0;
            iItemID2 = 0;
            iItemCount2 = 0;
            iItemID3 = 0;
            iItemCount3 = 0;
            iItemID4 = 0;
            iItemCount4 = 0;
            sValue = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new THonorTask_Server();
            copied.iID = this.iID;
            copied.iType = this.iType;
            copied.sProgressTarget = this.sProgressTarget;
            copied.sTaskDes = this.sTaskDes;
            copied.iTargetType = this.iTargetType;
            copied.sCondition = this.sCondition;
            copied.iItemID1 = this.iItemID1;
            copied.iItemCount1 = this.iItemCount1;
            copied.iItemID2 = this.iItemID2;
            copied.iItemCount2 = this.iItemCount2;
            copied.iItemID3 = this.iItemID3;
            copied.iItemCount3 = this.iItemCount3;
            copied.iItemID4 = this.iItemID4;
            copied.iItemCount4 = this.iItemCount4;
            copied.sValue = this.sValue;
            return copied;
        }
    }
}

