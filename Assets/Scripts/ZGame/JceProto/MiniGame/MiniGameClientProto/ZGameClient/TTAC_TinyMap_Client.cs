// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_TinyMap_Client : Wup.Jce.JceStruct
    {
        int _iID = 0;
        public int iID
        {
            get
            {
                 return _iID;
            }
            set
            {
                _iID = value; 
            }
        }

        string _sName = "";
        public string sName
        {
            get
            {
                 return _sName;
            }
            set
            {
                _sName = value; 
            }
        }

        string _sAccsee = "";
        public string sAccsee
        {
            get
            {
                 return _sAccsee;
            }
            set
            {
                _sAccsee = value; 
            }
        }

        string _sIntroduction = "";
        public string sIntroduction
        {
            get
            {
                 return _sIntroduction;
            }
            set
            {
                _sIntroduction = value; 
            }
        }

        string _sModel = "";
        public string sModel
        {
            get
            {
                 return _sModel;
            }
            set
            {
                _sModel = value; 
            }
        }

        string _sIcon = "";
        public string sIcon
        {
            get
            {
                 return _sIcon;
            }
            set
            {
                _sIcon = value; 
            }
        }

        string _sPreview = "";
        public string sPreview
        {
            get
            {
                 return _sPreview;
            }
            set
            {
                _sPreview = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sName, 1);
            _os.Write(sAccsee, 3);
            _os.Write(sIntroduction, 4);
            _os.Write(sModel, 5);
            _os.Write(sIcon, 6);
            _os.Write(sPreview, 7);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sName = (string) _is.Read(sName, 1, false);

            sAccsee = (string) _is.Read(sAccsee, 3, false);

            sIntroduction = (string) _is.Read(sIntroduction, 4, false);

            sModel = (string) _is.Read(sModel, 5, false);

            sIcon = (string) _is.Read(sIcon, 6, false);

            sPreview = (string) _is.Read(sPreview, 7, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sName, "sName");
            _ds.Display(sAccsee, "sAccsee");
            _ds.Display(sIntroduction, "sIntroduction");
            _ds.Display(sModel, "sModel");
            _ds.Display(sIcon, "sIcon");
            _ds.Display(sPreview, "sPreview");
        }

    }
}

