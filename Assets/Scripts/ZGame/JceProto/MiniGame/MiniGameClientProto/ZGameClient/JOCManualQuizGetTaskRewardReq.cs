// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class JOCManualQuizGetTaskRewardReq : Wup.Jce.JceStruct
    {
        public int taskID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(taskID, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            taskID = (int) _is.Read(taskID, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(taskID, "taskID");
        }

        public override void Clear()
        {
            taskID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new JOCManualQuizGetTaskRewardReq();
            copied.taskID = this.taskID;
            return copied;
        }
    }
}

