// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TMainHallRankUp_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iRankID = 0;

        public int iPlayType = 0;

        public string sIconEff = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iRankID, 1);
            _os.Write(iPlayType, 2);
            _os.Write(sIconEff, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iRankID = (int) _is.Read(iRankID, 1, false);

            iPlayType = (int) _is.Read(iPlayType, 2, false);

            sIconEff = (string) _is.Read(sIconEff, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iRankID, "iRankID");
            _ds.Display(iPlayType, "iPlayType");
            _ds.Display(sIconEff, "sIconEff");
        }

        public override void Clear()
        {
            iID = 0;
            iRankID = 0;
            iPlayType = 0;
            sIconEff = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TMainHallRankUp_Client();
            copied.iID = this.iID;
            copied.iRankID = this.iRankID;
            copied.iPlayType = this.iPlayType;
            copied.sIconEff = this.sIconEff;
            return copied;
        }
    }
}

