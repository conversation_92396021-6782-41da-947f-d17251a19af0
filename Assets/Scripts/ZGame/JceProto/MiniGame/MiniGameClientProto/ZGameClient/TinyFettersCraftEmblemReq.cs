// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TinyFettersCraftEmblemReq : Wup.Jce.JceStruct
    {
        public int fetterSameID = 0;

        /// <summary>
        /// 合成前的纹章书数量
        /// </summary>
        public int currentEmblemNum = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(fetterSameID, 0);
            _os.Write(currentEmblemNum, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            fetterSameID = (int) _is.Read(fetterSameID, 0, false);

            currentEmblemNum = (int) _is.Read(currentEmblemNum, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(fetterSameID, "fetterSameID");
            _ds.Display(currentEmblemNum, "currentEmblemNum");
        }

        public override void Clear()
        {
            fetterSameID = 0;
            currentEmblemNum = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TinyFettersCraftEmblemReq();
            copied.fetterSameID = this.fetterSameID;
            copied.currentEmblemNum = this.currentEmblemNum;
            return copied;
        }
    }
}

