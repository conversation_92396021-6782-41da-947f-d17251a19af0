//所在的Excel 【ACG_Hero.xlsm】
//**********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_HeroPackageDropConfig_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iTurnCountA = 0;

        public int iGroupA = 0;

        public int iTurnCountB = 0;

        public int iGroupB = 0;

        public int iTurnCountC = 0;

        public int iGroupC = 0;

        public int iRate = 0;

        public int iPlanID = 0;

        public int iRefreshCount = 0;

        public string sExtraCfg = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iTurnCountA, 1);
            _os.Write(iGroupA, 2);
            _os.Write(iTurnCountB, 3);
            _os.Write(iGroupB, 4);
            _os.Write(iTurnCountC, 5);
            _os.Write(iGroupC, 6);
            _os.Write(iRate, 7);
            _os.Write(iPlanID, 8);
            _os.Write(iRefreshCount, 9);
            _os.Write(sExtraCfg, 10);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iTurnCountA = (int) _is.Read(iTurnCountA, 1, false);

            iGroupA = (int) _is.Read(iGroupA, 2, false);

            iTurnCountB = (int) _is.Read(iTurnCountB, 3, false);

            iGroupB = (int) _is.Read(iGroupB, 4, false);

            iTurnCountC = (int) _is.Read(iTurnCountC, 5, false);

            iGroupC = (int) _is.Read(iGroupC, 6, false);

            iRate = (int) _is.Read(iRate, 7, false);

            iPlanID = (int) _is.Read(iPlanID, 8, false);

            iRefreshCount = (int) _is.Read(iRefreshCount, 9, false);

            sExtraCfg = (string) _is.Read(sExtraCfg, 10, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iTurnCountA, "iTurnCountA");
            _ds.Display(iGroupA, "iGroupA");
            _ds.Display(iTurnCountB, "iTurnCountB");
            _ds.Display(iGroupB, "iGroupB");
            _ds.Display(iTurnCountC, "iTurnCountC");
            _ds.Display(iGroupC, "iGroupC");
            _ds.Display(iRate, "iRate");
            _ds.Display(iPlanID, "iPlanID");
            _ds.Display(iRefreshCount, "iRefreshCount");
            _ds.Display(sExtraCfg, "sExtraCfg");
        }

        public override void Clear()
        {
            iID = 0;
            iTurnCountA = 0;
            iGroupA = 0;
            iTurnCountB = 0;
            iGroupB = 0;
            iTurnCountC = 0;
            iGroupC = 0;
            iRate = 0;
            iPlanID = 0;
            iRefreshCount = 0;
            sExtraCfg = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_HeroPackageDropConfig_Client();
            copied.iID = this.iID;
            copied.iTurnCountA = this.iTurnCountA;
            copied.iGroupA = this.iGroupA;
            copied.iTurnCountB = this.iTurnCountB;
            copied.iGroupB = this.iGroupB;
            copied.iTurnCountC = this.iTurnCountC;
            copied.iGroupC = this.iGroupC;
            copied.iRate = this.iRate;
            copied.iPlanID = this.iPlanID;
            copied.iRefreshCount = this.iRefreshCount;
            copied.sExtraCfg = this.sExtraCfg;
            return copied;
        }
    }
}

