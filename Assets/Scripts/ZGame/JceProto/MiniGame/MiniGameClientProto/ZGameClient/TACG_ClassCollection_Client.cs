// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_ClassCollection_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iClassID = 0;

        public string sName = "";

        public int iFetterID = 0;

        public string sIcon = "";

        public string sPicture = "";

        public int iisShow = 0;

        public int iSetID = 0;

        public int iSeasonID = 0;

        public int iTftClassID = 0;

        public int iisNew = 0;

        public int iMapID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iClassID, 1);
            _os.Write(sName, 2);
            _os.Write(iFetterID, 3);
            _os.Write(sIcon, 4);
            _os.Write(sPicture, 5);
            _os.Write(iisShow, 6);
            _os.Write(iSetID, 7);
            _os.Write(iSeasonID, 8);
            _os.Write(iTftClassID, 9);
            _os.Write(iisNew, 10);
            _os.Write(iMapID, 11);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iClassID = (int) _is.Read(iClassID, 1, false);

            sName = (string) _is.Read(sName, 2, false);

            iFetterID = (int) _is.Read(iFetterID, 3, false);

            sIcon = (string) _is.Read(sIcon, 4, false);

            sPicture = (string) _is.Read(sPicture, 5, false);

            iisShow = (int) _is.Read(iisShow, 6, false);

            iSetID = (int) _is.Read(iSetID, 7, false);

            iSeasonID = (int) _is.Read(iSeasonID, 8, false);

            iTftClassID = (int) _is.Read(iTftClassID, 9, false);

            iisNew = (int) _is.Read(iisNew, 10, false);

            iMapID = (int) _is.Read(iMapID, 11, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iClassID, "iClassID");
            _ds.Display(sName, "sName");
            _ds.Display(iFetterID, "iFetterID");
            _ds.Display(sIcon, "sIcon");
            _ds.Display(sPicture, "sPicture");
            _ds.Display(iisShow, "iisShow");
            _ds.Display(iSetID, "iSetID");
            _ds.Display(iSeasonID, "iSeasonID");
            _ds.Display(iTftClassID, "iTftClassID");
            _ds.Display(iisNew, "iisNew");
            _ds.Display(iMapID, "iMapID");
        }

        public override void Clear()
        {
            iID = 0;
            iClassID = 0;
            sName = "";
            iFetterID = 0;
            sIcon = "";
            sPicture = "";
            iisShow = 0;
            iSetID = 0;
            iSeasonID = 0;
            iTftClassID = 0;
            iisNew = 0;
            iMapID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_ClassCollection_Client();
            copied.iID = this.iID;
            copied.iClassID = this.iClassID;
            copied.sName = this.sName;
            copied.iFetterID = this.iFetterID;
            copied.sIcon = this.sIcon;
            copied.sPicture = this.sPicture;
            copied.iisShow = this.iisShow;
            copied.iSetID = this.iSetID;
            copied.iSeasonID = this.iSeasonID;
            copied.iTftClassID = this.iTftClassID;
            copied.iisNew = this.iisNew;
            copied.iMapID = this.iMapID;
            return copied;
        }
    }
}

