//所在的Excel 【ACG_Stage.xlsm】
//*********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_Stage_Priority_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iColumn1 = 0;

        public int iColumn2 = 0;

        public int iColumn3 = 0;

        public int iColumn4 = 0;

        public int iColumn5 = 0;

        public int iColumn6 = 0;

        public int iColumn7 = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iColumn1, 1);
            _os.Write(iColumn2, 2);
            _os.Write(iColumn3, 3);
            _os.Write(iColumn4, 4);
            _os.Write(iColumn5, 5);
            _os.Write(iColumn6, 6);
            _os.Write(iColumn7, 7);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iColumn1 = (int) _is.Read(iColumn1, 1, false);

            iColumn2 = (int) _is.Read(iColumn2, 2, false);

            iColumn3 = (int) _is.Read(iColumn3, 3, false);

            iColumn4 = (int) _is.Read(iColumn4, 4, false);

            iColumn5 = (int) _is.Read(iColumn5, 5, false);

            iColumn6 = (int) _is.Read(iColumn6, 6, false);

            iColumn7 = (int) _is.Read(iColumn7, 7, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iColumn1, "iColumn1");
            _ds.Display(iColumn2, "iColumn2");
            _ds.Display(iColumn3, "iColumn3");
            _ds.Display(iColumn4, "iColumn4");
            _ds.Display(iColumn5, "iColumn5");
            _ds.Display(iColumn6, "iColumn6");
            _ds.Display(iColumn7, "iColumn7");
        }

        public override void Clear()
        {
            iID = 0;
            iColumn1 = 0;
            iColumn2 = 0;
            iColumn3 = 0;
            iColumn4 = 0;
            iColumn5 = 0;
            iColumn6 = 0;
            iColumn7 = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_Stage_Priority_Client();
            copied.iID = this.iID;
            copied.iColumn1 = this.iColumn1;
            copied.iColumn2 = this.iColumn2;
            copied.iColumn3 = this.iColumn3;
            copied.iColumn4 = this.iColumn4;
            copied.iColumn5 = this.iColumn5;
            copied.iColumn6 = this.iColumn6;
            copied.iColumn7 = this.iColumn7;
            return copied;
        }
    }
}

