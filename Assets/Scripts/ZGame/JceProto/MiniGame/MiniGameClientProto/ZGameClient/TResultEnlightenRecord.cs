// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TResultEnlightenRecord : Wup.Jce.JceStruct
    {
        int _iMatchType = 0;
        public int iMatchType
        {
            get
            {
                 return _iMatchType;
            }
            set
            {
                _iMatchType = value; 
            }
        }

        int _iGameEndTime = 0;
        public int iGameEndTime
        {
            get
            {
                 return _iGameEndTime;
            }
            set
            {
                _iGameEndTime = value; 
            }
        }

        int _iRank = 0;
        public int iRank
        {
            get
            {
                 return _iRank;
            }
            set
            {
                _iRank = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iMatchType, 0);
            _os.Write(iGameEndTime, 1);
            _os.Write(iRank, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iMatchType = (int) _is.Read(iMatchType, 0, false);

            iGameEndTime = (int) _is.Read(iGameEndTime, 1, false);

            iRank = (int) _is.Read(iRank, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iMatchType, "iMatchType");
            _ds.Display(iGameEndTime, "iGameEndTime");
            _ds.Display(iRank, "iRank");
        }

        public override void Clear()
        {
            iMatchType = 0;
            iGameEndTime = 0;
            iRank = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TResultEnlightenRecord();
            copied.iMatchType = this.iMatchType;
            copied.iGameEndTime = this.iGameEndTime;
            copied.iRank = this.iRank;
            return copied;
        }
    }
}

