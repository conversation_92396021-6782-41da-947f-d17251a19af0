// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TButlerActivityExchangeInfo : Wup.Jce.JceStruct
    {
        public int id = 0;

        public int exchangedTimes = 0;

        public bool enableRemind = false;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(id, 0);
            _os.Write(exchangedTimes, 1);
            _os.Write(enableRemind, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            id = (int) _is.Read(id, 0, false);

            exchangedTimes = (int) _is.Read(exchangedTimes, 1, false);

            enableRemind = (bool) _is.Read(enableRemind, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(id, "id");
            _ds.Display(exchangedTimes, "exchangedTimes");
            _ds.Display(enableRemind, "enableRemind");
        }

        public override void Clear()
        {
            id = 0;
            exchangedTimes = 0;
            enableRemind = false;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TButlerActivityExchangeInfo();
            copied.id = this.id;
            copied.exchangedTimes = this.exchangedTimes;
            copied.enableRemind = this.enableRemind;
            return copied;
        }
    }
}

