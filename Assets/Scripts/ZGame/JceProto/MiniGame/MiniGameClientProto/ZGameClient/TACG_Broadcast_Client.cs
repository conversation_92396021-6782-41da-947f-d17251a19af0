// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_Broadcast_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sName = "";

        public string sIcon = "";

        public int iType = 0;

        public int iStar = 0;

        public string sHeroGroupSet = "";

        public int iSet = 0;

        public int iUseType = 0;

        public string sCardID = "";

        public string sGetWay = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sName, 1);
            _os.Write(sIcon, 2);
            _os.Write(iType, 3);
            _os.Write(iStar, 4);
            _os.Write(sHeroGroupSet, 7);
            _os.Write(iSet, 8);
            _os.Write(iUseType, 10);
            _os.Write(sCardID, 11);
            _os.Write(sGetWay, 12);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sName = (string) _is.Read(sName, 1, false);

            sIcon = (string) _is.Read(sIcon, 2, false);

            iType = (int) _is.Read(iType, 3, false);

            iStar = (int) _is.Read(iStar, 4, false);

            sHeroGroupSet = (string) _is.Read(sHeroGroupSet, 7, false);

            iSet = (int) _is.Read(iSet, 8, false);

            iUseType = (int) _is.Read(iUseType, 10, false);

            sCardID = (string) _is.Read(sCardID, 11, false);

            sGetWay = (string) _is.Read(sGetWay, 12, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sName, "sName");
            _ds.Display(sIcon, "sIcon");
            _ds.Display(iType, "iType");
            _ds.Display(iStar, "iStar");
            _ds.Display(sHeroGroupSet, "sHeroGroupSet");
            _ds.Display(iSet, "iSet");
            _ds.Display(iUseType, "iUseType");
            _ds.Display(sCardID, "sCardID");
            _ds.Display(sGetWay, "sGetWay");
        }

        public override void Clear()
        {
            iID = 0;
            sName = "";
            sIcon = "";
            iType = 0;
            iStar = 0;
            sHeroGroupSet = "";
            iSet = 0;
            iUseType = 0;
            sCardID = "";
            sGetWay = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_Broadcast_Client();
            copied.iID = this.iID;
            copied.sName = this.sName;
            copied.sIcon = this.sIcon;
            copied.iType = this.iType;
            copied.iStar = this.iStar;
            copied.sHeroGroupSet = this.sHeroGroupSet;
            copied.iSet = this.iSet;
            copied.iUseType = this.iUseType;
            copied.sCardID = this.sCardID;
            copied.sGetWay = this.sGetWay;
            return copied;
        }
    }
}

