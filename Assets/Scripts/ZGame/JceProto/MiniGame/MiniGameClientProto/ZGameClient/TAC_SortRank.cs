// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_SortRank : Wup.Jce.JceStruct
    {
        int _i8ChairID = 0;
        public int i8ChairID
        {
            get
            {
                 return _i8ChairID;
            }
            set
            {
                _i8ChairID = value; 
            }
        }

        int _iRank = 0;
        public int iRank
        {
            get
            {
                 return _iRank;
            }
            set
            {
                _iRank = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(i8ChairID, 0);
            _os.Write(iRank, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            i8ChairID = (int) _is.Read(i8ChairID, 0, false);

            iRank = (int) _is.Read(iRank, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(i8ChairID, "i8ChairID");
            _ds.Display(iRank, "iRank");
        }

        public override void Clear()
        {
            i8ChairID = 0;
            iRank = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_SortRank();
            copied.i8ChairID = this.i8ChairID;
            copied.iRank = this.iRank;
            return copied;
        }
    }
}

