// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAllGameCfgClientExt1 : Wup.Jce.JceStruct
    {
        public UniqueInfo.ConfigHashMap<int, TDummyExt1_Client> mapDummyExt1_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HABasicConfig_Client> mapACG_HABasicConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HACondition_Client> mapACG_HACondition_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HADropConfig_Client> mapACG_HADropConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HADropReward_Client> mapACG_HADropReward_Client;

        public UniqueInfo.ConfigHashMap<int, TCardInfo_Client> mapCardInfo_Client;

        public UniqueInfo.ConfigHashMap<int, TCardCollectTask_Client> mapCardCollectTask_Client;

        public UniqueInfo.ConfigHashMap<int, TCardValue_Client> mapCardValue_Client;

        public UniqueInfo.ConfigHashMap<int, TCardUpgrade_Client> mapCardUpgrade_Client;

        public UniqueInfo.ConfigHashMap<int, TCardDropProbability_Client> mapCardDropProbability_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_TinyHero_Client> mapACG_TinyHero_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_TinyTag_Client> mapACG_TinyTag_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_TinyOrigin_Client> mapACG_TinyOrigin_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_TinyClass_Client> mapACG_TinyClass_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_MapClass_Client> mapACG_MapClass_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_AttackEffect_Client> mapACG_AttackEffect_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_AttackEffectClass_Client> mapACG_AttackEffectClass_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_TerrainGrid_Client> mapACG_TerrainGrid_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Broadcast_Client> mapACG_Broadcast_Client;

        public UniqueInfo.ConfigHashMap<int, TCollege_Equipment_Client> mapCollege_Equipment_Client;

        public UniqueInfo.ConfigHashMap<int, TModeNameConfig_Client> mapModeNameConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HABattleRules_Client> mapACG_HABattleRules_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HABuff_Client> mapACG_HABuff_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_MercenaryRoll_Client> mapACG_MercenaryRoll_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_EconomicMechanismCfg_Client> mapACG_EconomicMechanismCfg_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_YordleDrop_Client> mapACG_YordleDrop_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_PresentEntry_Client> mapACG_PresentEntry_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Glutton_Client> mapACG_Glutton_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Glutton_Arr_Client> mapACG_Glutton_Arr_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_FortuneDrop_Client> mapACG_FortuneDrop_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HeroPackageDropConfig_Client> mapACG_HeroPackageDropConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HeroStoreConfig_Client> mapACG_HeroStoreConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_PKGHeroConfig_Client> mapACG_PKGHeroConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_PKGEquipConfig_Client> mapACG_PKGEquipConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_PKGHeroPool_Client> mapACG_PKGHeroPool_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_PKGEquipPool_Client> mapACG_PKGEquipPool_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HeavenChooser_Level_Client> mapACG_HeavenChooser_Level_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HeavenChooser_Refresh_Client> mapACG_HeavenChooser_Refresh_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HeavenChooser_Cfg_Client> mapACG_HeavenChooser_Cfg_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_FloatText_Client> mapACG_FloatText_Client;

        public UniqueInfo.ConfigHashMap<int, TDeviceExtremeQuality_Client> mapDeviceExtremeQuality_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_TinyFetters_Client> mapACG_TinyFetters_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HeavenChooser_Arr_Client> mapACG_HeavenChooser_Arr_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_UserLevel_Client> mapACG_UserLevel_Client;

        public UniqueInfo.ConfigHashMap<int, TBCRoguelikeGlobal_Client> mapBCRoguelikeGlobal_Client;

        public UniqueInfo.ConfigHashMap<int, TContest_LP_Cacualte_Config_Client> mapContest_LP_Cacualte_Config_Client;

        public UniqueInfo.ConfigHashMap<int, TContest_Score_Config_Client> mapContest_Score_Config_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_TransferClass_Client> mapACG_TransferClass_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_WinningEffectClass_Client> mapACG_WinningEffectClass_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_DraftEffectClass_Client> mapACG_DraftEffectClass_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HurtFontClass_Client> mapACG_HurtFontClass_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_ChessPieceHurtEffectClass_Client> mapACG_ChessPieceHurtEffectClass_Client;

        public UniqueInfo.ConfigHashMap<int, TChat_QuickChat_Client> mapChat_QuickChat_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_WinEffectConfigClass_Client> mapACG_WinEffectConfigClass_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_MailAddress_Client> mapACG_MailAddress_Client;

        public UniqueInfo.ConfigHashMap<int, TShieldMThreadedRenderingDevice_Client> mapShieldMThreadedRenderingDevice_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_BroadcastTrigger_Client> mapACG_BroadcastTrigger_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HeroCollection_Client> mapACG_HeroCollection_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_EquipmentCollection_Client> mapACG_EquipmentCollection_Client;

        public UniqueInfo.ConfigHashMap<int, TDeviceFPS_Client> mapDeviceFPS_Client;

        public UniqueInfo.ConfigHashMap<int, TBlockList_Client> mapBlockList_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_TinyMapDIYResource_Client> mapACG_TinyMapDIYResource_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_TinyMapDIYCollocation_Client> mapACG_TinyMapDIYCollocation_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_LimitedHeroKu_Client> mapTAC_LimitedHeroKu_Client;

        public UniqueInfo.ConfigHashMap<int, TCardSeasonType_Client> mapCardSeasonType_Client;

        public UniqueInfo.ConfigHashMap<int, TMainHallRankUp_Client> mapMainHallRankUp_Client;

        public UniqueInfo.ConfigHashMap<int, TPrivilegedCardExhibitItem_Client> mapPrivilegedCardExhibitItem_Client;

        public UniqueInfo.ConfigHashMap<int, TChat_MagicWord_Client> mapChat_MagicWord_Client;

        public UniqueInfo.ConfigHashMap<int, TDeviceRanking_Client> mapDeviceRanking_Client;

        public UniqueInfo.ConfigHashMap<int, TDeviceStatusParameter_Client> mapDeviceStatusParameter_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_InteractTiny_Client> mapACG_InteractTiny_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_BadLuckProtection_Client> mapACG_BadLuckProtection_Client;

        public UniqueInfo.ConfigHashMap<int, TNameCard_Related_Client> mapNameCard_Related_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_SceneToPlayerNum_Client> mapACG_SceneToPlayerNum_Client;

        public UniqueInfo.ConfigHashMap<int, TCardCelebrateEffect_Client> mapCardCelebrateEffect_Client;

        public UniqueInfo.ConfigHashMap<int, TCardBroadcastItem_Client> mapCardBroadcastItem_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_AstralHeroPool_Client> mapACG_AstralHeroPool_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_SetController_Client> mapACG_SetController_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_SetResConfig_Client> mapACG_SetResConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TCardCollectReportParam_Client> mapCardCollectReportParam_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_AstralDropPool_Client> mapACG_AstralDropPool_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HeroExtraRule_Client> mapACG_HeroExtraRule_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_BattleEndTag_Client> mapACG_BattleEndTag_Client;

        public UniqueInfo.ConfigHashMap<int, TCardSet_Client> mapCardSet_Client;

        public UniqueInfo.ConfigHashMap<int, TLotteryEffect_Client> mapLotteryEffect_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HeroEquipMutex_Client> mapACG_HeroEquipMutex_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_TransferCfg_Client> mapACG_TransferCfg_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_TransferChessUnitDisplayCfg_Client> mapACG_TransferChessUnitDisplayCfg_Client;

        public UniqueInfo.ConfigHashMap<int, TDeviceVRS_Client> mapDeviceVRS_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_KeyTargetDrop_Client> mapACG_KeyTargetDrop_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_Tag_V2_Client> mapTAC_Tag_V2_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_AIID_V2_Client> mapTAC_AIID_V2_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_AIAssignTag_V2_Client> mapTAC_AIAssignTag_V2_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_AIHeroCommon_V2_Client> mapTAC_AIHeroCommon_V2_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_AIEquipCommon_V2_Client> mapTAC_AIEquipCommon_V2_Client;

        public UniqueInfo.ConfigHashMap<int, TACGFetterLiveCounter_Client> mapACGFetterLiveCounter_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HAHeroBasicConfig_Client> mapACG_HAHeroBasicConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HAHeroDropConfig_Client> mapACG_HAHeroDropConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_EquipmentStore_TFT_Dual_Client> mapACG_EquipmentStore_TFT_Dual_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HASkinConfig_Client> mapACG_HASkinConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TTimeLimit_LP_Cacualte_Config_Client> mapTimeLimit_LP_Cacualte_Config_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_AIDifficult_V2_Client> mapTAC_AIDifficult_V2_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_AIConstKey_V2_Client> mapTAC_AIConstKey_V2_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_AIDropEquipment_V2_Client> mapTAC_AIDropEquipment_V2_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_Tips_Client> mapTAC_Tips_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_EmblemConfig_Client> mapTAC_EmblemConfig_Client;

        public UniqueInfo.ConfigHashMap<int, THeroesUniteReward_Client> mapHeroesUniteReward_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_Admin_Client> mapTAC_Admin_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_CommonTag_Client> mapACG_CommonTag_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_CommonGuideFlow_Client> mapACG_CommonGuideFlow_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_CommonGuideStep_Client> mapACG_CommonGuideStep_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_CommonGuideTips_Client> mapACG_CommonGuideTips_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_EquipLiveCounter_Client> mapACG_EquipLiveCounter_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_TiggerSound_Client> mapACG_TiggerSound_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_AITestTag_V2_Client> mapTAC_AITestTag_V2_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_AIRank_V2_Client> mapTAC_AIRank_V2_Client;

        public UniqueInfo.ConfigHashMap<int, TCardCollectSeasonShow_Client> mapCardCollectSeasonShow_Client;

        public UniqueInfo.ConfigHashMap<int, TMainHallLeftRecommand_Client> mapMainHallLeftRecommand_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_CelebrateEffectInBattle_Client> mapACG_CelebrateEffectInBattle_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_CelebrateEffectBanCfg_Client> mapACG_CelebrateEffectBanCfg_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_CorruptedCfg_Client> mapACG_CorruptedCfg_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_CorruptedPool_Client> mapACG_CorruptedPool_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_RoundSelectHeroCfg_Client> mapACG_RoundSelectHeroCfg_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_SpecCollection_Client> mapACG_SpecCollection_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_ClassCollection_Client> mapACG_ClassCollection_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_CelebrateUnitDisplayCfg_Client> mapACG_CelebrateUnitDisplayCfg_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_NewLootSet_Client> mapACG_NewLootSet_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_MonsterGroup_Client> mapACG_MonsterGroup_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_NewLootNum_Client> mapACG_NewLootNum_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_PublicAlloc_Client> mapACG_PublicAlloc_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_ItemAlloc_Client> mapACG_ItemAlloc_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_ItemPushBack_Client> mapACG_ItemPushBack_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_BonusBox_Client> mapACG_BonusBox_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_BonusAlloc_Client> mapACG_BonusAlloc_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_ValueAlloc_Client> mapACG_ValueAlloc_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_GalaxyViewConfig_Client> mapACG_GalaxyViewConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Legend_Client> mapACG_Legend_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_LegendDetail_Client> mapACG_LegendDetail_Client;

        public UniqueInfo.ConfigHashMap<int, TJOCManualBPLevelPic_Client> mapJOCManualBPLevelPic_Client;

        public UniqueInfo.ConfigHashMap<int, TJOCQualifierTask_Client> mapJOCQualifierTask_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_PiltoverDrop_Client> mapACG_PiltoverDrop_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_NameCard_Client> mapACG_NameCard_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_AIGalaxyTreeReplace_V2_Client> mapTAC_AIGalaxyTreeReplace_V2_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_EquipRecommCategory_Client> mapACG_EquipRecommCategory_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_EquipRecomm_Client> mapACG_EquipRecomm_Client;

        public UniqueInfo.ConfigHashMap<int, TGuildTips_Client> mapGuildTips_Client;

        public UniqueInfo.ConfigHashMap<int, TCardScoreRankInfo_Client> mapCardScoreRankInfo_Client;

        public UniqueInfo.ConfigHashMap<int, TDeviceWhiteProfile_Client> mapDeviceWhiteProfile_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HexEffect_Client> mapACG_HexEffect_Client;

        public UniqueInfo.ConfigHashMap<int, TCardScoreDefaultParam_Client> mapCardScoreDefaultParam_Client;

        public UniqueInfo.ConfigHashMap<int, TCardScoreAdvancedParam_Client> mapCardScoreAdvancedParam_Client;

        public UniqueInfo.ConfigHashMap<int, TCasual_LP_Cacualte_Config_Client> mapCasual_LP_Cacualte_Config_Client;

        public UniqueInfo.ConfigHashMap<int, TACGSkillLiveCounter_Client> mapACGSkillLiveCounter_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_ButtonSkinCfg_Client> mapACG_ButtonSkinCfg_Client;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(mapDummyExt1_Client, 0);
            _os.Write(mapACG_HABasicConfig_Client, 1);
            _os.Write(mapACG_HACondition_Client, 2);
            _os.Write(mapACG_HADropConfig_Client, 3);
            _os.Write(mapACG_HADropReward_Client, 4);
            _os.Write(mapCardInfo_Client, 5);
            _os.Write(mapCardCollectTask_Client, 6);
            _os.Write(mapCardValue_Client, 7);
            _os.Write(mapCardUpgrade_Client, 8);
            _os.Write(mapCardDropProbability_Client, 9);
            _os.Write(mapACG_TinyHero_Client, 10);
            _os.Write(mapACG_TinyTag_Client, 11);
            _os.Write(mapACG_TinyOrigin_Client, 12);
            _os.Write(mapACG_TinyClass_Client, 13);
            _os.Write(mapACG_MapClass_Client, 14);
            _os.Write(mapACG_AttackEffect_Client, 15);
            _os.Write(mapACG_AttackEffectClass_Client, 16);
            _os.Write(mapACG_TerrainGrid_Client, 17);
            _os.Write(mapACG_Broadcast_Client, 18);
            _os.Write(mapCollege_Equipment_Client, 19);
            _os.Write(mapModeNameConfig_Client, 20);
            _os.Write(mapACG_HABattleRules_Client, 21);
            _os.Write(mapACG_HABuff_Client, 22);
            _os.Write(mapACG_MercenaryRoll_Client, 23);
            _os.Write(mapACG_EconomicMechanismCfg_Client, 24);
            _os.Write(mapACG_YordleDrop_Client, 25);
            _os.Write(mapACG_PresentEntry_Client, 26);
            _os.Write(mapACG_Glutton_Client, 27);
            _os.Write(mapACG_Glutton_Arr_Client, 28);
            _os.Write(mapACG_FortuneDrop_Client, 29);
            _os.Write(mapACG_HeroPackageDropConfig_Client, 30);
            _os.Write(mapACG_HeroStoreConfig_Client, 31);
            _os.Write(mapACG_PKGHeroConfig_Client, 32);
            _os.Write(mapACG_PKGEquipConfig_Client, 33);
            _os.Write(mapACG_PKGHeroPool_Client, 34);
            _os.Write(mapACG_PKGEquipPool_Client, 35);
            _os.Write(mapACG_HeavenChooser_Level_Client, 36);
            _os.Write(mapACG_HeavenChooser_Refresh_Client, 37);
            _os.Write(mapACG_HeavenChooser_Cfg_Client, 38);
            _os.Write(mapACG_FloatText_Client, 39);
            _os.Write(mapDeviceExtremeQuality_Client, 40);
            _os.Write(mapACG_TinyFetters_Client, 41);
            _os.Write(mapACG_HeavenChooser_Arr_Client, 42);
            _os.Write(mapACG_UserLevel_Client, 43);
            _os.Write(mapBCRoguelikeGlobal_Client, 44);
            _os.Write(mapContest_LP_Cacualte_Config_Client, 45);
            _os.Write(mapContest_Score_Config_Client, 46);
            _os.Write(mapACG_TransferClass_Client, 47);
            _os.Write(mapACG_WinningEffectClass_Client, 48);
            _os.Write(mapACG_DraftEffectClass_Client, 49);
            _os.Write(mapACG_HurtFontClass_Client, 50);
            _os.Write(mapACG_ChessPieceHurtEffectClass_Client, 51);
            _os.Write(mapChat_QuickChat_Client, 52);
            _os.Write(mapACG_WinEffectConfigClass_Client, 53);
            _os.Write(mapACG_MailAddress_Client, 101);
            _os.Write(mapShieldMThreadedRenderingDevice_Client, 102);
            _os.Write(mapACG_BroadcastTrigger_Client, 103);
            _os.Write(mapACG_HeroCollection_Client, 104);
            _os.Write(mapACG_EquipmentCollection_Client, 105);
            _os.Write(mapDeviceFPS_Client, 106);
            _os.Write(mapBlockList_Client, 107);
            _os.Write(mapACG_TinyMapDIYResource_Client, 108);
            _os.Write(mapACG_TinyMapDIYCollocation_Client, 109);
            _os.Write(mapTAC_LimitedHeroKu_Client, 110);
            _os.Write(mapCardSeasonType_Client, 111);
            _os.Write(mapMainHallRankUp_Client, 112);
            _os.Write(mapPrivilegedCardExhibitItem_Client, 113);
            _os.Write(mapChat_MagicWord_Client, 114);
            _os.Write(mapDeviceRanking_Client, 115);
            _os.Write(mapDeviceStatusParameter_Client, 116);
            _os.Write(mapACG_InteractTiny_Client, 117);
            _os.Write(mapACG_BadLuckProtection_Client, 118);
            _os.Write(mapNameCard_Related_Client, 119);
            _os.Write(mapACG_SceneToPlayerNum_Client, 120);
            _os.Write(mapCardCelebrateEffect_Client, 121);
            _os.Write(mapCardBroadcastItem_Client, 122);
            _os.Write(mapACG_AstralHeroPool_Client, 123);
            _os.Write(mapACG_SetController_Client, 124);
            _os.Write(mapACG_SetResConfig_Client, 125);
            _os.Write(mapCardCollectReportParam_Client, 126);
            _os.Write(mapACG_AstralDropPool_Client, 127);
            _os.Write(mapACG_HeroExtraRule_Client, 128);
            _os.Write(mapACG_BattleEndTag_Client, 129);
            _os.Write(mapCardSet_Client, 130);
            _os.Write(mapLotteryEffect_Client, 131);
            _os.Write(mapACG_HeroEquipMutex_Client, 132);
            _os.Write(mapACG_TransferCfg_Client, 133);
            _os.Write(mapACG_TransferChessUnitDisplayCfg_Client, 134);
            _os.Write(mapDeviceVRS_Client, 135);
            _os.Write(mapACG_KeyTargetDrop_Client, 136);
            _os.Write(mapTAC_Tag_V2_Client, 137);
            _os.Write(mapTAC_AIID_V2_Client, 138);
            _os.Write(mapTAC_AIAssignTag_V2_Client, 139);
            _os.Write(mapTAC_AIHeroCommon_V2_Client, 140);
            _os.Write(mapTAC_AIEquipCommon_V2_Client, 141);
            _os.Write(mapACGFetterLiveCounter_Client, 142);
            _os.Write(mapACG_HAHeroBasicConfig_Client, 143);
            _os.Write(mapACG_HAHeroDropConfig_Client, 144);
            _os.Write(mapACG_EquipmentStore_TFT_Dual_Client, 145);
            _os.Write(mapACG_HASkinConfig_Client, 146);
            _os.Write(mapTimeLimit_LP_Cacualte_Config_Client, 147);
            _os.Write(mapTAC_AIDifficult_V2_Client, 148);
            _os.Write(mapTAC_AIConstKey_V2_Client, 149);
            _os.Write(mapTAC_AIDropEquipment_V2_Client, 150);
            _os.Write(mapTAC_Tips_Client, 151);
            _os.Write(mapTAC_EmblemConfig_Client, 152);
            _os.Write(mapHeroesUniteReward_Client, 153);
            _os.Write(mapTAC_Admin_Client, 154);
            _os.Write(mapACG_CommonTag_Client, 155);
            _os.Write(mapACG_CommonGuideFlow_Client, 156);
            _os.Write(mapACG_CommonGuideStep_Client, 157);
            _os.Write(mapACG_CommonGuideTips_Client, 158);
            _os.Write(mapACG_EquipLiveCounter_Client, 159);
            _os.Write(mapACG_TiggerSound_Client, 160);
            _os.Write(mapTAC_AITestTag_V2_Client, 161);
            _os.Write(mapTAC_AIRank_V2_Client, 162);
            _os.Write(mapCardCollectSeasonShow_Client, 163);
            _os.Write(mapMainHallLeftRecommand_Client, 164);
            _os.Write(mapACG_CelebrateEffectInBattle_Client, 165);
            _os.Write(mapACG_CelebrateEffectBanCfg_Client, 166);
            _os.Write(mapACG_CorruptedCfg_Client, 167);
            _os.Write(mapACG_CorruptedPool_Client, 168);
            _os.Write(mapACG_RoundSelectHeroCfg_Client, 169);
            _os.Write(mapACG_SpecCollection_Client, 170);
            _os.Write(mapACG_ClassCollection_Client, 171);
            _os.Write(mapACG_CelebrateUnitDisplayCfg_Client, 172);
            _os.Write(mapACG_NewLootSet_Client, 173);
            _os.Write(mapACG_MonsterGroup_Client, 174);
            _os.Write(mapACG_NewLootNum_Client, 175);
            _os.Write(mapACG_PublicAlloc_Client, 176);
            _os.Write(mapACG_ItemAlloc_Client, 177);
            _os.Write(mapACG_ItemPushBack_Client, 178);
            _os.Write(mapACG_BonusBox_Client, 179);
            _os.Write(mapACG_BonusAlloc_Client, 180);
            _os.Write(mapACG_ValueAlloc_Client, 181);
            _os.Write(mapACG_GalaxyViewConfig_Client, 182);
            _os.Write(mapACG_Legend_Client, 183);
            _os.Write(mapACG_LegendDetail_Client, 184);
            _os.Write(mapJOCManualBPLevelPic_Client, 185);
            _os.Write(mapJOCQualifierTask_Client, 186);
            _os.Write(mapACG_PiltoverDrop_Client, 187);
            _os.Write(mapACG_NameCard_Client, 188);
            _os.Write(mapTAC_AIGalaxyTreeReplace_V2_Client, 189);
            _os.Write(mapACG_EquipRecommCategory_Client, 190);
            _os.Write(mapACG_EquipRecomm_Client, 191);
            _os.Write(mapGuildTips_Client, 192);
            _os.Write(mapCardScoreRankInfo_Client, 194);
            _os.Write(mapDeviceWhiteProfile_Client, 195);
            _os.Write(mapACG_HexEffect_Client, 196);
            _os.Write(mapCardScoreDefaultParam_Client, 197);
            _os.Write(mapCardScoreAdvancedParam_Client, 198);
            _os.Write(mapCasual_LP_Cacualte_Config_Client, 199);
            _os.Write(mapACGSkillLiveCounter_Client, 200);
            _os.Write(mapACG_ButtonSkinCfg_Client, 201);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            mapDummyExt1_Client = (UniqueInfo.ConfigHashMap<int, TDummyExt1_Client>) _is.Read(mapDummyExt1_Client, 0, false);

            mapACG_HABasicConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_HABasicConfig_Client>) _is.Read(mapACG_HABasicConfig_Client, 1, false);

            mapACG_HACondition_Client = (UniqueInfo.ConfigHashMap<int, TACG_HACondition_Client>) _is.Read(mapACG_HACondition_Client, 2, false);

            mapACG_HADropConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_HADropConfig_Client>) _is.Read(mapACG_HADropConfig_Client, 3, false);

            mapACG_HADropReward_Client = (UniqueInfo.ConfigHashMap<int, TACG_HADropReward_Client>) _is.Read(mapACG_HADropReward_Client, 4, false);

            mapCardInfo_Client = (UniqueInfo.ConfigHashMap<int, TCardInfo_Client>) _is.Read(mapCardInfo_Client, 5, false);

            mapCardCollectTask_Client = (UniqueInfo.ConfigHashMap<int, TCardCollectTask_Client>) _is.Read(mapCardCollectTask_Client, 6, false);

            mapCardValue_Client = (UniqueInfo.ConfigHashMap<int, TCardValue_Client>) _is.Read(mapCardValue_Client, 7, false);

            mapCardUpgrade_Client = (UniqueInfo.ConfigHashMap<int, TCardUpgrade_Client>) _is.Read(mapCardUpgrade_Client, 8, false);

            mapCardDropProbability_Client = (UniqueInfo.ConfigHashMap<int, TCardDropProbability_Client>) _is.Read(mapCardDropProbability_Client, 9, false);

            mapACG_TinyHero_Client = (UniqueInfo.ConfigHashMap<int, TACG_TinyHero_Client>) _is.Read(mapACG_TinyHero_Client, 10, false);

            mapACG_TinyTag_Client = (UniqueInfo.ConfigHashMap<int, TACG_TinyTag_Client>) _is.Read(mapACG_TinyTag_Client, 11, false);

            mapACG_TinyOrigin_Client = (UniqueInfo.ConfigHashMap<int, TACG_TinyOrigin_Client>) _is.Read(mapACG_TinyOrigin_Client, 12, false);

            mapACG_TinyClass_Client = (UniqueInfo.ConfigHashMap<int, TACG_TinyClass_Client>) _is.Read(mapACG_TinyClass_Client, 13, false);

            mapACG_MapClass_Client = (UniqueInfo.ConfigHashMap<int, TACG_MapClass_Client>) _is.Read(mapACG_MapClass_Client, 14, false);

            mapACG_AttackEffect_Client = (UniqueInfo.ConfigHashMap<int, TACG_AttackEffect_Client>) _is.Read(mapACG_AttackEffect_Client, 15, false);

            mapACG_AttackEffectClass_Client = (UniqueInfo.ConfigHashMap<int, TACG_AttackEffectClass_Client>) _is.Read(mapACG_AttackEffectClass_Client, 16, false);

            mapACG_TerrainGrid_Client = (UniqueInfo.ConfigHashMap<int, TACG_TerrainGrid_Client>) _is.Read(mapACG_TerrainGrid_Client, 17, false);

            mapACG_Broadcast_Client = (UniqueInfo.ConfigHashMap<int, TACG_Broadcast_Client>) _is.Read(mapACG_Broadcast_Client, 18, false);

            mapCollege_Equipment_Client = (UniqueInfo.ConfigHashMap<int, TCollege_Equipment_Client>) _is.Read(mapCollege_Equipment_Client, 19, false);

            mapModeNameConfig_Client = (UniqueInfo.ConfigHashMap<int, TModeNameConfig_Client>) _is.Read(mapModeNameConfig_Client, 20, false);

            mapACG_HABattleRules_Client = (UniqueInfo.ConfigHashMap<int, TACG_HABattleRules_Client>) _is.Read(mapACG_HABattleRules_Client, 21, false);

            mapACG_HABuff_Client = (UniqueInfo.ConfigHashMap<int, TACG_HABuff_Client>) _is.Read(mapACG_HABuff_Client, 22, false);

            mapACG_MercenaryRoll_Client = (UniqueInfo.ConfigHashMap<int, TACG_MercenaryRoll_Client>) _is.Read(mapACG_MercenaryRoll_Client, 23, false);

            mapACG_EconomicMechanismCfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_EconomicMechanismCfg_Client>) _is.Read(mapACG_EconomicMechanismCfg_Client, 24, false);

            mapACG_YordleDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_YordleDrop_Client>) _is.Read(mapACG_YordleDrop_Client, 25, false);

            mapACG_PresentEntry_Client = (UniqueInfo.ConfigHashMap<int, TACG_PresentEntry_Client>) _is.Read(mapACG_PresentEntry_Client, 26, false);

            mapACG_Glutton_Client = (UniqueInfo.ConfigHashMap<int, TACG_Glutton_Client>) _is.Read(mapACG_Glutton_Client, 27, false);

            mapACG_Glutton_Arr_Client = (UniqueInfo.ConfigHashMap<int, TACG_Glutton_Arr_Client>) _is.Read(mapACG_Glutton_Arr_Client, 28, false);

            mapACG_FortuneDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_FortuneDrop_Client>) _is.Read(mapACG_FortuneDrop_Client, 29, false);

            mapACG_HeroPackageDropConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeroPackageDropConfig_Client>) _is.Read(mapACG_HeroPackageDropConfig_Client, 30, false);

            mapACG_HeroStoreConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeroStoreConfig_Client>) _is.Read(mapACG_HeroStoreConfig_Client, 31, false);

            mapACG_PKGHeroConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_PKGHeroConfig_Client>) _is.Read(mapACG_PKGHeroConfig_Client, 32, false);

            mapACG_PKGEquipConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_PKGEquipConfig_Client>) _is.Read(mapACG_PKGEquipConfig_Client, 33, false);

            mapACG_PKGHeroPool_Client = (UniqueInfo.ConfigHashMap<int, TACG_PKGHeroPool_Client>) _is.Read(mapACG_PKGHeroPool_Client, 34, false);

            mapACG_PKGEquipPool_Client = (UniqueInfo.ConfigHashMap<int, TACG_PKGEquipPool_Client>) _is.Read(mapACG_PKGEquipPool_Client, 35, false);

            mapACG_HeavenChooser_Level_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeavenChooser_Level_Client>) _is.Read(mapACG_HeavenChooser_Level_Client, 36, false);

            mapACG_HeavenChooser_Refresh_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeavenChooser_Refresh_Client>) _is.Read(mapACG_HeavenChooser_Refresh_Client, 37, false);

            mapACG_HeavenChooser_Cfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeavenChooser_Cfg_Client>) _is.Read(mapACG_HeavenChooser_Cfg_Client, 38, false);

            mapACG_FloatText_Client = (UniqueInfo.ConfigHashMap<int, TACG_FloatText_Client>) _is.Read(mapACG_FloatText_Client, 39, false);

            mapDeviceExtremeQuality_Client = (UniqueInfo.ConfigHashMap<int, TDeviceExtremeQuality_Client>) _is.Read(mapDeviceExtremeQuality_Client, 40, false);

            mapACG_TinyFetters_Client = (UniqueInfo.ConfigHashMap<int, TACG_TinyFetters_Client>) _is.Read(mapACG_TinyFetters_Client, 41, false);

            mapACG_HeavenChooser_Arr_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeavenChooser_Arr_Client>) _is.Read(mapACG_HeavenChooser_Arr_Client, 42, false);

            mapACG_UserLevel_Client = (UniqueInfo.ConfigHashMap<int, TACG_UserLevel_Client>) _is.Read(mapACG_UserLevel_Client, 43, false);

            mapBCRoguelikeGlobal_Client = (UniqueInfo.ConfigHashMap<int, TBCRoguelikeGlobal_Client>) _is.Read(mapBCRoguelikeGlobal_Client, 44, false);

            mapContest_LP_Cacualte_Config_Client = (UniqueInfo.ConfigHashMap<int, TContest_LP_Cacualte_Config_Client>) _is.Read(mapContest_LP_Cacualte_Config_Client, 45, false);

            mapContest_Score_Config_Client = (UniqueInfo.ConfigHashMap<int, TContest_Score_Config_Client>) _is.Read(mapContest_Score_Config_Client, 46, false);

            mapACG_TransferClass_Client = (UniqueInfo.ConfigHashMap<int, TACG_TransferClass_Client>) _is.Read(mapACG_TransferClass_Client, 47, false);

            mapACG_WinningEffectClass_Client = (UniqueInfo.ConfigHashMap<int, TACG_WinningEffectClass_Client>) _is.Read(mapACG_WinningEffectClass_Client, 48, false);

            mapACG_DraftEffectClass_Client = (UniqueInfo.ConfigHashMap<int, TACG_DraftEffectClass_Client>) _is.Read(mapACG_DraftEffectClass_Client, 49, false);

            mapACG_HurtFontClass_Client = (UniqueInfo.ConfigHashMap<int, TACG_HurtFontClass_Client>) _is.Read(mapACG_HurtFontClass_Client, 50, false);

            mapACG_ChessPieceHurtEffectClass_Client = (UniqueInfo.ConfigHashMap<int, TACG_ChessPieceHurtEffectClass_Client>) _is.Read(mapACG_ChessPieceHurtEffectClass_Client, 51, false);

            mapChat_QuickChat_Client = (UniqueInfo.ConfigHashMap<int, TChat_QuickChat_Client>) _is.Read(mapChat_QuickChat_Client, 52, false);

            mapACG_WinEffectConfigClass_Client = (UniqueInfo.ConfigHashMap<int, TACG_WinEffectConfigClass_Client>) _is.Read(mapACG_WinEffectConfigClass_Client, 53, false);

            mapACG_MailAddress_Client = (UniqueInfo.ConfigHashMap<int, TACG_MailAddress_Client>) _is.Read(mapACG_MailAddress_Client, 101, false);

            mapShieldMThreadedRenderingDevice_Client = (UniqueInfo.ConfigHashMap<int, TShieldMThreadedRenderingDevice_Client>) _is.Read(mapShieldMThreadedRenderingDevice_Client, 102, false);

            mapACG_BroadcastTrigger_Client = (UniqueInfo.ConfigHashMap<int, TACG_BroadcastTrigger_Client>) _is.Read(mapACG_BroadcastTrigger_Client, 103, false);

            mapACG_HeroCollection_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeroCollection_Client>) _is.Read(mapACG_HeroCollection_Client, 104, false);

            mapACG_EquipmentCollection_Client = (UniqueInfo.ConfigHashMap<int, TACG_EquipmentCollection_Client>) _is.Read(mapACG_EquipmentCollection_Client, 105, false);

            mapDeviceFPS_Client = (UniqueInfo.ConfigHashMap<int, TDeviceFPS_Client>) _is.Read(mapDeviceFPS_Client, 106, false);

            mapBlockList_Client = (UniqueInfo.ConfigHashMap<int, TBlockList_Client>) _is.Read(mapBlockList_Client, 107, false);

            mapACG_TinyMapDIYResource_Client = (UniqueInfo.ConfigHashMap<int, TACG_TinyMapDIYResource_Client>) _is.Read(mapACG_TinyMapDIYResource_Client, 108, false);

            mapACG_TinyMapDIYCollocation_Client = (UniqueInfo.ConfigHashMap<int, TACG_TinyMapDIYCollocation_Client>) _is.Read(mapACG_TinyMapDIYCollocation_Client, 109, false);

            mapTAC_LimitedHeroKu_Client = (UniqueInfo.ConfigHashMap<int, TTAC_LimitedHeroKu_Client>) _is.Read(mapTAC_LimitedHeroKu_Client, 110, false);

            mapCardSeasonType_Client = (UniqueInfo.ConfigHashMap<int, TCardSeasonType_Client>) _is.Read(mapCardSeasonType_Client, 111, false);

            mapMainHallRankUp_Client = (UniqueInfo.ConfigHashMap<int, TMainHallRankUp_Client>) _is.Read(mapMainHallRankUp_Client, 112, false);

            mapPrivilegedCardExhibitItem_Client = (UniqueInfo.ConfigHashMap<int, TPrivilegedCardExhibitItem_Client>) _is.Read(mapPrivilegedCardExhibitItem_Client, 113, false);

            mapChat_MagicWord_Client = (UniqueInfo.ConfigHashMap<int, TChat_MagicWord_Client>) _is.Read(mapChat_MagicWord_Client, 114, false);

            mapDeviceRanking_Client = (UniqueInfo.ConfigHashMap<int, TDeviceRanking_Client>) _is.Read(mapDeviceRanking_Client, 115, false);

            mapDeviceStatusParameter_Client = (UniqueInfo.ConfigHashMap<int, TDeviceStatusParameter_Client>) _is.Read(mapDeviceStatusParameter_Client, 116, false);

            mapACG_InteractTiny_Client = (UniqueInfo.ConfigHashMap<int, TACG_InteractTiny_Client>) _is.Read(mapACG_InteractTiny_Client, 117, false);

            mapACG_BadLuckProtection_Client = (UniqueInfo.ConfigHashMap<int, TACG_BadLuckProtection_Client>) _is.Read(mapACG_BadLuckProtection_Client, 118, false);

            mapNameCard_Related_Client = (UniqueInfo.ConfigHashMap<int, TNameCard_Related_Client>) _is.Read(mapNameCard_Related_Client, 119, false);

            mapACG_SceneToPlayerNum_Client = (UniqueInfo.ConfigHashMap<int, TACG_SceneToPlayerNum_Client>) _is.Read(mapACG_SceneToPlayerNum_Client, 120, false);

            mapCardCelebrateEffect_Client = (UniqueInfo.ConfigHashMap<int, TCardCelebrateEffect_Client>) _is.Read(mapCardCelebrateEffect_Client, 121, false);

            mapCardBroadcastItem_Client = (UniqueInfo.ConfigHashMap<int, TCardBroadcastItem_Client>) _is.Read(mapCardBroadcastItem_Client, 122, false);

            mapACG_AstralHeroPool_Client = (UniqueInfo.ConfigHashMap<int, TACG_AstralHeroPool_Client>) _is.Read(mapACG_AstralHeroPool_Client, 123, false);

            mapACG_SetController_Client = (UniqueInfo.ConfigHashMap<int, TACG_SetController_Client>) _is.Read(mapACG_SetController_Client, 124, false);

            mapACG_SetResConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_SetResConfig_Client>) _is.Read(mapACG_SetResConfig_Client, 125, false);

            mapCardCollectReportParam_Client = (UniqueInfo.ConfigHashMap<int, TCardCollectReportParam_Client>) _is.Read(mapCardCollectReportParam_Client, 126, false);

            mapACG_AstralDropPool_Client = (UniqueInfo.ConfigHashMap<int, TACG_AstralDropPool_Client>) _is.Read(mapACG_AstralDropPool_Client, 127, false);

            mapACG_HeroExtraRule_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeroExtraRule_Client>) _is.Read(mapACG_HeroExtraRule_Client, 128, false);

            mapACG_BattleEndTag_Client = (UniqueInfo.ConfigHashMap<int, TACG_BattleEndTag_Client>) _is.Read(mapACG_BattleEndTag_Client, 129, false);

            mapCardSet_Client = (UniqueInfo.ConfigHashMap<int, TCardSet_Client>) _is.Read(mapCardSet_Client, 130, false);

            mapLotteryEffect_Client = (UniqueInfo.ConfigHashMap<int, TLotteryEffect_Client>) _is.Read(mapLotteryEffect_Client, 131, false);

            mapACG_HeroEquipMutex_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeroEquipMutex_Client>) _is.Read(mapACG_HeroEquipMutex_Client, 132, false);

            mapACG_TransferCfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_TransferCfg_Client>) _is.Read(mapACG_TransferCfg_Client, 133, false);

            mapACG_TransferChessUnitDisplayCfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_TransferChessUnitDisplayCfg_Client>) _is.Read(mapACG_TransferChessUnitDisplayCfg_Client, 134, false);

            mapDeviceVRS_Client = (UniqueInfo.ConfigHashMap<int, TDeviceVRS_Client>) _is.Read(mapDeviceVRS_Client, 135, false);

            mapACG_KeyTargetDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_KeyTargetDrop_Client>) _is.Read(mapACG_KeyTargetDrop_Client, 136, false);

            mapTAC_Tag_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Tag_V2_Client>) _is.Read(mapTAC_Tag_V2_Client, 137, false);

            mapTAC_AIID_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIID_V2_Client>) _is.Read(mapTAC_AIID_V2_Client, 138, false);

            mapTAC_AIAssignTag_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIAssignTag_V2_Client>) _is.Read(mapTAC_AIAssignTag_V2_Client, 139, false);

            mapTAC_AIHeroCommon_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIHeroCommon_V2_Client>) _is.Read(mapTAC_AIHeroCommon_V2_Client, 140, false);

            mapTAC_AIEquipCommon_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIEquipCommon_V2_Client>) _is.Read(mapTAC_AIEquipCommon_V2_Client, 141, false);

            mapACGFetterLiveCounter_Client = (UniqueInfo.ConfigHashMap<int, TACGFetterLiveCounter_Client>) _is.Read(mapACGFetterLiveCounter_Client, 142, false);

            mapACG_HAHeroBasicConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_HAHeroBasicConfig_Client>) _is.Read(mapACG_HAHeroBasicConfig_Client, 143, false);

            mapACG_HAHeroDropConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_HAHeroDropConfig_Client>) _is.Read(mapACG_HAHeroDropConfig_Client, 144, false);

            mapACG_EquipmentStore_TFT_Dual_Client = (UniqueInfo.ConfigHashMap<int, TACG_EquipmentStore_TFT_Dual_Client>) _is.Read(mapACG_EquipmentStore_TFT_Dual_Client, 145, false);

            mapACG_HASkinConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_HASkinConfig_Client>) _is.Read(mapACG_HASkinConfig_Client, 146, false);

            mapTimeLimit_LP_Cacualte_Config_Client = (UniqueInfo.ConfigHashMap<int, TTimeLimit_LP_Cacualte_Config_Client>) _is.Read(mapTimeLimit_LP_Cacualte_Config_Client, 147, false);

            mapTAC_AIDifficult_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIDifficult_V2_Client>) _is.Read(mapTAC_AIDifficult_V2_Client, 148, false);

            mapTAC_AIConstKey_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIConstKey_V2_Client>) _is.Read(mapTAC_AIConstKey_V2_Client, 149, false);

            mapTAC_AIDropEquipment_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIDropEquipment_V2_Client>) _is.Read(mapTAC_AIDropEquipment_V2_Client, 150, false);

            mapTAC_Tips_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Tips_Client>) _is.Read(mapTAC_Tips_Client, 151, false);

            mapTAC_EmblemConfig_Client = (UniqueInfo.ConfigHashMap<int, TTAC_EmblemConfig_Client>) _is.Read(mapTAC_EmblemConfig_Client, 152, false);

            mapHeroesUniteReward_Client = (UniqueInfo.ConfigHashMap<int, THeroesUniteReward_Client>) _is.Read(mapHeroesUniteReward_Client, 153, false);

            mapTAC_Admin_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Admin_Client>) _is.Read(mapTAC_Admin_Client, 154, false);

            mapACG_CommonTag_Client = (UniqueInfo.ConfigHashMap<int, TACG_CommonTag_Client>) _is.Read(mapACG_CommonTag_Client, 155, false);

            mapACG_CommonGuideFlow_Client = (UniqueInfo.ConfigHashMap<int, TACG_CommonGuideFlow_Client>) _is.Read(mapACG_CommonGuideFlow_Client, 156, false);

            mapACG_CommonGuideStep_Client = (UniqueInfo.ConfigHashMap<int, TACG_CommonGuideStep_Client>) _is.Read(mapACG_CommonGuideStep_Client, 157, false);

            mapACG_CommonGuideTips_Client = (UniqueInfo.ConfigHashMap<int, TACG_CommonGuideTips_Client>) _is.Read(mapACG_CommonGuideTips_Client, 158, false);

            mapACG_EquipLiveCounter_Client = (UniqueInfo.ConfigHashMap<int, TACG_EquipLiveCounter_Client>) _is.Read(mapACG_EquipLiveCounter_Client, 159, false);

            mapACG_TiggerSound_Client = (UniqueInfo.ConfigHashMap<int, TACG_TiggerSound_Client>) _is.Read(mapACG_TiggerSound_Client, 160, false);

            mapTAC_AITestTag_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AITestTag_V2_Client>) _is.Read(mapTAC_AITestTag_V2_Client, 161, false);

            mapTAC_AIRank_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIRank_V2_Client>) _is.Read(mapTAC_AIRank_V2_Client, 162, false);

            mapCardCollectSeasonShow_Client = (UniqueInfo.ConfigHashMap<int, TCardCollectSeasonShow_Client>) _is.Read(mapCardCollectSeasonShow_Client, 163, false);

            mapMainHallLeftRecommand_Client = (UniqueInfo.ConfigHashMap<int, TMainHallLeftRecommand_Client>) _is.Read(mapMainHallLeftRecommand_Client, 164, false);

            mapACG_CelebrateEffectInBattle_Client = (UniqueInfo.ConfigHashMap<int, TACG_CelebrateEffectInBattle_Client>) _is.Read(mapACG_CelebrateEffectInBattle_Client, 165, false);

            mapACG_CelebrateEffectBanCfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_CelebrateEffectBanCfg_Client>) _is.Read(mapACG_CelebrateEffectBanCfg_Client, 166, false);

            mapACG_CorruptedCfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_CorruptedCfg_Client>) _is.Read(mapACG_CorruptedCfg_Client, 167, false);

            mapACG_CorruptedPool_Client = (UniqueInfo.ConfigHashMap<int, TACG_CorruptedPool_Client>) _is.Read(mapACG_CorruptedPool_Client, 168, false);

            mapACG_RoundSelectHeroCfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_RoundSelectHeroCfg_Client>) _is.Read(mapACG_RoundSelectHeroCfg_Client, 169, false);

            mapACG_SpecCollection_Client = (UniqueInfo.ConfigHashMap<int, TACG_SpecCollection_Client>) _is.Read(mapACG_SpecCollection_Client, 170, false);

            mapACG_ClassCollection_Client = (UniqueInfo.ConfigHashMap<int, TACG_ClassCollection_Client>) _is.Read(mapACG_ClassCollection_Client, 171, false);

            mapACG_CelebrateUnitDisplayCfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_CelebrateUnitDisplayCfg_Client>) _is.Read(mapACG_CelebrateUnitDisplayCfg_Client, 172, false);

            mapACG_NewLootSet_Client = (UniqueInfo.ConfigHashMap<int, TACG_NewLootSet_Client>) _is.Read(mapACG_NewLootSet_Client, 173, false);

            mapACG_MonsterGroup_Client = (UniqueInfo.ConfigHashMap<int, TACG_MonsterGroup_Client>) _is.Read(mapACG_MonsterGroup_Client, 174, false);

            mapACG_NewLootNum_Client = (UniqueInfo.ConfigHashMap<int, TACG_NewLootNum_Client>) _is.Read(mapACG_NewLootNum_Client, 175, false);

            mapACG_PublicAlloc_Client = (UniqueInfo.ConfigHashMap<int, TACG_PublicAlloc_Client>) _is.Read(mapACG_PublicAlloc_Client, 176, false);

            mapACG_ItemAlloc_Client = (UniqueInfo.ConfigHashMap<int, TACG_ItemAlloc_Client>) _is.Read(mapACG_ItemAlloc_Client, 177, false);

            mapACG_ItemPushBack_Client = (UniqueInfo.ConfigHashMap<int, TACG_ItemPushBack_Client>) _is.Read(mapACG_ItemPushBack_Client, 178, false);

            mapACG_BonusBox_Client = (UniqueInfo.ConfigHashMap<int, TACG_BonusBox_Client>) _is.Read(mapACG_BonusBox_Client, 179, false);

            mapACG_BonusAlloc_Client = (UniqueInfo.ConfigHashMap<int, TACG_BonusAlloc_Client>) _is.Read(mapACG_BonusAlloc_Client, 180, false);

            mapACG_ValueAlloc_Client = (UniqueInfo.ConfigHashMap<int, TACG_ValueAlloc_Client>) _is.Read(mapACG_ValueAlloc_Client, 181, false);

            mapACG_GalaxyViewConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_GalaxyViewConfig_Client>) _is.Read(mapACG_GalaxyViewConfig_Client, 182, false);

            mapACG_Legend_Client = (UniqueInfo.ConfigHashMap<int, TACG_Legend_Client>) _is.Read(mapACG_Legend_Client, 183, false);

            mapACG_LegendDetail_Client = (UniqueInfo.ConfigHashMap<int, TACG_LegendDetail_Client>) _is.Read(mapACG_LegendDetail_Client, 184, false);

            mapJOCManualBPLevelPic_Client = (UniqueInfo.ConfigHashMap<int, TJOCManualBPLevelPic_Client>) _is.Read(mapJOCManualBPLevelPic_Client, 185, false);

            mapJOCQualifierTask_Client = (UniqueInfo.ConfigHashMap<int, TJOCQualifierTask_Client>) _is.Read(mapJOCQualifierTask_Client, 186, false);

            mapACG_PiltoverDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_PiltoverDrop_Client>) _is.Read(mapACG_PiltoverDrop_Client, 187, false);

            mapACG_NameCard_Client = (UniqueInfo.ConfigHashMap<int, TACG_NameCard_Client>) _is.Read(mapACG_NameCard_Client, 188, false);

            mapTAC_AIGalaxyTreeReplace_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIGalaxyTreeReplace_V2_Client>) _is.Read(mapTAC_AIGalaxyTreeReplace_V2_Client, 189, false);

            mapACG_EquipRecommCategory_Client = (UniqueInfo.ConfigHashMap<int, TACG_EquipRecommCategory_Client>) _is.Read(mapACG_EquipRecommCategory_Client, 190, false);

            mapACG_EquipRecomm_Client = (UniqueInfo.ConfigHashMap<int, TACG_EquipRecomm_Client>) _is.Read(mapACG_EquipRecomm_Client, 191, false);

            mapGuildTips_Client = (UniqueInfo.ConfigHashMap<int, TGuildTips_Client>) _is.Read(mapGuildTips_Client, 192, false);

            mapCardScoreRankInfo_Client = (UniqueInfo.ConfigHashMap<int, TCardScoreRankInfo_Client>) _is.Read(mapCardScoreRankInfo_Client, 194, false);

            mapDeviceWhiteProfile_Client = (UniqueInfo.ConfigHashMap<int, TDeviceWhiteProfile_Client>) _is.Read(mapDeviceWhiteProfile_Client, 195, false);

            mapACG_HexEffect_Client = (UniqueInfo.ConfigHashMap<int, TACG_HexEffect_Client>) _is.Read(mapACG_HexEffect_Client, 196, false);

            mapCardScoreDefaultParam_Client = (UniqueInfo.ConfigHashMap<int, TCardScoreDefaultParam_Client>) _is.Read(mapCardScoreDefaultParam_Client, 197, false);

            mapCardScoreAdvancedParam_Client = (UniqueInfo.ConfigHashMap<int, TCardScoreAdvancedParam_Client>) _is.Read(mapCardScoreAdvancedParam_Client, 198, false);

            mapCasual_LP_Cacualte_Config_Client = (UniqueInfo.ConfigHashMap<int, TCasual_LP_Cacualte_Config_Client>) _is.Read(mapCasual_LP_Cacualte_Config_Client, 199, false);

            mapACGSkillLiveCounter_Client = (UniqueInfo.ConfigHashMap<int, TACGSkillLiveCounter_Client>) _is.Read(mapACGSkillLiveCounter_Client, 200, false);

            mapACG_ButtonSkinCfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_ButtonSkinCfg_Client>) _is.Read(mapACG_ButtonSkinCfg_Client, 201, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(mapDummyExt1_Client, "mapDummyExt1_Client");
            _ds.Display(mapACG_HABasicConfig_Client, "mapACG_HABasicConfig_Client");
            _ds.Display(mapACG_HACondition_Client, "mapACG_HACondition_Client");
            _ds.Display(mapACG_HADropConfig_Client, "mapACG_HADropConfig_Client");
            _ds.Display(mapACG_HADropReward_Client, "mapACG_HADropReward_Client");
            _ds.Display(mapCardInfo_Client, "mapCardInfo_Client");
            _ds.Display(mapCardCollectTask_Client, "mapCardCollectTask_Client");
            _ds.Display(mapCardValue_Client, "mapCardValue_Client");
            _ds.Display(mapCardUpgrade_Client, "mapCardUpgrade_Client");
            _ds.Display(mapCardDropProbability_Client, "mapCardDropProbability_Client");
            _ds.Display(mapACG_TinyHero_Client, "mapACG_TinyHero_Client");
            _ds.Display(mapACG_TinyTag_Client, "mapACG_TinyTag_Client");
            _ds.Display(mapACG_TinyOrigin_Client, "mapACG_TinyOrigin_Client");
            _ds.Display(mapACG_TinyClass_Client, "mapACG_TinyClass_Client");
            _ds.Display(mapACG_MapClass_Client, "mapACG_MapClass_Client");
            _ds.Display(mapACG_AttackEffect_Client, "mapACG_AttackEffect_Client");
            _ds.Display(mapACG_AttackEffectClass_Client, "mapACG_AttackEffectClass_Client");
            _ds.Display(mapACG_TerrainGrid_Client, "mapACG_TerrainGrid_Client");
            _ds.Display(mapACG_Broadcast_Client, "mapACG_Broadcast_Client");
            _ds.Display(mapCollege_Equipment_Client, "mapCollege_Equipment_Client");
            _ds.Display(mapModeNameConfig_Client, "mapModeNameConfig_Client");
            _ds.Display(mapACG_HABattleRules_Client, "mapACG_HABattleRules_Client");
            _ds.Display(mapACG_HABuff_Client, "mapACG_HABuff_Client");
            _ds.Display(mapACG_MercenaryRoll_Client, "mapACG_MercenaryRoll_Client");
            _ds.Display(mapACG_EconomicMechanismCfg_Client, "mapACG_EconomicMechanismCfg_Client");
            _ds.Display(mapACG_YordleDrop_Client, "mapACG_YordleDrop_Client");
            _ds.Display(mapACG_PresentEntry_Client, "mapACG_PresentEntry_Client");
            _ds.Display(mapACG_Glutton_Client, "mapACG_Glutton_Client");
            _ds.Display(mapACG_Glutton_Arr_Client, "mapACG_Glutton_Arr_Client");
            _ds.Display(mapACG_FortuneDrop_Client, "mapACG_FortuneDrop_Client");
            _ds.Display(mapACG_HeroPackageDropConfig_Client, "mapACG_HeroPackageDropConfig_Client");
            _ds.Display(mapACG_HeroStoreConfig_Client, "mapACG_HeroStoreConfig_Client");
            _ds.Display(mapACG_PKGHeroConfig_Client, "mapACG_PKGHeroConfig_Client");
            _ds.Display(mapACG_PKGEquipConfig_Client, "mapACG_PKGEquipConfig_Client");
            _ds.Display(mapACG_PKGHeroPool_Client, "mapACG_PKGHeroPool_Client");
            _ds.Display(mapACG_PKGEquipPool_Client, "mapACG_PKGEquipPool_Client");
            _ds.Display(mapACG_HeavenChooser_Level_Client, "mapACG_HeavenChooser_Level_Client");
            _ds.Display(mapACG_HeavenChooser_Refresh_Client, "mapACG_HeavenChooser_Refresh_Client");
            _ds.Display(mapACG_HeavenChooser_Cfg_Client, "mapACG_HeavenChooser_Cfg_Client");
            _ds.Display(mapACG_FloatText_Client, "mapACG_FloatText_Client");
            _ds.Display(mapDeviceExtremeQuality_Client, "mapDeviceExtremeQuality_Client");
            _ds.Display(mapACG_TinyFetters_Client, "mapACG_TinyFetters_Client");
            _ds.Display(mapACG_HeavenChooser_Arr_Client, "mapACG_HeavenChooser_Arr_Client");
            _ds.Display(mapACG_UserLevel_Client, "mapACG_UserLevel_Client");
            _ds.Display(mapBCRoguelikeGlobal_Client, "mapBCRoguelikeGlobal_Client");
            _ds.Display(mapContest_LP_Cacualte_Config_Client, "mapContest_LP_Cacualte_Config_Client");
            _ds.Display(mapContest_Score_Config_Client, "mapContest_Score_Config_Client");
            _ds.Display(mapACG_TransferClass_Client, "mapACG_TransferClass_Client");
            _ds.Display(mapACG_WinningEffectClass_Client, "mapACG_WinningEffectClass_Client");
            _ds.Display(mapACG_DraftEffectClass_Client, "mapACG_DraftEffectClass_Client");
            _ds.Display(mapACG_HurtFontClass_Client, "mapACG_HurtFontClass_Client");
            _ds.Display(mapACG_ChessPieceHurtEffectClass_Client, "mapACG_ChessPieceHurtEffectClass_Client");
            _ds.Display(mapChat_QuickChat_Client, "mapChat_QuickChat_Client");
            _ds.Display(mapACG_WinEffectConfigClass_Client, "mapACG_WinEffectConfigClass_Client");
            _ds.Display(mapACG_MailAddress_Client, "mapACG_MailAddress_Client");
            _ds.Display(mapShieldMThreadedRenderingDevice_Client, "mapShieldMThreadedRenderingDevice_Client");
            _ds.Display(mapACG_BroadcastTrigger_Client, "mapACG_BroadcastTrigger_Client");
            _ds.Display(mapACG_HeroCollection_Client, "mapACG_HeroCollection_Client");
            _ds.Display(mapACG_EquipmentCollection_Client, "mapACG_EquipmentCollection_Client");
            _ds.Display(mapDeviceFPS_Client, "mapDeviceFPS_Client");
            _ds.Display(mapBlockList_Client, "mapBlockList_Client");
            _ds.Display(mapACG_TinyMapDIYResource_Client, "mapACG_TinyMapDIYResource_Client");
            _ds.Display(mapACG_TinyMapDIYCollocation_Client, "mapACG_TinyMapDIYCollocation_Client");
            _ds.Display(mapTAC_LimitedHeroKu_Client, "mapTAC_LimitedHeroKu_Client");
            _ds.Display(mapCardSeasonType_Client, "mapCardSeasonType_Client");
            _ds.Display(mapMainHallRankUp_Client, "mapMainHallRankUp_Client");
            _ds.Display(mapPrivilegedCardExhibitItem_Client, "mapPrivilegedCardExhibitItem_Client");
            _ds.Display(mapChat_MagicWord_Client, "mapChat_MagicWord_Client");
            _ds.Display(mapDeviceRanking_Client, "mapDeviceRanking_Client");
            _ds.Display(mapDeviceStatusParameter_Client, "mapDeviceStatusParameter_Client");
            _ds.Display(mapACG_InteractTiny_Client, "mapACG_InteractTiny_Client");
            _ds.Display(mapACG_BadLuckProtection_Client, "mapACG_BadLuckProtection_Client");
            _ds.Display(mapNameCard_Related_Client, "mapNameCard_Related_Client");
            _ds.Display(mapACG_SceneToPlayerNum_Client, "mapACG_SceneToPlayerNum_Client");
            _ds.Display(mapCardCelebrateEffect_Client, "mapCardCelebrateEffect_Client");
            _ds.Display(mapCardBroadcastItem_Client, "mapCardBroadcastItem_Client");
            _ds.Display(mapACG_AstralHeroPool_Client, "mapACG_AstralHeroPool_Client");
            _ds.Display(mapACG_SetController_Client, "mapACG_SetController_Client");
            _ds.Display(mapACG_SetResConfig_Client, "mapACG_SetResConfig_Client");
            _ds.Display(mapCardCollectReportParam_Client, "mapCardCollectReportParam_Client");
            _ds.Display(mapACG_AstralDropPool_Client, "mapACG_AstralDropPool_Client");
            _ds.Display(mapACG_HeroExtraRule_Client, "mapACG_HeroExtraRule_Client");
            _ds.Display(mapACG_BattleEndTag_Client, "mapACG_BattleEndTag_Client");
            _ds.Display(mapCardSet_Client, "mapCardSet_Client");
            _ds.Display(mapLotteryEffect_Client, "mapLotteryEffect_Client");
            _ds.Display(mapACG_HeroEquipMutex_Client, "mapACG_HeroEquipMutex_Client");
            _ds.Display(mapACG_TransferCfg_Client, "mapACG_TransferCfg_Client");
            _ds.Display(mapACG_TransferChessUnitDisplayCfg_Client, "mapACG_TransferChessUnitDisplayCfg_Client");
            _ds.Display(mapDeviceVRS_Client, "mapDeviceVRS_Client");
            _ds.Display(mapACG_KeyTargetDrop_Client, "mapACG_KeyTargetDrop_Client");
            _ds.Display(mapTAC_Tag_V2_Client, "mapTAC_Tag_V2_Client");
            _ds.Display(mapTAC_AIID_V2_Client, "mapTAC_AIID_V2_Client");
            _ds.Display(mapTAC_AIAssignTag_V2_Client, "mapTAC_AIAssignTag_V2_Client");
            _ds.Display(mapTAC_AIHeroCommon_V2_Client, "mapTAC_AIHeroCommon_V2_Client");
            _ds.Display(mapTAC_AIEquipCommon_V2_Client, "mapTAC_AIEquipCommon_V2_Client");
            _ds.Display(mapACGFetterLiveCounter_Client, "mapACGFetterLiveCounter_Client");
            _ds.Display(mapACG_HAHeroBasicConfig_Client, "mapACG_HAHeroBasicConfig_Client");
            _ds.Display(mapACG_HAHeroDropConfig_Client, "mapACG_HAHeroDropConfig_Client");
            _ds.Display(mapACG_EquipmentStore_TFT_Dual_Client, "mapACG_EquipmentStore_TFT_Dual_Client");
            _ds.Display(mapACG_HASkinConfig_Client, "mapACG_HASkinConfig_Client");
            _ds.Display(mapTimeLimit_LP_Cacualte_Config_Client, "mapTimeLimit_LP_Cacualte_Config_Client");
            _ds.Display(mapTAC_AIDifficult_V2_Client, "mapTAC_AIDifficult_V2_Client");
            _ds.Display(mapTAC_AIConstKey_V2_Client, "mapTAC_AIConstKey_V2_Client");
            _ds.Display(mapTAC_AIDropEquipment_V2_Client, "mapTAC_AIDropEquipment_V2_Client");
            _ds.Display(mapTAC_Tips_Client, "mapTAC_Tips_Client");
            _ds.Display(mapTAC_EmblemConfig_Client, "mapTAC_EmblemConfig_Client");
            _ds.Display(mapHeroesUniteReward_Client, "mapHeroesUniteReward_Client");
            _ds.Display(mapTAC_Admin_Client, "mapTAC_Admin_Client");
            _ds.Display(mapACG_CommonTag_Client, "mapACG_CommonTag_Client");
            _ds.Display(mapACG_CommonGuideFlow_Client, "mapACG_CommonGuideFlow_Client");
            _ds.Display(mapACG_CommonGuideStep_Client, "mapACG_CommonGuideStep_Client");
            _ds.Display(mapACG_CommonGuideTips_Client, "mapACG_CommonGuideTips_Client");
            _ds.Display(mapACG_EquipLiveCounter_Client, "mapACG_EquipLiveCounter_Client");
            _ds.Display(mapACG_TiggerSound_Client, "mapACG_TiggerSound_Client");
            _ds.Display(mapTAC_AITestTag_V2_Client, "mapTAC_AITestTag_V2_Client");
            _ds.Display(mapTAC_AIRank_V2_Client, "mapTAC_AIRank_V2_Client");
            _ds.Display(mapCardCollectSeasonShow_Client, "mapCardCollectSeasonShow_Client");
            _ds.Display(mapMainHallLeftRecommand_Client, "mapMainHallLeftRecommand_Client");
            _ds.Display(mapACG_CelebrateEffectInBattle_Client, "mapACG_CelebrateEffectInBattle_Client");
            _ds.Display(mapACG_CelebrateEffectBanCfg_Client, "mapACG_CelebrateEffectBanCfg_Client");
            _ds.Display(mapACG_CorruptedCfg_Client, "mapACG_CorruptedCfg_Client");
            _ds.Display(mapACG_CorruptedPool_Client, "mapACG_CorruptedPool_Client");
            _ds.Display(mapACG_RoundSelectHeroCfg_Client, "mapACG_RoundSelectHeroCfg_Client");
            _ds.Display(mapACG_SpecCollection_Client, "mapACG_SpecCollection_Client");
            _ds.Display(mapACG_ClassCollection_Client, "mapACG_ClassCollection_Client");
            _ds.Display(mapACG_CelebrateUnitDisplayCfg_Client, "mapACG_CelebrateUnitDisplayCfg_Client");
            _ds.Display(mapACG_NewLootSet_Client, "mapACG_NewLootSet_Client");
            _ds.Display(mapACG_MonsterGroup_Client, "mapACG_MonsterGroup_Client");
            _ds.Display(mapACG_NewLootNum_Client, "mapACG_NewLootNum_Client");
            _ds.Display(mapACG_PublicAlloc_Client, "mapACG_PublicAlloc_Client");
            _ds.Display(mapACG_ItemAlloc_Client, "mapACG_ItemAlloc_Client");
            _ds.Display(mapACG_ItemPushBack_Client, "mapACG_ItemPushBack_Client");
            _ds.Display(mapACG_BonusBox_Client, "mapACG_BonusBox_Client");
            _ds.Display(mapACG_BonusAlloc_Client, "mapACG_BonusAlloc_Client");
            _ds.Display(mapACG_ValueAlloc_Client, "mapACG_ValueAlloc_Client");
            _ds.Display(mapACG_GalaxyViewConfig_Client, "mapACG_GalaxyViewConfig_Client");
            _ds.Display(mapACG_Legend_Client, "mapACG_Legend_Client");
            _ds.Display(mapACG_LegendDetail_Client, "mapACG_LegendDetail_Client");
            _ds.Display(mapJOCManualBPLevelPic_Client, "mapJOCManualBPLevelPic_Client");
            _ds.Display(mapJOCQualifierTask_Client, "mapJOCQualifierTask_Client");
            _ds.Display(mapACG_PiltoverDrop_Client, "mapACG_PiltoverDrop_Client");
            _ds.Display(mapACG_NameCard_Client, "mapACG_NameCard_Client");
            _ds.Display(mapTAC_AIGalaxyTreeReplace_V2_Client, "mapTAC_AIGalaxyTreeReplace_V2_Client");
            _ds.Display(mapACG_EquipRecommCategory_Client, "mapACG_EquipRecommCategory_Client");
            _ds.Display(mapACG_EquipRecomm_Client, "mapACG_EquipRecomm_Client");
            _ds.Display(mapGuildTips_Client, "mapGuildTips_Client");
            _ds.Display(mapCardScoreRankInfo_Client, "mapCardScoreRankInfo_Client");
            _ds.Display(mapDeviceWhiteProfile_Client, "mapDeviceWhiteProfile_Client");
            _ds.Display(mapACG_HexEffect_Client, "mapACG_HexEffect_Client");
            _ds.Display(mapCardScoreDefaultParam_Client, "mapCardScoreDefaultParam_Client");
            _ds.Display(mapCardScoreAdvancedParam_Client, "mapCardScoreAdvancedParam_Client");
            _ds.Display(mapCasual_LP_Cacualte_Config_Client, "mapCasual_LP_Cacualte_Config_Client");
            _ds.Display(mapACGSkillLiveCounter_Client, "mapACGSkillLiveCounter_Client");
            _ds.Display(mapACG_ButtonSkinCfg_Client, "mapACG_ButtonSkinCfg_Client");
        }

        public override void Clear()
        {
            if (mapDummyExt1_Client != null) mapDummyExt1_Client.Clear();
            if (mapACG_HABasicConfig_Client != null) mapACG_HABasicConfig_Client.Clear();
            if (mapACG_HACondition_Client != null) mapACG_HACondition_Client.Clear();
            if (mapACG_HADropConfig_Client != null) mapACG_HADropConfig_Client.Clear();
            if (mapACG_HADropReward_Client != null) mapACG_HADropReward_Client.Clear();
            if (mapCardInfo_Client != null) mapCardInfo_Client.Clear();
            if (mapCardCollectTask_Client != null) mapCardCollectTask_Client.Clear();
            if (mapCardValue_Client != null) mapCardValue_Client.Clear();
            if (mapCardUpgrade_Client != null) mapCardUpgrade_Client.Clear();
            if (mapCardDropProbability_Client != null) mapCardDropProbability_Client.Clear();
            if (mapACG_TinyHero_Client != null) mapACG_TinyHero_Client.Clear();
            if (mapACG_TinyTag_Client != null) mapACG_TinyTag_Client.Clear();
            if (mapACG_TinyOrigin_Client != null) mapACG_TinyOrigin_Client.Clear();
            if (mapACG_TinyClass_Client != null) mapACG_TinyClass_Client.Clear();
            if (mapACG_MapClass_Client != null) mapACG_MapClass_Client.Clear();
            if (mapACG_AttackEffect_Client != null) mapACG_AttackEffect_Client.Clear();
            if (mapACG_AttackEffectClass_Client != null) mapACG_AttackEffectClass_Client.Clear();
            if (mapACG_TerrainGrid_Client != null) mapACG_TerrainGrid_Client.Clear();
            if (mapACG_Broadcast_Client != null) mapACG_Broadcast_Client.Clear();
            if (mapCollege_Equipment_Client != null) mapCollege_Equipment_Client.Clear();
            if (mapModeNameConfig_Client != null) mapModeNameConfig_Client.Clear();
            if (mapACG_HABattleRules_Client != null) mapACG_HABattleRules_Client.Clear();
            if (mapACG_HABuff_Client != null) mapACG_HABuff_Client.Clear();
            if (mapACG_MercenaryRoll_Client != null) mapACG_MercenaryRoll_Client.Clear();
            if (mapACG_EconomicMechanismCfg_Client != null) mapACG_EconomicMechanismCfg_Client.Clear();
            if (mapACG_YordleDrop_Client != null) mapACG_YordleDrop_Client.Clear();
            if (mapACG_PresentEntry_Client != null) mapACG_PresentEntry_Client.Clear();
            if (mapACG_Glutton_Client != null) mapACG_Glutton_Client.Clear();
            if (mapACG_Glutton_Arr_Client != null) mapACG_Glutton_Arr_Client.Clear();
            if (mapACG_FortuneDrop_Client != null) mapACG_FortuneDrop_Client.Clear();
            if (mapACG_HeroPackageDropConfig_Client != null) mapACG_HeroPackageDropConfig_Client.Clear();
            if (mapACG_HeroStoreConfig_Client != null) mapACG_HeroStoreConfig_Client.Clear();
            if (mapACG_PKGHeroConfig_Client != null) mapACG_PKGHeroConfig_Client.Clear();
            if (mapACG_PKGEquipConfig_Client != null) mapACG_PKGEquipConfig_Client.Clear();
            if (mapACG_PKGHeroPool_Client != null) mapACG_PKGHeroPool_Client.Clear();
            if (mapACG_PKGEquipPool_Client != null) mapACG_PKGEquipPool_Client.Clear();
            if (mapACG_HeavenChooser_Level_Client != null) mapACG_HeavenChooser_Level_Client.Clear();
            if (mapACG_HeavenChooser_Refresh_Client != null) mapACG_HeavenChooser_Refresh_Client.Clear();
            if (mapACG_HeavenChooser_Cfg_Client != null) mapACG_HeavenChooser_Cfg_Client.Clear();
            if (mapACG_FloatText_Client != null) mapACG_FloatText_Client.Clear();
            if (mapDeviceExtremeQuality_Client != null) mapDeviceExtremeQuality_Client.Clear();
            if (mapACG_TinyFetters_Client != null) mapACG_TinyFetters_Client.Clear();
            if (mapACG_HeavenChooser_Arr_Client != null) mapACG_HeavenChooser_Arr_Client.Clear();
            if (mapACG_UserLevel_Client != null) mapACG_UserLevel_Client.Clear();
            if (mapBCRoguelikeGlobal_Client != null) mapBCRoguelikeGlobal_Client.Clear();
            if (mapContest_LP_Cacualte_Config_Client != null) mapContest_LP_Cacualte_Config_Client.Clear();
            if (mapContest_Score_Config_Client != null) mapContest_Score_Config_Client.Clear();
            if (mapACG_TransferClass_Client != null) mapACG_TransferClass_Client.Clear();
            if (mapACG_WinningEffectClass_Client != null) mapACG_WinningEffectClass_Client.Clear();
            if (mapACG_DraftEffectClass_Client != null) mapACG_DraftEffectClass_Client.Clear();
            if (mapACG_HurtFontClass_Client != null) mapACG_HurtFontClass_Client.Clear();
            if (mapACG_ChessPieceHurtEffectClass_Client != null) mapACG_ChessPieceHurtEffectClass_Client.Clear();
            if (mapChat_QuickChat_Client != null) mapChat_QuickChat_Client.Clear();
            if (mapACG_WinEffectConfigClass_Client != null) mapACG_WinEffectConfigClass_Client.Clear();
            if (mapACG_MailAddress_Client != null) mapACG_MailAddress_Client.Clear();
            if (mapShieldMThreadedRenderingDevice_Client != null) mapShieldMThreadedRenderingDevice_Client.Clear();
            if (mapACG_BroadcastTrigger_Client != null) mapACG_BroadcastTrigger_Client.Clear();
            if (mapACG_HeroCollection_Client != null) mapACG_HeroCollection_Client.Clear();
            if (mapACG_EquipmentCollection_Client != null) mapACG_EquipmentCollection_Client.Clear();
            if (mapDeviceFPS_Client != null) mapDeviceFPS_Client.Clear();
            if (mapBlockList_Client != null) mapBlockList_Client.Clear();
            if (mapACG_TinyMapDIYResource_Client != null) mapACG_TinyMapDIYResource_Client.Clear();
            if (mapACG_TinyMapDIYCollocation_Client != null) mapACG_TinyMapDIYCollocation_Client.Clear();
            if (mapTAC_LimitedHeroKu_Client != null) mapTAC_LimitedHeroKu_Client.Clear();
            if (mapCardSeasonType_Client != null) mapCardSeasonType_Client.Clear();
            if (mapMainHallRankUp_Client != null) mapMainHallRankUp_Client.Clear();
            if (mapPrivilegedCardExhibitItem_Client != null) mapPrivilegedCardExhibitItem_Client.Clear();
            if (mapChat_MagicWord_Client != null) mapChat_MagicWord_Client.Clear();
            if (mapDeviceRanking_Client != null) mapDeviceRanking_Client.Clear();
            if (mapDeviceStatusParameter_Client != null) mapDeviceStatusParameter_Client.Clear();
            if (mapACG_InteractTiny_Client != null) mapACG_InteractTiny_Client.Clear();
            if (mapACG_BadLuckProtection_Client != null) mapACG_BadLuckProtection_Client.Clear();
            if (mapNameCard_Related_Client != null) mapNameCard_Related_Client.Clear();
            if (mapACG_SceneToPlayerNum_Client != null) mapACG_SceneToPlayerNum_Client.Clear();
            if (mapCardCelebrateEffect_Client != null) mapCardCelebrateEffect_Client.Clear();
            if (mapCardBroadcastItem_Client != null) mapCardBroadcastItem_Client.Clear();
            if (mapACG_AstralHeroPool_Client != null) mapACG_AstralHeroPool_Client.Clear();
            if (mapACG_SetController_Client != null) mapACG_SetController_Client.Clear();
            if (mapACG_SetResConfig_Client != null) mapACG_SetResConfig_Client.Clear();
            if (mapCardCollectReportParam_Client != null) mapCardCollectReportParam_Client.Clear();
            if (mapACG_AstralDropPool_Client != null) mapACG_AstralDropPool_Client.Clear();
            if (mapACG_HeroExtraRule_Client != null) mapACG_HeroExtraRule_Client.Clear();
            if (mapACG_BattleEndTag_Client != null) mapACG_BattleEndTag_Client.Clear();
            if (mapCardSet_Client != null) mapCardSet_Client.Clear();
            if (mapLotteryEffect_Client != null) mapLotteryEffect_Client.Clear();
            if (mapACG_HeroEquipMutex_Client != null) mapACG_HeroEquipMutex_Client.Clear();
            if (mapACG_TransferCfg_Client != null) mapACG_TransferCfg_Client.Clear();
            if (mapACG_TransferChessUnitDisplayCfg_Client != null) mapACG_TransferChessUnitDisplayCfg_Client.Clear();
            if (mapDeviceVRS_Client != null) mapDeviceVRS_Client.Clear();
            if (mapACG_KeyTargetDrop_Client != null) mapACG_KeyTargetDrop_Client.Clear();
            if (mapTAC_Tag_V2_Client != null) mapTAC_Tag_V2_Client.Clear();
            if (mapTAC_AIID_V2_Client != null) mapTAC_AIID_V2_Client.Clear();
            if (mapTAC_AIAssignTag_V2_Client != null) mapTAC_AIAssignTag_V2_Client.Clear();
            if (mapTAC_AIHeroCommon_V2_Client != null) mapTAC_AIHeroCommon_V2_Client.Clear();
            if (mapTAC_AIEquipCommon_V2_Client != null) mapTAC_AIEquipCommon_V2_Client.Clear();
            if (mapACGFetterLiveCounter_Client != null) mapACGFetterLiveCounter_Client.Clear();
            if (mapACG_HAHeroBasicConfig_Client != null) mapACG_HAHeroBasicConfig_Client.Clear();
            if (mapACG_HAHeroDropConfig_Client != null) mapACG_HAHeroDropConfig_Client.Clear();
            if (mapACG_EquipmentStore_TFT_Dual_Client != null) mapACG_EquipmentStore_TFT_Dual_Client.Clear();
            if (mapACG_HASkinConfig_Client != null) mapACG_HASkinConfig_Client.Clear();
            if (mapTimeLimit_LP_Cacualte_Config_Client != null) mapTimeLimit_LP_Cacualte_Config_Client.Clear();
            if (mapTAC_AIDifficult_V2_Client != null) mapTAC_AIDifficult_V2_Client.Clear();
            if (mapTAC_AIConstKey_V2_Client != null) mapTAC_AIConstKey_V2_Client.Clear();
            if (mapTAC_AIDropEquipment_V2_Client != null) mapTAC_AIDropEquipment_V2_Client.Clear();
            if (mapTAC_Tips_Client != null) mapTAC_Tips_Client.Clear();
            if (mapTAC_EmblemConfig_Client != null) mapTAC_EmblemConfig_Client.Clear();
            if (mapHeroesUniteReward_Client != null) mapHeroesUniteReward_Client.Clear();
            if (mapTAC_Admin_Client != null) mapTAC_Admin_Client.Clear();
            if (mapACG_CommonTag_Client != null) mapACG_CommonTag_Client.Clear();
            if (mapACG_CommonGuideFlow_Client != null) mapACG_CommonGuideFlow_Client.Clear();
            if (mapACG_CommonGuideStep_Client != null) mapACG_CommonGuideStep_Client.Clear();
            if (mapACG_CommonGuideTips_Client != null) mapACG_CommonGuideTips_Client.Clear();
            if (mapACG_EquipLiveCounter_Client != null) mapACG_EquipLiveCounter_Client.Clear();
            if (mapACG_TiggerSound_Client != null) mapACG_TiggerSound_Client.Clear();
            if (mapTAC_AITestTag_V2_Client != null) mapTAC_AITestTag_V2_Client.Clear();
            if (mapTAC_AIRank_V2_Client != null) mapTAC_AIRank_V2_Client.Clear();
            if (mapCardCollectSeasonShow_Client != null) mapCardCollectSeasonShow_Client.Clear();
            if (mapMainHallLeftRecommand_Client != null) mapMainHallLeftRecommand_Client.Clear();
            if (mapACG_CelebrateEffectInBattle_Client != null) mapACG_CelebrateEffectInBattle_Client.Clear();
            if (mapACG_CelebrateEffectBanCfg_Client != null) mapACG_CelebrateEffectBanCfg_Client.Clear();
            if (mapACG_CorruptedCfg_Client != null) mapACG_CorruptedCfg_Client.Clear();
            if (mapACG_CorruptedPool_Client != null) mapACG_CorruptedPool_Client.Clear();
            if (mapACG_RoundSelectHeroCfg_Client != null) mapACG_RoundSelectHeroCfg_Client.Clear();
            if (mapACG_SpecCollection_Client != null) mapACG_SpecCollection_Client.Clear();
            if (mapACG_ClassCollection_Client != null) mapACG_ClassCollection_Client.Clear();
            if (mapACG_CelebrateUnitDisplayCfg_Client != null) mapACG_CelebrateUnitDisplayCfg_Client.Clear();
            if (mapACG_NewLootSet_Client != null) mapACG_NewLootSet_Client.Clear();
            if (mapACG_MonsterGroup_Client != null) mapACG_MonsterGroup_Client.Clear();
            if (mapACG_NewLootNum_Client != null) mapACG_NewLootNum_Client.Clear();
            if (mapACG_PublicAlloc_Client != null) mapACG_PublicAlloc_Client.Clear();
            if (mapACG_ItemAlloc_Client != null) mapACG_ItemAlloc_Client.Clear();
            if (mapACG_ItemPushBack_Client != null) mapACG_ItemPushBack_Client.Clear();
            if (mapACG_BonusBox_Client != null) mapACG_BonusBox_Client.Clear();
            if (mapACG_BonusAlloc_Client != null) mapACG_BonusAlloc_Client.Clear();
            if (mapACG_ValueAlloc_Client != null) mapACG_ValueAlloc_Client.Clear();
            if (mapACG_GalaxyViewConfig_Client != null) mapACG_GalaxyViewConfig_Client.Clear();
            if (mapACG_Legend_Client != null) mapACG_Legend_Client.Clear();
            if (mapACG_LegendDetail_Client != null) mapACG_LegendDetail_Client.Clear();
            if (mapJOCManualBPLevelPic_Client != null) mapJOCManualBPLevelPic_Client.Clear();
            if (mapJOCQualifierTask_Client != null) mapJOCQualifierTask_Client.Clear();
            if (mapACG_PiltoverDrop_Client != null) mapACG_PiltoverDrop_Client.Clear();
            if (mapACG_NameCard_Client != null) mapACG_NameCard_Client.Clear();
            if (mapTAC_AIGalaxyTreeReplace_V2_Client != null) mapTAC_AIGalaxyTreeReplace_V2_Client.Clear();
            if (mapACG_EquipRecommCategory_Client != null) mapACG_EquipRecommCategory_Client.Clear();
            if (mapACG_EquipRecomm_Client != null) mapACG_EquipRecomm_Client.Clear();
            if (mapGuildTips_Client != null) mapGuildTips_Client.Clear();
            if (mapCardScoreRankInfo_Client != null) mapCardScoreRankInfo_Client.Clear();
            if (mapDeviceWhiteProfile_Client != null) mapDeviceWhiteProfile_Client.Clear();
            if (mapACG_HexEffect_Client != null) mapACG_HexEffect_Client.Clear();
            if (mapCardScoreDefaultParam_Client != null) mapCardScoreDefaultParam_Client.Clear();
            if (mapCardScoreAdvancedParam_Client != null) mapCardScoreAdvancedParam_Client.Clear();
            if (mapCasual_LP_Cacualte_Config_Client != null) mapCasual_LP_Cacualte_Config_Client.Clear();
            if (mapACGSkillLiveCounter_Client != null) mapACGSkillLiveCounter_Client.Clear();
            if (mapACG_ButtonSkinCfg_Client != null) mapACG_ButtonSkinCfg_Client.Clear();
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAllGameCfgClientExt1();
            copied.mapDummyExt1_Client = (UniqueInfo.ConfigHashMap<int, TDummyExt1_Client>)JceUtil.DeepClone(this.mapDummyExt1_Client);
            copied.mapACG_HABasicConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_HABasicConfig_Client>)JceUtil.DeepClone(this.mapACG_HABasicConfig_Client);
            copied.mapACG_HACondition_Client = (UniqueInfo.ConfigHashMap<int, TACG_HACondition_Client>)JceUtil.DeepClone(this.mapACG_HACondition_Client);
            copied.mapACG_HADropConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_HADropConfig_Client>)JceUtil.DeepClone(this.mapACG_HADropConfig_Client);
            copied.mapACG_HADropReward_Client = (UniqueInfo.ConfigHashMap<int, TACG_HADropReward_Client>)JceUtil.DeepClone(this.mapACG_HADropReward_Client);
            copied.mapCardInfo_Client = (UniqueInfo.ConfigHashMap<int, TCardInfo_Client>)JceUtil.DeepClone(this.mapCardInfo_Client);
            copied.mapCardCollectTask_Client = (UniqueInfo.ConfigHashMap<int, TCardCollectTask_Client>)JceUtil.DeepClone(this.mapCardCollectTask_Client);
            copied.mapCardValue_Client = (UniqueInfo.ConfigHashMap<int, TCardValue_Client>)JceUtil.DeepClone(this.mapCardValue_Client);
            copied.mapCardUpgrade_Client = (UniqueInfo.ConfigHashMap<int, TCardUpgrade_Client>)JceUtil.DeepClone(this.mapCardUpgrade_Client);
            copied.mapCardDropProbability_Client = (UniqueInfo.ConfigHashMap<int, TCardDropProbability_Client>)JceUtil.DeepClone(this.mapCardDropProbability_Client);
            copied.mapACG_TinyHero_Client = (UniqueInfo.ConfigHashMap<int, TACG_TinyHero_Client>)JceUtil.DeepClone(this.mapACG_TinyHero_Client);
            copied.mapACG_TinyTag_Client = (UniqueInfo.ConfigHashMap<int, TACG_TinyTag_Client>)JceUtil.DeepClone(this.mapACG_TinyTag_Client);
            copied.mapACG_TinyOrigin_Client = (UniqueInfo.ConfigHashMap<int, TACG_TinyOrigin_Client>)JceUtil.DeepClone(this.mapACG_TinyOrigin_Client);
            copied.mapACG_TinyClass_Client = (UniqueInfo.ConfigHashMap<int, TACG_TinyClass_Client>)JceUtil.DeepClone(this.mapACG_TinyClass_Client);
            copied.mapACG_MapClass_Client = (UniqueInfo.ConfigHashMap<int, TACG_MapClass_Client>)JceUtil.DeepClone(this.mapACG_MapClass_Client);
            copied.mapACG_AttackEffect_Client = (UniqueInfo.ConfigHashMap<int, TACG_AttackEffect_Client>)JceUtil.DeepClone(this.mapACG_AttackEffect_Client);
            copied.mapACG_AttackEffectClass_Client = (UniqueInfo.ConfigHashMap<int, TACG_AttackEffectClass_Client>)JceUtil.DeepClone(this.mapACG_AttackEffectClass_Client);
            copied.mapACG_TerrainGrid_Client = (UniqueInfo.ConfigHashMap<int, TACG_TerrainGrid_Client>)JceUtil.DeepClone(this.mapACG_TerrainGrid_Client);
            copied.mapACG_Broadcast_Client = (UniqueInfo.ConfigHashMap<int, TACG_Broadcast_Client>)JceUtil.DeepClone(this.mapACG_Broadcast_Client);
            copied.mapCollege_Equipment_Client = (UniqueInfo.ConfigHashMap<int, TCollege_Equipment_Client>)JceUtil.DeepClone(this.mapCollege_Equipment_Client);
            copied.mapModeNameConfig_Client = (UniqueInfo.ConfigHashMap<int, TModeNameConfig_Client>)JceUtil.DeepClone(this.mapModeNameConfig_Client);
            copied.mapACG_HABattleRules_Client = (UniqueInfo.ConfigHashMap<int, TACG_HABattleRules_Client>)JceUtil.DeepClone(this.mapACG_HABattleRules_Client);
            copied.mapACG_HABuff_Client = (UniqueInfo.ConfigHashMap<int, TACG_HABuff_Client>)JceUtil.DeepClone(this.mapACG_HABuff_Client);
            copied.mapACG_MercenaryRoll_Client = (UniqueInfo.ConfigHashMap<int, TACG_MercenaryRoll_Client>)JceUtil.DeepClone(this.mapACG_MercenaryRoll_Client);
            copied.mapACG_EconomicMechanismCfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_EconomicMechanismCfg_Client>)JceUtil.DeepClone(this.mapACG_EconomicMechanismCfg_Client);
            copied.mapACG_YordleDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_YordleDrop_Client>)JceUtil.DeepClone(this.mapACG_YordleDrop_Client);
            copied.mapACG_PresentEntry_Client = (UniqueInfo.ConfigHashMap<int, TACG_PresentEntry_Client>)JceUtil.DeepClone(this.mapACG_PresentEntry_Client);
            copied.mapACG_Glutton_Client = (UniqueInfo.ConfigHashMap<int, TACG_Glutton_Client>)JceUtil.DeepClone(this.mapACG_Glutton_Client);
            copied.mapACG_Glutton_Arr_Client = (UniqueInfo.ConfigHashMap<int, TACG_Glutton_Arr_Client>)JceUtil.DeepClone(this.mapACG_Glutton_Arr_Client);
            copied.mapACG_FortuneDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_FortuneDrop_Client>)JceUtil.DeepClone(this.mapACG_FortuneDrop_Client);
            copied.mapACG_HeroPackageDropConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeroPackageDropConfig_Client>)JceUtil.DeepClone(this.mapACG_HeroPackageDropConfig_Client);
            copied.mapACG_HeroStoreConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeroStoreConfig_Client>)JceUtil.DeepClone(this.mapACG_HeroStoreConfig_Client);
            copied.mapACG_PKGHeroConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_PKGHeroConfig_Client>)JceUtil.DeepClone(this.mapACG_PKGHeroConfig_Client);
            copied.mapACG_PKGEquipConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_PKGEquipConfig_Client>)JceUtil.DeepClone(this.mapACG_PKGEquipConfig_Client);
            copied.mapACG_PKGHeroPool_Client = (UniqueInfo.ConfigHashMap<int, TACG_PKGHeroPool_Client>)JceUtil.DeepClone(this.mapACG_PKGHeroPool_Client);
            copied.mapACG_PKGEquipPool_Client = (UniqueInfo.ConfigHashMap<int, TACG_PKGEquipPool_Client>)JceUtil.DeepClone(this.mapACG_PKGEquipPool_Client);
            copied.mapACG_HeavenChooser_Level_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeavenChooser_Level_Client>)JceUtil.DeepClone(this.mapACG_HeavenChooser_Level_Client);
            copied.mapACG_HeavenChooser_Refresh_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeavenChooser_Refresh_Client>)JceUtil.DeepClone(this.mapACG_HeavenChooser_Refresh_Client);
            copied.mapACG_HeavenChooser_Cfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeavenChooser_Cfg_Client>)JceUtil.DeepClone(this.mapACG_HeavenChooser_Cfg_Client);
            copied.mapACG_FloatText_Client = (UniqueInfo.ConfigHashMap<int, TACG_FloatText_Client>)JceUtil.DeepClone(this.mapACG_FloatText_Client);
            copied.mapDeviceExtremeQuality_Client = (UniqueInfo.ConfigHashMap<int, TDeviceExtremeQuality_Client>)JceUtil.DeepClone(this.mapDeviceExtremeQuality_Client);
            copied.mapACG_TinyFetters_Client = (UniqueInfo.ConfigHashMap<int, TACG_TinyFetters_Client>)JceUtil.DeepClone(this.mapACG_TinyFetters_Client);
            copied.mapACG_HeavenChooser_Arr_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeavenChooser_Arr_Client>)JceUtil.DeepClone(this.mapACG_HeavenChooser_Arr_Client);
            copied.mapACG_UserLevel_Client = (UniqueInfo.ConfigHashMap<int, TACG_UserLevel_Client>)JceUtil.DeepClone(this.mapACG_UserLevel_Client);
            copied.mapBCRoguelikeGlobal_Client = (UniqueInfo.ConfigHashMap<int, TBCRoguelikeGlobal_Client>)JceUtil.DeepClone(this.mapBCRoguelikeGlobal_Client);
            copied.mapContest_LP_Cacualte_Config_Client = (UniqueInfo.ConfigHashMap<int, TContest_LP_Cacualte_Config_Client>)JceUtil.DeepClone(this.mapContest_LP_Cacualte_Config_Client);
            copied.mapContest_Score_Config_Client = (UniqueInfo.ConfigHashMap<int, TContest_Score_Config_Client>)JceUtil.DeepClone(this.mapContest_Score_Config_Client);
            copied.mapACG_TransferClass_Client = (UniqueInfo.ConfigHashMap<int, TACG_TransferClass_Client>)JceUtil.DeepClone(this.mapACG_TransferClass_Client);
            copied.mapACG_WinningEffectClass_Client = (UniqueInfo.ConfigHashMap<int, TACG_WinningEffectClass_Client>)JceUtil.DeepClone(this.mapACG_WinningEffectClass_Client);
            copied.mapACG_DraftEffectClass_Client = (UniqueInfo.ConfigHashMap<int, TACG_DraftEffectClass_Client>)JceUtil.DeepClone(this.mapACG_DraftEffectClass_Client);
            copied.mapACG_HurtFontClass_Client = (UniqueInfo.ConfigHashMap<int, TACG_HurtFontClass_Client>)JceUtil.DeepClone(this.mapACG_HurtFontClass_Client);
            copied.mapACG_ChessPieceHurtEffectClass_Client = (UniqueInfo.ConfigHashMap<int, TACG_ChessPieceHurtEffectClass_Client>)JceUtil.DeepClone(this.mapACG_ChessPieceHurtEffectClass_Client);
            copied.mapChat_QuickChat_Client = (UniqueInfo.ConfigHashMap<int, TChat_QuickChat_Client>)JceUtil.DeepClone(this.mapChat_QuickChat_Client);
            copied.mapACG_WinEffectConfigClass_Client = (UniqueInfo.ConfigHashMap<int, TACG_WinEffectConfigClass_Client>)JceUtil.DeepClone(this.mapACG_WinEffectConfigClass_Client);
            copied.mapACG_MailAddress_Client = (UniqueInfo.ConfigHashMap<int, TACG_MailAddress_Client>)JceUtil.DeepClone(this.mapACG_MailAddress_Client);
            copied.mapShieldMThreadedRenderingDevice_Client = (UniqueInfo.ConfigHashMap<int, TShieldMThreadedRenderingDevice_Client>)JceUtil.DeepClone(this.mapShieldMThreadedRenderingDevice_Client);
            copied.mapACG_BroadcastTrigger_Client = (UniqueInfo.ConfigHashMap<int, TACG_BroadcastTrigger_Client>)JceUtil.DeepClone(this.mapACG_BroadcastTrigger_Client);
            copied.mapACG_HeroCollection_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeroCollection_Client>)JceUtil.DeepClone(this.mapACG_HeroCollection_Client);
            copied.mapACG_EquipmentCollection_Client = (UniqueInfo.ConfigHashMap<int, TACG_EquipmentCollection_Client>)JceUtil.DeepClone(this.mapACG_EquipmentCollection_Client);
            copied.mapDeviceFPS_Client = (UniqueInfo.ConfigHashMap<int, TDeviceFPS_Client>)JceUtil.DeepClone(this.mapDeviceFPS_Client);
            copied.mapBlockList_Client = (UniqueInfo.ConfigHashMap<int, TBlockList_Client>)JceUtil.DeepClone(this.mapBlockList_Client);
            copied.mapACG_TinyMapDIYResource_Client = (UniqueInfo.ConfigHashMap<int, TACG_TinyMapDIYResource_Client>)JceUtil.DeepClone(this.mapACG_TinyMapDIYResource_Client);
            copied.mapACG_TinyMapDIYCollocation_Client = (UniqueInfo.ConfigHashMap<int, TACG_TinyMapDIYCollocation_Client>)JceUtil.DeepClone(this.mapACG_TinyMapDIYCollocation_Client);
            copied.mapTAC_LimitedHeroKu_Client = (UniqueInfo.ConfigHashMap<int, TTAC_LimitedHeroKu_Client>)JceUtil.DeepClone(this.mapTAC_LimitedHeroKu_Client);
            copied.mapCardSeasonType_Client = (UniqueInfo.ConfigHashMap<int, TCardSeasonType_Client>)JceUtil.DeepClone(this.mapCardSeasonType_Client);
            copied.mapMainHallRankUp_Client = (UniqueInfo.ConfigHashMap<int, TMainHallRankUp_Client>)JceUtil.DeepClone(this.mapMainHallRankUp_Client);
            copied.mapPrivilegedCardExhibitItem_Client = (UniqueInfo.ConfigHashMap<int, TPrivilegedCardExhibitItem_Client>)JceUtil.DeepClone(this.mapPrivilegedCardExhibitItem_Client);
            copied.mapChat_MagicWord_Client = (UniqueInfo.ConfigHashMap<int, TChat_MagicWord_Client>)JceUtil.DeepClone(this.mapChat_MagicWord_Client);
            copied.mapDeviceRanking_Client = (UniqueInfo.ConfigHashMap<int, TDeviceRanking_Client>)JceUtil.DeepClone(this.mapDeviceRanking_Client);
            copied.mapDeviceStatusParameter_Client = (UniqueInfo.ConfigHashMap<int, TDeviceStatusParameter_Client>)JceUtil.DeepClone(this.mapDeviceStatusParameter_Client);
            copied.mapACG_InteractTiny_Client = (UniqueInfo.ConfigHashMap<int, TACG_InteractTiny_Client>)JceUtil.DeepClone(this.mapACG_InteractTiny_Client);
            copied.mapACG_BadLuckProtection_Client = (UniqueInfo.ConfigHashMap<int, TACG_BadLuckProtection_Client>)JceUtil.DeepClone(this.mapACG_BadLuckProtection_Client);
            copied.mapNameCard_Related_Client = (UniqueInfo.ConfigHashMap<int, TNameCard_Related_Client>)JceUtil.DeepClone(this.mapNameCard_Related_Client);
            copied.mapACG_SceneToPlayerNum_Client = (UniqueInfo.ConfigHashMap<int, TACG_SceneToPlayerNum_Client>)JceUtil.DeepClone(this.mapACG_SceneToPlayerNum_Client);
            copied.mapCardCelebrateEffect_Client = (UniqueInfo.ConfigHashMap<int, TCardCelebrateEffect_Client>)JceUtil.DeepClone(this.mapCardCelebrateEffect_Client);
            copied.mapCardBroadcastItem_Client = (UniqueInfo.ConfigHashMap<int, TCardBroadcastItem_Client>)JceUtil.DeepClone(this.mapCardBroadcastItem_Client);
            copied.mapACG_AstralHeroPool_Client = (UniqueInfo.ConfigHashMap<int, TACG_AstralHeroPool_Client>)JceUtil.DeepClone(this.mapACG_AstralHeroPool_Client);
            copied.mapACG_SetController_Client = (UniqueInfo.ConfigHashMap<int, TACG_SetController_Client>)JceUtil.DeepClone(this.mapACG_SetController_Client);
            copied.mapACG_SetResConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_SetResConfig_Client>)JceUtil.DeepClone(this.mapACG_SetResConfig_Client);
            copied.mapCardCollectReportParam_Client = (UniqueInfo.ConfigHashMap<int, TCardCollectReportParam_Client>)JceUtil.DeepClone(this.mapCardCollectReportParam_Client);
            copied.mapACG_AstralDropPool_Client = (UniqueInfo.ConfigHashMap<int, TACG_AstralDropPool_Client>)JceUtil.DeepClone(this.mapACG_AstralDropPool_Client);
            copied.mapACG_HeroExtraRule_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeroExtraRule_Client>)JceUtil.DeepClone(this.mapACG_HeroExtraRule_Client);
            copied.mapACG_BattleEndTag_Client = (UniqueInfo.ConfigHashMap<int, TACG_BattleEndTag_Client>)JceUtil.DeepClone(this.mapACG_BattleEndTag_Client);
            copied.mapCardSet_Client = (UniqueInfo.ConfigHashMap<int, TCardSet_Client>)JceUtil.DeepClone(this.mapCardSet_Client);
            copied.mapLotteryEffect_Client = (UniqueInfo.ConfigHashMap<int, TLotteryEffect_Client>)JceUtil.DeepClone(this.mapLotteryEffect_Client);
            copied.mapACG_HeroEquipMutex_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeroEquipMutex_Client>)JceUtil.DeepClone(this.mapACG_HeroEquipMutex_Client);
            copied.mapACG_TransferCfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_TransferCfg_Client>)JceUtil.DeepClone(this.mapACG_TransferCfg_Client);
            copied.mapACG_TransferChessUnitDisplayCfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_TransferChessUnitDisplayCfg_Client>)JceUtil.DeepClone(this.mapACG_TransferChessUnitDisplayCfg_Client);
            copied.mapDeviceVRS_Client = (UniqueInfo.ConfigHashMap<int, TDeviceVRS_Client>)JceUtil.DeepClone(this.mapDeviceVRS_Client);
            copied.mapACG_KeyTargetDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_KeyTargetDrop_Client>)JceUtil.DeepClone(this.mapACG_KeyTargetDrop_Client);
            copied.mapTAC_Tag_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Tag_V2_Client>)JceUtil.DeepClone(this.mapTAC_Tag_V2_Client);
            copied.mapTAC_AIID_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIID_V2_Client>)JceUtil.DeepClone(this.mapTAC_AIID_V2_Client);
            copied.mapTAC_AIAssignTag_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIAssignTag_V2_Client>)JceUtil.DeepClone(this.mapTAC_AIAssignTag_V2_Client);
            copied.mapTAC_AIHeroCommon_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIHeroCommon_V2_Client>)JceUtil.DeepClone(this.mapTAC_AIHeroCommon_V2_Client);
            copied.mapTAC_AIEquipCommon_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIEquipCommon_V2_Client>)JceUtil.DeepClone(this.mapTAC_AIEquipCommon_V2_Client);
            copied.mapACGFetterLiveCounter_Client = (UniqueInfo.ConfigHashMap<int, TACGFetterLiveCounter_Client>)JceUtil.DeepClone(this.mapACGFetterLiveCounter_Client);
            copied.mapACG_HAHeroBasicConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_HAHeroBasicConfig_Client>)JceUtil.DeepClone(this.mapACG_HAHeroBasicConfig_Client);
            copied.mapACG_HAHeroDropConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_HAHeroDropConfig_Client>)JceUtil.DeepClone(this.mapACG_HAHeroDropConfig_Client);
            copied.mapACG_EquipmentStore_TFT_Dual_Client = (UniqueInfo.ConfigHashMap<int, TACG_EquipmentStore_TFT_Dual_Client>)JceUtil.DeepClone(this.mapACG_EquipmentStore_TFT_Dual_Client);
            copied.mapACG_HASkinConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_HASkinConfig_Client>)JceUtil.DeepClone(this.mapACG_HASkinConfig_Client);
            copied.mapTimeLimit_LP_Cacualte_Config_Client = (UniqueInfo.ConfigHashMap<int, TTimeLimit_LP_Cacualte_Config_Client>)JceUtil.DeepClone(this.mapTimeLimit_LP_Cacualte_Config_Client);
            copied.mapTAC_AIDifficult_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIDifficult_V2_Client>)JceUtil.DeepClone(this.mapTAC_AIDifficult_V2_Client);
            copied.mapTAC_AIConstKey_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIConstKey_V2_Client>)JceUtil.DeepClone(this.mapTAC_AIConstKey_V2_Client);
            copied.mapTAC_AIDropEquipment_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIDropEquipment_V2_Client>)JceUtil.DeepClone(this.mapTAC_AIDropEquipment_V2_Client);
            copied.mapTAC_Tips_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Tips_Client>)JceUtil.DeepClone(this.mapTAC_Tips_Client);
            copied.mapTAC_EmblemConfig_Client = (UniqueInfo.ConfigHashMap<int, TTAC_EmblemConfig_Client>)JceUtil.DeepClone(this.mapTAC_EmblemConfig_Client);
            copied.mapHeroesUniteReward_Client = (UniqueInfo.ConfigHashMap<int, THeroesUniteReward_Client>)JceUtil.DeepClone(this.mapHeroesUniteReward_Client);
            copied.mapTAC_Admin_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Admin_Client>)JceUtil.DeepClone(this.mapTAC_Admin_Client);
            copied.mapACG_CommonTag_Client = (UniqueInfo.ConfigHashMap<int, TACG_CommonTag_Client>)JceUtil.DeepClone(this.mapACG_CommonTag_Client);
            copied.mapACG_CommonGuideFlow_Client = (UniqueInfo.ConfigHashMap<int, TACG_CommonGuideFlow_Client>)JceUtil.DeepClone(this.mapACG_CommonGuideFlow_Client);
            copied.mapACG_CommonGuideStep_Client = (UniqueInfo.ConfigHashMap<int, TACG_CommonGuideStep_Client>)JceUtil.DeepClone(this.mapACG_CommonGuideStep_Client);
            copied.mapACG_CommonGuideTips_Client = (UniqueInfo.ConfigHashMap<int, TACG_CommonGuideTips_Client>)JceUtil.DeepClone(this.mapACG_CommonGuideTips_Client);
            copied.mapACG_EquipLiveCounter_Client = (UniqueInfo.ConfigHashMap<int, TACG_EquipLiveCounter_Client>)JceUtil.DeepClone(this.mapACG_EquipLiveCounter_Client);
            copied.mapACG_TiggerSound_Client = (UniqueInfo.ConfigHashMap<int, TACG_TiggerSound_Client>)JceUtil.DeepClone(this.mapACG_TiggerSound_Client);
            copied.mapTAC_AITestTag_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AITestTag_V2_Client>)JceUtil.DeepClone(this.mapTAC_AITestTag_V2_Client);
            copied.mapTAC_AIRank_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIRank_V2_Client>)JceUtil.DeepClone(this.mapTAC_AIRank_V2_Client);
            copied.mapCardCollectSeasonShow_Client = (UniqueInfo.ConfigHashMap<int, TCardCollectSeasonShow_Client>)JceUtil.DeepClone(this.mapCardCollectSeasonShow_Client);
            copied.mapMainHallLeftRecommand_Client = (UniqueInfo.ConfigHashMap<int, TMainHallLeftRecommand_Client>)JceUtil.DeepClone(this.mapMainHallLeftRecommand_Client);
            copied.mapACG_CelebrateEffectInBattle_Client = (UniqueInfo.ConfigHashMap<int, TACG_CelebrateEffectInBattle_Client>)JceUtil.DeepClone(this.mapACG_CelebrateEffectInBattle_Client);
            copied.mapACG_CelebrateEffectBanCfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_CelebrateEffectBanCfg_Client>)JceUtil.DeepClone(this.mapACG_CelebrateEffectBanCfg_Client);
            copied.mapACG_CorruptedCfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_CorruptedCfg_Client>)JceUtil.DeepClone(this.mapACG_CorruptedCfg_Client);
            copied.mapACG_CorruptedPool_Client = (UniqueInfo.ConfigHashMap<int, TACG_CorruptedPool_Client>)JceUtil.DeepClone(this.mapACG_CorruptedPool_Client);
            copied.mapACG_RoundSelectHeroCfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_RoundSelectHeroCfg_Client>)JceUtil.DeepClone(this.mapACG_RoundSelectHeroCfg_Client);
            copied.mapACG_SpecCollection_Client = (UniqueInfo.ConfigHashMap<int, TACG_SpecCollection_Client>)JceUtil.DeepClone(this.mapACG_SpecCollection_Client);
            copied.mapACG_ClassCollection_Client = (UniqueInfo.ConfigHashMap<int, TACG_ClassCollection_Client>)JceUtil.DeepClone(this.mapACG_ClassCollection_Client);
            copied.mapACG_CelebrateUnitDisplayCfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_CelebrateUnitDisplayCfg_Client>)JceUtil.DeepClone(this.mapACG_CelebrateUnitDisplayCfg_Client);
            copied.mapACG_NewLootSet_Client = (UniqueInfo.ConfigHashMap<int, TACG_NewLootSet_Client>)JceUtil.DeepClone(this.mapACG_NewLootSet_Client);
            copied.mapACG_MonsterGroup_Client = (UniqueInfo.ConfigHashMap<int, TACG_MonsterGroup_Client>)JceUtil.DeepClone(this.mapACG_MonsterGroup_Client);
            copied.mapACG_NewLootNum_Client = (UniqueInfo.ConfigHashMap<int, TACG_NewLootNum_Client>)JceUtil.DeepClone(this.mapACG_NewLootNum_Client);
            copied.mapACG_PublicAlloc_Client = (UniqueInfo.ConfigHashMap<int, TACG_PublicAlloc_Client>)JceUtil.DeepClone(this.mapACG_PublicAlloc_Client);
            copied.mapACG_ItemAlloc_Client = (UniqueInfo.ConfigHashMap<int, TACG_ItemAlloc_Client>)JceUtil.DeepClone(this.mapACG_ItemAlloc_Client);
            copied.mapACG_ItemPushBack_Client = (UniqueInfo.ConfigHashMap<int, TACG_ItemPushBack_Client>)JceUtil.DeepClone(this.mapACG_ItemPushBack_Client);
            copied.mapACG_BonusBox_Client = (UniqueInfo.ConfigHashMap<int, TACG_BonusBox_Client>)JceUtil.DeepClone(this.mapACG_BonusBox_Client);
            copied.mapACG_BonusAlloc_Client = (UniqueInfo.ConfigHashMap<int, TACG_BonusAlloc_Client>)JceUtil.DeepClone(this.mapACG_BonusAlloc_Client);
            copied.mapACG_ValueAlloc_Client = (UniqueInfo.ConfigHashMap<int, TACG_ValueAlloc_Client>)JceUtil.DeepClone(this.mapACG_ValueAlloc_Client);
            copied.mapACG_GalaxyViewConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_GalaxyViewConfig_Client>)JceUtil.DeepClone(this.mapACG_GalaxyViewConfig_Client);
            copied.mapACG_Legend_Client = (UniqueInfo.ConfigHashMap<int, TACG_Legend_Client>)JceUtil.DeepClone(this.mapACG_Legend_Client);
            copied.mapACG_LegendDetail_Client = (UniqueInfo.ConfigHashMap<int, TACG_LegendDetail_Client>)JceUtil.DeepClone(this.mapACG_LegendDetail_Client);
            copied.mapJOCManualBPLevelPic_Client = (UniqueInfo.ConfigHashMap<int, TJOCManualBPLevelPic_Client>)JceUtil.DeepClone(this.mapJOCManualBPLevelPic_Client);
            copied.mapJOCQualifierTask_Client = (UniqueInfo.ConfigHashMap<int, TJOCQualifierTask_Client>)JceUtil.DeepClone(this.mapJOCQualifierTask_Client);
            copied.mapACG_PiltoverDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_PiltoverDrop_Client>)JceUtil.DeepClone(this.mapACG_PiltoverDrop_Client);
            copied.mapACG_NameCard_Client = (UniqueInfo.ConfigHashMap<int, TACG_NameCard_Client>)JceUtil.DeepClone(this.mapACG_NameCard_Client);
            copied.mapTAC_AIGalaxyTreeReplace_V2_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIGalaxyTreeReplace_V2_Client>)JceUtil.DeepClone(this.mapTAC_AIGalaxyTreeReplace_V2_Client);
            copied.mapACG_EquipRecommCategory_Client = (UniqueInfo.ConfigHashMap<int, TACG_EquipRecommCategory_Client>)JceUtil.DeepClone(this.mapACG_EquipRecommCategory_Client);
            copied.mapACG_EquipRecomm_Client = (UniqueInfo.ConfigHashMap<int, TACG_EquipRecomm_Client>)JceUtil.DeepClone(this.mapACG_EquipRecomm_Client);
            copied.mapGuildTips_Client = (UniqueInfo.ConfigHashMap<int, TGuildTips_Client>)JceUtil.DeepClone(this.mapGuildTips_Client);
            copied.mapCardScoreRankInfo_Client = (UniqueInfo.ConfigHashMap<int, TCardScoreRankInfo_Client>)JceUtil.DeepClone(this.mapCardScoreRankInfo_Client);
            copied.mapDeviceWhiteProfile_Client = (UniqueInfo.ConfigHashMap<int, TDeviceWhiteProfile_Client>)JceUtil.DeepClone(this.mapDeviceWhiteProfile_Client);
            copied.mapACG_HexEffect_Client = (UniqueInfo.ConfigHashMap<int, TACG_HexEffect_Client>)JceUtil.DeepClone(this.mapACG_HexEffect_Client);
            copied.mapCardScoreDefaultParam_Client = (UniqueInfo.ConfigHashMap<int, TCardScoreDefaultParam_Client>)JceUtil.DeepClone(this.mapCardScoreDefaultParam_Client);
            copied.mapCardScoreAdvancedParam_Client = (UniqueInfo.ConfigHashMap<int, TCardScoreAdvancedParam_Client>)JceUtil.DeepClone(this.mapCardScoreAdvancedParam_Client);
            copied.mapCasual_LP_Cacualte_Config_Client = (UniqueInfo.ConfigHashMap<int, TCasual_LP_Cacualte_Config_Client>)JceUtil.DeepClone(this.mapCasual_LP_Cacualte_Config_Client);
            copied.mapACGSkillLiveCounter_Client = (UniqueInfo.ConfigHashMap<int, TACGSkillLiveCounter_Client>)JceUtil.DeepClone(this.mapACGSkillLiveCounter_Client);
            copied.mapACG_ButtonSkinCfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_ButtonSkinCfg_Client>)JceUtil.DeepClone(this.mapACG_ButtonSkinCfg_Client);
            return copied;
        }
    }
}

