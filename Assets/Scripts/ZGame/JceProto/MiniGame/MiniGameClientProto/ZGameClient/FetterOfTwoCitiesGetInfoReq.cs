// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class FetterOfTwoCitiesGetInfoReq : Wup.Jce.JceStruct
    {
        public int iActivityID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iActivityID, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iActivityID = (int) _is.Read(iActivityID, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iActivityID, "iActivityID");
        }

        public override void Clear()
        {
            iActivityID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new FetterOfTwoCitiesGetInfoReq();
            copied.iActivityID = this.iActivityID;
            return copied;
        }
    }
}

