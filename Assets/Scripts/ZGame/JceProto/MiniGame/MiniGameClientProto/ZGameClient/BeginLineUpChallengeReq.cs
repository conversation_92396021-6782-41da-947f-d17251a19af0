// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class BeginLineUpChallengeReq : Wup.Jce.JceStruct
    {
        public int lineUpId = 0;

        public TMidasTokenInfo stMidasToken;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(lineUpId, 0);
            _os.Write(stMidasToken, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            lineUpId = (int) _is.Read(lineUpId, 0, false);

            stMidasToken = (TMidasTokenInfo) _is.Read(stMidasToken, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(lineUpId, "lineUpId");
            _ds.Display(stMidasToken, "stMidasToken");
        }

        public override void Clear()
        {
            lineUpId = 0;
            stMidasToken = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new BeginLineUpChallengeReq();
            copied.lineUpId = this.lineUpId;
            copied.stMidasToken = (TMidasTokenInfo)JceUtil.DeepClone(this.stMidasToken);
            return copied;
        }
    }
}

