// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAllGameCfgClient : Wup.Jce.JceStruct
    {
        public UniqueInfo.ConfigHashMap<int, TACG_MatchingLoadTips_Client> mapACG_MatchingLoadTips_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Activity_Client> mapACG_Activity_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Activity_Tag_Client> mapACG_Activity_Tag_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Activity_Page_Client> mapACG_Activity_Page_Client;

        public UniqueInfo.ConfigHashMap<int, TGPU_Client> mapGPU_Client;

        public UniqueInfo.ConfigHashMap<int, TGlobal_Client> mapGlobal_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_ReportFeature_Client> mapTAC_ReportFeature_Client;

        public UniqueInfo.ConfigHashMap<int, TBPConfig_Client> mapBPConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TBPPay_Client> mapBPPay_Client;

        public UniqueInfo.ConfigHashMap<int, TDailyTask_Client> mapDailyTask_Client;

        public UniqueInfo.ConfigHashMap<int, TDailyTaskGroup_Client> mapDailyTaskGroup_Client;

        public UniqueInfo.ConfigHashMap<int, TEnlighten_Client> mapEnlighten_Client;

        public UniqueInfo.ConfigHashMap<int, TEnlightenRate_Client> mapEnlightenRate_Client;

        public UniqueInfo.ConfigHashMap<int, TBPMall_Client> mapBPMall_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_DropBoxItem_Client> mapACG_DropBoxItem_Client;

        public UniqueInfo.ConfigHashMap<int, TACGSkill_Client> mapACGSkill_Client;

        public UniqueInfo.ConfigHashMap<int, TACGSkillDurationTarget_Client> mapACGSkillDurationTarget_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Equipment_Client> mapACG_Equipment_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_EquipmentRandom_Client> mapACG_EquipmentRandom_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Attribute_Client> mapACG_Attribute_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_StealLevelRate_Client> mapACG_StealLevelRate_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_StealEquipRate_Client> mapACG_StealEquipRate_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_EquipmentStore_Client> mapACG_EquipmentStore_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_Module_Client> mapTAC_Module_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_RandName_Client> mapACG_RandName_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_Level_Client> mapTAC_Level_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_NewLevel_Client> mapTAC_NewLevel_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HeroRate_Client> mapACG_HeroRate_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_EquipRate_Client> mapACG_EquipRate_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HeroEquipMatchRate_Client> mapACG_HeroEquipMatchRate_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_SharedDraftCfg_Client> mapACG_SharedDraftCfg_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_BREquipRate_Client> mapACG_BREquipRate_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_BRHeroRate_Client> mapACG_BRHeroRate_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_AdvancedEquipRate_Client> mapACG_AdvancedEquipRate_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_MonsterDrop_Client> mapACG_MonsterDrop_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_DropPoolItem_Client> mapACG_DropPoolItem_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_DropBuff_Client> mapACG_DropBuff_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_BoxType_Client> mapACG_BoxType_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_BoxSkin_Client> mapACG_BoxSkin_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_WarmDropBuff_Client> mapACG_WarmDropBuff_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_AdvancedDrop_Client> mapACG_AdvancedDrop_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_MatchingControl_Client> mapACG_MatchingControl_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_ErrorCode_Client> mapTAC_ErrorCode_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_LoadingTips_Client> mapTAC_LoadingTips_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_Quest_Client> mapTAC_Quest_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_Quest_NPC_Client> mapTAC_Quest_NPC_Client;

        public UniqueInfo.ConfigHashMap<int, TAutoTransColumns_Client> mapAutoTransColumns_Client;

        public UniqueInfo.ConfigHashMap<int, TACGBuffProto_Client> mapACGBuffProto_Client;

        public UniqueInfo.ConfigHashMap<int, TACGBuff_Client> mapACGBuff_Client;

        public UniqueInfo.ConfigHashMap<int, TACGFetters_Client> mapACGFetters_Client;

        public UniqueInfo.ConfigHashMap<int, TACGBuffEffectGroup_Client> mapACGBuffEffectGroup_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_Global_Client> mapTAC_Global_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_RoomCfg_Client> mapTAC_RoomCfg_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Stage_Priority_Client> mapACG_Stage_Priority_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_Tag_Client> mapTAC_Tag_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_Priority_Client> mapTAC_Priority_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_Power_Client> mapTAC_Power_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_CPower_Client> mapTAC_CPower_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_AIEquip_Client> mapTAC_AIEquip_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_AIID_Client> mapTAC_AIID_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_AIRefresh_Client> mapTAC_AIRefresh_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_AIAssignTag_Client> mapTAC_AIAssignTag_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_AIDropEquip_Client> mapTAC_AIDropEquip_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_AICardBuff_Client> mapTAC_AICardBuff_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_EquipStrength_Client> mapTAC_EquipStrength_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_HeroKu_Client> mapTAC_HeroKu_Client;

        public UniqueInfo.ConfigHashMap<int, TChat_SimpleResponse_Client> mapChat_SimpleResponse_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Hero_Client> mapACG_Hero_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_SpecName_Client> mapACG_SpecName_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_ClassName_Client> mapACG_ClassName_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_TagName_Client> mapACG_TagName_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Summon_Client> mapACG_Summon_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_BattleMergeHero_Client> mapACG_BattleMergeHero_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_SXScreen_Client> mapTAC_SXScreen_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_DamageRules_Hero_Client> mapACG_DamageRules_Hero_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_DamageRules_Round_Client> mapACG_DamageRules_Round_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Item_Client> mapACG_Item_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_ChestKey_Client> mapACG_ChestKey_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HeroVioce_Client> mapACG_HeroVioce_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_TeamLeaderVioce_Client> mapACG_TeamLeaderVioce_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Mall_Client> mapACG_Mall_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Recommend_Client> mapACG_Recommend_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Egg_Client> mapACG_Egg_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_PageInfo_Client> mapACG_PageInfo_Client;

        public UniqueInfo.ConfigHashMap<int, TACGHeroStory_Client> mapACGHeroStory_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Mail_Client> mapACG_Mail_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HeroBuyCustomized_Client> mapACG_HeroBuyCustomized_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_RoundSelectCustomized_Client> mapACG_RoundSelectCustomized_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_MatchOpponentCustomized_Client> mapACG_MatchOpponentCustomized_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_NewerAIHeroDropEquipment_Client> mapACG_NewerAIHeroDropEquipment_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Final_Phase_Client> mapACG_Final_Phase_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Guide_Mission_Client> mapACG_Guide_Mission_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_LP_Cacualte_Config_Client> mapACG_LP_Cacualte_Config_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_MMR_Match_AI_Client> mapACG_MMR_Match_AI_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Notice_Client> mapACG_Notice_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_NoviceReward_Client> mapACG_NoviceReward_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_ShareDrop_Client> mapACG_ShareDrop_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_RookieMission_Client> mapACG_RookieMission_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_DeckRecommend_Client> mapACG_DeckRecommend_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_ElementTerrain_Client> mapACG_ElementTerrain_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Examination_Info_Client> mapACG_Examination_Info_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Examination_Questions_Client> mapACG_Examination_Questions_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Quiz_Event_Info_Client> mapACG_Quiz_Event_Info_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HandbookRule_Client> mapACG_HandbookRule_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_ProgramWord_Client> mapACG_ProgramWord_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_SpriteHelper_Client> mapACG_SpriteHelper_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_SpriteCondition_Client> mapACG_SpriteCondition_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_SpriteTips_Client> mapACG_SpriteTips_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_RefreshHeroCustomized_Client> mapACG_RefreshHeroCustomized_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Set_Client> mapACG_Set_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_CollegeSet_Client> mapACG_CollegeSet_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_GuideSet_Client> mapACG_GuideSet_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_UserProfilePanel_Client> mapACG_UserProfilePanel_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_BattleHelper_Client> mapACG_BattleHelper_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_CounterTriger_Client> mapACG_CounterTriger_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_BattleTipCondition_Client> mapACG_BattleTipCondition_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_BattleTips_Client> mapACG_BattleTips_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_BattleTipsSwitch_Client> mapACG_BattleTipsSwitch_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Jump_Client> mapACG_Jump_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_FightResultTag_Client> mapACG_FightResultTag_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_RoundCoin_Client> mapACG_RoundCoin_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_RoundExp_Client> mapACG_RoundExp_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_InitDrop_Client> mapACG_InitDrop_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_RankDrop_Client> mapACG_RankDrop_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_KillDrop_Client> mapACG_KillDrop_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_StarDrop_Client> mapACG_StarDrop_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Skill_Mercenary_Client> mapACG_Skill_Mercenary_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Mercenary_Level_Client> mapACG_Mercenary_Level_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_SKill_Mercenary_Show_Client> mapACG_SKill_Mercenary_Show_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Galaxy_Client> mapACG_Galaxy_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_GalaxyPool_Client> mapACG_GalaxyPool_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_AudioMgr_Client> mapTAC_AudioMgr_Client;

        public UniqueInfo.ConfigHashMap<int, TPandoraErrorCodeInfo_Client> mapPandoraErrorCodeInfo_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_GuideLabel_Client> mapACG_GuideLabel_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_GuideTipsPool_Client> mapACG_GuideTipsPool_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_GuidePushTip_Client> mapACG_GuidePushTip_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_RankScores_Client> mapACG_RankScores_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_TextTipConfig_Client> mapACG_TextTipConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_AIWarmBattle_Client> mapTAC_AIWarmBattle_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Newcomer_Config_Client> mapACG_Newcomer_Config_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_GuideCommonConfig_Client> mapACG_GuideCommonConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_GuideEquipmentConfig_Client> mapACG_GuideEquipmentConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_GuideHeroConfig_Client> mapACG_GuideHeroConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_GuidePosConfig_Client> mapACG_GuidePosConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_AIDropEquipNew_Client> mapTAC_AIDropEquipNew_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_GuideFetterConfig_Client> mapACG_GuideFetterConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TEvents_Client> mapEvents_Client;

        public UniqueInfo.ConfigHashMap<int, TEventIntroduction_Client> mapEventIntroduction_Client;

        public UniqueInfo.ConfigHashMap<int, TACGHeavenBuff_Client> mapACGHeavenBuff_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Buff_HeavenSelect_Level_Client> mapACG_Buff_HeavenSelect_Level_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Buff_HeavenSelect_Client> mapACG_Buff_HeavenSelect_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Buff_HeavenSelect_Show_Client> mapACG_Buff_HeavenSelect_Show_Client;

        public UniqueInfo.ConfigHashMap<int, TK6UserTag_Client> mapK6UserTag_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_MissTrans_Client> mapACG_MissTrans_Client;

        public UniqueInfo.ConfigHashMap<int, TChallengeMode_Client> mapChallengeMode_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_GuideEntryConfig_Client> mapACG_GuideEntryConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_BalanceRuleConfig_Client> mapTAC_BalanceRuleConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_LobbyHeroValid_Client> mapACG_LobbyHeroValid_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_LobbyHeroPassiveChecking_Client> mapACG_LobbyHeroPassiveChecking_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_WwiseBankConfig_Client> mapACG_WwiseBankConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_StagePlus_Client> mapTAC_StagePlus_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_Round_Client> mapTAC_Round_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Camera_Client> mapACG_Camera_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_WarmRefreshCustomized_Client> mapACG_WarmRefreshCustomized_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_WarmRefreshPlan_Client> mapACG_WarmRefreshPlan_Client;

        public UniqueInfo.ConfigHashMap<int, TUserTagConfig_Client> mapUserTagConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TACGDebuffGroup_Client> mapACGDebuffGroup_Client;

        public UniqueInfo.ConfigHashMap<int, TModuleSwitch_Client> mapModuleSwitch_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_EquipmentStoreS5_Client> mapACG_EquipmentStoreS5_Client;

        public UniqueInfo.ConfigHashMap<int, TTurbo_LP_Cacualte_Config_Client> mapTurbo_LP_Cacualte_Config_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_DraconicDrop_Client> mapACG_DraconicDrop_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_ExtraRoomCfg_Client> mapTAC_ExtraRoomCfg_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Channel_list_Client> mapACG_Channel_list_Client;

        public UniqueInfo.ConfigHashMap<int, TChat_BubbleFrame_Client> mapChat_BubbleFrame_Client;

        public UniqueInfo.ConfigHashMap<int, TClubGlobal_Client> mapClubGlobal_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Destiny_Client> mapACG_Destiny_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_AISitPos_Client> mapTAC_AISitPos_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_ExtraStealEquipRate_Client> mapACG_ExtraStealEquipRate_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HolyDrop_Client> mapACG_HolyDrop_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HolyDropPool_Client> mapACG_HolyDropPool_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_DestinyBanner_Client> mapACG_DestinyBanner_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_TechnologyJinNeng_Backgroun_Client> mapACG_TechnologyJinNeng_Backgroun_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_SeasonInfo_Client> mapACG_SeasonInfo_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_RecommendClientCfg_Client> mapACG_RecommendClientCfg_Client;

        public UniqueInfo.ConfigHashMap<int, TClubMall_Client> mapClubMall_Client;

        /// <summary>
        /// ext1
        /// </summary>
        public TAllGameCfgClientExt1 stTAllGameCfgClientExt1;

        public UniqueInfo.ConfigHashMap<int, TGameModuleGuideIntro_Client> mapGameModuleGuideIntro_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_CombatEffect_Client> mapACG_CombatEffect_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HeroSkin_Client> mapACG_HeroSkin_Client;

        public UniqueInfo.ConfigHashMap<int, TChat_SimpleResponseVoice_Client> mapChat_SimpleResponseVoice_Client;

        public UniqueInfo.ConfigHashMap<int, TManualTransTable_Client> mapManualTransTable_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_ItemGetType_Client> mapACG_ItemGetType_Client;

        /// <summary>
        /// ext2
        /// </summary>
        public TAllGameCfgClientExt2 stTAllGameCfgClientExt2;

        /// <summary>
        /// ext3
        /// </summary>
        public TAllGameCfgClientExt3 stTAllGameCfgClientExt3;

        /// <summary>
        /// ext4
        /// </summary>
        public TAllGameCfgClientExt4 stTAllGameCfgClientExt4;

        /// <summary>
        /// ext5
        /// </summary>
        public TAllGameCfgClientExt5 stTAllGameCfgClientExt5;

        /// <summary>
        /// ext6
        /// </summary>
        public TAllGameCfgClientExt6 stTAllGameCfgClientExt6;

        public UniqueInfo.ConfigHashMap<int, TBattleEndDisplayIconCfg_Client> mapBattleEndDisplayIconCfg_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_MallBuyTips_Client> mapACG_MallBuyTips_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_PrimeData_Client> mapACG_PrimeData_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_LuckShare_Client> mapACG_LuckShare_Client;

        public UniqueInfo.ConfigHashMap<int, TJOCRank_LP_Cacualte_Config_Client> mapJOCRank_LP_Cacualte_Config_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_FetterMusic_Client> mapTAC_FetterMusic_Client;

        public UniqueInfo.ConfigHashMap<int, TACGRecordBuffTrigger_Client> mapACGRecordBuffTrigger_Client;

        public UniqueInfo.ConfigHashMap<int, TTAC_FetterMusicPlan_Client> mapTAC_FetterMusicPlan_Client;

        /// <summary>
        /// ext7
        /// </summary>
        public TAllGameCfgClientExt7 stTAllGameCfgClientExt7;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(mapACG_MatchingLoadTips_Client, 0);
            _os.Write(mapACG_Activity_Client, 1);
            _os.Write(mapACG_Activity_Tag_Client, 2);
            _os.Write(mapACG_Activity_Page_Client, 3);
            _os.Write(mapGPU_Client, 4);
            _os.Write(mapGlobal_Client, 5);
            _os.Write(mapTAC_ReportFeature_Client, 6);
            _os.Write(mapBPConfig_Client, 8);
            _os.Write(mapBPPay_Client, 9);
            _os.Write(mapDailyTask_Client, 11);
            _os.Write(mapDailyTaskGroup_Client, 12);
            _os.Write(mapEnlighten_Client, 14);
            _os.Write(mapEnlightenRate_Client, 15);
            _os.Write(mapBPMall_Client, 16);
            _os.Write(mapACG_DropBoxItem_Client, 18);
            _os.Write(mapACGSkill_Client, 19);
            _os.Write(mapACGSkillDurationTarget_Client, 20);
            _os.Write(mapACG_Equipment_Client, 21);
            _os.Write(mapACG_EquipmentRandom_Client, 22);
            _os.Write(mapACG_Attribute_Client, 23);
            _os.Write(mapACG_StealLevelRate_Client, 24);
            _os.Write(mapACG_StealEquipRate_Client, 25);
            _os.Write(mapACG_EquipmentStore_Client, 26);
            _os.Write(mapTAC_Module_Client, 27);
            _os.Write(mapACG_RandName_Client, 28);
            _os.Write(mapTAC_Level_Client, 29);
            _os.Write(mapTAC_NewLevel_Client, 30);
            _os.Write(mapACG_HeroRate_Client, 31);
            _os.Write(mapACG_EquipRate_Client, 32);
            _os.Write(mapACG_HeroEquipMatchRate_Client, 33);
            _os.Write(mapACG_SharedDraftCfg_Client, 34);
            _os.Write(mapACG_BREquipRate_Client, 35);
            _os.Write(mapACG_BRHeroRate_Client, 36);
            _os.Write(mapACG_AdvancedEquipRate_Client, 37);
            _os.Write(mapACG_MonsterDrop_Client, 38);
            _os.Write(mapACG_DropPoolItem_Client, 39);
            _os.Write(mapACG_DropBuff_Client, 40);
            _os.Write(mapACG_BoxType_Client, 41);
            _os.Write(mapACG_BoxSkin_Client, 42);
            _os.Write(mapACG_WarmDropBuff_Client, 43);
            _os.Write(mapACG_AdvancedDrop_Client, 44);
            _os.Write(mapACG_MatchingControl_Client, 45);
            _os.Write(mapTAC_ErrorCode_Client, 46);
            _os.Write(mapTAC_LoadingTips_Client, 47);
            _os.Write(mapTAC_Quest_Client, 48);
            _os.Write(mapTAC_Quest_NPC_Client, 49);
            _os.Write(mapAutoTransColumns_Client, 50);
            _os.Write(mapACGBuffProto_Client, 51);
            _os.Write(mapACGBuff_Client, 52);
            _os.Write(mapACGFetters_Client, 53);
            _os.Write(mapACGBuffEffectGroup_Client, 54);
            _os.Write(mapTAC_Global_Client, 56);
            _os.Write(mapTAC_RoomCfg_Client, 57);
            _os.Write(mapACG_Stage_Priority_Client, 58);
            _os.Write(mapTAC_Tag_Client, 59);
            _os.Write(mapTAC_Priority_Client, 60);
            _os.Write(mapTAC_Power_Client, 61);
            _os.Write(mapTAC_CPower_Client, 62);
            _os.Write(mapTAC_AIEquip_Client, 63);
            _os.Write(mapTAC_AIID_Client, 64);
            _os.Write(mapTAC_AIRefresh_Client, 65);
            _os.Write(mapTAC_AIAssignTag_Client, 66);
            _os.Write(mapTAC_AIDropEquip_Client, 67);
            _os.Write(mapTAC_AICardBuff_Client, 68);
            _os.Write(mapTAC_EquipStrength_Client, 69);
            _os.Write(mapTAC_HeroKu_Client, 70);
            _os.Write(mapChat_SimpleResponse_Client, 71);
            _os.Write(mapACG_Hero_Client, 72);
            _os.Write(mapACG_SpecName_Client, 73);
            _os.Write(mapACG_ClassName_Client, 74);
            _os.Write(mapACG_TagName_Client, 75);
            _os.Write(mapACG_Summon_Client, 76);
            _os.Write(mapACG_BattleMergeHero_Client, 77);
            _os.Write(mapTAC_SXScreen_Client, 78);
            _os.Write(mapACG_DamageRules_Hero_Client, 79);
            _os.Write(mapACG_DamageRules_Round_Client, 80);
            _os.Write(mapACG_Item_Client, 82);
            _os.Write(mapACG_ChestKey_Client, 83);
            _os.Write(mapACG_HeroVioce_Client, 84);
            _os.Write(mapACG_TeamLeaderVioce_Client, 85);
            _os.Write(mapACG_Mall_Client, 86);
            _os.Write(mapACG_Recommend_Client, 88);
            _os.Write(mapACG_Egg_Client, 89);
            _os.Write(mapACG_PageInfo_Client, 90);
            _os.Write(mapACGHeroStory_Client, 91);
            _os.Write(mapACG_Mail_Client, 95);
            _os.Write(mapACG_HeroBuyCustomized_Client, 96);
            _os.Write(mapACG_RoundSelectCustomized_Client, 97);
            _os.Write(mapACG_MatchOpponentCustomized_Client, 98);
            _os.Write(mapACG_NewerAIHeroDropEquipment_Client, 99);
            _os.Write(mapACG_Final_Phase_Client, 100);
            _os.Write(mapACG_Guide_Mission_Client, 101);
            _os.Write(mapACG_LP_Cacualte_Config_Client, 102);
            _os.Write(mapACG_MMR_Match_AI_Client, 103);
            _os.Write(mapACG_Notice_Client, 105);
            _os.Write(mapACG_NoviceReward_Client, 106);
            _os.Write(mapACG_ShareDrop_Client, 107);
            _os.Write(mapACG_RookieMission_Client, 108);
            _os.Write(mapACG_DeckRecommend_Client, 109);
            _os.Write(mapACG_ElementTerrain_Client, 110);
            _os.Write(mapACG_Examination_Info_Client, 111);
            _os.Write(mapACG_Examination_Questions_Client, 112);
            _os.Write(mapACG_Quiz_Event_Info_Client, 113);
            _os.Write(mapACG_HandbookRule_Client, 114);
            _os.Write(mapACG_ProgramWord_Client, 115);
            _os.Write(mapACG_SpriteHelper_Client, 116);
            _os.Write(mapACG_SpriteCondition_Client, 117);
            _os.Write(mapACG_SpriteTips_Client, 118);
            _os.Write(mapACG_RefreshHeroCustomized_Client, 119);
            _os.Write(mapACG_Set_Client, 120);
            _os.Write(mapACG_CollegeSet_Client, 121);
            _os.Write(mapACG_GuideSet_Client, 122);
            _os.Write(mapACG_UserProfilePanel_Client, 123);
            _os.Write(mapACG_BattleHelper_Client, 124);
            _os.Write(mapACG_CounterTriger_Client, 125);
            _os.Write(mapACG_BattleTipCondition_Client, 126);
            _os.Write(mapACG_BattleTips_Client, 127);
            _os.Write(mapACG_BattleTipsSwitch_Client, 128);
            _os.Write(mapACG_Jump_Client, 129);
            _os.Write(mapACG_FightResultTag_Client, 130);
            _os.Write(mapACG_RoundCoin_Client, 131);
            _os.Write(mapACG_RoundExp_Client, 132);
            _os.Write(mapACG_InitDrop_Client, 133);
            _os.Write(mapACG_RankDrop_Client, 134);
            _os.Write(mapACG_KillDrop_Client, 135);
            _os.Write(mapACG_StarDrop_Client, 136);
            _os.Write(mapACG_Skill_Mercenary_Client, 137);
            _os.Write(mapACG_Mercenary_Level_Client, 138);
            _os.Write(mapACG_SKill_Mercenary_Show_Client, 139);
            _os.Write(mapACG_Galaxy_Client, 140);
            _os.Write(mapACG_GalaxyPool_Client, 141);
            _os.Write(mapTAC_AudioMgr_Client, 142);
            _os.Write(mapPandoraErrorCodeInfo_Client, 144);
            _os.Write(mapACG_GuideLabel_Client, 145);
            _os.Write(mapACG_GuideTipsPool_Client, 146);
            _os.Write(mapACG_GuidePushTip_Client, 147);
            _os.Write(mapACG_RankScores_Client, 148);
            _os.Write(mapACG_TextTipConfig_Client, 149);
            _os.Write(mapTAC_AIWarmBattle_Client, 151);
            _os.Write(mapACG_Newcomer_Config_Client, 152);
            _os.Write(mapACG_GuideCommonConfig_Client, 153);
            _os.Write(mapACG_GuideEquipmentConfig_Client, 154);
            _os.Write(mapACG_GuideHeroConfig_Client, 155);
            _os.Write(mapACG_GuidePosConfig_Client, 156);
            _os.Write(mapTAC_AIDropEquipNew_Client, 158);
            _os.Write(mapACG_GuideFetterConfig_Client, 159);
            _os.Write(mapEvents_Client, 160);
            _os.Write(mapEventIntroduction_Client, 161);
            _os.Write(mapACGHeavenBuff_Client, 163);
            _os.Write(mapACG_Buff_HeavenSelect_Level_Client, 164);
            _os.Write(mapACG_Buff_HeavenSelect_Client, 165);
            _os.Write(mapACG_Buff_HeavenSelect_Show_Client, 166);
            _os.Write(mapK6UserTag_Client, 167);
            _os.Write(mapACG_MissTrans_Client, 168);
            _os.Write(mapChallengeMode_Client, 174);
            _os.Write(mapACG_GuideEntryConfig_Client, 179);
            _os.Write(mapTAC_BalanceRuleConfig_Client, 180);
            _os.Write(mapACG_LobbyHeroValid_Client, 181);
            _os.Write(mapACG_LobbyHeroPassiveChecking_Client, 182);
            _os.Write(mapACG_WwiseBankConfig_Client, 183);
            _os.Write(mapTAC_StagePlus_Client, 184);
            _os.Write(mapTAC_Round_Client, 185);
            _os.Write(mapACG_Camera_Client, 186);
            _os.Write(mapACG_WarmRefreshCustomized_Client, 187);
            _os.Write(mapACG_WarmRefreshPlan_Client, 188);
            _os.Write(mapUserTagConfig_Client, 191);
            _os.Write(mapACGDebuffGroup_Client, 192);
            _os.Write(mapModuleSwitch_Client, 193);
            _os.Write(mapACG_EquipmentStoreS5_Client, 197);
            _os.Write(mapTurbo_LP_Cacualte_Config_Client, 198);
            _os.Write(mapACG_DraconicDrop_Client, 199);
            _os.Write(mapTAC_ExtraRoomCfg_Client, 200);
            _os.Write(mapACG_Channel_list_Client, 201);
            _os.Write(mapChat_BubbleFrame_Client, 202);
            _os.Write(mapClubGlobal_Client, 203);
            _os.Write(mapACG_Destiny_Client, 204);
            _os.Write(mapTAC_AISitPos_Client, 205);
            _os.Write(mapACG_ExtraStealEquipRate_Client, 206);
            _os.Write(mapACG_HolyDrop_Client, 207);
            _os.Write(mapACG_HolyDropPool_Client, 208);
            _os.Write(mapACG_DestinyBanner_Client, 209);
            _os.Write(mapACG_TechnologyJinNeng_Backgroun_Client, 210);
            _os.Write(mapACG_SeasonInfo_Client, 211);
            _os.Write(mapACG_RecommendClientCfg_Client, 212);
            _os.Write(mapClubMall_Client, 218);
            _os.Write(stTAllGameCfgClientExt1, 219);
            _os.Write(mapGameModuleGuideIntro_Client, 221);
            _os.Write(mapACG_CombatEffect_Client, 222);
            _os.Write(mapACG_HeroSkin_Client, 223);
            _os.Write(mapChat_SimpleResponseVoice_Client, 224);
            _os.Write(mapManualTransTable_Client, 225);
            _os.Write(mapACG_ItemGetType_Client, 226);
            _os.Write(stTAllGameCfgClientExt2, 227);
            _os.Write(stTAllGameCfgClientExt3, 228);
            _os.Write(stTAllGameCfgClientExt4, 229);
            _os.Write(stTAllGameCfgClientExt5, 230);
            _os.Write(stTAllGameCfgClientExt6, 231);
            _os.Write(mapBattleEndDisplayIconCfg_Client, 232);
            _os.Write(mapACG_MallBuyTips_Client, 233);
            _os.Write(mapACG_PrimeData_Client, 234);
            _os.Write(mapACG_LuckShare_Client, 235);
            _os.Write(mapJOCRank_LP_Cacualte_Config_Client, 236);
            _os.Write(mapTAC_FetterMusic_Client, 237);
            _os.Write(mapACGRecordBuffTrigger_Client, 238);
            _os.Write(mapTAC_FetterMusicPlan_Client, 239);
            _os.Write(stTAllGameCfgClientExt7, 240);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            mapACG_MatchingLoadTips_Client = (UniqueInfo.ConfigHashMap<int, TACG_MatchingLoadTips_Client>)_is.Read(mapACG_MatchingLoadTips_Client, 0, false);

            mapACG_Activity_Client = (UniqueInfo.ConfigHashMap<int, TACG_Activity_Client>)_is.Read(mapACG_Activity_Client, 1, false);

            mapACG_Activity_Tag_Client = (UniqueInfo.ConfigHashMap<int, TACG_Activity_Tag_Client>)_is.Read(mapACG_Activity_Tag_Client, 2, false);

            mapACG_Activity_Page_Client = (UniqueInfo.ConfigHashMap<int, TACG_Activity_Page_Client>)_is.Read(mapACG_Activity_Page_Client, 3, false);

            mapGPU_Client = (UniqueInfo.ConfigHashMap<int, TGPU_Client>)_is.Read(mapGPU_Client, 4, false);

            mapGlobal_Client = (UniqueInfo.ConfigHashMap<int, TGlobal_Client>)_is.Read(mapGlobal_Client, 5, false);

            mapTAC_ReportFeature_Client = (UniqueInfo.ConfigHashMap<int, TTAC_ReportFeature_Client>)_is.Read(mapTAC_ReportFeature_Client, 6, false);

            mapBPConfig_Client = (UniqueInfo.ConfigHashMap<int, TBPConfig_Client>)_is.Read(mapBPConfig_Client, 8, false);

            mapBPPay_Client = (UniqueInfo.ConfigHashMap<int, TBPPay_Client>)_is.Read(mapBPPay_Client, 9, false);

            mapDailyTask_Client = (UniqueInfo.ConfigHashMap<int, TDailyTask_Client>)_is.Read(mapDailyTask_Client, 11, false);

            mapDailyTaskGroup_Client = (UniqueInfo.ConfigHashMap<int, TDailyTaskGroup_Client>)_is.Read(mapDailyTaskGroup_Client, 12, false);

            mapEnlighten_Client = (UniqueInfo.ConfigHashMap<int, TEnlighten_Client>)_is.Read(mapEnlighten_Client, 14, false);

            mapEnlightenRate_Client = (UniqueInfo.ConfigHashMap<int, TEnlightenRate_Client>)_is.Read(mapEnlightenRate_Client, 15, false);

            mapBPMall_Client = (UniqueInfo.ConfigHashMap<int, TBPMall_Client>)_is.Read(mapBPMall_Client, 16, false);

            mapACG_DropBoxItem_Client = (UniqueInfo.ConfigHashMap<int, TACG_DropBoxItem_Client>)_is.Read(mapACG_DropBoxItem_Client, 18, false);

            mapACGSkill_Client = (UniqueInfo.ConfigHashMap<int, TACGSkill_Client>)_is.Read(mapACGSkill_Client, 19, false);

            mapACGSkillDurationTarget_Client = (UniqueInfo.ConfigHashMap<int, TACGSkillDurationTarget_Client>)_is.Read(mapACGSkillDurationTarget_Client, 20, false);

            mapACG_Equipment_Client = (UniqueInfo.ConfigHashMap<int, TACG_Equipment_Client>)_is.Read(mapACG_Equipment_Client, 21, false);

            mapACG_EquipmentRandom_Client = (UniqueInfo.ConfigHashMap<int, TACG_EquipmentRandom_Client>)_is.Read(mapACG_EquipmentRandom_Client, 22, false);

            mapACG_Attribute_Client = (UniqueInfo.ConfigHashMap<int, TACG_Attribute_Client>)_is.Read(mapACG_Attribute_Client, 23, false);

            mapACG_StealLevelRate_Client = (UniqueInfo.ConfigHashMap<int, TACG_StealLevelRate_Client>)_is.Read(mapACG_StealLevelRate_Client, 24, false);

            mapACG_StealEquipRate_Client = (UniqueInfo.ConfigHashMap<int, TACG_StealEquipRate_Client>)_is.Read(mapACG_StealEquipRate_Client, 25, false);

            mapACG_EquipmentStore_Client = (UniqueInfo.ConfigHashMap<int, TACG_EquipmentStore_Client>)_is.Read(mapACG_EquipmentStore_Client, 26, false);

            mapTAC_Module_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Module_Client>)_is.Read(mapTAC_Module_Client, 27, false);

            mapACG_RandName_Client = (UniqueInfo.ConfigHashMap<int, TACG_RandName_Client>)_is.Read(mapACG_RandName_Client, 28, false);

            mapTAC_Level_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Level_Client>)_is.Read(mapTAC_Level_Client, 29, false);

            mapTAC_NewLevel_Client = (UniqueInfo.ConfigHashMap<int, TTAC_NewLevel_Client>)_is.Read(mapTAC_NewLevel_Client, 30, false);

            mapACG_HeroRate_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeroRate_Client>)_is.Read(mapACG_HeroRate_Client, 31, false);

            mapACG_EquipRate_Client = (UniqueInfo.ConfigHashMap<int, TACG_EquipRate_Client>)_is.Read(mapACG_EquipRate_Client, 32, false);

            mapACG_HeroEquipMatchRate_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeroEquipMatchRate_Client>)_is.Read(mapACG_HeroEquipMatchRate_Client, 33, false);

            mapACG_SharedDraftCfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_SharedDraftCfg_Client>)_is.Read(mapACG_SharedDraftCfg_Client, 34, false);

            mapACG_BREquipRate_Client = (UniqueInfo.ConfigHashMap<int, TACG_BREquipRate_Client>)_is.Read(mapACG_BREquipRate_Client, 35, false);

            mapACG_BRHeroRate_Client = (UniqueInfo.ConfigHashMap<int, TACG_BRHeroRate_Client>)_is.Read(mapACG_BRHeroRate_Client, 36, false);

            mapACG_AdvancedEquipRate_Client = (UniqueInfo.ConfigHashMap<int, TACG_AdvancedEquipRate_Client>)_is.Read(mapACG_AdvancedEquipRate_Client, 37, false);

            mapACG_MonsterDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_MonsterDrop_Client>)_is.Read(mapACG_MonsterDrop_Client, 38, false);

            mapACG_DropPoolItem_Client = (UniqueInfo.ConfigHashMap<int, TACG_DropPoolItem_Client>)_is.Read(mapACG_DropPoolItem_Client, 39, false);

            mapACG_DropBuff_Client = (UniqueInfo.ConfigHashMap<int, TACG_DropBuff_Client>)_is.Read(mapACG_DropBuff_Client, 40, false);

            mapACG_BoxType_Client = (UniqueInfo.ConfigHashMap<int, TACG_BoxType_Client>)_is.Read(mapACG_BoxType_Client, 41, false);

            mapACG_BoxSkin_Client = (UniqueInfo.ConfigHashMap<int, TACG_BoxSkin_Client>)_is.Read(mapACG_BoxSkin_Client, 42, false);

            mapACG_WarmDropBuff_Client = (UniqueInfo.ConfigHashMap<int, TACG_WarmDropBuff_Client>)_is.Read(mapACG_WarmDropBuff_Client, 43, false);

            mapACG_AdvancedDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_AdvancedDrop_Client>)_is.Read(mapACG_AdvancedDrop_Client, 44, false);

            mapACG_MatchingControl_Client = (UniqueInfo.ConfigHashMap<int, TACG_MatchingControl_Client>)_is.Read(mapACG_MatchingControl_Client, 45, false);

            mapTAC_ErrorCode_Client = (UniqueInfo.ConfigHashMap<int, TTAC_ErrorCode_Client>)_is.Read(mapTAC_ErrorCode_Client, 46, false);

            mapTAC_LoadingTips_Client = (UniqueInfo.ConfigHashMap<int, TTAC_LoadingTips_Client>)_is.Read(mapTAC_LoadingTips_Client, 47, false);

            mapTAC_Quest_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Quest_Client>)_is.Read(mapTAC_Quest_Client, 48, false);

            mapTAC_Quest_NPC_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Quest_NPC_Client>)_is.Read(mapTAC_Quest_NPC_Client, 49, false);

            mapAutoTransColumns_Client = (UniqueInfo.ConfigHashMap<int, TAutoTransColumns_Client>)_is.Read(mapAutoTransColumns_Client, 50, false);

            mapACGBuffProto_Client = (UniqueInfo.ConfigHashMap<int, TACGBuffProto_Client>)_is.Read(mapACGBuffProto_Client, 51, false);

            mapACGBuff_Client = (UniqueInfo.ConfigHashMap<int, TACGBuff_Client>)_is.Read(mapACGBuff_Client, 52, false);

            mapACGFetters_Client = (UniqueInfo.ConfigHashMap<int, TACGFetters_Client>)_is.Read(mapACGFetters_Client, 53, false);

            mapACGBuffEffectGroup_Client = (UniqueInfo.ConfigHashMap<int, TACGBuffEffectGroup_Client>)_is.Read(mapACGBuffEffectGroup_Client, 54, false);

            mapTAC_Global_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Global_Client>)_is.Read(mapTAC_Global_Client, 56, false);

            mapTAC_RoomCfg_Client = (UniqueInfo.ConfigHashMap<int, TTAC_RoomCfg_Client>)_is.Read(mapTAC_RoomCfg_Client, 57, false);

            mapACG_Stage_Priority_Client = (UniqueInfo.ConfigHashMap<int, TACG_Stage_Priority_Client>)_is.Read(mapACG_Stage_Priority_Client, 58, false);

            mapTAC_Tag_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Tag_Client>)_is.Read(mapTAC_Tag_Client, 59, false);

            mapTAC_Priority_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Priority_Client>)_is.Read(mapTAC_Priority_Client, 60, false);

            mapTAC_Power_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Power_Client>)_is.Read(mapTAC_Power_Client, 61, false);

            mapTAC_CPower_Client = (UniqueInfo.ConfigHashMap<int, TTAC_CPower_Client>)_is.Read(mapTAC_CPower_Client, 62, false);

            mapTAC_AIEquip_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIEquip_Client>)_is.Read(mapTAC_AIEquip_Client, 63, false);

            mapTAC_AIID_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIID_Client>)_is.Read(mapTAC_AIID_Client, 64, false);

            mapTAC_AIRefresh_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIRefresh_Client>)_is.Read(mapTAC_AIRefresh_Client, 65, false);

            mapTAC_AIAssignTag_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIAssignTag_Client>)_is.Read(mapTAC_AIAssignTag_Client, 66, false);

            mapTAC_AIDropEquip_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIDropEquip_Client>)_is.Read(mapTAC_AIDropEquip_Client, 67, false);

            mapTAC_AICardBuff_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AICardBuff_Client>)_is.Read(mapTAC_AICardBuff_Client, 68, false);

            mapTAC_EquipStrength_Client = (UniqueInfo.ConfigHashMap<int, TTAC_EquipStrength_Client>)_is.Read(mapTAC_EquipStrength_Client, 69, false);

            mapTAC_HeroKu_Client = (UniqueInfo.ConfigHashMap<int, TTAC_HeroKu_Client>)_is.Read(mapTAC_HeroKu_Client, 70, false);

            mapChat_SimpleResponse_Client = (UniqueInfo.ConfigHashMap<int, TChat_SimpleResponse_Client>)_is.Read(mapChat_SimpleResponse_Client, 71, false);

            mapACG_Hero_Client = (UniqueInfo.ConfigHashMap<int, TACG_Hero_Client>)_is.Read(mapACG_Hero_Client, 72, false);

            mapACG_SpecName_Client = (UniqueInfo.ConfigHashMap<int, TACG_SpecName_Client>)_is.Read(mapACG_SpecName_Client, 73, false);

            mapACG_ClassName_Client = (UniqueInfo.ConfigHashMap<int, TACG_ClassName_Client>)_is.Read(mapACG_ClassName_Client, 74, false);

            mapACG_TagName_Client = (UniqueInfo.ConfigHashMap<int, TACG_TagName_Client>)_is.Read(mapACG_TagName_Client, 75, false);

            mapACG_Summon_Client = (UniqueInfo.ConfigHashMap<int, TACG_Summon_Client>)_is.Read(mapACG_Summon_Client, 76, false);

            mapACG_BattleMergeHero_Client = (UniqueInfo.ConfigHashMap<int, TACG_BattleMergeHero_Client>)_is.Read(mapACG_BattleMergeHero_Client, 77, false);

            mapTAC_SXScreen_Client = (UniqueInfo.ConfigHashMap<int, TTAC_SXScreen_Client>)_is.Read(mapTAC_SXScreen_Client, 78, false);

            mapACG_DamageRules_Hero_Client = (UniqueInfo.ConfigHashMap<int, TACG_DamageRules_Hero_Client>)_is.Read(mapACG_DamageRules_Hero_Client, 79, false);

            mapACG_DamageRules_Round_Client = (UniqueInfo.ConfigHashMap<int, TACG_DamageRules_Round_Client>)_is.Read(mapACG_DamageRules_Round_Client, 80, false);

            mapACG_Item_Client = (UniqueInfo.ConfigHashMap<int, TACG_Item_Client>)_is.Read(mapACG_Item_Client, 82, false);

            mapACG_ChestKey_Client = (UniqueInfo.ConfigHashMap<int, TACG_ChestKey_Client>)_is.Read(mapACG_ChestKey_Client, 83, false);

            mapACG_HeroVioce_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeroVioce_Client>)_is.Read(mapACG_HeroVioce_Client, 84, false);

            mapACG_TeamLeaderVioce_Client = (UniqueInfo.ConfigHashMap<int, TACG_TeamLeaderVioce_Client>)_is.Read(mapACG_TeamLeaderVioce_Client, 85, false);

            mapACG_Mall_Client = (UniqueInfo.ConfigHashMap<int, TACG_Mall_Client>)_is.Read(mapACG_Mall_Client, 86, false);

            mapACG_Recommend_Client = (UniqueInfo.ConfigHashMap<int, TACG_Recommend_Client>)_is.Read(mapACG_Recommend_Client, 88, false);

            mapACG_Egg_Client = (UniqueInfo.ConfigHashMap<int, TACG_Egg_Client>)_is.Read(mapACG_Egg_Client, 89, false);

            mapACG_PageInfo_Client = (UniqueInfo.ConfigHashMap<int, TACG_PageInfo_Client>)_is.Read(mapACG_PageInfo_Client, 90, false);

            mapACGHeroStory_Client = (UniqueInfo.ConfigHashMap<int, TACGHeroStory_Client>)_is.Read(mapACGHeroStory_Client, 91, false);

            mapACG_Mail_Client = (UniqueInfo.ConfigHashMap<int, TACG_Mail_Client>)_is.Read(mapACG_Mail_Client, 95, false);

            mapACG_HeroBuyCustomized_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeroBuyCustomized_Client>)_is.Read(mapACG_HeroBuyCustomized_Client, 96, false);

            mapACG_RoundSelectCustomized_Client = (UniqueInfo.ConfigHashMap<int, TACG_RoundSelectCustomized_Client>)_is.Read(mapACG_RoundSelectCustomized_Client, 97, false);

            mapACG_MatchOpponentCustomized_Client = (UniqueInfo.ConfigHashMap<int, TACG_MatchOpponentCustomized_Client>)_is.Read(mapACG_MatchOpponentCustomized_Client, 98, false);

            mapACG_NewerAIHeroDropEquipment_Client = (UniqueInfo.ConfigHashMap<int, TACG_NewerAIHeroDropEquipment_Client>)_is.Read(mapACG_NewerAIHeroDropEquipment_Client, 99, false);

            mapACG_Final_Phase_Client = (UniqueInfo.ConfigHashMap<int, TACG_Final_Phase_Client>)_is.Read(mapACG_Final_Phase_Client, 100, false);

            mapACG_Guide_Mission_Client = (UniqueInfo.ConfigHashMap<int, TACG_Guide_Mission_Client>)_is.Read(mapACG_Guide_Mission_Client, 101, false);

            mapACG_LP_Cacualte_Config_Client = (UniqueInfo.ConfigHashMap<int, TACG_LP_Cacualte_Config_Client>)_is.Read(mapACG_LP_Cacualte_Config_Client, 102, false);

            mapACG_MMR_Match_AI_Client = (UniqueInfo.ConfigHashMap<int, TACG_MMR_Match_AI_Client>)_is.Read(mapACG_MMR_Match_AI_Client, 103, false);

            mapACG_Notice_Client = (UniqueInfo.ConfigHashMap<int, TACG_Notice_Client>)_is.Read(mapACG_Notice_Client, 105, false);

            mapACG_NoviceReward_Client = (UniqueInfo.ConfigHashMap<int, TACG_NoviceReward_Client>)_is.Read(mapACG_NoviceReward_Client, 106, false);

            mapACG_ShareDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_ShareDrop_Client>)_is.Read(mapACG_ShareDrop_Client, 107, false);

            mapACG_RookieMission_Client = (UniqueInfo.ConfigHashMap<int, TACG_RookieMission_Client>)_is.Read(mapACG_RookieMission_Client, 108, false);

            mapACG_DeckRecommend_Client = (UniqueInfo.ConfigHashMap<int, TACG_DeckRecommend_Client>)_is.Read(mapACG_DeckRecommend_Client, 109, false);

            mapACG_ElementTerrain_Client = (UniqueInfo.ConfigHashMap<int, TACG_ElementTerrain_Client>)_is.Read(mapACG_ElementTerrain_Client, 110, false);

            mapACG_Examination_Info_Client = (UniqueInfo.ConfigHashMap<int, TACG_Examination_Info_Client>)_is.Read(mapACG_Examination_Info_Client, 111, false);

            mapACG_Examination_Questions_Client = (UniqueInfo.ConfigHashMap<int, TACG_Examination_Questions_Client>)_is.Read(mapACG_Examination_Questions_Client, 112, false);

            mapACG_Quiz_Event_Info_Client = (UniqueInfo.ConfigHashMap<int, TACG_Quiz_Event_Info_Client>)_is.Read(mapACG_Quiz_Event_Info_Client, 113, false);

            mapACG_HandbookRule_Client = (UniqueInfo.ConfigHashMap<int, TACG_HandbookRule_Client>)_is.Read(mapACG_HandbookRule_Client, 114, false);

            mapACG_ProgramWord_Client = (UniqueInfo.ConfigHashMap<int, TACG_ProgramWord_Client>)_is.Read(mapACG_ProgramWord_Client, 115, false);

            mapACG_SpriteHelper_Client = (UniqueInfo.ConfigHashMap<int, TACG_SpriteHelper_Client>)_is.Read(mapACG_SpriteHelper_Client, 116, false);

            mapACG_SpriteCondition_Client = (UniqueInfo.ConfigHashMap<int, TACG_SpriteCondition_Client>)_is.Read(mapACG_SpriteCondition_Client, 117, false);

            mapACG_SpriteTips_Client = (UniqueInfo.ConfigHashMap<int, TACG_SpriteTips_Client>)_is.Read(mapACG_SpriteTips_Client, 118, false);

            mapACG_RefreshHeroCustomized_Client = (UniqueInfo.ConfigHashMap<int, TACG_RefreshHeroCustomized_Client>)_is.Read(mapACG_RefreshHeroCustomized_Client, 119, false);

            mapACG_Set_Client = (UniqueInfo.ConfigHashMap<int, TACG_Set_Client>)_is.Read(mapACG_Set_Client, 120, false);

            mapACG_CollegeSet_Client = (UniqueInfo.ConfigHashMap<int, TACG_CollegeSet_Client>)_is.Read(mapACG_CollegeSet_Client, 121, false);

            mapACG_GuideSet_Client = (UniqueInfo.ConfigHashMap<int, TACG_GuideSet_Client>)_is.Read(mapACG_GuideSet_Client, 122, false);

            mapACG_UserProfilePanel_Client = (UniqueInfo.ConfigHashMap<int, TACG_UserProfilePanel_Client>)_is.Read(mapACG_UserProfilePanel_Client, 123, false);

            mapACG_BattleHelper_Client = (UniqueInfo.ConfigHashMap<int, TACG_BattleHelper_Client>)_is.Read(mapACG_BattleHelper_Client, 124, false);

            mapACG_CounterTriger_Client = (UniqueInfo.ConfigHashMap<int, TACG_CounterTriger_Client>)_is.Read(mapACG_CounterTriger_Client, 125, false);

            mapACG_BattleTipCondition_Client = (UniqueInfo.ConfigHashMap<int, TACG_BattleTipCondition_Client>)_is.Read(mapACG_BattleTipCondition_Client, 126, false);

            mapACG_BattleTips_Client = (UniqueInfo.ConfigHashMap<int, TACG_BattleTips_Client>)_is.Read(mapACG_BattleTips_Client, 127, false);

            mapACG_BattleTipsSwitch_Client = (UniqueInfo.ConfigHashMap<int, TACG_BattleTipsSwitch_Client>)_is.Read(mapACG_BattleTipsSwitch_Client, 128, false);

            mapACG_Jump_Client = (UniqueInfo.ConfigHashMap<int, TACG_Jump_Client>)_is.Read(mapACG_Jump_Client, 129, false);

            mapACG_FightResultTag_Client = (UniqueInfo.ConfigHashMap<int, TACG_FightResultTag_Client>)_is.Read(mapACG_FightResultTag_Client, 130, false);

            mapACG_RoundCoin_Client = (UniqueInfo.ConfigHashMap<int, TACG_RoundCoin_Client>)_is.Read(mapACG_RoundCoin_Client, 131, false);

            mapACG_RoundExp_Client = (UniqueInfo.ConfigHashMap<int, TACG_RoundExp_Client>)_is.Read(mapACG_RoundExp_Client, 132, false);

            mapACG_InitDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_InitDrop_Client>)_is.Read(mapACG_InitDrop_Client, 133, false);

            mapACG_RankDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_RankDrop_Client>)_is.Read(mapACG_RankDrop_Client, 134, false);

            mapACG_KillDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_KillDrop_Client>)_is.Read(mapACG_KillDrop_Client, 135, false);

            mapACG_StarDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_StarDrop_Client>)_is.Read(mapACG_StarDrop_Client, 136, false);

            mapACG_Skill_Mercenary_Client = (UniqueInfo.ConfigHashMap<int, TACG_Skill_Mercenary_Client>)_is.Read(mapACG_Skill_Mercenary_Client, 137, false);

            mapACG_Mercenary_Level_Client = (UniqueInfo.ConfigHashMap<int, TACG_Mercenary_Level_Client>)_is.Read(mapACG_Mercenary_Level_Client, 138, false);

            mapACG_SKill_Mercenary_Show_Client = (UniqueInfo.ConfigHashMap<int, TACG_SKill_Mercenary_Show_Client>)_is.Read(mapACG_SKill_Mercenary_Show_Client, 139, false);

            mapACG_Galaxy_Client = (UniqueInfo.ConfigHashMap<int, TACG_Galaxy_Client>)_is.Read(mapACG_Galaxy_Client, 140, false);

            mapACG_GalaxyPool_Client = (UniqueInfo.ConfigHashMap<int, TACG_GalaxyPool_Client>)_is.Read(mapACG_GalaxyPool_Client, 141, false);

            mapTAC_AudioMgr_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AudioMgr_Client>)_is.Read(mapTAC_AudioMgr_Client, 142, false);

            mapPandoraErrorCodeInfo_Client = (UniqueInfo.ConfigHashMap<int, TPandoraErrorCodeInfo_Client>)_is.Read(mapPandoraErrorCodeInfo_Client, 144, false);

            mapACG_GuideLabel_Client = (UniqueInfo.ConfigHashMap<int, TACG_GuideLabel_Client>)_is.Read(mapACG_GuideLabel_Client, 145, false);

            mapACG_GuideTipsPool_Client = (UniqueInfo.ConfigHashMap<int, TACG_GuideTipsPool_Client>)_is.Read(mapACG_GuideTipsPool_Client, 146, false);

            mapACG_GuidePushTip_Client = (UniqueInfo.ConfigHashMap<int, TACG_GuidePushTip_Client>)_is.Read(mapACG_GuidePushTip_Client, 147, false);

            mapACG_RankScores_Client = (UniqueInfo.ConfigHashMap<int, TACG_RankScores_Client>)_is.Read(mapACG_RankScores_Client, 148, false);

            mapACG_TextTipConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_TextTipConfig_Client>)_is.Read(mapACG_TextTipConfig_Client, 149, false);

            mapTAC_AIWarmBattle_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIWarmBattle_Client>)_is.Read(mapTAC_AIWarmBattle_Client, 151, false);

            mapACG_Newcomer_Config_Client = (UniqueInfo.ConfigHashMap<int, TACG_Newcomer_Config_Client>)_is.Read(mapACG_Newcomer_Config_Client, 152, false);

            mapACG_GuideCommonConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_GuideCommonConfig_Client>)_is.Read(mapACG_GuideCommonConfig_Client, 153, false);

            mapACG_GuideEquipmentConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_GuideEquipmentConfig_Client>)_is.Read(mapACG_GuideEquipmentConfig_Client, 154, false);

            mapACG_GuideHeroConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_GuideHeroConfig_Client>)_is.Read(mapACG_GuideHeroConfig_Client, 155, false);

            mapACG_GuidePosConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_GuidePosConfig_Client>)_is.Read(mapACG_GuidePosConfig_Client, 156, false);

            mapTAC_AIDropEquipNew_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIDropEquipNew_Client>)_is.Read(mapTAC_AIDropEquipNew_Client, 158, false);

            mapACG_GuideFetterConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_GuideFetterConfig_Client>)_is.Read(mapACG_GuideFetterConfig_Client, 159, false);

            mapEvents_Client = (UniqueInfo.ConfigHashMap<int, TEvents_Client>)_is.Read(mapEvents_Client, 160, false);

            mapEventIntroduction_Client = (UniqueInfo.ConfigHashMap<int, TEventIntroduction_Client>)_is.Read(mapEventIntroduction_Client, 161, false);

            mapACGHeavenBuff_Client = (UniqueInfo.ConfigHashMap<int, TACGHeavenBuff_Client>)_is.Read(mapACGHeavenBuff_Client, 163, false);

            mapACG_Buff_HeavenSelect_Level_Client = (UniqueInfo.ConfigHashMap<int, TACG_Buff_HeavenSelect_Level_Client>)_is.Read(mapACG_Buff_HeavenSelect_Level_Client, 164, false);

            mapACG_Buff_HeavenSelect_Client = (UniqueInfo.ConfigHashMap<int, TACG_Buff_HeavenSelect_Client>)_is.Read(mapACG_Buff_HeavenSelect_Client, 165, false);

            mapACG_Buff_HeavenSelect_Show_Client = (UniqueInfo.ConfigHashMap<int, TACG_Buff_HeavenSelect_Show_Client>)_is.Read(mapACG_Buff_HeavenSelect_Show_Client, 166, false);

            mapK6UserTag_Client = (UniqueInfo.ConfigHashMap<int, TK6UserTag_Client>)_is.Read(mapK6UserTag_Client, 167, false);

            mapACG_MissTrans_Client = (UniqueInfo.ConfigHashMap<int, TACG_MissTrans_Client>)_is.Read(mapACG_MissTrans_Client, 168, false);

            mapChallengeMode_Client = (UniqueInfo.ConfigHashMap<int, TChallengeMode_Client>)_is.Read(mapChallengeMode_Client, 174, false);

            mapACG_GuideEntryConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_GuideEntryConfig_Client>)_is.Read(mapACG_GuideEntryConfig_Client, 179, false);

            mapTAC_BalanceRuleConfig_Client = (UniqueInfo.ConfigHashMap<int, TTAC_BalanceRuleConfig_Client>)_is.Read(mapTAC_BalanceRuleConfig_Client, 180, false);

            mapACG_LobbyHeroValid_Client = (UniqueInfo.ConfigHashMap<int, TACG_LobbyHeroValid_Client>)_is.Read(mapACG_LobbyHeroValid_Client, 181, false);

            mapACG_LobbyHeroPassiveChecking_Client = (UniqueInfo.ConfigHashMap<int, TACG_LobbyHeroPassiveChecking_Client>)_is.Read(mapACG_LobbyHeroPassiveChecking_Client, 182, false);

            mapACG_WwiseBankConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_WwiseBankConfig_Client>)_is.Read(mapACG_WwiseBankConfig_Client, 183, false);

            mapTAC_StagePlus_Client = (UniqueInfo.ConfigHashMap<int, TTAC_StagePlus_Client>)_is.Read(mapTAC_StagePlus_Client, 184, false);

            mapTAC_Round_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Round_Client>)_is.Read(mapTAC_Round_Client, 185, false);

            mapACG_Camera_Client = (UniqueInfo.ConfigHashMap<int, TACG_Camera_Client>)_is.Read(mapACG_Camera_Client, 186, false);

            mapACG_WarmRefreshCustomized_Client = (UniqueInfo.ConfigHashMap<int, TACG_WarmRefreshCustomized_Client>)_is.Read(mapACG_WarmRefreshCustomized_Client, 187, false);

            mapACG_WarmRefreshPlan_Client = (UniqueInfo.ConfigHashMap<int, TACG_WarmRefreshPlan_Client>)_is.Read(mapACG_WarmRefreshPlan_Client, 188, false);

            mapUserTagConfig_Client = (UniqueInfo.ConfigHashMap<int, TUserTagConfig_Client>)_is.Read(mapUserTagConfig_Client, 191, false);

            mapACGDebuffGroup_Client = (UniqueInfo.ConfigHashMap<int, TACGDebuffGroup_Client>)_is.Read(mapACGDebuffGroup_Client, 192, false);

            mapModuleSwitch_Client = (UniqueInfo.ConfigHashMap<int, TModuleSwitch_Client>)_is.Read(mapModuleSwitch_Client, 193, false);

            mapACG_EquipmentStoreS5_Client = (UniqueInfo.ConfigHashMap<int, TACG_EquipmentStoreS5_Client>)_is.Read(mapACG_EquipmentStoreS5_Client, 197, false);

            mapTurbo_LP_Cacualte_Config_Client = (UniqueInfo.ConfigHashMap<int, TTurbo_LP_Cacualte_Config_Client>)_is.Read(mapTurbo_LP_Cacualte_Config_Client, 198, false);

            mapACG_DraconicDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_DraconicDrop_Client>)_is.Read(mapACG_DraconicDrop_Client, 199, false);

            mapTAC_ExtraRoomCfg_Client = (UniqueInfo.ConfigHashMap<int, TTAC_ExtraRoomCfg_Client>)_is.Read(mapTAC_ExtraRoomCfg_Client, 200, false);

            mapACG_Channel_list_Client = (UniqueInfo.ConfigHashMap<int, TACG_Channel_list_Client>)_is.Read(mapACG_Channel_list_Client, 201, false);

            mapChat_BubbleFrame_Client = (UniqueInfo.ConfigHashMap<int, TChat_BubbleFrame_Client>)_is.Read(mapChat_BubbleFrame_Client, 202, false);

            mapClubGlobal_Client = (UniqueInfo.ConfigHashMap<int, TClubGlobal_Client>)_is.Read(mapClubGlobal_Client, 203, false);

            mapACG_Destiny_Client = (UniqueInfo.ConfigHashMap<int, TACG_Destiny_Client>)_is.Read(mapACG_Destiny_Client, 204, false);

            mapTAC_AISitPos_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AISitPos_Client>)_is.Read(mapTAC_AISitPos_Client, 205, false);

            mapACG_ExtraStealEquipRate_Client = (UniqueInfo.ConfigHashMap<int, TACG_ExtraStealEquipRate_Client>)_is.Read(mapACG_ExtraStealEquipRate_Client, 206, false);

            mapACG_HolyDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_HolyDrop_Client>)_is.Read(mapACG_HolyDrop_Client, 207, false);

            mapACG_HolyDropPool_Client = (UniqueInfo.ConfigHashMap<int, TACG_HolyDropPool_Client>)_is.Read(mapACG_HolyDropPool_Client, 208, false);

            mapACG_DestinyBanner_Client = (UniqueInfo.ConfigHashMap<int, TACG_DestinyBanner_Client>)_is.Read(mapACG_DestinyBanner_Client, 209, false);

            mapACG_TechnologyJinNeng_Backgroun_Client = (UniqueInfo.ConfigHashMap<int, TACG_TechnologyJinNeng_Backgroun_Client>)_is.Read(mapACG_TechnologyJinNeng_Backgroun_Client, 210, false);

            mapACG_SeasonInfo_Client = (UniqueInfo.ConfigHashMap<int, TACG_SeasonInfo_Client>)_is.Read(mapACG_SeasonInfo_Client, 211, false);

            mapACG_RecommendClientCfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_RecommendClientCfg_Client>)_is.Read(mapACG_RecommendClientCfg_Client, 212, false);

            mapClubMall_Client = (UniqueInfo.ConfigHashMap<int, TClubMall_Client>)_is.Read(mapClubMall_Client, 218, false);

            stTAllGameCfgClientExt1 = (TAllGameCfgClientExt1)_is.Read(stTAllGameCfgClientExt1, 219, false);

            mapGameModuleGuideIntro_Client = (UniqueInfo.ConfigHashMap<int, TGameModuleGuideIntro_Client>)_is.Read(mapGameModuleGuideIntro_Client, 221, false);

            mapACG_CombatEffect_Client = (UniqueInfo.ConfigHashMap<int, TACG_CombatEffect_Client>)_is.Read(mapACG_CombatEffect_Client, 222, false);

            mapACG_HeroSkin_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeroSkin_Client>)_is.Read(mapACG_HeroSkin_Client, 223, false);

            mapChat_SimpleResponseVoice_Client = (UniqueInfo.ConfigHashMap<int, TChat_SimpleResponseVoice_Client>)_is.Read(mapChat_SimpleResponseVoice_Client, 224, false);

            mapManualTransTable_Client = (UniqueInfo.ConfigHashMap<int, TManualTransTable_Client>)_is.Read(mapManualTransTable_Client, 225, false);

            mapACG_ItemGetType_Client = (UniqueInfo.ConfigHashMap<int, TACG_ItemGetType_Client>)_is.Read(mapACG_ItemGetType_Client, 226, false);

            stTAllGameCfgClientExt2 = (TAllGameCfgClientExt2)_is.Read(stTAllGameCfgClientExt2, 227, false);

            stTAllGameCfgClientExt3 = (TAllGameCfgClientExt3)_is.Read(stTAllGameCfgClientExt3, 228, false);

            stTAllGameCfgClientExt4 = (TAllGameCfgClientExt4)_is.Read(stTAllGameCfgClientExt4, 229, false);

            stTAllGameCfgClientExt5 = (TAllGameCfgClientExt5)_is.Read(stTAllGameCfgClientExt5, 230, false);

            stTAllGameCfgClientExt6 = (TAllGameCfgClientExt6)_is.Read(stTAllGameCfgClientExt6, 231, false);

            mapBattleEndDisplayIconCfg_Client = (UniqueInfo.ConfigHashMap<int, TBattleEndDisplayIconCfg_Client>)_is.Read(mapBattleEndDisplayIconCfg_Client, 232, false);

            mapACG_MallBuyTips_Client = (UniqueInfo.ConfigHashMap<int, TACG_MallBuyTips_Client>)_is.Read(mapACG_MallBuyTips_Client, 233, false);

            mapACG_PrimeData_Client = (UniqueInfo.ConfigHashMap<int, TACG_PrimeData_Client>)_is.Read(mapACG_PrimeData_Client, 234, false);

            mapACG_LuckShare_Client = (UniqueInfo.ConfigHashMap<int, TACG_LuckShare_Client>)_is.Read(mapACG_LuckShare_Client, 235, false);

            mapJOCRank_LP_Cacualte_Config_Client = (UniqueInfo.ConfigHashMap<int, TJOCRank_LP_Cacualte_Config_Client>)_is.Read(mapJOCRank_LP_Cacualte_Config_Client, 236, false);

            mapTAC_FetterMusic_Client = (UniqueInfo.ConfigHashMap<int, TTAC_FetterMusic_Client>)_is.Read(mapTAC_FetterMusic_Client, 237, false);

            mapACGRecordBuffTrigger_Client = (UniqueInfo.ConfigHashMap<int, TACGRecordBuffTrigger_Client>)_is.Read(mapACGRecordBuffTrigger_Client, 238, false);

            mapTAC_FetterMusicPlan_Client = (UniqueInfo.ConfigHashMap<int, TTAC_FetterMusicPlan_Client>)_is.Read(mapTAC_FetterMusicPlan_Client, 239, false);

            stTAllGameCfgClientExt7 = (TAllGameCfgClientExt7)_is.Read(stTAllGameCfgClientExt7, 240, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(mapACG_MatchingLoadTips_Client, "mapACG_MatchingLoadTips_Client");
            _ds.Display(mapACG_Activity_Client, "mapACG_Activity_Client");
            _ds.Display(mapACG_Activity_Tag_Client, "mapACG_Activity_Tag_Client");
            _ds.Display(mapACG_Activity_Page_Client, "mapACG_Activity_Page_Client");
            _ds.Display(mapGPU_Client, "mapGPU_Client");
            _ds.Display(mapGlobal_Client, "mapGlobal_Client");
            _ds.Display(mapTAC_ReportFeature_Client, "mapTAC_ReportFeature_Client");
            _ds.Display(mapBPConfig_Client, "mapBPConfig_Client");
            _ds.Display(mapBPPay_Client, "mapBPPay_Client");
            _ds.Display(mapDailyTask_Client, "mapDailyTask_Client");
            _ds.Display(mapDailyTaskGroup_Client, "mapDailyTaskGroup_Client");
            _ds.Display(mapEnlighten_Client, "mapEnlighten_Client");
            _ds.Display(mapEnlightenRate_Client, "mapEnlightenRate_Client");
            _ds.Display(mapBPMall_Client, "mapBPMall_Client");
            _ds.Display(mapACG_DropBoxItem_Client, "mapACG_DropBoxItem_Client");
            _ds.Display(mapACGSkill_Client, "mapACGSkill_Client");
            _ds.Display(mapACGSkillDurationTarget_Client, "mapACGSkillDurationTarget_Client");
            _ds.Display(mapACG_Equipment_Client, "mapACG_Equipment_Client");
            _ds.Display(mapACG_EquipmentRandom_Client, "mapACG_EquipmentRandom_Client");
            _ds.Display(mapACG_Attribute_Client, "mapACG_Attribute_Client");
            _ds.Display(mapACG_StealLevelRate_Client, "mapACG_StealLevelRate_Client");
            _ds.Display(mapACG_StealEquipRate_Client, "mapACG_StealEquipRate_Client");
            _ds.Display(mapACG_EquipmentStore_Client, "mapACG_EquipmentStore_Client");
            _ds.Display(mapTAC_Module_Client, "mapTAC_Module_Client");
            _ds.Display(mapACG_RandName_Client, "mapACG_RandName_Client");
            _ds.Display(mapTAC_Level_Client, "mapTAC_Level_Client");
            _ds.Display(mapTAC_NewLevel_Client, "mapTAC_NewLevel_Client");
            _ds.Display(mapACG_HeroRate_Client, "mapACG_HeroRate_Client");
            _ds.Display(mapACG_EquipRate_Client, "mapACG_EquipRate_Client");
            _ds.Display(mapACG_HeroEquipMatchRate_Client, "mapACG_HeroEquipMatchRate_Client");
            _ds.Display(mapACG_SharedDraftCfg_Client, "mapACG_SharedDraftCfg_Client");
            _ds.Display(mapACG_BREquipRate_Client, "mapACG_BREquipRate_Client");
            _ds.Display(mapACG_BRHeroRate_Client, "mapACG_BRHeroRate_Client");
            _ds.Display(mapACG_AdvancedEquipRate_Client, "mapACG_AdvancedEquipRate_Client");
            _ds.Display(mapACG_MonsterDrop_Client, "mapACG_MonsterDrop_Client");
            _ds.Display(mapACG_DropPoolItem_Client, "mapACG_DropPoolItem_Client");
            _ds.Display(mapACG_DropBuff_Client, "mapACG_DropBuff_Client");
            _ds.Display(mapACG_BoxType_Client, "mapACG_BoxType_Client");
            _ds.Display(mapACG_BoxSkin_Client, "mapACG_BoxSkin_Client");
            _ds.Display(mapACG_WarmDropBuff_Client, "mapACG_WarmDropBuff_Client");
            _ds.Display(mapACG_AdvancedDrop_Client, "mapACG_AdvancedDrop_Client");
            _ds.Display(mapACG_MatchingControl_Client, "mapACG_MatchingControl_Client");
            _ds.Display(mapTAC_ErrorCode_Client, "mapTAC_ErrorCode_Client");
            _ds.Display(mapTAC_LoadingTips_Client, "mapTAC_LoadingTips_Client");
            _ds.Display(mapTAC_Quest_Client, "mapTAC_Quest_Client");
            _ds.Display(mapTAC_Quest_NPC_Client, "mapTAC_Quest_NPC_Client");
            _ds.Display(mapAutoTransColumns_Client, "mapAutoTransColumns_Client");
            _ds.Display(mapACGBuffProto_Client, "mapACGBuffProto_Client");
            _ds.Display(mapACGBuff_Client, "mapACGBuff_Client");
            _ds.Display(mapACGFetters_Client, "mapACGFetters_Client");
            _ds.Display(mapACGBuffEffectGroup_Client, "mapACGBuffEffectGroup_Client");
            _ds.Display(mapTAC_Global_Client, "mapTAC_Global_Client");
            _ds.Display(mapTAC_RoomCfg_Client, "mapTAC_RoomCfg_Client");
            _ds.Display(mapACG_Stage_Priority_Client, "mapACG_Stage_Priority_Client");
            _ds.Display(mapTAC_Tag_Client, "mapTAC_Tag_Client");
            _ds.Display(mapTAC_Priority_Client, "mapTAC_Priority_Client");
            _ds.Display(mapTAC_Power_Client, "mapTAC_Power_Client");
            _ds.Display(mapTAC_CPower_Client, "mapTAC_CPower_Client");
            _ds.Display(mapTAC_AIEquip_Client, "mapTAC_AIEquip_Client");
            _ds.Display(mapTAC_AIID_Client, "mapTAC_AIID_Client");
            _ds.Display(mapTAC_AIRefresh_Client, "mapTAC_AIRefresh_Client");
            _ds.Display(mapTAC_AIAssignTag_Client, "mapTAC_AIAssignTag_Client");
            _ds.Display(mapTAC_AIDropEquip_Client, "mapTAC_AIDropEquip_Client");
            _ds.Display(mapTAC_AICardBuff_Client, "mapTAC_AICardBuff_Client");
            _ds.Display(mapTAC_EquipStrength_Client, "mapTAC_EquipStrength_Client");
            _ds.Display(mapTAC_HeroKu_Client, "mapTAC_HeroKu_Client");
            _ds.Display(mapChat_SimpleResponse_Client, "mapChat_SimpleResponse_Client");
            _ds.Display(mapACG_Hero_Client, "mapACG_Hero_Client");
            _ds.Display(mapACG_SpecName_Client, "mapACG_SpecName_Client");
            _ds.Display(mapACG_ClassName_Client, "mapACG_ClassName_Client");
            _ds.Display(mapACG_TagName_Client, "mapACG_TagName_Client");
            _ds.Display(mapACG_Summon_Client, "mapACG_Summon_Client");
            _ds.Display(mapACG_BattleMergeHero_Client, "mapACG_BattleMergeHero_Client");
            _ds.Display(mapTAC_SXScreen_Client, "mapTAC_SXScreen_Client");
            _ds.Display(mapACG_DamageRules_Hero_Client, "mapACG_DamageRules_Hero_Client");
            _ds.Display(mapACG_DamageRules_Round_Client, "mapACG_DamageRules_Round_Client");
            _ds.Display(mapACG_Item_Client, "mapACG_Item_Client");
            _ds.Display(mapACG_ChestKey_Client, "mapACG_ChestKey_Client");
            _ds.Display(mapACG_HeroVioce_Client, "mapACG_HeroVioce_Client");
            _ds.Display(mapACG_TeamLeaderVioce_Client, "mapACG_TeamLeaderVioce_Client");
            _ds.Display(mapACG_Mall_Client, "mapACG_Mall_Client");
            _ds.Display(mapACG_Recommend_Client, "mapACG_Recommend_Client");
            _ds.Display(mapACG_Egg_Client, "mapACG_Egg_Client");
            _ds.Display(mapACG_PageInfo_Client, "mapACG_PageInfo_Client");
            _ds.Display(mapACGHeroStory_Client, "mapACGHeroStory_Client");
            _ds.Display(mapACG_Mail_Client, "mapACG_Mail_Client");
            _ds.Display(mapACG_HeroBuyCustomized_Client, "mapACG_HeroBuyCustomized_Client");
            _ds.Display(mapACG_RoundSelectCustomized_Client, "mapACG_RoundSelectCustomized_Client");
            _ds.Display(mapACG_MatchOpponentCustomized_Client, "mapACG_MatchOpponentCustomized_Client");
            _ds.Display(mapACG_NewerAIHeroDropEquipment_Client, "mapACG_NewerAIHeroDropEquipment_Client");
            _ds.Display(mapACG_Final_Phase_Client, "mapACG_Final_Phase_Client");
            _ds.Display(mapACG_Guide_Mission_Client, "mapACG_Guide_Mission_Client");
            _ds.Display(mapACG_LP_Cacualte_Config_Client, "mapACG_LP_Cacualte_Config_Client");
            _ds.Display(mapACG_MMR_Match_AI_Client, "mapACG_MMR_Match_AI_Client");
            _ds.Display(mapACG_Notice_Client, "mapACG_Notice_Client");
            _ds.Display(mapACG_NoviceReward_Client, "mapACG_NoviceReward_Client");
            _ds.Display(mapACG_ShareDrop_Client, "mapACG_ShareDrop_Client");
            _ds.Display(mapACG_RookieMission_Client, "mapACG_RookieMission_Client");
            _ds.Display(mapACG_DeckRecommend_Client, "mapACG_DeckRecommend_Client");
            _ds.Display(mapACG_ElementTerrain_Client, "mapACG_ElementTerrain_Client");
            _ds.Display(mapACG_Examination_Info_Client, "mapACG_Examination_Info_Client");
            _ds.Display(mapACG_Examination_Questions_Client, "mapACG_Examination_Questions_Client");
            _ds.Display(mapACG_Quiz_Event_Info_Client, "mapACG_Quiz_Event_Info_Client");
            _ds.Display(mapACG_HandbookRule_Client, "mapACG_HandbookRule_Client");
            _ds.Display(mapACG_ProgramWord_Client, "mapACG_ProgramWord_Client");
            _ds.Display(mapACG_SpriteHelper_Client, "mapACG_SpriteHelper_Client");
            _ds.Display(mapACG_SpriteCondition_Client, "mapACG_SpriteCondition_Client");
            _ds.Display(mapACG_SpriteTips_Client, "mapACG_SpriteTips_Client");
            _ds.Display(mapACG_RefreshHeroCustomized_Client, "mapACG_RefreshHeroCustomized_Client");
            _ds.Display(mapACG_Set_Client, "mapACG_Set_Client");
            _ds.Display(mapACG_CollegeSet_Client, "mapACG_CollegeSet_Client");
            _ds.Display(mapACG_GuideSet_Client, "mapACG_GuideSet_Client");
            _ds.Display(mapACG_UserProfilePanel_Client, "mapACG_UserProfilePanel_Client");
            _ds.Display(mapACG_BattleHelper_Client, "mapACG_BattleHelper_Client");
            _ds.Display(mapACG_CounterTriger_Client, "mapACG_CounterTriger_Client");
            _ds.Display(mapACG_BattleTipCondition_Client, "mapACG_BattleTipCondition_Client");
            _ds.Display(mapACG_BattleTips_Client, "mapACG_BattleTips_Client");
            _ds.Display(mapACG_BattleTipsSwitch_Client, "mapACG_BattleTipsSwitch_Client");
            _ds.Display(mapACG_Jump_Client, "mapACG_Jump_Client");
            _ds.Display(mapACG_FightResultTag_Client, "mapACG_FightResultTag_Client");
            _ds.Display(mapACG_RoundCoin_Client, "mapACG_RoundCoin_Client");
            _ds.Display(mapACG_RoundExp_Client, "mapACG_RoundExp_Client");
            _ds.Display(mapACG_InitDrop_Client, "mapACG_InitDrop_Client");
            _ds.Display(mapACG_RankDrop_Client, "mapACG_RankDrop_Client");
            _ds.Display(mapACG_KillDrop_Client, "mapACG_KillDrop_Client");
            _ds.Display(mapACG_StarDrop_Client, "mapACG_StarDrop_Client");
            _ds.Display(mapACG_Skill_Mercenary_Client, "mapACG_Skill_Mercenary_Client");
            _ds.Display(mapACG_Mercenary_Level_Client, "mapACG_Mercenary_Level_Client");
            _ds.Display(mapACG_SKill_Mercenary_Show_Client, "mapACG_SKill_Mercenary_Show_Client");
            _ds.Display(mapACG_Galaxy_Client, "mapACG_Galaxy_Client");
            _ds.Display(mapACG_GalaxyPool_Client, "mapACG_GalaxyPool_Client");
            _ds.Display(mapTAC_AudioMgr_Client, "mapTAC_AudioMgr_Client");
            _ds.Display(mapPandoraErrorCodeInfo_Client, "mapPandoraErrorCodeInfo_Client");
            _ds.Display(mapACG_GuideLabel_Client, "mapACG_GuideLabel_Client");
            _ds.Display(mapACG_GuideTipsPool_Client, "mapACG_GuideTipsPool_Client");
            _ds.Display(mapACG_GuidePushTip_Client, "mapACG_GuidePushTip_Client");
            _ds.Display(mapACG_RankScores_Client, "mapACG_RankScores_Client");
            _ds.Display(mapACG_TextTipConfig_Client, "mapACG_TextTipConfig_Client");
            _ds.Display(mapTAC_AIWarmBattle_Client, "mapTAC_AIWarmBattle_Client");
            _ds.Display(mapACG_Newcomer_Config_Client, "mapACG_Newcomer_Config_Client");
            _ds.Display(mapACG_GuideCommonConfig_Client, "mapACG_GuideCommonConfig_Client");
            _ds.Display(mapACG_GuideEquipmentConfig_Client, "mapACG_GuideEquipmentConfig_Client");
            _ds.Display(mapACG_GuideHeroConfig_Client, "mapACG_GuideHeroConfig_Client");
            _ds.Display(mapACG_GuidePosConfig_Client, "mapACG_GuidePosConfig_Client");
            _ds.Display(mapTAC_AIDropEquipNew_Client, "mapTAC_AIDropEquipNew_Client");
            _ds.Display(mapACG_GuideFetterConfig_Client, "mapACG_GuideFetterConfig_Client");
            _ds.Display(mapEvents_Client, "mapEvents_Client");
            _ds.Display(mapEventIntroduction_Client, "mapEventIntroduction_Client");
            _ds.Display(mapACGHeavenBuff_Client, "mapACGHeavenBuff_Client");
            _ds.Display(mapACG_Buff_HeavenSelect_Level_Client, "mapACG_Buff_HeavenSelect_Level_Client");
            _ds.Display(mapACG_Buff_HeavenSelect_Client, "mapACG_Buff_HeavenSelect_Client");
            _ds.Display(mapACG_Buff_HeavenSelect_Show_Client, "mapACG_Buff_HeavenSelect_Show_Client");
            _ds.Display(mapK6UserTag_Client, "mapK6UserTag_Client");
            _ds.Display(mapACG_MissTrans_Client, "mapACG_MissTrans_Client");
            _ds.Display(mapChallengeMode_Client, "mapChallengeMode_Client");
            _ds.Display(mapACG_GuideEntryConfig_Client, "mapACG_GuideEntryConfig_Client");
            _ds.Display(mapTAC_BalanceRuleConfig_Client, "mapTAC_BalanceRuleConfig_Client");
            _ds.Display(mapACG_LobbyHeroValid_Client, "mapACG_LobbyHeroValid_Client");
            _ds.Display(mapACG_LobbyHeroPassiveChecking_Client, "mapACG_LobbyHeroPassiveChecking_Client");
            _ds.Display(mapACG_WwiseBankConfig_Client, "mapACG_WwiseBankConfig_Client");
            _ds.Display(mapTAC_StagePlus_Client, "mapTAC_StagePlus_Client");
            _ds.Display(mapTAC_Round_Client, "mapTAC_Round_Client");
            _ds.Display(mapACG_Camera_Client, "mapACG_Camera_Client");
            _ds.Display(mapACG_WarmRefreshCustomized_Client, "mapACG_WarmRefreshCustomized_Client");
            _ds.Display(mapACG_WarmRefreshPlan_Client, "mapACG_WarmRefreshPlan_Client");
            _ds.Display(mapUserTagConfig_Client, "mapUserTagConfig_Client");
            _ds.Display(mapACGDebuffGroup_Client, "mapACGDebuffGroup_Client");
            _ds.Display(mapModuleSwitch_Client, "mapModuleSwitch_Client");
            _ds.Display(mapACG_EquipmentStoreS5_Client, "mapACG_EquipmentStoreS5_Client");
            _ds.Display(mapTurbo_LP_Cacualte_Config_Client, "mapTurbo_LP_Cacualte_Config_Client");
            _ds.Display(mapACG_DraconicDrop_Client, "mapACG_DraconicDrop_Client");
            _ds.Display(mapTAC_ExtraRoomCfg_Client, "mapTAC_ExtraRoomCfg_Client");
            _ds.Display(mapACG_Channel_list_Client, "mapACG_Channel_list_Client");
            _ds.Display(mapChat_BubbleFrame_Client, "mapChat_BubbleFrame_Client");
            _ds.Display(mapClubGlobal_Client, "mapClubGlobal_Client");
            _ds.Display(mapACG_Destiny_Client, "mapACG_Destiny_Client");
            _ds.Display(mapTAC_AISitPos_Client, "mapTAC_AISitPos_Client");
            _ds.Display(mapACG_ExtraStealEquipRate_Client, "mapACG_ExtraStealEquipRate_Client");
            _ds.Display(mapACG_HolyDrop_Client, "mapACG_HolyDrop_Client");
            _ds.Display(mapACG_HolyDropPool_Client, "mapACG_HolyDropPool_Client");
            _ds.Display(mapACG_DestinyBanner_Client, "mapACG_DestinyBanner_Client");
            _ds.Display(mapACG_TechnologyJinNeng_Backgroun_Client, "mapACG_TechnologyJinNeng_Backgroun_Client");
            _ds.Display(mapACG_SeasonInfo_Client, "mapACG_SeasonInfo_Client");
            _ds.Display(mapACG_RecommendClientCfg_Client, "mapACG_RecommendClientCfg_Client");
            _ds.Display(mapClubMall_Client, "mapClubMall_Client");
            _ds.Display(stTAllGameCfgClientExt1, "stTAllGameCfgClientExt1");
            _ds.Display(mapGameModuleGuideIntro_Client, "mapGameModuleGuideIntro_Client");
            _ds.Display(mapACG_CombatEffect_Client, "mapACG_CombatEffect_Client");
            _ds.Display(mapACG_HeroSkin_Client, "mapACG_HeroSkin_Client");
            _ds.Display(mapChat_SimpleResponseVoice_Client, "mapChat_SimpleResponseVoice_Client");
            _ds.Display(mapManualTransTable_Client, "mapManualTransTable_Client");
            _ds.Display(mapACG_ItemGetType_Client, "mapACG_ItemGetType_Client");
            _ds.Display(stTAllGameCfgClientExt2, "stTAllGameCfgClientExt2");
            _ds.Display(stTAllGameCfgClientExt3, "stTAllGameCfgClientExt3");
            _ds.Display(stTAllGameCfgClientExt4, "stTAllGameCfgClientExt4");
            _ds.Display(stTAllGameCfgClientExt5, "stTAllGameCfgClientExt5");
            _ds.Display(stTAllGameCfgClientExt6, "stTAllGameCfgClientExt6");
            _ds.Display(mapBattleEndDisplayIconCfg_Client, "mapBattleEndDisplayIconCfg_Client");
            _ds.Display(mapACG_MallBuyTips_Client, "mapACG_MallBuyTips_Client");
            _ds.Display(mapACG_PrimeData_Client, "mapACG_PrimeData_Client");
            _ds.Display(mapACG_LuckShare_Client, "mapACG_LuckShare_Client");
            _ds.Display(mapJOCRank_LP_Cacualte_Config_Client, "mapJOCRank_LP_Cacualte_Config_Client");
            _ds.Display(mapTAC_FetterMusic_Client, "mapTAC_FetterMusic_Client");
            _ds.Display(mapACGRecordBuffTrigger_Client, "mapACGRecordBuffTrigger_Client");
            _ds.Display(mapTAC_FetterMusicPlan_Client, "mapTAC_FetterMusicPlan_Client");
            _ds.Display(stTAllGameCfgClientExt7, "stTAllGameCfgClientExt7");
        }

        public override void Clear()
        {
            if (mapACG_MatchingLoadTips_Client != null) mapACG_MatchingLoadTips_Client.Clear();
            if (mapACG_Activity_Client != null) mapACG_Activity_Client.Clear();
            if (mapACG_Activity_Tag_Client != null) mapACG_Activity_Tag_Client.Clear();
            if (mapACG_Activity_Page_Client != null) mapACG_Activity_Page_Client.Clear();
            if (mapGPU_Client != null) mapGPU_Client.Clear();
            if (mapGlobal_Client != null) mapGlobal_Client.Clear();
            if (mapTAC_ReportFeature_Client != null) mapTAC_ReportFeature_Client.Clear();
            if (mapBPConfig_Client != null) mapBPConfig_Client.Clear();
            if (mapBPPay_Client != null) mapBPPay_Client.Clear();
            if (mapDailyTask_Client != null) mapDailyTask_Client.Clear();
            if (mapDailyTaskGroup_Client != null) mapDailyTaskGroup_Client.Clear();
            if (mapEnlighten_Client != null) mapEnlighten_Client.Clear();
            if (mapEnlightenRate_Client != null) mapEnlightenRate_Client.Clear();
            if (mapBPMall_Client != null) mapBPMall_Client.Clear();
            if (mapACG_DropBoxItem_Client != null) mapACG_DropBoxItem_Client.Clear();
            if (mapACGSkill_Client != null) mapACGSkill_Client.Clear();
            if (mapACGSkillDurationTarget_Client != null) mapACGSkillDurationTarget_Client.Clear();
            if (mapACG_Equipment_Client != null) mapACG_Equipment_Client.Clear();
            if (mapACG_EquipmentRandom_Client != null) mapACG_EquipmentRandom_Client.Clear();
            if (mapACG_Attribute_Client != null) mapACG_Attribute_Client.Clear();
            if (mapACG_StealLevelRate_Client != null) mapACG_StealLevelRate_Client.Clear();
            if (mapACG_StealEquipRate_Client != null) mapACG_StealEquipRate_Client.Clear();
            if (mapACG_EquipmentStore_Client != null) mapACG_EquipmentStore_Client.Clear();
            if (mapTAC_Module_Client != null) mapTAC_Module_Client.Clear();
            if (mapACG_RandName_Client != null) mapACG_RandName_Client.Clear();
            if (mapTAC_Level_Client != null) mapTAC_Level_Client.Clear();
            if (mapTAC_NewLevel_Client != null) mapTAC_NewLevel_Client.Clear();
            if (mapACG_HeroRate_Client != null) mapACG_HeroRate_Client.Clear();
            if (mapACG_EquipRate_Client != null) mapACG_EquipRate_Client.Clear();
            if (mapACG_HeroEquipMatchRate_Client != null) mapACG_HeroEquipMatchRate_Client.Clear();
            if (mapACG_SharedDraftCfg_Client != null) mapACG_SharedDraftCfg_Client.Clear();
            if (mapACG_BREquipRate_Client != null) mapACG_BREquipRate_Client.Clear();
            if (mapACG_BRHeroRate_Client != null) mapACG_BRHeroRate_Client.Clear();
            if (mapACG_AdvancedEquipRate_Client != null) mapACG_AdvancedEquipRate_Client.Clear();
            if (mapACG_MonsterDrop_Client != null) mapACG_MonsterDrop_Client.Clear();
            if (mapACG_DropPoolItem_Client != null) mapACG_DropPoolItem_Client.Clear();
            if (mapACG_DropBuff_Client != null) mapACG_DropBuff_Client.Clear();
            if (mapACG_BoxType_Client != null) mapACG_BoxType_Client.Clear();
            if (mapACG_BoxSkin_Client != null) mapACG_BoxSkin_Client.Clear();
            if (mapACG_WarmDropBuff_Client != null) mapACG_WarmDropBuff_Client.Clear();
            if (mapACG_AdvancedDrop_Client != null) mapACG_AdvancedDrop_Client.Clear();
            if (mapACG_MatchingControl_Client != null) mapACG_MatchingControl_Client.Clear();
            if (mapTAC_ErrorCode_Client != null) mapTAC_ErrorCode_Client.Clear();
            if (mapTAC_LoadingTips_Client != null) mapTAC_LoadingTips_Client.Clear();
            if (mapTAC_Quest_Client != null) mapTAC_Quest_Client.Clear();
            if (mapTAC_Quest_NPC_Client != null) mapTAC_Quest_NPC_Client.Clear();
            if (mapAutoTransColumns_Client != null) mapAutoTransColumns_Client.Clear();
            if (mapACGBuffProto_Client != null) mapACGBuffProto_Client.Clear();
            if (mapACGBuff_Client != null) mapACGBuff_Client.Clear();
            if (mapACGFetters_Client != null) mapACGFetters_Client.Clear();
            if (mapACGBuffEffectGroup_Client != null) mapACGBuffEffectGroup_Client.Clear();
            if (mapTAC_Global_Client != null) mapTAC_Global_Client.Clear();
            if (mapTAC_RoomCfg_Client != null) mapTAC_RoomCfg_Client.Clear();
            if (mapACG_Stage_Priority_Client != null) mapACG_Stage_Priority_Client.Clear();
            if (mapTAC_Tag_Client != null) mapTAC_Tag_Client.Clear();
            if (mapTAC_Priority_Client != null) mapTAC_Priority_Client.Clear();
            if (mapTAC_Power_Client != null) mapTAC_Power_Client.Clear();
            if (mapTAC_CPower_Client != null) mapTAC_CPower_Client.Clear();
            if (mapTAC_AIEquip_Client != null) mapTAC_AIEquip_Client.Clear();
            if (mapTAC_AIID_Client != null) mapTAC_AIID_Client.Clear();
            if (mapTAC_AIRefresh_Client != null) mapTAC_AIRefresh_Client.Clear();
            if (mapTAC_AIAssignTag_Client != null) mapTAC_AIAssignTag_Client.Clear();
            if (mapTAC_AIDropEquip_Client != null) mapTAC_AIDropEquip_Client.Clear();
            if (mapTAC_AICardBuff_Client != null) mapTAC_AICardBuff_Client.Clear();
            if (mapTAC_EquipStrength_Client != null) mapTAC_EquipStrength_Client.Clear();
            if (mapTAC_HeroKu_Client != null) mapTAC_HeroKu_Client.Clear();
            if (mapChat_SimpleResponse_Client != null) mapChat_SimpleResponse_Client.Clear();
            if (mapACG_Hero_Client != null) mapACG_Hero_Client.Clear();
            if (mapACG_SpecName_Client != null) mapACG_SpecName_Client.Clear();
            if (mapACG_ClassName_Client != null) mapACG_ClassName_Client.Clear();
            if (mapACG_TagName_Client != null) mapACG_TagName_Client.Clear();
            if (mapACG_Summon_Client != null) mapACG_Summon_Client.Clear();
            if (mapACG_BattleMergeHero_Client != null) mapACG_BattleMergeHero_Client.Clear();
            if (mapTAC_SXScreen_Client != null) mapTAC_SXScreen_Client.Clear();
            if (mapACG_DamageRules_Hero_Client != null) mapACG_DamageRules_Hero_Client.Clear();
            if (mapACG_DamageRules_Round_Client != null) mapACG_DamageRules_Round_Client.Clear();
            if (mapACG_Item_Client != null) mapACG_Item_Client.Clear();
            if (mapACG_ChestKey_Client != null) mapACG_ChestKey_Client.Clear();
            if (mapACG_HeroVioce_Client != null) mapACG_HeroVioce_Client.Clear();
            if (mapACG_TeamLeaderVioce_Client != null) mapACG_TeamLeaderVioce_Client.Clear();
            if (mapACG_Mall_Client != null) mapACG_Mall_Client.Clear();
            if (mapACG_Recommend_Client != null) mapACG_Recommend_Client.Clear();
            if (mapACG_Egg_Client != null) mapACG_Egg_Client.Clear();
            if (mapACG_PageInfo_Client != null) mapACG_PageInfo_Client.Clear();
            if (mapACGHeroStory_Client != null) mapACGHeroStory_Client.Clear();
            if (mapACG_Mail_Client != null) mapACG_Mail_Client.Clear();
            if (mapACG_HeroBuyCustomized_Client != null) mapACG_HeroBuyCustomized_Client.Clear();
            if (mapACG_RoundSelectCustomized_Client != null) mapACG_RoundSelectCustomized_Client.Clear();
            if (mapACG_MatchOpponentCustomized_Client != null) mapACG_MatchOpponentCustomized_Client.Clear();
            if (mapACG_NewerAIHeroDropEquipment_Client != null) mapACG_NewerAIHeroDropEquipment_Client.Clear();
            if (mapACG_Final_Phase_Client != null) mapACG_Final_Phase_Client.Clear();
            if (mapACG_Guide_Mission_Client != null) mapACG_Guide_Mission_Client.Clear();
            if (mapACG_LP_Cacualte_Config_Client != null) mapACG_LP_Cacualte_Config_Client.Clear();
            if (mapACG_MMR_Match_AI_Client != null) mapACG_MMR_Match_AI_Client.Clear();
            if (mapACG_Notice_Client != null) mapACG_Notice_Client.Clear();
            if (mapACG_NoviceReward_Client != null) mapACG_NoviceReward_Client.Clear();
            if (mapACG_ShareDrop_Client != null) mapACG_ShareDrop_Client.Clear();
            if (mapACG_RookieMission_Client != null) mapACG_RookieMission_Client.Clear();
            if (mapACG_DeckRecommend_Client != null) mapACG_DeckRecommend_Client.Clear();
            if (mapACG_ElementTerrain_Client != null) mapACG_ElementTerrain_Client.Clear();
            if (mapACG_Examination_Info_Client != null) mapACG_Examination_Info_Client.Clear();
            if (mapACG_Examination_Questions_Client != null) mapACG_Examination_Questions_Client.Clear();
            if (mapACG_Quiz_Event_Info_Client != null) mapACG_Quiz_Event_Info_Client.Clear();
            if (mapACG_HandbookRule_Client != null) mapACG_HandbookRule_Client.Clear();
            if (mapACG_ProgramWord_Client != null) mapACG_ProgramWord_Client.Clear();
            if (mapACG_SpriteHelper_Client != null) mapACG_SpriteHelper_Client.Clear();
            if (mapACG_SpriteCondition_Client != null) mapACG_SpriteCondition_Client.Clear();
            if (mapACG_SpriteTips_Client != null) mapACG_SpriteTips_Client.Clear();
            if (mapACG_RefreshHeroCustomized_Client != null) mapACG_RefreshHeroCustomized_Client.Clear();
            if (mapACG_Set_Client != null) mapACG_Set_Client.Clear();
            if (mapACG_CollegeSet_Client != null) mapACG_CollegeSet_Client.Clear();
            if (mapACG_GuideSet_Client != null) mapACG_GuideSet_Client.Clear();
            if (mapACG_UserProfilePanel_Client != null) mapACG_UserProfilePanel_Client.Clear();
            if (mapACG_BattleHelper_Client != null) mapACG_BattleHelper_Client.Clear();
            if (mapACG_CounterTriger_Client != null) mapACG_CounterTriger_Client.Clear();
            if (mapACG_BattleTipCondition_Client != null) mapACG_BattleTipCondition_Client.Clear();
            if (mapACG_BattleTips_Client != null) mapACG_BattleTips_Client.Clear();
            if (mapACG_BattleTipsSwitch_Client != null) mapACG_BattleTipsSwitch_Client.Clear();
            if (mapACG_Jump_Client != null) mapACG_Jump_Client.Clear();
            if (mapACG_FightResultTag_Client != null) mapACG_FightResultTag_Client.Clear();
            if (mapACG_RoundCoin_Client != null) mapACG_RoundCoin_Client.Clear();
            if (mapACG_RoundExp_Client != null) mapACG_RoundExp_Client.Clear();
            if (mapACG_InitDrop_Client != null) mapACG_InitDrop_Client.Clear();
            if (mapACG_RankDrop_Client != null) mapACG_RankDrop_Client.Clear();
            if (mapACG_KillDrop_Client != null) mapACG_KillDrop_Client.Clear();
            if (mapACG_StarDrop_Client != null) mapACG_StarDrop_Client.Clear();
            if (mapACG_Skill_Mercenary_Client != null) mapACG_Skill_Mercenary_Client.Clear();
            if (mapACG_Mercenary_Level_Client != null) mapACG_Mercenary_Level_Client.Clear();
            if (mapACG_SKill_Mercenary_Show_Client != null) mapACG_SKill_Mercenary_Show_Client.Clear();
            if (mapACG_Galaxy_Client != null) mapACG_Galaxy_Client.Clear();
            if (mapACG_GalaxyPool_Client != null) mapACG_GalaxyPool_Client.Clear();
            if (mapTAC_AudioMgr_Client != null) mapTAC_AudioMgr_Client.Clear();
            if (mapPandoraErrorCodeInfo_Client != null) mapPandoraErrorCodeInfo_Client.Clear();
            if (mapACG_GuideLabel_Client != null) mapACG_GuideLabel_Client.Clear();
            if (mapACG_GuideTipsPool_Client != null) mapACG_GuideTipsPool_Client.Clear();
            if (mapACG_GuidePushTip_Client != null) mapACG_GuidePushTip_Client.Clear();
            if (mapACG_RankScores_Client != null) mapACG_RankScores_Client.Clear();
            if (mapACG_TextTipConfig_Client != null) mapACG_TextTipConfig_Client.Clear();
            if (mapTAC_AIWarmBattle_Client != null) mapTAC_AIWarmBattle_Client.Clear();
            if (mapACG_Newcomer_Config_Client != null) mapACG_Newcomer_Config_Client.Clear();
            if (mapACG_GuideCommonConfig_Client != null) mapACG_GuideCommonConfig_Client.Clear();
            if (mapACG_GuideEquipmentConfig_Client != null) mapACG_GuideEquipmentConfig_Client.Clear();
            if (mapACG_GuideHeroConfig_Client != null) mapACG_GuideHeroConfig_Client.Clear();
            if (mapACG_GuidePosConfig_Client != null) mapACG_GuidePosConfig_Client.Clear();
            if (mapTAC_AIDropEquipNew_Client != null) mapTAC_AIDropEquipNew_Client.Clear();
            if (mapACG_GuideFetterConfig_Client != null) mapACG_GuideFetterConfig_Client.Clear();
            if (mapEvents_Client != null) mapEvents_Client.Clear();
            if (mapEventIntroduction_Client != null) mapEventIntroduction_Client.Clear();
            if (mapACGHeavenBuff_Client != null) mapACGHeavenBuff_Client.Clear();
            if (mapACG_Buff_HeavenSelect_Level_Client != null) mapACG_Buff_HeavenSelect_Level_Client.Clear();
            if (mapACG_Buff_HeavenSelect_Client != null) mapACG_Buff_HeavenSelect_Client.Clear();
            if (mapACG_Buff_HeavenSelect_Show_Client != null) mapACG_Buff_HeavenSelect_Show_Client.Clear();
            if (mapK6UserTag_Client != null) mapK6UserTag_Client.Clear();
            if (mapACG_MissTrans_Client != null) mapACG_MissTrans_Client.Clear();
            if (mapChallengeMode_Client != null) mapChallengeMode_Client.Clear();
            if (mapACG_GuideEntryConfig_Client != null) mapACG_GuideEntryConfig_Client.Clear();
            if (mapTAC_BalanceRuleConfig_Client != null) mapTAC_BalanceRuleConfig_Client.Clear();
            if (mapACG_LobbyHeroValid_Client != null) mapACG_LobbyHeroValid_Client.Clear();
            if (mapACG_LobbyHeroPassiveChecking_Client != null) mapACG_LobbyHeroPassiveChecking_Client.Clear();
            if (mapACG_WwiseBankConfig_Client != null) mapACG_WwiseBankConfig_Client.Clear();
            if (mapTAC_StagePlus_Client != null) mapTAC_StagePlus_Client.Clear();
            if (mapTAC_Round_Client != null) mapTAC_Round_Client.Clear();
            if (mapACG_Camera_Client != null) mapACG_Camera_Client.Clear();
            if (mapACG_WarmRefreshCustomized_Client != null) mapACG_WarmRefreshCustomized_Client.Clear();
            if (mapACG_WarmRefreshPlan_Client != null) mapACG_WarmRefreshPlan_Client.Clear();
            if (mapUserTagConfig_Client != null) mapUserTagConfig_Client.Clear();
            if (mapACGDebuffGroup_Client != null) mapACGDebuffGroup_Client.Clear();
            if (mapModuleSwitch_Client != null) mapModuleSwitch_Client.Clear();
            if (mapACG_EquipmentStoreS5_Client != null) mapACG_EquipmentStoreS5_Client.Clear();
            if (mapTurbo_LP_Cacualte_Config_Client != null) mapTurbo_LP_Cacualte_Config_Client.Clear();
            if (mapACG_DraconicDrop_Client != null) mapACG_DraconicDrop_Client.Clear();
            if (mapTAC_ExtraRoomCfg_Client != null) mapTAC_ExtraRoomCfg_Client.Clear();
            if (mapACG_Channel_list_Client != null) mapACG_Channel_list_Client.Clear();
            if (mapChat_BubbleFrame_Client != null) mapChat_BubbleFrame_Client.Clear();
            if (mapClubGlobal_Client != null) mapClubGlobal_Client.Clear();
            if (mapACG_Destiny_Client != null) mapACG_Destiny_Client.Clear();
            if (mapTAC_AISitPos_Client != null) mapTAC_AISitPos_Client.Clear();
            if (mapACG_ExtraStealEquipRate_Client != null) mapACG_ExtraStealEquipRate_Client.Clear();
            if (mapACG_HolyDrop_Client != null) mapACG_HolyDrop_Client.Clear();
            if (mapACG_HolyDropPool_Client != null) mapACG_HolyDropPool_Client.Clear();
            if (mapACG_DestinyBanner_Client != null) mapACG_DestinyBanner_Client.Clear();
            if (mapACG_TechnologyJinNeng_Backgroun_Client != null) mapACG_TechnologyJinNeng_Backgroun_Client.Clear();
            if (mapACG_SeasonInfo_Client != null) mapACG_SeasonInfo_Client.Clear();
            if (mapACG_RecommendClientCfg_Client != null) mapACG_RecommendClientCfg_Client.Clear();
            if (mapClubMall_Client != null) mapClubMall_Client.Clear();
            if (stTAllGameCfgClientExt1 != null) stTAllGameCfgClientExt1.Clear();
            if (mapGameModuleGuideIntro_Client != null) mapGameModuleGuideIntro_Client.Clear();
            if (mapACG_CombatEffect_Client != null) mapACG_CombatEffect_Client.Clear();
            if (mapACG_HeroSkin_Client != null) mapACG_HeroSkin_Client.Clear();
            if (mapChat_SimpleResponseVoice_Client != null) mapChat_SimpleResponseVoice_Client.Clear();
            if (mapManualTransTable_Client != null) mapManualTransTable_Client.Clear();
            if (mapACG_ItemGetType_Client != null) mapACG_ItemGetType_Client.Clear();
            if (stTAllGameCfgClientExt2 != null) stTAllGameCfgClientExt2.Clear();
            if (stTAllGameCfgClientExt3 != null) stTAllGameCfgClientExt3.Clear();
            if (stTAllGameCfgClientExt4 != null) stTAllGameCfgClientExt4.Clear();
            if (stTAllGameCfgClientExt5 != null) stTAllGameCfgClientExt5.Clear();
            if (stTAllGameCfgClientExt6 != null) stTAllGameCfgClientExt6.Clear();
            if (mapBattleEndDisplayIconCfg_Client != null) mapBattleEndDisplayIconCfg_Client.Clear();
            if (mapACG_MallBuyTips_Client != null) mapACG_MallBuyTips_Client.Clear();
            if (mapACG_PrimeData_Client != null) mapACG_PrimeData_Client.Clear();
            if (mapACG_LuckShare_Client != null) mapACG_LuckShare_Client.Clear();
            if (mapJOCRank_LP_Cacualte_Config_Client != null) mapJOCRank_LP_Cacualte_Config_Client.Clear();
            if (mapTAC_FetterMusic_Client != null) mapTAC_FetterMusic_Client.Clear();
            if (mapACGRecordBuffTrigger_Client != null) mapACGRecordBuffTrigger_Client.Clear();
            if (mapTAC_FetterMusicPlan_Client != null) mapTAC_FetterMusicPlan_Client.Clear();
            if (stTAllGameCfgClientExt7 != null) stTAllGameCfgClientExt7.Clear();
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAllGameCfgClient();
            copied.mapACG_MatchingLoadTips_Client = (UniqueInfo.ConfigHashMap<int, TACG_MatchingLoadTips_Client>)JceUtil.DeepClone(this.mapACG_MatchingLoadTips_Client);
            copied.mapACG_Activity_Client = (UniqueInfo.ConfigHashMap<int, TACG_Activity_Client>)JceUtil.DeepClone(this.mapACG_Activity_Client);
            copied.mapACG_Activity_Tag_Client = (UniqueInfo.ConfigHashMap<int, TACG_Activity_Tag_Client>)JceUtil.DeepClone(this.mapACG_Activity_Tag_Client);
            copied.mapACG_Activity_Page_Client = (UniqueInfo.ConfigHashMap<int, TACG_Activity_Page_Client>)JceUtil.DeepClone(this.mapACG_Activity_Page_Client);
            copied.mapGPU_Client = (UniqueInfo.ConfigHashMap<int, TGPU_Client>)JceUtil.DeepClone(this.mapGPU_Client);
            copied.mapGlobal_Client = (UniqueInfo.ConfigHashMap<int, TGlobal_Client>)JceUtil.DeepClone(this.mapGlobal_Client);
            copied.mapTAC_ReportFeature_Client = (UniqueInfo.ConfigHashMap<int, TTAC_ReportFeature_Client>)JceUtil.DeepClone(this.mapTAC_ReportFeature_Client);
            copied.mapBPConfig_Client = (UniqueInfo.ConfigHashMap<int, TBPConfig_Client>)JceUtil.DeepClone(this.mapBPConfig_Client);
            copied.mapBPPay_Client = (UniqueInfo.ConfigHashMap<int, TBPPay_Client>)JceUtil.DeepClone(this.mapBPPay_Client);
            copied.mapDailyTask_Client = (UniqueInfo.ConfigHashMap<int, TDailyTask_Client>)JceUtil.DeepClone(this.mapDailyTask_Client);
            copied.mapDailyTaskGroup_Client = (UniqueInfo.ConfigHashMap<int, TDailyTaskGroup_Client>)JceUtil.DeepClone(this.mapDailyTaskGroup_Client);
            copied.mapEnlighten_Client = (UniqueInfo.ConfigHashMap<int, TEnlighten_Client>)JceUtil.DeepClone(this.mapEnlighten_Client);
            copied.mapEnlightenRate_Client = (UniqueInfo.ConfigHashMap<int, TEnlightenRate_Client>)JceUtil.DeepClone(this.mapEnlightenRate_Client);
            copied.mapBPMall_Client = (UniqueInfo.ConfigHashMap<int, TBPMall_Client>)JceUtil.DeepClone(this.mapBPMall_Client);
            copied.mapACG_DropBoxItem_Client = (UniqueInfo.ConfigHashMap<int, TACG_DropBoxItem_Client>)JceUtil.DeepClone(this.mapACG_DropBoxItem_Client);
            copied.mapACGSkill_Client = (UniqueInfo.ConfigHashMap<int, TACGSkill_Client>)JceUtil.DeepClone(this.mapACGSkill_Client);
            copied.mapACGSkillDurationTarget_Client = (UniqueInfo.ConfigHashMap<int, TACGSkillDurationTarget_Client>)JceUtil.DeepClone(this.mapACGSkillDurationTarget_Client);
            copied.mapACG_Equipment_Client = (UniqueInfo.ConfigHashMap<int, TACG_Equipment_Client>)JceUtil.DeepClone(this.mapACG_Equipment_Client);
            copied.mapACG_EquipmentRandom_Client = (UniqueInfo.ConfigHashMap<int, TACG_EquipmentRandom_Client>)JceUtil.DeepClone(this.mapACG_EquipmentRandom_Client);
            copied.mapACG_Attribute_Client = (UniqueInfo.ConfigHashMap<int, TACG_Attribute_Client>)JceUtil.DeepClone(this.mapACG_Attribute_Client);
            copied.mapACG_StealLevelRate_Client = (UniqueInfo.ConfigHashMap<int, TACG_StealLevelRate_Client>)JceUtil.DeepClone(this.mapACG_StealLevelRate_Client);
            copied.mapACG_StealEquipRate_Client = (UniqueInfo.ConfigHashMap<int, TACG_StealEquipRate_Client>)JceUtil.DeepClone(this.mapACG_StealEquipRate_Client);
            copied.mapACG_EquipmentStore_Client = (UniqueInfo.ConfigHashMap<int, TACG_EquipmentStore_Client>)JceUtil.DeepClone(this.mapACG_EquipmentStore_Client);
            copied.mapTAC_Module_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Module_Client>)JceUtil.DeepClone(this.mapTAC_Module_Client);
            copied.mapACG_RandName_Client = (UniqueInfo.ConfigHashMap<int, TACG_RandName_Client>)JceUtil.DeepClone(this.mapACG_RandName_Client);
            copied.mapTAC_Level_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Level_Client>)JceUtil.DeepClone(this.mapTAC_Level_Client);
            copied.mapTAC_NewLevel_Client = (UniqueInfo.ConfigHashMap<int, TTAC_NewLevel_Client>)JceUtil.DeepClone(this.mapTAC_NewLevel_Client);
            copied.mapACG_HeroRate_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeroRate_Client>)JceUtil.DeepClone(this.mapACG_HeroRate_Client);
            copied.mapACG_EquipRate_Client = (UniqueInfo.ConfigHashMap<int, TACG_EquipRate_Client>)JceUtil.DeepClone(this.mapACG_EquipRate_Client);
            copied.mapACG_HeroEquipMatchRate_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeroEquipMatchRate_Client>)JceUtil.DeepClone(this.mapACG_HeroEquipMatchRate_Client);
            copied.mapACG_SharedDraftCfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_SharedDraftCfg_Client>)JceUtil.DeepClone(this.mapACG_SharedDraftCfg_Client);
            copied.mapACG_BREquipRate_Client = (UniqueInfo.ConfigHashMap<int, TACG_BREquipRate_Client>)JceUtil.DeepClone(this.mapACG_BREquipRate_Client);
            copied.mapACG_BRHeroRate_Client = (UniqueInfo.ConfigHashMap<int, TACG_BRHeroRate_Client>)JceUtil.DeepClone(this.mapACG_BRHeroRate_Client);
            copied.mapACG_AdvancedEquipRate_Client = (UniqueInfo.ConfigHashMap<int, TACG_AdvancedEquipRate_Client>)JceUtil.DeepClone(this.mapACG_AdvancedEquipRate_Client);
            copied.mapACG_MonsterDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_MonsterDrop_Client>)JceUtil.DeepClone(this.mapACG_MonsterDrop_Client);
            copied.mapACG_DropPoolItem_Client = (UniqueInfo.ConfigHashMap<int, TACG_DropPoolItem_Client>)JceUtil.DeepClone(this.mapACG_DropPoolItem_Client);
            copied.mapACG_DropBuff_Client = (UniqueInfo.ConfigHashMap<int, TACG_DropBuff_Client>)JceUtil.DeepClone(this.mapACG_DropBuff_Client);
            copied.mapACG_BoxType_Client = (UniqueInfo.ConfigHashMap<int, TACG_BoxType_Client>)JceUtil.DeepClone(this.mapACG_BoxType_Client);
            copied.mapACG_BoxSkin_Client = (UniqueInfo.ConfigHashMap<int, TACG_BoxSkin_Client>)JceUtil.DeepClone(this.mapACG_BoxSkin_Client);
            copied.mapACG_WarmDropBuff_Client = (UniqueInfo.ConfigHashMap<int, TACG_WarmDropBuff_Client>)JceUtil.DeepClone(this.mapACG_WarmDropBuff_Client);
            copied.mapACG_AdvancedDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_AdvancedDrop_Client>)JceUtil.DeepClone(this.mapACG_AdvancedDrop_Client);
            copied.mapACG_MatchingControl_Client = (UniqueInfo.ConfigHashMap<int, TACG_MatchingControl_Client>)JceUtil.DeepClone(this.mapACG_MatchingControl_Client);
            copied.mapTAC_ErrorCode_Client = (UniqueInfo.ConfigHashMap<int, TTAC_ErrorCode_Client>)JceUtil.DeepClone(this.mapTAC_ErrorCode_Client);
            copied.mapTAC_LoadingTips_Client = (UniqueInfo.ConfigHashMap<int, TTAC_LoadingTips_Client>)JceUtil.DeepClone(this.mapTAC_LoadingTips_Client);
            copied.mapTAC_Quest_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Quest_Client>)JceUtil.DeepClone(this.mapTAC_Quest_Client);
            copied.mapTAC_Quest_NPC_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Quest_NPC_Client>)JceUtil.DeepClone(this.mapTAC_Quest_NPC_Client);
            copied.mapAutoTransColumns_Client = (UniqueInfo.ConfigHashMap<int, TAutoTransColumns_Client>)JceUtil.DeepClone(this.mapAutoTransColumns_Client);
            copied.mapACGBuffProto_Client = (UniqueInfo.ConfigHashMap<int, TACGBuffProto_Client>)JceUtil.DeepClone(this.mapACGBuffProto_Client);
            copied.mapACGBuff_Client = (UniqueInfo.ConfigHashMap<int, TACGBuff_Client>)JceUtil.DeepClone(this.mapACGBuff_Client);
            copied.mapACGFetters_Client = (UniqueInfo.ConfigHashMap<int, TACGFetters_Client>)JceUtil.DeepClone(this.mapACGFetters_Client);
            copied.mapACGBuffEffectGroup_Client = (UniqueInfo.ConfigHashMap<int, TACGBuffEffectGroup_Client>)JceUtil.DeepClone(this.mapACGBuffEffectGroup_Client);
            copied.mapTAC_Global_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Global_Client>)JceUtil.DeepClone(this.mapTAC_Global_Client);
            copied.mapTAC_RoomCfg_Client = (UniqueInfo.ConfigHashMap<int, TTAC_RoomCfg_Client>)JceUtil.DeepClone(this.mapTAC_RoomCfg_Client);
            copied.mapACG_Stage_Priority_Client = (UniqueInfo.ConfigHashMap<int, TACG_Stage_Priority_Client>)JceUtil.DeepClone(this.mapACG_Stage_Priority_Client);
            copied.mapTAC_Tag_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Tag_Client>)JceUtil.DeepClone(this.mapTAC_Tag_Client);
            copied.mapTAC_Priority_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Priority_Client>)JceUtil.DeepClone(this.mapTAC_Priority_Client);
            copied.mapTAC_Power_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Power_Client>)JceUtil.DeepClone(this.mapTAC_Power_Client);
            copied.mapTAC_CPower_Client = (UniqueInfo.ConfigHashMap<int, TTAC_CPower_Client>)JceUtil.DeepClone(this.mapTAC_CPower_Client);
            copied.mapTAC_AIEquip_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIEquip_Client>)JceUtil.DeepClone(this.mapTAC_AIEquip_Client);
            copied.mapTAC_AIID_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIID_Client>)JceUtil.DeepClone(this.mapTAC_AIID_Client);
            copied.mapTAC_AIRefresh_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIRefresh_Client>)JceUtil.DeepClone(this.mapTAC_AIRefresh_Client);
            copied.mapTAC_AIAssignTag_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIAssignTag_Client>)JceUtil.DeepClone(this.mapTAC_AIAssignTag_Client);
            copied.mapTAC_AIDropEquip_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIDropEquip_Client>)JceUtil.DeepClone(this.mapTAC_AIDropEquip_Client);
            copied.mapTAC_AICardBuff_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AICardBuff_Client>)JceUtil.DeepClone(this.mapTAC_AICardBuff_Client);
            copied.mapTAC_EquipStrength_Client = (UniqueInfo.ConfigHashMap<int, TTAC_EquipStrength_Client>)JceUtil.DeepClone(this.mapTAC_EquipStrength_Client);
            copied.mapTAC_HeroKu_Client = (UniqueInfo.ConfigHashMap<int, TTAC_HeroKu_Client>)JceUtil.DeepClone(this.mapTAC_HeroKu_Client);
            copied.mapChat_SimpleResponse_Client = (UniqueInfo.ConfigHashMap<int, TChat_SimpleResponse_Client>)JceUtil.DeepClone(this.mapChat_SimpleResponse_Client);
            copied.mapACG_Hero_Client = (UniqueInfo.ConfigHashMap<int, TACG_Hero_Client>)JceUtil.DeepClone(this.mapACG_Hero_Client);
            copied.mapACG_SpecName_Client = (UniqueInfo.ConfigHashMap<int, TACG_SpecName_Client>)JceUtil.DeepClone(this.mapACG_SpecName_Client);
            copied.mapACG_ClassName_Client = (UniqueInfo.ConfigHashMap<int, TACG_ClassName_Client>)JceUtil.DeepClone(this.mapACG_ClassName_Client);
            copied.mapACG_TagName_Client = (UniqueInfo.ConfigHashMap<int, TACG_TagName_Client>)JceUtil.DeepClone(this.mapACG_TagName_Client);
            copied.mapACG_Summon_Client = (UniqueInfo.ConfigHashMap<int, TACG_Summon_Client>)JceUtil.DeepClone(this.mapACG_Summon_Client);
            copied.mapACG_BattleMergeHero_Client = (UniqueInfo.ConfigHashMap<int, TACG_BattleMergeHero_Client>)JceUtil.DeepClone(this.mapACG_BattleMergeHero_Client);
            copied.mapTAC_SXScreen_Client = (UniqueInfo.ConfigHashMap<int, TTAC_SXScreen_Client>)JceUtil.DeepClone(this.mapTAC_SXScreen_Client);
            copied.mapACG_DamageRules_Hero_Client = (UniqueInfo.ConfigHashMap<int, TACG_DamageRules_Hero_Client>)JceUtil.DeepClone(this.mapACG_DamageRules_Hero_Client);
            copied.mapACG_DamageRules_Round_Client = (UniqueInfo.ConfigHashMap<int, TACG_DamageRules_Round_Client>)JceUtil.DeepClone(this.mapACG_DamageRules_Round_Client);
            copied.mapACG_Item_Client = (UniqueInfo.ConfigHashMap<int, TACG_Item_Client>)JceUtil.DeepClone(this.mapACG_Item_Client);
            copied.mapACG_ChestKey_Client = (UniqueInfo.ConfigHashMap<int, TACG_ChestKey_Client>)JceUtil.DeepClone(this.mapACG_ChestKey_Client);
            copied.mapACG_HeroVioce_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeroVioce_Client>)JceUtil.DeepClone(this.mapACG_HeroVioce_Client);
            copied.mapACG_TeamLeaderVioce_Client = (UniqueInfo.ConfigHashMap<int, TACG_TeamLeaderVioce_Client>)JceUtil.DeepClone(this.mapACG_TeamLeaderVioce_Client);
            copied.mapACG_Mall_Client = (UniqueInfo.ConfigHashMap<int, TACG_Mall_Client>)JceUtil.DeepClone(this.mapACG_Mall_Client);
            copied.mapACG_Recommend_Client = (UniqueInfo.ConfigHashMap<int, TACG_Recommend_Client>)JceUtil.DeepClone(this.mapACG_Recommend_Client);
            copied.mapACG_Egg_Client = (UniqueInfo.ConfigHashMap<int, TACG_Egg_Client>)JceUtil.DeepClone(this.mapACG_Egg_Client);
            copied.mapACG_PageInfo_Client = (UniqueInfo.ConfigHashMap<int, TACG_PageInfo_Client>)JceUtil.DeepClone(this.mapACG_PageInfo_Client);
            copied.mapACGHeroStory_Client = (UniqueInfo.ConfigHashMap<int, TACGHeroStory_Client>)JceUtil.DeepClone(this.mapACGHeroStory_Client);
            copied.mapACG_Mail_Client = (UniqueInfo.ConfigHashMap<int, TACG_Mail_Client>)JceUtil.DeepClone(this.mapACG_Mail_Client);
            copied.mapACG_HeroBuyCustomized_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeroBuyCustomized_Client>)JceUtil.DeepClone(this.mapACG_HeroBuyCustomized_Client);
            copied.mapACG_RoundSelectCustomized_Client = (UniqueInfo.ConfigHashMap<int, TACG_RoundSelectCustomized_Client>)JceUtil.DeepClone(this.mapACG_RoundSelectCustomized_Client);
            copied.mapACG_MatchOpponentCustomized_Client = (UniqueInfo.ConfigHashMap<int, TACG_MatchOpponentCustomized_Client>)JceUtil.DeepClone(this.mapACG_MatchOpponentCustomized_Client);
            copied.mapACG_NewerAIHeroDropEquipment_Client = (UniqueInfo.ConfigHashMap<int, TACG_NewerAIHeroDropEquipment_Client>)JceUtil.DeepClone(this.mapACG_NewerAIHeroDropEquipment_Client);
            copied.mapACG_Final_Phase_Client = (UniqueInfo.ConfigHashMap<int, TACG_Final_Phase_Client>)JceUtil.DeepClone(this.mapACG_Final_Phase_Client);
            copied.mapACG_Guide_Mission_Client = (UniqueInfo.ConfigHashMap<int, TACG_Guide_Mission_Client>)JceUtil.DeepClone(this.mapACG_Guide_Mission_Client);
            copied.mapACG_LP_Cacualte_Config_Client = (UniqueInfo.ConfigHashMap<int, TACG_LP_Cacualte_Config_Client>)JceUtil.DeepClone(this.mapACG_LP_Cacualte_Config_Client);
            copied.mapACG_MMR_Match_AI_Client = (UniqueInfo.ConfigHashMap<int, TACG_MMR_Match_AI_Client>)JceUtil.DeepClone(this.mapACG_MMR_Match_AI_Client);
            copied.mapACG_Notice_Client = (UniqueInfo.ConfigHashMap<int, TACG_Notice_Client>)JceUtil.DeepClone(this.mapACG_Notice_Client);
            copied.mapACG_NoviceReward_Client = (UniqueInfo.ConfigHashMap<int, TACG_NoviceReward_Client>)JceUtil.DeepClone(this.mapACG_NoviceReward_Client);
            copied.mapACG_ShareDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_ShareDrop_Client>)JceUtil.DeepClone(this.mapACG_ShareDrop_Client);
            copied.mapACG_RookieMission_Client = (UniqueInfo.ConfigHashMap<int, TACG_RookieMission_Client>)JceUtil.DeepClone(this.mapACG_RookieMission_Client);
            copied.mapACG_DeckRecommend_Client = (UniqueInfo.ConfigHashMap<int, TACG_DeckRecommend_Client>)JceUtil.DeepClone(this.mapACG_DeckRecommend_Client);
            copied.mapACG_ElementTerrain_Client = (UniqueInfo.ConfigHashMap<int, TACG_ElementTerrain_Client>)JceUtil.DeepClone(this.mapACG_ElementTerrain_Client);
            copied.mapACG_Examination_Info_Client = (UniqueInfo.ConfigHashMap<int, TACG_Examination_Info_Client>)JceUtil.DeepClone(this.mapACG_Examination_Info_Client);
            copied.mapACG_Examination_Questions_Client = (UniqueInfo.ConfigHashMap<int, TACG_Examination_Questions_Client>)JceUtil.DeepClone(this.mapACG_Examination_Questions_Client);
            copied.mapACG_Quiz_Event_Info_Client = (UniqueInfo.ConfigHashMap<int, TACG_Quiz_Event_Info_Client>)JceUtil.DeepClone(this.mapACG_Quiz_Event_Info_Client);
            copied.mapACG_HandbookRule_Client = (UniqueInfo.ConfigHashMap<int, TACG_HandbookRule_Client>)JceUtil.DeepClone(this.mapACG_HandbookRule_Client);
            copied.mapACG_ProgramWord_Client = (UniqueInfo.ConfigHashMap<int, TACG_ProgramWord_Client>)JceUtil.DeepClone(this.mapACG_ProgramWord_Client);
            copied.mapACG_SpriteHelper_Client = (UniqueInfo.ConfigHashMap<int, TACG_SpriteHelper_Client>)JceUtil.DeepClone(this.mapACG_SpriteHelper_Client);
            copied.mapACG_SpriteCondition_Client = (UniqueInfo.ConfigHashMap<int, TACG_SpriteCondition_Client>)JceUtil.DeepClone(this.mapACG_SpriteCondition_Client);
            copied.mapACG_SpriteTips_Client = (UniqueInfo.ConfigHashMap<int, TACG_SpriteTips_Client>)JceUtil.DeepClone(this.mapACG_SpriteTips_Client);
            copied.mapACG_RefreshHeroCustomized_Client = (UniqueInfo.ConfigHashMap<int, TACG_RefreshHeroCustomized_Client>)JceUtil.DeepClone(this.mapACG_RefreshHeroCustomized_Client);
            copied.mapACG_Set_Client = (UniqueInfo.ConfigHashMap<int, TACG_Set_Client>)JceUtil.DeepClone(this.mapACG_Set_Client);
            copied.mapACG_CollegeSet_Client = (UniqueInfo.ConfigHashMap<int, TACG_CollegeSet_Client>)JceUtil.DeepClone(this.mapACG_CollegeSet_Client);
            copied.mapACG_GuideSet_Client = (UniqueInfo.ConfigHashMap<int, TACG_GuideSet_Client>)JceUtil.DeepClone(this.mapACG_GuideSet_Client);
            copied.mapACG_UserProfilePanel_Client = (UniqueInfo.ConfigHashMap<int, TACG_UserProfilePanel_Client>)JceUtil.DeepClone(this.mapACG_UserProfilePanel_Client);
            copied.mapACG_BattleHelper_Client = (UniqueInfo.ConfigHashMap<int, TACG_BattleHelper_Client>)JceUtil.DeepClone(this.mapACG_BattleHelper_Client);
            copied.mapACG_CounterTriger_Client = (UniqueInfo.ConfigHashMap<int, TACG_CounterTriger_Client>)JceUtil.DeepClone(this.mapACG_CounterTriger_Client);
            copied.mapACG_BattleTipCondition_Client = (UniqueInfo.ConfigHashMap<int, TACG_BattleTipCondition_Client>)JceUtil.DeepClone(this.mapACG_BattleTipCondition_Client);
            copied.mapACG_BattleTips_Client = (UniqueInfo.ConfigHashMap<int, TACG_BattleTips_Client>)JceUtil.DeepClone(this.mapACG_BattleTips_Client);
            copied.mapACG_BattleTipsSwitch_Client = (UniqueInfo.ConfigHashMap<int, TACG_BattleTipsSwitch_Client>)JceUtil.DeepClone(this.mapACG_BattleTipsSwitch_Client);
            copied.mapACG_Jump_Client = (UniqueInfo.ConfigHashMap<int, TACG_Jump_Client>)JceUtil.DeepClone(this.mapACG_Jump_Client);
            copied.mapACG_FightResultTag_Client = (UniqueInfo.ConfigHashMap<int, TACG_FightResultTag_Client>)JceUtil.DeepClone(this.mapACG_FightResultTag_Client);
            copied.mapACG_RoundCoin_Client = (UniqueInfo.ConfigHashMap<int, TACG_RoundCoin_Client>)JceUtil.DeepClone(this.mapACG_RoundCoin_Client);
            copied.mapACG_RoundExp_Client = (UniqueInfo.ConfigHashMap<int, TACG_RoundExp_Client>)JceUtil.DeepClone(this.mapACG_RoundExp_Client);
            copied.mapACG_InitDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_InitDrop_Client>)JceUtil.DeepClone(this.mapACG_InitDrop_Client);
            copied.mapACG_RankDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_RankDrop_Client>)JceUtil.DeepClone(this.mapACG_RankDrop_Client);
            copied.mapACG_KillDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_KillDrop_Client>)JceUtil.DeepClone(this.mapACG_KillDrop_Client);
            copied.mapACG_StarDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_StarDrop_Client>)JceUtil.DeepClone(this.mapACG_StarDrop_Client);
            copied.mapACG_Skill_Mercenary_Client = (UniqueInfo.ConfigHashMap<int, TACG_Skill_Mercenary_Client>)JceUtil.DeepClone(this.mapACG_Skill_Mercenary_Client);
            copied.mapACG_Mercenary_Level_Client = (UniqueInfo.ConfigHashMap<int, TACG_Mercenary_Level_Client>)JceUtil.DeepClone(this.mapACG_Mercenary_Level_Client);
            copied.mapACG_SKill_Mercenary_Show_Client = (UniqueInfo.ConfigHashMap<int, TACG_SKill_Mercenary_Show_Client>)JceUtil.DeepClone(this.mapACG_SKill_Mercenary_Show_Client);
            copied.mapACG_Galaxy_Client = (UniqueInfo.ConfigHashMap<int, TACG_Galaxy_Client>)JceUtil.DeepClone(this.mapACG_Galaxy_Client);
            copied.mapACG_GalaxyPool_Client = (UniqueInfo.ConfigHashMap<int, TACG_GalaxyPool_Client>)JceUtil.DeepClone(this.mapACG_GalaxyPool_Client);
            copied.mapTAC_AudioMgr_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AudioMgr_Client>)JceUtil.DeepClone(this.mapTAC_AudioMgr_Client);
            copied.mapPandoraErrorCodeInfo_Client = (UniqueInfo.ConfigHashMap<int, TPandoraErrorCodeInfo_Client>)JceUtil.DeepClone(this.mapPandoraErrorCodeInfo_Client);
            copied.mapACG_GuideLabel_Client = (UniqueInfo.ConfigHashMap<int, TACG_GuideLabel_Client>)JceUtil.DeepClone(this.mapACG_GuideLabel_Client);
            copied.mapACG_GuideTipsPool_Client = (UniqueInfo.ConfigHashMap<int, TACG_GuideTipsPool_Client>)JceUtil.DeepClone(this.mapACG_GuideTipsPool_Client);
            copied.mapACG_GuidePushTip_Client = (UniqueInfo.ConfigHashMap<int, TACG_GuidePushTip_Client>)JceUtil.DeepClone(this.mapACG_GuidePushTip_Client);
            copied.mapACG_RankScores_Client = (UniqueInfo.ConfigHashMap<int, TACG_RankScores_Client>)JceUtil.DeepClone(this.mapACG_RankScores_Client);
            copied.mapACG_TextTipConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_TextTipConfig_Client>)JceUtil.DeepClone(this.mapACG_TextTipConfig_Client);
            copied.mapTAC_AIWarmBattle_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIWarmBattle_Client>)JceUtil.DeepClone(this.mapTAC_AIWarmBattle_Client);
            copied.mapACG_Newcomer_Config_Client = (UniqueInfo.ConfigHashMap<int, TACG_Newcomer_Config_Client>)JceUtil.DeepClone(this.mapACG_Newcomer_Config_Client);
            copied.mapACG_GuideCommonConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_GuideCommonConfig_Client>)JceUtil.DeepClone(this.mapACG_GuideCommonConfig_Client);
            copied.mapACG_GuideEquipmentConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_GuideEquipmentConfig_Client>)JceUtil.DeepClone(this.mapACG_GuideEquipmentConfig_Client);
            copied.mapACG_GuideHeroConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_GuideHeroConfig_Client>)JceUtil.DeepClone(this.mapACG_GuideHeroConfig_Client);
            copied.mapACG_GuidePosConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_GuidePosConfig_Client>)JceUtil.DeepClone(this.mapACG_GuidePosConfig_Client);
            copied.mapTAC_AIDropEquipNew_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AIDropEquipNew_Client>)JceUtil.DeepClone(this.mapTAC_AIDropEquipNew_Client);
            copied.mapACG_GuideFetterConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_GuideFetterConfig_Client>)JceUtil.DeepClone(this.mapACG_GuideFetterConfig_Client);
            copied.mapEvents_Client = (UniqueInfo.ConfigHashMap<int, TEvents_Client>)JceUtil.DeepClone(this.mapEvents_Client);
            copied.mapEventIntroduction_Client = (UniqueInfo.ConfigHashMap<int, TEventIntroduction_Client>)JceUtil.DeepClone(this.mapEventIntroduction_Client);
            copied.mapACGHeavenBuff_Client = (UniqueInfo.ConfigHashMap<int, TACGHeavenBuff_Client>)JceUtil.DeepClone(this.mapACGHeavenBuff_Client);
            copied.mapACG_Buff_HeavenSelect_Level_Client = (UniqueInfo.ConfigHashMap<int, TACG_Buff_HeavenSelect_Level_Client>)JceUtil.DeepClone(this.mapACG_Buff_HeavenSelect_Level_Client);
            copied.mapACG_Buff_HeavenSelect_Client = (UniqueInfo.ConfigHashMap<int, TACG_Buff_HeavenSelect_Client>)JceUtil.DeepClone(this.mapACG_Buff_HeavenSelect_Client);
            copied.mapACG_Buff_HeavenSelect_Show_Client = (UniqueInfo.ConfigHashMap<int, TACG_Buff_HeavenSelect_Show_Client>)JceUtil.DeepClone(this.mapACG_Buff_HeavenSelect_Show_Client);
            copied.mapK6UserTag_Client = (UniqueInfo.ConfigHashMap<int, TK6UserTag_Client>)JceUtil.DeepClone(this.mapK6UserTag_Client);
            copied.mapACG_MissTrans_Client = (UniqueInfo.ConfigHashMap<int, TACG_MissTrans_Client>)JceUtil.DeepClone(this.mapACG_MissTrans_Client);
            copied.mapChallengeMode_Client = (UniqueInfo.ConfigHashMap<int, TChallengeMode_Client>)JceUtil.DeepClone(this.mapChallengeMode_Client);
            copied.mapACG_GuideEntryConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_GuideEntryConfig_Client>)JceUtil.DeepClone(this.mapACG_GuideEntryConfig_Client);
            copied.mapTAC_BalanceRuleConfig_Client = (UniqueInfo.ConfigHashMap<int, TTAC_BalanceRuleConfig_Client>)JceUtil.DeepClone(this.mapTAC_BalanceRuleConfig_Client);
            copied.mapACG_LobbyHeroValid_Client = (UniqueInfo.ConfigHashMap<int, TACG_LobbyHeroValid_Client>)JceUtil.DeepClone(this.mapACG_LobbyHeroValid_Client);
            copied.mapACG_LobbyHeroPassiveChecking_Client = (UniqueInfo.ConfigHashMap<int, TACG_LobbyHeroPassiveChecking_Client>)JceUtil.DeepClone(this.mapACG_LobbyHeroPassiveChecking_Client);
            copied.mapACG_WwiseBankConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_WwiseBankConfig_Client>)JceUtil.DeepClone(this.mapACG_WwiseBankConfig_Client);
            copied.mapTAC_StagePlus_Client = (UniqueInfo.ConfigHashMap<int, TTAC_StagePlus_Client>)JceUtil.DeepClone(this.mapTAC_StagePlus_Client);
            copied.mapTAC_Round_Client = (UniqueInfo.ConfigHashMap<int, TTAC_Round_Client>)JceUtil.DeepClone(this.mapTAC_Round_Client);
            copied.mapACG_Camera_Client = (UniqueInfo.ConfigHashMap<int, TACG_Camera_Client>)JceUtil.DeepClone(this.mapACG_Camera_Client);
            copied.mapACG_WarmRefreshCustomized_Client = (UniqueInfo.ConfigHashMap<int, TACG_WarmRefreshCustomized_Client>)JceUtil.DeepClone(this.mapACG_WarmRefreshCustomized_Client);
            copied.mapACG_WarmRefreshPlan_Client = (UniqueInfo.ConfigHashMap<int, TACG_WarmRefreshPlan_Client>)JceUtil.DeepClone(this.mapACG_WarmRefreshPlan_Client);
            copied.mapUserTagConfig_Client = (UniqueInfo.ConfigHashMap<int, TUserTagConfig_Client>)JceUtil.DeepClone(this.mapUserTagConfig_Client);
            copied.mapACGDebuffGroup_Client = (UniqueInfo.ConfigHashMap<int, TACGDebuffGroup_Client>)JceUtil.DeepClone(this.mapACGDebuffGroup_Client);
            copied.mapModuleSwitch_Client = (UniqueInfo.ConfigHashMap<int, TModuleSwitch_Client>)JceUtil.DeepClone(this.mapModuleSwitch_Client);
            copied.mapACG_EquipmentStoreS5_Client = (UniqueInfo.ConfigHashMap<int, TACG_EquipmentStoreS5_Client>)JceUtil.DeepClone(this.mapACG_EquipmentStoreS5_Client);
            copied.mapTurbo_LP_Cacualte_Config_Client = (UniqueInfo.ConfigHashMap<int, TTurbo_LP_Cacualte_Config_Client>)JceUtil.DeepClone(this.mapTurbo_LP_Cacualte_Config_Client);
            copied.mapACG_DraconicDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_DraconicDrop_Client>)JceUtil.DeepClone(this.mapACG_DraconicDrop_Client);
            copied.mapTAC_ExtraRoomCfg_Client = (UniqueInfo.ConfigHashMap<int, TTAC_ExtraRoomCfg_Client>)JceUtil.DeepClone(this.mapTAC_ExtraRoomCfg_Client);
            copied.mapACG_Channel_list_Client = (UniqueInfo.ConfigHashMap<int, TACG_Channel_list_Client>)JceUtil.DeepClone(this.mapACG_Channel_list_Client);
            copied.mapChat_BubbleFrame_Client = (UniqueInfo.ConfigHashMap<int, TChat_BubbleFrame_Client>)JceUtil.DeepClone(this.mapChat_BubbleFrame_Client);
            copied.mapClubGlobal_Client = (UniqueInfo.ConfigHashMap<int, TClubGlobal_Client>)JceUtil.DeepClone(this.mapClubGlobal_Client);
            copied.mapACG_Destiny_Client = (UniqueInfo.ConfigHashMap<int, TACG_Destiny_Client>)JceUtil.DeepClone(this.mapACG_Destiny_Client);
            copied.mapTAC_AISitPos_Client = (UniqueInfo.ConfigHashMap<int, TTAC_AISitPos_Client>)JceUtil.DeepClone(this.mapTAC_AISitPos_Client);
            copied.mapACG_ExtraStealEquipRate_Client = (UniqueInfo.ConfigHashMap<int, TACG_ExtraStealEquipRate_Client>)JceUtil.DeepClone(this.mapACG_ExtraStealEquipRate_Client);
            copied.mapACG_HolyDrop_Client = (UniqueInfo.ConfigHashMap<int, TACG_HolyDrop_Client>)JceUtil.DeepClone(this.mapACG_HolyDrop_Client);
            copied.mapACG_HolyDropPool_Client = (UniqueInfo.ConfigHashMap<int, TACG_HolyDropPool_Client>)JceUtil.DeepClone(this.mapACG_HolyDropPool_Client);
            copied.mapACG_DestinyBanner_Client = (UniqueInfo.ConfigHashMap<int, TACG_DestinyBanner_Client>)JceUtil.DeepClone(this.mapACG_DestinyBanner_Client);
            copied.mapACG_TechnologyJinNeng_Backgroun_Client = (UniqueInfo.ConfigHashMap<int, TACG_TechnologyJinNeng_Backgroun_Client>)JceUtil.DeepClone(this.mapACG_TechnologyJinNeng_Backgroun_Client);
            copied.mapACG_SeasonInfo_Client = (UniqueInfo.ConfigHashMap<int, TACG_SeasonInfo_Client>)JceUtil.DeepClone(this.mapACG_SeasonInfo_Client);
            copied.mapACG_RecommendClientCfg_Client = (UniqueInfo.ConfigHashMap<int, TACG_RecommendClientCfg_Client>)JceUtil.DeepClone(this.mapACG_RecommendClientCfg_Client);
            copied.mapClubMall_Client = (UniqueInfo.ConfigHashMap<int, TClubMall_Client>)JceUtil.DeepClone(this.mapClubMall_Client);
            copied.stTAllGameCfgClientExt1 = (TAllGameCfgClientExt1)JceUtil.DeepClone(this.stTAllGameCfgClientExt1);
            copied.mapGameModuleGuideIntro_Client = (UniqueInfo.ConfigHashMap<int, TGameModuleGuideIntro_Client>)JceUtil.DeepClone(this.mapGameModuleGuideIntro_Client);
            copied.mapACG_CombatEffect_Client = (UniqueInfo.ConfigHashMap<int, TACG_CombatEffect_Client>)JceUtil.DeepClone(this.mapACG_CombatEffect_Client);
            copied.mapACG_HeroSkin_Client = (UniqueInfo.ConfigHashMap<int, TACG_HeroSkin_Client>)JceUtil.DeepClone(this.mapACG_HeroSkin_Client);
            copied.mapChat_SimpleResponseVoice_Client = (UniqueInfo.ConfigHashMap<int, TChat_SimpleResponseVoice_Client>)JceUtil.DeepClone(this.mapChat_SimpleResponseVoice_Client);
            copied.mapManualTransTable_Client = (UniqueInfo.ConfigHashMap<int, TManualTransTable_Client>)JceUtil.DeepClone(this.mapManualTransTable_Client);
            copied.mapACG_ItemGetType_Client = (UniqueInfo.ConfigHashMap<int, TACG_ItemGetType_Client>)JceUtil.DeepClone(this.mapACG_ItemGetType_Client);
            copied.stTAllGameCfgClientExt2 = (TAllGameCfgClientExt2)JceUtil.DeepClone(this.stTAllGameCfgClientExt2);
            copied.stTAllGameCfgClientExt3 = (TAllGameCfgClientExt3)JceUtil.DeepClone(this.stTAllGameCfgClientExt3);
            copied.stTAllGameCfgClientExt4 = (TAllGameCfgClientExt4)JceUtil.DeepClone(this.stTAllGameCfgClientExt4);
            copied.stTAllGameCfgClientExt5 = (TAllGameCfgClientExt5)JceUtil.DeepClone(this.stTAllGameCfgClientExt5);
            copied.stTAllGameCfgClientExt6 = (TAllGameCfgClientExt6)JceUtil.DeepClone(this.stTAllGameCfgClientExt6);
            copied.mapBattleEndDisplayIconCfg_Client = (UniqueInfo.ConfigHashMap<int, TBattleEndDisplayIconCfg_Client>)JceUtil.DeepClone(this.mapBattleEndDisplayIconCfg_Client);
            copied.mapACG_MallBuyTips_Client = (UniqueInfo.ConfigHashMap<int, TACG_MallBuyTips_Client>)JceUtil.DeepClone(this.mapACG_MallBuyTips_Client);
            copied.mapACG_PrimeData_Client = (UniqueInfo.ConfigHashMap<int, TACG_PrimeData_Client>)JceUtil.DeepClone(this.mapACG_PrimeData_Client);
            copied.mapACG_LuckShare_Client = (UniqueInfo.ConfigHashMap<int, TACG_LuckShare_Client>)JceUtil.DeepClone(this.mapACG_LuckShare_Client);
            copied.mapJOCRank_LP_Cacualte_Config_Client = (UniqueInfo.ConfigHashMap<int, TJOCRank_LP_Cacualte_Config_Client>)JceUtil.DeepClone(this.mapJOCRank_LP_Cacualte_Config_Client);
            copied.mapTAC_FetterMusic_Client = (UniqueInfo.ConfigHashMap<int, TTAC_FetterMusic_Client>)JceUtil.DeepClone(this.mapTAC_FetterMusic_Client);
            copied.mapACGRecordBuffTrigger_Client = (UniqueInfo.ConfigHashMap<int, TACGRecordBuffTrigger_Client>)JceUtil.DeepClone(this.mapACGRecordBuffTrigger_Client);
            copied.mapTAC_FetterMusicPlan_Client = (UniqueInfo.ConfigHashMap<int, TTAC_FetterMusicPlan_Client>)JceUtil.DeepClone(this.mapTAC_FetterMusicPlan_Client);
            copied.stTAllGameCfgClientExt7 = (TAllGameCfgClientExt7)JceUtil.DeepClone(this.stTAllGameCfgClientExt7);
            return copied;
        }
    }
}

