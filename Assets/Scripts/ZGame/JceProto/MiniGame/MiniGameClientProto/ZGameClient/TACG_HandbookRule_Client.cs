//所在的Excel 【ACG_HandbookRule.xlsm】
//**************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_HandbookRule_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sName = "";

        public int iSequence = 0;

        public string sIndex = "";

        public string sForm = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sName, 1);
            _os.Write(iSequence, 2);
            _os.Write(sIndex, 4);
            _os.Write(sForm, 6);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sName = (string) _is.Read(sName, 1, false);

            iSequence = (int) _is.Read(iSequence, 2, false);

            sIndex = (string) _is.Read(sIndex, 4, false);

            sForm = (string) _is.Read(sForm, 6, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sName, "sName");
            _ds.Display(iSequence, "iSequence");
            _ds.Display(sIndex, "sIndex");
            _ds.Display(sForm, "sForm");
        }

        public override void Clear()
        {
            iID = 0;
            sName = "";
            iSequence = 0;
            sIndex = "";
            sForm = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_HandbookRule_Client();
            copied.iID = this.iID;
            copied.sName = this.sName;
            copied.iSequence = this.iSequence;
            copied.sIndex = this.sIndex;
            copied.sForm = this.sForm;
            return copied;
        }
    }
}

