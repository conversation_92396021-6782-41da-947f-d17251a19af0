// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGetContestRewardInfoReq : Wup.Jce.JceStruct
    {
        int _iContestID = 0;
        public int iContestID
        {
            get
            {
                 return _iContestID;
            }
            set
            {
                _iContestID = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iContestID, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iContestID = (int) _is.Read(iContestID, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iContestID, "iContestID");
        }

        public override void Clear()
        {
            iContestID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGetContestRewardInfoReq();
            copied.iContestID = this.iContestID;
            return copied;
        }
    }
}

