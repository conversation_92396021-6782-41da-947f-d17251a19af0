// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_EquipLiveCounter_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sDes = "";

        public string sParams = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sDes, 1);
            _os.Write(sParams, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sDes = (string) _is.Read(sDes, 1, false);

            sParams = (string) _is.Read(sParams, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sDes, "sDes");
            _ds.Display(sParams, "sParams");
        }

        public override void Clear()
        {
            iID = 0;
            sDes = "";
            sParams = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_EquipLiveCounter_Client();
            copied.iID = this.iID;
            copied.sDes = this.sDes;
            copied.sParams = this.sParams;
            return copied;
        }
    }
}

