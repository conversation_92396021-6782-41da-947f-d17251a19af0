//所在的Excel 【TAC_AITree.xlsm】
//********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_AIDropEquipNew_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iTrunCount1 = 0;

        public int iDropType1 = 0;

        public int iDifficultPct1 = 0;

        public int iGuarantee1 = 0;

        public int iTrunCount2 = 0;

        public int iDropType2 = 0;

        public int iDifficultPct2 = 0;

        public int iGuarantee2 = 0;

        public int iTrunCount3 = 0;

        public int iDropType3 = 0;

        public int iDifficultPct3 = 0;

        public int iGuarantee3 = 0;

        public int iTrunCount4 = 0;

        public int iDropType4 = 0;

        public int iDifficultPct4 = 0;

        public int iGuarantee4 = 0;

        public int iTrunCount5 = 0;

        public int iDropType5 = 0;

        public int iDifficultPct5 = 0;

        public int iGuarantee5 = 0;

        public int iTrunCount6 = 0;

        public int iDropType6 = 0;

        public int iDifficultPct6 = 0;

        public int iGuarantee6 = 0;

        public int iTrunCount7 = 0;

        public int iDropType7 = 0;

        public int iDifficultPct7 = 0;

        public int iGuarantee7 = 0;

        public int iTrunCount8 = 0;

        public int iDropType8 = 0;

        public int iDifficultPct8 = 0;

        public int iGuarantee8 = 0;

        public int iTrunCount9 = 0;

        public int iDropType9 = 0;

        public int iDifficultPct9 = 0;

        public int iGuarantee9 = 0;

        public int iTrunCount10 = 0;

        public int iDropType10 = 0;

        public int iDifficultPct10 = 0;

        public int iGuarantee10 = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iTrunCount1, 1);
            _os.Write(iDropType1, 2);
            _os.Write(iDifficultPct1, 3);
            _os.Write(iGuarantee1, 4);
            _os.Write(iTrunCount2, 5);
            _os.Write(iDropType2, 6);
            _os.Write(iDifficultPct2, 7);
            _os.Write(iGuarantee2, 8);
            _os.Write(iTrunCount3, 9);
            _os.Write(iDropType3, 10);
            _os.Write(iDifficultPct3, 11);
            _os.Write(iGuarantee3, 12);
            _os.Write(iTrunCount4, 13);
            _os.Write(iDropType4, 14);
            _os.Write(iDifficultPct4, 15);
            _os.Write(iGuarantee4, 16);
            _os.Write(iTrunCount5, 17);
            _os.Write(iDropType5, 18);
            _os.Write(iDifficultPct5, 19);
            _os.Write(iGuarantee5, 20);
            _os.Write(iTrunCount6, 21);
            _os.Write(iDropType6, 22);
            _os.Write(iDifficultPct6, 23);
            _os.Write(iGuarantee6, 24);
            _os.Write(iTrunCount7, 25);
            _os.Write(iDropType7, 26);
            _os.Write(iDifficultPct7, 27);
            _os.Write(iGuarantee7, 28);
            _os.Write(iTrunCount8, 29);
            _os.Write(iDropType8, 30);
            _os.Write(iDifficultPct8, 31);
            _os.Write(iGuarantee8, 32);
            _os.Write(iTrunCount9, 33);
            _os.Write(iDropType9, 34);
            _os.Write(iDifficultPct9, 35);
            _os.Write(iGuarantee9, 36);
            _os.Write(iTrunCount10, 37);
            _os.Write(iDropType10, 38);
            _os.Write(iDifficultPct10, 39);
            _os.Write(iGuarantee10, 40);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iTrunCount1 = (int) _is.Read(iTrunCount1, 1, false);

            iDropType1 = (int) _is.Read(iDropType1, 2, false);

            iDifficultPct1 = (int) _is.Read(iDifficultPct1, 3, false);

            iGuarantee1 = (int) _is.Read(iGuarantee1, 4, false);

            iTrunCount2 = (int) _is.Read(iTrunCount2, 5, false);

            iDropType2 = (int) _is.Read(iDropType2, 6, false);

            iDifficultPct2 = (int) _is.Read(iDifficultPct2, 7, false);

            iGuarantee2 = (int) _is.Read(iGuarantee2, 8, false);

            iTrunCount3 = (int) _is.Read(iTrunCount3, 9, false);

            iDropType3 = (int) _is.Read(iDropType3, 10, false);

            iDifficultPct3 = (int) _is.Read(iDifficultPct3, 11, false);

            iGuarantee3 = (int) _is.Read(iGuarantee3, 12, false);

            iTrunCount4 = (int) _is.Read(iTrunCount4, 13, false);

            iDropType4 = (int) _is.Read(iDropType4, 14, false);

            iDifficultPct4 = (int) _is.Read(iDifficultPct4, 15, false);

            iGuarantee4 = (int) _is.Read(iGuarantee4, 16, false);

            iTrunCount5 = (int) _is.Read(iTrunCount5, 17, false);

            iDropType5 = (int) _is.Read(iDropType5, 18, false);

            iDifficultPct5 = (int) _is.Read(iDifficultPct5, 19, false);

            iGuarantee5 = (int) _is.Read(iGuarantee5, 20, false);

            iTrunCount6 = (int) _is.Read(iTrunCount6, 21, false);

            iDropType6 = (int) _is.Read(iDropType6, 22, false);

            iDifficultPct6 = (int) _is.Read(iDifficultPct6, 23, false);

            iGuarantee6 = (int) _is.Read(iGuarantee6, 24, false);

            iTrunCount7 = (int) _is.Read(iTrunCount7, 25, false);

            iDropType7 = (int) _is.Read(iDropType7, 26, false);

            iDifficultPct7 = (int) _is.Read(iDifficultPct7, 27, false);

            iGuarantee7 = (int) _is.Read(iGuarantee7, 28, false);

            iTrunCount8 = (int) _is.Read(iTrunCount8, 29, false);

            iDropType8 = (int) _is.Read(iDropType8, 30, false);

            iDifficultPct8 = (int) _is.Read(iDifficultPct8, 31, false);

            iGuarantee8 = (int) _is.Read(iGuarantee8, 32, false);

            iTrunCount9 = (int) _is.Read(iTrunCount9, 33, false);

            iDropType9 = (int) _is.Read(iDropType9, 34, false);

            iDifficultPct9 = (int) _is.Read(iDifficultPct9, 35, false);

            iGuarantee9 = (int) _is.Read(iGuarantee9, 36, false);

            iTrunCount10 = (int) _is.Read(iTrunCount10, 37, false);

            iDropType10 = (int) _is.Read(iDropType10, 38, false);

            iDifficultPct10 = (int) _is.Read(iDifficultPct10, 39, false);

            iGuarantee10 = (int) _is.Read(iGuarantee10, 40, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iTrunCount1, "iTrunCount1");
            _ds.Display(iDropType1, "iDropType1");
            _ds.Display(iDifficultPct1, "iDifficultPct1");
            _ds.Display(iGuarantee1, "iGuarantee1");
            _ds.Display(iTrunCount2, "iTrunCount2");
            _ds.Display(iDropType2, "iDropType2");
            _ds.Display(iDifficultPct2, "iDifficultPct2");
            _ds.Display(iGuarantee2, "iGuarantee2");
            _ds.Display(iTrunCount3, "iTrunCount3");
            _ds.Display(iDropType3, "iDropType3");
            _ds.Display(iDifficultPct3, "iDifficultPct3");
            _ds.Display(iGuarantee3, "iGuarantee3");
            _ds.Display(iTrunCount4, "iTrunCount4");
            _ds.Display(iDropType4, "iDropType4");
            _ds.Display(iDifficultPct4, "iDifficultPct4");
            _ds.Display(iGuarantee4, "iGuarantee4");
            _ds.Display(iTrunCount5, "iTrunCount5");
            _ds.Display(iDropType5, "iDropType5");
            _ds.Display(iDifficultPct5, "iDifficultPct5");
            _ds.Display(iGuarantee5, "iGuarantee5");
            _ds.Display(iTrunCount6, "iTrunCount6");
            _ds.Display(iDropType6, "iDropType6");
            _ds.Display(iDifficultPct6, "iDifficultPct6");
            _ds.Display(iGuarantee6, "iGuarantee6");
            _ds.Display(iTrunCount7, "iTrunCount7");
            _ds.Display(iDropType7, "iDropType7");
            _ds.Display(iDifficultPct7, "iDifficultPct7");
            _ds.Display(iGuarantee7, "iGuarantee7");
            _ds.Display(iTrunCount8, "iTrunCount8");
            _ds.Display(iDropType8, "iDropType8");
            _ds.Display(iDifficultPct8, "iDifficultPct8");
            _ds.Display(iGuarantee8, "iGuarantee8");
            _ds.Display(iTrunCount9, "iTrunCount9");
            _ds.Display(iDropType9, "iDropType9");
            _ds.Display(iDifficultPct9, "iDifficultPct9");
            _ds.Display(iGuarantee9, "iGuarantee9");
            _ds.Display(iTrunCount10, "iTrunCount10");
            _ds.Display(iDropType10, "iDropType10");
            _ds.Display(iDifficultPct10, "iDifficultPct10");
            _ds.Display(iGuarantee10, "iGuarantee10");
        }

        public override void Clear()
        {
            iID = 0;
            iTrunCount1 = 0;
            iDropType1 = 0;
            iDifficultPct1 = 0;
            iGuarantee1 = 0;
            iTrunCount2 = 0;
            iDropType2 = 0;
            iDifficultPct2 = 0;
            iGuarantee2 = 0;
            iTrunCount3 = 0;
            iDropType3 = 0;
            iDifficultPct3 = 0;
            iGuarantee3 = 0;
            iTrunCount4 = 0;
            iDropType4 = 0;
            iDifficultPct4 = 0;
            iGuarantee4 = 0;
            iTrunCount5 = 0;
            iDropType5 = 0;
            iDifficultPct5 = 0;
            iGuarantee5 = 0;
            iTrunCount6 = 0;
            iDropType6 = 0;
            iDifficultPct6 = 0;
            iGuarantee6 = 0;
            iTrunCount7 = 0;
            iDropType7 = 0;
            iDifficultPct7 = 0;
            iGuarantee7 = 0;
            iTrunCount8 = 0;
            iDropType8 = 0;
            iDifficultPct8 = 0;
            iGuarantee8 = 0;
            iTrunCount9 = 0;
            iDropType9 = 0;
            iDifficultPct9 = 0;
            iGuarantee9 = 0;
            iTrunCount10 = 0;
            iDropType10 = 0;
            iDifficultPct10 = 0;
            iGuarantee10 = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TTAC_AIDropEquipNew_Client();
            copied.iID = this.iID;
            copied.iTrunCount1 = this.iTrunCount1;
            copied.iDropType1 = this.iDropType1;
            copied.iDifficultPct1 = this.iDifficultPct1;
            copied.iGuarantee1 = this.iGuarantee1;
            copied.iTrunCount2 = this.iTrunCount2;
            copied.iDropType2 = this.iDropType2;
            copied.iDifficultPct2 = this.iDifficultPct2;
            copied.iGuarantee2 = this.iGuarantee2;
            copied.iTrunCount3 = this.iTrunCount3;
            copied.iDropType3 = this.iDropType3;
            copied.iDifficultPct3 = this.iDifficultPct3;
            copied.iGuarantee3 = this.iGuarantee3;
            copied.iTrunCount4 = this.iTrunCount4;
            copied.iDropType4 = this.iDropType4;
            copied.iDifficultPct4 = this.iDifficultPct4;
            copied.iGuarantee4 = this.iGuarantee4;
            copied.iTrunCount5 = this.iTrunCount5;
            copied.iDropType5 = this.iDropType5;
            copied.iDifficultPct5 = this.iDifficultPct5;
            copied.iGuarantee5 = this.iGuarantee5;
            copied.iTrunCount6 = this.iTrunCount6;
            copied.iDropType6 = this.iDropType6;
            copied.iDifficultPct6 = this.iDifficultPct6;
            copied.iGuarantee6 = this.iGuarantee6;
            copied.iTrunCount7 = this.iTrunCount7;
            copied.iDropType7 = this.iDropType7;
            copied.iDifficultPct7 = this.iDifficultPct7;
            copied.iGuarantee7 = this.iGuarantee7;
            copied.iTrunCount8 = this.iTrunCount8;
            copied.iDropType8 = this.iDropType8;
            copied.iDifficultPct8 = this.iDifficultPct8;
            copied.iGuarantee8 = this.iGuarantee8;
            copied.iTrunCount9 = this.iTrunCount9;
            copied.iDropType9 = this.iDropType9;
            copied.iDifficultPct9 = this.iDifficultPct9;
            copied.iGuarantee9 = this.iGuarantee9;
            copied.iTrunCount10 = this.iTrunCount10;
            copied.iDropType10 = this.iDropType10;
            copied.iDifficultPct10 = this.iDifficultPct10;
            copied.iGuarantee10 = this.iGuarantee10;
            return copied;
        }
    }
}

