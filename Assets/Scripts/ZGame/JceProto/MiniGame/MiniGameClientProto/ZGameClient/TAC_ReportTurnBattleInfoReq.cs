// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_ReportTurnBattleInfoReq : Wup.Jce.JceStruct
    {
        public TKFrame.TKDictionary<long, TAC_UserTurnBattleHeroInfo> mapTurnBattle {get; set;} 

        int _iBattleType = 0;
        public int iBattleType
        {
            get
            {
                 return _iBattleType;
            }
            set
            {
                _iBattleType = value; 
            }
        }

        string _sFightVer = "";
        public string sFightVer
        {
            get
            {
                 return _sFightVer;
            }
            set
            {
                _sFightVer = value; 
            }
        }

        int _iTurn = 0;
        public int iTurn
        {
            get
            {
                 return _iTurn;
            }
            set
            {
                _iTurn = value; 
            }
        }

        long _iGid = 0;
        public long iGid
        {
            get
            {
                 return _iGid;
            }
            set
            {
                _iGid = value; 
            }
        }

        int _iGameType = 0;
        public int iGameType
        {
            get
            {
                 return _iGameType;
            }
            set
            {
                _iGameType = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(mapTurnBattle, 0);
            _os.Write(iBattleType, 1);
            _os.Write(sFightVer, 2);
            _os.Write(iTurn, 3);
            _os.Write(iGid, 4);
            _os.Write(iGameType, 5);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            mapTurnBattle = (TKFrame.TKDictionary<long, TAC_UserTurnBattleHeroInfo>) _is.Read(mapTurnBattle, 0, false);

            iBattleType = (int) _is.Read(iBattleType, 1, false);

            sFightVer = (string) _is.Read(sFightVer, 2, false);

            iTurn = (int) _is.Read(iTurn, 3, false);

            iGid = (long) _is.Read(iGid, 4, false);

            iGameType = (int) _is.Read(iGameType, 5, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(mapTurnBattle, "mapTurnBattle");
            _ds.Display(iBattleType, "iBattleType");
            _ds.Display(sFightVer, "sFightVer");
            _ds.Display(iTurn, "iTurn");
            _ds.Display(iGid, "iGid");
            _ds.Display(iGameType, "iGameType");
        }

    }
}

