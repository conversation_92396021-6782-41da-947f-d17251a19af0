// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GuildGeoSettlementInfo : Wup.Jce.JceStruct
    {
        /// <summary>
        /// 结算战区 (每周一结算)
        /// </summary>
        public GeoAddress settledGeoAddress;

        /// <summary>
        /// 结算排名 (每周一结算)
        /// </summary>
        public SettledRank settledRank;

        /// <summary>
        /// 最近更新结算战区/排名的时间; 用来保证每周仅需更新一次
        /// </summary>
        public int checkSettleTimestamp = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(settledGeoAddress, 0);
            _os.Write(settledRank, 1);
            _os.Write(checkSettleTimestamp, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            settledGeoAddress = (GeoAddress) _is.Read(settledGeoAddress, 0, false);

            settledRank = (SettledRank) _is.Read(settledRank, 1, false);

            checkSettleTimestamp = (int) _is.Read(checkSettleTimestamp, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(settledGeoAddress, "settledGeoAddress");
            _ds.Display(settledRank, "settledRank");
            _ds.Display(checkSettleTimestamp, "checkSettleTimestamp");
        }

        public override void Clear()
        {
            settledGeoAddress = null;
            settledRank = null;
            checkSettleTimestamp = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GuildGeoSettlementInfo();
            copied.settledGeoAddress = (GeoAddress)JceUtil.DeepClone(this.settledGeoAddress);
            copied.settledRank = (SettledRank)JceUtil.DeepClone(this.settledRank);
            copied.checkSettleTimestamp = this.checkSettleTimestamp;
            return copied;
        }
    }
}

