// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TMainHallLeftRecommend : Wup.Jce.JceStruct
    {
        public TMainHallLeftRecommand_Server stLeftRecommendConfig;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(stLeftRecommendConfig, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            stLeftRecommendConfig = (TMainHallLeftRecommand_Server) _is.Read(stLeftRecommendConfig, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(stLeftRecommendConfig, "stLeftRecommendConfig");
        }

        public override void Clear()
        {
            stLeftRecommendConfig = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TMainHallLeftRecommend();
            copied.stLeftRecommendConfig = (TMainHallLeftRecommand_Server)JceUtil.DeepClone(this.stLeftRecommendConfig);
            return copied;
        }
    }
}

