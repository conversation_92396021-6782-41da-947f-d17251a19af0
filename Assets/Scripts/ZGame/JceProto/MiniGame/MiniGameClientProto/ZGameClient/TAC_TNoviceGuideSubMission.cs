// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TNoviceGuideSubMission : Wup.Jce.JceStruct
    {
        int _iID = 0;
        public int iID
        {
            get
            {
                 return _iID;
            }
            set
            {
                _iID = value; 
            }
        }

        int _iType = 0;
        public int iType
        {
            get
            {
                 return _iType;
            }
            set
            {
                _iType = value; 
            }
        }

        string _sDesc = "";
        public string sDesc
        {
            get
            {
                 return _sDesc;
            }
            set
            {
                _sDesc = value; 
            }
        }

        int _iSpecTargetCount = 0;
        public int iSpecTargetCount
        {
            get
            {
                 return _iSpecTargetCount;
            }
            set
            {
                _iSpecTargetCount = value; 
            }
        }

        int _iSpecTargetId = 0;
        public int iSpecTargetId
        {
            get
            {
                 return _iSpecTargetId;
            }
            set
            {
                _iSpecTargetId = value; 
            }
        }

        int _iClassTargetCount = 0;
        public int iClassTargetCount
        {
            get
            {
                 return _iClassTargetCount;
            }
            set
            {
                _iClassTargetCount = value; 
            }
        }

        int _iClassTargetId = 0;
        public int iClassTargetId
        {
            get
            {
                 return _iClassTargetId;
            }
            set
            {
                _iClassTargetId = value; 
            }
        }

        int _iCoinNum = 0;
        public int iCoinNum
        {
            get
            {
                 return _iCoinNum;
            }
            set
            {
                _iCoinNum = value; 
            }
        }

        int _i3StarLevelUpHeroId = 0;
        public int i3StarLevelUpHeroId
        {
            get
            {
                 return _i3StarLevelUpHeroId;
            }
            set
            {
                _i3StarLevelUpHeroId = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iType, 1);
            _os.Write(sDesc, 2);
            _os.Write(iSpecTargetCount, 3);
            _os.Write(iSpecTargetId, 4);
            _os.Write(iClassTargetCount, 5);
            _os.Write(iClassTargetId, 6);
            _os.Write(iCoinNum, 7);
            _os.Write(i3StarLevelUpHeroId, 8);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iType = (int) _is.Read(iType, 1, false);

            sDesc = (string) _is.Read(sDesc, 2, false);

            iSpecTargetCount = (int) _is.Read(iSpecTargetCount, 3, false);

            iSpecTargetId = (int) _is.Read(iSpecTargetId, 4, false);

            iClassTargetCount = (int) _is.Read(iClassTargetCount, 5, false);

            iClassTargetId = (int) _is.Read(iClassTargetId, 6, false);

            iCoinNum = (int) _is.Read(iCoinNum, 7, false);

            i3StarLevelUpHeroId = (int) _is.Read(i3StarLevelUpHeroId, 8, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iType, "iType");
            _ds.Display(sDesc, "sDesc");
            _ds.Display(iSpecTargetCount, "iSpecTargetCount");
            _ds.Display(iSpecTargetId, "iSpecTargetId");
            _ds.Display(iClassTargetCount, "iClassTargetCount");
            _ds.Display(iClassTargetId, "iClassTargetId");
            _ds.Display(iCoinNum, "iCoinNum");
            _ds.Display(i3StarLevelUpHeroId, "i3StarLevelUpHeroId");
        }

    }
}

