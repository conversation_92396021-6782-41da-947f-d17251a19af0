// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ReportLogNotify : Wup.Jce.JceStruct
    {
        public string lastDate = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(lastDate, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            lastDate = (string) _is.Read(lastDate, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(lastDate, "lastDate");
        }

        public override void Clear()
        {
            lastDate = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ReportLogNotify();
            copied.lastDate = this.lastDate;
            return copied;
        }
    }
}

