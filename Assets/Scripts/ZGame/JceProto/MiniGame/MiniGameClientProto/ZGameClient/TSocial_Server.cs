// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TSocial_Server : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sName = "";

        public int iShareType = 0;

        public int iOpenPlatType = 0;

        public string sStartTime = "";

        public int iStartTime = 0;

        public string sOverTime = "";

        public int iOverTime = 0;

        public string sParams1 = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sName, 1);
            _os.Write(iShareType, 2);
            _os.Write(iOpenPlatType, 3);
            _os.Write(sStartTime, 4);
            _os.Write(iStartTime, 5);
            _os.Write(sOverTime, 6);
            _os.Write(iOverTime, 7);
            _os.Write(sParams1, 8);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sName = (string) _is.Read(sName, 1, false);

            iShareType = (int) _is.Read(iShareType, 2, false);

            iOpenPlatType = (int) _is.Read(iOpenPlatType, 3, false);

            sStartTime = (string) _is.Read(sStartTime, 4, false);

            iStartTime = (int) _is.Read(iStartTime, 5, false);

            sOverTime = (string) _is.Read(sOverTime, 6, false);

            iOverTime = (int) _is.Read(iOverTime, 7, false);

            sParams1 = (string) _is.Read(sParams1, 8, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sName, "sName");
            _ds.Display(iShareType, "iShareType");
            _ds.Display(iOpenPlatType, "iOpenPlatType");
            _ds.Display(sStartTime, "sStartTime");
            _ds.Display(iStartTime, "iStartTime");
            _ds.Display(sOverTime, "sOverTime");
            _ds.Display(iOverTime, "iOverTime");
            _ds.Display(sParams1, "sParams1");
        }

        public override void Clear()
        {
            iID = 0;
            sName = "";
            iShareType = 0;
            iOpenPlatType = 0;
            sStartTime = "";
            iStartTime = 0;
            sOverTime = "";
            iOverTime = 0;
            sParams1 = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TSocial_Server();
            copied.iID = this.iID;
            copied.sName = this.sName;
            copied.iShareType = this.iShareType;
            copied.iOpenPlatType = this.iOpenPlatType;
            copied.sStartTime = this.sStartTime;
            copied.iStartTime = this.iStartTime;
            copied.sOverTime = this.sOverTime;
            copied.iOverTime = this.iOverTime;
            copied.sParams1 = this.sParams1;
            return copied;
        }
    }
}

