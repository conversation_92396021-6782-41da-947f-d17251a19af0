// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TFriendSysRefreshRealtimeReq : Wup.Jce.JceStruct
    {
        public TUserID uid;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(uid, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            uid = (TUserID) _is.Read(uid, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(uid, "uid");
        }

        public override void Clear()
        {
            uid = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TFriendSysRefreshRealtimeReq();
            copied.uid = (TUserID)JceUtil.DeepClone(this.uid);
            return copied;
        }
    }
}

