// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGetDailyTaskRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public System.Collections.Generic.List<TBPTaskInfo> vecDailyTask;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(vecDailyTask, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            vecDailyTask = (System.Collections.Generic.List<TBPTaskInfo>) _is.Read(vecDailyTask, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(vecDailyTask, "vecDailyTask");
        }

        public override void Clear()
        {
            iRet = 0;
            vecDailyTask = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGetDailyTaskRsp();
            copied.iRet = this.iRet;
            copied.vecDailyTask = (System.Collections.Generic.List<TBPTaskInfo>)JceUtil.DeepClone(this.vecDailyTask);
            return copied;
        }
    }
}

