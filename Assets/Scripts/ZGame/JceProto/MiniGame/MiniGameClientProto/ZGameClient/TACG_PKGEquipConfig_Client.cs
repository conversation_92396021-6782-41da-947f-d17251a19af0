// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_PKGEquipConfig_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iPlan = 0;

        public int iWeight = 0;

        public int iEquipA = 0;

        public int iEquipB = 0;

        public int iEquipC = 0;

        public string sEquipC = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iPlan, 1);
            _os.Write(iWeight, 2);
            _os.Write(iEquipA, 3);
            _os.Write(iEquipB, 4);
            _os.Write(iEquipC, 5);
            _os.Write(sEquipC, 6);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iPlan = (int) _is.Read(iPlan, 1, false);

            iWeight = (int) _is.Read(iWeight, 2, false);

            iEquipA = (int) _is.Read(iEquipA, 3, false);

            iEquipB = (int) _is.Read(iEquipB, 4, false);

            iEquipC = (int) _is.Read(iEquipC, 5, false);

            sEquipC = (string) _is.Read(sEquipC, 6, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iPlan, "iPlan");
            _ds.Display(iWeight, "iWeight");
            _ds.Display(iEquipA, "iEquipA");
            _ds.Display(iEquipB, "iEquipB");
            _ds.Display(iEquipC, "iEquipC");
            _ds.Display(sEquipC, "sEquipC");
        }

        public override void Clear()
        {
            iID = 0;
            iPlan = 0;
            iWeight = 0;
            iEquipA = 0;
            iEquipB = 0;
            iEquipC = 0;
            sEquipC = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_PKGEquipConfig_Client();
            copied.iID = this.iID;
            copied.iPlan = this.iPlan;
            copied.iWeight = this.iWeight;
            copied.iEquipA = this.iEquipA;
            copied.iEquipB = this.iEquipB;
            copied.iEquipC = this.iEquipC;
            copied.sEquipC = this.sEquipC;
            return copied;
        }
    }
}

