// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAllGameCfgClientExt7 : Wup.Jce.JceStruct
    {

        public UniqueInfo.ConfigHashMap<int, TPackage_Client> mapPackage_Client;

        public UniqueInfo.ConfigHashMap<int, TPackageRule_Client> mapPackageRule_Client;


        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(mapPackage_Client, 7);
            _os.Write(mapPackageRule_Client, 8);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {

            mapPackage_Client = (UniqueInfo.ConfigHashMap<int, TPackage_Client>) _is.Read(mapPackage_Client, 7, false);

            mapPackageRule_Client = (UniqueInfo.ConfigHashMap<int, TPackageRule_Client>) _is.Read(mapPackageRule_Client, 8, false);

           

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);

            _ds.Display(mapPackage_Client, "mapPackage_Client");
            _ds.Display(mapPackageRule_Client, "mapPackageRule_Client");

        }

        public override void Clear()
        {

            if (mapPackage_Client != null) mapPackage_Client.Clear();
            if (mapPackageRule_Client != null) mapPackageRule_Client.Clear();

        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAllGameCfgClientExt7();

            copied.mapPackage_Client = (UniqueInfo.ConfigHashMap<int, TPackage_Client>)JceUtil.DeepClone(this.mapPackage_Client);
            copied.mapPackageRule_Client = (UniqueInfo.ConfigHashMap<int, TPackageRule_Client>)JceUtil.DeepClone(this.mapPackageRule_Client);

            return copied;
        }
    }
}

