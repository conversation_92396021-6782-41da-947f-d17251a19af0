//所在的Excel 【TAC_AITree.xlsm】
//********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_AIAssignTag_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iSetID = 0;

        public int iSegmentID = 0;

        public string sGroupTag1 = "";

        public int iGroupTag1ChooseNum = 0;

        public string sGroupTag2 = "";

        public int iGroupTag2ChooseNum = 0;

        public string sGroupTag3 = "";

        public int iGroupTag3ChooseNum = 0;

        public int iSelectOneByOne = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iSetID, 3);
            _os.Write(iSegmentID, 4);
            _os.Write(sGroupTag1, 5);
            _os.Write(iGroupTag1ChooseNum, 6);
            _os.Write(sGroupTag2, 7);
            _os.Write(iGroupTag2ChooseNum, 8);
            _os.Write(sGroupTag3, 9);
            _os.Write(iGroupTag3ChooseNum, 10);
            _os.Write(iSelectOneByOne, 11);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iSetID = (int) _is.Read(iSetID, 3, false);

            iSegmentID = (int) _is.Read(iSegmentID, 4, false);

            sGroupTag1 = (string) _is.Read(sGroupTag1, 5, false);

            iGroupTag1ChooseNum = (int) _is.Read(iGroupTag1ChooseNum, 6, false);

            sGroupTag2 = (string) _is.Read(sGroupTag2, 7, false);

            iGroupTag2ChooseNum = (int) _is.Read(iGroupTag2ChooseNum, 8, false);

            sGroupTag3 = (string) _is.Read(sGroupTag3, 9, false);

            iGroupTag3ChooseNum = (int) _is.Read(iGroupTag3ChooseNum, 10, false);

            iSelectOneByOne = (int) _is.Read(iSelectOneByOne, 11, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iSetID, "iSetID");
            _ds.Display(iSegmentID, "iSegmentID");
            _ds.Display(sGroupTag1, "sGroupTag1");
            _ds.Display(iGroupTag1ChooseNum, "iGroupTag1ChooseNum");
            _ds.Display(sGroupTag2, "sGroupTag2");
            _ds.Display(iGroupTag2ChooseNum, "iGroupTag2ChooseNum");
            _ds.Display(sGroupTag3, "sGroupTag3");
            _ds.Display(iGroupTag3ChooseNum, "iGroupTag3ChooseNum");
            _ds.Display(iSelectOneByOne, "iSelectOneByOne");
        }

        public override void Clear()
        {
            iID = 0;
            iSetID = 0;
            iSegmentID = 0;
            sGroupTag1 = "";
            iGroupTag1ChooseNum = 0;
            sGroupTag2 = "";
            iGroupTag2ChooseNum = 0;
            sGroupTag3 = "";
            iGroupTag3ChooseNum = 0;
            iSelectOneByOne = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TTAC_AIAssignTag_Client();
            copied.iID = this.iID;
            copied.iSetID = this.iSetID;
            copied.iSegmentID = this.iSegmentID;
            copied.sGroupTag1 = this.sGroupTag1;
            copied.iGroupTag1ChooseNum = this.iGroupTag1ChooseNum;
            copied.sGroupTag2 = this.sGroupTag2;
            copied.iGroupTag2ChooseNum = this.iGroupTag2ChooseNum;
            copied.sGroupTag3 = this.sGroupTag3;
            copied.iGroupTag3ChooseNum = this.iGroupTag3ChooseNum;
            copied.iSelectOneByOne = this.iSelectOneByOne;
            return copied;
        }
    }
}

