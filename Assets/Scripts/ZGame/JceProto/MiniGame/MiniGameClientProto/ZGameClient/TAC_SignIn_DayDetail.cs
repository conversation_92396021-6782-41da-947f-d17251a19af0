// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 3.2.1.7 by WSRD Tencent.
// Generated from `./Common/ClientProto/ZGameClient.jce'
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_SignIn_DayDetail : Wup.Jce.JceStruct
    {
        public int SignInStatus = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(SignInStatus, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            SignInStatus = (int) _is.Read(SignInStatus, 0, true);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(SignInStatus, "SignInStatus");
        }

    }
}

