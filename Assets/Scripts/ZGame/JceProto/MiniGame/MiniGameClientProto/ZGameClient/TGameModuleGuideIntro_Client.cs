//所在的Excel 【GameModule.xlsm】
//********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGameModuleGuideIntro_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iSceneType = 0;

        public string sTitle = "";

        public string sDesc = "";

        public string sPic_1 = "";

        public string sPicTitle_1 = "";

        public string sPicDesc_1 = "";

        public string sPic_2 = "";

        public string sPicTitle_2 = "";

        public string sPicDesc_2 = "";

        public string sPic_3 = "";

        public string sPicTitle_3 = "";

        public string sPicDesc_3 = "";

        public int iShowTimes = 0;

        public string sEndDisplayIcon = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iSceneType, 1);
            _os.Write(sTitle, 2);
            _os.Write(sDesc, 3);
            _os.Write(sPic_1, 4);
            _os.Write(sPicTitle_1, 5);
            _os.Write(sPicDesc_1, 6);
            _os.Write(sPic_2, 7);
            _os.Write(sPicTitle_2, 8);
            _os.Write(sPicDesc_2, 9);
            _os.Write(sPic_3, 10);
            _os.Write(sPicTitle_3, 11);
            _os.Write(sPicDesc_3, 12);
            _os.Write(iShowTimes, 13);
            _os.Write(sEndDisplayIcon, 14);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iSceneType = (int) _is.Read(iSceneType, 1, false);

            sTitle = (string) _is.Read(sTitle, 2, false);

            sDesc = (string) _is.Read(sDesc, 3, false);

            sPic_1 = (string) _is.Read(sPic_1, 4, false);

            sPicTitle_1 = (string) _is.Read(sPicTitle_1, 5, false);

            sPicDesc_1 = (string) _is.Read(sPicDesc_1, 6, false);

            sPic_2 = (string) _is.Read(sPic_2, 7, false);

            sPicTitle_2 = (string) _is.Read(sPicTitle_2, 8, false);

            sPicDesc_2 = (string) _is.Read(sPicDesc_2, 9, false);

            sPic_3 = (string) _is.Read(sPic_3, 10, false);

            sPicTitle_3 = (string) _is.Read(sPicTitle_3, 11, false);

            sPicDesc_3 = (string) _is.Read(sPicDesc_3, 12, false);

            iShowTimes = (int) _is.Read(iShowTimes, 13, false);

            sEndDisplayIcon = (string) _is.Read(sEndDisplayIcon, 14, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iSceneType, "iSceneType");
            _ds.Display(sTitle, "sTitle");
            _ds.Display(sDesc, "sDesc");
            _ds.Display(sPic_1, "sPic_1");
            _ds.Display(sPicTitle_1, "sPicTitle_1");
            _ds.Display(sPicDesc_1, "sPicDesc_1");
            _ds.Display(sPic_2, "sPic_2");
            _ds.Display(sPicTitle_2, "sPicTitle_2");
            _ds.Display(sPicDesc_2, "sPicDesc_2");
            _ds.Display(sPic_3, "sPic_3");
            _ds.Display(sPicTitle_3, "sPicTitle_3");
            _ds.Display(sPicDesc_3, "sPicDesc_3");
            _ds.Display(iShowTimes, "iShowTimes");
            _ds.Display(sEndDisplayIcon, "sEndDisplayIcon");
        }

        public override void Clear()
        {
            iID = 0;
            iSceneType = 0;
            sTitle = "";
            sDesc = "";
            sPic_1 = "";
            sPicTitle_1 = "";
            sPicDesc_1 = "";
            sPic_2 = "";
            sPicTitle_2 = "";
            sPicDesc_2 = "";
            sPic_3 = "";
            sPicTitle_3 = "";
            sPicDesc_3 = "";
            iShowTimes = 0;
            sEndDisplayIcon = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGameModuleGuideIntro_Client();
            copied.iID = this.iID;
            copied.iSceneType = this.iSceneType;
            copied.sTitle = this.sTitle;
            copied.sDesc = this.sDesc;
            copied.sPic_1 = this.sPic_1;
            copied.sPicTitle_1 = this.sPicTitle_1;
            copied.sPicDesc_1 = this.sPicDesc_1;
            copied.sPic_2 = this.sPic_2;
            copied.sPicTitle_2 = this.sPicTitle_2;
            copied.sPicDesc_2 = this.sPicDesc_2;
            copied.sPic_3 = this.sPic_3;
            copied.sPicTitle_3 = this.sPicTitle_3;
            copied.sPicDesc_3 = this.sPicDesc_3;
            copied.iShowTimes = this.iShowTimes;
            copied.sEndDisplayIcon = this.sEndDisplayIcon;
            return copied;
        }
    }
}

