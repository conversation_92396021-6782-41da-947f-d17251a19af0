// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGetSeasonTaskInfoRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public System.Collections.Generic.List<TSeasonActiveTaskInfo> vecSeasonActiveTaskInfo;

        public int iTimestamp = 0;

        public System.Collections.Generic.List<TSeasonReward_Server> vecSeasonReward;

        public TKFrame.TKDictionary<int, System.Collections.Generic.List<TSeasonActiveTaskInfo>> mapTimeLimitSeasonTaskInfo;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(vecSeasonActiveTaskInfo, 1);
            _os.Write(iTimestamp, 2);
            _os.Write(vecSeasonReward, 3);
            _os.Write(mapTimeLimitSeasonTaskInfo, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            vecSeasonActiveTaskInfo = (System.Collections.Generic.List<TSeasonActiveTaskInfo>) _is.Read(vecSeasonActiveTaskInfo, 1, false);

            iTimestamp = (int) _is.Read(iTimestamp, 2, false);

            vecSeasonReward = (System.Collections.Generic.List<TSeasonReward_Server>) _is.Read(vecSeasonReward, 3, false);

            mapTimeLimitSeasonTaskInfo = (TKFrame.TKDictionary<int, System.Collections.Generic.List<TSeasonActiveTaskInfo>>) _is.Read(mapTimeLimitSeasonTaskInfo, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(vecSeasonActiveTaskInfo, "vecSeasonActiveTaskInfo");
            _ds.Display(iTimestamp, "iTimestamp");
            _ds.Display(vecSeasonReward, "vecSeasonReward");
            _ds.Display(mapTimeLimitSeasonTaskInfo, "mapTimeLimitSeasonTaskInfo");
        }

        public override void Clear()
        {
            iRet = 0;
            vecSeasonActiveTaskInfo = null;
            iTimestamp = 0;
            vecSeasonReward = null;
            mapTimeLimitSeasonTaskInfo = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGetSeasonTaskInfoRsp();
            copied.iRet = this.iRet;
            copied.vecSeasonActiveTaskInfo = (System.Collections.Generic.List<TSeasonActiveTaskInfo>)JceUtil.DeepClone(this.vecSeasonActiveTaskInfo);
            copied.iTimestamp = this.iTimestamp;
            copied.vecSeasonReward = (System.Collections.Generic.List<TSeasonReward_Server>)JceUtil.DeepClone(this.vecSeasonReward);
            copied.mapTimeLimitSeasonTaskInfo = (TKFrame.TKDictionary<int, System.Collections.Generic.List<TSeasonActiveTaskInfo>>)JceUtil.DeepClone(this.mapTimeLimitSeasonTaskInfo);
            return copied;
        }
    }
}

