// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class CardCollectCraftCardReq : Wup.Jce.JceStruct
    {
        public int cardID = 0;

        public int num = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(cardID, 0);
            _os.Write(num, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            cardID = (int) _is.Read(cardID, 0, false);

            num = (int) _is.Read(num, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(cardID, "cardID");
            _ds.Display(num, "num");
        }

        public override void Clear()
        {
            cardID = 0;
            num = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new CardCollectCraftCardReq();
            copied.cardID = this.cardID;
            copied.num = this.num;
            return copied;
        }
    }
}

