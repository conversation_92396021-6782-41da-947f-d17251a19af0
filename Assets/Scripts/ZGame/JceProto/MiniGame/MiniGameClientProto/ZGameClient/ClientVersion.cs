// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ClientVersion : Wup.Jce.JceStruct
    {
        public string appVersion = "";

        public string resVersion = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(appVersion, 0);
            _os.Write(resVersion, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            appVersion = (string) _is.Read(appVersion, 0, false);

            resVersion = (string) _is.Read(resVersion, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(appVersion, "appVersion");
            _ds.Display(resVersion, "resVersion");
        }

        public override void Clear()
        {
            appVersion = "";
            resVersion = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ClientVersion();
            copied.appVersion = this.appVersion;
            copied.resVersion = this.resVersion;
            return copied;
        }
    }
}

