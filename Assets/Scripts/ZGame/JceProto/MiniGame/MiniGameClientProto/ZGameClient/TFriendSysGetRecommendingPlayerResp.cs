// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TFriendSysGetRecommendingPlayerResp : Wup.Jce.JceStruct
    {
        int _nResultID = 0;
        public int nResultID
        {
            get
            {
                 return _nResultID;
            }
            set
            {
                _nResultID = value; 
            }
        }

        public System.Collections.Generic.List<TCommonFriendInfo> vec {get; set;} 

        string _algoType = "";
        public string algoType
        {
            get
            {
                 return _algoType;
            }
            set
            {
                _algoType = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(nResultID, 0);
            _os.Write(vec, 1);
            _os.Write(algoType, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            nResultID = (int) _is.Read(nResultID, 0, false);

            vec = (System.Collections.Generic.List<TCommonFriendInfo>) _is.Read(vec, 1, false);

            algoType = (string) _is.Read(algoType, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(nResultID, "nResultID");
            _ds.Display(vec, "vec");
            _ds.Display(algoType, "algoType");
        }

        public override void Clear()
        {
            nResultID = 0;
            vec = null;
            algoType = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TFriendSysGetRecommendingPlayerResp();
            copied.nResultID = this.nResultID;
            copied.vec = (System.Collections.Generic.List<TCommonFriendInfo>)JceUtil.DeepClone(this.vec);
            copied.algoType = this.algoType;
            return copied;
        }
    }
}

