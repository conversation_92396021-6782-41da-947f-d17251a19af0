// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_AutoMoveToBattleHero : Wup.Jce.JceStruct
    {
        public TAC_WaitHero stWaitHero {get; set;} 

        public TAC_BattleGroundPos stBattleGroundPos {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(stWaitHero, 0);
            _os.Write(stBattleGroundPos, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            stWaitHero = (TAC_WaitHero) _is.Read(stWaitHero, 0, false);

            stBattleGroundPos = (TAC_BattleGroundPos) _is.Read(stBattleGroundPos, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(stWaitHero, "stWaitHero");
            _ds.Display(stBattleGroundPos, "stBattleGroundPos");
        }

        public override void Clear()
        {
            stWaitHero = null;
            stBattleGroundPos = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_AutoMoveToBattleHero();
            copied.stWaitHero = (TAC_WaitHero)JceUtil.DeepClone(this.stWaitHero);
            copied.stBattleGroundPos = (TAC_BattleGroundPos)JceUtil.DeepClone(this.stBattleGroundPos);
            return copied;
        }
    }
}

