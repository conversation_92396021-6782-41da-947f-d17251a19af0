// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TJOCManualVoteReward_Server : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iManualID = 0;

        public int iVoteCount = 0;

        public int iItemID = 0;

        public int iItemCount = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iManualID, 1);
            _os.Write(iVoteCount, 2);
            _os.Write(iItemID, 3);
            _os.Write(iItemCount, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iManualID = (int) _is.Read(iManualID, 1, false);

            iVoteCount = (int) _is.Read(iVoteCount, 2, false);

            iItemID = (int) _is.Read(iItemID, 3, false);

            iItemCount = (int) _is.Read(iItemCount, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iManualID, "iManualID");
            _ds.Display(iVoteCount, "iVoteCount");
            _ds.Display(iItemID, "iItemID");
            _ds.Display(iItemCount, "iItemCount");
        }

        public override void Clear()
        {
            iID = 0;
            iManualID = 0;
            iVoteCount = 0;
            iItemID = 0;
            iItemCount = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TJOCManualVoteReward_Server();
            copied.iID = this.iID;
            copied.iManualID = this.iManualID;
            copied.iVoteCount = this.iVoteCount;
            copied.iItemID = this.iItemID;
            copied.iItemCount = this.iItemCount;
            return copied;
        }
    }
}

