//所在的Excel 【SceneType.xlsm】
//*********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_UserProfilePanel_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iModeID = 0;

        public string sDesc = "";

        public string sSeasonBelong = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iModeID, 1);
            _os.Write(sDesc, 2);
            _os.Write(sSeasonBelong, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iModeID = (int) _is.Read(iModeID, 1, false);

            sDesc = (string) _is.Read(sDesc, 2, false);

            sSeasonBelong = (string) _is.Read(sSeasonBelong, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iModeID, "iModeID");
            _ds.Display(sDesc, "sDesc");
            _ds.Display(sSeasonBelong, "sSeasonBelong");
        }

        public override void Clear()
        {
            iID = 0;
            iModeID = 0;
            sDesc = "";
            sSeasonBelong = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_UserProfilePanel_Client();
            copied.iID = this.iID;
            copied.iModeID = this.iModeID;
            copied.sDesc = this.sDesc;
            copied.sSeasonBelong = this.sSeasonBelong;
            return copied;
        }
    }
}

