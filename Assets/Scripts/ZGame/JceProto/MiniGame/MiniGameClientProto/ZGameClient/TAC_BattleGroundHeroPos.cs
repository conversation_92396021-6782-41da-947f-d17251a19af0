// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_BattleGroundHeroPos : Wup.Jce.JceStruct
    {
        public int iX = 0;

        public int iY = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iX, 0);
            _os.Write(iY, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iX = (int) _is.Read(iX, 0, false);

            iY = (int) _is.Read(iY, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iX, "iX");
            _ds.Display(iY, "iY");
        }

        public override void Clear()
        {
            iX = 0;
            iY = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_BattleGroundHeroPos();
            copied.iX = this.iX;
            copied.iY = this.iY;
            return copied;
        }
    }
}

