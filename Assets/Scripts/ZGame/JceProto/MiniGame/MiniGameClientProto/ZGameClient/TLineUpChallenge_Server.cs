// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TLineUpChallenge_Server : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iConsumeID = 0;

        public int iConsumeNum = 0;

        public int iFreeChallengeTimes = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iConsumeID, 1);
            _os.Write(iConsumeNum, 2);
            _os.Write(iFreeChallengeTimes, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iConsumeID = (int) _is.Read(iConsumeID, 1, false);

            iConsumeNum = (int) _is.Read(iConsumeNum, 2, false);

            iFreeChallengeTimes = (int) _is.Read(iFreeChallengeTimes, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iConsumeID, "iConsumeID");
            _ds.Display(iConsumeNum, "iConsumeNum");
            _ds.Display(iFreeChallengeTimes, "iFreeChallengeTimes");
        }

        public override void Clear()
        {
            iID = 0;
            iConsumeID = 0;
            iConsumeNum = 0;
            iFreeChallengeTimes = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TLineUpChallenge_Server();
            copied.iID = this.iID;
            copied.iConsumeID = this.iConsumeID;
            copied.iConsumeNum = this.iConsumeNum;
            copied.iFreeChallengeTimes = this.iFreeChallengeTimes;
            return copied;
        }
    }
}

