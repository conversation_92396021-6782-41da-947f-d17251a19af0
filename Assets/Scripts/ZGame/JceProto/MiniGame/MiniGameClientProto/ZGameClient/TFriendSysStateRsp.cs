// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TFriendSysStateRsp : Wup.Jce.JceStruct
    {
        int _nResultID = 0;
        public int nResultID
        {
            get
            {
                 return _nResultID;
            }
            set
            {
                _nResultID = value; 
            }
        }

        public System.Collections.Generic.List<TFriendStateInfo> vecInfo {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(nResultID, 0);
            _os.Write(vecInfo, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            nResultID = (int) _is.Read(nResultID, 0, false);

            vecInfo = (System.Collections.Generic.List<TFriendStateInfo>) _is.Read(vecInfo, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(nResultID, "nResultID");
            _ds.Display(vecInfo, "vecInfo");
        }

        public override void Clear()
        {
            nResultID = 0;
            vecInfo = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TFriendSysStateRsp();
            copied.nResultID = this.nResultID;
            copied.vecInfo = (System.Collections.Generic.List<TFriendStateInfo>)JceUtil.DeepClone(this.vecInfo);
            return copied;
        }
    }
}

