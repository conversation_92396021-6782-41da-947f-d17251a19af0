// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TCardScoreDefaultParam_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iReportType = 0;

        public float fCoefficient = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iReportType, 1);
            _os.Write(fCoefficient, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iReportType = (int) _is.Read(iReportType, 1, false);

            fCoefficient = (float) _is.Read(fCoefficient, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iReportType, "iReportType");
            _ds.Display(fCoefficient, "fCoefficient");
        }

        public override void Clear()
        {
            iID = 0;
            iReportType = 0;
            fCoefficient = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TCardScoreDefaultParam_Client();
            copied.iID = this.iID;
            copied.iReportType = this.iReportType;
            copied.fCoefficient = this.fCoefficient;
            return copied;
        }
    }
}

