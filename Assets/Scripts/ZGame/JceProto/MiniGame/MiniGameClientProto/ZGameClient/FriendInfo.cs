// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class FriendInfo : Wup.Jce.JceStruct
    {
        public string sIconUrl = "";

        public string sOpenPlatNickName = "";

        public int iZoneId = 0;

        public string sNickName = "";

        public int iTier = 0;

        public int iLvl = 0;

        public int iLastLoginTime = 0;

        public int tierBelongSceneType = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(sIconUrl, 0);
            _os.Write(sOpenPlatNickName, 1);
            _os.Write(iZoneId, 2);
            _os.Write(sNickName, 3);
            _os.Write(iTier, 4);
            _os.Write(iLvl, 5);
            _os.Write(iLastLoginTime, 6);
            _os.Write(tierBelongSceneType, 7);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            sIconUrl = (string) _is.Read(sIconUrl, 0, false);

            sOpenPlatNickName = (string) _is.Read(sOpenPlatNickName, 1, false);

            iZoneId = (int) _is.Read(iZoneId, 2, false);

            sNickName = (string) _is.Read(sNickName, 3, false);

            iTier = (int) _is.Read(iTier, 4, false);

            iLvl = (int) _is.Read(iLvl, 5, false);

            iLastLoginTime = (int) _is.Read(iLastLoginTime, 6, false);

            tierBelongSceneType = (int) _is.Read(tierBelongSceneType, 7, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(sIconUrl, "sIconUrl");
            _ds.Display(sOpenPlatNickName, "sOpenPlatNickName");
            _ds.Display(iZoneId, "iZoneId");
            _ds.Display(sNickName, "sNickName");
            _ds.Display(iTier, "iTier");
            _ds.Display(iLvl, "iLvl");
            _ds.Display(iLastLoginTime, "iLastLoginTime");
            _ds.Display(tierBelongSceneType, "tierBelongSceneType");
        }

        public override void Clear()
        {
            sIconUrl = "";
            sOpenPlatNickName = "";
            iZoneId = 0;
            sNickName = "";
            iTier = 0;
            iLvl = 0;
            iLastLoginTime = 0;
            tierBelongSceneType = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new FriendInfo();
            copied.sIconUrl = this.sIconUrl;
            copied.sOpenPlatNickName = this.sOpenPlatNickName;
            copied.iZoneId = this.iZoneId;
            copied.sNickName = this.sNickName;
            copied.iTier = this.iTier;
            copied.iLvl = this.iLvl;
            copied.iLastLoginTime = this.iLastLoginTime;
            copied.tierBelongSceneType = this.tierBelongSceneType;
            return copied;
        }
    }
}

