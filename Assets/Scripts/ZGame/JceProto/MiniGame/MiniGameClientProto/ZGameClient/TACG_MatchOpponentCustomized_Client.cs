//所在的Excel 【ACG_HeroCustomized.xlsm】
//************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_MatchOpponentCustomized_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iSceneID = 0;

        public int iStage = 0;

        public int iRound = 0;

        public string sMatchOpponent = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iSceneID, 1);
            _os.Write(iStage, 2);
            _os.Write(iRound, 3);
            _os.Write(sMatchOpponent, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iSceneID = (int) _is.Read(iSceneID, 1, false);

            iStage = (int) _is.Read(iStage, 2, false);

            iRound = (int) _is.Read(iRound, 3, false);

            sMatchOpponent = (string) _is.Read(sMatchOpponent, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iSceneID, "iSceneID");
            _ds.Display(iStage, "iStage");
            _ds.Display(iRound, "iRound");
            _ds.Display(sMatchOpponent, "sMatchOpponent");
        }

        public override void Clear()
        {
            iID = 0;
            iSceneID = 0;
            iStage = 0;
            iRound = 0;
            sMatchOpponent = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_MatchOpponentCustomized_Client();
            copied.iID = this.iID;
            copied.iSceneID = this.iSceneID;
            copied.iStage = this.iStage;
            copied.iRound = this.iRound;
            copied.sMatchOpponent = this.sMatchOpponent;
            return copied;
        }
    }
}

