// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class BeginBCRoguelikeRsp : Wup.Jce.JceStruct
    {
        public int err = 0;

        public int levelID = 0;

        public int gameModuleID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(err, 0);
            _os.Write(levelID, 1);
            _os.Write(gameModuleID, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            err = (int) _is.Read(err, 0, false);

            levelID = (int) _is.Read(levelID, 1, false);

            gameModuleID = (int) _is.Read(gameModuleID, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(err, "err");
            _ds.Display(levelID, "levelID");
            _ds.Display(gameModuleID, "gameModuleID");
        }

        public override void Clear()
        {
            err = 0;
            levelID = 0;
            gameModuleID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new BeginBCRoguelikeRsp();
            copied.err = this.err;
            copied.levelID = this.levelID;
            copied.gameModuleID = this.gameModuleID;
            return copied;
        }
    }
}

