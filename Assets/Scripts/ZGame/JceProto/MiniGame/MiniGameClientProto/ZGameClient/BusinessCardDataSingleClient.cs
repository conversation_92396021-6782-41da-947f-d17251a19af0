// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class BusinessCardDataSingleClient : Wup.Jce.JceStruct
    {
        public int iCurrentID = 0;

        public TKFrame.TKDictionary<int, BusinessCardRecordClient> mapRecord;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iCurrentID, 0);
            _os.Write(mapRecord, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iCurrentID = (int) _is.Read(iCurrentID, 0, false);

            mapRecord = (TKFrame.TKDictionary<int, BusinessCardRecordClient>) _is.Read(mapRecord, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iCurrentID, "iCurrentID");
            _ds.Display(mapRecord, "mapRecord");
        }

        public override void Clear()
        {
            iCurrentID = 0;
            mapRecord = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new BusinessCardDataSingleClient();
            copied.iCurrentID = this.iCurrentID;
            copied.mapRecord = (TKFrame.TKDictionary<int, BusinessCardRecordClient>)JceUtil.DeepClone(this.mapRecord);
            return copied;
        }
    }
}

