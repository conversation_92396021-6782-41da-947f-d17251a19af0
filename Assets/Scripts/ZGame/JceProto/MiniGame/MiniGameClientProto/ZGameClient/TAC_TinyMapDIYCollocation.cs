// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TinyMapDIYCollocation : Wup.Jce.JceStruct
    {
        public int iType = 0;

        public string sName = "";

        public TKFrame.TKDictionary<int, int> mapPartId;

        public int iOwnPartNum = 0;

        public int iTotalPartNum = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iType, 1);
            _os.Write(sName, 3);
            _os.Write(mapPartId, 4);
            _os.Write(iOwnPartNum, 5);
            _os.Write(iTotalPartNum, 6);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iType = (int) _is.Read(iType, 1, false);

            sName = (string) _is.Read(sName, 3, false);

            mapPartId = (TKFrame.TKDictionary<int, int>) _is.Read(mapPartId, 4, false);

            iOwnPartNum = (int) _is.Read(iOwnPartNum, 5, false);

            iTotalPartNum = (int) _is.Read(iTotalPartNum, 6, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iType, "iType");
            _ds.Display(sName, "sName");
            _ds.Display(mapPartId, "mapPartId");
            _ds.Display(iOwnPartNum, "iOwnPartNum");
            _ds.Display(iTotalPartNum, "iTotalPartNum");
        }

        public override void Clear()
        {
            iType = 0;
            sName = "";
            mapPartId = null;
            iOwnPartNum = 0;
            iTotalPartNum = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_TinyMapDIYCollocation();
            copied.iType = this.iType;
            copied.sName = this.sName;
            copied.mapPartId = (TKFrame.TKDictionary<int, int>)JceUtil.DeepClone(this.mapPartId);
            copied.iOwnPartNum = this.iOwnPartNum;
            copied.iTotalPartNum = this.iTotalPartNum;
            return copied;
        }
    }
}

