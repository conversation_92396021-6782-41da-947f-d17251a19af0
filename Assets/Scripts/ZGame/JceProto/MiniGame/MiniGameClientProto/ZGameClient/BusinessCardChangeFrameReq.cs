// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class BusinessCardChangeFrameReq : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string password = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(password, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            password = (string) _is.Read(password, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(password, "password");
        }

        public override void Clear()
        {
            iID = 0;
            password = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new BusinessCardChangeFrameReq();
            copied.iID = this.iID;
            copied.password = this.password;
            return copied;
        }
    }
}

