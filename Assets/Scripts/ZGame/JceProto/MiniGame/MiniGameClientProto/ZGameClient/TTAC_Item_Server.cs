// **********************************************************************
// This file was generated by a TAF parser!
// Generated from `SGameDBConfigProto.jce'
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_Item_Server : Wup.Jce.JceStruct
    {
        int _iID = 0;
        public int iID
        {
            get
            {
                 return _iID;
            }
            set
            {
                _iID = value; 
            }
        }

        string _sItemName = "";
        public string sItemName
        {
            get
            {
                 return _sItemName;
            }
            set
            {
                _sItemName = value; 
            }
        }

        string _sTypeName = "";
        public string sTypeName
        {
            get
            {
                 return _sTypeName;
            }
            set
            {
                _sTypeName = value; 
            }
        }

        string _sDesc = "";
        public string sDesc
        {
            get
            {
                 return _sDesc;
            }
            set
            {
                _sDesc = value; 
            }
        }

        int _iType = 0;
        public int iType
        {
            get
            {
                 return _iType;
            }
            set
            {
                _iType = value; 
            }
        }

        string _sIcon = "";
        public string sIcon
        {
            get
            {
                 return _sIcon;
            }
            set
            {
                _sIcon = value; 
            }
        }

        int _iLevel = 0;
        public int iLevel
        {
            get
            {
                 return _iLevel;
            }
            set
            {
                _iLevel = value; 
            }
        }

        int _iDropID = 0;
        public int iDropID
        {
            get
            {
                 return _iDropID;
            }
            set
            {
                _iDropID = value; 
            }
        }

        int _iVersion = 0;
        public int iVersion
        {
            get
            {
                 return _iVersion;
            }
            set
            {
                _iVersion = value; 
            }
        }

        string _sModel = "";
        public string sModel
        {
            get
            {
                 return _sModel;
            }
            set
            {
                _sModel = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sItemName, 1);
            _os.Write(sTypeName, 2);
            _os.Write(sDesc, 3);
            _os.Write(iType, 4);
            _os.Write(sIcon, 5);
            _os.Write(iLevel, 6);
            _os.Write(iDropID, 7);
            _os.Write(iVersion, 8);
            _os.Write(sModel, 9);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sItemName = (string) _is.Read(sItemName, 1, false);

            sTypeName = (string) _is.Read(sTypeName, 2, false);

            sDesc = (string) _is.Read(sDesc, 3, false);

            iType = (int) _is.Read(iType, 4, false);

            sIcon = (string) _is.Read(sIcon, 5, false);

            iLevel = (int) _is.Read(iLevel, 6, false);

            iDropID = (int) _is.Read(iDropID, 7, false);

            iVersion = (int) _is.Read(iVersion, 8, false);

            sModel = (string) _is.Read(sModel, 9, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sItemName, "sItemName");
            _ds.Display(sTypeName, "sTypeName");
            _ds.Display(sDesc, "sDesc");
            _ds.Display(iType, "iType");
            _ds.Display(sIcon, "sIcon");
            _ds.Display(iLevel, "iLevel");
            _ds.Display(iDropID, "iDropID");
            _ds.Display(iVersion, "iVersion");
            _ds.Display(sModel, "sModel");
        }

    }
}

