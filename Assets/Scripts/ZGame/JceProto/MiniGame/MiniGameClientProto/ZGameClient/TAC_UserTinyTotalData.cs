// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_UserTinyTotalData : Wup.Jce.JceStruct
    {
        public TAC_TinyData stTinyData;

        public TAC_TinyExpressionData stExpressionData;

        public TAC_TinyMapData stTinyMapData;

        public bool bIsTransferredToNew = false;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(stTinyData, 0);
            _os.Write(stExpressionData, 1);
            _os.Write(stTinyMapData, 2);
            _os.Write(bIsTransferredToNew, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            stTinyData = (TAC_TinyData) _is.Read(stTinyData, 0, false);

            stExpressionData = (TAC_TinyExpressionData) _is.Read(stExpressionData, 1, false);

            stTinyMapData = (TAC_TinyMapData) _is.Read(stTinyMapData, 2, false);

            bIsTransferredToNew = (bool) _is.Read(bIsTransferredToNew, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(stTinyData, "stTinyData");
            _ds.Display(stExpressionData, "stExpressionData");
            _ds.Display(stTinyMapData, "stTinyMapData");
            _ds.Display(bIsTransferredToNew, "bIsTransferredToNew");
        }

        public override void Clear()
        {
            stTinyData = null;
            stExpressionData = null;
            stTinyMapData = null;
            bIsTransferredToNew = false;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_UserTinyTotalData();
            copied.stTinyData = (TAC_TinyData)JceUtil.DeepClone(this.stTinyData);
            copied.stExpressionData = (TAC_TinyExpressionData)JceUtil.DeepClone(this.stExpressionData);
            copied.stTinyMapData = (TAC_TinyMapData)JceUtil.DeepClone(this.stTinyMapData);
            copied.bIsTransferredToNew = this.bIsTransferredToNew;
            return copied;
        }
    }
}

