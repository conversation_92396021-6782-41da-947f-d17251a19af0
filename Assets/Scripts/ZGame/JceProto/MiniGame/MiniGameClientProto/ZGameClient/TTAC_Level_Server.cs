// **********************************************************************
// This file was generated by a TAF parser!
// Generated from `SGameDBConfigProto.jce'
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_Level_Server : Wup.Jce.JceStruct
    {
        int _iID = 0;
        public int iID
        {
            get
            {
                 return _iID;
            }
            set
            {
                _iID = value; 
            }
        }

        int _iExp = 0;
        public int iExp
        {
            get
            {
                 return _iExp;
            }
            set
            {
                _iExp = value; 
            }
        }

        int _iWeight1 = 0;
        public int iWeight1
        {
            get
            {
                 return _iWeight1;
            }
            set
            {
                _iWeight1 = value; 
            }
        }

        int _iWeight2 = 0;
        public int iWeight2
        {
            get
            {
                 return _iWeight2;
            }
            set
            {
                _iWeight2 = value; 
            }
        }

        int _iWeight3 = 0;
        public int iWeight3
        {
            get
            {
                 return _iWeight3;
            }
            set
            {
                _iWeight3 = value; 
            }
        }

        int _iWeight4 = 0;
        public int iWeight4
        {
            get
            {
                 return _iWeight4;
            }
            set
            {
                _iWeight4 = value; 
            }
        }

        int _iWeight5 = 0;
        public int iWeight5
        {
            get
            {
                 return _iWeight5;
            }
            set
            {
                _iWeight5 = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iExp, 1);
            _os.Write(iWeight1, 2);
            _os.Write(iWeight2, 3);
            _os.Write(iWeight3, 4);
            _os.Write(iWeight4, 5);
            _os.Write(iWeight5, 6);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iExp = (int) _is.Read(iExp, 1, false);

            iWeight1 = (int) _is.Read(iWeight1, 2, false);

            iWeight2 = (int) _is.Read(iWeight2, 3, false);

            iWeight3 = (int) _is.Read(iWeight3, 4, false);

            iWeight4 = (int) _is.Read(iWeight4, 5, false);

            iWeight5 = (int) _is.Read(iWeight5, 6, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iExp, "iExp");
            _ds.Display(iWeight1, "iWeight1");
            _ds.Display(iWeight2, "iWeight2");
            _ds.Display(iWeight3, "iWeight3");
            _ds.Display(iWeight4, "iWeight4");
            _ds.Display(iWeight5, "iWeight5");
        }

    }
}

