//所在的Excel 【ACG_Hero.xlsm】
//**********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_Hero_Client : Wup.Jce.JceStruct
    {
        /// <summary>
        /// 索引ID
        /// </summary>
        public int iID = 0;

        /// <summary>
        /// 英雄名称
        /// </summary>
        public string sName = "";

        /// <summary>
        /// AI名称
        /// </summary>
        public string sAIName = "";

        /// <summary>
        /// 战斗延时前置时间
        /// </summary>
        public int iDelayLeadTime = 0;

        /// <summary>
        /// 战斗延时最小帧数
        /// </summary>
        public int iMinDelayFrame = 0;

        /// <summary>
        /// 战斗延时最大帧数
        /// </summary>
        public int iMaxDelayFrame = 0;

        /// <summary>
        /// 星级
        /// </summary>
        public int iStar = 0;

        /// <summary>
        /// 品质
        /// </summary>
        public int iQuality = 0;

        /// <summary>
        /// 描述
        /// </summary>
        public string sDesc = "";

        /// <summary>
        /// 种族
        /// </summary>
        public string sSpec = "";

        /// <summary>
        /// 职业
        /// </summary>
        public string sClass = "";

        /// <summary>
        /// 技能
        /// </summary>
        public string sSkill = "";

        /// <summary>
        /// 购买价格
        /// </summary>
        public int iCost = 0;

        /// <summary>
        /// 出售价格
        /// </summary>
        public int iPrice = 0;

        /// <summary>
        /// 测试用预制体ID
        /// </summary>
        public string sPrefabShowID = "";

        /// <summary>
        /// 预制体ID
        /// </summary>
        public string sPrefabID = "";

        /// <summary>
        /// 攻击力数值下限
        /// </summary>
        public int iProperty1 = 0;

        /// <summary>
        /// 攻击力数值上限
        /// </summary>
        public int iProperty2 = 0;

        /// <summary>
        /// 生命上限数值
        /// </summary>
        public int iProperty3 = 0;

        /// <summary>
        /// 魔法上限数值
        /// </summary>
        public int iProperty4 = 0;

        /// <summary>
        /// 魔法初始数值
        /// </summary>
        public int iProperty5 = 0;

        /// <summary>
        /// 攻击速度值数值
        /// </summary>
        public int iProperty6 = 0;

        /// <summary>
        /// 护甲数值
        /// </summary>
        public int iProperty7 = 0;

        /// <summary>
        /// 攻击距离数值
        /// </summary>
        public int iProperty8 = 0;

        /// <summary>
        /// 魔法抗性数值
        /// </summary>
        public int iProperty9 = 0;

        /// <summary>
        /// 闪避率数值
        /// </summary>
        public int iProperty10 = 0;

        /// <summary>
        /// 生命恢复数值
        /// </summary>
        public int iProperty11 = 0;

        /// <summary>
        /// 攻击魔法恢复数值
        /// </summary>
        public int iProperty12 = 0;

        /// <summary>
        /// 移动速度数值
        /// </summary>
        public int iProperty13 = 0;

        /// <summary>
        /// 合成英雄ID
        /// </summary>
        public int iNextHeroID = 0;

        /// <summary>
        /// 第二种族
        /// </summary>
        public int iSpec1 = 0;

        /// <summary>
        /// 基础暴击率
        /// </summary>
        public int iProperty15 = 0;

        /// <summary>
        /// 基础暴击倍数
        /// </summary>
        public int iProperty16 = 0;

        /// <summary>
        /// 预留
        /// </summary>
        public int iProperty19 = 0;

        /// <summary>
        /// 预留
        /// </summary>
        public int iProperty20 = 0;

        /// <summary>
        /// 种族羁绊技能
        /// </summary>
        public string sFetterSkill = "";

        /// <summary>
        /// 职业羁绊技能
        /// </summary>
        public string sClassFetterSkill = "";

        /// <summary>
        /// 英雄组
        /// </summary>
        public int iGroup = 0;

        /// <summary>
        /// 隐藏技能ID
        /// </summary>
        public int iHiddenSkill = 0;

        /// <summary>
        /// 是否显示图鉴
        /// </summary>
        public int iShowherotag = 0;

        /// <summary>
        /// 棋子类型
        /// </summary>
        public int iHeroType = 0;

        /// <summary>
        /// 标签
        /// </summary>
        public int iTag = 0;

        /// <summary>
        /// 英雄对应图
        /// </summary>
        public string sHeroPaint = "";

        /// <summary>
        /// 英雄小头像
        /// </summary>
        public string sHeroPaintSmall = "";

        /// <summary>
        /// 初始buff列表
        /// </summary>
        public string sEnterBuff = "";

        /// <summary>
        /// 模型比例
        /// </summary>
        public int iModelScale = 0;

        /// <summary>
        /// 变体绑定的原来ID
        /// </summary>
        public int iAttachOriginID = 0;

        /// <summary>
        /// 是否C位
        /// </summary>
        public int iHeroMapTag = 0;

        /// <summary>
        /// 版本
        /// </summary>
        public int iSetNum = 0;

        /// <summary>
        /// 英雄个人信息页对应图
        /// </summary>
        public string sHeroPaintShow = "";

        /// <summary>
        /// 特效缩放比例
        /// </summary>
        public int iEffectScale = 0;

        /// <summary>
        /// 购买扣除血量
        /// </summary>
        public int iCostLife = 0;

        /// <summary>
        /// 上场即召唤cfgid
        /// </summary>
        public int iSummonCfgId = 0;

        /// <summary>
        /// 英雄蓝条颜色
        /// </summary>
        public string sHeroBulletColor = "";

        /// <summary>
        /// 是否Boss
        /// </summary>
        public int iBoss = 0;

        /// <summary>
        /// 端游TFT英雄ID
        /// </summary>
        public int iTftHeroId = 0;

        /// <summary>
        /// 换形师配置
        /// </summary>
        public string sTransferConf = "";

        /// <summary>
        /// 占用人口个数
        /// </summary>
        public int iPeopleCnt = 0;

        /// <summary>
        /// 英雄类型组
        /// </summary>
        public int iHeroTypeGroup = 0;

        /// <summary>
        /// 羁绊计算个数
        /// </summary>
        public string sFetterConf = "";

        /// <summary>
        /// 能力类型标签
        /// </summary>
        public int iAbilityTag = 0;

        /// <summary>
        /// 特殊功能类型
        /// </summary>
        public int iMoreFunType = 0;

        /// <summary>
        /// 特殊功能类型Int参数
        /// </summary>
        public int iMoreFunIntParam = 0;

        /// <summary>
        /// 特殊功能类型string参数
        /// </summary>
        public string sMoreFunStrParam = "";

        /// <summary>
        /// 是否在判断战斗结束时被忽略计算
        /// </summary>
        public int iIsIgnoreInEndBattle = 0;

        /// <summary>
        /// 推荐装备（多个标签竖线分割）
        /// </summary>
        public string sEquipmentRecomm = "";

        /// <summary>
        /// 备战席被召唤上场特殊处理
        /// </summary>
        public string sSummonWaitHeroStrParam = "";

        /// <summary>
        /// 是否能被复活召唤
        /// </summary>
        public int iIsCanReborn = 0;

        /// <summary>
        /// 由羁绊等生成的固定装备(非池)
        /// </summary>
        public string sEquipmentGet = "";

        /// <summary>
        /// 攻击速度调整系数（万分比）
        /// </summary>
        public int iAttackSpeedRate = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sName, 1);
            _os.Write(sAIName, 2);
            _os.Write(iDelayLeadTime, 3);
            _os.Write(iMinDelayFrame, 4);
            _os.Write(iMaxDelayFrame, 5);
            _os.Write(iStar, 6);
            _os.Write(iQuality, 7);
            _os.Write(sDesc, 8);
            _os.Write(sSpec, 9);
            _os.Write(sClass, 10);
            _os.Write(sSkill, 11);
            _os.Write(iCost, 12);
            _os.Write(iPrice, 13);
            _os.Write(sPrefabShowID, 14);
            _os.Write(sPrefabID, 15);
            _os.Write(iProperty1, 16);
            _os.Write(iProperty2, 17);
            _os.Write(iProperty3, 18);
            _os.Write(iProperty4, 19);
            _os.Write(iProperty5, 20);
            _os.Write(iProperty6, 21);
            _os.Write(iProperty7, 22);
            _os.Write(iProperty8, 23);
            _os.Write(iProperty9, 24);
            _os.Write(iProperty10, 25);
            _os.Write(iProperty11, 26);
            _os.Write(iProperty12, 27);
            _os.Write(iProperty13, 28);
            _os.Write(iNextHeroID, 29);
            _os.Write(iSpec1, 30);
            _os.Write(iProperty15, 31);
            _os.Write(iProperty16, 32);
            _os.Write(iProperty19, 33);
            _os.Write(iProperty20, 34);
            _os.Write(sFetterSkill, 35);
            _os.Write(sClassFetterSkill, 37);
            _os.Write(iGroup, 38);
            _os.Write(iHiddenSkill, 40);
            _os.Write(iShowherotag, 45);
            _os.Write(iHeroType, 47);
            _os.Write(iTag, 48);
            _os.Write(sHeroPaint, 49);
            _os.Write(sHeroPaintSmall, 50);
            _os.Write(sEnterBuff, 51);
            _os.Write(iModelScale, 52);
            _os.Write(iAttachOriginID, 53);
            _os.Write(iHeroMapTag, 54);
            _os.Write(iSetNum, 55);
            _os.Write(sHeroPaintShow, 56);
            _os.Write(iEffectScale, 57);
            _os.Write(iCostLife, 58);
            _os.Write(iSummonCfgId, 59);
            _os.Write(sHeroBulletColor, 60);
            _os.Write(iBoss, 61);
            _os.Write(iTftHeroId, 62);
            _os.Write(sTransferConf, 63);
            _os.Write(iPeopleCnt, 64);
            _os.Write(iHeroTypeGroup, 66);
            _os.Write(sFetterConf, 67);
            _os.Write(iAbilityTag, 68);
            _os.Write(iMoreFunType, 69);
            _os.Write(iMoreFunIntParam, 70);
            _os.Write(sMoreFunStrParam, 71);
            _os.Write(iIsIgnoreInEndBattle, 72);
            _os.Write(sEquipmentRecomm, 73);
            _os.Write(sSummonWaitHeroStrParam, 74);
            _os.Write(iIsCanReborn, 75);
            _os.Write(sEquipmentGet, 76);
            _os.Write(iAttackSpeedRate, 77);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sName = (string) _is.Read(sName, 1, false);

            sAIName = (string) _is.Read(sAIName, 2, false);

            iDelayLeadTime = (int) _is.Read(iDelayLeadTime, 3, false);

            iMinDelayFrame = (int) _is.Read(iMinDelayFrame, 4, false);

            iMaxDelayFrame = (int) _is.Read(iMaxDelayFrame, 5, false);

            iStar = (int) _is.Read(iStar, 6, false);

            iQuality = (int) _is.Read(iQuality, 7, false);

            sDesc = (string) _is.Read(sDesc, 8, false);

            sSpec = (string) _is.Read(sSpec, 9, false);

            sClass = (string) _is.Read(sClass, 10, false);

            sSkill = (string) _is.Read(sSkill, 11, false);

            iCost = (int) _is.Read(iCost, 12, false);

            iPrice = (int) _is.Read(iPrice, 13, false);

            sPrefabShowID = (string) _is.Read(sPrefabShowID, 14, false);

            sPrefabID = (string) _is.Read(sPrefabID, 15, false);

            iProperty1 = (int) _is.Read(iProperty1, 16, false);

            iProperty2 = (int) _is.Read(iProperty2, 17, false);

            iProperty3 = (int) _is.Read(iProperty3, 18, false);

            iProperty4 = (int) _is.Read(iProperty4, 19, false);

            iProperty5 = (int) _is.Read(iProperty5, 20, false);

            iProperty6 = (int) _is.Read(iProperty6, 21, false);

            iProperty7 = (int) _is.Read(iProperty7, 22, false);

            iProperty8 = (int) _is.Read(iProperty8, 23, false);

            iProperty9 = (int) _is.Read(iProperty9, 24, false);

            iProperty10 = (int) _is.Read(iProperty10, 25, false);

            iProperty11 = (int) _is.Read(iProperty11, 26, false);

            iProperty12 = (int) _is.Read(iProperty12, 27, false);

            iProperty13 = (int) _is.Read(iProperty13, 28, false);

            iNextHeroID = (int) _is.Read(iNextHeroID, 29, false);

            iSpec1 = (int) _is.Read(iSpec1, 30, false);

            iProperty15 = (int) _is.Read(iProperty15, 31, false);

            iProperty16 = (int) _is.Read(iProperty16, 32, false);

            iProperty19 = (int) _is.Read(iProperty19, 33, false);

            iProperty20 = (int) _is.Read(iProperty20, 34, false);

            sFetterSkill = (string) _is.Read(sFetterSkill, 35, false);

            sClassFetterSkill = (string) _is.Read(sClassFetterSkill, 37, false);

            iGroup = (int) _is.Read(iGroup, 38, false);

            iHiddenSkill = (int) _is.Read(iHiddenSkill, 40, false);

            iShowherotag = (int) _is.Read(iShowherotag, 45, false);

            iHeroType = (int) _is.Read(iHeroType, 47, false);

            iTag = (int) _is.Read(iTag, 48, false);

            sHeroPaint = (string) _is.Read(sHeroPaint, 49, false);

            sHeroPaintSmall = (string) _is.Read(sHeroPaintSmall, 50, false);

            sEnterBuff = (string) _is.Read(sEnterBuff, 51, false);

            iModelScale = (int) _is.Read(iModelScale, 52, false);

            iAttachOriginID = (int) _is.Read(iAttachOriginID, 53, false);

            iHeroMapTag = (int) _is.Read(iHeroMapTag, 54, false);

            iSetNum = (int) _is.Read(iSetNum, 55, false);

            sHeroPaintShow = (string) _is.Read(sHeroPaintShow, 56, false);

            iEffectScale = (int) _is.Read(iEffectScale, 57, false);

            iCostLife = (int) _is.Read(iCostLife, 58, false);

            iSummonCfgId = (int) _is.Read(iSummonCfgId, 59, false);

            sHeroBulletColor = (string) _is.Read(sHeroBulletColor, 60, false);

            iBoss = (int) _is.Read(iBoss, 61, false);

            iTftHeroId = (int) _is.Read(iTftHeroId, 62, false);

            sTransferConf = (string) _is.Read(sTransferConf, 63, false);

            iPeopleCnt = (int) _is.Read(iPeopleCnt, 64, false);

            iHeroTypeGroup = (int) _is.Read(iHeroTypeGroup, 66, false);

            sFetterConf = (string) _is.Read(sFetterConf, 67, false);

            iAbilityTag = (int) _is.Read(iAbilityTag, 68, false);

            iMoreFunType = (int) _is.Read(iMoreFunType, 69, false);

            iMoreFunIntParam = (int) _is.Read(iMoreFunIntParam, 70, false);

            sMoreFunStrParam = (string) _is.Read(sMoreFunStrParam, 71, false);

            iIsIgnoreInEndBattle = (int) _is.Read(iIsIgnoreInEndBattle, 72, false);

            sEquipmentRecomm = (string) _is.Read(sEquipmentRecomm, 73, false);

            sSummonWaitHeroStrParam = (string) _is.Read(sSummonWaitHeroStrParam, 74, false);

            iIsCanReborn = (int) _is.Read(iIsCanReborn, 75, false);

            sEquipmentGet = (string) _is.Read(sEquipmentGet, 76, false);

            iAttackSpeedRate = (int) _is.Read(iAttackSpeedRate, 77, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sName, "sName");
            _ds.Display(sAIName, "sAIName");
            _ds.Display(iDelayLeadTime, "iDelayLeadTime");
            _ds.Display(iMinDelayFrame, "iMinDelayFrame");
            _ds.Display(iMaxDelayFrame, "iMaxDelayFrame");
            _ds.Display(iStar, "iStar");
            _ds.Display(iQuality, "iQuality");
            _ds.Display(sDesc, "sDesc");
            _ds.Display(sSpec, "sSpec");
            _ds.Display(sClass, "sClass");
            _ds.Display(sSkill, "sSkill");
            _ds.Display(iCost, "iCost");
            _ds.Display(iPrice, "iPrice");
            _ds.Display(sPrefabShowID, "sPrefabShowID");
            _ds.Display(sPrefabID, "sPrefabID");
            _ds.Display(iProperty1, "iProperty1");
            _ds.Display(iProperty2, "iProperty2");
            _ds.Display(iProperty3, "iProperty3");
            _ds.Display(iProperty4, "iProperty4");
            _ds.Display(iProperty5, "iProperty5");
            _ds.Display(iProperty6, "iProperty6");
            _ds.Display(iProperty7, "iProperty7");
            _ds.Display(iProperty8, "iProperty8");
            _ds.Display(iProperty9, "iProperty9");
            _ds.Display(iProperty10, "iProperty10");
            _ds.Display(iProperty11, "iProperty11");
            _ds.Display(iProperty12, "iProperty12");
            _ds.Display(iProperty13, "iProperty13");
            _ds.Display(iNextHeroID, "iNextHeroID");
            _ds.Display(iSpec1, "iSpec1");
            _ds.Display(iProperty15, "iProperty15");
            _ds.Display(iProperty16, "iProperty16");
            _ds.Display(iProperty19, "iProperty19");
            _ds.Display(iProperty20, "iProperty20");
            _ds.Display(sFetterSkill, "sFetterSkill");
            _ds.Display(sClassFetterSkill, "sClassFetterSkill");
            _ds.Display(iGroup, "iGroup");
            _ds.Display(iHiddenSkill, "iHiddenSkill");
            _ds.Display(iShowherotag, "iShowherotag");
            _ds.Display(iHeroType, "iHeroType");
            _ds.Display(iTag, "iTag");
            _ds.Display(sHeroPaint, "sHeroPaint");
            _ds.Display(sHeroPaintSmall, "sHeroPaintSmall");
            _ds.Display(sEnterBuff, "sEnterBuff");
            _ds.Display(iModelScale, "iModelScale");
            _ds.Display(iAttachOriginID, "iAttachOriginID");
            _ds.Display(iHeroMapTag, "iHeroMapTag");
            _ds.Display(iSetNum, "iSetNum");
            _ds.Display(sHeroPaintShow, "sHeroPaintShow");
            _ds.Display(iEffectScale, "iEffectScale");
            _ds.Display(iCostLife, "iCostLife");
            _ds.Display(iSummonCfgId, "iSummonCfgId");
            _ds.Display(sHeroBulletColor, "sHeroBulletColor");
            _ds.Display(iBoss, "iBoss");
            _ds.Display(iTftHeroId, "iTftHeroId");
            _ds.Display(sTransferConf, "sTransferConf");
            _ds.Display(iPeopleCnt, "iPeopleCnt");
            _ds.Display(iHeroTypeGroup, "iHeroTypeGroup");
            _ds.Display(sFetterConf, "sFetterConf");
            _ds.Display(iAbilityTag, "iAbilityTag");
            _ds.Display(iMoreFunType, "iMoreFunType");
            _ds.Display(iMoreFunIntParam, "iMoreFunIntParam");
            _ds.Display(sMoreFunStrParam, "sMoreFunStrParam");
            _ds.Display(iIsIgnoreInEndBattle, "iIsIgnoreInEndBattle");
            _ds.Display(sEquipmentRecomm, "sEquipmentRecomm");
            _ds.Display(sSummonWaitHeroStrParam, "sSummonWaitHeroStrParam");
            _ds.Display(iIsCanReborn, "iIsCanReborn");
            _ds.Display(sEquipmentGet, "sEquipmentGet");
            _ds.Display(iAttackSpeedRate, "iAttackSpeedRate");
        }

        public override void Clear()
        {
            iID = 0;
            sName = "";
            sAIName = "";
            iDelayLeadTime = 0;
            iMinDelayFrame = 0;
            iMaxDelayFrame = 0;
            iStar = 0;
            iQuality = 0;
            sDesc = "";
            sSpec = "";
            sClass = "";
            sSkill = "";
            iCost = 0;
            iPrice = 0;
            sPrefabShowID = "";
            sPrefabID = "";
            iProperty1 = 0;
            iProperty2 = 0;
            iProperty3 = 0;
            iProperty4 = 0;
            iProperty5 = 0;
            iProperty6 = 0;
            iProperty7 = 0;
            iProperty8 = 0;
            iProperty9 = 0;
            iProperty10 = 0;
            iProperty11 = 0;
            iProperty12 = 0;
            iProperty13 = 0;
            iNextHeroID = 0;
            iSpec1 = 0;
            iProperty15 = 0;
            iProperty16 = 0;
            iProperty19 = 0;
            iProperty20 = 0;
            sFetterSkill = "";
            sClassFetterSkill = "";
            iGroup = 0;
            iHiddenSkill = 0;
            iShowherotag = 0;
            iHeroType = 0;
            iTag = 0;
            sHeroPaint = "";
            sHeroPaintSmall = "";
            sEnterBuff = "";
            iModelScale = 0;
            iAttachOriginID = 0;
            iHeroMapTag = 0;
            iSetNum = 0;
            sHeroPaintShow = "";
            iEffectScale = 0;
            iCostLife = 0;
            iSummonCfgId = 0;
            sHeroBulletColor = "";
            iBoss = 0;
            iTftHeroId = 0;
            sTransferConf = "";
            iPeopleCnt = 0;
            iHeroTypeGroup = 0;
            sFetterConf = "";
            iAbilityTag = 0;
            iMoreFunType = 0;
            iMoreFunIntParam = 0;
            sMoreFunStrParam = "";
            iIsIgnoreInEndBattle = 0;
            sEquipmentRecomm = "";
            sSummonWaitHeroStrParam = "";
            iIsCanReborn = 0;
            sEquipmentGet = "";
            iAttackSpeedRate = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_Hero_Client();
            copied.iID = this.iID;
            copied.sName = this.sName;
            copied.sAIName = this.sAIName;
            copied.iDelayLeadTime = this.iDelayLeadTime;
            copied.iMinDelayFrame = this.iMinDelayFrame;
            copied.iMaxDelayFrame = this.iMaxDelayFrame;
            copied.iStar = this.iStar;
            copied.iQuality = this.iQuality;
            copied.sDesc = this.sDesc;
            copied.sSpec = this.sSpec;
            copied.sClass = this.sClass;
            copied.sSkill = this.sSkill;
            copied.iCost = this.iCost;
            copied.iPrice = this.iPrice;
            copied.sPrefabShowID = this.sPrefabShowID;
            copied.sPrefabID = this.sPrefabID;
            copied.iProperty1 = this.iProperty1;
            copied.iProperty2 = this.iProperty2;
            copied.iProperty3 = this.iProperty3;
            copied.iProperty4 = this.iProperty4;
            copied.iProperty5 = this.iProperty5;
            copied.iProperty6 = this.iProperty6;
            copied.iProperty7 = this.iProperty7;
            copied.iProperty8 = this.iProperty8;
            copied.iProperty9 = this.iProperty9;
            copied.iProperty10 = this.iProperty10;
            copied.iProperty11 = this.iProperty11;
            copied.iProperty12 = this.iProperty12;
            copied.iProperty13 = this.iProperty13;
            copied.iNextHeroID = this.iNextHeroID;
            copied.iSpec1 = this.iSpec1;
            copied.iProperty15 = this.iProperty15;
            copied.iProperty16 = this.iProperty16;
            copied.iProperty19 = this.iProperty19;
            copied.iProperty20 = this.iProperty20;
            copied.sFetterSkill = this.sFetterSkill;
            copied.sClassFetterSkill = this.sClassFetterSkill;
            copied.iGroup = this.iGroup;
            copied.iHiddenSkill = this.iHiddenSkill;
            copied.iShowherotag = this.iShowherotag;
            copied.iHeroType = this.iHeroType;
            copied.iTag = this.iTag;
            copied.sHeroPaint = this.sHeroPaint;
            copied.sHeroPaintSmall = this.sHeroPaintSmall;
            copied.sEnterBuff = this.sEnterBuff;
            copied.iModelScale = this.iModelScale;
            copied.iAttachOriginID = this.iAttachOriginID;
            copied.iHeroMapTag = this.iHeroMapTag;
            copied.iSetNum = this.iSetNum;
            copied.sHeroPaintShow = this.sHeroPaintShow;
            copied.iEffectScale = this.iEffectScale;
            copied.iCostLife = this.iCostLife;
            copied.iSummonCfgId = this.iSummonCfgId;
            copied.sHeroBulletColor = this.sHeroBulletColor;
            copied.iBoss = this.iBoss;
            copied.iTftHeroId = this.iTftHeroId;
            copied.sTransferConf = this.sTransferConf;
            copied.iPeopleCnt = this.iPeopleCnt;
            copied.iHeroTypeGroup = this.iHeroTypeGroup;
            copied.sFetterConf = this.sFetterConf;
            copied.iAbilityTag = this.iAbilityTag;
            copied.iMoreFunType = this.iMoreFunType;
            copied.iMoreFunIntParam = this.iMoreFunIntParam;
            copied.sMoreFunStrParam = this.sMoreFunStrParam;
            copied.iIsIgnoreInEndBattle = this.iIsIgnoreInEndBattle;
            copied.sEquipmentRecomm = this.sEquipmentRecomm;
            copied.sSummonWaitHeroStrParam = this.sSummonWaitHeroStrParam;
            copied.iIsCanReborn = this.iIsCanReborn;
            copied.sEquipmentGet = this.sEquipmentGet;
            copied.iAttackSpeedRate = this.iAttackSpeedRate;
            return copied;
        }
    }
}

