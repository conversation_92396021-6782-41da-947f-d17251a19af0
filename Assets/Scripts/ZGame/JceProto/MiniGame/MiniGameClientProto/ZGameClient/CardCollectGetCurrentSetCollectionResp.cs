// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class CardCollectGetCurrentSetCollectionResp : Wup.Jce.JceStruct
    {
        public int err = 0;

        public int currentSetID = 0;

        public TKFrame.TKDictionary<int, CardData> cardDataPerHero;

        public System.Collections.Generic.List<int> packIDs;

        public int currentPackID = 0;

        public TKFrame.TKDictionary<int, bool> historySetIsRed;

        public bool heroSkinIsRed = true;

        public MultiCardsDataToChoose multiCardsDataToChoose;

        public int giftPackID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(err, 0);
            _os.Write(currentSetID, 6);
            _os.Write(cardDataPerHero, 9);
            _os.Write(packIDs, 10);
            _os.Write(currentPackID, 12);
            _os.Write(historySetIsRed, 14);
            _os.Write(heroSkinIsRed, 15);
            _os.Write(multiCardsDataToChoose, 16);
            _os.Write(giftPackID, 17);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            err = (int) _is.Read(err, 0, false);

            currentSetID = (int) _is.Read(currentSetID, 6, false);

            cardDataPerHero = (TKFrame.TKDictionary<int, CardData>) _is.Read(cardDataPerHero, 9, false);

            packIDs = (System.Collections.Generic.List<int>) _is.Read(packIDs, 10, false);

            currentPackID = (int) _is.Read(currentPackID, 12, false);

            historySetIsRed = (TKFrame.TKDictionary<int, bool>) _is.Read(historySetIsRed, 14, false);

            heroSkinIsRed = (bool) _is.Read(heroSkinIsRed, 15, false);

            multiCardsDataToChoose = (MultiCardsDataToChoose) _is.Read(multiCardsDataToChoose, 16, false);

            giftPackID = (int) _is.Read(giftPackID, 17, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(err, "err");
            _ds.Display(currentSetID, "currentSetID");
            _ds.Display(cardDataPerHero, "cardDataPerHero");
            _ds.Display(packIDs, "packIDs");
            _ds.Display(currentPackID, "currentPackID");
            _ds.Display(historySetIsRed, "historySetIsRed");
            _ds.Display(heroSkinIsRed, "heroSkinIsRed");
            _ds.Display(multiCardsDataToChoose, "multiCardsDataToChoose");
            _ds.Display(giftPackID, "giftPackID");
        }

        public override void Clear()
        {
            err = 0;
            currentSetID = 0;
            cardDataPerHero = null;
            packIDs = null;
            currentPackID = 0;
            historySetIsRed = null;
            heroSkinIsRed = true;
            multiCardsDataToChoose = null;
            giftPackID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new CardCollectGetCurrentSetCollectionResp();
            copied.err = this.err;
            copied.currentSetID = this.currentSetID;
            copied.cardDataPerHero = (TKFrame.TKDictionary<int, CardData>)JceUtil.DeepClone(this.cardDataPerHero);
            copied.packIDs = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.packIDs);
            copied.currentPackID = this.currentPackID;
            copied.historySetIsRed = (TKFrame.TKDictionary<int, bool>)JceUtil.DeepClone(this.historySetIsRed);
            copied.heroSkinIsRed = this.heroSkinIsRed;
            copied.multiCardsDataToChoose = (MultiCardsDataToChoose)JceUtil.DeepClone(this.multiCardsDataToChoose);
            copied.giftPackID = this.giftPackID;
            return copied;
        }
    }
}

