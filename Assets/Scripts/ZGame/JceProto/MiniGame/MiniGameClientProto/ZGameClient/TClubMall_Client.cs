//所在的Excel 【Club.xlsm】
//**************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TClubMall_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iItemId = 0;

        public int iSellCount = 0;

        public string sDesc = "";

        public string sPayment = "";

        public int iLimitType = 0;

        public int iLimitCount = 0;

        public string sBeginTime = "";

        public int iBeginTime = 0;

        public string sEndTime = "";

        public int iEndTime = 0;

        public int iDiscountValue = 0;

        public string sDiscountBeginTime = "";

        public int iDiscountBeginTime = 0;

        public string sDiscountEndTime = "";

        public int iDiscountEndTime = 0;

        public int iWeight = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iItemId, 1);
            _os.Write(iSellCount, 2);
            _os.Write(sDesc, 3);
            _os.Write(sPayment, 4);
            _os.Write(iLimitType, 5);
            _os.Write(iLimitCount, 6);
            _os.Write(sBeginTime, 7);
            _os.Write(iBeginTime, 8);
            _os.Write(sEndTime, 9);
            _os.Write(iEndTime, 10);
            _os.Write(iDiscountValue, 11);
            _os.Write(sDiscountBeginTime, 12);
            _os.Write(iDiscountBeginTime, 13);
            _os.Write(sDiscountEndTime, 14);
            _os.Write(iDiscountEndTime, 15);
            _os.Write(iWeight, 16);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iItemId = (int) _is.Read(iItemId, 1, false);

            iSellCount = (int) _is.Read(iSellCount, 2, false);

            sDesc = (string) _is.Read(sDesc, 3, false);

            sPayment = (string) _is.Read(sPayment, 4, false);

            iLimitType = (int) _is.Read(iLimitType, 5, false);

            iLimitCount = (int) _is.Read(iLimitCount, 6, false);

            sBeginTime = (string) _is.Read(sBeginTime, 7, false);

            iBeginTime = (int) _is.Read(iBeginTime, 8, false);

            sEndTime = (string) _is.Read(sEndTime, 9, false);

            iEndTime = (int) _is.Read(iEndTime, 10, false);

            iDiscountValue = (int) _is.Read(iDiscountValue, 11, false);

            sDiscountBeginTime = (string) _is.Read(sDiscountBeginTime, 12, false);

            iDiscountBeginTime = (int) _is.Read(iDiscountBeginTime, 13, false);

            sDiscountEndTime = (string) _is.Read(sDiscountEndTime, 14, false);

            iDiscountEndTime = (int) _is.Read(iDiscountEndTime, 15, false);

            iWeight = (int) _is.Read(iWeight, 16, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iItemId, "iItemId");
            _ds.Display(iSellCount, "iSellCount");
            _ds.Display(sDesc, "sDesc");
            _ds.Display(sPayment, "sPayment");
            _ds.Display(iLimitType, "iLimitType");
            _ds.Display(iLimitCount, "iLimitCount");
            _ds.Display(sBeginTime, "sBeginTime");
            _ds.Display(iBeginTime, "iBeginTime");
            _ds.Display(sEndTime, "sEndTime");
            _ds.Display(iEndTime, "iEndTime");
            _ds.Display(iDiscountValue, "iDiscountValue");
            _ds.Display(sDiscountBeginTime, "sDiscountBeginTime");
            _ds.Display(iDiscountBeginTime, "iDiscountBeginTime");
            _ds.Display(sDiscountEndTime, "sDiscountEndTime");
            _ds.Display(iDiscountEndTime, "iDiscountEndTime");
            _ds.Display(iWeight, "iWeight");
        }

        public override void Clear()
        {
            iID = 0;
            iItemId = 0;
            iSellCount = 0;
            sDesc = "";
            sPayment = "";
            iLimitType = 0;
            iLimitCount = 0;
            sBeginTime = "";
            iBeginTime = 0;
            sEndTime = "";
            iEndTime = 0;
            iDiscountValue = 0;
            sDiscountBeginTime = "";
            iDiscountBeginTime = 0;
            sDiscountEndTime = "";
            iDiscountEndTime = 0;
            iWeight = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TClubMall_Client();
            copied.iID = this.iID;
            copied.iItemId = this.iItemId;
            copied.iSellCount = this.iSellCount;
            copied.sDesc = this.sDesc;
            copied.sPayment = this.sPayment;
            copied.iLimitType = this.iLimitType;
            copied.iLimitCount = this.iLimitCount;
            copied.sBeginTime = this.sBeginTime;
            copied.iBeginTime = this.iBeginTime;
            copied.sEndTime = this.sEndTime;
            copied.iEndTime = this.iEndTime;
            copied.iDiscountValue = this.iDiscountValue;
            copied.sDiscountBeginTime = this.sDiscountBeginTime;
            copied.iDiscountBeginTime = this.iDiscountBeginTime;
            copied.sDiscountEndTime = this.sDiscountEndTime;
            copied.iDiscountEndTime = this.iDiscountEndTime;
            copied.iWeight = this.iWeight;
            return copied;
        }
    }
}

