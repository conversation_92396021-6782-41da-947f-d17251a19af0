// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class NoReplacementItem : Wup.Jce.JceStruct
    {
        public int uniqueID = 0;

        public int itemID = 0;

        public int itemNum = 0;

        public bool isTerminalItem = false;

        public bool isTopItem = false;

        public bool isOwnPreItem = true;

        public bool isEnableAdded = true;

        public int dropLimit = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(uniqueID, 0);
            _os.Write(itemID, 1);
            _os.Write(itemNum, 2);
            _os.Write(isTerminalItem, 3);
            _os.Write(isTopItem, 4);
            _os.Write(isOwnPreItem, 6);
            _os.Write(isEnableAdded, 7);
            _os.Write(dropLimit, 8);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            uniqueID = (int) _is.Read(uniqueID, 0, false);

            itemID = (int) _is.Read(itemID, 1, false);

            itemNum = (int) _is.Read(itemNum, 2, false);

            isTerminalItem = (bool) _is.Read(isTerminalItem, 3, false);

            isTopItem = (bool) _is.Read(isTopItem, 4, false);

            isOwnPreItem = (bool) _is.Read(isOwnPreItem, 6, false);

            isEnableAdded = (bool) _is.Read(isEnableAdded, 7, false);

            dropLimit = (int) _is.Read(dropLimit, 8, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(uniqueID, "uniqueID");
            _ds.Display(itemID, "itemID");
            _ds.Display(itemNum, "itemNum");
            _ds.Display(isTerminalItem, "isTerminalItem");
            _ds.Display(isTopItem, "isTopItem");
            _ds.Display(isOwnPreItem, "isOwnPreItem");
            _ds.Display(isEnableAdded, "isEnableAdded");
            _ds.Display(dropLimit, "dropLimit");
        }

        public override void Clear()
        {
            uniqueID = 0;
            itemID = 0;
            itemNum = 0;
            isTerminalItem = false;
            isTopItem = false;
            isOwnPreItem = true;
            isEnableAdded = true;
            dropLimit = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new NoReplacementItem();
            copied.uniqueID = this.uniqueID;
            copied.itemID = this.itemID;
            copied.itemNum = this.itemNum;
            copied.isTerminalItem = this.isTerminalItem;
            copied.isTopItem = this.isTopItem;
            copied.isOwnPreItem = this.isOwnPreItem;
            copied.isEnableAdded = this.isEnableAdded;
            copied.dropLimit = this.dropLimit;
            return copied;
        }
    }
}

