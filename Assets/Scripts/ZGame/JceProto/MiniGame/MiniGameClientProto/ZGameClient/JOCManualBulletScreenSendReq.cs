// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class JOCManualBulletScreenSendReq : Wup.Jce.JceStruct
    {
        public int bulletScreenID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(bulletScreenID, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            bulletScreenID = (int) _is.Read(bulletScreenID, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(bulletScreenID, "bulletScreenID");
        }

        public override void Clear()
        {
            bulletScreenID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new JOCManualBulletScreenSendReq();
            copied.bulletScreenID = this.bulletScreenID;
            return copied;
        }
    }
}

