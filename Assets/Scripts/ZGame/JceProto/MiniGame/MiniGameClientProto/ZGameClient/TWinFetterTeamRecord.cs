// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TWinFetterTeamRecord : Wup.Jce.JceStruct
    {
        public System.Collections.Generic.List<TWinFetterInfo> vecWinFetterInfo;

        public int iWinCount = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(vecWinFetterInfo, 0);
            _os.Write(iWinCount, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            vecWinFetterInfo = (System.Collections.Generic.List<TWinFetterInfo>) _is.Read(vecWinFetterInfo, 0, false);

            iWinCount = (int) _is.Read(iWinCount, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(vecWinFetterInfo, "vecWinFetterInfo");
            _ds.Display(iWinCount, "iWinCount");
        }

        public override void Clear()
        {
            vecWinFetterInfo = null;
            iWinCount = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TWinFetterTeamRecord();
            copied.vecWinFetterInfo = (System.Collections.Generic.List<TWinFetterInfo>)JceUtil.DeepClone(this.vecWinFetterInfo);
            copied.iWinCount = this.iWinCount;
            return copied;
        }
    }
}

