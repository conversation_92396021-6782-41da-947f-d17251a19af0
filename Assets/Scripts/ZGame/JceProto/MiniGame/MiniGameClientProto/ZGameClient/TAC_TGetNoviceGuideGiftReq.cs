// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TGetNoviceGuideGiftReq : Wup.Jce.JceStruct
    {
        int _iType = 0;
        public int iType
        {
            get
            {
                 return _iType;
            }
            set
            {
                _iType = value; 
            }
        }

        public TMidasTokenInfo stMidasTokenInfo {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iType, 0);
            _os.Write(stMidasTokenInfo, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iType = (int) _is.Read(iType, 0, false);

            stMidasTokenInfo = (TMidasTokenInfo) _is.Read(stMidasTokenInfo, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iType, "iType");
            _ds.Display(stMidasTokenInfo, "stMidasTokenInfo");
        }

    }
}

