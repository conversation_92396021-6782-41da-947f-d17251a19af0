// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TModuleSwitch_Server : Wup.Jce.JceStruct
    {
        int _iID = 0;
        public int iID
        {
            get
            {
                 return _iID;
            }
            set
            {
                _iID = value; 
            }
        }

        string _sCloseBeginTime = "";
        public string sCloseBeginTime
        {
            get
            {
                 return _sCloseBeginTime;
            }
            set
            {
                _sCloseBeginTime = value; 
            }
        }

        string _sCloseEndTime = "";
        public string sCloseEndTime
        {
            get
            {
                 return _sCloseEndTime;
            }
            set
            {
                _sCloseEndTime = value; 
            }
        }

        int _iServerID = 0;
        public int iServerID
        {
            get
            {
                 return _iServerID;
            }
            set
            {
                _iServerID = value; 
            }
        }

        int _iType = 0;
        public int iType
        {
            get
            {
                 return _iType;
            }
            set
            {
                _iType = value; 
            }
        }

        int _iTierLimitMin = 0;
        public int iTierLimitMin
        {
            get
            {
                 return _iTierLimitMin;
            }
            set
            {
                _iTierLimitMin = value; 
            }
        }

        int _iTierLimitMax = 0;
        public int iTierLimitMax
        {
            get
            {
                 return _iTierLimitMax;
            }
            set
            {
                _iTierLimitMax = value; 
            }
        }

        int _iLevelLimit = 0;
        public int iLevelLimit
        {
            get
            {
                 return _iLevelLimit;
            }
            set
            {
                _iLevelLimit = value; 
            }
        }

        string _sTips = "";
        public string sTips
        {
            get
            {
                 return _sTips;
            }
            set
            {
                _sTips = value; 
            }
        }

        int _iUserLabel = 0;
        public int iUserLabel
        {
            get
            {
                 return _iUserLabel;
            }
            set
            {
                _iUserLabel = value; 
            }
        }

        int _iUnLockLevel = 0;
        public int iUnLockLevel
        {
            get
            {
                 return _iUnLockLevel;
            }
            set
            {
                _iUnLockLevel = value; 
            }
        }

        int _iUnLockTipStatus = 0;
        public int iUnLockTipStatus
        {
            get
            {
                 return _iUnLockTipStatus;
            }
            set
            {
                _iUnLockTipStatus = value; 
            }
        }

        string _sIcon = "";
        public string sIcon
        {
            get
            {
                 return _sIcon;
            }
            set
            {
                _sIcon = value; 
            }
        }

        string _sName = "";
        public string sName
        {
            get
            {
                 return _sName;
            }
            set
            {
                _sName = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sCloseBeginTime, 1);
            _os.Write(sCloseEndTime, 2);
            _os.Write(iServerID, 3);
            _os.Write(iType, 4);
            _os.Write(iTierLimitMin, 5);
            _os.Write(iTierLimitMax, 6);
            _os.Write(iLevelLimit, 7);
            _os.Write(sTips, 8);
            _os.Write(iUserLabel, 9);
            _os.Write(iUnLockLevel, 10);
            _os.Write(iUnLockTipStatus, 11);
            _os.Write(sIcon, 12);
            _os.Write(sName, 13);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sCloseBeginTime = (string) _is.Read(sCloseBeginTime, 1, false);

            sCloseEndTime = (string) _is.Read(sCloseEndTime, 2, false);

            iServerID = (int) _is.Read(iServerID, 3, false);

            iType = (int) _is.Read(iType, 4, false);

            iTierLimitMin = (int) _is.Read(iTierLimitMin, 5, false);

            iTierLimitMax = (int) _is.Read(iTierLimitMax, 6, false);

            iLevelLimit = (int) _is.Read(iLevelLimit, 7, false);

            sTips = (string) _is.Read(sTips, 8, false);

            iUserLabel = (int) _is.Read(iUserLabel, 9, false);

            iUnLockLevel = (int) _is.Read(iUnLockLevel, 10, false);

            iUnLockTipStatus = (int) _is.Read(iUnLockTipStatus, 11, false);

            sIcon = (string) _is.Read(sIcon, 12, false);

            sName = (string) _is.Read(sName, 13, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sCloseBeginTime, "sCloseBeginTime");
            _ds.Display(sCloseEndTime, "sCloseEndTime");
            _ds.Display(iServerID, "iServerID");
            _ds.Display(iType, "iType");
            _ds.Display(iTierLimitMin, "iTierLimitMin");
            _ds.Display(iTierLimitMax, "iTierLimitMax");
            _ds.Display(iLevelLimit, "iLevelLimit");
            _ds.Display(sTips, "sTips");
            _ds.Display(iUserLabel, "iUserLabel");
            _ds.Display(iUnLockLevel, "iUnLockLevel");
            _ds.Display(iUnLockTipStatus, "iUnLockTipStatus");
            _ds.Display(sIcon, "sIcon");
            _ds.Display(sName, "sName");
        }

        public override void Clear()
        {
            iID = 0;
            sCloseBeginTime = "";
            sCloseEndTime = "";
            iServerID = 0;
            iType = 0;
            iTierLimitMin = 0;
            iTierLimitMax = 0;
            iLevelLimit = 0;
            sTips = "";
            iUserLabel = 0;
            iUnLockLevel = 0;
            iUnLockTipStatus = 0;
            sIcon = "";
            sName = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TModuleSwitch_Server();
            copied.iID = this.iID;
            copied.sCloseBeginTime = this.sCloseBeginTime;
            copied.sCloseEndTime = this.sCloseEndTime;
            copied.iServerID = this.iServerID;
            copied.iType = this.iType;
            copied.iTierLimitMin = this.iTierLimitMin;
            copied.iTierLimitMax = this.iTierLimitMax;
            copied.iLevelLimit = this.iLevelLimit;
            copied.sTips = this.sTips;
            copied.iUserLabel = this.iUserLabel;
            copied.iUnLockLevel = this.iUnLockLevel;
            copied.iUnLockTipStatus = this.iUnLockTipStatus;
            copied.sIcon = this.sIcon;
            copied.sName = this.sName;
            return copied;
        }
    }
}

