using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading;
using DG.Tweening;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Constraint = UnityEngine.UI.GridLayoutGroup.Constraint;

namespace TKFrame
{
    public partial class CommonUtil
    {
        //递归查找子节点
        public static GameObject GetChild(Transform root, string name)
        {
            Transform node = FindTransform(root, name);
            if (node != null)
            {
                return node.gameObject;
            }
            return null;
        }

        //递归查找子节点
        public static Transform FindTransform(Transform root, string name)
        {
            Transform dt = root.Find(name);
            if (null != dt)
            {
                return dt;
            }
            else
            {
                foreach (Transform child in root)
                {
                    dt = FindTransform(child, name);
                    if (dt)
                    {
                        return dt;
                    }
                }
            }
            return null;
        }

        /// <summary>
        /// 野怪命名   m_****_1_show
        /// 英雄      [s*_]h_****_1_show   [s*_]为s1时省略了.....要做恶心的兼容
        /// 召唤物    毫无规则 
        /// </summary>
        /// <param name="prefabName"></param>
        /// <param name="abPath"></param>
        /// <param name="assetName"></param>
        /// <returns></returns>
        public static void GetHeroLLodMeshData(string prefabName, out string abPath, out string assetName)
        {
            string name = prefabName.Substring(0, prefabName.Length - "_1_show(Clone)".Length);
            // 兼容 s1的模型不带s1前缀
            string set = prefabName.Substring(1,1);
            if(prefabName.Substring(0,1) == "h")
                set = "1";
            abPath = string.Format("art_tft_raw/model_res/hero/set{0}/{1}/lv1", set, name);
            assetName = string.Format("{0}_1-lod_l", name);
        }
        
        
        public static void RemoveEvent<T>(ref T dele, T fucntion) where  T:System.Delegate
        {
            dele = (T)Delegate.Remove(dele, fucntion);
        }
    
        public static void AddEvent<T>(ref T dele, T fucntion) where  T:System.Delegate
        {
            dele = (T)Delegate.Remove(dele, fucntion);
            dele = (T)Delegate.Combine(dele, fucntion);
        }

        public static char OnMyValidateInput(string text, int charIndex, char addedChar)
        {
            //屏蔽输入法的Emoji输入
            if (char.GetUnicodeCategory(addedChar) == System.Globalization.UnicodeCategory.Surrogate)
            {
                return '\0';
            }
            return addedChar;
        }

        public static void ComPareExchange<T>(ref T location1, T value, T comparand) where  T:class
        {
            Interlocked.CompareExchange<T>(ref location1, value, comparand);
        }
        
        //递归查找子节点的组件
        public static T GetChildComponent<T>(Transform root, string name) where T : Component
        {
            Transform node = FindTransform(root, name);
            if (node == null)
            {
                return null;
            }

            GameObject obj = node.gameObject;

            if (obj)
            {
                return obj.GetComponent<T>();
            }
            else
            {
                return null;
            }
        }

        //重置Transform
        public static void ResetTransform(Transform node)
        {
            node.localPosition = Vector3.zero;
            node.localRotation = Quaternion.identity;
            node.localScale = Vector3.one;
        }

        //递归设置物体的layer
        public static void SetLayerRecursive(GameObject obj, int layer)
        {
            obj.layer = layer;
            foreach (Transform child in obj.transform)
            {
                SetLayerRecursive(child.gameObject, layer);
            }
        }

        //递归设置物体的layer: 若layer等于changeLayer，则修改为targetLayer；否则不变
        public static void SetLayerRecursive(GameObject obj, int targetLayer, int changeLayer)
        {
            obj.layer = obj.layer == changeLayer ? targetLayer : obj.layer;
            foreach (Transform child in obj.transform)
            {
                SetLayerRecursive(child.gameObject, targetLayer, changeLayer);
            }
        }

        //递归设置物体的layer: 若layer等于changeLayer，则修改为targetLayer；否则不变
        public static void SetLayerRecursive(GameObject obj, int targetLayer1, int changeLayer1, int targetLayer2, int changeLayer2)
        {
            obj.layer = obj.layer == changeLayer1 ? targetLayer1 : obj.layer == changeLayer2 ? targetLayer2 : obj.layer;
            foreach (Transform child in obj.transform)
            {
                SetLayerRecursive(child.gameObject, targetLayer1, changeLayer1, targetLayer2, changeLayer2);
            }
        }

        //删除所有的子节点
        public static void DestroyChildren(Transform root)
        {
            foreach (Transform child in root)
            {
                if (child.gameObject)
                    GameObject.Destroy(child.gameObject);
            }
        }

        public static void CopyTransform(Transform obj, Transform other)
        {
            obj.localPosition = other.localPosition;
            obj.localRotation = other.localRotation;
            obj.localScale = other.localScale;
        }

        public static int[] SplitInteger(int num)
        {
            List<int> bit_num_list = new List<int>();
            while (num > 0)
            {
                bit_num_list.Add(num % 10);
                num = num / 10;
            }
            return bit_num_list.ToArray();
        }

        public static T AddMissComponent<T>(GameObject go) where T : Component {
            T t = go.GetComponent<T>();
            if (t == null) {
                t = go.AddComponent<T>();
            }
            return t;
        }

#if ACGGAME_CLIENT
		  public static void NoDevelop(string msg) {
            UIOverlay.Instance.ShowCommonTips("未开发: "+msg, true);
        }
#endif

        public delegate void ActionBoolIntHandler(bool param1, int param2);
        public delegate void ActionIntHandler(int param1);
        /// <summary>
        /// 设置数据用于循环列表;
        /// </summary>
        /// <param name="dataCount">数据量</param>
        /// <param name="preItemCount">之前的item数量</param>
        /// <param name="setItemDataCallback">isNeedCreateNewItem, dataIndex</param>
        /// <param name="setInVisibleItem">itemIndex</param>
        public static void SetDataForLoopList(int dataCount, int preItemCount, ActionBoolIntHandler setItemDataCallback, ActionIntHandler setInVisibleItem)
        {
            for (int i = 0, len = dataCount; i < len; i++)
            {
                bool isNeedCreateNewItem = i + 1 > preItemCount;
                setItemDataCallback(isNeedCreateNewItem, i);
            }
            //将多余的item调用invisiblecallback;
            for (int i = dataCount; i < preItemCount; i++)
            {
                setInVisibleItem(i);
            }
        }

        /// <summary>
        /// 实例化并设置parent;
        /// </summary>
        /// <param name="prefabGo"></param>
        /// <returns></returns>
        public static GameObject InstantiateGoAndSetParent(GameObject prefabGo, Transform cursomParent = null) {
            GameObject tmpGo = GameObject.Instantiate(prefabGo);
            Transform trans = tmpGo.transform;
            trans.SetParent(cursomParent == null ? prefabGo.transform.parent: cursomParent);
            trans.localPosition = Vector3.zero;
            trans.localEulerAngles = Vector3.zero;
            trans.localScale = Vector3.one;
            return tmpGo;
        }

        public static Vector2Int ResizeGridlayoutCellSizeAdjustViewPort(Vector2 cellSize, Vector2 padding, float viewWidth, float viewHeight, Constraint constraintType, int constraintCount){
            //先获取viewPort的宽高;
            Vector2 viewPortWh = new Vector2(viewWidth, viewHeight);
            //获取原有的cellSize;
            Vector2 preCellSize = cellSize;
            Vector2 prePadding = padding;
            int tmpCount = constraintCount;
            Vector2Int rtnVec2Int = Vector2Int.one;
            //判断下是否能显示的下;
            if(constraintType == Constraint.FixedColumnCount){
                float totalColumnWidth = preCellSize.x * tmpCount + prePadding.x * (tmpCount - 1);
                if(totalColumnWidth > viewPortWh.x){
                    //显示不下需要调整, 从之前数量-1开始计算;
                    for(int i = constraintCount-1; i>=0; i--){
                        totalColumnWidth = preCellSize.x * i + prePadding.x * (i - 1);
                        if(totalColumnWidth <= viewPortWh.x){
                            //找到了;
                            constraintCount = i;
                            break;
                        }
                    }
                }
                rtnVec2Int.x = Mathf.CeilToInt(viewPortWh.y/(preCellSize.y+prePadding.y));
                rtnVec2Int.y = constraintCount;

                rtnVec2Int.x = Mathf.Max(rtnVec2Int.x, 1);
                rtnVec2Int.y = Mathf.Max(rtnVec2Int.y, 1);
            }else{
                float totalRowHeight = preCellSize.y * tmpCount + prePadding.y * (tmpCount - 1);
                if(totalRowHeight > viewPortWh.y){
                    //显示不下需要调整, 从之前数量-1开始计算;
                    for(int i = constraintCount-1; i>=0; i--){
                        totalRowHeight = preCellSize.y * i + prePadding.y * (i - 1);
                        if(totalRowHeight <= viewPortWh.y){
                            //找到了;
                            constraintCount = i;
                            break;
                        }
                    }
                }
                rtnVec2Int.x = constraintCount;
                rtnVec2Int.y = Mathf.CeilToInt(viewPortWh.x/(preCellSize.x+prePadding.x));

                rtnVec2Int.x = Mathf.Max(rtnVec2Int.x, 1);
                rtnVec2Int.y = Mathf.Max(rtnVec2Int.y, 1);
            }
            return rtnVec2Int;
        }

        /// <summary>
        /// 重设置GridLayout的cellSize去满足能够在Viewport里显示;
        /// </summary>
        /// <param name="scrollRect"></param>
        /// <returns>可以显示的行数和列数; (向上取整)</returns>
        public static Vector2Int ResizeGridlayoutCellSizeAdjustViewPort(ScrollRect scrollRect){
            GridLayoutGroup gridLayoutGroup = scrollRect.content.GetComponent<GridLayoutGroup>();
            RectTransform viewPortRectTrans = scrollRect.viewport.transform as RectTransform;
            if(gridLayoutGroup == null){
                Diagnostic.Warn("ScrollRect content must has GridlayoutGroup component");
                return Vector2Int.zero;
            }
            if(viewPortRectTrans == null){
                Diagnostic.Warn("ScrollRect viewPort must has RectTransform component");
                return Vector2Int.zero;
            }
            if(gridLayoutGroup.constraint == Constraint.Flexible){
                Diagnostic.Warn("ResizeGridlayoutCellSizeAdjustViewPort hasn't effective when gridlayoutgroup.constraint == Constraint.Flexible");
                return Vector2Int.zero;
            }
            float viewPortW = viewPortRectTrans.rect.width;
            float viewPortH = viewPortRectTrans.rect.height;
            if(viewPortW <= 0.0f || viewPortH <= 0.0f){
                viewPortW = (scrollRect.transform as RectTransform).sizeDelta.x;
                viewPortH = (scrollRect.transform as RectTransform).sizeDelta.y;
            }
            return ResizeGridlayoutCellSizeAdjustViewPort(gridLayoutGroup.cellSize, gridLayoutGroup.spacing, viewPortW, viewPortH, gridLayoutGroup.constraint, gridLayoutGroup.constraintCount);
        }

        //获取左上角的rect数据;
        //可以指定父节点;
        public static Rect GetLeftTopRect(RectTransform rectTrans, Transform refParent){
            Vector3 locPos = refParent.InverseTransformPoint(rectTrans.position);
            //float w = rectTrans.sizeDelta.x;
            //float h = rectTrans.sizeDelta.y;
            float w = rectTrans.rect.width;
            float h = rectTrans.rect.height;
            return new Rect(
                locPos.x - rectTrans.pivot.x * w,
                locPos.y + (1 - rectTrans.pivot.y) * h,
                w,
                h
            );
        }

         /**
         * 检查下是否交叉了;
         */
        //public static InterestType CheckIsCrossRect(RectTransform checkTrans1, RectTransform checkTrans2){
        //    Rect rect1 = CommonUtil.GetLeftTopRect(checkTrans1, checkTrans2.parent);
        //    Rect rect2 = CommonUtil.GetLeftTopRect(checkTrans2, checkTrans2.parent);
        //    // bool isCross = true;
        //    float maxx = Mathf.Max(rect1.x + rect1.width , rect2.x+rect2.width);
        //    float maxy = Mathf.Max(rect1.y , rect2.y);

        //    float minx = Mathf.Min(rect1.x , rect2.x);
        //    float miny = Mathf.Min(rect1.y - rect1.height , rect2.y - rect2.height);
        //    if(minx > maxx || miny > maxy)
        //    {
        //        // isCross = false;
        //         return InterestType.None;
        //    }else{
        //        //判断是否是在里面
        //        if(rect1.x > rect2.x && rect1.x+rect1.width < rect2.x+rect2.width && rect1.y-rect1.height > rect2.y - rect2.height && rect1.y < rect2.y){
        //            return InterestType.In;
        //        }
        //    }
        //    return InterestType.Cross;
        //}
        
        public static string GetRelativePath()
        {
#if UNITY_IOS
			if (Application.isMobilePlatform || Application.isConsolePlatform)
				return "file://" + Application.streamingAssetsPath;
#else
            if (Application.isMobilePlatform || Application.isConsolePlatform)
                return Application.streamingAssetsPath;
#endif
            if (Application.isMobilePlatform || Application.isConsolePlatform)
                return Application.streamingAssetsPath;

            else // For standalone player.
                return "file://" + Application.streamingAssetsPath;
        }

        #region 解决PC下加载ab，出现粉色问题。仅PC下,AB模式有效
        /// <summary>
        /// 解决PC下加载ab，出现粉色问题。
        /// 仅PC下次方法有效
        /// </summary>
        /// <param name="go"></param>
#if ACGGAME_CLIENT
		public static void SetTrueShaderToGameObject(GameObject go)
        {
            if (Application.platform == RuntimePlatform.Android || Application.platform == RuntimePlatform.IPhonePlayer)
            {
                return;
            }

#if UNITY_EDITOR
            if (TKFrame.AssetBundleManager.SimulateAssetBundleInEditor == true) return;
#endif

            if (go == null)
                return;

            Renderer r = null;
            //  QualitySettings.SetQualityLevel(0, true);
            // Log.info("Current Quality Level:" + QualitySettings.GetQualityLevel());
            ParticleSystem[] pss = go.GetComponentsInChildren<ParticleSystem>(true);
            
            foreach (ParticleSystem ps in pss)
            {
                r = ps.GetComponent<Renderer>();
                if (r == null) continue;
                ResetShaders(r.materials);
            }

            MeshRenderer[] mrs = go.GetComponentsInChildren<MeshRenderer>(true);
            foreach (MeshRenderer mr in mrs)
            {
                r = mr.GetComponent<Renderer>();
                if (r == null) continue;
                ResetShaders(r.materials);
            }

            SkinnedMeshRenderer[] smrs = go.GetComponentsInChildren<SkinnedMeshRenderer>(true);
            foreach (SkinnedMeshRenderer smr in smrs)
            {
                ResetShaders(smr.materials);
            }

            TrailRenderer[] trs = go.GetComponentsInChildren<TrailRenderer>(true);
            foreach (TrailRenderer tr in trs)
            {
                ResetShaders(tr.materials);
            }

            NcUvAnimation[] nuas = go.GetComponentsInChildren<NcUvAnimation>(true);
            foreach (NcUvAnimation nua in nuas)
            {
                r = nua.GetComponent<Renderer>();
                if (r == null) continue;
                ResetShaders(r.materials);
            }

            NcSpriteAnimation[] nsas = go.GetComponentsInChildren<NcSpriteAnimation>(true);
            foreach (NcSpriteAnimation nsa in nsas)
            {
                r = nsa.GetComponent<Renderer>();
                if (r == null) continue;
                ResetShaders(r.materials);
            }
        }
        
        private static void ResetShaders(Material[] materials)
        {
            for (int m = 0; m < materials.Length; m++)
            {
                Material material = materials[m];
                material.shader = Shader.Find(material.shader.name);
            }
        }


        /// <summary>
        /// 把事件继续往下传;
        /// </summary>
        /// <param name="eventData"></param>
        /// <param name="eventFunc"></param>
        /// <typeparam name="T"></typeparam>
        public static void PassEvent<T>(PointerEventData eventData, ExecuteEvents.EventFunction<T> eventFunc)
            where T : IEventSystemHandler
        {
            List<RaycastResult> results = new List<RaycastResult>();
            EventSystem.current.RaycastAll(eventData, results);
            //过滤掉当前点中的gameobject;
            GameObject curGo = eventData.pointerCurrentRaycast.gameObject;
            for (int i = 0, len = results.Count; i < len; i++)
            {
                if (curGo != results[i].gameObject)
                {
                    ExecuteEvents.Execute(results[i].gameObject, eventData, eventFunc);
                    break;
                }
            }
        }

#endif

        #endregion
        
        public static bool CheckTweenerCanReUsed(Tweener tweener)
        {
            return tweener != null && (tweener.IsActive() || tweener.IsPlaying());
        }
    }
}
