using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
[RequireComponent(typeof(Camera))]
[RequireComponent(typeof(Light))]
[ExecuteInEditMode]
public class SendShadow : MonoBehaviour
{
    public Shader replacementShader;
    public Shader gausBlurShader;

    private Camera shadowCam;
    private Light directionalLight;
    static int _ShadowVPMatrix;
    static int _CurrentShadowmap;
    //static int _OffsetScale;
    static int _DirectionalLightColor;
    static int _DirectionalLightDir;

    private Material gausBlurMaterial;
    public int shadowmapResolution = 1024;
    private RenderTexture shadowrender;
    private RenderTexture shadowmap;
    private float originShadowDistance;

    private void Awake()
    {
#if UNITY_EDITOR
        replacementShader = Shader.Find("ZChess_Scene/ShadowmapReplacement");
        gausBlurShader = Shader.Find("ZChess_Scene/GausBlur");
#endif
        _ShadowVPMatrix = Shader.PropertyToID("_ShadowVPMatrix");
        _CurrentShadowmap = Shader.PropertyToID("_CurrentShadowmap");
        //_OffsetScale = Shader.PropertyToID("_OffsetScale");
        _DirectionalLightColor = Shader.PropertyToID("_DirectionalLightColor");
        _DirectionalLightDir = Shader.PropertyToID("_DirectionalLightDir");
        shadowCam = GetComponent<Camera>();
        directionalLight = GetComponent<Light>();
    }
    private void OnEnable()
    {
        originShadowDistance = QualitySettings.shadowDistance;
        QualitySettings.shadowDistance = 350;

        shadowCam.SetReplacementShader(replacementShader, "");
        shadowCam.enabled = false;

        gausBlurMaterial = new Material(gausBlurShader);
        shadowrender = new RenderTexture(shadowmapResolution, shadowmapResolution, 16, RenderTextureFormat.ARGB32, RenderTextureReadWrite.Default);
        shadowrender.filterMode = FilterMode.Bilinear;
        shadowmap = new RenderTexture(shadowmapResolution, shadowmapResolution, 0, RenderTextureFormat.ARGB32, RenderTextureReadWrite.Default);
        shadowmap.filterMode = FilterMode.Bilinear;
        shadowCam.targetTexture = shadowrender;
        UpdateCamera();
    }
    /**
     * Update the shadow projection camera
     * Should be called in initializing & scene transforming
     */
    public void UpdateShadowCamera()
    {
        Matrix4x4 projection = GL.GetGPUProjectionMatrix(shadowCam.projectionMatrix, false);
        Matrix4x4 vp = projection * shadowCam.worldToCameraMatrix;
        shadowCam.Render();
        // var lodGroups = FindObjectsOfType<LODGroup>().Where(o => o.enabled).ToArray();
        // LODGroup[] groups = FindObjectsOfType<LODGroup>();
        // List<LODGroup> enabledlodGroup = new List<LODGroup>();
        // for (int k = 0; k < groups.Length; ++k)
        // {
        //     if (groups[k].enabled)
        //     {
        //         enabledlodGroup.Add(groups[k]);
        //     }
        // }
        // var lodGroups = enabledlodGroup.ToArray();
        // for (int i = 0; i < lodGroups.Length; ++i) lodGroups[i].enabled = false;
        // shadowCam.Render();
        // for (int i = 0; i < lodGroups.Length; ++i) lodGroups[i].enabled = true;
        Graphics.Blit(shadowrender, shadowmap, gausBlurMaterial, 0);
        Graphics.Blit(shadowmap, shadowrender, gausBlurMaterial, 1);
        Shader.SetGlobalMatrix(_ShadowVPMatrix, vp);
        Shader.SetGlobalTexture(_CurrentShadowmap, shadowrender);
    }
    /**
     * Update light information into shader
     * Should be called in initializing & scene transforming
     */
    public void UpdateLightInformation()
    {
        Shader.SetGlobalVector(_DirectionalLightDir, -transform.forward);
        Vector4 col = directionalLight.color * directionalLight.intensity;
        col.w = 1;
        Shader.SetGlobalVector(_DirectionalLightColor, col);
    }

    private void OnDisable()
    {
        if (shadowmap != null)
        {
            shadowmap.Release();
            shadowmap = null;
        }
        if (shadowrender != null)
        {
            if (shadowCam != null)
            {
                shadowCam.targetTexture = null;
            }
            shadowrender.Release();
            shadowrender = null;
        }
        QualitySettings.shadowDistance = originShadowDistance;
    }

#if UNITY_EDITOR
    private void Update()
    {
        UpdateCamera();
    }
#endif

    public void UpdateCamera()
    {
        UpdateShadowCamera();
        UpdateLightInformation();
    }
}