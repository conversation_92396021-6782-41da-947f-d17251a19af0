Shader "Hidden/ImageEffect/TKPostProcessingStack"
{
    Properties
    {
        // Defined in "GlobalParams.cginc"
        _MainTex("Texture", 2D) = "white" {}
    }

    SubShader
    {
        Cull Back
        ZTest Always
        ZWrite Off

        Tags { "RenderType" = "Opaque" }
        LOD 100

        Pass //0 down sample
        {
            Name "DownSample"
            CGPROGRAM
            #pragma vertex vert_min
            #pragma fragment frag
            #pragma fragmentoption ARB_precision_hint_fastest
            #include "CGInclude/EffectStack.cginc"

            half4 frag(v2f_mini i) : SV_Target
            {
                half4 c = 0;
                c += tex2D(_MainTex, i.uv + _MainTex_TexelSize.xy * half2(-1, -1)) * 0.25;
                c += tex2D(_MainTex, i.uv + _MainTex_TexelSize.xy * half2(1, 1)) * 0.25;
                c += tex2D(_MainTex, i.uv + _MainTex_TexelSize.xy * half2(1, -1)) * 0.25;
                c += tex2D(_MainTex, i.uv + _MainTex_TexelSize.xy * half2(-1, 1)) * 0.25;
                return c;
            }
            ENDCG
        }

        Pass //1 pack depth
        {
            Name "Pack_Depth"
            CGPROGRAM
            #pragma shader_feature _TK_INCLUDE_NORMAL
            #pragma vertex vert_min
            #pragma fragment frag
            #pragma fragmentoption ARB_precision_hint_fastest
            #include "CGInclude/EffectStack.cginc"

            half4 frag(v2f_mini i) : SV_Target
            {
                half4 c = tex2D(_MainTex, i.uv);
                float linearDepth;
                // the depth from DecodeFloatRG is lineared 0-1 depth, so we should unlinear it to make it the same as SAMPLE_DEPTH_TEXTURE.
#if _TK_INCLUDE_NORMAL
                // if else statement is OK when threads in one wavefront/wrap executes the same branch.
                // otherwise, all branches will be executed per pixel.
                // ** Well, not sure for mobile gpus. no documents tells me that mobile gpu has designed for if-else statement optimization.
                // ** Considering the fact that decode texture is an expensive operation, so be it.
                // _TexelSize.y < 0 often occurs when graphics apis like d3d and vulkan and metal flips the rendertexture and this shader is used for blit rt purpose.
                // if (_MainTex_TexelSize.y < 0)
                //     linearDepth = LinearEyeDepth(UnLinear01Depth(DecodeFloatRG(tex2D(_CameraDepthNormalsTexture, float2(i.uv.x, 1 - i.uv.y)).zw)));
                // else
                    linearDepth = LinearEyeDepth(UnLinear01Depth(DecodeFloatRG(tex2D(_CameraDepthNormalsTexture, i.uv).zw)));
#else
                // if (_MainTex_TexelSize.y < 0)
                //     linearDepth = LinearEyeDepth(SAMPLE_DEPTH_TEXTURE(_CameraDepthTexture, float2(i.uv.x, 1 - i.uv.y)));
                // else
                    linearDepth = LinearEyeDepth(SAMPLE_DEPTH_TEXTURE(_CameraDepthTexture, i.uv));
#endif
                c.a = (linearDepth - _Near) / (_Far - _Near);
                // return half4(depth, 0, 0, 1);
                return c;
            }
            ENDCG
        }

        Pass //2 blur horizontal with depth
        {
            Name "Blur_Horizontal_With_Depth"
            CGPROGRAM
            #pragma vertex vert_min
            #pragma fragment frag
            #pragma fragmentoption ARB_precision_hint_fastest
            #include "CGInclude/EffectStack.cginc"

            half4 frag(v2f_mini i) : SV_Target
            {
                half4 c = tex2D(_MainTex, i.uv) * curve[3];
                float2 offset = float2(_MainTex_TexelSize.x * c.a * _Dof_HorizontalRadius, 0);
                c += tex2D(_MainTex, i.uv - 3 * offset) * curve[0];
                c += tex2D(_MainTex, i.uv - 2 * offset) * curve[1];
                c += tex2D(_MainTex, i.uv - 1 * offset) * curve[2];
                c += tex2D(_MainTex, i.uv + 1 * offset) * curve[4];
                c += tex2D(_MainTex, i.uv + 2 * offset) * curve[5];
                c += tex2D(_MainTex, i.uv + 3 * offset) * curve[6];
                return c;
            }
            ENDCG
        }

        Pass //3 blur vertical with depth
        {
            Name "Blur_Vertical_With_Depth"
            CGPROGRAM
            #pragma vertex vert_min
            #pragma fragment frag
            #pragma fragmentoption ARB_precision_hint_fastest
            #include "CGInclude/EffectStack.cginc"

            half4 frag(v2f_mini i) : SV_Target
            {
                half4 c = tex2D(_MainTex, i.uv) * curve[3];
                float2 offset = float2(0, _MainTex_TexelSize.y * c.a * _Dof_VerticalRadius);
                c += tex2D(_MainTex, i.uv - 3 * offset) * curve[0];
                c += tex2D(_MainTex, i.uv - 2 * offset) * curve[1];
                c += tex2D(_MainTex, i.uv - 1 * offset) * curve[2];
                c += tex2D(_MainTex, i.uv + 1 * offset) * curve[4];
                c += tex2D(_MainTex, i.uv + 2 * offset) * curve[5];
                c += tex2D(_MainTex, i.uv + 3 * offset) * curve[6];
                return c;
            }
            ENDCG
        }

        Pass //4 brightness filter
        {
            Name "Brightness_Filter"
            CGPROGRAM
            #pragma vertex vert_min
            #pragma fragment frag
            #pragma fragmentoption ARB_precision_hint_fastest
            #include "CGInclude/EffectStack.cginc"

            half4 frag(v2f_mini i) : SV_Target
            {
                half4 c = tex2D(_MainTex, i.uv);
                c.rgb -= _Threshold;
                return c;
            }
            ENDCG
        }

        Pass //5 Blur Horizontal
        {
            Name "Blur_Horizontal"
            CGPROGRAM
            #pragma vertex vert_min
            #pragma fragment frag
            #pragma fragmentoption ARB_precision_hint_fastest
            #include "CGInclude/EffectStack.cginc"

            half4 frag(v2f_mini i) : SV_Target
            {
                half4 c = 0;
                float2 offset = float2(_MainTex_TexelSize.x * _Bloom_HorizontalRadius, 0);
                for (int l = 0; l < 7; ++l)
                {
                    c += tex2D(_MainTex, i.uv + (l - 3) * offset) * curve[l];
                }
                return c;
            }
            ENDCG
        }

        Pass //6 Blur Vertical
        {
            Name "Blur_Vertical"
            CGPROGRAM
            #pragma vertex vert_min
            #pragma fragment frag
            #pragma fragmentoption ARB_precision_hint_fastest
            #include "CGInclude/EffectStack.cginc"

            half4 frag(v2f_mini i) : SV_Target
            {
                half4 c = 0;
                float2 offset = float2(0, _MainTex_TexelSize.y * _Bloom_VerticalRadius);
                for (int l = 0; l < 7; ++l)
                {
                    c += tex2D(_MainTex, i.uv + (l - 3) * offset) * curve[l];
                }
                return c;
            }
            ENDCG
        }

        Pass //7 Radial Blur
        {
            Name "Radial_Blur"
            CGPROGRAM
            #pragma vertex vert_min
            #pragma fragment frag
            #pragma fragmentoption ARB_precision_hint_fastest
            #include "CGInclude/EffectStack.cginc"

            half4 frag(v2f_mini i) : SV_Target
            {
                half2 dir = 0.5 - i.uv;
                half dist = length(dir);
                dir /= dist;
                dir *= _SampleDist;

                half4 c = 0;
                c += tex2D(_MainTex, i.uv - dir * 0.01);
                c += tex2D(_MainTex, i.uv - dir * 0.02);
                c += tex2D(_MainTex, i.uv - dir * 0.03);
                c += tex2D(_MainTex, i.uv - dir * 0.05);
                c += tex2D(_MainTex, i.uv - dir * 0.08);
                c += tex2D(_MainTex, i.uv + dir * 0.01);
                c += tex2D(_MainTex, i.uv + dir * 0.02);
                c += tex2D(_MainTex, i.uv + dir * 0.03);
                c += tex2D(_MainTex, i.uv + dir * 0.05);
                c += tex2D(_MainTex, i.uv + dir * 0.08);
                c *= 0.1;

                return c;
            }
            ENDCG
        }

        Pass //8 TKPostProcessingStack uber pass
        {
            Name "I am BERRRRRRRT!!"
            CGPROGRAM
            #pragma shader_feature _TK_INCLUDE_NORMAL
            #pragma shader_feature TKPPS_OTHER // reuse this kw in project.
            // #pragma shader_feature _DOF
            // #pragma shader_feature _RADIAL_BLUR
            // #pragma shader_feature _BLOOM
            // #pragma shader_feature TK_LUT_LDR TK_LUT_HDR
            // #pragma shader_feature _VIGNETTE
            #pragma shader_feature _APPLYFXAA
            // #pragma shader_feature _APPLYSSAO
            // #pragma shader_feature _APPLYFOGEFFECT
            // #pragma shader_feature _APPLYHSL
            #pragma shader_feature _HIGHLIGHT
            // If hdr enabled, make sure using outline instead of outline_alpha, because r11g11b10 has no alpha channel.
            #pragma shader_feature _OUTLINE // _OUTLINE_ALPHA
            #pragma shader_feature _BLOOM_HDR_LOW // _BLOOM_HDR_HIGH contains in TKPPS_OTHER
            // #pragma shader_feature _BLUE_NOISE_DITHERING
            #pragma shader_feature COLOR_GRADING_LDR_2D // COLOR_GRADING_HDR_2D COLOR_GRADING_HDR_3D
            // #pragma shader_feature TONEMAPPING_CUSTOM TONEMAPPING_ACES TONEMAPPING_NEUTRAL
            #pragma shader_feature _SOFT_LIGHT
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 3.0
            #pragma fragmentoption ARB_precision_hint_fastest
            #include "CGInclude/Sample.cginc"
            #include "CGInclude/Colors.cginc"
            #include "CGInclude/EffectStack.cginc"
            #include "CGInclude/SubpixelMorphologicalAntialiasingBridge.cginc"

            float3 _Lut2D_Params;
            float _EnableOffscreenFloat;

            // SSAO(X), DOF(Y), RGBGlitch(Z) and RadialBlur(W)
            fixed4 _ShaderUploadData1;
            // HDR_High(X), SMAA(Y)
            fixed4 _ShaderUploadData2;

            sampler2D _OffscreenRenderFinalTexture;
            sampler2D _BlendTex;

            #if TKPPS_OTHER
            // Vignette
            half3 _Vignette_Color;
            half2 _Vignette_Center; // UV space
            half4 _Vignette_Settings; // x: intensity, y: smoothness, z: roundness, w: rounded
            half _Vignette_Opacity;
            half _Vignette_Mode; // <0.5: procedural, >=0.5: masked
            sampler2D _Vignette_Mask;
            #endif

            struct v2f_uber
            {
                float4 pos : SV_POSITION;
                float2 uv : TEXCOORD0;
            #if _BLUE_NOISE_DITHERING
                float2 uvBlueNoise : TEXCOORD1;
            #endif
            #if _APPLYFOGEFFECT
                float3 worldDirection : TEXCOORD2;
            #endif
            #if _APPLYFXAA
                float4 interpolatorA : TEXCOORD3;
                float4 interpolatorB : TEXCOORD4;
                float4 interpolatorC : TEXCOORD5;
            #endif
            #if TKPPS_OTHER
                float4 offset : TEXCOORD6;
            #endif
            };

            v2f_uber vert(AttributesDefault v)
            {
                v2f_uber o;
                o.pos = UnityObjectToClipPos(v.vertex);
                o.uv = v.uv;
            #if TKPPS_OTHER
                // SMAA
                if (_ShaderUploadData2.y > 5)
                {
                    o.offset = mad(SMAA_RT_METRICS.xyxy, float4(1.0, 0.0, 0.0, 1.0), o.uv.xyxy);
                }
            #endif

            #if _APPLYFXAA
                float4 extents;
                float2 offset = ( _MainTex_TexelSize.xy ) * 0.5f;
                extents.xy = v.uv.xy - offset;
                extents.zw = v.uv.xy + offset;

                float4 rcpSize;
                rcpSize.xy = -_MainTex_TexelSize.xy * 0.5f;
                rcpSize.zw = _MainTex_TexelSize.xy * 0.5f;

            #if defined (SHADER_API_PSP2)
                //cg compiler linker bug workaround
                float almostzero = v.uv.x*0.000001f;
                rcpSize.x += almostzero;
            #endif

                o.interpolatorA = extents;
                o.interpolatorB = rcpSize;
                o.interpolatorC = rcpSize;

                o.interpolatorC.xy *= 4.0;
                o.interpolatorC.zw *= 4.0;
            #endif

            #if _APPLYFOGEFFECT
                o.worldDirection = mul(_ClipToWorld, float4(o.pos.xy, 0, 1)) - _WorldSpaceCameraPos;
            #endif
            #if _BLUE_NOISE_DITHERING
                _BlueNoise_ST = fixed4(_BlueNoise_ST.xy * _ParticleFactor, _BlueNoise_ST.zw);
                o.uvBlueNoise = TRANSFORM_TEX(v.uv, _BlueNoise);
            #endif
                return o;
            }

            half4 frag(v2f_uber i) : SV_Target
            {
                half4 mainColor;
#if _APPLYFXAA
                // half4 mainColor = FxaaPixelShader_QualitySpeed(
                //     i.uv,
                //     i.uvAux,
                //     _MainTex,
                //     _rcpFrame.xy,
                //     _rcpFrameOpt
                // );
                mainColor = half4(FxaaPixelShader(i.uv, i.interpolatorA, i.interpolatorB, i.interpolatorC).rgb, 1);
#else

#if TKPPS_OTHER
                // SMAA
                if (_ShaderUploadData2.y > 5)
                {
                    mainColor = SMAANeighborhoodBlendingPS(i.uv, i.offset, _MainTex, _BlendTex);
                }
                else
#endif
                {
                    mainColor = tex2D(_MainTex, i.uv);
                }
#endif

                // _EnableOffscreenFloat should be 0f or 10f.
                if (_EnableOffscreenFloat > 5.0f)
                {
                    half4 offscreenColor = tex2D(_OffscreenRenderFinalTexture, i.uv);
                    mainColor.rgb = offscreenColor.rgb + mainColor.rgb * offscreenColor.a;
                }

#if _OUTLINE_ALPHA || _OUTLINE
                {
                    // 计算梯度值，edge越小该片段越有可能是个边缘点
                    half edge = RobertHard(i.uv);
                    edge += saturate(_OutlineThreshold) * (1 - edge);
                    mainColor = lerp(_OutlineColor * _OutlineIntensity, mainColor, edge);
                }
#endif

#if _HIGHLIGHT
                {
                    // 计算梯度值，edge越小该片段越有可能是个边缘点
                    half softEdge = RobertSoft(i.uv);
                    mainColor = lerp(_HighlightColor * _HighlightIntensity, mainColor, softEdge);
                }
#endif

#if _APPLYHSL
                {
                    half3 renderTex = mainColor.rgb;
                    // apply brightness
                    mainColor.rgb = renderTex.rgb * _Brightness;

                    // apply saturation
                    half luminance = 0.2125 * renderTex.r + 0.7154 * renderTex.g + 0.0721 * renderTex.b;
                    half3 luminanceColor = half3(luminance, luminance, luminance);
                    mainColor.rgb = lerp(luminanceColor, mainColor.rgb, _Saturation);

                    // apply contrast
                    half3 avgColor = half3(0.5, 0.5, 0.5);
                    mainColor.rgb = lerp(avgColor, mainColor.rgb, _Contrast);
                }
#endif

#if _BLUE_NOISE_DITHERING
                {
                    half colBlue = TriangleRemap(tex2D(_BlueNoise, i.uvBlueNoise + _Time.w).r) / 255.0f;
                    mainColor.rgb += half3(colBlue, colBlue, colBlue);
                }
#endif

#if _APPLYFOGEFFECT
                {
                    float linearDepth;
#if _TK_INCLUDE_NORMAL
                    linearDepth = LinearEyeDepth(UnLinear01Depth(DecodeFloatRG(tex2D(_CameraDepthNormalsTexture, i.uv).zw)));
#else
                    linearDepth = LinearEyeDepth(SAMPLE_DEPTH_TEXTURE(_CameraDepthTexture, i.uv));
#endif
                    float3 worldPos = i.worldDirection * linearDepth + _WorldSpaceCameraPos;

                    float2 speed = _Time.y * float2(_FogXSpeed, _FogYSpeed);
                    float noise = (tex2D(_NoiseFogTex, i.uv + speed).r - 0.5) * _NoiseFogAmount;

                    float fogDensity = (_FogEnd - worldPos.y) / (_FogEnd - _FogStart);
                    fogDensity = saturate(fogDensity * _FogDensity * (1 + noise));
                    mainColor.rgb = lerp(mainColor.rgb, _FogColor.rgb, fogDensity);
                }
#endif

#if TKPPS_OTHER
// #if _APPLYSSAO
                if (_ShaderUploadData1.x > 5)
                {
                    float2 delta = _MainTex_TexelSize.xy;
                    half ao = BlurSmall(_OcclusionTexture, i.uv, delta);
                    mainColor.rgb *= 1 - EncodeAO(ao);
                }
// #endif

// #if _DOF
                if (_ShaderUploadData1.y > 5)
                {
                    half4 dof = tex2D(_DofTex, i.uv);
                    mainColor = lerp(mainColor, dof, saturate(dof.a * _BlendDistance));
                }
// #endif

// #if _RADIAL_BLUR
                if (_ShaderUploadData1.w > 5)
                {
                    half4 msk = tex2D(_CameraMask, i.uv);
                    half dist = length(half2(0.5 - i.uv.x, _CenterY - i.uv.y));
                    half4 radialBlur = tex2D(_RadialBlurTex, i.uv);
                    mainColor = lerp(mainColor, radialBlur, saturate(_SampleStrength * dist));
                    mainColor.rgb *= saturate(msk.r + 1 - _MaskStrength);
                }
#endif

#if _BLOOM
                {
                    half4 bloom = saturate(tex2D(_BloomTex, i.uv) * _Intensity);
                    mainColor = mainColor * (1 - bloom) + bloom;
                }
#endif

#if UNITY_COLORSPACE_GAMMA
                {
                    // Gamma space... Gah.
                    mainColor = SRGBToLinear(mainColor);
                }
#endif

#if _SOFT_LIGHT
                {
                    float4 blurredColor = UpsampleBox(_SoftLightTexture, i.uv, _SoftLightTexture_TexelSize.xy * _SoftLightBlurRadius, 1);
                    float3 overlayColor = BlendMode_Overlay(mainColor.rgb, blurredColor.rgb);
                    // float3 overlayColor = BlendMode_Overlay(LinearToSRGB(mainColor.rgb), LinearToSRGB(blurredColor.rgb));
                    // overlayColor = SRGBToLinear(overlayColor);
                    mainColor.rgb = lerp(mainColor.rgb, overlayColor, _SoftLightIntensity);
                    // mainColor.rgb = BlendMode_Overlay(mainColor.rgb, blurredColor.rgb * _SoftLightIntensity);
                }
#endif

#if _BLOOM_HDR_LOW
                {
                    float2 uvDistorted = i.uv;
                    half4 bloom = UpsampleBox(_BloomTexHDR, uvDistorted, _BloomTexHDR_TexelSize.xy * _HDRBloom_BlurRadius, _Bloom_Settings.x);
                    bloom *= _Bloom_Settings.y;
                    mainColor += bloom * half4(_Bloom_Color, 1.0);
                }
#endif

#if TKPPS_OTHER && !_BLOOM_HDR_LOW
                if (_ShaderUploadData2.x > 5)
                {
                    float2 uvDistorted = i.uv;
                    half4 bloom = UpsampleTent(_BloomTexHDR, uvDistorted, _BloomTexHDR_TexelSize.xy * _HDRBloom_BlurRadius, _Bloom_Settings.x);
                    bloom *= _Bloom_Settings.y;
                    mainColor += bloom * half4(_Bloom_Color, 1.0);
                }
#endif

// #if _VIGNETTE
#if TKPPS_OTHER
                if (_ShaderUploadData2.z > 5)
                {
                    float2 uvDistorted = i.uv;
                    if (_Vignette_Mode < 0.5)
                    {
                        half2 d = abs(uvDistorted - _Vignette_Center) * _Vignette_Settings.x;
                        d.x *= lerp(1.0, _ScreenParams.x / _ScreenParams.y, _Vignette_Settings.w);
                        d = pow(saturate(d), _Vignette_Settings.z); // Roundness
                        half vfactor = pow(saturate(1.0 - dot(d, d)), _Vignette_Settings.y);
                        mainColor.rgb *= lerp(_Vignette_Color, (1.0).xxx, vfactor);
                        mainColor.a = lerp(1.0, mainColor.a, vfactor);
                    }
                    else
                    {
                        half vfactor = SAMPLE_TEXTURE2D(_Vignette_Mask, sampler_Vignette_Mask, uvDistorted).a;

                        #if !UNITY_COLORSPACE_GAMMA
                        {
                            vfactor = SRGBToLinear(vfactor);
                        }
                        #endif

                        half3 new_color = mainColor.rgb * lerp(_Vignette_Color, (1.0).xxx, vfactor);
                        mainColor.rgb = lerp(mainColor.rgb, new_color, _Vignette_Opacity);
                        mainColor.a = lerp(1.0, mainColor.a, vfactor);
                    }
                }
#endif

#if TK_LUT_LDR
                {
                    mainColor.rgb = LinearToSRGB(mainColor.rgb);
                    half3 colorGraded = ApplyLut2d(_UserLut, mainColor.rgb, _UserLut_Params.xyz);
                    mainColor.rgb = lerp(mainColor.rgb, colorGraded, _UserLut_Params.w);
                    mainColor.rgb = SRGBToLinear(mainColor.rgb);
                }
#elif TK_LUT_HDR
                {
                    half3 colorGraded = ApplyLut2d(_UserLut, mainColor.rgb, _UserLut_Params.xyz);
                    mainColor.rgb = lerp(mainColor.rgb, colorGraded, _UserLut_Params.w);
                }
#endif

                // please make sure that color grading is the last post process step!!
                // useless, keep this for archive.
#if COLOR_GRADING_HDR_3D
                /*{
                    mainColor *= _PostExposure;
                    float3 colorLutSpace = saturate(LUT_SPACE_ENCODE(mainColor.rgb));
                    mainColor.rgb = ApplyLut3D(TEXTURE3D_PARAM(_Lut3D, sampler_Lut3D), colorLutSpace, _Lut3D_Params);
                }*/
#elif COLOR_GRADING_HDR_2D
                {
                    mainColor *= _PostExposure;
                    float3 colorLutSpace = saturate(LUT_SPACE_ENCODE(mainColor.rgb));
                    mainColor.rgb = ApplyLut2D(TEXTURE2D_PARAM(_Lut2D, sampler_Lut2D), colorLutSpace, _Lut2D_Params);
                }
#elif COLOR_GRADING_LDR_2D
                {
                    mainColor = saturate(mainColor);

                    // LDR Lut lookup needs to be in sRGB - for HDR stick to linear
                    mainColor.rgb = LinearToSRGB(mainColor.rgb);
                    mainColor.rgb = ApplyLut2D(TEXTURE2D_PARAM(_Lut2D, sampler_Lut2D), mainColor.rgb, _Lut2D_Params);
                    mainColor.rgb = SRGBToLinear(mainColor.rgb);
                }
#endif

#if UNITY_COLORSPACE_GAMMA
                {
                    mainColor = LinearToSRGB(mainColor);
                }
#endif

                return mainColor;
            }
            ENDCG
        }

        // 9: Occlusion estimation with CameraDepthTexture
        Pass
        {
            Name "Occlusion estimation with CameraDepthTexture"
            ZTest Always Cull Back ZWrite Off
            CGPROGRAM
            #define SOURCE_DEPTH
            #include "CGInclude/Obscurance.cginc"
            #pragma vertex vert
            #pragma fragment frag_ao
            #pragma target 3.0
            #pragma fragmentoption ARB_precision_hint_fastest
            ENDCG
        }
        // 10: Occlusion estimation with CameraDepthNormalsTexture
        Pass
        {
            Name "Occlusion estimation with CameraDepthNormalsTexture"
            ZTest Always Cull Back ZWrite Off
            CGPROGRAM
            #define SOURCE_DEPTHNORMALS
            #include "CGInclude/Obscurance.cginc"
            #pragma vertex vert
            #pragma fragment frag_ao
            #pragma target 3.0
            #pragma fragmentoption ARB_precision_hint_fastest
            ENDCG
        }
        // 11: Occlusion estimation with G-Buffer
        // Useless, keep pass number only
        Pass
        {
            COLORMASK 0
            //ZTest Always Cull Back ZWrite Off
            //CGPROGRAM
            //#define SOURCE_GBUFFER
            //#include "CGInclude/Obscurance.cginc"
            //#pragma vertex vert
            //#pragma fragment frag_ao
            //#pragma target 3.0
            //ENDCG
        }
        // 12: Separable blur (horizontal pass) with CameraDepthNormalsTexture
        Pass
        {
            Name "Separable_Blur_Horizontal"
            ZTest Always Cull Back ZWrite Off
            CGPROGRAM
            #define SOURCE_DEPTHNORMALS
            #define BLUR_HORIZONTAL
            #define BLUR_SAMPLE_CENTER_NORMAL
            #include "CGInclude/SeparableBlur.cginc"
            #pragma vertex vert
            #pragma fragment frag_blur
            #pragma target 3.0
            #pragma fragmentoption ARB_precision_hint_fastest
            ENDCG
        }
        // 13: Separable blur (horizontal pass) with G-Buffer
        // Useless, keep pass number only
        Pass
        {
            Name "Separable_Blur_Horizontal_GBuffer"
            COLORMASK 0
            //ZTest Always Cull Back ZWrite Off
            //CGPROGRAM
            //#define SOURCE_GBUFFER
            //#define BLUR_HORIZONTAL
            //#define BLUR_SAMPLE_CENTER_NORMAL
            //#include "CGInclude/SeparableBlur.cginc"
            //#pragma vertex vert
            //#pragma fragment frag_blur
            //#pragma target 3.0
            //ENDCG
        }
        // 14: Separable blur (vertical pass)
        Pass
        {
            Name "Separable_Blur_Vertical"
            ZTest Always Cull Back ZWrite Off
            CGPROGRAM
            #define BLUR_VERTICAL
            #include "CGInclude/SeparableBlur.cginc"
            #pragma vertex vert
            #pragma fragment frag_blur
            #pragma target 3.0
            #pragma fragmentoption ARB_precision_hint_fastest
            ENDCG
        }
        // 15: Final composition, merge to pass 8
        // Useless, keep pass number only
        Pass
        {
            COLORMASK 0
            // ZTest Always Cull Back ZWrite Off
            // CGPROGRAM
            // #include "CGInclude/Composition.cginc"
            // #pragma vertex vert
            // #pragma fragment frag_composition
            // #pragma target 3.0
            // ENDCG
        }
        // 16: Final composition (ambient only mode)
        // Useless, keep pass number only
        Pass
        {
            COLORMASK 0
            // Blend Zero OneMinusSrcColor, Zero OneMinusSrcAlpha
            // ZTest Always Cull Back ZWrite Off
            // CGPROGRAM
            // #include "CGInclude/Composition.cginc"
            // #pragma vertex vert_composition_gbuffer
            // #pragma fragment frag_composition_gbuffer
            // #pragma target 3.0
            // ENDCG
        }

        // 17: Prefilter 13 taps
        Pass
        {
            Name "Prefilter 13 taps"
            Cull Back ZWrite Off ZTest Always
            CGPROGRAM
                // #include "UnityCG.cginc"
                // #include "CGInclude/EffectStack.cginc"
                // #pragma vertex vert_img
                // #pragma fragment frag_img
                // half4 frag_img(v2f_img i) : SV_Target
                // {
                //     return tex2D(_MainTex, i.uv.xy);
                // }
                #include "CGInclude/BloomInc.cginc"

                #pragma vertex VertDefault
                #pragma fragment FragPrefilter13

            ENDCG
        }

        // 18: Prefilter 4 taps
        Pass
        {
            Name "Prefilter 4 taps"
            Cull Back ZWrite Off ZTest Always
            CGPROGRAM
                #include "CGInclude/BloomInc.cginc"

                #pragma vertex VertDefault
                #pragma fragment FragPrefilter4

            ENDCG
        }

        // 19: Downsample 13 taps
        Pass
        {
            Name "Downsample 13 taps"
            Cull Back ZWrite Off ZTest Always
            CGPROGRAM
                #include "CGInclude/BloomInc.cginc"

                #pragma vertex VertDefault
                #pragma fragment FragDownsample13

            ENDCG
        }

        // 20: Downsample 4 taps
        Pass
        {
            Name "Downsample 4 taps"
            Cull Back ZWrite Off ZTest Always
            CGPROGRAM
                #include "CGInclude/BloomInc.cginc"

                #pragma vertex VertDefault
                #pragma fragment FragDownsample4

            ENDCG
        }

        // 21: Upsample tent filter
        Pass
        {
            Name "Upsample tent filter"
            Cull Back ZWrite Off ZTest Always
            CGPROGRAM
                #include "CGInclude/BloomInc.cginc"

                #pragma vertex VertDefault
                #pragma fragment FragUpsampleTent

            ENDCG
        }

        // 22: Upsample box filter
        Pass
        {
            Name "Upsample box filter"
            Cull Back ZWrite Off ZTest Always
            CGPROGRAM
                #include "CGInclude/BloomInc.cginc"

                #pragma vertex VertDefault
                #pragma fragment FragUpsampleBox

            ENDCG
        }

        // 23: TAA perspective
        Pass
        {
            Name "TAA perspective"
            CGPROGRAM
            #include "CGInclude/TemporalAntialiasingInc.cginc"
            #pragma shader_feature _TK_INCLUDE_NORMAL
            #pragma vertex VertDefault
            #pragma fragment FragSolverDilate
            ENDCG
        }

        // 24: TAA orthographic
        Pass
        {
            Name "TAA orthographic"
            CGPROGRAM
            #include "CGInclude/TemporalAntialiasingInc.cginc"
            #pragma shader_feature _TK_INCLUDE_NORMAL
            #pragma vertex VertDefault
            #pragma fragment FragSolverNoDilate
            ENDCG
        }

        // 25: LutGenLDRFromScratch
        Pass
        {
            Name "LutGenLDRFromScratch"
            CGPROGRAM
            #include "CGInclude/ColorGrading.cginc"
            #pragma vertex VertDefault
            #pragma fragment FragLDRFromScratch
            #pragma target 3.0
            ENDCG
        }

        //26: LutGenLDR
        Pass
        {
            Name "LutGenLDR"
            CGPROGRAM
            #include "CGInclude/ColorGrading.cginc"
            #pragma vertex VertDefault
            #pragma fragment FragLDR
            #pragma target 3.0
            ENDCG
        }

        // 27: LutGenHDR2D
        Pass
        {
            Name "LutGenHDR2D"
            CGPROGRAM
            #include "CGInclude/ColorGrading.cginc"
            #pragma vertex VertDefault
            #pragma fragment FragHDRLut
            // #pragma shader_feature __ TONEMAPPING_ACES TONEMAPPING_NEUTRAL TONEMAPPING_CUSTOM
            #pragma target 3.0
            ENDCG
        }

        // 28: Hard outline
        Pass
        {
            Name "Hard outline"
            CGPROGRAM
            #include "CGInclude/Outline.cginc"
            #pragma vertex VertHardOutlineFast
            #pragma fragment FragHardOutlineFast
            #pragma target 3.0
            ENDCG
        }

        // 29: Highlight blur outline
        Pass
        {
            Name "Highlight blur outline"
            CGPROGRAM
            #include "CGInclude/Outline.cginc"
            #pragma vertex VertOutlineBlur
            #pragma fragment FragOutlineBlur
            #pragma target 3.0
            ENDCG
        }

        // 30: Soft outline
        Pass
        {
            Name "Soft outline"
            CGPROGRAM
            #include "CGInclude/Outline.cginc"
            #pragma vertex VertSoftOutlineFast
            #pragma fragment FragSoftOutlineFast
            #pragma target 3.0
            ENDCG
        }

        // 31: draw an empty quad with texture.
        Pass
        {
            Name "draw an empty quad with texture."
            ZTest Always
            Cull Back
            ZWrite Off
            SetTexture [_MainTex] {combine one-texture}
        }

        // 32: Hard outline(Alpha version)
        Pass
        {
            Name "Hard outline(Alpha version)"
            CGPROGRAM
            #include "CGInclude/Outline.cginc"
            #pragma vertex VertHardOutlineFastAlphaVersion
            #pragma fragment FragHardOutlineFastAlphaVersion
            #pragma target 3.0
            ENDCG
        }

        // 33 Dual kawase blur downsample
        Pass
        {
            Name "Dual kawase blur downsample"
            CGPROGRAM
            #include "CGInclude/DualKawaseBlur.cginc"
            #pragma vertex Vert_DownSample
            #pragma fragment Frag_DownSample
            ENDCG
        }

        // 34 Dual kawase blur upsample
        Pass
        {
            Name "Dual kawase blur upsample"
            CGPROGRAM
            #include "CGInclude/DualKawaseBlur.cginc"
            #pragma vertex Vert_UpSample
            #pragma fragment Frag_UpSample
            ENDCG
        }

        // 35 sharpen v3 pass
        Pass
        {
            Name "sharpen v3 pass"
            CGPROGRAM
            #include "CGInclude/SharpenV3.cginc"
            #pragma vertex Vert_Sharpen
            #pragma fragment Frag_Sharpen
            ENDCG
        }

        // 36 contrast composite pass
        Pass
        {
            Name "contrast composite pass"
            CGPROGRAM
            #include "CGInclude/ContrastComposite.cginc"
            #pragma vertex vert
            #pragma fragment frag
            ENDCG
        }

        // 37 simple separable blur
        Pass
        {
            Name "simple separable blur"
            CGPROGRAM
            #include "CGInclude/SimpleSeparableBlur.cginc"
            #pragma vertex vert
            #pragma fragment frag
            ENDCG
        }

        // 38 effect glitch rgp split horizontal
        Pass
        {
            Name "effect glitch rgp split horizontal"
            CGPROGRAM
            #include "CGInclude/EffectGlitchRGBSplit.cginc"
            #pragma vertex VertDefault
            #pragma fragment Frag_Horizontal
            ENDCG
        }

        // 39 effect glitch rgp split vertical
        Pass
        {
            Name "effect glitch rgp split vertical"
            CGPROGRAM
            #include "CGInclude/EffectGlitchRGBSplit.cginc"
            #pragma vertex VertDefault
            #pragma fragment Frag_Vertical
            ENDCG
        }

        // 40 effect glitch rgp split horizontal vertical
        Pass
        {
            Name "effect glitch rgp split horizontal vertical"
            CGPROGRAM
            #include "CGInclude/EffectGlitchRGBSplit.cginc"
            #pragma vertex VertDefault
            #pragma fragment Frag_Horizontal_Vertical
            ENDCG
        }

        // 41 hard edge outline stencil pass
        Pass
        {
            Name "hard edge outline stencil pass"
            Stencil
            {
                Ref [_HardRefValue]
                Comp Equal
            }

            CGPROGRAM
            #pragma vertex vert_min
            #pragma fragment frag
            #include "CGInclude/EffectStack.cginc"

            // sampler2D _MainTex;
            fixed4 _HardStencilColor;

            fixed4 frag (v2f_mini i) : SV_Target
            {
                return _HardStencilColor;
            }
            ENDCG
        }

        // 42 soft edge outline stencil pass
        Pass
        {
            Name "soft edge outline stencil pass"
            Stencil
            {
                Ref [_SoftRefValue]
                Comp Equal
            }

            CGPROGRAM
            #pragma vertex vert_min
            #pragma fragment frag
            #include "CGInclude/EffectStack.cginc"

            // sampler2D _MainTex;
            fixed4 _SoftStencilColor;

            fixed4 frag(v2f_mini i) : SV_Target
            {
                return _SoftStencilColor;
            }
            ENDCG
        }

        // 43 Screen Distortion pass
        Pass
        {
            Name "Screen Distortion pass"
            CGPROGRAM
            #include "CGInclude/ScreenDistortion.cginc"
            #pragma vertex vert
            #pragma fragment frag
            ENDCG
        }

        // 44 EdgeDetection (Low) (smaa)
        Pass
        {
            CGPROGRAM

                #pragma vertex VertEdge
                #pragma fragment FragEdge
                #define SMAA_PRESET_LOW
                #include "CGInclude/SubpixelMorphologicalAntialiasingBridge.cginc"

            ENDCG
        }

        // 45 EdgeDetection (Medium) (smaa)
        Pass
        {
            CGPROGRAM

                #pragma vertex VertEdge
                #pragma fragment FragEdge
                #define SMAA_PRESET_MEDIUM
                #include "CGInclude/SubpixelMorphologicalAntialiasingBridge.cginc"

            ENDCG
        }

        // 46 EdgeDetection (High) (smaa)
        Pass
        {
            CGPROGRAM

                #pragma vertex VertEdge
                #pragma fragment FragEdge
                #define SMAA_PRESET_HIGH
                #include "CGInclude/SubpixelMorphologicalAntialiasingBridge.cginc"

            ENDCG
        }

        // 47 Blend Weights Calculation (Low) (smaa)
        Pass
        {
            CGPROGRAM

                #pragma vertex VertBlend
                #pragma fragment FragBlend
                #define SMAA_PRESET_LOW
                #include "CGInclude/SubpixelMorphologicalAntialiasingBridge.cginc"

            ENDCG
        }

        // 48 Blend Weights Calculation (Medium) (smaa)
        Pass
        {
            CGPROGRAM

                #pragma vertex VertBlend
                #pragma fragment FragBlend
                #define SMAA_PRESET_MEDIUM
                #include "CGInclude/SubpixelMorphologicalAntialiasingBridge.cginc"

            ENDCG
        }

        // 49 Blend Weights Calculation (High) (smaa)
        Pass
        {
            CGPROGRAM

                #pragma vertex VertBlend
                #pragma fragment FragBlend
                #define SMAA_PRESET_HIGH
                #include "CGInclude/SubpixelMorphologicalAntialiasingBridge.cginc"

            ENDCG
        }

        // 50 Neighborhood Blending (smaa) (Keep for reference.)
        Pass
        {
            COLORMASK 0
            // CGPROGRAM

            //     #pragma vertex VertNeighbor
            //     #pragma fragment FragNeighbor
            //     #include "CGInclude/SubpixelMorphologicalAntialiasingBridge.cginc"

            // ENDCG
        }
    }

    FallBack "Unlit/Texture"
}
