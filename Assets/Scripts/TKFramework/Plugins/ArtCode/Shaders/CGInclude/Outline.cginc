#ifndef __OUTLINE_CGINC_INCLUDED__
#define __OUTLINE_CGINC_INCLUDED__

sampler2D _MainTex;
half4 _MainTex_TexelSize;
sampler2D _OutlineCulledRT;
half4 _OutlineCulledRT_TexelSize;
sampler2D _BlurMap;
half4 _BlurMap_TexelSize;
fixed4 _OutlineColor;
fixed _OutlineIntensity;
fixed _OutlineThreshold;
fixed2 _BlurOffset;
fixed4 _HighlightColor;
fixed _HighlightIntensity;

struct AttributesOutline
{
    float4 vertex : POSITION;
    half2 texcoord : TEXCOORD0;
};

struct VaryingsOutline
{
    float4 position : SV_POSITION;
    half2 uv[9] : TEXCOORD0;
};

struct VaryingsOutlineFast
{
    float4 position : SV_POSITION;
    half2 uv[5] : TEXCOORD0;
};

struct VaryingsOutlineBlur
{
    float4 vertex : SV_POSITION;
    half2 uv : TEXCOORD0;
    half2 offsets[4] : TEXCOORD1;
};

VaryingsOutline VertHardOutline(AttributesOutline v)
{
    VaryingsOutline o;
    o.position = UnityObjectToClipPos(v.vertex);

    half2 uv = v.texcoord;

    // ������Χ9�����ص����������
    o.uv[0] = uv + _OutlineCulledRT_TexelSize.xy * half2(-1, -1);
    o.uv[1] = uv + _OutlineCulledRT_TexelSize.xy * half2(0, -1);
    o.uv[2] = uv + _OutlineCulledRT_TexelSize.xy * half2(1, -1);
    o.uv[3] = uv + _OutlineCulledRT_TexelSize.xy * half2(-1, 0);
    o.uv[4] = uv + _OutlineCulledRT_TexelSize.xy * half2(0, 0);
    o.uv[5] = uv + _OutlineCulledRT_TexelSize.xy * half2(1, 0);
    o.uv[6] = uv + _OutlineCulledRT_TexelSize.xy * half2(-1, 1);
    o.uv[7] = uv + _OutlineCulledRT_TexelSize.xy * half2(0, 1);
    o.uv[8] = uv + _OutlineCulledRT_TexelSize.xy * half2(1, 1);

    return o;
}

VaryingsOutline VertSoftOutline(AttributesOutline v)
{
    VaryingsOutline o;
    o.position = UnityObjectToClipPos(v.vertex);

    half2 uv = v.texcoord;

    // ������Χ9�����ص����������
    o.uv[0] = uv + _BlurMap_TexelSize.xy * half2(-1, -1);
    o.uv[1] = uv + _BlurMap_TexelSize.xy * half2(0, -1);
    o.uv[2] = uv + _BlurMap_TexelSize.xy * half2(1, -1);
    o.uv[3] = uv + _BlurMap_TexelSize.xy * half2(-1, 0);
    o.uv[4] = uv + _BlurMap_TexelSize.xy * half2(0, 0);
    o.uv[5] = uv + _BlurMap_TexelSize.xy * half2(1, 0);
    o.uv[6] = uv + _BlurMap_TexelSize.xy * half2(-1, 1);
    o.uv[7] = uv + _BlurMap_TexelSize.xy * half2(0, 1);
    o.uv[8] = uv + _BlurMap_TexelSize.xy * half2(1, 1);

    return o;
}

VaryingsOutlineFast VertHardOutlineFast(AttributesOutline v)
{
    VaryingsOutlineFast o;
    o.position = UnityObjectToClipPos(v.vertex);

    half2 uv = v.texcoord;

	//������Χ4�����ص����������
    o.uv[0] = uv + _OutlineCulledRT_TexelSize.xy * half2(-1, -1);
    o.uv[1] = uv + _OutlineCulledRT_TexelSize.xy * half2(1, -1);
    o.uv[2] = uv + _OutlineCulledRT_TexelSize.xy * half2(-1, 1);
    o.uv[3] = uv + _OutlineCulledRT_TexelSize.xy * half2(1, 1);
    o.uv[4] = uv;

    return o;
}

VaryingsOutlineFast VertHardOutlineFastAlphaVersion(AttributesOutline v)
{
    VaryingsOutlineFast o;
    o.position = UnityObjectToClipPos(v.vertex);

    half2 uv = v.texcoord;

	//������Χ4�����ص����������
    o.uv[0] = uv + _MainTex_TexelSize.xy * half2(-1, -1);
    o.uv[1] = uv + _MainTex_TexelSize.xy * half2(1, -1);
    o.uv[2] = uv + _MainTex_TexelSize.xy * half2(-1, 1);
    o.uv[3] = uv + _MainTex_TexelSize.xy * half2(1, 1);
    o.uv[4] = uv;

    return o;
}

VaryingsOutlineFast VertSoftOutlineFast(AttributesOutline v)
{
    VaryingsOutlineFast o;
    o.position = UnityObjectToClipPos(v.vertex);

    half2 uv = v.texcoord;

	//������Χ4�����ص����������
    o.uv[0] = uv + _BlurMap_TexelSize.xy * half2(-1, -1);
    o.uv[1] = uv + _BlurMap_TexelSize.xy * half2(1, -1);
    o.uv[2] = uv + _BlurMap_TexelSize.xy * half2(-1, 1);
    o.uv[3] = uv + _BlurMap_TexelSize.xy * half2(1, 1);
    o.uv[4] = uv;

    return o;
}

VaryingsOutlineBlur VertOutlineBlur(AttributesOutline v)
{
    VaryingsOutlineBlur o;
    o.vertex = UnityObjectToClipPos(v.vertex);

    o.uv = v.texcoord.xy;
    o.offsets[0] = fixed2(_BlurOffset.x, _BlurOffset.y);
    o.offsets[1] = fixed2(-_BlurOffset.x, _BlurOffset.y);
    o.offsets[2] = fixed2(_BlurOffset.x, -_BlurOffset.y);
    o.offsets[3] = fixed2(-_BlurOffset.x, -_BlurOffset.y);

    return o;
}

// Soble��Ե���
half SobelHard(VaryingsOutline i)
{
    //Sobel����(�ᡢ��)
	/*const half Gx[9] = { -1,  0,  1,
						   -2,  0,  2,
						   -1,  0,  1 };
	  const half Gy[9] = { -1, -2, -1,
						    0,  0,  0,
							1,  2,  1 };*/

	//Sobel����(�Խ���)
    const half Gx[9] =
    {
        0, -1, -2,
	    1,  0, -1,
		2,  1,  0
    };
    const half Gy[9] =
    {
        -2, -1,  0,
		-1,  0,  1,
		 0,  1,  2
    };

    half edgeX = 0;
    half edgeY = 0;
    half luminance;
    for (int it = 0; it < 9; it++)
    {
        // Ӳ���ʹ��Rͨ����Ϊ���Ƚ��м���
        luminance = tex2D(_OutlineCulledRT, i.uv[it]).r;
        edgeX += luminance * Gx[it];
        edgeY += luminance * Gy[it];
    }

    half edge = 1 - abs(edgeX) - abs(edgeY);
    half culled = tex2D(_OutlineCulledRT, i.uv[4]).r;
    edge += step(0.618, culled) * (1 - edge);

    return edge;
}

half SobelSoft(VaryingsOutline i)
{
    //Sobel����(�ᡢ��)
	/*const half Gx[9] = { -1,  0,  1,
						   -2,  0,  2,
						   -1,  0,  1 };
	  const half Gy[9] = { -1, -2, -1,
						    0,  0,  0,
							1,  2,  1 };*/

	//Sobel����(�Խ���)
    const half Gx[9] =
    {
        0, -1, -2,
	    1, 0, -1,
		2, 1, 0
    };
    const half Gy[9] =
    {
        -2, -1, 0,
		-1, 0, 1,
		 0, 1, 2
    };

    half edgeX = 0;
    half edgeY = 0;
    half luminance;
    for (int it = 0; it < 9; it++)
    {
        // �����ʹ��Gͨ����Ϊ���Ƚ��м���
        luminance = tex2D(_BlurMap, i.uv[it]).g;
        edgeX += luminance * Gx[it];
        edgeY += luminance * Gy[it];
    }

    half edge = 1 - abs(edgeX) - abs(edgeY);
    half culled = tex2D(_OutlineCulledRT, i.uv[4]).g;
    edge += step(0.618, culled) * (1 - edge);

    return edge;
}

// Robert��Ե���
half RobertHard(VaryingsOutlineFast i)
{
	//Robert����
    const half Gx[4] =
    {
        -2,  0,
		 0,  2
    };
    const half Gy[4] =
    {
        0, -2,
		2,  0
    };

    half edgeX = 0;
    half edgeY = 0;
    half luminance;

    //for (int it = 0; it < 4; it++)
    //{
    //    // Ӳ���ʹ��Rͨ����Ϊ���Ƚ��м���
    //    luminance = tex2D(_OutlineCulledRT, i.uv[it]).r;
    //    edgeX += luminance * Gx[it];
    //    edgeY += luminance * Gy[it];
    //}

    // Ӳ���ʹ��Rͨ����Ϊ���Ƚ��м���
    luminance = tex2D(_OutlineCulledRT, i.uv[0]).r;
    edgeX += luminance * Gx[0];
    edgeY += luminance * Gy[0];
    luminance = tex2D(_OutlineCulledRT, i.uv[1]).r;
    edgeX += luminance * Gx[1];
    edgeY += luminance * Gy[1];
    luminance = tex2D(_OutlineCulledRT, i.uv[2]).r;
    edgeX += luminance * Gx[2];
    edgeY += luminance * Gy[2];
    luminance = tex2D(_OutlineCulledRT, i.uv[3]).r;
    edgeX += luminance * Gx[3];
    edgeY += luminance * Gy[3];

    half edge = 1 - abs(edgeX) - abs(edgeY);
    half culled = tex2D(_OutlineCulledRT, i.uv[4]).r;
    edge += step(0.618, culled) * (1 - edge);

    return edge;
}

// Robert��Ե���(ʹ��Alpha��Ϊ���Mask)
half RobertHardAlphaVersion(VaryingsOutlineFast i)
{
    //Robert����
    const half Gx[4] =
    {
        -2, 0,
		 0, 2
    };
    const half Gy[4] =
    {
        0, -2,
		2, 0
    };

    half edgeX = 0;
    half edgeY = 0;
    half luminance;

    // Ӳ���ʹ��aͨ����Ϊ���Ƚ��м���
    luminance = 1 - step(0.999, tex2D(_MainTex, i.uv[0]).a);
    edgeX += luminance * Gx[0];
    edgeY += luminance * Gy[0];

    luminance = 1 - step(0.999, tex2D(_MainTex, i.uv[1]).a);
    edgeX += luminance * Gx[1];
    edgeY += luminance * Gy[1];

    luminance = 1 - step(0.999, tex2D(_MainTex, i.uv[2]).a);
    edgeX += luminance * Gx[2];
    edgeY += luminance * Gy[2];

    luminance = 1 - step(0.999, tex2D(_MainTex, i.uv[3]).a);
    edgeX += luminance * Gx[3];
    edgeY += luminance * Gy[3];

    half edge = 1 - abs(edgeX) - abs(edgeY);
    half culled = 1 - step(0.999, tex2D(_MainTex, i.uv[4]).a);
    edge += step(0.618, culled) * (1 - edge);

    return edge;
}

half RobertSoft(VaryingsOutlineFast i)
{
	//Robert����
    const half Gx[4] =
    {
        -2, 0,
		 0, 2
    };
    const half Gy[4] =
    {
        0, -2,
		2, 0
    };

    half edgeX = 0;
    half edgeY = 0;
    half luminance;

    //for (int it = 0; it < 4; it++)
    //{
    //    // �����ʹ��Gͨ����Ϊ���Ƚ��м���
    //    luminance = tex2D(_OutlineCulledRT, i.uv[it]).g;
    //    edgeX += luminance * Gx[it];
    //    edgeY += luminance * Gy[it];
    //}

    // �����ʹ��Gͨ����Ϊ���Ƚ��м���
    luminance = tex2D(_BlurMap, i.uv[0]).g;
    edgeX += luminance * Gx[0];
    edgeY += luminance * Gy[0];
    luminance = tex2D(_BlurMap, i.uv[1]).g;
    edgeX += luminance * Gx[1];
    edgeY += luminance * Gy[1];
    luminance = tex2D(_BlurMap, i.uv[2]).g;
    edgeX += luminance * Gx[2];
    edgeY += luminance * Gy[2];
    luminance = tex2D(_BlurMap, i.uv[3]).g;
    edgeX += luminance * Gx[3];
    edgeY += luminance * Gy[3];

    half edge = 1 - abs(edgeX) - abs(edgeY);
    half culled = tex2D(_OutlineCulledRT, i.uv[4]).g;
    edge += step(0.618, culled) * (1 - edge);

    return edge;
}

fixed4 FragHardOutline(VaryingsOutline i) : SV_Target
{
    // �����ݶ�ֵ��edgeԽС��Ƭ��Խ�п����Ǹ���Ե��
    half edge = SobelHard(i);
    edge += saturate(_OutlineThreshold) * (1 - edge);
    return lerp(_OutlineColor * _OutlineIntensity, tex2D(_MainTex, i.uv[4]), edge);
}

fixed4 FragSoftOutline(VaryingsOutline i) : SV_Target
{
    // �����ݶ�ֵ��edgeԽС��Ƭ��Խ�п����Ǹ���Ե��
    half edge = SobelSoft(i);
    return lerp(_HighlightColor * _HighlightIntensity, tex2D(_MainTex, i.uv[4]), edge);
}

fixed4 FragHardOutlineFast(VaryingsOutlineFast i) : SV_Target
{
    // �����ݶ�ֵ��edgeԽС��Ƭ��Խ�п����Ǹ���Ե��
    half edge = RobertHard(i);
	edge += saturate(_OutlineThreshold) * (1 - edge);
    return lerp(_OutlineColor * _OutlineIntensity, tex2D(_MainTex, i.uv[4]), edge);
}

// �����mask����Alphaͨ��, ���Ż�һ��StencilMask��һ��Blit
fixed4 FragHardOutlineFastAlphaVersion(VaryingsOutlineFast i) : SV_Target
{
    // �����ݶ�ֵ��edgeԽС��Ƭ��Խ�п����Ǹ���Ե��
    half edge = RobertHardAlphaVersion(i);
    edge += saturate(_OutlineThreshold) * (1 - edge);
    return lerp(_OutlineColor * _OutlineIntensity, tex2D(_MainTex, i.uv[4]), edge);
}

fixed4 FragSoftOutlineFast(VaryingsOutlineFast i) : SV_Target
{
    // �����ݶ�ֵ��edgeԽС��Ƭ��Խ�п����Ǹ���Ե��
    half edge = RobertSoft(i);
    return lerp(_HighlightColor * _HighlightIntensity, tex2D(_MainTex, i.uv[4]), edge);
}

fixed4 FragOutlineBlur(VaryingsOutlineBlur i) : SV_Target
{
    // Soft���ʹ��Gͨ����Mask
    fixed4 o = tex2D(_MainTex, i.uv);
    o.g += tex2D(_MainTex, i.uv + i.offsets[0]).g;
    o.g += tex2D(_MainTex, i.uv + i.offsets[1]).g;
    o.g += tex2D(_MainTex, i.uv + i.offsets[2]).g;
    o.g += tex2D(_MainTex, i.uv + i.offsets[3]).g;
    o.g *= 0.2f;

    return o;
}
#endif // __OUTLINE_CGINC_INCLUDED__