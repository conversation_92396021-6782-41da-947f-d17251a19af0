using UnityEngine;
using System.Collections;
using System;

[Serializable]
public class EffectNoiseFog : EffectComponent
{
    [Range(0.1f, 3)]
    public float fogDensity = 1;
    public Color fogColor = Color.white;
    public float fogStartYAxis = 0;
    public float fogEndYAxis = 2;
    public float fogXSpeed = 0;
    public float fogYSpeed = 0;
    [Range(1, 3)]
    public float noiseAmount = 1;
    public Texture noiseTexture;

    private int m_ID_clipToWorld;
    private int m_ID_fogDensity;
    private int m_ID_fogColor;
    private int m_ID_fogStartYAxis;
    private int m_ID_fogEndYAxis;
    private int m_ID_fogXSpeed;
    private int m_ID_fogYSpeed;
    private int m_ID_noiseFogTexture;
    private int m_ID_noiseFogAmount;

    public Camera Camera { get; set; }

    public override void Init()
    {
        m_ID_clipToWorld = Shader.PropertyToID("_ClipToWorld");
        m_ID_fogColor = Shader.PropertyToID("_FogColor");
        m_ID_fogDensity = Shader.PropertyToID("_FogDensity");
        m_ID_fogStartYAxis = Shader.PropertyToID("_FogStart");
        m_ID_fogEndYAxis = Shader.PropertyToID("_FogEnd");
        m_ID_noiseFogAmount = Shader.PropertyToID("_NoiseFogAmount");
        m_ID_fogXSpeed = Shader.PropertyToID("_FogXSpeed");
        m_ID_fogYSpeed = Shader.PropertyToID("_FogYSpeed");
        m_ID_noiseFogTexture = Shader.PropertyToID("_NoiseFogTex");
        RequireDepth = true;
    }

    public override void Prepare(Material mat, RenderTexture quarterTex)
    {
        if (IsOn && Camera)
        {
            m_IsOnForDirtyDetection = IsOn;
            mat.EnableKeyword("_APPLYFOGEFFECT");

            // algorithm referenced by https://gamedev.stackexchange.com/questions/131978/shader-reconstructing-position-from-depth-in-vr-through-projection-matrix
            // shader algorithm referenced by CandyCat1992
            var p = GL.GetGPUProjectionMatrix(Camera.projectionMatrix, false);
            // Undo some of the weird projection-y things so it's more intuitive to work with.
            p[2, 3] = p[3, 2] = 0f;
            p[3, 3] = 1f;
            // I don't know why this worked, maybe this is kind of magic.
            Matrix4x4 clipToWorld = Matrix4x4.Inverse(p * Camera.worldToCameraMatrix) * Matrix4x4.TRS(new Vector3(0, 0, -p[2, 2]), Quaternion.identity, Vector3.one);

            mat.SetMatrix(m_ID_clipToWorld, clipToWorld);
            mat.SetFloat(m_ID_fogDensity, fogDensity);
            mat.SetFloat(m_ID_fogStartYAxis, fogStartYAxis);
            mat.SetFloat(m_ID_fogEndYAxis, fogEndYAxis);
            mat.SetFloat(m_ID_fogXSpeed, fogXSpeed);
            mat.SetFloat(m_ID_fogYSpeed, fogYSpeed);
            mat.SetColor(m_ID_fogColor, fogColor);
            mat.SetFloat(m_ID_noiseFogAmount, noiseAmount);
            mat.SetTexture(m_ID_noiseFogTexture, noiseTexture);
        }
    }

    public override void Clear(Material mat)
    {
        mat.DisableKeyword("_APPLYFOGEFFECT");
        m_IsOnForDirtyDetection = IsOn;
    }
}
