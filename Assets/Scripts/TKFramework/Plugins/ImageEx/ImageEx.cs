using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

/// <summary>
/// 扩展UGUI的Image控件，支持Alpha通道分离的sprite。
/// 同时兼容普通的sprite
/// 在打IOS包的前，脚本自动把prefab上的Image组件替换为ImageEx
/// </summary>
[AddComponentMenu("UI/ImageEx", 10)]
public class ImageEx : Image
{
    static Material ms_splited_alpha_material = null;

    public enum AlphaUnpackDirection
    {
        None = 0,
        Vertical,
        Horizontal,
    }

    public override float preferredWidth
    {
        get
        {
            float width = base.preferredWidth;
            if (alphaUnpackDirection == AlphaUnpackDirection.Horizontal)
                return width / 2;
            else
                return width;
        }
    }

    public override float preferredHeight
    {
        get
        {
            float height = base.preferredHeight;
            if (alphaUnpackDirection == AlphaUnpackDirection.Vertical)
                return height / 2;
            else
                return height;
        }
    }

    public AlphaUnpackDirection alphaUnpackDirection
    {
        get
        {
            if (overrideSprite != null)
            {
                if (overrideSprite.rect.size.x < 2
                    || overrideSprite.rect.size.y < 2)
                {
                    return AlphaUnpackDirection.None;
                }

                if (overrideSprite.pivot.x < 1)
                {
                    return AlphaUnpackDirection.Horizontal;
                }
                else if (overrideSprite.pivot.x >= overrideSprite.rect.size.x - 1)
                {
                    return AlphaUnpackDirection.Vertical;
                }
            }
            return AlphaUnpackDirection.None;
        }
    }

    //alpha通道UV的偏移量(水平/垂直)
    public float alphaPixelOffset
    {
        get
        {
            float offset = 0f;
            if (alphaUnpackDirection == AlphaUnpackDirection.Horizontal)
            {
                if ((int)overrideSprite.textureRect.width % 2 == 1)
                    offset = 1.0f / mainTexture.width;
            }
            else if (alphaUnpackDirection == AlphaUnpackDirection.Vertical)
            {
                if ((int)overrideSprite.textureRect.height % 2 == 1)
                    offset = 1.0f / mainTexture.height;
            }
            return offset;
        }
    }


    public override Material defaultMaterial
    {
        get
        {
            if (alphaUnpackDirection != AlphaUnpackDirection.None)
            {
                if (ms_splited_alpha_material == null)
                    ms_splited_alpha_material = Resources.Load<Material>("ui_split_alpha");

                return ms_splited_alpha_material;
            }
            else
            {
                return base.defaultMaterial;
            }
        }
    }

    private Vector4 GetDrawingDimensions(bool shouldPreserveAspect)
    {
        var padding = overrideSprite == null ? Vector4.zero : UnityEngine.Sprites.DataUtility.GetPadding(overrideSprite);
        var size = overrideSprite == null ? Vector2.zero : new Vector2(overrideSprite.rect.width, overrideSprite.rect.height);

        Rect r = GetPixelAdjustedRect();
        // Debug.Log(string.Format("r:{2}, size:{0}, padding:{1}", size, padding, r));

        int spriteW;// = Mathf.RoundToInt(size.x);
        int spriteH;// = Mathf.RoundToInt(size.y);

        if (alphaUnpackDirection == AlphaUnpackDirection.Horizontal)
        {
            size.x = size.x / 2 - alphaPixelOffset;
        }
        else if (alphaUnpackDirection == AlphaUnpackDirection.Vertical)
        {
            size.y = size.y / 2 - alphaPixelOffset;
        }

        spriteW = Mathf.RoundToInt(size.x);
        spriteH = Mathf.RoundToInt(size.y);

        var v = new Vector4(
                padding.x / spriteW,
                padding.y / spriteH,
                (spriteW - padding.z) / spriteW,
                (spriteH - padding.w) / spriteH);

        if (shouldPreserveAspect && size.sqrMagnitude > 0.0f)
        {
            var spriteRatio = size.x / size.y;
            var rectRatio = r.width / r.height;

            if (spriteRatio > rectRatio)
            {
                var oldHeight = r.height;
                r.height = r.width * (1.0f / spriteRatio);
                r.y += (oldHeight - r.height) * rectTransform.pivot.y;
            }
            else
            {
                var oldWidth = r.width;
                r.width = r.height * spriteRatio;
                r.x += (oldWidth - r.width) * rectTransform.pivot.x;
            }
        }

        v = new Vector4(
                r.x + r.width * v.x,
                r.y + r.height * v.y,
                r.x + r.width * v.z,
                r.y + r.height * v.w
                );

        return v;
    }

    protected override void OnPopulateMesh(VertexHelper toFill)
    {
        if (alphaUnpackDirection == AlphaUnpackDirection.None
            || overrideSprite == null)
        {
            //没有做alpha通道分离，直接使用基类Image的方法函数
            base.OnPopulateMesh(toFill);
            return;
        }

        if (type == Type.Sliced)
        {
            if (GenerateSlicedSprite(toFill) == false)
            {
                GenerateSimpleSprite(toFill, preserveAspect);
            }
        }
        else if (type == Type.Simple)
        {
            GenerateSimpleSprite(toFill, preserveAspect);
        }
        else if (type == Type.Tiled)
        {
            GenerateTiledSprite(toFill);
        }
        else
        {
            base.OnPopulateMesh(toFill);
            ModifyAlphaUV(toFill);
        }
    }

    void GenerateTiledSprite(VertexHelper toFill)
    {
        Vector4 outer, inner, border;
        Vector2 spriteSize;

        float _alphaPixelOffset = alphaPixelOffset;

        if (overrideSprite != null)
        {
            outer = UnityEngine.Sprites.DataUtility.GetOuterUV(overrideSprite);
            inner = UnityEngine.Sprites.DataUtility.GetInnerUV(overrideSprite);
            border = overrideSprite.border;
            spriteSize = overrideSprite.rect.size;

            if (alphaUnpackDirection == AlphaUnpackDirection.Horizontal)
            {
                if (_alphaPixelOffset > 0.0f)
                    spriteSize.x = (spriteSize.x - 1) / 2;
                else
                    spriteSize.x = spriteSize.x / 2;
            }
            else if (alphaUnpackDirection == AlphaUnpackDirection.Vertical)
            {
                if (_alphaPixelOffset > 0.0f)
                    spriteSize.y = (spriteSize.y - 1) / 2;
                else
                    spriteSize.y = spriteSize.y / 2;
            }

            if (alphaUnpackDirection == AlphaUnpackDirection.Horizontal)
            {
                if (_alphaPixelOffset > 0.0f)
                    border.z -= (overrideSprite.textureRect.width + 1) / 2;
                else
                    border.z -= overrideSprite.textureRect.width / 2;
            }
            else if (alphaUnpackDirection == AlphaUnpackDirection.Vertical)
            {
                if (_alphaPixelOffset > 0.0f)
                    border.w -= (overrideSprite.textureRect.height + 1) / 2;
                else
                    border.w -= overrideSprite.textureRect.height / 2;
            }

            Vector2 min_uv0 = new Vector2(outer.x, outer.y);
            Vector2 max_uv0 = new Vector2(outer.z, outer.w);

            if (alphaUnpackDirection == AlphaUnpackDirection.Horizontal)
            {
                float width_u = (max_uv0.x - min_uv0.x) / 2 - _alphaPixelOffset * 0.5f;

                outer.z = outer.x + width_u;

                //outer1 = outer;
                //outer1.x += width_u + _alphaPixelOffset * 1;
                //outer1.z += width_u + _alphaPixelOffset * 1;

                //inner1 = inner;
                //inner1.x += width_u + _alphaPixelOffset * 1;
                //inner1.z += width_u + _alphaPixelOffset * 1;
            }
            else
            {
                float height_v = (max_uv0.y - min_uv0.y) / 2 - _alphaPixelOffset * 0.5f;

                outer.w = outer.y + height_v;

                //outer1 = outer;
                //outer1.y += height_v + _alphaPixelOffset * 1;
                //outer1.w += height_v + _alphaPixelOffset * 1;

                //inner1 = inner;
                //inner1.y += height_v + _alphaPixelOffset * 1;
                //inner1.w += height_v + _alphaPixelOffset * 1;
            }
        }
        else
        {
            outer = Vector4.zero;
            inner = Vector4.zero;
            border = Vector4.zero;
            spriteSize = Vector2.one * 100;
        }

        Rect rect = GetPixelAdjustedRect();
        float tileWidth = (spriteSize.x - border.x - border.z) / pixelsPerUnit;
        float tileHeight = (spriteSize.y - border.y - border.w) / pixelsPerUnit;
        border = GetAdjustedBorders(border / pixelsPerUnit, rect);

        var uvMin = new Vector2(inner.x, inner.y);
        var uvMax = new Vector2(inner.z, inner.w);

        var v = UIVertex.simpleVert;
        v.color = color;

        // Min to max max range for tiled region in coordinates relative to lower left corner.
        float xMin = border.x;
        float xMax = rect.width - border.z;
        float yMin = border.y;
        float yMax = rect.height - border.w;

        toFill.Clear();
        var clipped = uvMax;

        // if either with is zero we cant tile so just assume it was the full width.
        if (tileWidth == 0)
            tileWidth = xMax - xMin;

        if (tileHeight == 0)
            tileHeight = yMax - yMin;

        if (fillCenter)
        {
            for (float y1 = yMin; y1 < yMax; y1 += tileHeight)
            {
                float y2 = y1 + tileHeight;
                if (y2 > yMax)
                {
                    clipped.y = uvMin.y + (uvMax.y - uvMin.y) * (yMax - y1) / (y2 - y1);
                    y2 = yMax;
                }

                clipped.x = uvMax.x;
                for (float x1 = xMin; x1 < xMax; x1 += tileWidth)
                {
                    float x2 = x1 + tileWidth;
                    if (x2 > xMax)
                    {
                        clipped.x = uvMin.x + (uvMax.x - uvMin.x) * (xMax - x1) / (x2 - x1);
                        x2 = xMax;
                    }
                    AddQuad(toFill, new Vector2(x1, y1) + rect.position, new Vector2(x2, y2) + rect.position, color, uvMin, clipped);
                }
            }
        }

        if (hasBorder)
        {
            clipped = uvMax;
            for (float y1 = yMin; y1 < yMax; y1 += tileHeight)
            {
                float y2 = y1 + tileHeight;
                if (y2 > yMax)
                {
                    clipped.y = uvMin.y + (uvMax.y - uvMin.y) * (yMax - y1) / (y2 - y1);
                    y2 = yMax;
                }
                AddQuad(toFill,
                    new Vector2(0, y1) + rect.position,
                    new Vector2(xMin, y2) + rect.position,
                    color,
                    new Vector2(outer.x, uvMin.y),
                    new Vector2(uvMin.x, clipped.y));
                AddQuad(toFill,
                    new Vector2(xMax, y1) + rect.position,
                    new Vector2(rect.width, y2) + rect.position,
                    color,
                    new Vector2(uvMax.x, uvMin.y),
                    new Vector2(outer.z, clipped.y));
            }

            // Bottom and top tiled border
            clipped = uvMax;
            for (float x1 = xMin; x1 < xMax; x1 += tileWidth)
            {
                float x2 = x1 + tileWidth;
                if (x2 > xMax)
                {
                    clipped.x = uvMin.x + (uvMax.x - uvMin.x) * (xMax - x1) / (x2 - x1);
                    x2 = xMax;
                }
                AddQuad(toFill,
                    new Vector2(x1, 0) + rect.position,
                    new Vector2(x2, yMin) + rect.position,
                    color,
                    new Vector2(uvMin.x, outer.y),
                    new Vector2(clipped.x, uvMin.y));
                AddQuad(toFill,
                    new Vector2(x1, yMax) + rect.position,
                    new Vector2(x2, rect.height) + rect.position,
                    color,
                    new Vector2(uvMin.x, uvMax.y),
                    new Vector2(clipped.x, outer.w));
            }

            // Corners
            AddQuad(toFill,
                new Vector2(0, 0) + rect.position,
                new Vector2(xMin, yMin) + rect.position,
                color,
                new Vector2(outer.x, outer.y),
                new Vector2(uvMin.x, uvMin.y));
            AddQuad(toFill,
                new Vector2(xMax, 0) + rect.position,
                new Vector2(rect.width, yMin) + rect.position,
                color,
                new Vector2(uvMax.x, outer.y),
                new Vector2(outer.z, uvMin.y));
            AddQuad(toFill,
                new Vector2(0, yMax) + rect.position,
                new Vector2(xMin, rect.height) + rect.position,
                color,
                new Vector2(outer.x, uvMax.y),
                new Vector2(uvMin.x, outer.w));
            AddQuad(toFill,
                new Vector2(xMax, yMax) + rect.position,
                new Vector2(rect.width, rect.height) + rect.position,
                color,
                new Vector2(uvMax.x, uvMax.y),
                new Vector2(outer.z, outer.w));
        }

        if (alphaUnpackDirection == AlphaUnpackDirection.Horizontal)
        {
            float alpha_base_u = 1.0f / 2 + _alphaPixelOffset * 0.5f;


            for (int i = 0; i < toFill.currentVertCount; i++)
            {
                UIVertex vertex = new UIVertex();
                toFill.PopulateUIVertex(ref vertex, i);

                vertex.uv1 = new Vector2(vertex.uv0.x + alpha_base_u, vertex.uv0.y);

                toFill.SetUIVertex(vertex, i);
            }
        }
        else
        {
            float alpha_base_v = 1.0f / 2 + _alphaPixelOffset * 0.5f;


            for (int i = 0; i < toFill.currentVertCount; i++)
            {
                UIVertex vertex = new UIVertex();
                toFill.PopulateUIVertex(ref vertex, i);
                vertex.uv1 = new Vector2(vertex.uv0.x, vertex.uv0.y + alpha_base_v);

                toFill.SetUIVertex(vertex, i);
            }
        }
    }

    void ModifyAlphaUV(VertexHelper toFill)
    {
        var uv = (overrideSprite != null) ? UnityEngine.Sprites.DataUtility.GetOuterUV(overrideSprite) : Vector4.zero;

        Vector2 min_uv0 = new Vector2(uv.x, uv.y);
        Vector2 max_uv0 = new Vector2(uv.z, uv.w);

        float _alphaPixelOffset = alphaPixelOffset;

        if (alphaUnpackDirection == AlphaUnpackDirection.Horizontal)
        {
            float alpha_base_u = (max_uv0.x - min_uv0.x) / 2 + _alphaPixelOffset * 0.5f;

            UIVertex vertex = new UIVertex();
            for (int i = 0; i < toFill.currentVertCount; i++)
            {
                toFill.PopulateUIVertex(ref vertex, i);

                if (vertex.uv0.x > min_uv0.x)
                {
                    float x = min_uv0.x + (vertex.uv0.x - min_uv0.x) / 2.0f - _alphaPixelOffset * 0.5f;
                    vertex.uv0 = new Vector2(x, vertex.uv0.y);
                }

                vertex.uv1 = new Vector2(vertex.uv0.x + alpha_base_u, vertex.uv0.y);

                toFill.SetUIVertex(vertex, i);
            }
        }
        else
        {
            float alpha_base_v = (max_uv0.y - min_uv0.y) / 2 + _alphaPixelOffset * 0.5f;

            UIVertex vertex = new UIVertex();
            for (int i = 0; i < toFill.currentVertCount; i++)
            {
                toFill.PopulateUIVertex(ref vertex, i);

                if (vertex.uv0.y > min_uv0.y)
                {
                    float y = min_uv0.y + (vertex.uv0.y - min_uv0.y) / 2.0f - _alphaPixelOffset * 0.5f;
                    vertex.uv0 = new Vector2(vertex.uv0.x, y);
                }

                vertex.uv1 = new Vector2(vertex.uv0.x, vertex.uv0.y + alpha_base_v);

                toFill.SetUIVertex(vertex, i);
            }
        }
    }

    private static readonly Vector4 s_DefaultTangent = new Vector4(1.0f, 0.0f, 0.0f, -1.0f);
    private static readonly Vector3 s_DefaultNormal = Vector3.back;

    void GenerateSimpleSprite(VertexHelper vh, bool lPreserveAspect)
    {
        var uv = (overrideSprite != null) ? UnityEngine.Sprites.DataUtility.GetOuterUV(overrideSprite) : Vector4.zero;

        Vector2 min_uv0 = new Vector2(uv.x, uv.y);
        Vector2 max_uv0 = new Vector2(uv.z, uv.w);

        Vector2 min_uv1;
        Vector2 max_uv1;

        float _alphaPixelOffset = alphaPixelOffset;

        if (alphaUnpackDirection == AlphaUnpackDirection.Horizontal)
        {
            float width_u = (max_uv0.x - min_uv0.x) / 2 - _alphaPixelOffset;

            max_uv0.x = min_uv0.x + width_u;

            min_uv1 = new Vector2(min_uv0.x + width_u + _alphaPixelOffset * 2, min_uv0.y);
            max_uv1 = new Vector2(max_uv0.x + width_u + _alphaPixelOffset * 2, max_uv0.y);
        }
        else
        {
            float height_v = (max_uv0.y - min_uv0.y) / 2 - _alphaPixelOffset;

            max_uv0.y = min_uv0.y + height_v;

            min_uv1 = new Vector2(min_uv0.x, min_uv0.y + height_v + _alphaPixelOffset * 2);
            max_uv1 = new Vector2(max_uv0.x, max_uv0.y + height_v + _alphaPixelOffset * 2);
        }

        Vector4 v = GetDrawingDimensions(lPreserveAspect);

        var color32 = color;
        vh.Clear();
        vh.AddVert(new Vector3(v.x, v.y), color32, new Vector2(min_uv0.x, min_uv0.y), new Vector2(min_uv1.x, min_uv1.y), s_DefaultTangent, s_DefaultTangent);
        vh.AddVert(new Vector3(v.x, v.w), color32, new Vector2(min_uv0.x, max_uv0.y), new Vector2(min_uv1.x, max_uv1.y), s_DefaultTangent, s_DefaultTangent);
        vh.AddVert(new Vector3(v.z, v.w), color32, new Vector2(max_uv0.x, max_uv0.y), new Vector2(max_uv1.x, max_uv1.y), s_DefaultTangent, s_DefaultTangent);
        vh.AddVert(new Vector3(v.z, v.y), color32, new Vector2(max_uv0.x, min_uv0.y), new Vector2(max_uv1.x, min_uv1.y), s_DefaultTangent, s_DefaultTangent);

        vh.AddTriangle(0, 1, 2);
        vh.AddTriangle(2, 3, 0);
    }

    static readonly Vector2[] s_VertScratch = new Vector2[4];
    static readonly Vector2[] s_UVScratch = new Vector2[8];

    private bool GenerateSlicedSprite(VertexHelper toFill)
    {
        if (!hasBorder)
        {
            return false;
        }

        Vector4 outer, inner, padding, border;

        if (overrideSprite != null)
        {
            outer = UnityEngine.Sprites.DataUtility.GetOuterUV(overrideSprite);
            inner = UnityEngine.Sprites.DataUtility.GetInnerUV(overrideSprite);
            padding = UnityEngine.Sprites.DataUtility.GetPadding(overrideSprite);
            border = overrideSprite.border;

            if (alphaUnpackDirection == AlphaUnpackDirection.Horizontal)
            {
                border.z -= Mathf.Round(overrideSprite.textureRect.width / 2 + 0.1f);
            }
            else
            {
                border.w -= Mathf.Round(overrideSprite.textureRect.height / 2 + 0.1f);
            }
        }
        else
        {
            outer = Vector4.zero;
            inner = Vector4.zero;
            padding = Vector4.zero;
            border = Vector4.zero;
        }

        Rect rect = GetPixelAdjustedRect();
        border = GetAdjustedBorders(border / pixelsPerUnit, rect);
        padding = padding / pixelsPerUnit;

        s_VertScratch[0] = new Vector2(padding.x, padding.y);
        s_VertScratch[3] = new Vector2(rect.width - padding.z, rect.height - padding.w);

        s_VertScratch[1].x = border.x;
        s_VertScratch[1].y = border.y;
        s_VertScratch[2].x = rect.width - border.z;
        s_VertScratch[2].y = rect.height - border.w;

        for (int i = 0; i < 4; ++i)
        {
            s_VertScratch[i].x += rect.x;
            s_VertScratch[i].y += rect.y;
        }

        Vector2 min_uv0 = new Vector2(outer.x, outer.y);
        Vector2 max_uv0 = new Vector2(outer.z, outer.w);

        Vector4 outer1;
        Vector4 inner1;

        float _alphaPixelOffset = alphaPixelOffset;

        if (alphaUnpackDirection == AlphaUnpackDirection.Horizontal)
        {
            float width_u = (max_uv0.x - min_uv0.x) / 2 - _alphaPixelOffset * 0.5f;

            outer.z = outer.x + width_u;

            outer1 = outer;
            outer1.x += width_u + _alphaPixelOffset * 1;
            outer1.z += width_u + _alphaPixelOffset * 1;

            inner1 = inner;
            inner1.x += width_u + _alphaPixelOffset * 1;
            inner1.z += width_u + _alphaPixelOffset * 1;
        }
        else
        {
            float height_v = (max_uv0.y - min_uv0.y) / 2 - _alphaPixelOffset * 0.5f;

            outer.w = outer.y + height_v;

            outer1 = outer;
            outer1.y += height_v + _alphaPixelOffset * 1;
            outer1.w += height_v + _alphaPixelOffset * 1;

            inner1 = inner;
            inner1.y += height_v + _alphaPixelOffset * 1;
            inner1.w += height_v + _alphaPixelOffset * 1;
        }

        s_UVScratch[0] = new Vector2(outer.x, outer.y);
        s_UVScratch[1] = new Vector2(inner.x, inner.y);
        s_UVScratch[2] = new Vector2(inner.z, inner.w);
        s_UVScratch[3] = new Vector2(outer.z, outer.w);

        s_UVScratch[4] = new Vector2(outer1.x, outer1.y);
        s_UVScratch[5] = new Vector2(inner1.x, inner1.y);
        s_UVScratch[6] = new Vector2(inner1.z, inner1.w);
        s_UVScratch[7] = new Vector2(outer1.z, outer1.w);

        toFill.Clear();

        for (int x = 0; x < 3; ++x)
        {
            int x2 = x + 1;

            for (int y = 0; y < 3; ++y)
            {
                if (!fillCenter && x == 1 && y == 1)
                    continue;

                int y2 = y + 1;

                AddQuad(toFill,
                    new Vector2(s_VertScratch[x].x, s_VertScratch[y].y),
                    new Vector2(s_VertScratch[x2].x, s_VertScratch[y2].y),
                    color,
                    new Vector2(s_UVScratch[x].x, s_UVScratch[y].y),
                    new Vector2(s_UVScratch[x2].x, s_UVScratch[y2].y),
                    new Vector2(s_UVScratch[x + 4].x, s_UVScratch[y + 4].y),
                    new Vector2(s_UVScratch[x2 + 4].x, s_UVScratch[y2 + 4].y));
            }
        }

        return true;
    }

    Vector4 GetAdjustedBorders(Vector4 border, Rect rect)
    {
        for (int axis = 0; axis <= 1; axis++)
        {
            // If the rect is smaller than the combined borders, then there's not room for the borders at their normal size.
            // In order to avoid artefacts with overlapping borders, we scale the borders down to fit.
            float combinedBorders = border[axis] + border[axis + 2];
            if (rect.size[axis] < combinedBorders && combinedBorders != 0)
            {
                float borderScaleRatio = rect.size[axis] / combinedBorders;
                border[axis] *= borderScaleRatio;
                border[axis + 2] *= borderScaleRatio;
            }
        }
        return border;
    }

    static void AddQuad(VertexHelper vertexHelper, Vector3[] quadPositions, Color32 color, Vector3[] quadUVs)
    {
        int startIndex = vertexHelper.currentVertCount;

        for (int i = 0; i < 4; ++i)
            vertexHelper.AddVert(quadPositions[i], color, quadUVs[i]);

        vertexHelper.AddTriangle(startIndex, startIndex + 1, startIndex + 2);
        vertexHelper.AddTriangle(startIndex + 2, startIndex + 3, startIndex);
    }

    static void AddQuad(VertexHelper vertexHelper, Vector2 posMin, Vector2 posMax, Color32 color, Vector2 uvMin, Vector2 uvMax)
    {
        int startIndex = vertexHelper.currentVertCount;

        vertexHelper.AddVert(new Vector3(posMin.x, posMin.y, 0), color, new Vector2(uvMin.x, uvMin.y));
        vertexHelper.AddVert(new Vector3(posMin.x, posMax.y, 0), color, new Vector2(uvMin.x, uvMax.y));
        vertexHelper.AddVert(new Vector3(posMax.x, posMax.y, 0), color, new Vector2(uvMax.x, uvMax.y));
        vertexHelper.AddVert(new Vector3(posMax.x, posMin.y, 0), color, new Vector2(uvMax.x, uvMin.y));

        vertexHelper.AddTriangle(startIndex, startIndex + 1, startIndex + 2);
        vertexHelper.AddTriangle(startIndex + 2, startIndex + 3, startIndex);
    }

    static void AddQuad(VertexHelper vertexHelper, Vector2 posMin, Vector2 posMax, Color32 color, Vector2 uvMin, Vector2 uvMax, Vector2 uvMin1, Vector2 uvMax1)
    {
        int startIndex = vertexHelper.currentVertCount;

        vertexHelper.AddVert(new Vector3(posMin.x, posMin.y, 0), color, new Vector2(uvMin.x, uvMin.y), new Vector2(uvMin1.x, uvMin1.y), s_DefaultNormal, s_DefaultTangent);
        vertexHelper.AddVert(new Vector3(posMin.x, posMax.y, 0), color, new Vector2(uvMin.x, uvMax.y), new Vector2(uvMin1.x, uvMax1.y), s_DefaultNormal, s_DefaultTangent);
        vertexHelper.AddVert(new Vector3(posMax.x, posMax.y, 0), color, new Vector2(uvMax.x, uvMax.y), new Vector2(uvMax1.x, uvMax1.y), s_DefaultNormal, s_DefaultTangent);
        vertexHelper.AddVert(new Vector3(posMax.x, posMin.y, 0), color, new Vector2(uvMax.x, uvMin.y), new Vector2(uvMax1.x, uvMin1.y), s_DefaultNormal, s_DefaultTangent);

        vertexHelper.AddTriangle(startIndex, startIndex + 1, startIndex + 2);
        vertexHelper.AddTriangle(startIndex + 2, startIndex + 3, startIndex);
    }

    //protected  void OnPopulateMesh11(VertexHelper toFill)
    //{
    //    base.OnPopulateMesh(toFill);
    //    return;

    //    if (overrideSprite == null)
    //        return;


    //    List<UIVertex> vertexList = new List<UIVertex>();
    //    toFill.GetUIVertexStream(vertexList);

    //    //foreach(var vertex in vertexList)
    //    //{
    //    //    Debug.Log(vertex.uv0.ToString());
    //    //}

    //    //AlphaUnpackDirection alphaUnpackDirection = m_alphaUnpackDirection;

    //    //if (alphaUnpackDirection == AlphaUnpackDirection.Auto)
    //    //{
    //    //    var rect = (transform as RectTransform).rect;
    //    //    if (rect.width <= rect.height)
    //    //    {
    //    //        alphaUnpackDirection = AlphaUnpackDirection.Horizontal;
    //    //    }
    //    //    else
    //    //    {
    //    //        alphaUnpackDirection = AlphaUnpackDirection.Vertical;
    //    //    }
    //    //}

    //    //if (overrideSprite.pivot.x < 1)
    //    //    alphaUnpackDirection = AlphaUnpackDirection.Horizontal;
    //    //else
    //    //    alphaUnpackDirection = AlphaUnpackDirection.Vertical;

    //    //if(alphaUnpackDirection == AlphaUnpackDirection.Horizontal)

    //    Vector4 rect_uv = UnityEngine.Sprites.DataUtility.GetOuterUV(overrideSprite);
    //    Vector2 min_uv = new Vector2(rect_uv.x, rect_uv.y);
    //    Vector2 max_uv = new Vector2(rect_uv.z, rect_uv.w);

    //    if (alphaUnpackDirection == AlphaUnpackDirection.Horizontal)
    //    {
    //        float pixel_u = 0f;

    //        if((int)overrideSprite.textureRect.width % 2 == 1)
    //            pixel_u = 1.0f / mainTexture.width;

    //        float width_u = (max_uv.x - min_uv.x) / 2;

    //        for (int i = 0; i < vertexList.Count; i++)
    //        {
    //            var vertex = vertexList[i];

    //            if (vertex.uv0.x > min_uv.x)
    //            {
    //                float x = min_uv.x + (vertex.uv0.x - min_uv.x) / 2.0f - pixel_u;
    //                vertex.uv0 = new Vector2(x, vertex.uv0.y);
    //            }

    //            vertex.uv1 = new Vector2(vertex.uv0.x + width_u + pixel_u, vertex.uv0.y);
    //            vertexList[i] = vertex;
    //        }
    //    }
    //    else
    //    {
    //        float pixel_v = 0f;

    //        if ((int)overrideSprite.textureRect.height % 2 == 1)
    //            pixel_v = 1.0f / mainTexture.height;

    //        float width_v = (max_uv.y - min_uv.y) / 2;

    //        for (int i = 0; i < vertexList.Count; i++)
    //        {
    //            var vertex = vertexList[i];

    //            if (vertex.uv0.y > min_uv.y)
    //            {
    //                float y = min_uv.x + (vertex.uv0.y - min_uv.y) / 2.0f - pixel_v;
    //                vertex.uv0 = new Vector2(vertex.uv0.x, y);
    //            }

    //            vertex.uv1 = new Vector2(vertex.uv0.x, vertex.uv0.y + width_v + pixel_v);
    //            vertexList[i] = vertex;
    //        }
    //    }


    //    toFill.Clear();
    //    toFill.AddUIVertexTriangleStream(vertexList);
    //}

    public override void SetNativeSize()
    {
        Debug.Log("SetNativeSize");
        if (alphaUnpackDirection == AlphaUnpackDirection.None)
        {
            base.SetNativeSize();
            return;
        }

        if (overrideSprite != null)
        {
            float w;
            float h;

            if (alphaUnpackDirection == AlphaUnpackDirection.Horizontal)
            {
                //Alpha水平扩展
                if (alphaPixelOffset > 0.0f)
                    w = (overrideSprite.rect.width - 1) / 2 / pixelsPerUnit;
                else
                    w = overrideSprite.rect.width / 2 / pixelsPerUnit;
                h = overrideSprite.rect.height / pixelsPerUnit;
            }
            else
            {
                //Alpha垂直扩展
                w = overrideSprite.rect.width / pixelsPerUnit;

                if (alphaPixelOffset > 0.0f)
                    h = (overrideSprite.rect.height - 1) / 2 / pixelsPerUnit;
                else
                    h = overrideSprite.rect.height / 2 / pixelsPerUnit;
            }

            rectTransform.anchorMax = rectTransform.anchorMin;
            rectTransform.sizeDelta = new Vector2(w, h);
            SetAllDirty();
        }
    }
}
