using System;
using UnityEngine;
using UnityEngine.UI;
using System.Reflection;
using TKFrame;
using UnityEngine.Sprites;

/// <summary>
/// 扩展UGUI的Image控件，基于自编引擎实现sprite缓存，避免Rebuild。
/// </summary>
[AddComponentMenu("UI/ImageFast", 12)]
public class ImageFast : Image
{
#if !OUTSOURCE
    private Mesh m_currentMesh = null;
    private static Vector2[] m_uvs = null;
    private static Vector3[] m_vertices = null;
    private MeshCache m_meshCache = null;
    private bool m_newMesh = false;
    private bool m_isFillAmountAllow = false;
    
    #if !CANVAS_BATCHS_OPT
    
    private static FieldInfo m_spriteField = null;
    private static FieldInfo m_colorField = null;
    private static FieldInfo m_fillAmountField = null;

    private static FieldInfo spriteField
    {
        get
        {
            if (m_spriteField == null)
            {
                System.Type imageType = typeof(Image);
                m_spriteField = imageType.GetField("m_Sprite", BindingFlags.Instance | BindingFlags.NonPublic);
            }

            return m_spriteField;
        }
    }
    
    private static FieldInfo colorField
    {
        get
        {
            if (m_colorField == null)
            {
                System.Type graphicType = typeof(Graphic);
                m_colorField = graphicType.GetField("m_Color", BindingFlags.Instance | BindingFlags.NonPublic);
            }

            return m_colorField;
        }
    }
    
    private static FieldInfo fillAmountField
    {
        get
        {
            if (m_fillAmountField == null)
            {
                System.Type imageType = typeof(Image);
                m_fillAmountField = imageType.GetField("m_FillAmount", BindingFlags.Instance | BindingFlags.NonPublic);
            }

            return m_fillAmountField;
        }
    }
    
    #endif

    private bool isUseCache
    {
        get
        {
            return m_meshCache != null;
        }
    }

    protected override void OnEnable()
    {
        base.OnEnable();
        if (m_uvs == null)
        {
            m_uvs = new Vector2[4];
            m_uvs[0] = Vector2.zero;
            m_uvs[1] = Vector2.zero;
            m_uvs[2] = Vector2.zero;
            m_uvs[3] = Vector2.zero;
        }

        if (m_vertices == null)
        {
            m_vertices = new Vector3[4];
            m_vertices[0] = Vector3.zero;
            m_vertices[1] = Vector3.zero;   
            m_vertices[2] = Vector3.zero;   
            m_vertices[3] = Vector3.zero;   
        }

        if (isFilledModel())
        {
#if !CANVAS_BATCHS_OPT
            fillAmountField.SetValue(this, 1.0f);
#else
            m_FillAmount = 1.0f;
#endif
            m_isFillAmountAllow = false;
        }
    }

    protected override void UpdateGeometry()
    {
        base.UpdateGeometry();
        
        if (isSlicedModel())
        {
            m_meshCache.AddMesh(sprite.name, workerMesh);
        }
        else
        {
            CacheMesh();
        }
        
        m_newMesh = false;
    }

    public void SetCache(MeshCache meshCache)
    {
        m_meshCache = meshCache;
    }

    public void SetImageDirect(Sprite targetSprite)
    {
        if (targetSprite == null || sprite == targetSprite)
        {
            return;
        }
        
        if (m_currentMesh == null)
        {
            sprite = targetSprite;
            m_newMesh = true;
            return;
        }        

        if (isSimpleModel())
        {
            ShowSimpleSprite(targetSprite);
        }
        else if (isSlicedModel())
        {
            ShowSlicedSprite(targetSprite);
        }
        else if (isFilledModel())
        {
            ShowFilledSprite(targetSprite);
        }
        else
        {
            sprite = targetSprite;
            m_newMesh = true;
        }
    }

    public override void SetNativeSize()
    {
        if (overrideSprite != null)
        {
            float w = overrideSprite.rect.width / pixelsPerUnit;
            float h = overrideSprite.rect.height / pixelsPerUnit;
            rectTransform.anchorMax = rectTransform.anchorMin;
            rectTransform.sizeDelta = new Vector2(w, h);
            SetLayoutDirty();
            SetVerticesDirty();
        }
    }

   
    private void CacheMesh()
    {
        if (m_currentMesh == null)
        {
            m_currentMesh = new Mesh();
        }
        MeshUtil.CopyMesh(workerMesh, m_currentMesh);
        if (isFilledModel())
            m_isFillAmountAllow = true;
    }

    private void GetUVs()
    {
        Vector4 tmpUv = DataUtility.GetOuterUV(sprite);

        m_uvs[0].x = tmpUv.x;
        m_uvs[0].y = tmpUv.y;
        
        m_uvs[1].x = tmpUv.x;
        m_uvs[1].y = tmpUv.w;
        
        m_uvs[2].x = tmpUv.z;
        m_uvs[2].y = tmpUv.w;
        
        m_uvs[3].x = tmpUv.z;
        m_uvs[3].y = tmpUv.y;
    }

    private void GetVertices()
    {
        Vector4 padding = m_Sprite == null ? Vector4.zero : DataUtility.GetPadding(m_Sprite);
        Vector4 size = m_Sprite == null ? Vector2.zero : new Vector2(m_Sprite.rect.width, m_Sprite.rect.height);

        Rect r = GetPixelAdjustedRect();

        int spriteW = Mathf.RoundToInt(size.x);
        int spriteH = Mathf.RoundToInt(size.y);

        float paddingX = padding.x / spriteW;
        float paddingY = padding.y / spriteH;
        float paddingZ = (spriteW - padding.z) / spriteW;
        float paddingW = (spriteH - padding.w) / spriteH;

        if (preserveAspect && size.sqrMagnitude > 0.0f)
        {
            PreserveSpriteAspectRatio(ref r, size);
        }

        float x = r.x + r.width * paddingX;
        float y = r.y + r.height * paddingY;
        float z = r.x + r.width * paddingZ;
        float w = r.y + r.height * paddingW;

        m_vertices[0].x = x;
        m_vertices[0].y = y;
        
        m_vertices[1].x = x;
        m_vertices[1].y = w;
        
        m_vertices[2].x = z;
        m_vertices[2].y = w;
        
        m_vertices[3].x = z;
        m_vertices[3].y = y;

    }
    
    private void PreserveSpriteAspectRatio(ref Rect rect, Vector2 spriteSize)
    {
        var spriteRatio = spriteSize.x / spriteSize.y;
        var rectRatio = rect.width / rect.height;

        if (spriteRatio > rectRatio)
        {
            var oldHeight = rect.height;
            rect.height = rect.width * (1.0f / spriteRatio);
            rect.y += (oldHeight - rect.height) * rectTransform.pivot.y;
        }
        else
        {
            var oldWidth = rect.width;
            rect.width = rect.height * spriteRatio;
            rect.x += (oldWidth - rect.width) * rectTransform.pivot.x;
        }
    }

    #region SimpleModel

    private bool isSimpleModel()
    {
        if (type == Type.Simple && !useSpriteMesh)
            return true;
        else
            return false;
    }

    private void ShowSimpleSprite(Sprite targetSprite)
    {
#if !CANVAS_BATCHS_OPT
        spriteField.SetValue(this, targetSprite);
#else
        m_Sprite = targetSprite;
#endif
            
        GetUVs();
        m_currentMesh.uv = m_uvs;
        
        GetVertices();
        m_currentMesh.vertices = m_vertices;
        
        canvasRenderer.SetMesh(m_currentMesh);
        canvasRenderer.SetTexture(sprite.texture);

        Texture2D alphaTex = sprite.associatedAlphaSplitTexture;

        if (alphaTex != null)
        {
            canvasRenderer.SetAlphaTexture(alphaTex);
        }
    }

    #endregion
    
    #region FilledModel
    
    public void SetFillAmount(float amount)
    {
        if (amount > 1)
            amount = 1;
        else if(amount < 0)
            amount = 0;

        if (m_currentMesh != null && type == Type.Filled)
        {
            if (fillMethod == FillMethod.Horizontal)
            {
                GetUVs();
                SetHorizontalFillAmount(amount);
                ShowFillAmount(amount);
                return;
            }
            else if (fillMethod == FillMethod.Vertical)
            {
                GetUVs();
                SetVerticalFillAmount(amount);
                ShowFillAmount(amount);
                return;
            }
        }

        fillAmount = amount;
    }
    
    private bool isFilledModel()
    {
        if (type == Type.Filled && (fillMethod == FillMethod.Horizontal || fillMethod == FillMethod.Vertical))
            return true;
        else
            return false;
    }
    
    private void SetHorizontalFillAmount(float amount)
    {
        Vector2 pivot = rectTransform.pivot;
        Vector3 scale = rectTransform.localScale;
        
        float x = m_uvs[0].x;
        float z = m_uvs[3].x;
        float fill = (z - x) * amount;
        
        if (fillOrigin == 0)
        {
            if (!Mathf.Approximately(pivot.x, 0))
            {
                pivot.x = 0;
                rectTransform.pivot = pivot;
            }
            
            float newz = x + fill;
            m_uvs[2].x = newz;
            m_uvs[3].x = newz;
        }
        else
        {
            if (!Mathf.Approximately(pivot.x, 1))
            {
                pivot.x = 1;
                rectTransform.pivot = pivot;
            }

            float newx = z - fill;
            m_uvs[0].x = newx;
            m_uvs[1].x = newx;
        }

        scale.x = amount;
        rectTransform.localScale = scale;
    }

    private void SetVerticalFillAmount(float amount)
    {
        Vector2 pivot = rectTransform.pivot;
        Vector3 scale = rectTransform.localScale;
        
        float y = m_uvs[0].y;
        float w = m_uvs[1].y;
        float fill = (w - y) * amount;
        
        if (fillOrigin == 0)
        {
            if (!Mathf.Approximately(pivot.y, 0))
            {
                pivot.y = 0;
                rectTransform.pivot = pivot;
            }

            float newy = y + fill;
            m_uvs[0].y = newy;
            m_uvs[3].y = newy;
        }
        else
        {
            if (!Mathf.Approximately(pivot.y, 1))
            {
                pivot.y = 1;
                rectTransform.pivot = pivot;
            }
            
            float neww = w - fill;
            m_uvs[1].y = neww;
            m_uvs[2].y = neww;
        }
        
        scale.y = amount;
        rectTransform.localScale = scale;
    }

    private void ShowFillAmount(float amount)
    {
        if (m_isFillAmountAllow)
        {
#if !CANVAS_BATCHS_OPT
            fillAmountField.SetValue(this, amount);
#else
            m_FillAmount = amount;
#endif
            m_currentMesh.uv = m_uvs;
            canvasRenderer.SetMesh(m_currentMesh);
        }
    }
    
    private void ShowFilledSprite(Sprite targetSprite)
    {
#if !CANVAS_BATCHS_OPT
        spriteField.SetValue(this, targetSprite);
#else
        m_Sprite = targetSprite;
#endif
        
        GetUVs();
            
        if (fillMethod == FillMethod.Horizontal)
        {
            SetHorizontalFillAmount(fillAmount);
        }
        else if (fillMethod == FillMethod.Vertical)
        {
            SetVerticalFillAmount(fillAmount);
        }
            
        m_currentMesh.uv = m_uvs;
        canvasRenderer.SetMesh(m_currentMesh);
        canvasRenderer.SetTexture(sprite.texture);

        Texture2D alphaTex = sprite.associatedAlphaSplitTexture;

        if (alphaTex != null)
        {
            canvasRenderer.SetAlphaTexture(alphaTex);
        }
    }
    
    #endregion
    
    #region SlicedModel
    
    private bool isSlicedModel()
    {
        if (type == Type.Sliced && fillCenter && isUseCache)
            return true;
        else
            return false;
    }

    private void ShowSlicedSprite(Sprite targetSprite)
    {
        Mesh mesh = m_meshCache.GetMesh(targetSprite.name);
        if (mesh != null)
        {
#if !CANVAS_BATCHS_OPT
            spriteField.SetValue(this, targetSprite);
#else
            m_Sprite = targetSprite;
#endif
            m_currentMesh = mesh;
            SetColor();
            
            canvasRenderer.SetMesh(mesh);
            canvasRenderer.SetTexture(targetSprite.texture);
            
            Texture2D alphaTex = sprite.associatedAlphaSplitTexture;
            
            if (alphaTex != null)
            { 
                canvasRenderer.SetAlphaTexture(alphaTex);
            }        
        }
        else
        {
            sprite = targetSprite;
            m_newMesh = true;
        }
    }
    
    #endregion

    #region 设置颜色
    
    public void SetColorDirect(Color targetColor)
    {
        if (color == targetColor)
        {
            return;
        }

        if (m_newMesh || m_currentMesh == null)
        {
            color = targetColor;
            return;
        }
        
#if !CANVAS_BATCHS_OPT
        colorField.SetValue(this, targetColor);
#else
        m_Color = targetColor;
#endif
        ShowColor();
    }
    
    private void ShowColor()
    {
        SetColor();
        canvasRenderer.SetMesh(m_currentMesh);
    }

    private void SetColor()
    {
        Color[] colors = m_currentMesh.colors;
        if (colors.Length > 0 && colors[0] != color)
        {
            for (int i = 0; i < colors.Length; i++)
            {
                colors[i].r = color.r;
                colors[i].g = color.g;
                colors[i].b = color.b;
                colors[i].a = color.a;
            }

            m_currentMesh.colors = colors;
        }
    }

    #endregion
#endif
}

public static class MeshUtil
{
    public static void CopyMesh(Mesh from, Mesh to)
    {
        if (from == null)
            return;
        
        if (to == null)
            to = new Mesh();

        if (to.vertices.Length > from.vertexCount)
        {
            to = new Mesh();
        }
        
        to.vertices = from.vertices;
        to.colors = from.colors;
        to.uv = from.uv;
        to.uv2 = from.uv2;
        to.uv3 = from.uv3;
        to.uv4 = from.uv4;
        to.normals = from.normals;
        to.tangents = from.tangents;
        to.triangles = from.triangles;
        to.bounds = from.bounds;
    }
}

public class SpritesCache : LRUCacheEx<string, Mesh>.ILRUCacheHandler
{
    private LRUCacheEx<string, Mesh> _cache;
    public LRUCacheEx<string, Mesh> cache
    {
        get
        {
            if (_cache == null)
            {
                _cache = new LRUCacheEx<string, Mesh>(this, 10);
            }
            return _cache;
        }
    }

    void LRUCacheEx<string, Mesh>.ILRUCacheHandler.ReleaseItem(ref Mesh value)
    {
        value.Clear();
    }

    public void CacheSprite(string targetName, Mesh targetMesh)
    {
        if (cache.IsFull())
        {
            DLinkedListNode<string, Mesh> uselessNode = cache.PopTheLastNode();
            if (uselessNode != null && uselessNode.value != null)
            {
                uselessNode.key = targetName;
                MeshUtil.CopyMesh(targetMesh, uselessNode.value);
                cache.Add(uselessNode);
            }
            return;
        }

        Mesh to = new Mesh();
        MeshUtil.CopyMesh(targetMesh, to);
        cache.Add(targetName, to);
    }

    public void Clear()
    {
        if (_cache != null)
        {
            _cache.Clear();
            _cache = null;
        }
    }
}
