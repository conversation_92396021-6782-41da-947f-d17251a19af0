using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

/// <summary>
/// 扩展UGUI的Image控件，UV修改
/// </summary>
[AddComponentMenu("UI/ImageUV", 11)]
public class ImageUV : Image
{
    [SerializeField] Rect m_UVRect = new Rect(0f, 0f, 1f, 1f);

    /// <summary>
    /// UV rectangle used by the texture.
    /// </summary>
    public Rect uvRect
    {
        get
        {
            return m_UVRect;
        }
        set
        {
            if (m_UVRect == value)
                return;
            m_UVRect = value;
            SetVerticesDirty();
        }
    }

    protected override void OnPopulateMesh(VertexHelper toFill)
    {
        if (type == Type.Simple)
        {
            GenerateSimpleSpriteWithUV(toFill, preserveAspect);
        }
        else
        {
            base.OnPopulateMesh(toFill);
        }
    }

    void GenerateSimpleSpriteWithUV(VertexHelper vh, bool lPreserveAspect)
    {
        Vector4 v = GetDrawingDimensionsWithUV(lPreserveAspect);
        var uv = (overrideSprite != null) ? UnityEngine.Sprites.DataUtility.GetOuterUV(overrideSprite) : Vector4.zero;

        var color32 = color;
        vh.Clear();
        vh.AddVert(new Vector3(v.x, v.y), color32, new Vector2((uv.z - uv.x) * m_UVRect.xMin + uv.x, (uv.w - uv.y) * m_UVRect.yMin + uv.y));
        vh.AddVert(new Vector3(v.x, v.w), color32, new Vector2((uv.z - uv.x) * m_UVRect.xMin + uv.x, (uv.w - uv.y) * m_UVRect.yMax + uv.y));
        vh.AddVert(new Vector3(v.z, v.w), color32, new Vector2((uv.z - uv.x) * m_UVRect.xMax + uv.x, (uv.w - uv.y) * m_UVRect.yMax + uv.y));
        vh.AddVert(new Vector3(v.z, v.y), color32, new Vector2((uv.z - uv.x) * m_UVRect.xMax + uv.x, (uv.w - uv.y) * m_UVRect.yMin + uv.y));

        vh.AddTriangle(0, 1, 2);
        vh.AddTriangle(2, 3, 0);
    }

    private Vector4 GetDrawingDimensionsWithUV(bool shouldPreserveAspect)
    {
        var padding = overrideSprite == null ? Vector4.zero : UnityEngine.Sprites.DataUtility.GetPadding(overrideSprite);
        var size = overrideSprite == null ? Vector2.zero : new Vector2(overrideSprite.rect.width, overrideSprite.rect.height);

        Rect r = GetPixelAdjustedRect();
        // Debug.Log(string.Format("r:{2}, size:{0}, padding:{1}", size, padding, r));

        int spriteW = Mathf.RoundToInt(size.x);
        int spriteH = Mathf.RoundToInt(size.y);

        var v = new Vector4(
            padding.x / spriteW,
            padding.y / spriteH,
            (spriteW - padding.z) / spriteW,
            (spriteH - padding.w) / spriteH);

        if (shouldPreserveAspect && size.sqrMagnitude > 0.0f)
        {
            var spriteRatio = size.x / size.y;
            var rectRatio = r.width / r.height;

            if (spriteRatio > rectRatio)
            {
                var oldHeight = r.height;
                r.height = r.width * (1.0f / spriteRatio);
                r.y += (oldHeight - r.height) * rectTransform.pivot.y;
            }
            else
            {
                var oldWidth = r.width;
                r.width = r.height * spriteRatio;
                r.x += (oldWidth - r.width) * rectTransform.pivot.x;
            }
        }

        v = new Vector4(
            r.x + r.width * v.x,
            r.y + r.height * v.y,
            r.x + r.width * v.z,
            r.y + r.height * v.w
        );

        return v;
    }

    //protected override void OnPopulateMesh(VertexHelper vh)
    //{
    //    Texture tex = mainTexture;
    //    vh.Clear();
    //    if (tex != null)
    //    {
    //        var r = GetPixelAdjustedRect();
    //        var v = new Vector4(r.x, r.y, r.x + r.width, r.y + r.height);
    //        var scaleX = tex.width * tex.texelSize.x;
    //        var scaleY = tex.height * tex.texelSize.y;
    //        {
    //            var color32 = color;
    //            vh.AddVert(new Vector3(v.x, v.y), color32, new Vector2(m_UVRect.xMin * scaleX, m_UVRect.yMin * scaleY));
    //            vh.AddVert(new Vector3(v.x, v.w), color32, new Vector2(m_UVRect.xMin * scaleX, m_UVRect.yMax * scaleY));
    //            vh.AddVert(new Vector3(v.z, v.w), color32, new Vector2(m_UVRect.xMax * scaleX, m_UVRect.yMax * scaleY));
    //            vh.AddVert(new Vector3(v.z, v.y), color32, new Vector2(m_UVRect.xMax * scaleX, m_UVRect.yMin * scaleY));

    //            vh.AddTriangle(0, 1, 2);
    //            vh.AddTriangle(2, 3, 0);
    //        }
    //    }
    //}
}
