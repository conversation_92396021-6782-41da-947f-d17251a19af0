#if TKF_EDITOR &&(TKF_ALL_EXTEND||TKFE_NTWEEN)//TKFrame Auto Gen
//----------------------------------------------
//            NGUI: Next-Gen UI kit
// Copyright © 2011-2014 Tasharen Entertainment
//----------------------------------------------

using UnityEngine;
using UnityEditor;

namespace NTween
{

    [CustomEditor(typeof(TweenAnchoredPosition))]
    public class TweenAnchoredPositionEditor : UITweenerEditor
    {
        public override void OnInspectorGUI()
        {
            GUILayout.Space(6f);
            NTweenEditorTools.SetLabelWidth(120f);

            TweenAnchoredPosition tw = target as TweenAnchoredPosition;
            GUI.changed = false;

            Object targetObj = EditorGUILayout.ObjectField("Target", tw.target, typeof(RectTransform), true);
            Vector2 from = EditorGUILayout.Vector2Field("From", tw.from);
            Vector2 to = EditorGUILayout.Vector2Field("To", tw.to);

            bool SetX = EditorGUILayout.Toggle("SetX", tw.SetX);
            bool SetY = EditorGUILayout.Toggle("SetY", tw.SetY);

            if (GUI.changed)
            {
                NTweenEditorTools.RegisterUndo("Tween Change", tw);
                tw.from = from;
                tw.to = to;
                tw.target = (targetObj as RectTransform);
                tw.SetX = SetX;
                tw.SetY = SetY;
                NTweenTools.SetDirty(tw);
            }

            DrawCommonProperties();
        }
    }
}
#endif //TKFrame Auto Gen
