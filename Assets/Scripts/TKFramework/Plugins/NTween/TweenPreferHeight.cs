using System;
using UnityEngine;
using UnityEngine.UI;

namespace NTween
{

    /// <summary>
    /// Tween the object's position.
    /// </summary>
    /// 

    [RequireComponent(typeof(RectTransform))]
    [AddComponentMenu("NTween/Tween Prefer Height")]
    public class TweenPreferHeight : UITweener, ITransformTween
    {
        public float from;
        public float to;


        public Transform target;

        public Transform cachedTransform
        {
            get
            {
                return target;
            }
        }

        /// <summary>
        /// Tween's current value.
        /// </summary>
        public float value
        {
            get
            {
                return cachedTransform.GetComponent<LayoutElement>().preferredHeight;
            }
            set
            {
                cachedTransform.GetComponent<LayoutElement>().preferredHeight = value;
            }
        }

        void Awake()
        {

        }

        /// <summary>
        /// Tween the value.
        /// </summary>

        protected override void OnUpdate(float factor, bool isFinished)
        {
            value = from * (1f - factor) + to * factor;
        }

        /// <summary>
        /// Start the tweening operation.
        /// </summary>

        static public TweenPreferHeight Begin(GameObject go, float duration, float to)
        {
            TweenPreferHeight comp = UITweener.Begin<TweenPreferHeight>(go, duration);
            comp.from = comp.value;
            comp.to = to;

            if (duration <= 0f)
            {
                comp.Sample(1f, true);
                comp.enabled = false;
            }
            return comp;
        }

        static public TweenPreferHeight Begin(GameObject go, float duration, float from, float to)
        {
            TweenPreferHeight comp = UITweener.Begin<TweenPreferHeight>(go, duration);
            comp.value = from;
            comp.from = comp.value;
            comp.to = to;

            if (duration <= 0f)
            {
                comp.Sample(1f, true);
                comp.enabled = false;
            }
            return comp;
        }

        [ContextMenu("Set 'From' to current value")]
        public override void SetStartToCurrentValue() { from = value; }

        [ContextMenu("Set 'To' to current value")]
        public override void SetEndToCurrentValue() { to = value; }

        [ContextMenu("Assume value of 'From'")]
        void SetCurrentValueToStart() { value = from; }

        [ContextMenu("Assume value of 'To'")]
        void SetCurrentValueToEnd() { value = to; }

        public Transform GetTarget()
        {
            return target;
        }

        public void SetTarget(Transform target)
        {
            this.target = target;
        }
    }
}