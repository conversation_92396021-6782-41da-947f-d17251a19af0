using System;
using UnityEngine;

namespace NTween
{

    /// <summary>
    /// Tween the object's position.
    /// </summary>
    /// 

    [RequireComponent(typeof(RectTransform))]
    [AddComponentMenu("NTween/Tween Height")]
    public class TweenHeight : UITweener, ITransformTween
    {
        public float from;
        public float to;


        public RectTransform target;

        public RectTransform cachedTransform
        {
            get
            {
                if (target == null)
                    target = GetComponent<RectTransform>();
                return target;
            }
        }

        /// <summary>
        /// Tween's current value.
        /// </summary>
        public float value
        {
            get
            {
                return cachedTransform.sizeDelta.y;
            }
            set
            {
                var sizeDelta = cachedTransform.sizeDelta;
                sizeDelta.y = value;
                cachedTransform.sizeDelta = sizeDelta;
            }
        }

        void Awake()
        {

        }

        /// <summary>
        /// Tween the value.
        /// </summary>

        protected override void OnUpdate(float factor, bool isFinished)
        {
            value = from * (1f - factor) + to * factor;
        }

        /// <summary>
        /// Start the tweening operation.
        /// </summary>

        static public TweenWidth Begin(GameObject go, float duration, float to)
        {
            TweenWidth comp = UITweener.Begin<TweenWidth>(go, duration);
            comp.from = comp.value;
            comp.to = to;

            if (duration <= 0f)
            {
                comp.Sample(1f, true);
                comp.enabled = false;
            }
            return comp;
        }

        static public TweenWidth Begin(GameObject go, float duration, float from, float to)
        {
            TweenWidth comp = UITweener.Begin<TweenWidth>(go, duration);
            comp.value = from;
            comp.from = comp.value;
            comp.to = to;

            if (duration <= 0f)
            {
                comp.Sample(1f, true);
                comp.enabled = false;
            }
            return comp;
        }

        [ContextMenu("Set 'From' to current value")]
        public override void SetStartToCurrentValue() { from = value; }

        [ContextMenu("Set 'To' to current value")]
        public override void SetEndToCurrentValue() { to = value; }

        [ContextMenu("Assume value of 'From'")]
        void SetCurrentValueToStart() { value = from; }

        [ContextMenu("Assume value of 'To'")]
        void SetCurrentValueToEnd() { value = to; }

        public Transform GetTarget()
        {
            return target;
        }

        public void SetTarget(Transform target)
        {
            this.target = target as RectTransform;
        }
    }
}