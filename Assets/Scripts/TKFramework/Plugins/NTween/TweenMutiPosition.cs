using System;
using System.Collections;
using System.Collections.Generic;
using NTween;
using UnityEngine;

public class TweenMutiPosition : MonoBehaviour
{
    public Vector3 from;
    public Vector3 to;
    public float Duration = 1f;
    public float Time_Delay = 0f;
    public int Frame_Delay = 0;
    
    public float Height = -1f;
    
    public Transform endTargetTransform = null;
    
    public AnimationCurve XAxisCurve;
    public AnimationCurve YAxisCurve;
    public AnimationCurve ZAxisCurve;
    public AnimationCurve ParabolaCurve;

    private TweenSingleAxisPosition xAxisTween;
    private TweenSingleAxisPosition yAxisTween;
    private TweenSingleAxisPosition zAxisTween;

    private bool inited = false;

    public void Awake()
    {
        ParabolaCurve = YAxisCurve;
    }

    public void InitTweenParma()
    {
        xAxisTween.@from = @from;
        xAxisTween.to = to;
        xAxisTween.animationCurve = XAxisCurve;
        xAxisTween.SetX = true;
        xAxisTween.SetY = false;
        xAxisTween.SetZ = false;
        xAxisTween.duration = Duration;
        xAxisTween.delayFrame = Frame_Delay;
        xAxisTween.delay = Time_Delay;
        xAxisTween.endTarget = endTargetTransform;
        
        
        yAxisTween.@from = @from;
        yAxisTween.to = to;
        yAxisTween.SetX = false;
        yAxisTween.SetY = true;
        yAxisTween.SetZ = false;
        yAxisTween.duration = Duration;
        yAxisTween.delayFrame = Frame_Delay;
        yAxisTween.delay = Time_Delay;
        yAxisTween.endTarget = endTargetTransform;
        if (Math.Abs(Height - (-1f)) > 0.005)
        {
            yAxisTween.to.y = Height;
            YAxisCurve = ParabolaCurve;
        }
        else
        {
            YAxisCurve = XAxisCurve;
        }
        yAxisTween.animationCurve = YAxisCurve;
        
        zAxisTween.@from = @from;
        zAxisTween.to = to;
        zAxisTween.animationCurve = ZAxisCurve;
        zAxisTween.SetX = false;
        zAxisTween.SetY = false;
        zAxisTween.SetZ = true;
        zAxisTween.duration = Duration;
        zAxisTween.delayFrame = Frame_Delay;
        zAxisTween.delay = Time_Delay;
        zAxisTween.endTarget = endTargetTransform;
    }
    
    void OnEnable()
    {
        if (!inited)
        {
            xAxisTween = this.gameObject.AddComponent<TweenSingleAxisPosition>();

            yAxisTween = this.gameObject.AddComponent<TweenSingleAxisPosition>();

            zAxisTween = this.gameObject.AddComponent<TweenSingleAxisPosition>();
            inited = true;
        }
        InitTweenParma();
        if (xAxisTween != null)
        {
            xAxisTween.ResetToBeginning();
            xAxisTween.PlayForward();
        }

        if (yAxisTween != null)
        {
            yAxisTween.ResetToBeginning();
            yAxisTween.PlayForward();
        }
        if (zAxisTween != null)
        {
            zAxisTween.ResetToBeginning();
            zAxisTween.PlayForward();
        }
        this.enabled = false;
    }
    
}
