using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace NTween
{
    [AddComponentMenu("NTween/Tween Fov")]
    public class TweenCameraFov : UITweener
    {
        public float from = 0.0f;
        public float to = 0.0f;

        public bool include_children = true;

        Camera _camera;

        private GameObject target;
        public GameObject Target
        {
            set
            {
                target = value;
                _camera = target.GetComponent<Camera>();
            }
            get { return target; }
        }

        [System.Obsolete("Use 'value' instead")]
        public float fieldOfView { get { return this.value; } set { this.value = value; } }

        /// <summary>
        /// Tween's current value.
        /// </summary>

        public float value
        {
            get
            {
                if (_camera != null)
                    return _camera.fieldOfView;
                else
                    return 0.0f;
            }
            set
            {
                if (_camera != null)
                {
                    _camera.fieldOfView = value;
                    
                }
            }
        }

        /// <summary>
        /// Tween the value.
        /// </summary>

        protected override void OnUpdate(float factor, bool isFinished) { value = Mathf.Lerp(from, to, factor); }

        /// <summary>
        /// Start the tweening operation.
        /// </summary>

        static public TweenFov Begin(GameObject go, float duration, float fov)
        {
#if UNITY_EDITOR
            if (!Application.isPlaying) return null;
#endif
            TweenFov comp = UITweener.Begin<TweenFov>(go, duration);
            comp.from = comp.value;
            comp.to = fov;

            if (duration <= 0f)
            {
                comp.Sample(1f, true);
                comp.enabled = false;
            }
            return comp;
        }

        static public TweenFov Begin(GameObject go, float duration, float from, float to)
        {
#if UNITY_EDITOR
            if (!Application.isPlaying) return null;
#endif
            TweenFov comp = UITweener.Begin<TweenFov>(go, duration);
            comp.value = from;
            comp.from = comp.value;
            comp.to = to;

            if (duration <= 0f)
            {
                comp.Sample(1f, true);
                comp.enabled = false;
            }
            return comp;
        }

        [ContextMenu("Set 'From' to current value")]
        public override void SetStartToCurrentValue() { from = value; }

        [ContextMenu("Set 'To' to current value")]
        public override void SetEndToCurrentValue() { to = value; }

        [ContextMenu("Assume value of 'From'")]
        void SetCurrentValueToStart() { value = from; }

        [ContextMenu("Assume value of 'To'")]
        void SetCurrentValueToEnd() { value = to; }
    }
}


