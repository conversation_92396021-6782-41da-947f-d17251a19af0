using System.Collections;
using System.Collections.Generic;
using UnityEngine;

// 1 2 3 是低配， 4 5是中配， 6 7 8是高配
public enum ETCQualityGrade
{
    Grade01 = 1, // 最低配置
    Grade02,
    Grade03,
    Grade04,
    Grade05,
    Grade06,
    Grade07,
    Grade08// 当前最高配置 
}

public enum ESourceType
{
    SRC_PreviousRegexMatch,
    SRC_GpuFamily,
    SRC_GlVersion,
    SRC_AndroidVersion,
    SRC_DeviceMake,
    SRC_DeviceModel,
    SRC_VulkanVersion,
    SRC_UsingHoudini,
    SRC_VulkanAvailable,
    SRC_MemorySizeInGB, // 内存
    SRC_CPUCoreNum, // CPU核数
    SRC_CPUMaxFreq, // CPU最大频率
    SRC_GLExtensions, // opengl extensions 支持
    SRC_DeviceID, // IOS用sysctlbyname获取的DeviceID
    SRC_DeviceID_MAJOR,// IOS用sysctlbyname获取的DeviceID
    SRC_DeviceID_MINOR,
    SRC_MAX,
}
public enum ECompareType
{
    <PERSON>MP_Equal,
    C<PERSON>_Less,
    CMP_LessEqual,
    CMP_Greater,
    CMP_GreaterEqual,
    CMP_NotEqual,
    CMP_Regex,
    CMP_StartWith, //开头匹配
    CMP_MAX,
};

[System.Serializable]
public class FProfileMatchItem
{
    public string SourceType;
    public string CompareType;
    public string MatchString;
}

[System.Serializable]
public class FProfileMatch
{
    public string Profile;
    public string TCQualityGrade;
    public FProfileMatchItem[] Match;
}

[System.Serializable]
public enum EGradeScoreType
{
    GST_GPU = 0, // GPU得分
    GST_Memory, // 内存得分
    GST_CPUCore, // CPU核数得分
    GST_CPUFreq, // CPU最大频率
    GST_MAX,
}

[System.Serializable]
public class FGradeProfileMatch
{
    public string ScoreType;
    public int Score;
    public FProfileMatchItem[] Match;
}

[System.Serializable]
public class FGradeScoreProfileName
{
    public string ProfileName;
    public string TCQualityGrade;
    public int Score;
}

[System.Serializable]
public class FGradeScoreTypePercentage
{
    public int index;
    public float value;
}

[System.Serializable]
public class FAndroidCommonDeviceProfileConfig
{
    public int MaxScore;
    public FGradeScoreProfileName[] GradeScoreProfileName;
    public FGradeScoreTypePercentage[] GradeScoreTypePercentage;
    public FGradeProfileMatch[] GradeMatchProfile;
    public FProfileMatch[] GradeMatchProfileWhiteList;
}