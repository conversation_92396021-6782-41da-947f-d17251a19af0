using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// 低画质LOD配置
/// </summary>
public class LODLowConfig : LODBaseConfig
{
    public LODLowConfig()
    {
        // 渲染分辨率设置
        DesignResolutionWidth = 1136;  //设置特定渲染分辨率--宽
        DesignResolutionHight = 640; //设置特定渲染分辨率--高

        // 降级渲染分辨率设置
        TraverseDesignResolutionWidth = 854; //设置降级渲染分辨率--宽
        TraverseDesignResolutionHeight = 480;  //设置降级渲染分辨率--高

        // 高清设置
        HighResolution = false;

        // 帧率设置
        TargetFrameRate = 30;

        // Shader的LOD设置
        Global_ShaderLOD = 150;

        // 摄像机LOD--bloom效果和景深效果
        CameraBloom = false;
        CameraDepth = false;

        // 资源缓冲池:最大缓冲个数
        AssetCacheNumCapacity = 80;

        // 资源缓冲池:最大缓冲内存
        AssetCacheMemCapacity = 50 * 1024 * 1024;

        Tier = UnityEngine.Rendering.GraphicsTier.Tier1;

        BoneNumber = BlendWeights.FourBones;

        ShadowCustomResolution = 512;

        Overload = 30;
    } 
}
