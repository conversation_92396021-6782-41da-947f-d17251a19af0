using System.IO;
using TKFrame;
using UnityEngine;

namespace TKPlugins
{
    /// <summary>
    /// 游戏启动环境相关配置
    /// </summary>
    public class GameEnvironment : ScriptableObject
	{
		//游戏存储根目录
		public string gameRootFolder = "Game/";

        //资源文件根目录
//        public string assetRootFolder = "Assets/";
        public string assetRootFolder = "";

        //Assembly目录
        public string assemblyFolder = "AssemblyFolder/";

        //Assembly文件
        public string assemblyFile = "AssemblyFile";

        private static GameEnvironment _instance = null;
		public  static GameEnvironment Instance
        {
			get
            {
				if (_instance == null)
                {
					//游戏启动环境配置资源
					_instance = Resources.Load<GameEnvironment> ("GameEnvironment");
					//创建默认GameEnvironment对象。
					if (_instance == null) {
						_instance = ScriptableObject.CreateInstance<GameEnvironment> ();
					}
				}
				return _instance;
			}
		}

		public static string GameRootPath
        {
			get
            {
                return Instance != null ? Instance.gameRootFolder : string.Empty;
			}
		}

		//存储目录 （也是GCloud的更新资源的存放路径）
		private static string _storagePath;
		public static string StoragePath
		{
			get
			{
				if (string.IsNullOrEmpty(_storagePath))
				{
#if UNITY_EDITOR
                    _storagePath = Application.dataPath + "/../";
#elif UNITY_ANDROID
					_storagePath = Application.persistentDataPath + "/";
#elif UNITY_IPHONE
					_storagePath = Application.persistentDataPath+ "/";
#endif
					if (!Directory.Exists(_storagePath))
					{
					    Directory.CreateDirectory(_storagePath);
					}
                }
                return _storagePath;
            }
        }

        //资源根目录
        private static string _assetRootPath;
        public static string AssetRootPath
        {
            get
            {
                if (string.IsNullOrEmpty(_assetRootPath))
                {
                    _assetRootPath = StoragePath + Instance.assetRootFolder;
                }
                if (!Directory.Exists(_assetRootPath))
                {
                    Directory.CreateDirectory(_assetRootPath);
                }
                return _assetRootPath;
            }
        }
        
        public static string BundleMapAssetName = "assetbundle_map.config";
        public static string BundleMapPath =>
	        TKFrameConfig.Instance.AssetBundlesFolder() + "/" +
	        BaseLoader.GetPlatformFolderForAssetBundles() + "/" +
	        BundleMapAssetName;

        public static string BundleMapPath_Ext => AssetRootPath + "VersionUpdate/" + BundleMapPath;

        public static string ETC2_SUFFIX = "_etc";
        public static string HD_SUFFIX = "_hd";
        
        
        

        //Assembly目录
        private static string _assemblyFolder;
        public static string AssemblyFolder
        {
            get
            {
                if (string.IsNullOrEmpty(_assemblyFolder))
                {
                    _assemblyFolder = AssetRootPath + Instance.assemblyFolder;
                }
                if (!Directory.Exists(_assemblyFolder))
                {
                    Directory.CreateDirectory(_assemblyFolder);
                }
                return _assemblyFolder;
            }
        }

        //Assembly文件路径
        public static string AssemblyPath
        {
            get
            {
                return AssemblyFolder + Instance.assemblyFile;
            }
        }

    }
}

