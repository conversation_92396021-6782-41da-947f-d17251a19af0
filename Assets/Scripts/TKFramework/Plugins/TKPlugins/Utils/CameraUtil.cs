using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class CameraUtil : MonoBehaviour
{
    public static Vector3 ScreenToWorldPoint(Vector3 position, Camera srcCamera)
    {
        if (srcCamera == null)
        {
            return Vector3.zero;
        }

        if (!IsDownsampling(srcCamera))
        {
            return srcCamera.ScreenToWorldPoint(position);
        }

        RectInt viewport = GetScreenViewportRectInt(srcCamera, false);

        Matrix4x4 cameraToWorld = srcCamera.cameraToWorldMatrix;
        Matrix4x4 clipToWorld = srcCamera.cameraToWorldMatrix * srcCamera.projectionMatrix.inverse;

        Vector3 outVec = Vector3.zero;
        if (!CameraUnProject(ref position, ref cameraToWorld, ref clipToWorld, ref viewport, ref outVec))
        {
        //    Debug.LogErrorFormat("Screen position out of view frustum (screen pos {0}, {1}, {2}) (Camera rect {3} {4} {5} {6})", position.x, position.y, position.z, viewport.x, viewport.y, viewport.width, viewport.height);
        }

        return outVec;
    }

    public static Ray ScreenPointToRay(Vector3 viewPortPos, Camera srcCamera)
    {
        if (srcCamera == null)
        {
            return new Ray();
        }

        if (!IsDownsampling(srcCamera))
        {
            return srcCamera.ScreenPointToRay(viewPortPos);
        }

        RectInt viewport = GetScreenViewportRectInt(srcCamera, false);

        Matrix4x4 cameraToWorld = srcCamera.cameraToWorldMatrix;
        Matrix4x4 clipToWorld = srcCamera.cameraToWorldMatrix * srcCamera.projectionMatrix.inverse;

        Ray ray = new Ray();
        Vector3 outVec = Vector3.zero;
        Vector3 inOrigin = new Vector3(viewPortPos.x, viewPortPos.y, srcCamera.nearClipPlane);
        if (!CameraUnProject(ref inOrigin, ref cameraToWorld, ref clipToWorld, ref viewport, ref outVec))
        {
            //if (viewport.x > 0 || viewport.y > 0 || viewport.width > 0 || viewport.height > 0)
            //{
            //    Debug.LogErrorFormat("Screen position out of view frustum (screen pos {0}, {1}) (Camera rect {2} {3} {4} {5})", viewPortPos.x, viewPortPos.y, viewport.x, viewport.y, viewport.width, viewport.height);
            //}
            return new Ray(srcCamera.transform.position, Vector3.forward);
        }
        ray.origin = outVec;

        if (srcCamera.orthographic)
        {
            // In orthographic projection we get better precision by circumventing the whole projection and subtraction.
            Vector3 forward = -new Vector3(cameraToWorld.m02, cameraToWorld.m12, cameraToWorld.m22);
            ray.direction = forward.normalized;
        }
        else
        {
            Vector3 inEnd = new Vector3(viewPortPos.x, viewPortPos.y, srcCamera.nearClipPlane + 1000);
            // We need to sample a point much further out than the near clip plane to ensure decimals in the ray direction
            // don't get lost when subtracting the ray origin position.
            if (!CameraUnProject(ref inEnd, ref cameraToWorld, ref clipToWorld, ref viewport, ref outVec))
            {
                //if (viewport.x > 0 || viewport.y > 0 || viewport.width > 0 || viewport.height > 0)
                //{
                //    Debug.LogErrorFormat("Screen position out of view frustum (screen pos {0}, {1}) (Camera rect {2} {3} {4} {5})", viewPortPos.x, viewPortPos.y, viewport.x, viewport.y, viewport.width, viewport.height);
                //}
                return new Ray(srcCamera.transform.position, Vector3.forward);
            }
            Vector3 dir = outVec - ray.origin;
            ray.direction = dir.normalized;
        }

        return ray;
    }

    public static Vector3 WorldToScreenPoint(Vector3 position, Camera srcCamera)
    {
        if (srcCamera == null)
        {
            return Vector3.zero;
        }

        if (!IsDownsampling(srcCamera))
        {
            return srcCamera.WorldToScreenPoint(position);
        }

        RectInt viewport = GetScreenViewportRectInt(srcCamera, false);

        Matrix4x4 cameraToWorld = srcCamera.cameraToWorldMatrix;
        Matrix4x4 worldToClip = srcCamera.projectionMatrix * srcCamera.worldToCameraMatrix;

        Vector3 outVec = Vector3.zero;
        CameraProject(ref position, ref cameraToWorld, ref worldToClip, ref viewport, ref outVec);

        return outVec;
    }

    public static Vector3 ScreenToViewportPoint(Vector3 screenPos, Camera srcCamera)
    {
        if (srcCamera == null)
        {
            return Vector3.zero;
        }

        if (!IsDownsampling(srcCamera))
        {
            return srcCamera.ScreenToViewportPoint(screenPos);
        }

        Rect screenViewportRect = GetScreenViewportRect(srcCamera);

        float nx = (screenPos.x - screenViewportRect.x) / screenViewportRect.width;
        float ny = (screenPos.y - screenViewportRect.y) / screenViewportRect.height;

        return new Vector3(nx, ny, screenPos.z);
    }

    private static bool IsDownsampling(Camera srcCamera)
    {
        if (srcCamera == null)
        {
            return false;
        }

        if (srcCamera == Camera.main && srcCamera.targetTexture != null && (srcCamera.pixelRect.width != Screen.width || srcCamera.pixelRect.height != Screen.height))
        {
            return true;
        }

        return false;
    }

    public static Rect GetScreenViewportRect(Camera srcCamera, bool checkDownsample = true)
    {
        if (srcCamera == null)
        {
            return new Rect(0, 0, Screen.width, Screen.height);
        }

        if (checkDownsample && !IsDownsampling(srcCamera))
        {
            return srcCamera.pixelRect;
        }

        // 降采样相机需要重新计算视口
        Rect screenRect = new Rect(0, 0, Screen.width, Screen.height);
        Rect viewRect = srcCamera.rect;

        // Scale
        viewRect.x *= screenRect.width;
        viewRect.width *= screenRect.width;
        viewRect.y *= screenRect.height;
        viewRect.height *= screenRect.height;
        // Move
        viewRect.x += screenRect.x;
        viewRect.y += screenRect.y;
        // Clamp
        float x2 = viewRect.x + viewRect.width;
        float y2 = viewRect.y + viewRect.height;
        float rx2 = screenRect.x + screenRect.width;
        float ry2 = screenRect.y + screenRect.height;

        if (viewRect.x < screenRect.x)
        {
            viewRect.x = screenRect.x;
        }
        if (x2 > rx2)
        {
            x2 = rx2;
        }
        if (viewRect.y < screenRect.y)
        {
            viewRect.y = screenRect.y;
        }
        if (y2 > ry2)
        {
            y2 = ry2;
        }

        viewRect.width = x2 - viewRect.x;
        if (viewRect.width < 0)
        {
            viewRect.width = 0;
        }

        viewRect.height = y2 - viewRect.y;
        if (viewRect.height < 0)
        {
            viewRect.height = 0;
        }

        return viewRect;
    }

    public static RectInt GetScreenViewportRectInt(Camera srcCamera, bool checkDownsample = true)
    {
        if (srcCamera == null)
        {
            return new RectInt();
        }

        Rect viewRect = GetScreenViewportRect(srcCamera, checkDownsample);

        RectInt result = new RectInt();
        result.x = Mathf.RoundToInt(viewRect.x);
        result.y = Mathf.RoundToInt(viewRect.y);
        uint right = (uint)(viewRect.x + viewRect.width + 0.5f);
        result.width = (int)(right - result.x);
        uint bottom = (uint)(viewRect.y + viewRect.height + 0.5f);
        result.height = (int)(bottom - result.y);
        return result;
    }

    private static bool CameraProject(ref Vector3 p, ref Matrix4x4 cameraToWorld, ref Matrix4x4 worldToClip, ref RectInt viewport, ref Vector3 outP)
    {
        Vector3 clipPoint = Vector3.zero;

        if (PerspectiveMultiplyPoint3(ref worldToClip, ref p, ref clipPoint))
        {
            Vector3 cameraPos = new Vector3(cameraToWorld.m03, cameraToWorld.m13, cameraToWorld.m23);
            Vector3 dir = p - cameraPos;
            // The camera/projection matrices follow OpenGL convention: positive Z is towards the viewer.
            // So negate it to get into Unity convention.
            Vector3 forward = -new Vector3(cameraToWorld.m02, cameraToWorld.m12, cameraToWorld.m22);
            float dist = Vector3.Dot(dir, forward);

            outP.x = viewport.x + (1.0f + clipPoint.x) * viewport.width * 0.5f;
            outP.y = viewport.y + (1.0f + clipPoint.y) * viewport.height * 0.5f;
            outP.z = dist;

            return true;
        }

        outP = Vector3.zero;
        return false;
    }

    private static bool CameraUnProject(ref Vector3 p, ref Matrix4x4 cameraToWorld, ref Matrix4x4 clipToWorld, ref RectInt viewport, ref Vector3 outP)
    {
        // pixels to -1..1
        Vector3 inVec;
        inVec.x = (p.x - viewport.x) * 2.0f / viewport.width - 1.0f;
        inVec.y = (p.y - viewport.y) * 2.0f / viewport.height - 1.0f;
        // It does not matter where the point we unproject lies in depth; so we choose 0.95, which
        // is further than near plane and closer than far plane, for precision reasons.
        // In a perspective camera setup (near=0.1, far=1000), a point at 0.95 projected depth is about
        // 5 units from the camera.
        inVec.z = 0.95f;

        Vector3 pointOnPlane = Vector3.zero;
        if (PerspectiveMultiplyPoint3(ref clipToWorld, ref inVec, ref pointOnPlane))
        {
            // Now we have a point on the plane perpendicular to the viewing direction. We need to return the one that is on the line
            // towards this point, and at p.z distance along camera's viewing axis.
            Vector3 cameraPos = new Vector3(cameraToWorld.m03, cameraToWorld.m13, cameraToWorld.m23);
            Vector3 dir = pointOnPlane - cameraPos;

            // The camera/projection matrices follow OpenGL convention: positive Z is towards the viewer.
            // So negate it to get into Unity convention.
            Vector3 forward = -new Vector3(cameraToWorld.m02, cameraToWorld.m12, cameraToWorld.m22);
            float distToPlane = Vector3.Dot(dir, forward);
            if (Mathf.Abs(distToPlane) >= 1.0e-6f)
            {
                // Returns whether a matrix is a perspective projection transform (i.e. doesn't have 0,0,0,1 in the last column).
                bool isPerspective = clipToWorld.m03 != 0.0f || clipToWorld.m13 != 0.0f || clipToWorld.m23 != 0.0f || clipToWorld.m33 != 1.0f;
                if (isPerspective)
                {
                    dir *= p.z / distToPlane;
                    outP = cameraPos + dir;
                }
                else
                {
                    outP = pointOnPlane - forward * (distToPlane - p.z);
                }
                return true;
            }
        }

        outP = Vector3.zero;
        return false;
    }

    private static bool PerspectiveMultiplyPoint3(ref Matrix4x4 inMatrix, ref Vector3 v, ref Vector3 output)
    {
        Vector3 res;
        float w;
        res.x = inMatrix.m00 * v.x + inMatrix.m01 * v.y + inMatrix.m02 * v.z + inMatrix.m03;
        res.y = inMatrix.m10 * v.x + inMatrix.m11 * v.y + inMatrix.m12 * v.z + inMatrix.m13;
        res.z = inMatrix.m20 * v.x + inMatrix.m21 * v.y + inMatrix.m22 * v.z + inMatrix.m23;
        w = inMatrix.m30 * v.x + inMatrix.m31 * v.y + inMatrix.m32 * v.z + inMatrix.m33;

        if (Mathf.Abs(w) > 1.0e-7f)
        {
            float invW = 1.0f / w;
            output.x = res.x * invW;
            output.y = res.y * invW;
            output.z = res.z * invW;
            return true;
        }
        else
        {
            output.x = 0.0f;
            output.y = 0.0f;
            output.z = 0.0f;
            return false;
        }
    }
}

