using System;
using UnityEngine;
using ZGameChess;

namespace TKPlugins
{ 
    /// <summary>
    /// Like INotifyPropertyChanged. Contains property name, value and sender.
    /// </summary>
    [Serializable]
    public class ObservableMessage : IDisposable
    {
        public enum ValueType
        {
            None,
            INT,
            FLOAT,
            BOOL,
            OBJECT,
            TMessagerExp,
            TTurnTime,
        }
        
        /// <summary>
        /// The sender of this message
        /// </summary>
        [SerializeField]
        public object Sender;

		/// <summary>
		/// Property, Field, or Event ID
		/// </summary>
		[SerializeField]
		public int ID = 0;

        [SerializeField]
        public ValueType valueType = ValueType.None;
            /// <summary>
        /// Property, Field, or Member name
        /// </summary>
        [SerializeField]
        public string Name;
            
        /// <summary>
        /// Property,Field or Method arguments
        /// </summary>
        [SerializeField]
        public object Value;

        [SerializeField]
        public int intValue;

        [SerializeField] 
        public float floatValue;
        
        [SerializeField] 
        public bool boolValue;
        
        //[SerializeField] 
        //public BattleTurnModel.TTurnTime turnTimeValue;

        //[SerializeField] 
        //public BattleTurnModel.TMessagerExp messagerExpValue;

        /// <summary>
        /// Casts as value helper
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public T CastValue<T>()
        {
            return (T)Value;
        }

        public void Dispose()
        {
            Name = null;
            Value = Sender = null;
        }

        public override string ToString()
        {
            return "BindingMessage " + Name + " " + Value;
        }
    }

    /// <summary>
    /// Like INotifyPropertyChanged but with a more detailed event.
    /// </summary>
    public interface IObservableModel
    {
        /// <summary>
        /// Raised when a property is changed.
        /// </summary>
        event Action<ObservableMessage> ObserverList;

        /// <summary>
        /// Raises OnBindingMessage
        /// </summary>
        /// <param name="memberName"></param>
        /// <param name="paramater"></param>
        void NotifyUpdate(string memberName, object paramater);
 
        /// <summary>
        /// Gets the value of the property of field.
        /// Calls the method with return value
        /// </summary>
        /// <param name="memberName"></param>
        object GetValue(string memberName);

        /// <summary>
        /// Calls the method with argument and return value
        /// </summary>
        /// <param name="memberName"></param>
        /// <param name="paramater"></param>
        object GetValue(string memberName, object paramater);

    }
}