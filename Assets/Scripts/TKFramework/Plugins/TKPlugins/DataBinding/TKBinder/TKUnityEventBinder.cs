using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;


namespace TKPlugins
{
    public class TKUnityEventBinder : TKModelBinder
    {
//        // 绑定通知执行的Event
//        public UnityEvent eventAction;

        [HideInInspector]
        public BindingInfo EventBinding = new BindingInfo { BindingName = "event" };


        protected bool IsInit;
        protected object _oldValue;
        public override void Awake()
        {
            Init();
        }

		protected override void OnEnable()
		{
			Init();
		}

		protected override void OnDisable()
		{
			IsInit = false;
		}

        public override void Init()
        {
            if (IsInit)
                return;
            IsInit = true;
            _oldValue = null;
            EventBinding.Action = HandleEvent;
            EventBinding.Filters = BindingFilter.Properties;
        }

        public void HandleEvent(object arg)
        {
            if (_oldValue == null)
            {
                _oldValue = arg;
                return;
            }

            if (_oldValue != arg)
            {
               // eventAction.Invoke();

				SendMessage ("PlayAnim",null,SendMessageOptions.DontRequireReceiver);
                _oldValue = arg;
            }

        }

        protected override void RegisterBindingInfos()
        {
            bindingLst.Clear();
            bindingLst.Add(EventBinding);

            _infoCache = bindingLst.ToArray();
        }
    }
}
