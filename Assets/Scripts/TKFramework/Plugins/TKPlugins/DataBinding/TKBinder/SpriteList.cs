using System;
using System.Collections.Generic;
using UnityEngine;

namespace TKPlugins
{
    /// <summary>
    /// 同一个对象多精灵切换
    /// </summary>
    /// 
    [AddComponentMenu("TKPlugins/Databinding/SpritList")]
    public class SpriteList : MonoBehaviour
    {
        /// <summary>
        /// for ui editor. save editor binder.
        /// </summary>
        public List<Sprite> SpriteLst; 

        public Sprite[] ToArray()
        {
            return SpriteLst.ToArray();
        }
    }
}
