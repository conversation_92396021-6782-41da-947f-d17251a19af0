using System;
using System.Collections.Generic;
using UnityEngine;
using TKPlugins;

namespace TKPlugins
{
    public  class CammandBinder : BindingBase
    {
        [HideInInspector]
        public BindingInfo CmdBinding = new BindingInfo { BindingName = "Cammand" };

        protected bool IsInit;

        private Cammand _CmdHandler;

        public event Cammand CmdHandler
        {
            add
			{
				_CmdHandler = (Cammand)Delegate.Remove(_CmdHandler, value);
				_CmdHandler = (Cammand)Delegate.Combine(_CmdHandler, value);
            }
            remove
            {
                _CmdHandler = (Cammand)Delegate.Remove(_CmdHandler, value);
            }
        }

        public string Cammand
        {
            get
            {
                return CmdBinding.MemberName;
            }
        }



        protected void Awake()
        {
            Init();
        }

        public override void Init()
        {
            if (IsInit)
                return;
            IsInit = true;

            CmdBinding.Action = null;
            CmdBinding.Filters = BindingFilter.Commands;
        }

        public virtual void Execute()
        {
            if( _CmdHandler != null )
            {
                _CmdHandler();
            }
        }

        /// <summary>
        /// Handle all change notification here
        /// </summary>
        /// <param name="m"></param>
        public override void OnBindingMessage(ObservableMessage m)
        {

        }

        public virtual void OnDestroy()
        {
            _CmdHandler = null;
        }
    }
}
