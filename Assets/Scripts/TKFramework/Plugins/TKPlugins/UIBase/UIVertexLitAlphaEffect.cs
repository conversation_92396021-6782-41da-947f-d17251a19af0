using UnityEngine;
using UnityEngine.UI;

namespace ZGame.UIBase
{
    /// <summary>
    /// 用于处理带有Material的Image渐变效果的脚本
    /// </summary>
    public class UIVertexLitAlphaEffect : MonoBehaviour
    {
        private Material material;
        private Color _color;
        public int mainColorTo;
        private float colorNormalization;

        public float time = 0.5f;
        private float startTime = 0;

        void OnEnable()
        {
            startTime = 0;
            material = GetComponent<Image>().material;
            _color = material.GetColor("_Color");
            colorNormalization = mainColorTo / 255f;
        }

        void Start()
        {
            
        }

        void LateUpdate()
        {
            if(material != null)
            {
                startTime += Time.deltaTime;

                _color.a = startTime / time * colorNormalization;
                if (_color.a > colorNormalization)
                    _color.a = colorNormalization;

                material.SetColor("_Color", _color);
            }
        }

        void Destroy()
        {
            
        }
    }
}
