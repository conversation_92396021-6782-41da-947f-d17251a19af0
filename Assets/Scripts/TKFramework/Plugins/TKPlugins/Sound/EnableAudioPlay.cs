using UnityEngine;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using ZGameChess;

namespace TKPlugins
{
   
	/// <summary>
	/// 激活类音频播放脚本
	/// 物体enbale时就会播放脚本
	/// </summary>
    public class EnableAudioPlay : MonoBehaviour
	{
		/// <summary>
		/// 声音来源
		/// </summary>
		public AudioClip audioClip;

        ////////////////////wwise相关参数 start/////////////////////////////
        // 声音来源:bank文件名
        public string bankName = "";

        //AkSoundEngine.PostEvent()第一个event参数
        public string str_wise_eventName = "";

        //AkSoundEngine.SetState第一个stateGroup参数，可不填
        public string str_wwise_stateGroup = "";

        //AkSoundEngine.SetState第二个stateGroup参数，可不填
        public string str_wwise_state = "";
        ////////////////////wwise相关参数 end/////////////////////////////

        //重载点击响应接口
        private void OnEnable()
        {
            TKFrameworkDelegateInterface.ChessUtil_PlayWwiseBankByPath(bankName, str_wise_eventName, this.gameObject, null);
	        //ChessUtil.PlayWwiseBankByPath(bankName, str_wise_eventName, this.gameObject);
        }
	}
}
