using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using TKFrame;
using System;

/// <summary>
/// UGUI描边
/// </summary>
public class OutlineEx : BaseMeshEffect
{
    public Color OutlineColor = Color.white;
    [Range(0f, 12f)]
    public float OutlineWidth = 0f;      // 注意: OutlineWidth为0, ShadowOffset不为0时UV会错乱, 需要保证OutlineWidth大于0
    public Color ShadowColor = Color.white;
    public Vector2 ShadowOffset = new Vector2(0f, 0f);

    private static List<UIVertex> m_VetexList = new List<UIVertex>();

    private const string shaderPath = "shaders/based_shaders",
                         UIEffectKeyword = "_UIEFFECT_ON", 
                         outlineShaderName = "OutlineEx", 
                         outlineShaderReferenceName = "UI/OutlineEx", 
                         shadowColorName = "_ShadowColor", 
                         shadowOffsetName = "_ShadowOffset";

    private static Shader m_OutlineShader;
    private static Shader OutlineShader
    {
        get
        {
            if (m_OutlineShader == null)
            {
                if (Application.isEditor)
                {
                    m_OutlineShader = Shader.Find(outlineShaderReferenceName);
                }
                else
                {
                    var assetService = Services.GetService<IAssetService>();
                    var loadedAsset = assetService.LoadAssetSync<Shader>(shaderPath, outlineShaderName);
                    m_OutlineShader = loadedAsset.GetAsset<Shader>();
                    loadedAsset.Retain();
                }

                if (m_OutlineShader == null)
                {
                    Diagnostic.Error(string.Format("OutlineEx: failed to find {0}.shader asset in assetbundle {1}", outlineShaderName, shaderPath));
                }
            }

            return m_OutlineShader;
        }
    }

    protected override void Awake()
    {
        base.Awake();

        if (graphic)
        {
            if ((graphic.material == null || graphic.material.shader.name != outlineShaderReferenceName) && OutlineShader != null)
            {
                graphic.material = new Material(OutlineShader);
                graphic.material.EnableKeyword(UIEffectKeyword);
            }

            if (graphic.canvas)
            {
                var v1 = graphic.canvas.additionalShaderChannels;
                var v2 = AdditionalCanvasShaderChannels.TexCoord1;
                if ((v1 & v2) != v2)
                {
                    graphic.canvas.additionalShaderChannels |= v2;
                }
                v2 = AdditionalCanvasShaderChannels.TexCoord2;
                if ((v1 & v2) != v2)
                {
                    graphic.canvas.additionalShaderChannels |= v2;
                }
                v2 = AdditionalCanvasShaderChannels.TexCoord3;
                if ((v1 & v2) != v2)
                {
                    graphic.canvas.additionalShaderChannels |= v2;
                }
                v2 = AdditionalCanvasShaderChannels.Tangent;
                if ((v1 & v2) != v2)
                {
                    graphic.canvas.additionalShaderChannels |= v2;
                }
                v2 = AdditionalCanvasShaderChannels.Normal;
                if ((v1 & v2) != v2)
                {
                    graphic.canvas.additionalShaderChannels |= v2;
                }
            }
            Refresh();
        }
    }


#if UNITY_EDITOR
    protected override void OnValidate()
    {
        base.OnValidate();

        if (graphic != null && graphic.material != null)
        {
            if ((graphic.material.shader.name != outlineShaderReferenceName) && OutlineShader != null)
            {
                graphic.material = new Material(OutlineShader);
                graphic.material.EnableKeyword(UIEffectKeyword);
            }
            Refresh();
        }
    }
#endif


    private void Refresh()
    {
        if (graphic == null || graphic.material == null)
        {
            return;
        }

        /*if (graphic.material.GetInt("_OutlineWidth") != OutlineWidth || graphic.material.GetColor("_OutlineColor") != OutlineColor)
        {
            graphic.material.SetColor("_OutlineColor", this.OutlineColor);
            graphic.material.SetInt("_OutlineWidth", this.OutlineWidth);
        }*/

        Vector4 offset = new Vector4(ShadowOffset.x, ShadowOffset.y, 0f, 0f);
        if (graphic.material.GetColor(shadowColorName) != ShadowColor || graphic.material.GetVector(shadowOffsetName) != offset)
        {
            graphic.material.SetColor(shadowColorName, ShadowColor);
            graphic.material.SetVector(shadowOffsetName, offset);
        }

        graphic.SetVerticesDirty();
    }


    public override void ModifyMesh(VertexHelper vh)
    {
        vh.GetUIVertexStream(m_VetexList);

        ProcessVertices();

        vh.Clear();
        vh.AddUIVertexTriangleStream(m_VetexList);
    }


    private void ProcessVertices()
    {
        for (int i = 0, count = m_VetexList.Count - 3; i <= count; i += 3)
        {
            var v1 = m_VetexList[i];
            var v2 = m_VetexList[i + 1];
            var v3 = m_VetexList[i + 2];
            // 计算原顶点坐标中心点
            var minX = _Min(v1.position.x, v2.position.x, v3.position.x);
            var minY = _Min(v1.position.y, v2.position.y, v3.position.y);
            var maxX = _Max(v1.position.x, v2.position.x, v3.position.x);
            var maxY = _Max(v1.position.y, v2.position.y, v3.position.y);
            var posCenter = new Vector2(minX + maxX, minY + maxY) * 0.5f;
            // 计算原始顶点坐标和UV的方向
            Vector2 triX, triY, uvX, uvY;
            Vector2 pos1 = v1.position;
            Vector2 pos2 = v2.position;
            Vector2 pos3 = v3.position;
            if (Mathf.Abs(Vector2.Dot((pos2 - pos1).normalized, Vector2.right))
                > Mathf.Abs(Vector2.Dot((pos3 - pos2).normalized, Vector2.right)))
            {
                triX = pos2 - pos1;
                triY = pos3 - pos2;
                uvX = v2.uv0 - v1.uv0;
                uvY = v3.uv0 - v2.uv0;
            }
            else
            {
                triX = pos3 - pos2;
                triY = pos2 - pos1;
                uvX = v3.uv0 - v2.uv0;
                uvY = v2.uv0 - v1.uv0;
            }
            // 计算原始UV框
            var uvMin = _Min(v1.uv0, v2.uv0, v3.uv0);
            var uvMax = _Max(v1.uv0, v2.uv0, v3.uv0);
            //OutlineColor 和 OutlineWidth 也传入，避免出现不同的材质球
            var col_rg = new Vector2(OutlineColor.r, OutlineColor.g);       //描边颜色 用uv3 和 tangent的 zw传递
            var col_ba = new Vector4(0, 0, OutlineColor.b, OutlineColor.a);
            var normal = new Vector3(0, 0, OutlineWidth);                   //描边的宽度 用normal的z传递

            // 为每个顶点设置新的Position和UV，并传入原始UV框
            v1 = SetNewPosAndUV(v1, this.OutlineWidth, this.ShadowOffset, posCenter, triX, triY, uvX, uvY, uvMin, uvMax);
            v1.uv3 = col_rg;
            v1.tangent = col_ba;
            v1.normal = normal;
            v2 = SetNewPosAndUV(v2, this.OutlineWidth, this.ShadowOffset, posCenter, triX, triY, uvX, uvY, uvMin, uvMax);
            v2.uv3 = col_rg;
            v2.tangent = col_ba;
            v2.normal = normal;
            v3 = SetNewPosAndUV(v3, this.OutlineWidth, this.ShadowOffset, posCenter, triX, triY, uvX, uvY, uvMin, uvMax);
            v3.uv3 = col_rg;
            v3.tangent = col_ba;
            v3.normal = normal;

            // 应用设置后的UIVertex
            m_VetexList[i] = v1;
            m_VetexList[i + 1] = v2;
            m_VetexList[i + 2] = v3;
        }
    }

    private static UIVertex SetNewPosAndUV(UIVertex pVertex, float pOutLineWidth, Vector2 pShadowOffset, 
        Vector2 pPosCenter,
        Vector2 pTriangleX, Vector2 pTriangleY,
        Vector2 pUVX, Vector2 pUVY,
        Vector2 pUVOriginMin, Vector2 pUVOriginMax)
    {
        // 最终的Offset由outlineWidth和shadowOffset中的最大值决定
        float finalWidth = Mathf.Max(pOutLineWidth, Mathf.Max(Mathf.Abs(pShadowOffset.x), Mathf.Abs(pShadowOffset.y)));
        // Position
        var pos = pVertex.position;
        var posXOffset = pos.x > pPosCenter.x ? finalWidth : -finalWidth;
        var posYOffset = pos.y > pPosCenter.y ? finalWidth : -finalWidth;
        pos.x += posXOffset;
        pos.y += posYOffset;
        pVertex.position = pos;
        // UV
        var uv = pVertex.uv0;
        uv += pUVX / pTriangleX.magnitude * posXOffset * (Vector2.Dot(pTriangleX, Vector2.right) > 0 ? 1 : -1);
        uv += pUVY / pTriangleY.magnitude * posYOffset * (Vector2.Dot(pTriangleY, Vector2.up) > 0 ? 1 : -1);
        pVertex.uv0 = uv;

        pVertex.uv1 = pUVOriginMin;     //uv1 uv2 可用  tangent  normal 在缩放情况 会有问题
        pVertex.uv2 = pUVOriginMax;

        return pVertex;
    }

    private static float _Min(float pA, float pB, float pC)
    {
        return Mathf.Min(Mathf.Min(pA, pB), pC);
    }

    private static float _Max(float pA, float pB, float pC)
    {
        return Mathf.Max(Mathf.Max(pA, pB), pC);
    }

    private static Vector2 _Min(Vector2 pA, Vector2 pB, Vector2 pC)
    {
        return new Vector2(_Min(pA.x, pB.x, pC.x), _Min(pA.y, pB.y, pC.y));
    }

    private static Vector2 _Max(Vector2 pA, Vector2 pB, Vector2 pC)
    {
        return new Vector2(_Max(pA.x, pB.x, pC.x), _Max(pA.y, pB.y, pC.y));
    }
}