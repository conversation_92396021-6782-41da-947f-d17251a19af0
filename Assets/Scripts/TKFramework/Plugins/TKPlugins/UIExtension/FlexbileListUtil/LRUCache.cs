using UnityEngine;
using System.Collections.Generic;

namespace TKFrame
{
    public class LRUCache<T>
    {
        TKDictionary<T, LinkedListNode<T>>
                        _dict = new TKDictionary<T, LinkedListNode<T>>();
        LinkedList<T> _list = new LinkedList<T>();

        ICacheHandler _cacheHandler;
        float _touchTime;
        public LRUCache(ICacheHandler handler)
        {
            _cacheHandler = handler;
        }
        public float TouchTime
        {
            get
            {
                return _touchTime;
            }
        }
        public int Count
        {
            get
            {
                return _list.Count;
            }
        }
        public interface ICacheHandler
        {
            void ReleaseItem(T value);
            int MaxCount { get; }
        }

        public void RemoveLast()
        {
            var node = _list.Last;
            _list.RemoveLast();
            _dict.Remove(node.Value);
            _cacheHandler.ReleaseItem(node.Value);
        }
        public void Check()
        {
            var _maxCount = _cacheHandler.MaxCount;
            if (_maxCount <= 0) return;
            while (_list.Count > _maxCount)
            {
                RemoveLast();
            }
        }
        public void Remove(T obj)
        {
            LinkedListNode<T> node;
            if (_dict.TryGetValue(obj, out node))
            {
                _dict.Remove(node.Value);
                _list.Remove(node);
            }
        }
        public void Touch(T obj)
        {
            _touchTime = Time.realtimeSinceStartup;
            LinkedListNode<T> node;
            if (_dict.TryGetValue(obj, out node))
            {
                _list.Remove(node);
                _list.AddFirst(node);
                Check();
            }
            else
            {
                node = _list.AddFirst(obj);
                _dict.Add(obj, node);
            }
        }

        public void Clear()
        {
            _list.Clear();
            _dict.Clear();
        }
    }
}