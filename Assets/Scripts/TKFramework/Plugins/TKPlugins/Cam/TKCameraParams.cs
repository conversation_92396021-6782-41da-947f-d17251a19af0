using UnityEngine;
using System;
using System.Collections;


public enum ECameraCtrlType
{
    ECam_Focus,
    ECam_FocusNoDir,
}

//摄像机控制参数辅助类
public class TKCameraParams : ScriptableObject {
    public ECameraCtrlType camCtrl;
    public float delay;
    public float lifeTime;
    public float enterTime;//进入时间
    public float exitTime;//退出时间


    public float distance; //距离目标距离

    public float xAngle;// pitch 角度倾斜
    public float yAngle;// yaw 左右转到角度
}
