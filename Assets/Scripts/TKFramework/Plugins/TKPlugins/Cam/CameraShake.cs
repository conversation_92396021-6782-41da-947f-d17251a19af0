using UnityEngine;
using System.Collections;
using TKFrame;


namespace TKPlugins
{
    public enum ShakeAmplitude
    {
        Slight,
        Middle,
        High
    }

    public class CameraShake : MonoBehaviour
    {
        // Transform of the camera to shake. Grabs the gameObject's transform
        // if null.
        public Transform camTransform;

        // How long the object should shake for.
        public float shake = 0f;

        // Amplitude of the shake. A larger value shakes the camera harder.
        public float shakeAmount = 0.2f;
        public float decreaseFactor = 1.0f;

        Vector3 originalPos;

        public float Shake
        {
            set
            {
                shake = value;
                this.enabled = true;
            }
        }

        void Awake()
        {
            if (camTransform == null)
            {
                camTransform = GetComponent(typeof(Transform)) as Transform;
            }
        }

        void OnEnable()
        {
            originalPos = camTransform.localPosition;
        }

        void Update()
        {
            if (shake > 0)
            {
                camTransform.localPosition = originalPos + Random.insideUnitSphere * shakeAmount;

                shake -= Time.unscaledDeltaTime * decreaseFactor;
            }
            else
            {
                shake = 0f;
                camTransform.localPosition = originalPos;
                this.enabled = false;
            }
        }

        public void SetShake(ShakeAmplitude amplitude)
        {
            //Diagnostic.Error("CameraShake SetShake {0}", amplitude.ToString());
            if(amplitude == ShakeAmplitude.Slight)
            {
                Shake = 0.1f;
                shakeAmount = 0.1f;
                decreaseFactor = 1;
            }
            else if (amplitude == ShakeAmplitude.Middle)
            {
                Shake = 0.1f;
                shakeAmount = 0.2f;
                decreaseFactor = 1;
            }
            else if (amplitude == ShakeAmplitude.High)
            {
                Shake = 0.1f;
                shakeAmount = 0.3f;
                decreaseFactor = 1;
            }
        }
    }
}
