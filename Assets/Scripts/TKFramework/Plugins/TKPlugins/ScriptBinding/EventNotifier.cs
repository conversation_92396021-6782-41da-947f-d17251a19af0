using UnityEngine;
using System.Collections.Generic;
using System;
namespace TKPlugins.ScriptBinding
{

    /// <summary>
    /// ScriptBehaivourBinder事件通知器，帮助ScriptBehaivourBinder接受MonoBehaviour层的事件
    /// 当MonoBehaviour产生事件时，Unity先会通知对应的EventNotifier。EventNotifier则将事件转发到ScriptBehaivourBinder再之LuaBehaviour
    /// </summary>
    public abstract class EventNotifier : MonoBehaviour
    {
        public ScriptBehaviourBinder binder;
    }
}