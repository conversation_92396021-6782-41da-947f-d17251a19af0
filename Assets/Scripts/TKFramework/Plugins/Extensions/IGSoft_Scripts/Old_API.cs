using System;
using UnityEngine;

public struct Particle
{
    public Vector3 position;
    public float energy;
    public float size;
    internal float angularVelocity;
    internal float velocity;
}
public class ParticleEmitter : MonoBehaviour
{
    public int particleCount;
    public float minSize;
    public float maxSize;
    public float minEnergy;
    public float maxEnergy;
    public float minEmission;
    public float maxEmission;
    public Vector3 worldVelocity;
    public Vector3 localVelocity;
    public Vector3 rndVelocity;
    public float angularVelocity;
    public float rndAngularVelocity;
    public float emitterVelocityScale;
    Particle[] _particles = new Particle[0];
    public bool emit { get; set; }
    public Particle[] particles { get { return _particles; } set { _particles = value; } }
    public bool useWorldSpace { get; set; }

    public void ClearParticles()
    {
        
    }

    public void Emit(int m_fBurstEmissionCount)
    {
        
    }
}
public class EllipsoidParticleEmitter : MonoBehaviour
{

}
public class MeshParticleEmitter : MonoBehaviour
{

}
public class ParticleAnimator : MonoBehaviour
{
    public Vector3 rndForce;
    public Vector3 force;

    public bool autodestruct { get; set; }
}
public class ParticleRenderer : MonoBehaviour
{
    public float lengthScale;
}