using UnityEngine;
using UnityEditor;


public class SetShaderLOD : EditorWindow
{
    string lodText = "0";

    [MenuItem("Tools/GraphicsTool/Set Global Shader LOD...")]
    public static void ShowWindow()
    {
        GetWindow<SetShaderLOD>();
    }

    void OnGUI()
    {
        lodText = EditorGUILayout.TextField("Shader LOD", lodText);
        if (GUILayout.Button("Set LOD"))
        {
            int.TryParse(lodText, out int lod);
            if (lod > 0)
            {
                Debug.Log("Set global shader lod: " + lodText);
                Shader.globalMaximumLOD = lod;
            }
        }
    }
}