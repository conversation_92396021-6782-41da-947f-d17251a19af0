/*
	SetRenderQueue.cs
	
	Sets the RenderQueue of an object's materials on Awake. This will instance
	the materials, so the script won't interfere with other renderers that
	reference the same materials.
*/

using System.Collections.Generic;
using UnityEngine;

[AddComponentMenu("Rendering/SetRenderQueue")]
public class SetRenderQueue : MonoBehaviour {
	
	public int m_queue = 3000;
	private List<Renderer> m_renderers = new List<Renderer>();
	
	protected void Update() 
	{
		GetComponentsInChildren<Renderer>(m_renderers);
		foreach (Renderer renderer in m_renderers)
		{
			Material[] mats = renderer.materials;
			for(int m = 0; m< mats.Length; m++)
			{
				mats[m].renderQueue = m_queue;
			}
		}
	}
}