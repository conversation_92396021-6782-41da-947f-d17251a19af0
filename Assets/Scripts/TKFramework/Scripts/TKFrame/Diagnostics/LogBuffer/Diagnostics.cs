using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using TKFrame;
using UnityEngine;
using Wup.Jce;

namespace TKFrame
{
    public enum LogMsgType
    {
        STR = 0,
        JCE,
        BYTE,
    }

    public enum LogMsgContent
    {
        NORMAL = 0,
        SEND,
        RECV,
        BATTLE,
        CSOGAME,
        LOCKSTEP,
    }

    public class Diagnostic
    {
        public static byte logVersion = 1;

        public static byte decodeLogVersion = 0;
        
        private static string _logFilePath;

        public delegate bool LogEnableOverrideDelegate();
        public static LogEnableOverrideDelegate LogEnableOverride;

        public static void SaveAssets()
        {
#if UNITY_EDITOR
            Debug.Log("Saved assets");
            UnityEditor.AssetDatabase.SaveAssets();
#endif
        }

        // 工程内所有打印日志接口全部收归到此处。
        public static void SetLogEnabled(bool enabled)
        {
            Debug.unityLogger.logEnabled = LogEnableOverride != null ? LogEnableOverride() : enabled;
        }

        public static bool GetLogEnabled()
        {
            return Debug.unityLogger.logEnabled;
        }
        
        private static string logFilePath
        {
            get
            {
#if ACGGAME_CLIENT
                if (_logFilePath == null)
                    _logFilePath = TKFrameConfig.Instance.LogFilePath();
                return _logFilePath;
#else
               return Application.dataPath.Replace("Assets/","") + "/TKFrame_Logs/";
#endif
            }
        }

        private static string _replayFilePath;

        private static string ReplayFilePath
        {
            get
            {
#if ACGGAME_CLIENT
                if (_replayFilePath == null)
                    _replayFilePath = TKFrameworkDelegateInterface.GetReplayDirectory();
                return _replayFilePath;
#else
               return Application.dataPath.Replace("Assets/","") + "/FrameSyncReplayData/";
#endif
            }
        }

        public static void InitLogPath()
        {
#if ACGGAME_CLIENT
            _logFilePath = TKFrameConfig.Instance.LogFilePath();  
#endif
        }
        
        //日志名称设定,区分不同平台
        public static string logFileName
        {
            get
            {
                return logFilePath;
            }
            set
            {
                _logFilePath = value;
            }
        }

        //是否已经清理过期日志
        public static bool hasClearOutDateLog = false;
        //最大存放日志目录数目，多余的程序启动时删除
        public static int MAX_SAVE_LOG_FOLDER_COUNT = 7;
        //玩家手动上报日志的最大天数
        public static int MAX_UPLOAD_LOG_FOLDER_COUNT = 3;
        //当前正在写入日志文件的绝对路径
        public static string CurrentFilePath { get; private set; }
        //当前正在写入日志文件的名字
        public static string CurrentLogFileName { get; private set; }

        #region Log，Warn，Error For Lua

#if !OUTSOURCE
        [System.Diagnostics.Conditional("DEBUG_LEVEL_LOG")]
        [System.Diagnostics.Conditional("DEBUG_LEVEL_WARN")]
        [System.Diagnostics.Conditional("DEBUG_LEVEL_ERROR")]
#endif
        [LightProfiler.ProfilerAttribute]
        public static void Log(string str)
        {
            if (str.Contains("ShaderManager.cs"))
                return;
#if UNITY_EDITOR
            UnityEngine.Debug.Log(str);
#endif
            WriteLog(str);
        }
#if !OUTSOURCE
        [System.Diagnostics.Conditional("DEBUG_LEVEL_WARN")]
        [System.Diagnostics.Conditional("DEBUG_LEVEL_ERROR")]
#endif
        [LightProfiler.ProfilerAttribute]
        public static void Warn(string str)
        {
#if UNITY_EDITOR
            UnityEngine.Debug.LogWarning(str);
#endif
            WriteLog(str);
        }
#if !OUTSOURCE
        [System.Diagnostics.Conditional("DEBUG_LEVEL_ERROR")]
#endif
        [LightProfiler.ProfilerAttribute]
        public static void Error(string str)
        {
#if UNITY_EDITOR
            UnityEngine.Debug.LogError(str);
#endif
            WriteLog(str);
        }
#endregion

#region Log，Warn，Error
#if !OUTSOURCE
        [System.Diagnostics.Conditional("DEBUG_LEVEL_LOG")]
        [System.Diagnostics.Conditional("DEBUG_LEVEL_WARN")]
        [System.Diagnostics.Conditional("DEBUG_LEVEL_ERROR")]
#endif
        [LightProfiler.ProfilerAttribute]
        public static void Log(object format, params object[] paramList)
        {
#if DEBUG_LEVEL_LOG && DEBUG_LEVEL_WARN && DEBUG_LEVEL_ERROR
            string str = convertToString(format, paramList);
#if UNITY_EDITOR
            UnityEngine.Debug.Log(str);
#endif
            WriteLog(str);
#endif
        }

        /// <summary>
        /// <br>Unlike <see cref="Log(object, object[])"/>, verbose will no longer output log to release version.</br>
        /// <br>Controlled by JK_RELEASE macro.</br>
        /// </summary>
        /// <param name="format"></param>
        /// <param name="paramList"></param>
#if !OUTSOURCE
        [System.Diagnostics.Conditional("DEBUG_LEVEL_LOG")]
        [System.Diagnostics.Conditional("DEBUG_LEVEL_WARN")]
        [System.Diagnostics.Conditional("DEBUG_LEVEL_ERROR")]
#endif
        [LightProfiler.ProfilerAttribute]
        public static void Verbose(object format, params object[] paramList)
        {
#if !JK_RELEASE
            Log(format, paramList);
#endif
        }
#if !OUTSOURCE
        [System.Diagnostics.Conditional("DEBUG_LEVEL_LOG")]
        [System.Diagnostics.Conditional("DEBUG_LEVEL_WARN")]
        [System.Diagnostics.Conditional("DEBUG_LEVEL_ERROR")]
#endif
        public static void LogJceStruct(int msgId, JceStruct data, LogMsgContent type = LogMsgContent.NORMAL)
        {
#if UNITY_EDITOR
            UnityEngine.Debug.Log(JceStructToString(data));
#endif
            WriteLog(msgId, data, type);
        }
#if !OUTSOURCE
        [System.Diagnostics.Conditional("DEBUG_LEVEL_WARN")]
        [System.Diagnostics.Conditional("DEBUG_LEVEL_ERROR")]
#endif
        [LightProfiler.ProfilerAttribute]
        public static void Warn(object format, params object[] paramList)
        {
#if DEBUG_LEVEL_WARN && DEBUG_LEVEL_ERROR
            string str = convertToString(format, paramList);
#if UNITY_EDITOR
            UnityEngine.Debug.LogWarning(str);
#endif
            WriteLog(str);
#endif
        }
#if !OUTSOURCE
        [System.Diagnostics.Conditional("DEBUG_LEVEL_WARN")]
        [System.Diagnostics.Conditional("DEBUG_LEVEL_ERROR")]
#endif
        public static void WarnJceStruct(int msgId, JceStruct data, LogMsgContent type = LogMsgContent.NORMAL)
        {
#if UNITY_EDITOR
            UnityEngine.Debug.LogWarning(JceStructToString(data));
#endif
            WriteLog(msgId, data, type);
        }
#if !OUTSOURCE
        [System.Diagnostics.Conditional("DEBUG_LEVEL_ERROR")]
#endif
        [LightProfiler.ProfilerAttribute]
        public static void Error(object format, params object[] paramList)
        {
#if DEBUG_LEVEL_ERROR || OUTSOURCE
            string str = convertToString(format, paramList);
#if UNITY_EDITOR
            UnityEngine.Debug.LogError(str);
#endif
            WriteLog(str);
#endif
        }
#if !OUTSOURCE
        [System.Diagnostics.Conditional("DEBUG_LEVEL_ERROR")]
#endif
        public static void ErrorJceStruct(int msgId, JceStruct data, LogMsgContent type = LogMsgContent.NORMAL)
        {
#if UNITY_EDITOR
            UnityEngine.Debug.LogError(JceStructToString(data));
#endif
            WriteLog(msgId, data, type);
        }
#endregion

#region AI打印
#if !OUTSOURCE
        [System.Diagnostics.Conditional("DEBUG_LEVEL_LOG")]
        [System.Diagnostics.Conditional("DEBUG_LEVEL_WARN")]
        [System.Diagnostics.Conditional("DEBUG_LEVEL_ERROR")]
#endif
        public static void AiOptLog(object format, params object[] paramList)
        {
            string str = convertToString(format, paramList);
//#if UNITY_EDITOR
//                UnityEngine.Debug.LogError(str);
//#endif
            WriteLog(str);
        }
#endregion
        
#region lua打印
        public static void LuaLog(string msg)
        {
            var savedLogType = Application.GetStackTraceLogType(LogType.Log);
            Application.SetStackTraceLogType(LogType.Log, StackTraceLogType.None);
#if UNITY_EDITOR
            UnityEngine.Debug.Log(msg);
#endif
            Application.SetStackTraceLogType(LogType.Log, savedLogType);
            WriteLog(msg);
        }

        public static void LuaWarn(string msg)
        {
            var savedLogType = Application.GetStackTraceLogType(LogType.Log);
            Application.SetStackTraceLogType(LogType.Warning, StackTraceLogType.None);
#if UNITY_EDITOR
            UnityEngine.Debug.LogWarning(msg);
#endif
            Application.SetStackTraceLogType(LogType.Warning, savedLogType);
            WriteLog(msg);
        }

        public static void LuaError(string msg)
        {
            //var savedLogType = Application.stackTraceLogType;
            //Application.stackTraceLogType = StackTraceLogType.None;
#if UNITY_EDITOR
            UnityEngine.Debug.LogError(msg);
#endif
            //Application.stackTraceLogType = savedLogType;
            WriteLog(msg);
        }
#endregion
        
#region Assert
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        [System.Diagnostics.Conditional("DEBUG_LEVEL_LOG")]
        public static void Assert(bool condition)
        {
            Assert(condition, string.Empty, true);
        }

        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        [System.Diagnostics.Conditional("DEBUG_LEVEL_LOG")]
        public static void Assert(bool condition, string assertString)
        {
            Assert(condition, assertString, false);
        }

        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        [System.Diagnostics.Conditional("DEBUG_LEVEL_LOG")]
        public static void Assert(bool condition, string format, params object[] args)
        {
            Assert(condition, convertToString(format, args), false);
        }

        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        [System.Diagnostics.Conditional("DEBUG_LEVEL_LOG")]
        public static void Assert(bool condition, string assertString, bool pauseOnFail)
        {
            if (!condition)
            {
                UnityEngine.Debug.LogError(assertString);

                if (pauseOnFail)
                    UnityEngine.Debug.Break();
            }
        }
#endregion

#region 将各种信息转换为byte，并写入线程中

        private static void Reset(MemoryStream ms)
        {
            ms.Seek(0, SeekOrigin.Begin);
            ms.SetLength(0);
        }
        
        private static void WriteLog(string msg)
        {
            //清理过期日志
            if (hasClearOutDateLog == false)
            {
                hasClearOutDateLog = true;
                SearchAndDeleteOutDateLogFile();
            }

            MemoryStream ms = ThreadData.GetMemoryStream();
            BinaryWriter bw = ThreadData.GetBinaryWriter();

            Reset(ms);
            bw.Write(DateTime.Now.Ticks);
            bw.Write((byte)LogMsgType.STR);
            bw.Write((byte) LogMsgContent.NORMAL);
            
            MemoryStream byteMs = ThreadData.GetByteMemoryStream();
            BinaryWriter byteBw = ThreadData.GetByteBinaryWriter();
            Reset(byteMs);
            byteBw.WriteRawString(msg.TrimEnd('\0'));   // 某些Log会莫名带有\0字符结尾，可能是字符编码的问题，会导致文本编辑器(sublime)全局搜索时产生问题，这里通过trim处理一下

            bw.Write((int)byteMs.Position);
            bw.Write(byteMs.GetBuffer(), 0, (int)byteMs.Position);
            
            WriteBuffer(ms.GetBuffer(), 0, (int)ms.Position);
        }

        public static void WriteLog(int msgID, JceStruct data, LogMsgContent msgContent = LogMsgContent.NORMAL)
        {
            //清理过期日志
            if (hasClearOutDateLog == false)
            {
                hasClearOutDateLog = true;
                SearchAndDeleteOutDateLogFile();
            }

            MemoryStream ms = ThreadData.GetMemoryStream();
            BinaryWriter bw = ThreadData.GetBinaryWriter();
            
            Reset(ms);
            bw.Write(DateTime.Now.Ticks);
            bw.Write((byte)LogMsgType.JCE);
            bw.Write((byte)msgContent);
            bw.Write(msgID);

            MemoryStream jceMs = ThreadData.GetJceWriteMemoryStream();
            BinaryWriter jceBw = ThreadData.GetJceBinaryWriter();
            Reset(jceMs);

            JceOutputStream msgOS = new JceOutputStream(jceMs, jceBw);
            if (data != null)
                data.WriteTo(msgOS);

            //ProtoUtil.JceStructToBytes(data, jceMs, jceBw);
            
            bw.Write((int)jceMs.Position);
            bw.Write(jceMs.GetBuffer(), 0, (int)jceMs.Position);
            
            WriteBuffer(ms.GetBuffer(), 0, (int)ms.Position);
        }

        public static BinaryWriter BeginWriteLog(int msgID, LogMsgContent msgContent = LogMsgContent.NORMAL)
        {
            MemoryStream ms = ThreadData.GetMemoryStream();
            BinaryWriter bw = ThreadData.GetBinaryWriter();
            
            Reset(ms);
            bw.Write(DateTime.Now.Ticks);
            bw.Write((byte)LogMsgType.BYTE);
            bw.Write(((byte)msgContent));
            bw.Write(msgID);
            
            MemoryStream byteMs = ThreadData.GetByteMemoryStream();
            BinaryWriter byteBw = ThreadData.GetByteBinaryWriter();
            Reset(byteMs);

            return byteBw;
        }

        public static void EndWriteLog()
        {
            MemoryStream ms = ThreadData.GetMemoryStream();
            BinaryWriter bw = ThreadData.GetBinaryWriter();
            
            MemoryStream byteMs = ThreadData.GetByteMemoryStream();
            
            bw.Write((int)byteMs.Position);
            bw.Write(byteMs.GetBuffer(), 0, (int)byteMs.Position);
            
            WriteBuffer(ms.GetBuffer(), 0, (int)ms.Position);
        }

        /*
        public static void WriteLog(int msgID, Action<BinaryWriter> func, LogMsgContent msgContent = LogMsgContent.NORMAL)
        {
            MemoryStream ms = ThreadData.GetMemoryStream();
            BinaryWriter bw = ThreadData.GetBinaryWriter();
            
            Reset(ms);
            bw.Write(DateTime.Now.Ticks);
            bw.Write((byte)LogMsgType.BYTE);
            bw.Write(((byte)msgContent));
            bw.Write(msgID);
            
            MemoryStream byteMs = ThreadData.GetByteMemoryStream();
            BinaryWriter byteBw = ThreadData.GetByteBinaryWriter();
            Reset(byteMs);
            if (func != null)
                func(byteBw);
            
            bw.Write((int)byteMs.Position);
            bw.Write(byteMs.GetBuffer(), 0, (int)byteMs.Position);
            
            WriteBuffer(ms.GetBuffer(), 0, (int)ms.Position);
        }
        */
        
        public static void WriteJceByte(int msgID, MemoryStream data, LogMsgContent msgContent = LogMsgContent.NORMAL)
        {
            MemoryStream ms = ThreadData.GetMemoryStream();
            BinaryWriter bw = ThreadData.GetBinaryWriter();
            
            Reset(ms);
            bw.Write(DateTime.Now.Ticks);
            bw.Write((byte)LogMsgType.JCE);
            bw.Write(((byte)msgContent));
            bw.Write(msgID);
            bw.Write((int)data.Length);
            bw.Write(data.GetBuffer(), 0, (int)data.Length);
            WriteBuffer(ms.GetBuffer(), 0, (int)ms.Position);
        }
        
        private static void WriteBuffer(byte[] data, int startIndex, int count)
        {
            LogWriteThread.Instance.PushData(data, startIndex, count);
        }

#endregion

#region 将任意格式转换为string
        private static string convertToString(object format, params object[] paramList)
        {
            string str;

            if (format is string)
            {
                if (paramList != null && paramList.Length > 0)
                    str = string.Format(format as string, paramList);
                else
                    str = format as string;
            }
            else
            {
                str = format.ToString();
            }
            return str;
        }
        
        private static string JceStructToString(JceStruct msg)
        {
            if (msg != null)
            {
                StringBuilder _sb = MicroObjectPool<StringBuilder>.Get();
                
                _sb.Append(msg.GetType().Name + "=>");
                msg.Display(_sb, 0);
                
                string resultStr = _sb.ToString();
                MicroObjectPool<StringBuilder>.Release(_sb);
                return resultStr;
            }
            else
            {
                return string.Empty;
            }
        }
        
#endregion
 
        public static string GenerateLogFileName()
        {
            CurrentLogFileName = DateTime.Now.ToString("yyyy_MM_dd");
            string curDirName = logFilePath + CurrentLogFileName;
            if (Directory.Exists(logFilePath) == false)
                Directory.CreateDirectory(logFilePath);
            if (Directory.Exists(curDirName) == false)
                Directory.CreateDirectory(curDirName);
            string curLogFileName = curDirName + DateTime.Now.ToString("/HH_mm_ss");
            CurrentFilePath = curLogFileName;
            return curLogFileName;
        }

#region 关闭线程和换文件

        public static void CloseAndResetLogFile()
        {
            LogWriteThread.Instance.CloseAndResetLogFile();
        }

        // 这个API尽量不要用。要用using (new LogShutDownSafe(1)){}代替
        public static void Shutdown()
        {
            LogWriteThread.Instance.Shutdown();
        }

        public static void Start()
        {
            LogWriteThread.Instance.Start();
        }

#endregion

#region 查找文件
        //查找最新创建的日志文件夹,以天为单位        
        public static List<string> SearchNewestDateLogFile(int iDayCount)
        {
            List<string> pathList = new List<string>();
            try
            {
                //遍历文件夹,按照文件夹创建时间进行排序
                DirectoryInfo TheFolder = new DirectoryInfo(logFilePath);
                DirectoryInfo[] dirNames = TheFolder.GetDirectories();
                if (dirNames != null && dirNames.Length > 0)
                {
                    //根据日志文件夹的创建时间进行排序，按照创建时间新的在最前面排序 
                    DateCompareFileInfo dateCompareFileInfo = new DateCompareFileInfo();
                    Array.Sort(dirNames, dateCompareFileInfo);

                    for (int i = 0; (i < iDayCount) && (i <= dirNames.Length); i++)
                    {
                        pathList.Add(dirNames[i].FullName);
                    }
                }
            }
            catch
            {
            }
            finally
            {
            }
            return pathList;
        }

        public static bool SearchLogFileTime(DateTime dateBegin, DateTime dateEnd,out List<FileInfo> fileList)
        {
            List<string> pathList = new List<string>();
            fileList = new List<FileInfo>();
            try
            {
                //遍历文件夹,按照文件夹创建时间进行排序
                DirectoryInfo TheFolder = new DirectoryInfo(logFilePath);
                DirectoryInfo[] dirNames = TheFolder.GetDirectories();
                if (dirNames != null && dirNames.Length > 0)
                {
                    //根据日志文件夹的创建时间进行排序，按照创建时间新的在最前面排序 
                    DateCompareFileInfo dateCompareFileInfo = new DateCompareFileInfo();
                    Array.Sort(dirNames, dateCompareFileInfo);
                    for (int i = 0; (i < 100) && (i <= dirNames.Length); i++)
                    {
                        foreach (var file in dirNames[i].GetFiles())
                        {
                            DateTime createTime = file.CreationTime;
                            if (createTime >= dateBegin && createTime <= dateEnd)
                            {
                                pathList.Add(file.FullName);
                                fileList.Add(file);
                            }
                        }
                    }
                }
            }
            catch
            {
            }
            finally
            {
            }
            return pathList.Count > 0;
        }
        
        //获取最近几天内的文件夹列表
        public static List<DirectoryInfo> GetNewestLogFolders()
        {
            //遍历文件夹,按照文件夹创建时间进行排序
            DirectoryInfo TheFolder = new DirectoryInfo(logFilePath);
            DirectoryInfo[] dirArr = TheFolder.GetDirectories();
            List<DirectoryInfo> result = new List<DirectoryInfo>();
            if (dirArr != null)
            {
                //100天日志目录限制
                int dayLimit = 100;
                foreach (var dir in dirArr)
                {
                    Debug.Log("scan dir:" + dir.FullName);
                    result.Add(dir);
                    //只取前N天的目录
                    if (--dayLimit <= 0)
                    {
                        Debug.LogWarning("GetNewestLogFolders out of limit");
                        break;
                    }
                }
            }
            //按时间排序
            result.Sort(FileNewestOrder);
            return result;
        }

        //根据日志文件夹的创建时间进行排序，超过X天的日志会被清除掉。
        public static void SearchAndDeleteOutDateLogFile()
        {
            try
            {
                //遍历文件夹,按照文件夹创建时间进行排序
                DirectoryInfo TheFolder = new DirectoryInfo(logFilePath);
                DirectoryInfo[] dirNames = TheFolder.GetDirectories();
                if (dirNames != null && dirNames.Length > 0)
                {
                    //根据日志文件夹的创建时间进行排序，按照创建时间新的在最前面排序 
                    DateCompareFileInfo dateCompareFileInfo = new DateCompareFileInfo();
                    Array.Sort(dirNames, dateCompareFileInfo);
                    //删除超过期限的日志文件，避免过多占用用户空间
                    for (int i = 0; i < dirNames.Length; i++)
                    {
                        if (i < MAX_SAVE_LOG_FOLDER_COUNT)
                        {
                            //do nothing
                        }
                        else
                        {
                            try
                            {
                                Directory.Delete(dirNames[i].FullName, true);
                            }
                            catch (IOException)
                            {

                            }
                        }
                    }
                }
            }
            catch
            {
            }
            finally
            {
            }
        }

        public static List<DirectoryInfo> GetNewestReplayFolders()
        {
            //遍历文件夹,按照文件夹创建时间进行排序
            DirectoryInfo TheFolder = new DirectoryInfo(ReplayFilePath);
            DirectoryInfo[] dirArr = TheFolder.GetDirectories();
            List<DirectoryInfo> result = new List<DirectoryInfo>();
            if (dirArr != null)
            {
                //10个目录限制
                int countLimit = 10;
                foreach (var dir in dirArr)
                {
                    Debug.Log("scan dir:" + dir.FullName);
                    result.Add(dir);
                    //只取前10个目录
                    if (--countLimit <= 0)
                    {
                        Debug.LogWarning("GetNewestLogFolders out of limit");
                        break;
                    }
                }
            }
            //按时间排序
            result.Sort(FileNewestOrder);
            return result;

        }

        //文件或者文件夹顺序规则，新创建的文件或者文件夹排在前面
        public static int FileNewestOrder(FileSystemInfo fi1, FileSystemInfo fi2)
        {
            int result;
            if (fi1.CreationTime == fi2.CreationTime)
            {
                result = 0;
            }
            else if (fi1.CreationTime < fi2.CreationTime)
            {
                result = 1;
            }
            else
            {
                result = -1;
            }
            return result;
        }

        //上报最近x小时的日志(至少会上报一份日志)
        public static List<FileInfo> SearchLogFilePathByRangeTime(int rangeHour)
        {
            DateTime endTime = DateTime.Now;
            DateTime beginTime = endTime.AddHours(-3);
            int fileLimit = 300;//
            List<FileInfo> fileList = new List<FileInfo>();
            try
            {
                var allDirect = GetNewestLogFolders();
                //取到最近x小时的日志
                foreach (var dir in allDirect)
                {
                    foreach (var file in dir.GetFiles())
                    {
                        DateTime createTime = file.CreationTime;
                        if (createTime >= beginTime && createTime <= endTime)
                        {
                            //Debug.LogWarning("add file:" + file.FullName);
                            fileList.Add(file);
                        }
                        if (fileLimit-- < 0)
                        {
                            break;
                        }
                    }
                    if (fileLimit < 0)
                    {
                        break;
                    }
                }
                //如果最近x小时没有日志，就取最近的一份日志上报
                if (fileList.Count <= 0)
                {
                    allDirect = allDirect.GetRange(0, 1);
                    DateTime createTime = new DateTime(1970, 1, 1, 0, 0, 0, 0);
                    foreach (var dir in allDirect)
                    {
                        foreach (var file in dir.GetFiles())
                        {
                            if (file.CreationTime > createTime)
                            {
                                createTime = file.CreationTime;
                                fileList.Clear();
                                fileList.Add(file);
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                //此处不能LogError，bugly递归
                Debug.LogWarning(e);
                Debug.LogWarning(e.StackTrace);
            }
            //按时间排序
            fileList.Sort(FileNewestOrder);
            return fileList;
        }

        //获取N天内所有的日志文件
        public static List<FileInfo> SearchNewestLogFilePath(int dayCount = 1, int fileLimit = 300)
        {
            List<FileInfo> fileList = new List<FileInfo>();
            try
            {
                var allDirect = GetNewestLogFolders();
                allDirect = allDirect.GetRange(0, Math.Min(dayCount, allDirect.Count));
                foreach (var dir in allDirect)
                {
                    foreach (var file in dir.GetFiles())
                    {
                        Debug.LogWarning("add file:" + file.FullName);
                        fileList.Add(file);
                        if (fileLimit-- < 0)
                        {
                            break;
                        }
                    }
                    if (fileLimit < 0)
                    {
                        break;
                    }
                }
            }
            catch (Exception e)
            {
                //此处不能LogError，bugly递归
                Debug.LogWarning(e);
                Debug.LogWarning(e.StackTrace);
            }
            //按时间排序
            fileList.Sort(FileNewestOrder);
            return fileList;
        }

        //获取最近10个录像文件
        public static List<FileInfo> SearchNewestReplayFilePath(int folderCount = 10, int fileLimit = 300)
        {
            List<FileInfo> fileList = new List<FileInfo>();
            try
            {
                var allDirect = GetNewestReplayFolders();
                allDirect = allDirect.GetRange(0, Math.Min(folderCount, allDirect.Count));
                foreach (var dir in allDirect)
                {
                    foreach (var file in dir.GetFiles())
                    {
                        Debug.LogWarning("add file:" + file.FullName);
                        fileList.Add(file);
                        if (fileLimit-- < 0)
                        {
                            break;
                        }
                    }
                    if (fileLimit < 0)
                    {
                        break;
                    }
                }
            }
            catch (Exception e)
            {
                //此处不能LogError，bugly递归
                Debug.LogWarning(e);
                Debug.LogWarning(e.StackTrace);
            }
            //按时间排序
            fileList.Sort(FileNewestOrder);
            return fileList;
        }

        //获取最近X天的GCloud文件
        public static List<FileInfo> SearchNewestGCloudFilePath(int iDayCount = 5)
        {
            //DirectoryInfo CachebbFolder = new DirectoryInfo(Application.temporaryCachePath + "/GCloudSDKLog/GCloud");
            //FileInfo[] dirCachebbNames = CachebbFolder.GetFiles();
            //if (CachebbFolder.Exists)
            //{
            //    for (int i = 0; i < dirCachebbNames.Length; i++)
            //    {
            //        Diagnostic.Log("CachebbNames" + dirCachebbNames[i].FullName + "  size" + dirCachebbNames[i].Length / 1024 + "KB");
            //    }
            //}
            //else
            //{
            //    Diagnostic.Log(" not found CachebbNames");
            //}
            List<FileInfo> fileList = new List<FileInfo>();
            string gcloudfilepath = Application.temporaryCachePath + "/GCloudSDKLog/GCloud";
            if (!Directory.Exists(gcloudfilepath))
            {
                Diagnostic.Error("GCloud Log Path Is not Exist");
                return fileList;
            }
            DirectoryInfo folder = new DirectoryInfo(gcloudfilepath);
            FileInfo[] dirNames = folder.GetFiles();
            if (dirNames != null && dirNames.Length > 0)
            {
                DateCompareFileInfo dateCompareFileInfo = new DateCompareFileInfo();
                Array.Sort(dirNames, dateCompareFileInfo);
                DateTime nowDate = DateTime.Now;
                nowDate = nowDate.AddDays(-iDayCount);
                for (int i = 0; i < dirNames.Length; i++)
                {
                    if (dirNames[i].CreationTime >= nowDate)
                    {
                        fileList.Add(dirNames[i]);
                        Diagnostic.Log("dirNames" + dirNames[i].FullName + "  size" + dirNames[i].Length / 1024 + "KB");
                    }
                }
            }

            return fileList;
        }
#endregion

#region 输出不同步文件
        public static void WriteSynErrLog(string log)
        {
#if ACGGAME_CLIENT
            string logPath = TKApplication.StoragePath + "SynLogs/";
            string logName = DateTime.Now.ToString("yyyy_MM_dd_HH_mm_ss");
            if (!Directory.Exists(logPath))
                Directory.CreateDirectory(logPath);

            logPath = logPath + logName + ".txt";

            try
            {
                using (FileStream fs = File.Open(logPath, FileMode.Create, FileAccess.Write))
                {
                    using (StreamWriter sw = new StreamWriter(fs))
                    {
                        //开始写入
                        sw.Write(log);
                        //清空缓冲区
                        sw.Flush();
                    }
                }
            }
            catch
            {
                logPath += logName;
            }
#endif
        }
#endregion
        
        //通知业务层进行错误上报
        public static event Action<string, string> NotifyErrorReport;
        
        public static void ReportError(string error, string msg = "")
        {
            if (NotifyErrorReport != null)
                NotifyErrorReport(error, msg);
        }
        
    }
}


