using UnityEngine;
using System.Collections;

namespace TKFrame
{
	//检测全屏UI及3d场景 自动适配
	public class TKScreenUIScaler : MonoBehaviour
	{
		//UI资源原始尺寸
		private const int _referenceWidth = 1334;
		private const int _referenceHeight = 750;
        public static int ReferenceHeight
        {
            get { return _referenceHeight; }
        }

        public static bool NeedScale{
			get{
				bool  _needScale = false;
				float width = (float)Screen.width;
				float height = (float)Screen.height;

				if (width < height) 
                {
                    float tmp = width;
                    width = height;
                    height = tmp;
					Diagnostic.Error ( "ScreenUIScaler, width :{0},height:{1} error,do not scale", Screen.width,Screen.height);
				}

                if(height > 0.0f)
                {
                    float tAspect = width / height;
                    if (tAspect < (16.0f / 10.0f))
                    {
                        _needScale = true;
                        widthRatio = (tAspect * _referenceHeight) / (float)_referenceWidth;
                    }
                }
				return _needScale;
			}
		}
		public static float widthRatio{ get; set; } 

		public void Awake()
		{

			float width = (float)Screen.width;
			float height = (float)Screen.height;
			if (width < height) {
				//Diagnostic.Error ( "ScreenUIScaler, width :{0},height:{1} error,do not scale", Screen.width,Screen.height);
			}

			//NeedScale = false;
			float	tAspect =  (float)Screen.width / (float)Screen.height;		
			if (tAspect < (16.0f / 10.0f)) {
				//NeedScale = true;

				widthRatio = (tAspect * _referenceHeight) / (float)_referenceWidth;
			}
		}

        //计算当前设备的宽高比，在"iPad的宽高比-资源分辨率宽高比"的百分比，可以用于线性适配
        //注意，在设备宽高比大于资源分辨率宽高比时，这个值会算出负值，调用者需要根据情况，在负值时是否当作0.0f处理，或者取绝对值处理
        public static float getRatioPercent()
        {
            float width = (float)Screen.width;
            float height = (float)Screen.height;

            float ratioScreen = width / height;     //  假设：4/3=1.33
            float ratioIPad = 1024.0f / 768.0f;    //  4/3=1.33
            float ratioResource = 1338.0f / 640.0f; //  16/9=1.77

            float ratioDiff = ratioResource - ratioScreen;      //0.44
            float ratioDiffTotal = ratioResource - ratioIPad;  //0.44

            float ratioPercent = ratioDiff / ratioDiffTotal;    //1.0f

            //Diagnostic.Error("ScreenUIScaler ratioScreen = %f, ratioPercent = %f", ratioScreen, ratioPercent);

            return ratioPercent;
        }

        //获取屏幕的宽高比
        public static float getAspectRatio()
        {
            float aspectRatio = (float)Screen.width / (float)Screen.height;
            return aspectRatio;
        }

        public static float getAspectRatioPortrait()
        {
            float aspectRatio = (float)Screen.height / (float)Screen.width;
            return aspectRatio;
        }

        public static float getReferenceRatioPortrait()
        {
            return _referenceHeight * 1.0f / _referenceWidth;
        }

        public static float getReferenceRatio()
        {
            return _referenceWidth * 1.0f / _referenceHeight;
        }

        public static float getScale()
        {
            float w = _referenceHeight;
            float h = _referenceWidth;

            return (getAspectRatioPortrait() * w) / h;
        }

        // 检查是否为宽屏（2:1）
        public static bool CheckWideScreen()
        {
	        if (getAspectRatio() >= 2.0f)
	        {
		        return true;
	        }
	        return false;
        }
    }

}

