using UnityEngine;
using System.Collections;

public class Billboard : MonoBehaviour
{
    public Camera cameraNew;
    Quaternion direction = new Quaternion();
    // Use this for initialization
    void Start()
    {
        direction.x = transform.localRotation.x;
        direction.y = transform.localRotation.y;
        direction.z = transform.localRotation.z;
        direction.w = transform.localRotation.w;
    }
    // Update is called once per frame
    void Update()
    {
        Camera cam = null;
        if (cameraNew != null)
        {
            cam = cameraNew;
        }
        else
        {
            cam = Camera.main;
            if (!cam)
                return;
        }
        transform.rotation = cam.transform.rotation * direction;
    }
}