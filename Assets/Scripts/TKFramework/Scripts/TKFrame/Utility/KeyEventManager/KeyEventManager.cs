using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System;
using TKFrame.Item;
using TKFrame;
using ZGame;

namespace ZGameChess
{
    public class KeyEventManager : MonoBehaviour
    {
        private string IDFlag_exitDialog = "ExitDialog@Application";
        private Dialog m_exitDialog;
        private float m_lasttime;
        private float m_deltatime = 1f;
        private int m_keyClickCount = 0;
        private bool m_keyDownState = false;

        public Action InBattleDoubleClickAction;

        private static KeyEventManager m_instance;
        public static KeyEventManager Instance
        {
            get
            {
                return m_instance;
            }
        }
        
        private void Awake()
        {
            m_instance = this;
            m_lasttime = Time.realtimeSinceStartup;
        }

        private void Update()
        {
#if !UNITY_IOS
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                if (m_keyClickCount == 0)
                {
                    m_keyClickCount = 1;
                    SingleClick();
                    m_lasttime = Time.realtimeSinceStartup;
                }
                else
                {
                    m_keyClickCount++;
                    if (m_keyClickCount >= 2)
                    {
                        m_keyClickCount = 0;
                        DoubleClick();
                    }
                }
            }

            if (Time.realtimeSinceStartup - m_lasttime > m_deltatime)
            {
                m_keyClickCount = 0;
            }
#endif
        }

        private bool CheckTransform(Transform trans, string tag)
        {
            bool isFind = false;
            for (int i = trans.childCount - 1; i >= 0; i--)
            {
                Transform popTrans = trans.GetChild(i);
                if (popTrans.childCount <= 0)//空的
                    continue;
                if (popTrans.gameObject.name.Contains("Pop_Pandora") && TKFrameworkDelegateInterface.IsHasPandoraPopLayout())//潘多拉页面
                {
                    isFind = true;
                    Diagnostic.Error("KeyEventManager BackEvtNotify " + tag + " " + popTrans.gameObject.name);
                    break;
                }
                Transform popSubTrans = null;
                for (int j = 0; j < popTrans.childCount; j++)
                {
                    popSubTrans = popTrans.GetChild(j);
                    if (popSubTrans.gameObject.name.Contains("Pop_AlphaBg"))//背景Alpha的
                        continue;
                }
                if (popSubTrans == null)//异常了
                    return true;
                if (!popTrans.gameObject.activeInHierarchy || !popSubTrans.gameObject.activeInHierarchy)//隐藏了
                    continue;
                UIPanel uiPanel = popTrans.GetComponent<UIPanel>();//非UIPanel
                if (uiPanel == null)
                    continue;
                if (!uiPanel.IsNeedCheckAndroidBackEvt)//该UIPanel不需要响应返回事件
                    continue;
                Action action = uiPanel.AndroidBackEvtNotify;
                isFind = true;
                Diagnostic.Error("KeyEventManager BackEvtNotify " + tag + " " + popTrans.gameObject.name);
                if (action != null)
                    action();
                break;
            }
            return isFind;
        }

        public void SingleClick()
        {
            Diagnostic.Log("Escape Single Click");
            if (SystemManager.getInstance() == null)
                return;
            BaseStage baseStage = SystemManager.getInstance().GetStage() as BaseStage;
            if (baseStage == null || baseStage.PopList == null || baseStage.UIList == null)
                return;
            //如果当前Stage上有UIPanel或者Poplayout在加载中 不响应返回按键
            if (baseStage.IsStageHasUIResLoading())
                return;
            //找到当前最上层的UIPanel
            bool isFind = false;
            if (TKFrameworkDelegateInterface.QQGameSystem_Ins_Null())
                return;
            TKPlugins.GameContext gameContext = TKFrameworkDelegateInterface.QQGameSystem_GetGameContext();
            if (gameContext == null || gameContext.PopList == null || gameContext.UIList == null)
                return;
            //1、找系统层的PopList
            isFind = CheckTransform(gameContext.PopList, "SysPop");
            if (isFind)
                return;
            //2、找系统层的UIList
            isFind = CheckTransform(gameContext.UIList, "SysUIList");
            if (isFind)
                return;
            //3、找当前Stage的PopList
            isFind = CheckTransform(baseStage.PopList, "StagePop");
            if (isFind)
                return;
            //4、找当前Stage的UIList
            isFind = CheckTransform(baseStage.UIList, "StageUIList");
            if (isFind)
                return;
        }

        public void DoubleClick()
        {
            Diagnostic.Log("Escape Double Click");
            if (TKFrameworkDelegateInterface.CurrentStageIsChessBattleStage != null && TKFrameworkDelegateInterface.CurrentStageIsChessBattleStage())
            {
                if (InBattleDoubleClickAction != null)
                    InBattleDoubleClickAction();
            }
            else
            {
                if (m_exitDialog == null || m_exitDialog.IsDisposed)
                    LastBack();
                else
                    CloseExitDialog();
            }
        }

        private void OnDestroy()
        {
            m_instance = null;
        }

        private void LastBack()
        {
            PopLayout lastExitDialog = SystemManager.getInstance().PopManager.FindFirstPopLayoutByID(IDFlag_exitDialog);
            if (lastExitDialog == null)
            {
                m_exitDialog = DefaultDialogUtils.ShowTxtDialog(TKFrameworkDelegateInterface.Localization_Trans("您确认退出游戏吗？"), 
                    true, () => { UnityEngine.Application.Quit(); } , null, true, true, false);
                m_exitDialog.id = IDFlag_exitDialog;
                m_exitDialog.uiPanel.AndroidBackEvtNotify = CloseExitDialog;
            }
        }

        private void CloseExitDialog()
        {
            if (m_exitDialog != null)
            {
                m_exitDialog.dispose();
                m_exitDialog = null;
            }
        }

        //public void AddBackFunc(Func<bool> back)
        //{
        //    return;
        //}

        //public void RemoveBackFunc(Func<bool> back)
        //{
        //    return;
        //}
    }
}


