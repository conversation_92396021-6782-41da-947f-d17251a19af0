using System;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace LightProfiler
{
    public class ProfilerChosenPanel
    {
        private ProfilerType m_profilerType = ProfilerType.None;

        public ProfilerType ProfilerType
        {
            get { return m_profilerType; }
        }
        
        private Dictionary<ProfilerType, IProfilerGraphInterface> m_allProfilerGraph = new Dictionary<ProfilerType, IProfilerGraphInterface>();

        private IProfilerGraphInterface m_currentGraph = null;

        private string[] m_allTypeName;
        private ProfilerType[] m_allType;
        private int m_selectedType = 0;

        private Color m_backgroundColor = new Color(0.2f, 0.2f, 0.2f);

        public void AddProfilerGraph(IProfilerGraphInterface profilerGraph)
        {
            m_allProfilerGraph.Add(profilerGraph.GetProfilerType(), profilerGraph);
            if (m_profilerType == ProfilerType.None)
                m_profilerType = profilerGraph.GetProfilerType();
        }

        public void OnGUI(Rect rect)
        {
            EditorGUI.DrawRect(rect, m_backgroundColor);
            
            Rect chosenRect = rect;
            chosenRect.height = ProfilerViewConfig.TopBarHeight;
            DrawChosen(chosenRect);

            Rect detailRect = rect;
            detailRect.yMin = chosenRect.yMax;
            detailRect.height = rect.height - chosenRect.height;
            DrawChosenDetail(detailRect);
        }

        private void DrawChosen(Rect rect)
        {
            if (m_allTypeName == null)
            {
                m_allType = new ProfilerType[m_allProfilerGraph.Count];
                m_allTypeName = new string[m_allProfilerGraph.Count];
                int i = 0;
                foreach (IProfilerGraphInterface pair in m_allProfilerGraph.Values)
                {
                    m_allType[i] = pair.GetProfilerType();
                    m_allTypeName[i]= pair.GetName();
                    i++;
                }
            }
            
            EditorGUI.BeginChangeCheck();
            m_selectedType = GUI.Toolbar(rect, m_selectedType, m_allTypeName);
            if (EditorGUI.EndChangeCheck())
            {
                m_profilerType = m_allType[m_selectedType];
            }
        }

        private void DrawChosenDetail(Rect rect)
        {
            if (m_currentGraph == null || m_profilerType != m_currentGraph.GetProfilerType())
            {
                m_allProfilerGraph.TryGetValue(m_profilerType, out m_currentGraph);
            }
            
            if (m_currentGraph != null)
                m_currentGraph.DrawChosen(rect);
            
        }

        public void ClearDraw()
        {
            m_allTypeName = null;
            m_allType = null;
            m_currentGraph = null;
        }
    }
}


