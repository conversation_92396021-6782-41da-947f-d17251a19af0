using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text;
using UnityEditor;
using UnityEngine;

namespace LightProfiler
{
    public class ClassMsg
    {
        public ulong pointer = 0;
        public int count = 0;
        public ulong size = 0;
        public ulong threadId = 0;
        public string className = string.Empty;
        
        public MonoCallStackTree callStackTree = new MonoCallStackTree();
    }
    
    public class ThreadClassMsg
    {
        public int count = 0;
        public ulong size = 0;
        public ulong threadId = 0;
        public Dictionary<ulong, ClassMsg> classMsgs = new Dictionary<ulong, ClassMsg>();

        public void Clear()
        {
            count = 0;
            size = 0;
            threadId = 0;
            classMsgs.Clear();
        }
    }
    
    public class ClassDataModel : IModelInterface
    {
        private Dictionary<ulong, string> m_classNames = new Dictionary<ulong, string>();

        public int classCount
        {
            get { return m_classNames.Count; }
        }

        #region 全局信息
        private Dictionary<ulong, ThreadClassMsg> m_threadClassMsgs = new Dictionary<ulong, ThreadClassMsg>();

        public Dictionary<ulong, ThreadClassMsg> threadClassMsgs
        {
            get { return m_threadClassMsgs; }
        }
        
        private ThreadClassMsg m_totalClassMsg = new ThreadClassMsg();
        public ThreadClassMsg totalClassMsg
        {
            get { return m_totalClassMsg; }
        }
        #endregion
        

        public string GetClassName(ulong pointer)
        {
            string className = string.Empty;
            m_classNames.TryGetValue(pointer, out className);
            return className;
        }
        
        #region 加载数据
        public bool LoadData(string path)
        {
            using (var stream = File.OpenRead(path))
            {
                using (var br = new BinaryReader(stream))
                {
                    int pointerSize = br.ReadInt32();

                    while (br.BaseStream.Position < br.BaseStream.Length)
                    {
                        ulong pointer = DecodePointer(br, pointerSize);
                        string className = DecodeString(br, pointerSize);
                        
                        m_classNames.Add(pointer, className);
                    }
                }
            }
            
            return true;
        }

        private string DecodeString(BinaryReader br, int pointerSize)
        {
            ulong size = 0;
            if (pointerSize == 4)
            {
                size = br.ReadUInt32();
            }
            else if(pointerSize == 8)
            {
                size = br.ReadUInt64();
            }
            
            byte[] buffer = br.ReadBytes((int)size);
            
            return Encoding.UTF8.GetString(buffer, 0, (int)size);
        }

        private ulong DecodePointer(BinaryReader br, int pointerSize)
        {
            ulong pointer = 0;
            if (pointerSize == 4)
            {
                pointer = br.ReadUInt32();
            }
            else if(pointerSize == 8)
            {
                pointer = br.ReadUInt64();
            }

            return pointer;
        }

        public void AddMonoMsg(ulong threadId, ulong pointer, ulong size, List<int> callMethodId)
        {
            ThreadClassMsg threadClassMsg = getThreadClassMsg(threadId);
            threadClassMsg.count++;
            threadClassMsg.size += size;

            ClassMsg classMsg = getClassMsg(threadClassMsg, pointer);
            classMsg.count++;
            classMsg.size += size;
            classMsg.callStackTree.AddCallNode(callMethodId, size);

            m_totalClassMsg.count++;
            m_totalClassMsg.size += size;
            
            classMsg = getClassMsg(m_totalClassMsg, pointer);
            classMsg.count++;
            classMsg.size += size;
            classMsg.callStackTree.AddCallNode(callMethodId, size);
        }

        private ThreadClassMsg getThreadClassMsg(ulong threadId)
        {
            ThreadClassMsg result = null;
            if (!m_threadClassMsgs.TryGetValue(threadId, out result))
            {
                result = new ThreadClassMsg();
                result.threadId = threadId;
                m_threadClassMsgs.Add(threadId, result);
            }

            return result;
        }

        private ClassMsg getClassMsg(ThreadClassMsg threadClassMsg, ulong pointer)
        {
            Dictionary<ulong, ClassMsg> classMsgs = threadClassMsg.classMsgs;

            ClassMsg result = null;
            if (!classMsgs.TryGetValue(pointer, out result))
            {
                result = new ClassMsg();
                result.threadId = threadClassMsg.threadId;
                result.className = GetClassName(pointer);
                result.pointer = pointer;
                classMsgs.Add(pointer, result);
            }

            return result;
        }
        #endregion

        public void Clear()
        {
            foreach (ThreadClassMsg threadClassMsg in m_threadClassMsgs.Values)
            {
                threadClassMsg.Clear();
            }
            
            m_classNames.Clear();
            m_threadClassMsgs.Clear();

            m_totalClassMsg.Clear();
        }
    }
}


