using System;
using System.Collections.Generic;
using System.Threading;
using TKFrame;
using UnityEngine;

namespace LightProfiler
{
    public class MultiThreadLoadMethodData
    {
        private int m_threadCount;
        private QueueRingBuffer m_cacheBuffer;
        private SingleThreadLoadData m_singleThreadLoadData;
        private CountdownEvent m_countDownEvent;
        private List<DecodeDataThread> m_allDecodeThread;
        
        private int m_bufferSize;

        private static bool m_isLoadFinish = false;
        
        public static AtomicLock locker = new AtomicLock();

        public static bool isLoadFinish
        {
            get
            {
                using (new Locker(locker))
                {
                    return m_isLoadFinish;
                }
            }
            set
            {
                using (new Locker(locker))
                {
                    m_isLoadFinish = value;
                }
            }
        }
        
        public MultiThreadLoadMethodData()
        {
            m_threadCount = Environment.ProcessorCount;
            if (m_threadCount > 8)
                m_threadCount = 8;

            m_bufferSize = m_threadCount * DecodeDataThread.cacheSize;
            
            m_cacheBuffer = new QueueRingBuffer(m_bufferSize);
            m_singleThreadLoadData = new SingleThreadLoadData(m_cacheBuffer);
            m_countDownEvent = new CountdownEvent(m_threadCount);
            m_allDecodeThread = new List<DecodeDataThread>();

            for (int i = 0; i < m_threadCount; i++)
            {
                DecodeDataThread decodeDataThread = new DecodeDataThread(m_cacheBuffer, m_countDownEvent);
                m_allDecodeThread.Add(decodeDataThread);
            }
        }
        
        public void Decode(string path, Action<float, int> progressCallback)
        {
            Clear();
            foreach (DecodeDataThread decodeDataThread in m_allDecodeThread)
            {
                decodeDataThread.Run();
                Thread.Sleep(10);
            }
            
            m_singleThreadLoadData.LoadData(path, progressCallback);
            isLoadFinish = true;
            
            m_countDownEvent.Wait();

            CombineAllResult();
            
            MethodCallNodePool.Instance.Clear();
            
            System.GC.Collect();
        }

        private void CombineAllResult()
        {
            MethodProfilerModel methodProfilerModel = ProfilerModel.Instance.GetMethodProfilerModel();
            int count = 0;
            uint minFrameCount = uint.MaxValue;
            ProfilerFrameStatus allStatus = methodProfilerModel.totalFrames;
            
            foreach (DecodeDataThread decodeDataThread in m_allDecodeThread)
            {
                count += decodeDataThread.Count;
                if (minFrameCount > decodeDataThread.minFrameCount)
                {
                    minFrameCount = decodeDataThread.minFrameCount;
                }

                allStatus.frameTime += decodeDataThread.totalFrames.frameTime;
                allStatus.methodCallStackTree.AddCallStackTree(decodeDataThread.totalFrames.methodCallStackTree);
            }

            methodProfilerModel.totalFrames.methodCallStackTree.ClipTree(0.5);

            allStatus.frameCount = (uint)count;
            
            CombineList(count, minFrameCount);
        }

        private void CombineList(int totalCount, uint minFrameCount)
        {
            MethodProfilerModel methodProfilerModel = ProfilerModel.Instance.GetMethodProfilerModel();
            List<ProfilerFrameStatus> allFrames = methodProfilerModel.allFrames;
            allFrames.Capacity = totalCount;
            allFrames.Clear();
            for (int i = 0; i < totalCount; i++)
            {
                allFrames.Add(null);
            }
            
            foreach (DecodeDataThread decodeDataThread in m_allDecodeThread)
            {
                foreach (ProfilerFrameStatus profilerFrameStatus in decodeDataThread.allFrames)
                {
                    int index = (int)(profilerFrameStatus.frameCount - minFrameCount);
                    if (index > -1 && index < totalCount)
                    {
                        allFrames[index] = profilerFrameStatus;
                    }
                }
            }
        }

        private void Clear()
        {
            m_countDownEvent.Reset();
            m_cacheBuffer.Reset();
            m_singleThreadLoadData.Clear();
            int i = 0;
            foreach (DecodeDataThread decodeDataThread in m_allDecodeThread)
            {
                decodeDataThread.Clear();
            }

            isLoadFinish = false;
        }
    }
}


