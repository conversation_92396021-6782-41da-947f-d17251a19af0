using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEngine;

namespace LightProfiler
{
    public static class BinaryReaderExtend
    {
        public static double ReadMethodTime(this BinaryReader br)
        {
            ulong timeData = br.ReadUInt64();
            return timeData / 1000.0;
        }

        public static string ReadMethodName(this BinaryReader br, int len)
        {
            byte[] buffer = br.ReadBytes(len);
            
            return Encoding.UTF8.GetString(buffer, 0, len);
        }
    }
}


