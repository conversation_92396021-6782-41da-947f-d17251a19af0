using UnityEngine;
using System.Diagnostics;
using Debug = UnityEngine.Debug;

namespace ManagedHeapProfiler
{
    public static class Marker
    {
        public enum Range
        {
            None = 0,
            Loading = 1,
            RoundSelect = 2,
            NewTurn = 3,
            Battle = 4,
        }
        
        private static Range _range = Range.None;
        
        // Native implementation, DON'T change method signature
        private static void SetCurrentRange(int range)
        {
        }
            
        public static void BeginRange(Range range)
        {
            if (range != Range.None && _range == Range.None)
            {
                 _range = range;
                SetCurrentRange((int)_range);
            }
            else
            {
#if UNITY_EDITOR
                Debug.LogAssertion($"BeginRange {range} Fail, Current Range: {_range}");
#endif
            }
        }

        public static void EndRange(Range range)
        {
            if (range != Range.None && _range == range)
            {
                _range = Range.None;
                SetCurrentRange((int)_range);
            }
            else
            {
#if UNITY_EDITOR
                Debug.LogAssertion($"EndRange {range} Fail, Current Range: {_range}");
#endif
            }
        }
    }
}

