using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.Serialization;
using System.Threading;
#if OPTIMIZE_COLLECTION
using IFix.Core;
#endif

namespace TKFrame
{
    #if OPTIMIZE_COLLECTION
    internal struct Entry_Val_Val<TKey, TValue>
    {
        internal int hashCode;
        internal int next;
        internal TKey key;
        internal TValue value;
    }
        
    internal struct Entry_Ref_Val<TValue>
    {
        internal int hashCode;
        internal int next;
        internal object key;
        internal TValue value;
    }
        
    internal struct Entry_Val_Ref<TKey>
    {
        internal int hashCode;
        internal int next;
        internal object value;
        internal TKey key;
    }

    internal struct Entry_Ref_Ref
    {
        internal int hashCode;
        internal int next;
        internal object key;
        internal object value;
    }
    
    [DebuggerTypeProxy(typeof(TKDebugDisplay_Dictionary<,>))]
    [DebuggerDisplay("Count = {Count}")]
    [Serializable]
    public class TKDictionary<TKey, TValue> : IDictionary<TKey, TValue>, IDictionary, ISerializable, IDeserializationCallback
    {
        private const String VersionName = "Version";
        private const String HashSizeName = "HashSize";  // Must save buckets.Length
        private const String KeyValuePairsName = "KeyValuePairs";
        private const String ComparerName = "Comparer";
        private const String MiniCountName = "MiniCount";
        
        internal int[] buckets;
        internal Array entries;
        internal IEqualityComparer<TKey> comparer;
        private TKDictionary_KeyCollection<TKey, TValue> keys;
        private TKDictionary_ValueCollection<TKey, TValue> values;
        private object _syncRoot;
        internal int internalCount;
        internal int version;
        internal int freeList;
        internal int freeCount;
        internal int minCount = 1;
        
        #region Init

        public TKDictionary() : this(0, null)
        {
        }

        public TKDictionary(int capacity) : this(capacity, null)
        {
        }

        public TKDictionary(IEqualityComparer<TKey> comparer) : this(0, comparer)
        {
        }

        public TKDictionary(int capacity, IEqualityComparer<TKey> comparer)
        {
            if (capacity < 0)
                throw new ArgumentOutOfRangeException(nameof(capacity), capacity, "Non-negative number required.");
            if (capacity > 0)
                this.Initialize(capacity);
            this.comparer = comparer ?? EqualityComparer<TKey>.Default;
        }

        public TKDictionary(IDictionary<TKey, TValue> dictionary) : this(dictionary, null)
        {
        }

        public TKDictionary(IDictionary<TKey, TValue> dictionary, IEqualityComparer<TKey> comparer) : 
            this(dictionary != null ? dictionary.Count : 0, comparer)
        {
            if (dictionary == null)
                throw new ArgumentNullException(nameof(dictionary));

            foreach (KeyValuePair<TKey, TValue> pair in dictionary)
            {
                this.Add(pair.Key, pair.Value);
            }
        }
        
        protected TKDictionary(SerializationInfo info, StreamingContext context) 
        {
            TKDictionary_Util.SerializationInfoTable.Add(this, info);
        }
        
        #endregion
        
        #region Property
    
        public TKDictionary_KeyCollection<TKey, TValue> Keys
        {
            get
            {
                if (keys == null)
                    keys = new TKDictionary_KeyCollection<TKey, TValue>(this);
                return keys;
            }
        }

        public TKDictionary_ValueCollection<TKey, TValue> Values
        {
            get
            {
                if (values == null)
                    values = new TKDictionary_ValueCollection<TKey, TValue>(this);
                return values;
            }
        }
    
        public TKDictionary_Enumerator<TKey, TValue> GetEnumerator()
        {
            return new TKDictionary_Enumerator<TKey, TValue>(this, 2);
        }
        
        public IEqualityComparer<TKey> Comparer {
            get {
                return comparer;                
            }               
        }
    
        #endregion
        
        #region ICollection<KeyValuePair<TKey, TValue>>

        void ICollection<KeyValuePair<TKey, TValue>>.Add(KeyValuePair<TKey, TValue> keyValuePair)
        {
            this.Add(keyValuePair.Key, keyValuePair.Value);
        }

        bool ICollection<KeyValuePair<TKey, TValue>>.Contains(KeyValuePair<TKey, TValue> keyValuePair)
        {
            int entry = this.FindEntry(keyValuePair.Key);
            if (entry >= 0)
            {
                TValue value = this.GetValue(entry);
                return EqualityComparer<TValue>.Default.Equals(value, keyValuePair.Value);
            }

            return false;
        }

        bool ICollection<KeyValuePair<TKey, TValue>>.Remove(KeyValuePair<TKey, TValue> keyValuePair)
        {
            int entry = this.FindEntry(keyValuePair.Key);
            if (entry < 0)
                return false;

            TValue value = this.GetValue(entry);
            if (!EqualityComparer<TValue>.Default.Equals(value, keyValuePair.Value))
            {
                return false;
            }

            this.Remove(keyValuePair.Key);
            return true;
        }

        void ICollection<KeyValuePair<TKey, TValue>>.CopyTo(KeyValuePair<TKey, TValue>[] array, int index)
        {
            this.CopyTo(array, index);
        }

        #endregion
        
        #region IEnumerable<KeyValuePair<TKey, TValue>>

        IEnumerator<KeyValuePair<TKey, TValue>> IEnumerable<KeyValuePair<TKey, TValue>>.GetEnumerator() => GetEnumerator();

        #endregion
        
        #region ICollection

        void ICollection.CopyTo(Array array, int index)
        {
            if (array == null)
                throw new ArgumentNullException(nameof(array));
            if (array.Rank != 1)
                throw new ArgumentException("Only single dimensional arrays are supported for the requested action.",
                    nameof(array));
            if (array.GetLowerBound(0) != 0)
                throw new ArgumentException("The lower bound of target array must be zero.", nameof(array));
            if (index < 0 || index > array.Length)
                throw new ArgumentOutOfRangeException(nameof(index), (object) index,
                    "Index was out of range. Must be non-negative and less than the size of the collection.");
            if (array.Length - index < Count)
                throw new ArgumentException(
                    "Destination array is not long enough to copy all the items in the collection. Check array index and length.");
            
            KeyValuePair<TKey, TValue>[] array1 = array as KeyValuePair<TKey, TValue>[];
            if (array1 != null)
                this.CopyTo(array1, index);
            else if (array is DictionaryEntry[])
            {
                DictionaryEntry[] dictionaryEntryArray = array as DictionaryEntry[];

                switch (TKTwoType<TKey, TValue>.GenericType)
                {
                    case 0:
                    {
                        Entry_Val_Val<TKey, TValue>[] tEntries = (Entry_Val_Val<TKey, TValue>[]) entries;
                        for (int i = 0; i < internalCount; i++)
                        {
                            if (tEntries[i].hashCode >= 0)
                            {
                                dictionaryEntryArray[index++] = new DictionaryEntry(tEntries[i].key, tEntries[i].value);
                            }
                        }
                        break;
                    }
                    case 1:
                    {
                        Entry_Ref_Val<TValue>[] tEntries = (Entry_Ref_Val<TValue>[]) entries;
                        for (int i = 0; i < internalCount; i++)
                        {
                            if (tEntries[i].hashCode >= 0)
                            {
                                dictionaryEntryArray[index++] = new DictionaryEntry(tEntries[i].key, tEntries[i].value);
                            }
                        }
                        break;
                    }
                    case 2:
                    {
                        Entry_Val_Ref<TKey>[] tEntries = (Entry_Val_Ref<TKey>[]) entries;
                        for (int i = 0; i < internalCount; i++)
                        {
                            if (tEntries[i].hashCode >= 0)
                            {
                                dictionaryEntryArray[index++] = new DictionaryEntry(tEntries[i].key, tEntries[i].value);
                            }
                        }
                        break;
                    }
                    case 3:
                    {
                        Entry_Ref_Ref[] tEntries = (Entry_Ref_Ref[]) entries;
                        for (int i = 0; i < internalCount; i++)
                        {
                            if (tEntries[i].hashCode >= 0)
                            {
                                dictionaryEntryArray[index++] = new DictionaryEntry(tEntries[i].key, tEntries[i].value);
                            }
                        }
                        break;
                    }
                }
            }
            else
            {
                object[] objArray = array as object[];
                if (objArray == null)
                    throw new ArgumentException(
                        "Target array type is not compatible with the type of items in the collection.", nameof(array));

                switch (TKTwoType<TKey, TValue>.GenericType)
                {
                    case 0:
                    {
                        Entry_Val_Val<TKey, TValue>[] tEntries = (Entry_Val_Val<TKey, TValue>[]) entries;
                        for (int i = 0; i < internalCount; i++)
                        {
                            if (tEntries[i].hashCode >= 0)
                            {
                                objArray[index++] = new KeyValuePair<TKey, TValue>(tEntries[i].key, tEntries[i].value);
                            }
                        }
                        break;
                    }
                    case 1:
                    {
                        Entry_Ref_Val<TValue>[] tEntries = (Entry_Ref_Val<TValue>[]) entries;
                        for (int i = 0; i < internalCount; i++)
                        {
                            if (tEntries[i].hashCode >= 0)
                            {
                                objArray[index++] = new KeyValuePair<TKey, TValue>((TKey) tEntries[i].key, tEntries[i].value);
                            }
                        }
                        break;
                    }
                    case 2:
                    {
                        Entry_Val_Ref<TKey>[] tEntries = (Entry_Val_Ref<TKey>[]) entries;
                        for (int i = 0; i < internalCount; i++)
                        {
                            if (tEntries[i].hashCode >= 0)
                            {
                                objArray[index++] = new KeyValuePair<TKey, TValue>(tEntries[i].key, (TValue) tEntries[i].value);
                            }
                        }
                        break;
                    }
                    case 3:
                    {
                        Entry_Ref_Ref[] tEntries = (Entry_Ref_Ref[]) entries;
                        for (int i = 0; i < internalCount; i++)
                        {
                            if (tEntries[i].hashCode >= 0)
                            {
                                objArray[index++] = new KeyValuePair<TKey, TValue>((TKey) tEntries[i].key, (TValue) tEntries[i].value);
                            }
                        }
                        break;
                    }
                }
            }
        }
        
        object ICollection.SyncRoot
        {
            get
            {
                if (_syncRoot == null)
                    Interlocked.CompareExchange<object>(ref _syncRoot, new object(), null);
                return _syncRoot;
            }
        }

        bool ICollection.IsSynchronized
        {
            get { return false; }
        }
        
        #endregion
        
        #region IEnumerable

        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();

        #endregion
        
        #region IDictionary<TKey, TValue>

        ICollection<TKey> IDictionary<TKey, TValue>.Keys => Keys;

        ICollection<TValue> IDictionary<TKey, TValue>.Values => Values;

        #endregion
        
        #region IDictionary
        
        object IDictionary.this[object key]
        {
            get
            {
                if (key is TKey)
                {
                    return this[(TKey) key];
                }

                return default(TValue);
            }
            set
            {
                if (key is TKey && value is TValue)
                {
                    this[(TKey) key] = (TValue) value;
                }
            }
        }
        
        ICollection IDictionary.Keys => Keys;

        ICollection IDictionary.Values => Values;
        
        bool IDictionary.Contains(object key)
        {
            if (key is TKey)
                return ContainsKey((TKey) key);
            return false;
        }
        
        void IDictionary.Add(object key, object value)
        {
            this.Add((TKey) key, (TValue) value);
        }
        
        bool IDictionary.IsFixedSize
        {
            get { return false; }
        }

        IDictionaryEnumerator IDictionary.GetEnumerator() => GetEnumerator();
        
        void IDictionary.Remove(object key)
        {
            this.Remove((TKey) key);
        }
        
        #endregion
        
        #region ISerialize
        
        public virtual void GetObjectData(SerializationInfo info, StreamingContext context) 
        {
            if (info==null) {
                throw new ArgumentNullException(nameof(info));
            }
            info.AddValue(VersionName, version);
 
            info.AddValue(ComparerName, comparer, typeof(IEqualityComparer<TKey>));
 
            info.AddValue(HashSizeName, buckets == null ? 0 : buckets.Length); //This is the length of the bucket array.
            
            info.AddValue(MiniCountName, minCount);
            
            if( buckets != null) {
                KeyValuePair<TKey, TValue>[] array = new KeyValuePair<TKey, TValue>[Count];
                this.CopyTo(array, 0);
                info.AddValue(KeyValuePairsName, array, typeof(KeyValuePair<TKey, TValue>[]));
            }
        }
        
        #endregion
        
        #region IDeserializationCallback
        
        public virtual void OnDeserialization(Object sender) 
        {
            SerializationInfo siInfo;
            TKDictionary_Util.SerializationInfoTable.TryGetValue(this, out siInfo);
            
            if (siInfo==null) 
                return;
            
            int realVersion = siInfo.GetInt32(VersionName);
            int hashsize = siInfo.GetInt32(HashSizeName);
            int minCount = siInfo.GetInt32(MiniCountName);
            comparer = (IEqualityComparer<TKey>)siInfo.GetValue(ComparerName, typeof(IEqualityComparer<TKey>));

            if (hashsize != 0)
            {
                buckets = new int[hashsize];
                for (int i = 0; i < buckets.Length; i++) 
                    buckets[i] = -1;
                
                switch (TKTwoType<TKey, TValue>.GenericType)
                {
                    case 0:
                        entries = new Entry_Val_Val<TKey, TValue>[hashsize];
                        break;
                    case 1:
                        entries = new Entry_Ref_Val<TValue>[hashsize];
                        break;
                    case 2:
                        entries = new Entry_Val_Ref<TKey>[hashsize];
                        break;
                    case 3:
                        entries = new Entry_Ref_Ref[hashsize];
                        break;
                }
                freeList = -1;
 
                KeyValuePair<TKey, TValue>[] keyValuePairArray = (KeyValuePair<TKey, TValue>[]) siInfo.GetValue(KeyValuePairsName, typeof(KeyValuePair<TKey, TValue>[]));
                if (keyValuePairArray == null)
                    throw new SerializationException("The keys for this dictionary are missing.");
 
                for (int index = 0; index < keyValuePairArray.Length; ++index)
                {
                    if (keyValuePairArray[index].Key == null)
                        throw new SerializationException("One of the serialized keys is null.");
                    Add(keyValuePairArray[index].Key, keyValuePairArray[index].Value);
                }
            }
            else {
                buckets = null;
            }

            this.minCount = minCount;
            version = realVersion;
            TKDictionary_Util.SerializationInfoTable.Remove(this);
        }
        
        #endregion
        
        #region CommonMethod

        public void Clear()
        {
            if (internalCount <= 0)
                return;
            
            for (int index = 0; index < buckets.Length; ++index)
                buckets[index] = -1;
            
            Array.Clear(entries, 0, internalCount);
            
            freeList = -1;
            internalCount = 0;
            freeCount = 0;
            ++version;
        }
        
        public bool ContainsKey(TKey key)
        {
            return this.FindEntry(key) >= 0;
        }
        
        public int Count
        {
            get { return internalCount - freeCount; }
        }
        
        public bool IsReadOnly
        {
            get { return true; }
        }
        
        public bool TryGetValue(TKey key, out TValue value)
        {
            int entry = this.FindEntry(key);
            if (entry >= 0)
            {
                value = this.GetValue(entry);
                return true;
            }

            value = default(TValue);
            return false;
        }
        
        public TValue this[TKey key]
        {
            get
            {
                int entry = this.FindEntry(key);
                if (entry >= 0)
                {
                    return this.GetValue(entry);
                }

                throw new KeyNotFoundException();
            }
            set
            {
                this.TryInsert(key, value, true);
            }
        }
        
        public void Add(TKey key, TValue value)
        {
            this.TryInsert(key, value, false);
        }
        
        public bool Remove(TKey key)
        {
            if (buckets == null)
                return false;
            
            int lastIndex = -1;

            switch (TKTwoType<TKey, TValue>.GenericType)
            {
                case 0:
                {
                    int num = comparer.GetHashCode(key) & 0x7ffffff;
                    int currentBucketIndex = num % buckets.Length;
                    
                    Entry_Val_Val<TKey, TValue>[] tEntries = (Entry_Val_Val<TKey, TValue>[]) entries;
                    
                    for (int currentIndex = buckets[currentBucketIndex]; currentIndex >= 0; currentIndex = tEntries[currentIndex].next)
                    {
                        if (tEntries[currentIndex].hashCode == num && comparer.Equals(tEntries[currentIndex].key, key))
                        {
                            if (lastIndex < 0)
                                buckets[currentBucketIndex] = tEntries[currentIndex].next;
                            else
                                tEntries[lastIndex].next = tEntries[currentIndex].next;
                            
                            tEntries[currentIndex].hashCode = -1;
                            tEntries[currentIndex].next = freeList;
                            tEntries[currentIndex].key = default(TKey);
                            tEntries[currentIndex].value = default(TValue);
                            freeList = currentIndex;
                            ++freeCount;
                            ++version;

                            return true;
                        }

                        lastIndex = currentIndex;
                    }
                    break;
                }
                case 1:
                {
                    if (key == null)
                        throw new ArgumentNullException(nameof(key));
                    
                    int num = comparer.GetHashCode(key) & 0x7ffffff;
                    int currentBucketIndex = num % buckets.Length;
                    
                    Entry_Ref_Val<TValue>[] tEntries = (Entry_Ref_Val<TValue>[]) entries;
                    
                    for (int currentIndex = buckets[currentBucketIndex]; currentIndex >= 0; currentIndex = tEntries[currentIndex].next)
                    {
                        if (tEntries[currentIndex].hashCode == num && comparer.Equals((TKey) tEntries[currentIndex].key, key))
                        {
                            if (lastIndex < 0)
                                buckets[currentBucketIndex] = tEntries[currentIndex].next;
                            else
                                tEntries[lastIndex].next = tEntries[currentIndex].next;
                            
                            tEntries[currentIndex].hashCode = -1;
                            tEntries[currentIndex].next = freeList;
                            tEntries[currentIndex].key = null;
                            tEntries[currentIndex].value = default(TValue);
                            freeList = currentIndex;
                            ++freeCount;
                            ++version;

                            return true;
                        }

                        lastIndex = currentIndex;
                    }
                    break;
                }
                case 2:
                {
                    int num = comparer.GetHashCode(key) & 0x7ffffff;
                    int currentBucketIndex = num % buckets.Length;
                    
                    Entry_Val_Ref<TKey>[] tEntries = (Entry_Val_Ref<TKey>[]) entries;
                    
                    for (int currentIndex = buckets[currentBucketIndex]; currentIndex >= 0; currentIndex = tEntries[currentIndex].next)
                    {
                        if (tEntries[currentIndex].hashCode == num && comparer.Equals(tEntries[currentIndex].key, key))
                        {
                            if (lastIndex < 0)
                                buckets[currentBucketIndex] = tEntries[currentIndex].next;
                            else
                                tEntries[lastIndex].next = tEntries[currentIndex].next;
                            
                            tEntries[currentIndex].hashCode = -1;
                            tEntries[currentIndex].next = freeList;
                            tEntries[currentIndex].key = default(TKey);
                            tEntries[currentIndex].value = null;
                            freeList = currentIndex;
                            ++freeCount;
                            ++version;

                            return true;
                        }

                        lastIndex = currentIndex;
                    }
                    break;
                }
                case 3:
                {
                    if (key == null)
                        throw new ArgumentNullException(nameof(key));
                    
                    int num = comparer.GetHashCode(key) & 0x7ffffff;
                    int currentBucketIndex = num % buckets.Length;
                    
                    Entry_Ref_Ref[] tEntries = (Entry_Ref_Ref[]) entries;
                    
                    for (int currentIndex = buckets[currentBucketIndex]; currentIndex >= 0; currentIndex = tEntries[currentIndex].next)
                    {
                        if (tEntries[currentIndex].hashCode == num && comparer.Equals((TKey) tEntries[currentIndex].key, key))
                        {
                            if (lastIndex < 0)
                                buckets[currentBucketIndex] = tEntries[currentIndex].next;
                            else
                                tEntries[lastIndex].next = tEntries[currentIndex].next;
                            
                            tEntries[currentIndex].hashCode = -1;
                            tEntries[currentIndex].next = freeList;
                            tEntries[currentIndex].key = default(TKey);
                            tEntries[currentIndex].value = default(TValue);
                            freeList = currentIndex;
                            ++freeCount;
                            ++version;

                            return true;
                        }

                        lastIndex = currentIndex;
                    }
                    break;
                }
            }
            
            return false;
        }
        
        #endregion
    }
    
    [Serializable]
    public struct TKDictionary_Enumerator<TKey, TValue> : IEnumerator<KeyValuePair<TKey, TValue>>, IDictionaryEnumerator
    {
        private TKDictionary<TKey, TValue> dictionary;
        private int version;
        private int index;
        private KeyValuePair<TKey, TValue> current;
        private int getEnumeratorRetType;
        
        #region Init
        
        internal TKDictionary_Enumerator(TKDictionary<TKey, TValue> dictionary, int getEnumeratorRetType)
        {
            this.dictionary = dictionary;
            version = dictionary.version;
            index = 0;
            this.getEnumeratorRetType = getEnumeratorRetType;
            current = new KeyValuePair<TKey, TValue>();
        }
        
        #endregion
        
        #region Iter

        public bool MoveNext()
        {
            if (version != dictionary.version)
                throw new InvalidOperationException("Collection was modified; enumeration operation may not execute.");

            switch (TKTwoType<TKey, TValue>.GenericType)
            {
                case 0:
                {
                    Entry_Val_Val<TKey, TValue>[] entries = (Entry_Val_Val<TKey, TValue>[]) dictionary.entries;
                    while (index < dictionary.internalCount)
                    {
                        if (entries[index].hashCode >= 0)
                        {
                            current = new KeyValuePair<TKey, TValue>(entries[index].key, entries[index].value);
                            index++;
                            return true;
                        }
                        index++;
                    }
                    break;
                }
                case 1:
                {
                    Entry_Ref_Val<TValue>[] entries = (Entry_Ref_Val<TValue>[]) dictionary.entries;
                    while (index < dictionary.internalCount)
                    {
                        if (entries[index].hashCode >= 0)
                        {
                            current = new KeyValuePair<TKey, TValue>((TKey) entries[index].key, entries[index].value);
                            index++;
                            return true;
                        }
                        index++;
                    }
                    break;
                }
                case 2:
                {
                    Entry_Val_Ref<TKey>[] entries = (Entry_Val_Ref<TKey>[]) dictionary.entries;
                    while (index < dictionary.internalCount)
                    {
                        if (entries[index].hashCode >= 0)
                        {
                            current = new KeyValuePair<TKey, TValue>(entries[index].key, (TValue) entries[index].value);
                            index++;
                            return true;
                        }
                        index++;
                    }
                    break;
                }
                case 3:
                {
                    Entry_Ref_Ref[] entries = (Entry_Ref_Ref[]) dictionary.entries;
                    while (index < dictionary.internalCount)
                    {
                        if (entries[index].hashCode >= 0)
                        {
                            current = new KeyValuePair<TKey, TValue>((TKey) entries[index].key, (TValue) entries[index].value);
                            index++;
                            return true;
                        }
                        index++;
                    }
                    break;
                }
            }
            
            index = dictionary.internalCount + 1;
            current = new KeyValuePair<TKey, TValue>();
            return false;
        }
        
        void IEnumerator.Reset()
        {
            if (version != dictionary.version)
                throw new InvalidOperationException("Collection was modified; enumeration operation may not execute.");
            index = 0;
            current = new KeyValuePair<TKey, TValue>();
        }
        
        public void Dispose()
        {
        }
        
        #endregion
        
        #region Current

        public KeyValuePair<TKey, TValue> Current
        {
            get { return current; }
        }

        object IEnumerator.Current
        {
            get
            {
                if (index == 0 || index == dictionary.internalCount + 1)
                    throw new InvalidOperationException("Enumeration has either not started or has already finished.");

                if (getEnumeratorRetType == 1)
                    return new DictionaryEntry(current.Key, current.Value);
                
                return current;
            }
        }

        #endregion
        
        #region IDictionaryEnumerator

        DictionaryEntry IDictionaryEnumerator.Entry
        {
            get
            {
                if (index == 0 || index == dictionary.internalCount + 1)
                    throw new InvalidOperationException("Enumeration has either not started or has already finished.");
                return new DictionaryEntry(current.Key, current.Value);
            }
        }

        object IDictionaryEnumerator.Key
        {
            get
            {
                if (index == 0 || index == dictionary.internalCount + 1)
                    throw new InvalidOperationException("Enumeration has either not started or has already finished.");
                return current.Key;
            }
        }

        object IDictionaryEnumerator.Value
        {
            get
            {
                if (index == 0 || index == dictionary.internalCount + 1)
                    throw new InvalidOperationException("Enumeration has either not started or has already finished.");
                return current.Value;
            }
        }

        #endregion
    }
    
    [Serializable]
    public sealed class TKDictionary_KeyCollection<TKey, TValue> : ICollection<TKey>, ICollection, IReadOnlyCollection<TKey>
    {
        private TKDictionary<TKey, TValue> dictionary;
        
        #region Init
        
        public TKDictionary_KeyCollection(TKDictionary<TKey, TValue> dictionary)
        {
            if (dictionary == null)
                throw new ArgumentNullException(nameof(dictionary));
            this.dictionary = dictionary;
        }
        
        #endregion
        
        #region Property

        public int Count
        {
            get { return dictionary.Count; }
        }

        bool ICollection<TKey>.IsReadOnly
        {
            get { return true; }
        }

        bool ICollection.IsSynchronized
        {
            get { return false; }
        }

        object ICollection.SyncRoot
        {
            get { return ((ICollection) dictionary).SyncRoot; }
        }

        #endregion
        
        #region Get Enumerator

        public Enumerator GetEnumerator()
        {
            return new Enumerator(dictionary);
        }

        IEnumerator<TKey> IEnumerable<TKey>.GetEnumerator() => GetEnumerator();

        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();

        #endregion
        
        #region Not Implement

        public void Add(TKey item)
        {
            throw new NotSupportedException("Mutating a key collection derived from a ConfigHashMap is not allowed.");
        }

        public void Clear()
        {
            throw new NotSupportedException("Mutating a key collection derived from a ConfigHashMap is not allowed.");
        }

        public bool Remove(TKey item)
        {
            throw new NotSupportedException("Mutating a key collection derived from a ConfigHashMap is not allowed.");
        }

        #endregion
        
        #region Implement
        
        public bool Contains(TKey item)
        {
            return dictionary.ContainsKey(item);
        }

        public void CopyTo(TKey[] array, int index)
        {
            if (array == null)
                throw new ArgumentNullException(nameof(array));
            if (index < 0 || index > array.Length)
                throw new ArgumentOutOfRangeException(nameof(index), (object) index,
                    "Index was out of range. Must be non-negative and less than the size of the collection.");
            if (array.Length - index < dictionary.Count)
                throw new ArgumentException(
                    "Destination array is not long enough to copy all the items in the collection. Check array index and length.");

            int count = dictionary.internalCount;

            switch (TKTwoType<TKey, TValue>.GenericType)
            {
                case 0:
                {
                    Entry_Val_Val<TKey, TValue>[] entries = (Entry_Val_Val<TKey, TValue>[]) dictionary.entries;
                    for (int i = 0; i < count; i++)
                    {
                        if (entries[i].hashCode >= 0)
                        {
                            array[index++] = entries[i].key;
                        }
                    }
                    break;
                }
                case 1:
                {
                    Entry_Ref_Val<TValue>[] entries = (Entry_Ref_Val<TValue>[]) dictionary.entries;
                    for (int i = 0; i < count; i++)
                    {
                        if (entries[i].hashCode >= 0)
                        {
                            array[index++] = (TKey) entries[i].key;
                        }
                    }
                    break;
                }
                case 2:
                {
                    Entry_Val_Ref<TKey>[] entries = (Entry_Val_Ref<TKey>[]) dictionary.entries;
                    for (int i = 0; i < count; i++)
                    {
                        if (entries[i].hashCode >= 0)
                        {
                            array[index++] = entries[i].key;
                        }
                    }
                    break;
                }
                case 3:
                {
                    Entry_Ref_Ref[] entries = (Entry_Ref_Ref[]) dictionary.entries;
                    for (int i = 0; i < count; i++)
                    {
                        if (entries[i].hashCode >= 0)
                        {
                            array[index++] = (TKey) entries[i].key;
                        }
                    }
                    break;
                }
            }
        }

        void ICollection.CopyTo(Array array, int index)
        {
            if (array == null)
                throw new ArgumentNullException(nameof(array));
            if (array.Rank != 1)
                throw new ArgumentException("Only single dimensional arrays are supported for the requested action.",
                    nameof(array));
            if (array.GetLowerBound(0) != 0)
                throw new ArgumentException("The lower bound of target array must be zero.", nameof(array));
            if (index < 0 || index > array.Length)
                throw new ArgumentOutOfRangeException(nameof(index), index,
                    "Index was out of range. Must be non-negative and less than the size of the collection.");
            if (array.Length - index < dictionary.Count)
                throw new ArgumentException(
                    "Destination array is not long enough to copy all the items in the collection. Check array index and length.");
            
            TKey[] array1 = array as TKey[];
            if (array1 != null)
            {
                CopyTo(array1, index);
            }
            else
            {
                object[] objArray = array as object[];
                if (objArray == null)
                    throw new ArgumentException(
                        "Target array type is not compatible with the type of items in the collection.", nameof(array));
                
                int count = dictionary.internalCount;
                
                try
                {
                    switch (TKTwoType<TKey, TValue>.GenericType)
                    {
                        case 0:
                        {
                            Entry_Val_Val<TKey, TValue>[] entries = (Entry_Val_Val<TKey, TValue>[]) dictionary.entries;
                            for (int i = 0; i < count; i++)
                            {
                                if (entries[i].hashCode >= 0)
                                {
                                    objArray[index++] = entries[i].key;
                                }
                            }
                            break;
                        }
                        case 1:
                        {
                            Entry_Ref_Val<TValue>[] entries = (Entry_Ref_Val<TValue>[]) dictionary.entries;
                            for (int i = 0; i < count; i++)
                            {
                                if (entries[i].hashCode >= 0)
                                {
                                    objArray[index++] = entries[i].key;
                                }
                            }
                            break;
                        }
                        case 2:
                        {
                            Entry_Val_Ref<TKey>[] entries = (Entry_Val_Ref<TKey>[]) dictionary.entries;
                            for (int i = 0; i < count; i++)
                            {
                                if (entries[i].hashCode >= 0)
                                {
                                    objArray[index++] = entries[i].key;
                                }
                            }
                            break;
                        }
                        case 3:
                        {
                            Entry_Ref_Ref[] entries = (Entry_Ref_Ref[]) dictionary.entries;
                            for (int i = 0; i < count; i++)
                            {
                                if (entries[i].hashCode >= 0)
                                {
                                    objArray[index++] = entries[i].key;
                                }
                            }
                            break;
                        }
                    }
                }
                catch (ArrayTypeMismatchException)
                {
                    throw new ArgumentException(
                        "Target array type is not compatible with the type of items in the collection.", nameof(array));
                }
            }
        }
        
        #endregion
        
        public struct Enumerator : IEnumerator<TKey>
        {
            private TKDictionary<TKey, TValue> dictionary;
            private int index;
            private int version;
            private TKey currentKey;

            #region Init

            public Enumerator(TKDictionary<TKey, TValue> dictionary)
            {
                this.dictionary = dictionary;
                version = dictionary.version;
                index = 0;
                currentKey = default(TKey);
            }

            #endregion

            #region Iter

            public bool MoveNext()
            {
                if (version != dictionary.version)
                    throw new InvalidOperationException("Collection was modified; enumeration operation may not execute.");

                switch (TKTwoType<TKey, TValue>.GenericType)
                {
                    case 0:
                    {
                        Entry_Val_Val<TKey, TValue>[] entries = (Entry_Val_Val<TKey, TValue>[]) dictionary.entries;
                        while (index < dictionary.internalCount)
                        {
                            if (entries[index].hashCode >= 0)
                            {
                                currentKey = entries[index].key;
                                index++;
                                return true;
                            }
                            index++;
                        }
                        break;
                    }
                    case 1:
                    {
                        Entry_Ref_Val<TValue>[] entries = (Entry_Ref_Val<TValue>[]) dictionary.entries;
                        while (index < dictionary.internalCount)
                        {
                            if (entries[index].hashCode >= 0)
                            {
                                currentKey = (TKey) entries[index].key;
                                index++;
                                return true;
                            }
                            index++;
                        }
                        break;
                    }
                    case 2:
                    {
                        Entry_Val_Ref<TKey>[] entries = (Entry_Val_Ref<TKey>[]) dictionary.entries;
                        while (index < dictionary.internalCount)
                        {
                            if (entries[index].hashCode >= 0)
                            {
                                currentKey = entries[index].key;
                                index++;
                                return true;
                            }
                            index++;
                        }
                        break;
                    }
                    case 3:
                    {
                        Entry_Ref_Ref[] entries = (Entry_Ref_Ref[]) dictionary.entries;
                        while (index < dictionary.internalCount)
                        {
                            if (entries[index].hashCode >= 0)
                            {
                                currentKey = (TKey) entries[index].key;
                                index++;
                                return true;
                            }
                            index++;
                        }
                        break;
                    }
                }

                index = dictionary.internalCount + 1;
                currentKey = default(TKey);
                return false;
            }

            void IEnumerator.Reset()
            {
                if (version != dictionary.version)
                    throw new InvalidOperationException("Collection was modified; enumeration operation may not execute.");
                index = 0;
                currentKey = default(TKey);
            }

            public void Dispose()
            {
            }

            #endregion

            #region Current

            public TKey Current
            {
                get { return currentKey; }
            }

            object IEnumerator.Current
            {
                get
                {
                    if (index == 0 || index == dictionary.Count + 1)
                        throw new InvalidOperationException("Enumeration has either not started or has already finished.");
                    return currentKey;
                }
            }

            #endregion
        }
    }
    
    [Serializable]
    public sealed class TKDictionary_ValueCollection<TKey, TValue> : ICollection<TValue>, ICollection, IReadOnlyCollection<TValue>
    {
        private TKDictionary<TKey, TValue> dictionary;
        
        #region Init
        
        public TKDictionary_ValueCollection(TKDictionary<TKey, TValue> dictionary)
        {
            if (dictionary == null)
                throw new ArgumentNullException(nameof(dictionary));
            this.dictionary = dictionary;
        }
        
        #endregion
        
        #region Property

        public int Count
        {
            get { return dictionary.Count; }
        }

        bool ICollection<TValue>.IsReadOnly
        {
            get { return true; }
        }

        bool ICollection.IsSynchronized
        {
            get { return false; }
        }

        object ICollection.SyncRoot
        {
            get { return ((ICollection) dictionary).SyncRoot; }
        }

        #endregion
        
        #region Get Enumerator

        public Enumerator GetEnumerator()
        {
            return new Enumerator(dictionary);
        }

        IEnumerator<TValue> IEnumerable<TValue>.GetEnumerator() => GetEnumerator();

        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();

        #endregion
        
        #region Not Implement

        public void Add(TValue item)
        {
            throw new NotSupportedException("Mutating a value collection derived from a ConfigHashMap is not allowed.");
        }

        public void Clear()
        {
            throw new NotSupportedException("Mutating a value collection derived from a ConfigHashMap is not allowed.");
        }

        public bool Remove(TValue item)
        {
            throw new NotSupportedException("Mutating a value collection derived from a ConfigHashMap is not allowed.");
        }

        #endregion
        
        #region Implement
        
        public bool Contains(TValue item)
        {
            return dictionary.ContainsValue(item);
        }

        public void CopyTo(TValue[] array, int index)
        {
            if (array == null)
                throw new ArgumentNullException(nameof(array));
            if (index < 0 || index > array.Length)
                throw new ArgumentOutOfRangeException(nameof(index), (object) index,
                    "Index was out of range. Must be non-negative and less than the size of the collection.");
            if (array.Length - index < dictionary.Count)
                throw new ArgumentException(
                    "Destination array is not long enough to copy all the items in the collection. Check array index and length.");

            int count = dictionary.internalCount;

            switch (TKTwoType<TKey, TValue>.GenericType)
            {
                case 0:
                {
                    Entry_Val_Val<TKey, TValue>[] entries = (Entry_Val_Val<TKey, TValue>[]) dictionary.entries;
                    for (int i = 0; i < count; i++)
                    {
                        if (entries[i].hashCode >= 0)
                        {
                            array[index++] = entries[i].value;
                        }
                    }
                    break;
                }
                case 1:
                {
                    Entry_Ref_Val<TValue>[] entries = (Entry_Ref_Val<TValue>[]) dictionary.entries;
                    for (int i = 0; i < count; i++)
                    {
                        if (entries[i].hashCode >= 0)
                        {
                            array[index++] = entries[i].value;
                        }
                    }
                    break;
                }
                case 2:
                {
                    Entry_Val_Ref<TKey>[] entries = (Entry_Val_Ref<TKey>[]) dictionary.entries;
                    for (int i = 0; i < count; i++)
                    {
                        if (entries[i].hashCode >= 0)
                        {
                            array[index++] = (TValue) entries[i].value;
                        }
                    }
                    break;
                }
                case 3:
                {
                    Entry_Ref_Ref[] entries = (Entry_Ref_Ref[]) dictionary.entries;
                    for (int i = 0; i < count; i++)
                    {
                        if (entries[i].hashCode >= 0)
                        {
                            array[index++] = (TValue) entries[i].value;
                        }
                    }
                    break;
                }
            }
        }

        void ICollection.CopyTo(Array array, int index)
        {
            if (array == null)
                throw new ArgumentNullException(nameof(array));
            if (array.Rank != 1)
                throw new ArgumentException("Only single dimensional arrays are supported for the requested action.",
                    nameof(array));
            if (array.GetLowerBound(0) != 0)
                throw new ArgumentException("The lower bound of target array must be zero.", nameof(array));
            if (index < 0 || index > array.Length)
                throw new ArgumentOutOfRangeException(nameof(index), index,
                    "Index was out of range. Must be non-negative and less than the size of the collection.");
            if (array.Length - index < dictionary.Count)
                throw new ArgumentException(
                    "Destination array is not long enough to copy all the items in the collection. Check array index and length.");
            
            TValue[] array1 = array as TValue[];
            if (array1 != null)
            {
                CopyTo(array1, index);
            }
            else
            {
                object[] objArray = array as object[];
                if (objArray == null)
                    throw new ArgumentException(
                        "Target array type is not compatible with the type of items in the collection.", nameof(array));
                
                int count = dictionary.internalCount;
                try
                {
                    switch (TKTwoType<TKey, TValue>.GenericType)
                    {
                        case 0:
                        {
                            Entry_Val_Val<TKey, TValue>[] entries = (Entry_Val_Val<TKey, TValue>[]) dictionary.entries;
                            for (int i = 0; i < count; i++)
                            {
                                if (entries[i].hashCode >= 0)
                                {
                                    objArray[index++] = entries[i].value;
                                }
                            }
                            break;
                        }
                        case 1:
                        {
                            Entry_Ref_Val<TValue>[] entries = (Entry_Ref_Val<TValue>[]) dictionary.entries;
                            for (int i = 0; i < count; i++)
                            {
                                if (entries[i].hashCode >= 0)
                                {
                                    objArray[index++] = entries[i].value;
                                }
                            }
                            break;
                        }
                        case 2:
                        {
                            Entry_Val_Ref<TKey>[] entries = (Entry_Val_Ref<TKey>[]) dictionary.entries;
                            for (int i = 0; i < count; i++)
                            {
                                if (entries[i].hashCode >= 0)
                                {
                                    objArray[index++] = entries[i].value;
                                }
                            }
                            break;
                        }
                        case 3:
                        {
                            Entry_Ref_Ref[] entries = (Entry_Ref_Ref[]) dictionary.entries;
                            for (int i = 0; i < count; i++)
                            {
                                if (entries[i].hashCode >= 0)
                                {
                                    objArray[index++] = entries[i].value;
                                }
                            }
                            break;
                        }
                    }
                }
                catch (ArrayTypeMismatchException)
                {
                    throw new ArgumentException(
                        "Target array type is not compatible with the type of items in the collection.", nameof(array));
                }
            }
        }
        
        #endregion
        
        public struct Enumerator : IEnumerator<TValue>
        {
            private TKDictionary<TKey, TValue> dictionary;
            private int index;
            private int version;
            private TValue currentValue;

            #region Init

            public Enumerator(TKDictionary<TKey, TValue> dictionary)
            {
                this.dictionary = dictionary;
                version = dictionary.version;
                index = 0;
                currentValue = default(TValue);
            }

            #endregion

            #region Iter

            public bool MoveNext()
            {
                if (version != dictionary.version)
                    throw new InvalidOperationException("Collection was modified; enumeration operation may not execute.");

                switch (TKTwoType<TKey, TValue>.GenericType)
                {
                    case 0:
                    {
                        Entry_Val_Val<TKey, TValue>[] entries = (Entry_Val_Val<TKey, TValue>[]) dictionary.entries;
                        while (index < dictionary.internalCount)
                        {
                            if (entries[index].hashCode >= 0)
                            {
                                currentValue = entries[index].value;
                                index++;
                                return true;
                            }
                            index++;
                        }
                        break;
                    }
                    case 1:
                    {
                        Entry_Ref_Val<TValue>[] entries = (Entry_Ref_Val<TValue>[]) dictionary.entries;
                        while (index < dictionary.internalCount)
                        {
                            if (entries[index].hashCode >= 0)
                            {
                                currentValue = entries[index].value;
                                index++;
                                return true;
                            }
                            index++;
                        }
                        break;
                    }
                    case 2:
                    {
                        Entry_Val_Ref<TKey>[] entries = (Entry_Val_Ref<TKey>[]) dictionary.entries;
                        while (index < dictionary.internalCount)
                        {
                            if (entries[index].hashCode >= 0)
                            {
                                currentValue = (TValue) entries[index].value;
                                index++;
                                return true;
                            }
                            index++;
                        }
                        break;
                    }
                    case 3:
                    {
                        Entry_Ref_Ref[] entries = (Entry_Ref_Ref[]) dictionary.entries;
                        while (index < dictionary.internalCount)
                        {
                            if (entries[index].hashCode >= 0)
                            {
                                currentValue = (TValue) entries[index].value;
                                index++;
                                return true;
                            }
                            index++;
                        }
                        break;
                    }
                }

                index = dictionary.internalCount + 1;
                currentValue = default(TValue);
                return false;
            }

            void IEnumerator.Reset()
            {
                if (version != dictionary.version)
                    throw new InvalidOperationException("Collection was modified; enumeration operation may not execute.");
                index = 0;
                currentValue = default(TValue);
            }

            public void Dispose()
            {
            }

            #endregion

            #region Current

            public TValue Current
            {
                get { return currentValue; }
            }

            object IEnumerator.Current
            {
                get
                {
                    if (index == 0 || index == dictionary.Count + 1)
                        throw new InvalidOperationException("Enumeration has either not started or has already finished.");
                    return currentValue;
                }
            }

            #endregion
        }
    }

    public static class TKDictionary_Util
    {
        internal static void Initialize<TKey, TValue>(this TKDictionary<TKey, TValue> map, int capacity)
        {
            map.minCount = capacity <= 2 ? 2 : capacity;
            int size = TKPrime.GetPrime(capacity);
            
            int[] buckets = new int[size];
            for (int i = 0; i < buckets.Length; i++)
            {
                buckets[i] = -1;
            }

            map.buckets = buckets;
            map.freeList = -1;

            switch (TKTwoType<TKey, TValue>.GenericType)
            {
                case 0:
                    map.entries = new Entry_Val_Val<TKey, TValue>[size];
                    break;
                case 1:
                    map.entries = new Entry_Ref_Val<TValue>[size];
                    break;
                case 2:
                    map.entries = new Entry_Val_Ref<TKey>[size];
                    break;
                case 3:
                    map.entries = new Entry_Ref_Ref[size];
                    break;
            }
        }
        
        internal static bool TryInsert<TKey, TValue>(this TKDictionary<TKey, TValue> map, TKey key, TValue value, bool isOverWrite)
        {
            switch (TKTwoType<TKey, TValue>.GenericType)
            {
                case 0:
                {
                    if(map.buckets == null)
                        map.Initialize(3);
            
                    int[] buckets = map.buckets;
                    IEqualityComparer<TKey> comparer = map.comparer;
                    Entry_Val_Val<TKey, TValue>[] entries = (Entry_Val_Val<TKey, TValue>[]) map.entries;

                    int num1 = comparer.GetHashCode(key) & 0x7ffffff;
                    int currentBucketIndex = num1 % buckets.Length;
                    
                    for (int index2 = buckets[currentBucketIndex]; index2 >= 0; index2 = entries[index2].next)
                    {
                        if (entries[index2].hashCode == num1 && comparer.Equals(entries[index2].key, key))
                        {
                            if (isOverWrite)
                            {
                                entries[index2].value = value;
                                ++map.version;
                                return true;
                            }
                            else
                            {
                                throw new ArgumentException(string.Format("An item with the same key has already been added. Key: {0}",
                                    key));
                            }
                        }
                    }
                    
                    int currentIndex;
                    if (map.freeCount > 0)
                    {
                        currentIndex = map.freeList;
                        map.freeList = entries[currentIndex].next;
                        --map.freeCount;
                    }
                    else
                    {
                        if (map.internalCount == entries.Length)
                        {
                            map.Resize();
                        
                            buckets = map.buckets;
                            entries = (Entry_Val_Val<TKey, TValue>[]) map.entries;
                        
                            currentBucketIndex = num1 % buckets.Length;
                        }

                        currentIndex = map.internalCount;
                        ++map.internalCount;
                    }
                    
                    entries[currentIndex].hashCode = num1;
                    entries[currentIndex].next = buckets[currentBucketIndex];
                    entries[currentIndex].key = key;
                    entries[currentIndex].value = value;
                    buckets[currentBucketIndex] = currentIndex;
                    ++map.version;
                    break;
                }
                case 1:
                {
                    if (key == null)
                        throw new ArgumentNullException(nameof(key));
                    
                    if(map.buckets == null)
                        map.Initialize(3);
            
                    int[] buckets = map.buckets;
                    IEqualityComparer<TKey> comparer = map.comparer;
                    Entry_Ref_Val<TValue>[] entries = (Entry_Ref_Val<TValue>[]) map.entries;

                    int num1 = comparer.GetHashCode(key) & 0x7ffffff;
                    int currentBucketIndex = num1 % buckets.Length;
                    
                    for (int index2 = buckets[currentBucketIndex]; index2 >= 0; index2 = entries[index2].next)
                    {
                        if (entries[index2].hashCode == num1 && comparer.Equals((TKey) entries[index2].key, key))
                        {
                            if (isOverWrite)
                            {
                                entries[index2].value = value;
                                ++map.version;
                                return true;
                            }
                            else
                            {
                                throw new ArgumentException(string.Format("An item with the same key has already been added. Key: {0}",
                                    key));
                            }
                        }
                    }
                    
                    int currentIndex;
                    if (map.freeCount > 0)
                    {
                        currentIndex = map.freeList;
                        map.freeList = entries[currentIndex].next;
                        --map.freeCount;
                    }
                    else
                    {
                        if (map.internalCount == entries.Length)
                        {
                            map.Resize();
                        
                            buckets = map.buckets;
                            entries = (Entry_Ref_Val<TValue>[]) map.entries;
                        
                            currentBucketIndex = num1 % buckets.Length;
                        }

                        currentIndex = map.internalCount;
                        ++map.internalCount;
                    }
                    
                    entries[currentIndex].hashCode = num1;
                    entries[currentIndex].next = buckets[currentBucketIndex];
                    entries[currentIndex].key = key;
                    entries[currentIndex].value = value;
                    buckets[currentBucketIndex] = currentIndex;
                    ++map.version;
                    break;
                }
                case 2:
                {
                    if(map.buckets == null)
                        map.Initialize(3);
            
                    int[] buckets = map.buckets;
                    IEqualityComparer<TKey> comparer = map.comparer;
                    Entry_Val_Ref<TKey>[] entries = (Entry_Val_Ref<TKey>[]) map.entries;

                    int num1 = comparer.GetHashCode(key) & 0x7ffffff;
                    int currentBucketIndex = num1 % buckets.Length;
                    
                    for (int index2 = buckets[currentBucketIndex]; index2 >= 0; index2 = entries[index2].next)
                    {
                        if (entries[index2].hashCode == num1 && comparer.Equals(entries[index2].key, key))
                        {
                            if (isOverWrite)
                            {
                                entries[index2].value = value;
                                ++map.version;
                                return true;
                            }
                            else
                            {
                                throw new ArgumentException(string.Format("An item with the same key has already been added. Key: {0}",
                                    key));
                            }
                        }
                    }
                    
                    int currentIndex;
                    if (map.freeCount > 0)
                    {
                        currentIndex = map.freeList;
                        map.freeList = entries[currentIndex].next;
                        --map.freeCount;
                    }
                    else
                    {
                        if (map.internalCount == entries.Length)
                        {
                            map.Resize();
                        
                            buckets = map.buckets;
                            entries = (Entry_Val_Ref<TKey>[]) map.entries;
                        
                            currentBucketIndex = num1 % buckets.Length;
                        }

                        currentIndex = map.internalCount;
                        ++map.internalCount;
                    }
                    
                    entries[currentIndex].hashCode = num1;
                    entries[currentIndex].next = buckets[currentBucketIndex];
                    entries[currentIndex].key = key;
                    entries[currentIndex].value = value;
                    buckets[currentBucketIndex] = currentIndex;
                    ++map.version;
                    break;
                }
                case 3:
                {
                    if (key == null)
                        throw new ArgumentNullException(nameof(key));
                    
                    if(map.buckets == null)
                        map.Initialize(3);
            
                    int[] buckets = map.buckets;
                    IEqualityComparer<TKey> comparer = map.comparer;
                    Entry_Ref_Ref[] entries = (Entry_Ref_Ref[]) map.entries;

                    int num1 = comparer.GetHashCode(key) & 0x7ffffff;
                    int currentBucketIndex = num1 % buckets.Length;
                    
                    for (int index2 = buckets[currentBucketIndex]; index2 >= 0; index2 = entries[index2].next)
                    {
                        if (entries[index2].hashCode == num1 && comparer.Equals((TKey) entries[index2].key, key))
                        {
                            if (isOverWrite)
                            {
                                entries[index2].value = value;
                                ++map.version;
                                return true;
                            }
                            else
                            {
                                throw new ArgumentException(string.Format("An item with the same key has already been added. Key: {0}",
                                    key));
                            }
                        }
                    }
                    
                    int currentIndex;
                    if (map.freeCount > 0)
                    {
                        currentIndex = map.freeList;
                        map.freeList = entries[currentIndex].next;
                        --map.freeCount;
                    }
                    else
                    {
                        if (map.internalCount == entries.Length)
                        {
                            map.Resize();
                        
                            buckets = map.buckets;
                            entries = (Entry_Ref_Ref[]) map.entries;
                        
                            currentBucketIndex = num1 % buckets.Length;
                        }

                        currentIndex = map.internalCount;
                        ++map.internalCount;
                    }
                    
                    entries[currentIndex].hashCode = num1;
                    entries[currentIndex].next = buckets[currentBucketIndex];
                    entries[currentIndex].key = key;
                    entries[currentIndex].value = value;
                    buckets[currentBucketIndex] = currentIndex;
                    ++map.version;
                    break;
                }
            }

            return true;
        }
        
        internal static int FindEntry<TKey, TValue>(this TKDictionary<TKey, TValue> map, TKey key)
        {
            int[] buckets = map.buckets;
            if (buckets == null)
                return -1;
            
            IEqualityComparer<TKey> comparer = map.comparer;

            switch (TKTwoType<TKey, TValue>.GenericType)
            {
                case 0:
                {
                    int num = comparer.GetHashCode(key) & 0x7ffffff;
                    
                    Entry_Val_Val<TKey, TValue>[] entries = (Entry_Val_Val<TKey, TValue>[]) map.entries;
                    for (int index = buckets[num % buckets.Length]; index >= 0; index = entries[index].next)
                    {
                        if (entries[index].hashCode == num && comparer.Equals(entries[index].key, key))
                            return index;
                    }
                    break;
                }
                case 1:
                {
                    if (key == null)
                        throw new ArgumentNullException(nameof(key));
                    
                    int num = comparer.GetHashCode(key) & 0x7ffffff;
                    
                    Entry_Ref_Val<TValue>[] entries = (Entry_Ref_Val<TValue>[]) map.entries;
                    for (int index = buckets[num % buckets.Length]; index >= 0; index = entries[index].next)
                    {
                        if (entries[index].hashCode == num && comparer.Equals((TKey) entries[index].key, key))
                            return index;
                    }
                    break;
                }
                case 2:
                {
                    int num = comparer.GetHashCode(key) & 0x7ffffff;
                    
                    Entry_Val_Ref<TKey>[] entries = (Entry_Val_Ref<TKey>[]) map.entries;
                    for (int index = buckets[num % buckets.Length]; index >= 0; index = entries[index].next)
                    {
                        if (entries[index].hashCode == num && comparer.Equals(entries[index].key, key))
                            return index;
                    }
                    break;
                }
                case 3:
                {
                    if (key == null)
                        throw new ArgumentNullException(nameof(key));
                    
                    int num = comparer.GetHashCode(key) & 0x7ffffff;
                    
                    Entry_Ref_Ref[] entries = (Entry_Ref_Ref[]) map.entries;
                    for (int index = buckets[num % buckets.Length]; index >= 0; index = entries[index].next)
                    {
                        if (entries[index].hashCode == num && comparer.Equals((TKey) entries[index].key, key))
                            return index;
                    }
                    break;
                }
            }

            return -1;
        }
        
        public static bool ContainsValue<TKey, TValue>(this TKDictionary<TKey, TValue> map, TValue value)
        {
            int count = map.internalCount;

            switch (TKTwoType<TKey, TValue>.GenericType)
            {
                case 0:
                {
                    Entry_Val_Val<TKey, TValue>[] entries = (Entry_Val_Val<TKey, TValue>[]) map.entries;
                    EqualityComparer<TValue> equalityComparer = EqualityComparer<TValue>.Default;
                    for (int index = 0; index < count; ++index)
                    {
                        if (entries[index].hashCode >= 0 && equalityComparer.Equals(entries[index].value, value))
                            return true;
                    }
                    break;
                }
                case 1:
                {
                    Entry_Ref_Val<TValue>[] entries = (Entry_Ref_Val<TValue>[]) map.entries;
                    EqualityComparer<TValue> equalityComparer = EqualityComparer<TValue>.Default;
                    for (int index = 0; index < count; ++index)
                    {
                        if (entries[index].hashCode >= 0 && equalityComparer.Equals(entries[index].value, value))
                            return true;
                    }
                    break;
                }
                case 2:
                {
                    Entry_Val_Ref<TKey>[] entries = (Entry_Val_Ref<TKey>[]) map.entries;
                    EqualityComparer<TValue> equalityComparer = EqualityComparer<TValue>.Default;
                    for (int index = 0; index < count; ++index)
                    {
                        if (entries[index].hashCode >= 0 && equalityComparer.Equals((TValue) entries[index].value, value))
                            return true;
                    }
                    break;
                }
                case 3:
                {
                    Entry_Ref_Ref[] entries = (Entry_Ref_Ref[]) map.entries;
                    EqualityComparer<TValue> equalityComparer = EqualityComparer<TValue>.Default;
                    for (int index = 0; index < count; ++index)
                    {
                        if (entries[index].hashCode >= 0 && equalityComparer.Equals((TValue) entries[index].value, value))
                            return true;
                    }
                    break;
                }
            }

            return false;
        }
        
        internal static void CopyTo<TKey, TValue>(this TKDictionary<TKey, TValue> map, KeyValuePair<TKey, TValue>[] array, int index)
        {
            if (array == null)
                throw new ArgumentNullException(nameof(array));
            if (index < 0 || index > array.Length)
                throw new ArgumentOutOfRangeException(nameof(index), (object) index,
                    "Index was out of range. Must be non-negative and less than the size of the collection.");
            if (array.Length - index < map.Count)
                throw new ArgumentException(
                    "Destination array is not long enough to copy all the items in the collection. Check array index and length.");

            switch (TKTwoType<TKey, TValue>.GenericType)
            {
                case 0:
                {
                    Entry_Val_Val<TKey, TValue>[] entries = (Entry_Val_Val<TKey, TValue>[]) map.entries;
                    for(int i = 0; i < map.internalCount; i++)
                    {
                        if (entries[i].hashCode >= 0)
                        {
                            array[index++] = new KeyValuePair<TKey, TValue>(entries[i].key, entries[i].value);
                        }
                    }
                    break;
                }
                case 1:
                {
                    Entry_Ref_Val<TValue>[] entries = (Entry_Ref_Val<TValue>[]) map.entries;
                    for(int i = 0; i < map.internalCount; i++)
                    {
                        if (entries[i].hashCode >= 0)
                        {
                            array[index++] = new KeyValuePair<TKey, TValue>((TKey) entries[i].key, entries[i].value);
                        }
                    }
                    break;
                }
                case 2:
                {
                    Entry_Val_Ref<TKey>[] entries = (Entry_Val_Ref<TKey>[]) map.entries;
                    for(int i = 0; i < map.internalCount; i++)
                    {
                        if (entries[i].hashCode >= 0)
                        {
                            array[index++] = new KeyValuePair<TKey, TValue>(entries[i].key, (TValue) entries[i].value);
                        }
                    }
                    break;
                }
                case 3:
                {
                    Entry_Ref_Ref[] entries = (Entry_Ref_Ref[]) map.entries;
                    for(int i = 0; i < map.internalCount; i++)
                    {
                        if (entries[i].hashCode >= 0)
                        {
                            array[index++] = new KeyValuePair<TKey, TValue>((TKey) entries[i].key, (TValue) entries[i].value);
                        }
                    }
                    break;
                }
            }
        }
        
        private static void Resize<TKey, TValue>(this TKDictionary<TKey, TValue> map)
        {
            int count = map.internalCount;
            if (map.minCount == 0)
                map.minCount = 1;
            while (map.minCount <= map.internalCount)
            {
                map.minCount *= 2;
            }
            int newSize = TKPrime.GetPrime(map.minCount);

            int[] numArray = new int[newSize];
            for (int index = 0; index < numArray.Length; ++index)
                numArray[index] = -1;

            switch (TKTwoType<TKey, TValue>.GenericType)
            {
                case 0:
                {
                    Entry_Val_Val<TKey, TValue>[] entryArray = new Entry_Val_Val<TKey, TValue>[newSize];
                    Array.Copy(map.entries, 0, entryArray, 0, count);
                    
                    for (int index1 = 0; index1 < count; index1++)
                    {
                        if (entryArray[index1].hashCode >= 0)
                        {
                            int index2 = entryArray[index1].hashCode % newSize;
                            entryArray[index1].next = numArray[index2];
                            numArray[index2] = index1;
                        }
                    }
                    
                    map.buckets = numArray;
                    map.entries = entryArray;
                    break;
                }
                case 1:
                {
                    Entry_Ref_Val<TValue>[] entryArray = new Entry_Ref_Val<TValue>[newSize];
                    Array.Copy(map.entries, 0, entryArray, 0, count);
                    
                    for (int index1 = 0; index1 < count; index1++)
                    {
                        if (entryArray[index1].hashCode >= 0)
                        {
                            int index2 = entryArray[index1].hashCode % newSize;
                            entryArray[index1].next = numArray[index2];
                            numArray[index2] = index1;
                        }
                    }
                    
                    map.buckets = numArray;
                    map.entries = entryArray;
                    break;
                }
                case 2:
                {
                    Entry_Val_Ref<TKey>[] entryArray = new Entry_Val_Ref<TKey>[newSize];
                    Array.Copy(map.entries, 0, entryArray, 0, count);
                    
                    for (int index1 = 0; index1 < count; index1++)
                    {
                        if (entryArray[index1].hashCode >= 0)
                        {
                            int index2 = entryArray[index1].hashCode % newSize;
                            entryArray[index1].next = numArray[index2];
                            numArray[index2] = index1;
                        }
                    }
                    
                    map.buckets = numArray;
                    map.entries = entryArray;
                    break;
                }
                case 3:
                {
                    Entry_Ref_Ref[] entryArray = new Entry_Ref_Ref[newSize];
                    Array.Copy(map.entries, 0, entryArray, 0, count);
                    
                    for (int index1 = 0; index1 < count; index1++)
                    {
                        if (entryArray[index1].hashCode >= 0)
                        {
                            int index2 = entryArray[index1].hashCode % newSize;
                            entryArray[index1].next = numArray[index2];
                            numArray[index2] = index1;
                        }
                    }
                    
                    map.buckets = numArray;
                    map.entries = entryArray;
                    break;
                }
            }
            
        }
        
        internal static TValue GetValue<TKey, TValue>(this TKDictionary<TKey, TValue> map, int index)
        {
            switch (TKTwoType<TKey, TValue>.GenericType)
            {
                case 0:
                {
                    Entry_Val_Val<TKey, TValue>[] entries = (Entry_Val_Val<TKey, TValue>[]) map.entries;
                    return entries[index].value;
                }
                case 1:
                {
                    Entry_Ref_Val<TValue>[] entries = (Entry_Ref_Val<TValue>[]) map.entries;
                    return entries[index].value;
                }
                case 2:
                {
                    Entry_Val_Ref<TKey>[] entries = (Entry_Val_Ref<TKey>[]) map.entries;
                    return (TValue) entries[index].value;
                }
                case 3:
                {
                    Entry_Ref_Ref[] entries = (Entry_Ref_Ref[]) map.entries;
                    return (TValue) entries[index].value;
                }
            }

            return default(TValue);
        }
        
        #region ConditionalWeakTable
        
        private static MonoConditionalWeakTable<object, SerializationInfo> m_serializationInfoTable;

        internal static MonoConditionalWeakTable<object, SerializationInfo> SerializationInfoTable
        {
            get
            {
                if(m_serializationInfoTable == null)
                {
                    MonoConditionalWeakTable<object, SerializationInfo> newTable = new MonoConditionalWeakTable<object, SerializationInfo>();
                    Interlocked.CompareExchange(ref m_serializationInfoTable, newTable, null);
                }
 
                return m_serializationInfoTable;
            }
        }
        
        #endregion
    }
    
    #else

    public class TKDictionary<TKey, TValue> : Dictionary<TKey, TValue>
    {
        public TKDictionary() : base() { }
        public TKDictionary(int capacity) : base(capacity) { }
        public TKDictionary(IEqualityComparer<TKey> comparer) : base(comparer) { }
        public TKDictionary(IDictionary<TKey, TValue> dictionary) : base(dictionary) { }
        public TKDictionary(int capacity, IEqualityComparer<TKey> comparer) : base(capacity, comparer) { }
    }
    
    #endif

    public static class TKDictionary_Enumerable_Util
    {
        internal class IdentityFunction<TElement>
        {
            public static Func<TElement, TElement> Instance => (Func<TElement, TElement>) (x => x);
        }

        public static TKDictionary<TKey, TSource> ToTKDictionary<TSource, TKey>(this IEnumerable<TSource> source, Func<TSource, TKey> keySelector)
        {
            return ToTKDictionary<TSource, TKey, TSource>(source, keySelector, IdentityFunction<TSource>.Instance, null);
        }

        public static TKDictionary<TKey, TSource> ToTKDictionary<TSource, TKey>(this IEnumerable<TSource> source, Func<TSource, TKey> keySelector, IEqualityComparer<TKey> comparer)
        {
            return ToTKDictionary<TSource, TKey, TSource>(source, keySelector, IdentityFunction<TSource>.Instance, comparer);
        }

        public static TKDictionary<TKey, TElement> ToTKDictionary<TSource, TKey, TElement>(this IEnumerable<TSource> source, Func<TSource, TKey> keySelector, Func<TSource, TElement> elementSelector)
        {
            return ToTKDictionary<TSource, TKey, TElement>(source, keySelector, elementSelector, null);
        }

        public static TKDictionary<TKey, TElement> ToTKDictionary<TSource, TKey, TElement>(
            this IEnumerable<TSource> source, Func<TSource, TKey> keySelector, Func<TSource, TElement> elementSelector,
            IEqualityComparer<TKey> comparer)
        {
            if (source == null)
                throw new ArgumentNullException("source");
            if (keySelector == null)
                throw new ArgumentNullException("keySelector");
            if (elementSelector == null)
                throw new ArgumentNullException("elementSelector");
            TKDictionary<TKey, TElement> d = new TKDictionary<TKey, TElement>(comparer);
            foreach (TSource element in source)
                d.Add(keySelector(element), elementSelector(element));
            return d;
        }
    }
}


