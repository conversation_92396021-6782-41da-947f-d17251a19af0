using System.Collections.Generic;

namespace TKFrame
{
    /// <summary>
    /// 小红点数据结构类
    /// </summary>
    public class RedDotInfo
    {
        /// <summary>
        /// 小红点ID
        /// </summary>
        public int id;

        /// <summary>
        /// 小红点类型
        /// </summary>
        public int type;

        /// <summary>
        /// 父亲ID
        /// </summary>
        public int parentID;

        /// <summary>
        /// 父亲红点类型
        /// </summary>
        public int parentType;

        /// <summary>
        /// 构造类
        /// </summary>
        public RedDotInfo(int _id, int _type, int _parentID, int _parentType)
        {
            this.id = _id;
            this.type = _type;
            this.parentID = _parentID;
            this.parentType = _parentType;
        }
    }


    /// <summary>
    /// 红点管理器类
    /// </summary>
    public class RedDotMgr:TKObject
    {
		/// <summary>
		/// 管理器单例句柄
		/// </summary>
		private static RedDotMgr _Instance = null;
		public static RedDotMgr Instance
		{
			get
			{
				if(_Instance == null)
				{
					_Instance = new RedDotMgr();
				}
				return _Instance;
			}
		}

		/// <summary>
		/// 红点树根节点
		/// </summary>
		private RedDot redDotRoot = null;
		public RedDotMgr()
		{
            redDotRoot = new RedDot(RedDot.RED_DOT_ID_ROOT);
		}

		/// <summary>
		/// 析构管理器类
		/// </summary>
		public virtual void Dispose()
		{
			_Instance = null;
		}

		/// <summary>
		/// 根据ID和type查找小红点
		/// </summary>
		public RedDot GetRedDotByIDAndType(int id, int type = RedDot.RED_DOT_TYPE_DEFAULT)
		{
			RedDot dot = redDotRoot.SearchRedDotRecursion(id, type);
			return dot;
		}

		/// <summary>
		/// 初始化红点树结构
		/// </summary>
        public void InitRedDotTree(List<RedDotInfo> all_reddot_configs)
		{
            for (int i = 0; i < all_reddot_configs.Count; i++)
			{
                this.GenerateRedDot(all_reddot_configs[i].id, all_reddot_configs[i].type,
                                    all_reddot_configs[i].parentID, all_reddot_configs[i].parentType);
			}
		}

		/// <summary>
		/// 动态创建红点
		/// </summary>
		public RedDot GenerateRedDot(int id, int type, int parentID, int parentType)
		{
			RedDot dot = null;
			RedDot parent = this.GetRedDotByIDAndType(parentID, parentType);
			if(parent != null)
			{
				dot = new RedDot(id, type);
				if(dot != null && parent != null)
				{
					dot.Parent = parent;
					parent.AddChild(dot);
				}
			}
			else
			{
				Diagnostic.Error($"Add RedDot Error, parent is null parent id is:{parentID}, type is {parentType}");
			}
			return dot;
		}
		
		
		/// <summary>
		/// 动态创建红点, 如果已经有了的话，要先删除再创建，为了应对换父这种情况，活动会有
		/// </summary>
		public RedDot GenerateRedDotWithRemove(int id, int type, int parentID, int parentType)
		{
			RedDot dot = null;
			RedDot now = this.GetRedDotByIDAndType(id, type);
			if (now != null)
			{
				//有了，先删一下
				var parent = now.Parent;
				RefreshRedPoint(false, id, type);
				parent.DeleteChild(id, type);
			}

			return GenerateRedDot(id, type, parentID, parentType);
		}

		/// <summary>
		/// 用于刷新红点
		/// </summary>
		/// <param name="bNeedShow">是否打开显示红点</param>
		/// <param name="id">红点id</param>
		/// <param name="type">红点类型</param>
		/// <param name="forceUpdate">是否强制更新,红点刷新默认必须是叶子节点，除非强制更新</param>
		public void RefreshRedPoint(bool bNeedShow, int id, int type = RedDot.RED_DOT_TYPE_DEFAULT, bool forceUpdate = false)
		{
			RedDot dot = redDotRoot.SearchRedDotRecursion(id, type);
			if (dot != null)
			{
				if (!dot.isLeaf && !forceUpdate)
				{
					//红点刷新必须是叶子节点，这里直接禁止手动刷新非叶子节点
					Diagnostic.Error("RefreshRedPoint id={0} type={1}is not a leaf node, please check!", id, type);
					return;
				}
				dot.IsShown = bNeedShow; //里面会从子往父节点遍历发通知,所以要写在最后面

//#if UNITY_EDITOR
//                Diagnostic.Log("RefreshRedPoint id={0}({1}) type={2} IsShown={3}", id, (ZGameChess.ERedDotID)id, type, bNeedShow);
//#endif 
			}
			else
			{
				Diagnostic.Log("RefreshRedPoint id={0} type={1} is not in redDot tree", id, type);
			}
		}

		/// <summary>
		/// 用于刷新红点
		/// </summary>
		/// <param name="bNeedShow">是否打开显示红点</param>
		/// <param name="id">红点id</param>
		/// <param name="num">红点数量</param>
		/// <param name="type">红点类型</param>
		/// <param name="forceUpdate">是否强制更新,红点刷新默认必须是叶子节点，除非强制更新</param>
		public void RefreshRedPointWithNum(bool bNeedShow, int id,  int type = RedDot.RED_DOT_TYPE_DEFAULT, int num = 0, bool forceUpdate = false)
		{
			RedDot dot = redDotRoot.SearchRedDotRecursion(id, type);
			if (dot != null)
			{
				if (!dot.isLeaf && !forceUpdate)
				{
					//红点刷新必须是叶子节点，这里直接禁止手动刷新非叶子节点
					Diagnostic.Error("RefreshRedPoint id={0} type={1}is not a leaf node, please check!", id, type);
					return;
				}
				dot.Value = num;
				dot.IsShown = bNeedShow; //里面会从子往父节点遍历发通知,所以要写在最后面
//#if UNITY_EDITOR
//                Diagnostic.Log("RefreshRedPoint id={0}({1}) type={2} IsShown={3}", id, (ZGameChess.ERedDotID)id, type, bNeedShow);
//#endif 
			}
			else
			{
				Diagnostic.Log("RefreshRedPoint id={0} type={1} is not in redDot tree", id, type);
			}
		}

        /// <summary>
        /// 删除树
        /// </summary>
        public void DestroyRedTree()
        {
            if (redDotRoot != null)
            {
                redDotRoot.DeleteChildren();
                Diagnostic.Log("---DestroyRedTree---");
            }
        }
    }
}
