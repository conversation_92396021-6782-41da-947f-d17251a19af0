using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using ProtoBuf;
using TKFrame;

namespace UniqueInfo
{
    [DebuggerTypeProxy(typeof(TKDebugDisplay_Dictionary<,>))]
    [DebuggerDisplay("Count = {Count}")]
    public class ConfigHashMap<TKey, TValue> : IDictionary<TKey, TValue>, IDictionary
    {
        [ProtoMember(1)] 
        internal volatile TKDictionary<TKey, int> mm_indexMap;
        internal Func<int, FastBinaryReader, TValue> m_readValueFunc;
        internal Action<ConfigHashMap<TKey, TValue>, FastBinaryReader> m_readKeyFunc;
        internal ConfigHashMap_KeyCollection<TKey, TValue> m_keys;
        internal ConfigHashMap_ValueCollection<TKey, TValue> m_values;
        internal TLSStreamReader m_reader;
        internal uint m_readOffset = 0;
        internal int m_externalId = -1;

        internal List<TValue> m_datas = null;
        internal TKDictionary<int, TValue> m_datasExternal = null;
        internal bool m_isOriginData = true;

        #region Init

        public ConfigHashMap()
        {
        }

        public ConfigHashMap(ConfigHashMap<TKey, TValue> other)
        {
            if (other.m_indexMap != null)
                mm_indexMap = new TKDictionary<TKey, int>(other.mm_indexMap);
            else
                mm_indexMap = new TKDictionary<TKey, int>();

            m_readValueFunc = other.m_readValueFunc;
            m_readKeyFunc = other.m_readKeyFunc;
            m_reader = other.m_reader;
            m_readOffset = other.m_readOffset;
            m_isOriginData = false;

            if (m_reader == null)
            {
                m_datas = new List<TValue>(mm_indexMap.Count);
                m_datas.AddRange(other.m_datas);
            }
        }

        #endregion

        #region Property

        public bool isOriginData
        {
            get
            {
                return m_isOriginData;
            }
        }

        public bool isJce
        {
            get
            {
                return m_reader == null;
            }
        }

        public bool hasExternalData
        {
            get
            {
                return m_datasExternal.Count > 0;
            }
        }

        public ConfigHashMap_KeyCollection<TKey, TValue> Keys
        {
            get
            {
                if (m_keys == null)
                    m_keys = new ConfigHashMap_KeyCollection<TKey, TValue>(this);
                return m_keys;
            }
        }

        public ConfigHashMap_ValueCollection<TKey, TValue> Values
        {
            get
            {
                if (m_values == null)
                    m_values = new ConfigHashMap_ValueCollection<TKey, TValue>(this);
                return m_values;
            }
        }

        public ConfigHashMap_Enumerator<TKey, TValue> GetEnumerator()
        {
            return new ConfigHashMap_Enumerator<TKey, TValue>(this);
        }

        internal TKDictionary<TKey, int> m_indexMap
        {
            get
            {
                if (mm_indexMap == null)
                {
                    this.InitIndexMap();
                }

                return mm_indexMap;
            }
        }

        #endregion

        #region ICollection<KeyValuePair<TKey, TValue>>

        void ICollection<KeyValuePair<TKey, TValue>>.Add(KeyValuePair<TKey, TValue> keyValuePair)
        {
            if (m_isOriginData)
            {
#if UNITY_EDITOR
                this.AddKeyValue(keyValuePair.Key, keyValuePair.Value);
#else
                throw new NotSupportedException("Add Key Value in a original ConfigHashMap is not allowed.");
#endif
            }
            else
            {
                Add(keyValuePair.Key, keyValuePair.Value);
            }
        }

        bool ICollection<KeyValuePair<TKey, TValue>>.Contains(KeyValuePair<TKey, TValue> keyValuePair)
        {
            int index = 0;
            if (m_indexMap.TryGetValue(keyValuePair.Key, out index))
            {
                EqualityComparer<TValue> comparer = EqualityComparer<TValue>.Default;
                TValue value = this.GetValue(index, this.GetReader());
                if (comparer.Equals(keyValuePair.Value, value))
                    return true;
            }

            return false;
        }

        bool ICollection<KeyValuePair<TKey, TValue>>.Remove(KeyValuePair<TKey, TValue> keyValuePair)
        {
            if (m_isOriginData)
                throw new NotSupportedException("Remove Key Value in a original ConfigHashMap is not allowed.");

            TKey key = keyValuePair.Key;
            if (m_indexMap.TryGetValue(key, out int index))
            {
                EqualityComparer<TValue> comparer = EqualityComparer<TValue>.Default;
                TValue value = this.GetValue(index, this.GetReader());
                if (comparer.Equals(keyValuePair.Value, value))
                {
                    if (index >= 0)
                        m_indexMap.Remove(key);
                    else
                    {
                        m_datasExternal.Remove(index);
                        m_indexMap.Remove(key);
                    }
                }
            }
            return false;
        }

        void ICollection<KeyValuePair<TKey, TValue>>.CopyTo(KeyValuePair<TKey, TValue>[] array, int arrayIndex)
        {
            this.CopyTo(array, arrayIndex);
        }

        #endregion

        #region IEnumerable<KeyValuePair<TKey, TValue>>

        IEnumerator<KeyValuePair<TKey, TValue>> IEnumerable<KeyValuePair<TKey, TValue>>.GetEnumerator()
        {
            return (IEnumerator<KeyValuePair<TKey, TValue>>) new ConfigHashMap_Enumerator<TKey, TValue>(this);
        }

        #endregion

        #region ICollection

        void ICollection.CopyTo(Array array, int index)
        {
            if (array == null)
                throw new ArgumentNullException(nameof(array));
            if (array.Rank != 1)
                throw new ArgumentException("Only single dimensional arrays are supported for the requested action.",
                    nameof(array));
            if (array.GetLowerBound(0) != 0)
                throw new ArgumentException("The lower bound of target array must be zero.", nameof(array));
            if (index < 0 || index > array.Length)
                throw new ArgumentOutOfRangeException(nameof(index), (object) index,
                    "Index was out of range. Must be non-negative and less than the size of the collection.");
            if (array.Length - index < Count)
                throw new ArgumentException(
                    "Destination array is not long enough to copy all the items in the collection. Check array index and length.");

            if (array is KeyValuePair<TKey, TValue>[])
            {
                KeyValuePair<TKey, TValue>[] pairArray = array as KeyValuePair<TKey, TValue>[];
                this.CopyTo(pairArray, index);
            }
            else if (array is DictionaryEntry[])
            {
                DictionaryEntry[] dictionaryEntryArray = array as DictionaryEntry[];
                FastBinaryReader br = this.GetReader();
                foreach (KeyValuePair<TKey, int> pair in m_indexMap)
                {
                    TKey key = pair.Key;
                    TValue value = this.GetValue(pair.Value, br);
                    dictionaryEntryArray[index] = new DictionaryEntry(key, value);
                    index++;
                }
            }
            else
            {
                object[] objArray = array as object[];
                if (objArray == null)
                    throw new ArgumentException(
                        "Target array type is not compatible with the type of items in the collection.", nameof(array));
                try
                {
                    FastBinaryReader br = this.GetReader();
                    foreach (KeyValuePair<TKey, int> pair in m_indexMap)
                    {
                        TKey key = pair.Key;
                        TValue value = this.GetValue(pair.Value, br);
                        objArray[index] = new KeyValuePair<TKey, TValue>(key, value);
                        index++;
                    }
                }
                catch (ArrayTypeMismatchException)
                {
                    throw new ArgumentException(
                        "Target array type is not compatible with the type of items in the collection.", nameof(array));
                }
            }
        }

        bool ICollection.IsSynchronized
        {
            get { return ((ICollection) m_indexMap).IsSynchronized; }
        }

        Object ICollection.SyncRoot
        {
            get { return ((ICollection) m_indexMap).SyncRoot; }
        }

        #endregion

        #region IEnumerable

        IEnumerator IEnumerable.GetEnumerator()
        {
            return new ConfigHashMap_Enumerator<TKey, TValue>(this);
        }

        #endregion

        #region IDictionary<TKey, TValue>

        ICollection<TKey> IDictionary<TKey, TValue>.Keys => Keys;

        ICollection<TValue> IDictionary<TKey, TValue>.Values => Values;

        void IDictionary<TKey, TValue>.Add(TKey key, TValue value)
        {
            Add(key, value);
        }

        bool IDictionary<TKey, TValue>.Remove(TKey key)
        {
            return Remove(key);
        }

        #endregion

        #region IDictionary

        object IDictionary.this[object key]
        {
            get
            {
                if (key is TKey)
                {
                    int index = -1;
                    if (m_indexMap.TryGetValue((TKey) key, out index))
                    {
                        return this.GetValue(index, this.GetReader());
                    }
                }

                return default(TValue);
            }
            set
            {
                if(m_isOriginData)
                    throw new NotSupportedException("Change Key from a ConfigHashMap is not allowed.");
                
                if (key is TKey && value is TValue)
                {
                    this[(TKey) key] = (TValue) value;
                }
            }
        }

        ICollection IDictionary.Keys => Keys;

        ICollection IDictionary.Values => Values;

        bool IDictionary.Contains(object key)
        {
            if (key is TKey)
                return m_indexMap.ContainsKey((TKey) key);
            return false;
        }

        void IDictionary.Add(object key, object value)
        {
            Add((TKey) key, (TValue) value);
        }

        bool IDictionary.IsFixedSize
        {
            get { return false; }
        }

        IDictionaryEnumerator IDictionary.GetEnumerator()
        {
            return GetEnumerator();
        }

        void IDictionary.Remove(object key)
        {
            Remove((TKey) key);
        }

        #endregion

        #region Common Method

        public void Clear()
        {
            if (m_reader == null)
            {
                if (mm_indexMap != null)
                    mm_indexMap.Clear();
                if (m_datas != null)
                    m_datas.Clear();
            }
        }

        public bool ContainsKey(TKey key)
        {
            return m_indexMap.ContainsKey(key);
        }

        public bool TryGetValue(TKey key, out TValue value)
        {
            int index = -1;
            if (m_indexMap.TryGetValue(key, out index))
            {
                value = this.GetValue(index, this.GetReader());
                return true;
            }

            value = default(TValue);
            return false;
        }

        public int Count
        {
            get { return m_indexMap.Count; }
        }

        public bool IsReadOnly
        {
            get { return true; }
        }

        public TValue this[TKey key]
        {
            get
            {
                int index = -1;
                if (m_indexMap.TryGetValue(key, out index))
                {
                    return this.GetValue(index, this.GetReader());
                }
                else
                {
                    throw new System.Collections.Generic.KeyNotFoundException();
                    return default(TValue);
                }
            }
            set
            {
                if (m_isOriginData)
                    throw new NotSupportedException("Change Key in a original ConfigHashMap is not allowed.");
                
                this.TryInsert(key, value, true);
            }
        }
        
        public void Add(TKey key, TValue value)
        {
            if (m_isOriginData)
                throw new NotSupportedException("Add Key Value in a original ConfigHashMap is not allowed.");
            
            this.TryInsert(key, value, false);
        }

        public bool Remove(TKey key)
        {
            if(m_isOriginData)
                throw new NotSupportedException("Remove Key Value in a original ConfigHashMap is not allowed.");
            
            if (m_indexMap.TryGetValue(key, out int index))
            {
                if (index >= 0)
                    m_indexMap.Remove(key);
                else
                {
                    m_datasExternal.Remove(index);
                    m_indexMap.Remove(key);
                }

                return true;
            }
            return false;
        }

        #endregion
    }

    public struct ConfigHashMap_Enumerator<TKey, TValue> : IEnumerator<KeyValuePair<TKey, TValue>>, IDictionaryEnumerator
    {
        private ConfigHashMap<TKey, TValue> m_dict;
        private KeyValuePair<TKey, TValue> m_current;
#if OPTIMIZE_COLLECTION
        private TKDictionary_Enumerator<TKey, int> m_enumerator;
#else
        private Dictionary<TKey, int>.Enumerator m_enumerator;
#endif
        private FastBinaryReader m_br;
        private int m_index;

        #region Init

        public ConfigHashMap_Enumerator(ConfigHashMap<TKey, TValue> dictionary)
        {
            m_dict = dictionary;
            m_current = default(KeyValuePair<TKey, TValue>);
            m_enumerator = m_dict.m_indexMap.GetEnumerator();
            m_br = m_dict.GetReader();
            m_index = 0;
        }

        #endregion

        #region Iter

        public bool MoveNext()
        {
            if (m_enumerator.MoveNext())
            {
                KeyValuePair<TKey, int> currentPair = m_enumerator.Current;
                int index = currentPair.Value;
                TValue value = m_dict.GetValue(index, m_br);
                m_current = new KeyValuePair<TKey, TValue>(currentPair.Key, value);
                m_index++;
                return true;
            }
            else
            {
                m_index = m_dict.Count;
                m_current = new KeyValuePair<TKey, TValue>();
                return false;
            }
        }

        void IEnumerator.Reset()
        {
            ((IEnumerator) m_enumerator).Reset();
            m_current = new KeyValuePair<TKey, TValue>();
        }

        public void Dispose()
        {
        }

        #endregion

        #region Current

        public KeyValuePair<TKey, TValue> Current
        {
            get { return m_current; }
        }

        object IEnumerator.Current
        {
            get
            {
                if (m_index == 0 || m_index == m_dict.Count + 1)
                    throw new InvalidOperationException("Enumeration has either not started or has already finished.");
                return m_current;
            }
        }

        #endregion

        #region IDictionaryEnumerator

        DictionaryEntry IDictionaryEnumerator.Entry
        {
            get
            {
                if (m_index == 0 || m_index == m_dict.Count + 1)
                    throw new InvalidOperationException("Enumeration has either not started or has already finished.");
                return new DictionaryEntry(m_current.Key, m_current.Value);
            }
        }

        object IDictionaryEnumerator.Key
        {
            get
            {
                if (m_index == 0 || m_index == m_dict.Count + 1)
                    throw new InvalidOperationException("Enumeration has either not started or has already finished.");
                return m_current.Key;
            }
        }

        object IDictionaryEnumerator.Value
        {
            get
            {
                if (m_index == 0 || m_index == m_dict.Count + 1)
                    throw new InvalidOperationException("Enumeration has either not started or has already finished.");
                return m_current.Value;
            }
        }

        #endregion
    }

    public sealed class ConfigHashMap_KeyCollection<TKey, TValue> : ICollection<TKey>, ICollection, IReadOnlyCollection<TKey>
    {
        private ConfigHashMap<TKey, TValue> m_dict;

        #region Init

        public ConfigHashMap_KeyCollection(ConfigHashMap<TKey, TValue> dictionary)
        {
            if (dictionary == null)
                throw new ArgumentNullException(nameof(dictionary));
            m_dict = dictionary;
        }

        #endregion

        #region Property

        public int Count
        {
            get { return m_dict.Count; }
        }

        bool ICollection<TKey>.IsReadOnly
        {
            get { return true; }
        }

        object ICollection.SyncRoot
        {
            get { return ((ICollection) m_dict).SyncRoot; }
        }

        bool ICollection.IsSynchronized
        {
            get { return false; }
        }

        #endregion

        #region Get Enumerator

#if OPTIMIZE_COLLECTION
        public TKDictionary_KeyCollection<TKey, int>.Enumerator GetEnumerator()
        {
            return m_dict.m_indexMap.Keys.GetEnumerator();
        }
#else
        public Dictionary<TKey, int>.KeyCollection.Enumerator GetEnumerator()
        {
            return m_dict.m_indexMap.Keys.GetEnumerator();
        }
#endif

        IEnumerator<TKey> IEnumerable<TKey>.GetEnumerator()
        {
            return m_dict.m_indexMap.Keys.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return ((IEnumerable) m_dict.m_indexMap.Keys).GetEnumerator();
        }

        #endregion

        #region Not Implement

        public void Add(TKey item)
        {
            throw new NotSupportedException("Mutating a key collection derived from a ConfigHashMap is not allowed.");
        }

        public void Clear()
        {
            throw new NotSupportedException("Mutating a key collection derived from a ConfigHashMap is not allowed.");
        }

        public bool Remove(TKey item)
        {
            throw new NotSupportedException("Mutating a key collection derived from a ConfigHashMap is not allowed.");
        }

        #endregion

        #region Implement

        public bool Contains(TKey item)
        {
            return m_dict.ContainsKey(item);
        }

        public void CopyTo(TKey[] array, int index)
        {
            m_dict.m_indexMap.Keys.CopyTo(array, index);
        }

        void ICollection.CopyTo(Array array, int index)
        {
            ((ICollection) m_dict.m_indexMap.Keys).CopyTo(array, index);
        }

        #endregion
    }

    public sealed class ConfigHashMap_ValueCollection<TKey, TValue> : ICollection<TValue>, ICollection, IReadOnlyCollection<TValue>
    {
        private ConfigHashMap<TKey, TValue> m_dict;
        private Func<int, FastBinaryReader, TValue> m_readValueFunc;

        #region Init

        public ConfigHashMap_ValueCollection(ConfigHashMap<TKey, TValue> dictionary)
        {
            m_dict = dictionary;
            m_readValueFunc = m_dict.GetValue;
        }

        #endregion

        #region Property

        public int Count
        {
            get { return m_dict.Count; }
        }

        bool ICollection<TValue>.IsReadOnly
        {
            get { return true; }
        }

        bool ICollection.IsSynchronized
        {
            get { return false; }
        }

        object ICollection.SyncRoot
        {
            get { return ((ICollection) m_dict).SyncRoot; }
        }

        #endregion

        #region Get Enumerator

        public Enumerator GetEnumerator()
        {
            return new Enumerator(m_dict);
        }

        IEnumerator<TValue> IEnumerable<TValue>.GetEnumerator()
        {
            return new Enumerator(m_dict);
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return new Enumerator(m_dict);
        }

        #endregion

        #region Not Implement

        public void Add(TValue item)
        {
            throw new NotSupportedException("Mutating a value collection derived from a ConfigHashMap is not allowed.");
        }

        public void Clear()
        {
            throw new NotSupportedException("Mutating a value collection derived from a ConfigHashMap is not allowed.");
        }

        public bool Remove(TValue item)
        {
            throw new NotSupportedException("Mutating a value collection derived from a ConfigHashMap is not allowed.");
        }

        #endregion

        #region Implement

        public bool Contains(TValue item)
        {
            return m_dict.ContainsValue(item);
        }

        public void CopyTo(TValue[] array, int index)
        {
            if (array == null)
                throw new ArgumentNullException(nameof(array));
            if (index < 0 || index > array.Length)
                throw new ArgumentOutOfRangeException(nameof(index), (object) index,
                    "Index was out of range. Must be non-negative and less than the size of the collection.");
            if (array.Length - index < m_dict.Count)
                throw new ArgumentException(
                    "Destination array is not long enough to copy all the items in the collection. Check array index and length.");

            FastBinaryReader br = m_dict.GetReader();
            foreach (int valueIndex in m_dict.m_indexMap.Values)
            {
                TValue value = m_readValueFunc(valueIndex, br);
                array[index] = value;
                index++;
            }
        }

        void ICollection.CopyTo(Array array, int index)
        {
            if (array == null)
                throw new ArgumentNullException(nameof(array));
            if (index < 0 || index > array.Length)
                throw new ArgumentOutOfRangeException(nameof(index), (object) index,
                    "Index was out of range. Must be non-negative and less than the size of the collection.");
            if (array.Length - index < m_dict.Count)
                throw new ArgumentException(
                    "Destination array is not long enough to copy all the items in the collection. Check array index and length.");

            if (array is TValue[])
            {
                TValue[] valueArray = array as TValue[];
                CopyTo(valueArray, index);
            }
            else
            {
                object[] objArray = array as object[];
                if (objArray == null)
                    throw new ArgumentException(
                        "Target array type is not compatible with the type of items in the collection.", nameof(array));
                try
                {
                    FastBinaryReader br = m_dict.GetReader();
                    foreach (int valueIndex in m_dict.m_indexMap.Values)
                    {
                        TValue value = m_readValueFunc(valueIndex, br);
                        objArray[index] = value;
                        index++;
                    }
                }
                catch (ArrayTypeMismatchException)
                {
                    throw new ArgumentException(
                        "Target array type is not compatible with the type of items in the collection.", nameof(array));
                }
            }
        }

        #endregion

        public struct Enumerator : IEnumerator<TValue>
        {
            private ConfigHashMap<TKey, TValue> m_dict;
            private TValue m_current;
#if OPTIMIZE_COLLECTION
            private TKDictionary_Enumerator<TKey, int> m_enumerator;
#else
            private Dictionary<TKey, int>.Enumerator m_enumerator;
#endif
            private FastBinaryReader m_br;
            private int m_index;

            #region Init

            public Enumerator(ConfigHashMap<TKey, TValue> dictionary)
            {
                m_dict = dictionary;
                m_current = default(TValue);
                m_enumerator = m_dict.m_indexMap.GetEnumerator();
                m_br = m_dict.GetReader();
                m_index = 0;
            }

            #endregion

            #region Iter

            public bool MoveNext()
            {
                if (m_enumerator.MoveNext())
                {
                    KeyValuePair<TKey, int> currentPair = m_enumerator.Current;
                    int index = currentPair.Value;
                    TValue value = m_dict.GetValue(index, m_br);
                    m_current = value;
                    m_index++;
                    return true;
                }
                else
                {
                    m_index = m_dict.Count;
                    m_current = default(TValue);
                    return false;
                }
            }

            void IEnumerator.Reset()
            {
                ((IEnumerator) m_enumerator).Reset();
                m_current = default(TValue);
            }

            public void Dispose()
            {
            }

            #endregion

            #region Current

            public TValue Current
            {
                get { return m_current; }
            }

            object IEnumerator.Current
            {
                get
                {
                    if (m_index == 0 || m_index == m_dict.Count + 1)
                        throw new InvalidOperationException(
                            "Enumeration has either not started or has already finished.");
                    return m_current;
                }
            }

            #endregion
        }
    }

    public static class ConfigHashMap_Util
    {
        #region Init

        public static void Init<TKey, TValue>(this ConfigHashMap<TKey, TValue> map, int capacity)
        {
            if (capacity < 0)
                throw new ArgumentOutOfRangeException(nameof(capacity), capacity, "Non-negative number required.");
            map.mm_indexMap = new TKDictionary<TKey, int>(capacity, EqualityComparer<TKey>.Default);

            if (map.m_reader == null)
                map.m_datas = new List<TValue>(capacity);
        }

        #endregion
        
        #region External Data

        internal static bool TryInsert<TKey, TValue>(this ConfigHashMap<TKey, TValue> map, TKey key, TValue value, bool isOverWrite)
        {
            TKDictionary<int, TValue> datasExternal = map.m_datasExternal;
            if (datasExternal == null)
            {
                datasExternal = new TKDictionary<int, TValue>();
                map.m_datasExternal = datasExternal;
            }
            
            TKDictionary<TKey, int> indexMap = map.m_indexMap;
            if (indexMap.TryGetValue(key, out int index))
            {
                if (isOverWrite)
                {
                    if (index < 0)
                    {
                        datasExternal[index] = value;
                    }
                    else
                    {
                        int externalIndex = map.m_externalId--;
                        datasExternal.Add(externalIndex, value);
                        indexMap[key] = externalIndex;
                    }
                    
                    return true;
                }
                else
                {
                    throw new ArgumentException(string.Format("An item with the same key has already been added. Key: {0}", key));
                }
            }
            
            int dataExternalIndex = map.m_externalId--;
            datasExternal.Add(dataExternalIndex, value);
            indexMap.Add(key, dataExternalIndex);

            return true;
        }
        
        private static bool PointToSameFile<TKey, TValue>(this ConfigHashMap<TKey, TValue> map, ConfigHashMap<TKey, TValue> other)
        {
            return map.m_reader == other.m_reader;
        }

        public static bool InsertRange<TKey, TValue>(this ConfigHashMap<TKey, TValue> map, ConfigHashMap<TKey, TValue> other)
        {
            if (map.m_isOriginData)
                throw new NotSupportedException("Add Key Value in a original ConfigHashMap is not allowed.");
            
            if (PointToSameFile(map, other))
            {
                if (map.m_reader != null)
                {
                    TKDictionary<TKey, int> indexMap = map.m_indexMap;
                    foreach (var pair in other.m_indexMap)
                    {
                        TKey key = pair.Key;
                        int valueIndex = pair.Value;
                    
                        if (valueIndex < 0)
                        {
                            TValue value = other.m_datasExternal[valueIndex];
                            map.TryInsert(key, value, true);
                        }
                        else
                        {
                            indexMap[key] = valueIndex;
                        }
                    }
                }
                else
                {
                    foreach (var pair in other)
                    {
                        map.TryInsert(pair.Key, pair.Value, true);
                    }
                }
                
                return true;
            }
            else
            {
                Diagnostic.Error("Two ConfigHashMaps Have Different File Handlers, Combine Failed");
            }

            return false;
        }

        public static bool InsertRange<TKey, TValue>(this ConfigHashMap<TKey, TValue> map, TKDictionary<TKey, TValue> other)
        {
            if (map.m_isOriginData)
                throw new NotSupportedException("Add Key Value in a original ConfigHashMap is not allowed.");

            foreach (var pair in other)
            {
                map.TryInsert(pair.Key, pair.Value, true);
            }
            
            return true;
        }
        
        #endregion

        #region Index Data

        public static void SetReadOffset<TKey, TValue>(this ConfigHashMap<TKey, TValue> map, uint dataOffset)
        {
            map.m_readOffset = dataOffset;
        }

        public static void AddDataIndex<TKey, TValue>(this ConfigHashMap<TKey, TValue> map,
            KeyValuePair<TKey, int> data, ConfigHashMap<TKey, TValue> other = null)
        {
            TKey key = data.Key;
            int index = data.Value;

            if (other != null && map.m_reader == null)
            {
                TValue value = other.m_datas[index];
                index = map.m_datas.Count;
                map.m_datas.Add(value);
            }

            map.mm_indexMap.Add(key, index);
        }

        public static void AddKeyValue<TKey, TValue>(this ConfigHashMap<TKey, TValue> map, TKey key, TValue value)
        {
            if (map.m_reader == null)
            {
                int index = map.m_datas.Count;
                map.m_datas.Add(value);
                map.m_indexMap.Add(key, index);
            }
        }

        #endregion

        #region Read Data

        public static void SetValueRead<TKey, TValue>(this ConfigHashMap<TKey, TValue> map, TLSStreamReader reader,
            Func<int, FastBinaryReader, TValue> valueFunc)
        {
            map.m_reader = reader;
            map.m_readValueFunc = valueFunc;
        }

        public static void SetValueRead<TKey, TValue>(this ConfigHashMap<TKey, TValue> map, TLSStreamReader reader,
            Func<int, FastBinaryReader, TValue> valueFunc,
            Action<ConfigHashMap<TKey, TValue>, FastBinaryReader> keyFunc)
        {
            map.m_reader = reader;
            map.m_readValueFunc = valueFunc;
            map.m_readKeyFunc = keyFunc;
        }

        internal static FastBinaryReader GetReader<TKey, TValue>(this ConfigHashMap<TKey, TValue> map)
        {
            if (map.m_reader == null)
                return null;
            return map.m_reader.GetReader();
        }

        internal static void InitIndexMap<TKey, TValue>(this ConfigHashMap<TKey, TValue> map)
        {
            if (map.m_reader == null)
                return;

            lock (map)
            {
                if (map.mm_indexMap == null)
                {
                    FastBinaryReader reader = map.GetReader();
                    if (map.m_readKeyFunc != null && reader != null)
                    {
                        reader.BaseStream.Position = map.m_readOffset;
                        map.m_readKeyFunc(map, reader);
                    }
                }
            }

            // 0 表示 没加载
            // 1 表示 加载中
            // 2 表示 加载完成

            /*
            if (m_isInitKey == 2)
                return;

            if (Interlocked.CompareExchange(ref m_isInitKey, 2, 2) == 2)
                return;

            if (Interlocked.CompareExchange(ref m_isInitKey, 1, 0) == 1)
            {
                while (Interlocked.CompareExchange(ref m_isInitKey, 1, 1) == 1)
                {
                    Thread.Sleep(1);
                }
            }
            else
            {
                FastBinaryReader reader = GetReader();
                if (m_readKeyFunc != null && reader != null)
                {
                    reader.BaseStream.Position = m_readOffset;
                    m_readKeyFunc(this, reader);
                }
                Interlocked.CompareExchange(ref m_isInitKey, 2, 1);
            }
            */
        }

        internal static TValue GetValue<TKey, TValue>(this ConfigHashMap<TKey, TValue> map, int index,
            FastBinaryReader br)
        {
            TValue result = default(TValue);
            if (index >= 0)
            {
                if (map.m_readValueFunc != null && br != null)
                {
                    result = map.m_readValueFunc(index, br);
                }
                else
                {
                    result = map.m_datas[index];
                }
            }
            else
            {
                result = map.m_datasExternal[index];
            }

            return result;
        }

        #endregion

        #region Contain

        public static bool ContainsValue<TKey, TValue>(this ConfigHashMap<TKey, TValue> map, TValue value)
        {
            FastBinaryReader br = map.GetReader();
            EqualityComparer<TValue> comparer = EqualityComparer<TValue>.Default;
            foreach (int index in map.m_indexMap.Values)
            {
                TValue tmp = map.GetValue(index, br);
                if (comparer.Equals(tmp, value))
                    return true;
            }

            return false;
        }

        #endregion

        #region CopyTo

        public static void CopyTo<TKey, TValue>(this ConfigHashMap<TKey, TValue> map,
            KeyValuePair<TKey, TValue>[] array, int index)
        {
            if (array == null)
                throw new ArgumentNullException(nameof(array));
            if (index < 0 || index > array.Length)
                throw new ArgumentOutOfRangeException(nameof(index), (object) index,
                    "Index was out of range. Must be non-negative and less than the size of the collection.");
            if (array.Length - index < map.Count)
                throw new ArgumentException(
                    "Destination array is not long enough to copy all the items in the collection. Check array index and length.");

            FastBinaryReader br = map.GetReader();
            foreach (KeyValuePair<TKey, int> pair in map.m_indexMap)
            {
                TKey key = pair.Key;
                TValue value = map.GetValue(pair.Value, br);
                array[index] = new KeyValuePair<TKey, TValue>(key, value);
                index++;
            }
        }

        #endregion
    }
}