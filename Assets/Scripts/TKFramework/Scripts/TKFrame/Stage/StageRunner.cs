using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace TKFrame
{
    public class StageSwitchInfo
    {
        public class PopInfo
        {
            public string assetBundle;
            public string resName;
            public string id;
            public object data;
        }

        public Type stageType;
        public string stageTypeName;
        public string sceneName;
        public bool needloadScene;
        public Action<IStage> addStageCallback;

        public bool IsValid
        {
            get { return !string.IsNullOrEmpty(this.sceneName); }
        }

        public void Clear()
        {
            this.sceneName = null;
            this.needloadScene = false;
            this.addStageCallback = null;
        }
    }
    /// <summary>
    /// Game Stage 的创建及切换。
    /// </summary>
    public class StageRunner :ServiceBehaviour,  IStageRunner
    {
        private IStage m_currentStage;
		//当前场景
		public IStage CurrentStage
        {
            get { return m_currentStage;}
            protected set { m_currentStage = value; }
        }
        //目前切换的stage
        private string TargetStage;
        
        //下一个Stage 开始加载回调
        public Action<string> StageBegunLoadCallback;

        //离开当前stage，下一个stage资源还没有加载完成就会回调
        public Action<IStage> StageLeaveCallBack;

        //离开当前stage，下一个stage资源已经加载完毕并且显示后回调
        public Action<IStage> StageDisposedCallBack;

        //下一个Stage 初始化的时候的回调
        public Action<IStage> StageInitCallback;

        //进入新的stage之前，新stage资源已经加载好
        public Action<IStage> StagePreEnterCallBack;
        //进入新的stage，新stage资源已经加载好
        public Action<IStage> StageEnterCallBack;

        public string CurrentStageName
        {
            get;
            protected set;
        }

        public string destStageName;

        #region stage_info
        private EventHandler<CreateStageProgressChangeEventArgs> _StageChangeProgressHandler;
        public event EventHandler<CreateStageProgressChangeEventArgs> CreateStageProgressChange
        {
            add 
            {
                _StageChangeProgressHandler += value;
            }

            remove
            {
                _StageChangeProgressHandler -= value;
            }
        }

        private EventHandler<StageChangeEventArgs> _StageChangeHandler;
        public event EventHandler<StageChangeEventArgs> StageChange
        {
            add
            {
                _StageChangeHandler += value;
            }

            remove
            {
                _StageChangeHandler -= value;
            }
        }
        #endregion

        /// <summary>
        /// Stage 游戏对象预制件模版。
        /// </summary>
        private GameObject _stageGameObject;

        //过渡界面管理
        private ITransition _transition = null;
        public ITransition Transition
        {
            get { return _transition; }
            set { _transition = value; }
        }

        public bool IsLoding
        {
           get { return _isLoading; }
        }
        //标明当前是否正在加载stage
        private bool _isLoading = false;

        //用于加入到stage切换历史记录;
        private StageSwitchInfo m_StageSwitchInfo = new StageSwitchInfo();
        
        public Func<Type,string,bool> StageOpenCheck;

        //通知加载状态
        protected override void Awake()
        {
            base.Awake();
        }
        
        public void NotifyStageStep(STAGE_STEP step, bool bLoading)
        {
            if (Transition != null && bLoading)
            {
                Transition.NotifyStageStep(step);
            }
        }
        
        public void NotifyProgress(int p)
        {
            if (Transition != null)
            {
                Transition.NotifyStageProgress(p);
            }
        }
        
        public void NotifyStageProgress(int p)
        {
            if (Transition != null)
            {
                Transition.NotifyStageProgress(p);
            }
        }

        public void ClearStageSwitchInfo()
        {
            this.m_StageSwitchInfo.Clear();
        }

        protected override IEnumerator BindService()
        {
            Services.AddService<IStageRunner>(this);

            //加载Stage Main GameObject 模版
            ResourceRequest resReq = Resources.LoadAsync<GameObject>("Prefabs/StageObject");
            //yield return resReq;
            while (!resReq.isDone) yield return null;
            _stageGameObject = resReq.asset as GameObject;
        }

        /// <summary>
        /// 释放原有Stage，创建新的Stage 并加载场景资源，启动新的Stage
        /// </summary>
        /// <param name="stageType"> Stage类型 </param>
        /// <param name="sceneName"> Stage 使用的Unity 场景 </param>
        public void ShowStage(Type stageType, string sceneName, bool needloadScene, Action<IStage> addStageCallBack = null, bool isGotoPrevious = false, bool bNeedLoading = false, EventHandler<CoroutineExceptionEventArgs> exceptionHandler = null,  Action LastStageReleaseCallBack = null, string tkluaStageName = "", bool bReserveGI = false)
        {
            if (StageOpenCheck!=null && !StageOpenCheck(stageType,tkluaStageName))
            {
                Diagnostic.Log("stage close ! " + stageType+" , lua ="+tkluaStageName);
                return;
            }
            destStageName = sceneName;
            NotifyStageStep(STAGE_STEP.STAGE_STEP_INIT, bNeedLoading);
            UnityCoroutine.StartCoroutine(this, LoadStage(stageType, sceneName, needloadScene, addStageCallBack, isGotoPrevious, bNeedLoading, LastStageReleaseCallBack, tkluaStageName, bReserveGI), exceptionHandler);
        }


        public void ReleaseGameStage()
        {
            CurrentStage.Release();
        }

		public void NotifyBegunLoadStage(string stageTypeName)
		{

			if (StageBegunLoadCallback != null)
			{
				StageBegunLoadCallback(stageTypeName);
			}
			 
		}

        public void NotifyInitStage(IStage stage)
        {
            if (StageInitCallback != null)
            {
                StageInitCallback(stage);
            }
        }

        //通知进入界面
        public void NotifyEnterStage(IStage stage)
        {
            if (stage != null)
            {
                if (StagePreEnterCallBack != null)
                {
                    StagePreEnterCallBack(stage);
                }
                stage.Enter();
                if (StageEnterCallBack != null)
                {
                    StageEnterCallBack(stage);
                }
            }
        }

        //通知离开界面
        public void NotifyLeaveStage(IStage stage)
        {
            //屏蔽切换之前的界面的点击事件
            if (stage != null)
            {
                stage.Leave();
                if (StageLeaveCallBack != null)
                {
                    StageLeaveCallBack(stage);
                }
                BaseStage bs = stage as BaseStage;
                if (bs!=null)
                {
                    if (!m_CacheStage.ContainsKey(bs.name))
                    {
                        bs.setEventEnable(false);
                        bs.UnRegisterFromObjectRegistor();
                    }
                }
            }
        }

        public GameObject CreateStageObject()
        {
            GameObject stageMainGo = GameObject.Instantiate(_stageGameObject);
            return stageMainGo;
        }

        public TKFrame.STAGE_STEP currentStageStep;
        private bool m_bLoading = false;

        IEnumerator LoadStage(Type stageType, string sceneName, bool needloadScene, Action<IStage> addStageCallBack=null, bool isGotoPrevious = false, bool bLoading = false,  Action LastStageReleaseCallBack = null, string luaStageName = "", bool bReserveGI = false)
        {
            string stageTypeName = TKFrameworkDelegateInterface.GetStageTypeName != null ? TKFrameworkDelegateInterface.GetStageTypeName(stageType, luaStageName) : stageType.Name;
            if (TargetStage == stageTypeName) 
            {
                yield return null;
                yield break;
            }

            TargetStage = stageTypeName;

            m_bLoading = bLoading;
            LightProfiler.RuntimeProfiler.AddPoint("开始载入场景");
            Diagnostic.Log("[StageRunner.LoadStage.checkLoading] stageType:{0} sceneName:{1} needloadScene:{2} time:{3}", stageTypeName, sceneName, needloadScene, Time.realtimeSinceStartup);
            while (_isLoading)
            {
                yield return null;
            }
            Diagnostic.Log("[StageRunner.LoadStage._isLoading = true] stageType:{0} sceneName:{1} needloadScene:{2} time:{3}", stageTypeName, sceneName, needloadScene, Time.realtimeSinceStartup);
            _isLoading = true;

            while (Transition != null && bLoading && Transition.IsPrepared == false)
            {
                //等待过度界面已经准备完毕（主要是过度界面的资源加载相关）
                yield return null;
            }
           
            EventManager eventMgr = Services.GetService<IEventService>() as EventManager;
            if (eventMgr != null)
            {
                eventMgr.Notify(new Event("Bind_Transition_Notify", null));
            }
            Diagnostic.Log("[StageRunner.LoadStage.finish Transition] stageType:{0} sceneName:{1} needloadScene:{2} time:{3}", stageTypeName, sceneName, needloadScene, Time.realtimeSinceStartup);

            NotifyLeaveStage(CurrentStage);
            IStage lastStage = CurrentStage;
            //needloadScene标志位用于表征是否需要loadlevel,以下是先删除上个stage,并loadlevel

            string lastStageName = string.Empty;

            NotifyBegunLoadStage(stageTypeName);
            if (needloadScene)
            {
                currentStageStep = STAGE_STEP.STAGE_STEP_START;
                NotifyStageStep(currentStageStep, bLoading);
                if (Transition != null && Transition.ShowAnimTime > 0.0f)
                {
					yield return Coroutine.WaitForSeconds (Transition.ShowAnimTime);
				}
                if (CurrentStage != null)
                {
                    //切换Scene会直接Destroy，导致下面的stage方法不会执行，在这里需要执行一次
                    lastStage.Disposed();
                    if (StageDisposedCallBack != null)
                        StageDisposedCallBack(lastStage);

                    CurrentStage.Release();
                    BaseStage bs = CurrentStage as BaseStage;
                    lastStageName = bs.name;
                    CurrentStage = null;
                    TKFrameworkDelegateInterface.ClearStageCache(m_CacheStage);
                    AddCache();
                    GameObject.DestroyImmediate(bs);
                    if (LastStageReleaseCallBack != null)
                    {
                        LastStageReleaseCallBack();
                    }
					//bs = null;
                }
                yield return null;
                Diagnostic.Log("load Stage begun load unit scene...");
                currentStageStep = STAGE_STEP.STAGE_STEP_LOAD_SCENE;
                NotifyStageStep(currentStageStep, bLoading);
                yield return LoadStageScene(sceneName, bReserveGI);                         
            }

            if (TKFrameworkDelegateInterface.SaveLastStagePopInfo != null)
                TKFrameworkDelegateInterface.SaveLastStagePopInfo(CurrentStageName, lastStage, needloadScene);
            yield return CreateNewStage(sceneName, stageType, bLoading, luaStageName);

            TargetStage = string.Empty;

            //如果上个stage存在，则销毁上个stage
            yield return DestroyLastStage(lastStage);
            //lastStage = null;

            if (CurrentStage != null)
            {
                if (needloadScene)
                    yield return DelayRequestUnload();

                /*
                var unloadDelay = CurrentStage.UnloadDelay;
                if (unloadDelay >= 0)
                {
                    UnityCoroutine.StartCoroutine(this, DelayRequestUnload(unloadDelay));
                }
                */
                //恢复当前界面的点击事件
                CurrentStage.setEventEnable(true);
            }
            currentStageStep = STAGE_STEP.STAGE_STEP_END;
            NotifyStageStep(currentStageStep, bLoading);

            NotifyEnterStage(CurrentStage);

            //通知到历史记录列表中;
            bool needRecordToSwitchHistory = (CurrentStage as BaseStage).NeedSaveToSwitchHistory;
            if (this.m_StageSwitchInfo.IsValid && needRecordToSwitchHistory && !isGotoPrevious)
            {
                if (TKFrameworkDelegateInterface.EventType_UI_STAGE_SWITCH_RECORD != null)
                    ACGEventManager.Instance.Send(TKFrameworkDelegateInterface.EventType_UI_STAGE_SWITCH_RECORD(), this.m_StageSwitchInfo);   
            }
            if (TKFrameworkDelegateInterface.EventType_UI_CURRENT_STAGE_CHANGED != null)
                ACGEventManager.Instance.Send(TKFrameworkDelegateInterface.EventType_UI_CURRENT_STAGE_CHANGED(), sceneName);
            if (needRecordToSwitchHistory)
            {
                //记录打开的信息, 供下次使用;
                this.m_StageSwitchInfo.stageType = stageType;
                this.m_StageSwitchInfo.stageTypeName = stageTypeName;
                this.m_StageSwitchInfo.sceneName = sceneName;
                this.m_StageSwitchInfo.addStageCallback = addStageCallBack;   
            }

            if (addStageCallBack != null)
            {
                addStageCallBack(CurrentStage);
            }

            _isLoading = false;
            destStageName = string.Empty;

            LightProfiler.RuntimeProfiler.AddPoint("结束载入场景");
        }

        public IEnumerator CreateNewStage(string sceneName, Type stageType, bool bLoading, string luaStageName)
        {
            BaseStage stageGo = null;
            string stageName = "[Stage] " + sceneName;
            bool needCache = ((TKFrameworkDelegateInterface.IsOpenStageCache != null && TKFrameworkDelegateInterface.IsOpenStageCache()) || IsNeedCacheMainStage(sceneName)) && m_CacheStage.TryGetValue(stageName, out stageGo);
            if (needCache && stageGo != null)
            {
                CurrentStage = stageGo;
                CurrentStageName = sceneName;
                currentStageStep = STAGE_STEP.STAGE_STEP_LOAD_CONTENT;
                NotifyStageStep(currentStageStep, bLoading);
                NotifyInitStage(CurrentStage);
                stageGo.gameObject.SetActive(true);
                stageGo.ResetStageNode();
                //yield return TKFrame.CoroutineWait.waitForEndFrame;
                //yield return null;
            }
            else
            {
                GameObject stageMainGo = GameObject.Instantiate(_stageGameObject);
                stageMainGo.name = stageName;
                BaseStage newStage = null;
                if (string.IsNullOrEmpty(luaStageName))
                {
                    newStage = stageMainGo.AddComponent(stageType) as BaseStage;
                }
                else
                {
                    //TKLua翻译
                    newStage = TKFrameworkDelegateInterface.AddLuaStage(stageMainGo, luaStageName);
                    //newStage = stageMainGo.AddComponent(typeof(TKLua.BaseStageProxy)) as BaseStage;
                    //LuaBehaviourClassCache classCahe = TKLuaManager.instance.LoadLuaBehaviourClass(luaStageName);
                    //if (classCahe != null)
                    //{
                    //    XLua.LuaTable luaStageIns = classCahe.New();
                    //    LuaFunctionEx initScript = classCahe.GetFunByName("InitScript");
                    //    initScript.BeginPCall();
                    //    if (luaStageIns != null)
                    //    {
                    //        initScript.Push(luaStageIns);
                    //    }
                    //    initScript.Push(newStage);
                    //    initScript.Push(luaStageName);
                    //    initScript.PCall();
                    //    initScript.EndPCall();
                    //}
                }
                CurrentStage = newStage;
                CurrentStageName = sceneName;

                //<TKLua> 绑定Lua层场景和stage实例
                newStage.BindLuaType(sceneName);
                //</TKLua>

                //通过canvasgroup隐藏新的stage
                BaseStage.HideByCanvasGroup((CurrentStage as BaseStage).gameObject);

                //屏蔽切换之后的界面的点击事件
                if (CurrentStage != null)
                {
                    CurrentStage.setEventEnable(false);
                }

                currentStageStep = STAGE_STEP.STAGE_STEP_LOAD_CONTENT;
                NotifyStageStep(currentStageStep, bLoading);
                NotifyInitStage(CurrentStage);
                newStage.ProgerssAction = NotifyStageProgress;
                yield return newStage.LoadStage();
                newStage.ProgerssAction = null;

                Diagnostic.Log("BaseStage LoadStage finish");

                ///显示当前stage
                BaseStage.ShowByCanvasGroup((CurrentStage as BaseStage).gameObject);

                if (needCache)
                {
                    m_CacheStage[stageName] = newStage;
                }
            }
        }

        public void ClearCurrentStage()
        {
            BaseStage bs = CurrentStage as BaseStage;
            CurrentStage = null;
            GameObject.DestroyImmediate(bs);
        }

        public void ExceptionExit()
        {
            if(CurrentStage != null)
            {
                CurrentStage.setEventEnable(true);
            }
            NotifyStageStep(STAGE_STEP.STAGE_STEP_END, m_bLoading);
            Transition.Hide();
            _isLoading = false;
        }

        /// <summary>
        /// 加载Unity Scene 资源，等待完成
        /// </summary>
        /// <param name="sceneName"></param>
        /// <param name="bReserveGI">是否保留GI贴图</param>
        /// <returns></returns>
        IEnumerator LoadStageScene(string sceneName, bool bReserveGI = false)
        {
            ISceneLoad sceneLoader = Services.GetService<ISceneLoad>();
            if (sceneName != "LoginStage")
            {
                sceneLoader.LoadScene(sceneName, false, bReserveGI);

                Diagnostic.Log($"[StageRunner.LoadStageScene] Scene:{sceneName} load begin! {Time.realtimeSinceStartup}");

                while (sceneLoader.Loading)
                {
                    yield return null;
                }
                Diagnostic.Log($"[StageRunner.LoadStageScene]Scene:{sceneName} load end! {Time.realtimeSinceStartup}");
            }
        }

        public void UnloadUnusedAsset()
        {
            TKFrameworkDelegateInterface.QQGameSystem_StartCoroutine(DelayRequestUnload());
        }

        private IEnumerator DelayRequestUnload()
        {
            IAssetService assetService = Services.GetService<IAssetService>();
            while (assetService.Loading)
                yield return null;
            if (CurrentStage != null && 
                (TKFrameworkDelegateInterface.CurrentStageIsChessBattleStage() || TKFrameworkDelegateInterface.CurrentStageIsChessMainHallStage3D()) &&
                !TKFrameworkDelegateInterface.LuaEnvIsNull())
            {
                Diagnostic.Log("XLua FullGC");
                TKFrameworkDelegateInterface.CSLClearListener();
                TKFrameworkDelegateInterface.LuaFullGC();
            }
            yield return null;
            GC.Collect();
            yield return null;
            assetService.RequestUnloadUnityUnusedAsset();
            while (assetService.AssetUnloading)
            {
                yield return null;
            }
        }

        IEnumerator DelayRequestUnload(float delay)
		{
			yield return TKFrame.CoroutineWait.GetWaitForSeconds (delay);
            SystemManager.getInstance().PostExpensiveTask(this, "StageRunner.DelayRequestUnload", () => {
                GC.Collect();
                IAssetService assetService = Services.GetService<IAssetService>();
                assetService.RequestUnloadUnityUnusedAsset();
            });
           
		}

        ///销毁上一个场景的StageObject
        public IEnumerator DestroyLastStage(IStage lastStage)
        {
            BaseStage _lastStage = lastStage as BaseStage;
            if (_lastStage != null)
            {
                lastStage.Disposed();
                if (StageDisposedCallBack != null)
                {
                    StageDisposedCallBack(lastStage);
                }
                if ((TKFrameworkDelegateInterface.IsOpenStageCache() || IsNeedCacheMainStage(_lastStage.name)) && m_CacheStage.ContainsKey(_lastStage.name))
                {
                    string stageName = _lastStage.name;
                    if (stageName.Contains("[Stage] "))
                        stageName = stageName.Substring(8);
                    _lastStage.gameObject.SetActive(false);
                }
                else
                {
                    _lastStage.Release();
                    BaseStage.HideByCanvasGroup(_lastStage.gameObject);
                    yield return null;
                    GameObject.DestroyImmediate(_lastStage.gameObject);
                }
                ///位置是否合适呢
                //释放已经没有引用的asset
                // 不再使用Unity 自身释放
                //  Resources.UnloadUnusedAssets();
            }
        }

        /// <summary>
        /// 添加接口用于编辑器
        /// </summary>
        /// <param name="curStage"></param>
        public void SetCurStage(IStage curStage)
        {
            CurrentStage = curStage;
        }

        #region 界面缓存
        private bool m_IsNeedCacheMainStage = true;//是否缓存主界面 保证低端机不卡顿
        private TKDictionary<string, BaseStage> m_CacheStage = new TKDictionary<string, BaseStage>();
        /// <summary>
        /// 添加需要Cache的场景，仅支持没有跳unity场景的场景
        /// </summary>
        /// <param name="scene"></param>
        private void AddCacheStage(string sceneName)
        {
            string stageName = "[Stage] " + sceneName;
            if (!m_CacheStage.ContainsKey(stageName))
                m_CacheStage.Add(stageName, null);
        }
        public void AddCache()
        {
            m_CacheStage.Clear();
            if (TKFrameworkDelegateInterface.IsOpenStageCache())
            {
                List<string> cacheCfg = TKFrameworkDelegateInterface.GetStageCacheCfg();
                if (cacheCfg != null)
                {
                    for (int i = 0; i < cacheCfg.Count; i++)
                    {
                        AddCacheStage(cacheCfg[i]);
                    }
                }
            }
            else if (m_IsNeedCacheMainStage)
            {
                AddCacheStage("Lobby_science_02");
            }
        }
        public bool IsNeedCacheMainStage(string sceneName)
        {
            if (!m_IsNeedCacheMainStage)
                return false;
            if (sceneName.Contains("Lobby_science_02"))
                return true;
            return false;
        }
        #endregion
    }
}
