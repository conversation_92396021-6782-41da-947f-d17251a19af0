//#define UNITY_IOS
#define NO_AB_CACHE
using System;
using UnityEngine;
#if UNITY_EDITOR	
using UnityEditor;
#endif
using System.Collections;
using System.Collections.Generic;
using TKFrame;
using TKPlugins;
using TKFrame.Item;
using System.IO;
using GameUpdate;
using Object = UnityEngine.Object;

namespace TKFrame
{

    /*
        In this demo, we demonstrate:
        1.	Automatic asset bundle dependency resolving & loading.
            It shows how to use the manifest assetbundle like how to get the dependencies etc.
        2.	Automatic unloading of asset bundles (When an asset bundle or a dependency thereof is no longer needed, the asset bundle is unloaded)
        3.	Editor simulation. A bool defines if we load asset bundles from the project or are actually using asset bundles(doesn't work with assetbundle variants for now.)
            With this, you can player in editor mode without actually building the assetBundles.
        4.	Optional setup where to download all asset bundles
        5.	Build pipeline build postprocessor, integration so that building a player builds the asset bundles and puts them into the player data (Default implmenetation for loading assetbundles from disk on any platform)
        6.	Use WWW.LoadFromCacheOrDownload and feed 128 bit hash to it when downloading via web
            You can get the hash from the manifest assetbundle.
        7.	AssetBundle variants. A prioritized list of variants that should be used if the asset bundle with that variant exists, first variant in the list is the most preferred etc.
    */

    // Loaded assetBundle contains the references count which can be used to unload dependent assetBundles automatically.
    public class LoadedAssetBundle
    {
        public AssetBundle m_AssetBundle;
        public int m_ReferencedCount;
        public bool m_isUnloadable;

        public LoadedAssetBundle(AssetBundle assetBundle)
        {
            m_AssetBundle = assetBundle;
            m_ReferencedCount = 1;
            m_isUnloadable = AssetBundleDependenciesManager.Instance.GetUnloadable(m_AssetBundle.name);
        }
    }

    // Class takes care of loading assetBundle and its dependencies automatically, loading variants automatically.
    public class AssetBundleManager : TKBehaviour
    {
        //public static readonly string EXTERNAL_FOLDER = Application.persistentDataPath + "/AssetBundles/";/

        // the following property is used to replace the following initialize code
        //public static readonly string EXTERNAL_FOLDER = GameEnvironment.AssetRootPath + TKFrameConfig.Instance.AssetBundlesFolder() + "/";
        // Considering the fact that Application.dataPath could not be called during class initialization or construction code
        // I change it to the following and do remember dont use it at init or constructor.
        // Next time I see this piece of shit, I will bring a gun to work.
        private static string externalFolder;
        public static string EXTERNAL_FOLDER
        {
            get
            {
                if (string.IsNullOrEmpty(externalFolder))
                {
                    externalFolder = GameEnvironment.AssetRootPath + "VersionUpdate/" + TKFrameConfig.Instance.AssetBundlesFolder() + "/";
                }
                return externalFolder;
            }
            private set { externalFolder = value; }
        }
        static string m_BaseDownloadingURL = "";
        static string[] m_Variants = { };
        private static string[] m_emptyDependency = new string[0];
        public static TKDictionary<string, AssetBundleMapData> m_AssetBundleMap = null;

#if UNITY_EDITOR
        static int m_SimulateAssetBundleInEditor = -1;
        const string kSimulateAssetBundles = "SimulateAssetBundles";

        private static int m_IgnoreTypeTree = -1;
        const string kIgnoreTypeTree = "IgnoreTypeTree";
#endif
        
        public static event System.Action<string> NotifyMissingBundle;
        public static System.Action NotifyRepairHandler;
        private static int _bundleMissingTimes = 0;

        static TKDictionary<string, LoadedAssetBundle> m_LoadedAssetBundles = new TKDictionary<string, LoadedAssetBundle>();

#if UNITY_IOS || NO_AB_CACHE
		static TKDictionary<string, AssetBundleCreateRequest> m_DownloadingWWWs = new TKDictionary<string, AssetBundleCreateRequest>();
#else
		static TKDictionary<string, WWW> m_DownloadingWWWs = new TKDictionary<string, WWW>();
#endif
        
        static TKDictionary<string, int> m_DownloadingWWWsReqCount = new TKDictionary<string, int>();

        static TKDictionary<string, string> m_DownloadingErrors = new TKDictionary<string, string>();
        static LinkedList<AssetBundleLoadOperation> m_InProgressOperations = new LinkedList<AssetBundleLoadOperation>();

        static TKDictionary<string, string> m_OverrideAssetbundles = new TKDictionary<string, string>();

        static List<string> m_keysToRemove = new List<string>();
        static List<string> m_keysToRetry = new List<string>();
        static private int _retryTTest = 0;

        //构建并使用HD和etc贴图的开关
        public static bool HDTexture_Open = false;

        public static bool Unload_Ab = true;

#if ENABLE_ASSET_INFO
        public static bool isPrintAssetInfo = false;
#endif

        // The base downloading url which is used to generate the full downloading url with the assetBundle names.
        public static string BaseDownloadingURL
        {
            get { return m_BaseDownloadingURL; }
            set { m_BaseDownloadingURL = value; }
        }

        // Variants which is used to define the active variants.
        public static string[] Variants
        {
            get { return m_Variants; }
            set { m_Variants = value; }
        }

        public static bool isLoadingAB
        {
            get
            {
                return m_InProgressOperations.Count > 0;
            }
        }
        
#if UNITY_EDITOR
        // Flag to indicate if we want to simulate assetBundles in Editor without building them actually.
        public static bool SimulateAssetBundleInEditor
        {
            get
            {
                if (TKFrameConfig.Instance.ForceLoadfromAssetBundlesInEditor())
                {
                    return false;
                }

                if (m_SimulateAssetBundleInEditor == -1)
                {
                    m_SimulateAssetBundleInEditor = File.Exists("RealABModel") ? 0 : 1;
                }
                return m_SimulateAssetBundleInEditor != 0;
            }
            set
            {
                int newValue = value ? 1 : 0;
                if (newValue != m_SimulateAssetBundleInEditor)
                {
                    m_SimulateAssetBundleInEditor = newValue;
                }
            }
        }
#endif

        // Get loaded AssetBundle, only return vaild object when all the dependencies are downloaded successfully.
        static public LoadedAssetBundle GetLoadedAssetBundle(string assetBundleName, out string error)
        {
            if (m_DownloadingErrors.TryGetValue(assetBundleName, out error))
                return null;

            LoadedAssetBundle bundle = null;
            m_LoadedAssetBundles.TryGetValue(assetBundleName, out bundle);
            if (bundle == null)
                return null;

            string[] dependencies = AssetBundleDependenciesManager.Instance.GetAllDependencies(assetBundleName);

            // Make sure all dependencies are loaded
            foreach (string dependency in dependencies)
            {
                if (m_DownloadingErrors.TryGetValue(dependency, out error))
                    return null;

                // Wait all the dependent assetBundles being loaded.
                LoadedAssetBundle dependentBundle;
                m_LoadedAssetBundles.TryGetValue(dependency, out dependentBundle);
                if (dependentBundle == null)
                    return null;
            }

            return bundle;
        }

        static private LoadedAssetBundle GetLoadedAssetBundleInternal(string assetBundleName, out string error)
        {
            if (m_DownloadingErrors.TryGetValue(assetBundleName, out error))
                return null;

            LoadedAssetBundle bundle = null;
            m_LoadedAssetBundles.TryGetValue(assetBundleName, out bundle);
            if (bundle == null)
                return null;

            return bundle;
        }

        // Load AssetBundleManifest.
        static public AssetBundleLoadManifestOperation Initialize(string manifestAssetBundleName)
        {
            var go = new GameObject("AssetBundleManager", typeof(AssetBundleManager));
            DontDestroyOnLoad(go);

#if UNITY_EDITOR
            // If we're in Editor simulation mode, we don't need the manifest assetBundle.
            if (SimulateAssetBundleInEditor)
                return null;
#endif

            LoadAssetBundle(manifestAssetBundleName, true);
            var operation = new AssetBundleLoadManifestOperation(manifestAssetBundleName, "AssetBundleManifest", typeof(AssetBundleManifest), false);
            m_InProgressOperations.AddLast(operation);
            return operation;
        }

        static public void SetOverrideAssetBundle(string assetBundleName, string overrideABPath)
        {
            m_OverrideAssetbundles[assetBundleName] = overrideABPath;
        }

        //load AssetBundleMap, 包含etc和ASTC高清的信息
        static public IEnumerator LoadBundleMap()
        {
            m_AssetBundleMap = null;
#if UNITY_EDITOR
            // If we're in Editor simulation mode, we don't need the BundleMap
            if (SimulateAssetBundleInEditor)
                yield break;
#endif
            Diagnostic.Log("load bundlemap start");
            if (File.Exists(GameEnvironment.BundleMapPath_Ext))
            {
                AssetBundleMap bundleMap = new AssetBundleMap(GameEnvironment.BundleMapPath_Ext, false);
                bundleMap.Read();
                m_AssetBundleMap = bundleMap.DataDict;
                Diagnostic.Log("load bundlemap finish");
                yield break;
            }

            string url = BaseDownloadingURL + GameEnvironment.BundleMapAssetName;
            using (WWW www = new WWW(url))
            {
                while (!www.isDone)
                {
                    yield return null;
                }

                if (string.IsNullOrEmpty(www.error) && www.bytes != null)
                {
                    AssetBundleMap bundleMap = new AssetBundleMap(www.bytes);
                    bundleMap.Read();
                    m_AssetBundleMap = bundleMap.DataDict;
                    Diagnostic.Log("load bundlemap ext finish");
                }
                else
                {
                    Diagnostic.Log("load bundlemap ext err :" + www.error);
                }
            }
        }

        //加载外挂AssetData 和 总的Manifest
        static public AssetBundleLoadManifestOperation LoadManifestExternal(string manifestAssetBundleName)
        {
#if UNITY_EDITOR
            // If we're in Editor simulation mode, we don't need the manifest assetBundle.
            if (SimulateAssetBundleInEditor)
                return null;
#endif
            TKAssetDatabaseLoad.Instance.ExternalLoad();

            //外部的Manifest也必须要加载
            //            bool external;
            //            GetAssetBundleUrl(manifestAssetBundleName, out external);
            //            if (external)
            
            string manifestPath = EXTERNAL_FOLDER + BaseLoader.GetPlatformFolderForAssetBundles() + "/" + manifestAssetBundleName;
            if (File.Exists(manifestPath))
            {
                AssetBundleDependenciesManager.Instance.ClearInternalManifest();
                AssetBundleDependenciesManager.Instance.ClearManifestAB();
                LoadAssetBundle(manifestAssetBundleName, true);
                var operation = new AssetBundleLoadManifestOperation(manifestAssetBundleName, "AssetBundleManifest", typeof(AssetBundleManifest), true);
                m_InProgressOperations.AddLast(operation);
                return operation;
            }
            return null;
        }

        // load assetDatabase manifest file
        static public IEnumerator InitAssetDatabase()
        {
#if UNITY_EDITOR
            // If we're in Editor simulation mode, we don't need the manifest assetBundle.
            if (SimulateAssetBundleInEditor)
                yield break;
#endif

            yield return TKAssetDatabaseLoad.Instance.InternalLoad();
        }

        // 仅提供给COS那边调用 COS和正常资源的shader ab可以共用
        static public bool LoadAssetbundleFromCOS(string assetBundleName)
        {
#if UNITY_EDITOR
            // If we're in Editor simulation mode, we don't have to really load the assetBundle and its dependencies.
            if (SimulateAssetBundleInEditor)
                return false;
#endif
            LoadAssetBundle(assetBundleName, false);
            return true;
        }

        // 仅提供给COS那边调用 COS和正常资源的shader ab可以共用
        static public AssetBundle LoadAssetBundleSyncFromCos(string assetBundleName)
        {
#if UNITY_EDITOR
            // If we're in Editor simulation mode, we don't have to really load the assetBundle and its dependencies.
            if (SimulateAssetBundleInEditor)
                return null;
#endif
            LoadAssetbundleFromCOS(assetBundleName);
            var loadedBundle = GetLoadedAssetBundle(assetBundleName, out var error);
            if (loadedBundle != null)
            {
                return loadedBundle.m_AssetBundle;
            }
            else
            {
                if (m_DownloadingWWWs.TryGetValue(assetBundleName, out var request))
                {
                    return request.assetBundle;
                }
            }
            return null;
        }

        // Load AssetBundle and its dependencies.
        static protected void LoadAssetBundle(string assetBundleName, bool isLoadingAssetBundleManifest = false)
        {
#if UNITY_EDITOR
            // If we're in Editor simulation mode, we don't have to really load the assetBundle and its dependencies.
            if (SimulateAssetBundleInEditor)
                return;
#endif
            UnityEngine.Profiling.Profiler.BeginSample("AssetBundleManager.LoadAssetBundle:" + assetBundleName);
            if (!isLoadingAssetBundleManifest)
                assetBundleName = RemapVariantName(assetBundleName);

            // Check if the assetBundle has already been processed.
            bool isAlreadyProcessed = LoadAssetBundleInternal(assetBundleName, isLoadingAssetBundleManifest);

            // Load dependencies.
            if (!isAlreadyProcessed && !isLoadingAssetBundleManifest)
                LoadDependencies(assetBundleName);
            UnityEngine.Profiling.Profiler.EndSample();
            //Debug.LogError("LoadAssetBundle:" + assetBundleName + "  assetbundle: " + m_LoadedAssetBundles.Count);
        }

        // Remaps the asset bundle name to the best fitting asset bundle variant.
        public static string RemapVariantName(string assetBundleName)
        {
            
#if  ENABLE_ASSET_BUNDLE_EXTEND
            if (assetBundleName.EndsWith(".unity3d"))
            {
                int len = ".unity3d".Length;
                int subLen = assetBundleName.Length - len;
                assetBundleName = assetBundleName.Substring(0, subLen);
            }
#endif
            //string[] bundlesWithVariant = m_AssetBundleManifest.GetAllAssetBundlesWithVariant();
            string[] bundlesWithVariant = { };

            // If the asset bundle doesn't have variant, simply return.
            if (System.Array.IndexOf(bundlesWithVariant, assetBundleName) < 0)
                return assetBundleName;

            FastStringSplit split = assetBundleName.BeginSplit('.');

            int bestFit = int.MaxValue;
            int bestFitIndex = -1;
            // Loop all the assetBundles with variant to find the best fit variant assetBundle.
            for (int i = 0; i < bundlesWithVariant.Length; i++)
            {
                FastStringSplit curSplit = bundlesWithVariant[i].BeginSplit('.');
                string data = curSplit[0];
                curSplit.EndSplit();
                
                if (data != split[0])
                    continue;

                int found = System.Array.IndexOf(m_Variants, curSplit[1]);
                if (found != -1 && found < bestFit)
                {
                    bestFit = found;
                    bestFitIndex = i;
                }
            }
            split.EndSplit();

            if (bestFitIndex != -1)
                return bundlesWithVariant[bestFitIndex];
            else
                return assetBundleName;
        }

        const string FILE_START_TAG = "file://";

        // Where we actuall call WWW to download the assetBundle.
        static protected bool LoadAssetBundleInternal(string assetBundleName, bool isLoadingAssetBundleManifest, bool pdependencies = true)
        {
            // Already loaded.
            //Diagnostic.Log("AssetBundleManager.LoadAssetBundleInternal: " + assetBundleName);
            LoadedAssetBundle bundle = null;
            m_LoadedAssetBundles.TryGetValue(assetBundleName, out bundle);
            if (bundle != null)
            {
                bundle.m_ReferencedCount++;

                if (pdependencies)
                {
                    string[] dependencies = AssetBundleDependenciesManager.Instance.GetAllDependencies(assetBundleName);
                    for (int i = 0; i < dependencies.Length; i++)
                    {
                        string dependencyBudnleName = dependencies[i];
                        LoadedAssetBundle dependencyBundle = null;
                        if (m_LoadedAssetBundles.TryGetValue(dependencyBudnleName, out dependencyBundle))
                        {
                            dependencyBundle.m_ReferencedCount++;
                        }
                        else if (m_DownloadingWWWs.ContainsKey(dependencyBudnleName))
                        {
                            m_DownloadingWWWsReqCount[dependencyBudnleName]++;
                        }
                        else
                        {
                            LoadAssetBundleInternal(dependencyBudnleName, false, false);
                        }

                    }
                    return true;

                }

                return false;

            }

            // @TODO: Do we need to consider the referenced count of WWWs?
            // In the demo, we never have duplicate WWWs as we wait LoadAssetAsync()/LoadLevelAsync() to be finished before calling another LoadAssetAsync()/LoadLevelAsync().
            // But in the real case, users can call LoadAssetAsync()/LoadLevelAsync() several times then wait them to be finished which might have duplicate WWWs.
            if (m_DownloadingWWWs.ContainsKey(assetBundleName))
            {
                m_DownloadingWWWsReqCount[assetBundleName]++;

                return false;
            }

#if UNITY_IOS || NO_AB_CACHE
			AssetBundleCreateRequest download = null;
#else
			WWW download = null;
#endif

            //string url = AssetBundleFileLoader.AssetbundleFilesRootPath + assetBundleName;
            string url = GetAssetBundleUrl(assetBundleName, out bool external, isLoadingAssetBundleManifest);

            // For manifest assetbundle, always download it as we don't have hash for it.
            if (isLoadingAssetBundleManifest)
            {

#if UNITY_IOS
				string	filePath = url.Contains(FILE_START_TAG) ? url.Replace(FILE_START_TAG,"") : url;
				download = AssetBundle.LoadFromFileAsync(filePath);
#elif NO_AB_CACHE
                string filePath = url;
                if (!external)
                {
                    filePath = GetInPkgAssetBundlePath(assetBundleName, true);
                }
                else
                {
                    if (url.Contains(FILE_START_TAG))
                        filePath = url.Replace(FILE_START_TAG, "");
                }
                download = AssetBundle.LoadFromFileAsync(filePath);
#else
				download = new WWW (url);
#endif
            }
            else
            {
#if UNITY_IOS
                string filePath = url.Contains(FILE_START_TAG) ? url.Replace(FILE_START_TAG,"") : url;
				download = AssetBundle.LoadFromFileAsync(filePath);

#elif NO_AB_CACHE
                string filePath = url;
                if (!external)
                {
                    filePath = GetInPkgAssetBundlePath(assetBundleName);
                }
                else
                {
                    if (url.Contains(FILE_START_TAG))
                        filePath = url.Replace(FILE_START_TAG, "");
                }
                
                download = AssetBundle.LoadFromFileAsync(filePath);
#elif UNITY_EDITOR || UNITY_STANDALONE_WIN
                download = new WWW(url);

#else
                var manifest = AssetBundleDependenciesManager.Instance.AssetBundleManifestObject;
                Hash128 bundleHash = manifest.GetAssetBundleHash(GetAssetBundleNameWithExtend(assetBundleName));
                uint bundleCrc = TKAssetDatabase.Instance.GetAssetBundleCRC(GetAssetBundleNameWithExtend(assetBundleName), external);
                download = WWW.LoadFromCacheOrDownload(url, bundleHash, bundleCrc);
#endif
            }

#if !JK_RELEASE
            Diagnostic.Log($"[AssetbundleManager.LoadAssetBundleInternal] StartLoad {assetBundleName} {Time.frameCount}");
#endif

            if (download != null)
            {
                m_DownloadingWWWs.Add(assetBundleName, download);
                m_DownloadingWWWsReqCount.Add(assetBundleName, 1);
            }

            return false;
        }

        private static string GetAssetBundleNameWithExtend(string assetBundleName)
        {
#if ENABLE_ASSET_BUNDLE_EXTEND
            if(assetBundleName.Contains(".unity3d") == false)
                assetBundleName += ".unity3d";
#endif
            return assetBundleName;
        }
#if !JK_RELEASE
        public static bool IsPrintABName
        {
            get
            {
                int key = PlayerPrefs.GetInt("IS_PRINT_AB_NAME", 0);
                if (key > 0)
                {
                    TKFrame.Diagnostic.Log("IS_PRINT_AB_NAME");
                    return true;
                }
                return false;
            }
            set
            {
                if (value)
                    PlayerPrefs.SetInt("IS_PRINT_AB_NAME", 1);
                else
                    PlayerPrefs.SetInt("IS_PRINT_AB_NAME", 0);
                PlayerPrefs.Save();
            }
        }
#endif

#if ENABLE_ASSET_BUNDLE_EXTEND
        private static Hash128 zero128 = new Hash128();
#endif
        
        static protected string GetAssetBundleUrl(string assetBundleName, out bool external, bool isLoadingAssetBundleManifest = false)
        {
#if !JK_RELEASE
            if(IsPrintABName)
                Diagnostic.Log("******====== " + assetBundleName);
#endif

            /*
            var newAssetBundleName = GetAssetBundleRealName(assetBundleName);
            if (!assetBundleName.Equals(newAssetBundleName, StringComparison.OrdinalIgnoreCase))
            {
                string path = EXTERNAL_FOLDER + BaseLoader.GetPlatformFolderForAssetBundles() + "/" + assetBundleName + ".unity3d";
                if (File.Exists(path))
                {
                    // assetBundleName = newAssetBundleName;
                    external = true;
                    return "file://" + path;
                }
            }
            */

            bool isOverrideAssetbundleName = m_OverrideAssetbundles.TryGetValue(assetBundleName, out string url);
            if (isOverrideAssetbundleName)
            {
                external = true;
                return url;
            }

#if ENABLE_ASSET_BUNDLE_EXTEND
            if ( assetBundleName.Contains(".unity3d") == false && isLoadingAssetBundleManifest == false)
            {
                assetBundleName += ".unity3d";
            }
            else
            {
                Diagnostic.Log($"{assetBundleName} contains unity3d extend");
            }
#endif
            
            string abTureName = assetBundleName;
#if ENABLE_ASSET_BUNDLE_PATH_ENCRYPT
            if (!isLoadingAssetBundleManifest)
            {
                Hash128 hash128 = AssetBundleDependenciesManager.Instance.AssetBundleManifestObject.GetAssetBundleHash(assetBundleName);
                if (hash128 != zero128)
                {
                    string hash128str = hash128.ToString();
                    string dir = hash128str.Substring(0, 2);
                    abTureName = dir + "/" + hash128 + ".unity3d";
                }
            }
#endif
            
            var descExternal = TKAssetDatabase.Instance.GetAssetbundleDesc(assetBundleName, true, isLoadingAssetBundleManifest);

            if (descExternal == null)
            {
                external = false;
                return m_BaseDownloadingURL + abTureName;
            }
            
            //屏蔽对比CRC，如果外部有资源，就用外部的
            var desc = TKAssetDatabase.Instance.GetAssetbundleDesc(assetBundleName, false, isLoadingAssetBundleManifest);
            if (desc != null)
            {
                if (desc.CRC == descExternal.CRC)
                {
                    string pathEx = EXTERNAL_FOLDER + BaseLoader.GetPlatformFolderForAssetBundles() + "/" + abTureName;
                    if (File.Exists(pathEx))
                    {
                        external = true;
                        return FILE_START_TAG + pathEx;
                    }
                    external = false;
                    return m_BaseDownloadingURL + abTureName;
                }
                string path = EXTERNAL_FOLDER + BaseLoader.GetPlatformFolderForAssetBundles() + "/" + abTureName;
                Diagnostic.Log("******desc CRC**: " + desc.CRC + "*********descExternal CRC**: " + descExternal.CRC + "***path**" + path);
                if (File.Exists(path))
                {
                    external = true;
                    return FILE_START_TAG + path;
                }

                //下载的bundle丢失，且包内没有可使用的bundle
                HandleBundleMissLogic(assetBundleName);

                external = false;
                return m_BaseDownloadingURL + abTureName;

            }
            else
            {
                string path = EXTERNAL_FOLDER + BaseLoader.GetPlatformFolderForAssetBundles() + "/" + abTureName;
                if (File.Exists(path))
                {
                    external = true;
                    return FILE_START_TAG + path;
                }
                //下载的bundle丢失，且包内没有可使用的bundle
                HandleBundleMissLogic(assetBundleName);

                external = false;
                return m_BaseDownloadingURL + abTureName;
            }
        }

        //获取Packge包内 AssetBundle路径
        static protected string GetInPkgAssetBundlePath(string assetBundleName, bool isLoadingAssetBundleManifest = false)
        {
            //assetBundleName = GetAssetBundleRealName(assetBundleName);
            
#if ENABLE_ASSET_BUNDLE_EXTEND
            if( assetBundleName.Contains(".unity3d") == false && isLoadingAssetBundleManifest == false)
            {
                assetBundleName += ".unity3d";
            }
            else
            {
                Diagnostic.Log($"{assetBundleName} contains unity3d extend");
            }
#endif
            
            string abTureName = assetBundleName;
#if ENABLE_ASSET_BUNDLE_PATH_ENCRYPT
            if (!isLoadingAssetBundleManifest)
            {
                Hash128 hash128 = AssetBundleDependenciesManager.Instance.AssetBundleManifestObject.GetAssetBundleHash(assetBundleName);
                if (hash128 != zero128)
                {
                    string hash128str = hash128.ToString();
                    string dir = hash128str.Substring(0, 2);
                    abTureName = dir + "/" + hash128 + ".unity3d";
                }
                    
            }
#endif
            
            return AssetBundleFileLoader.AssetbundleFilesRootPath + abTureName;
        }

        private static void HandleBundleMissLogic(string assetBundleName)
        {
            //下载的bundle丢失，且包内没有可使用的bundle
            Diagnostic.Error("AssetBundleManager: bundle {0} is missing", assetBundleName);

            _bundleMissingTimes++;
            //#if UNITY_ANDROID
            if (_bundleMissingTimes == 1)
            {
                DefaultDialogUtils.ShowTxtDialog(TKFrameworkDelegateInterface.Localization_Trans("检测到下载资源损坏，可能会影响正常游戏体验，请进行修复，该修复会自动退出游戏，需要手动重新启动。"), true, RepairLostBundle);
            }
            //#endif
            if (NotifyMissingBundle != null)
                NotifyMissingBundle(assetBundleName);
        }

        private static void RepairLostBundle()
        {
            if (NotifyRepairHandler != null)
                NotifyRepairHandler();

            //屏蔽Android的特殊处理，根据具体更新时机来处理
            //            TKPlatform.RestartApplication();
        }
#if UNITY_EDITOR
        private static TKDictionary<string, string[]> m_cacheAssetDict = new TKDictionary<string, string[]>();
#endif
        public static bool CheckAssetExist(string assetBundleName, string asset)
        {
#if UNITY_EDITOR
            if (AssetBundleManager.SimulateAssetBundleInEditor)
            {
#if ENABLE_ASSET_BUNDLE_EXTEND
                if (assetBundleName.Contains(".unity3d") == false)
                {
                    assetBundleName += ".unity3d";
                }
#endif
                string[] paths; // 提高点编辑器下的速度
                if (!m_cacheAssetDict.TryGetValue(assetBundleName, out paths))
                {
                    paths = AssetDatabase.GetAssetPathsFromAssetBundle(assetBundleName);
                    m_cacheAssetDict.Add(assetBundleName, paths);
                }
                //string[] paths = AssetDatabase.GetAssetPathsFromAssetBundle(assetBundleName);
                if (paths != null && paths.Length > 0)
                {
                    for (int i = 0; i < paths.Length; ++i)
                    {
                        string fileName = Path.GetFileNameWithoutExtension(paths[i]);
                        if (fileName == asset)
                        {
                            return true;
                        }
                    }
                }
                return false;
            }
#endif
            string desc = TKAssetDatabase.Instance.GetAssetDesc(assetBundleName, asset, outputLog: false);
            if (desc == null)
            {
                desc = TKAssetDatabase.Instance.GetAssetDesc(assetBundleName, asset, true, false);
            }
            // return desc != null;
            return true;
        }

        //检测指定路径的assetbundle资源是否存在
        public static bool CheckAssetBundleExist(string assetBundleName)
        {
#if UNITY_EDITOR
            if (AssetBundleManager.SimulateAssetBundleInEditor)
            {
#if ENABLE_ASSET_BUNDLE_EXTEND
                if (assetBundleName.Contains(".unity3d") == false)
                {
                    assetBundleName += ".unity3d";
                }
#endif
                string[] paths = AssetDatabase.GetAssetPathsFromAssetBundle(assetBundleName);
                if (paths != null && paths.Length > 0)
                {
                    return true;
                }
                return false;
            }
#endif
#if ENABLE_ASSET_BUNDLE_EXTEND
            if (assetBundleName.Contains(".unity3d") == false)
            {
                assetBundleName += ".unity3d";
            }
#endif
            TKAssetbundleDesc desc = TKAssetDatabase.Instance.GetAssetbundleDesc(assetBundleName);
            if (desc == null)
            {
                desc = TKAssetDatabase.Instance.GetAssetbundleDesc(assetBundleName, true);
            }
            // return desc != null;
            return true;
        }

        // Where we get all the dependencies and load them all.
        static protected void LoadDependencies(string assetBundleName)
        {
            
            if (AssetBundleDependenciesManager.Instance.AssetBundleManifestObject == null)
            {
#if OPEN_DEBUG
                Debug.LogError("Please initialize AssetBundleManifest by calling AssetBundleManager.Initialize()");
#endif
                return;
            }

            // Get dependecies from the AssetBundleManifest object..
            string[] dependencies;
            if (!AssetBundleDependenciesManager.Instance.isExternal)
            {
                dependencies = AssetBundleDependenciesManager.Instance.GetAllDependencies(assetBundleName);
            }
            else
            {
                dependencies = AssetBundleDependenciesManager.Instance.GetAllDependencies(assetBundleName);
            }

            for (int i = 0; i < dependencies.Length; i++)
            {
                LoadAssetBundleInternal(dependencies[i], false, false);
            }
        }

        // Unload assetbundle and its dependencies.
        static public void UnloadAssetBundle(string assetBundleName)
        {
#if UNITY_EDITOR
            // If we're in Editor simulation mode, we don't have to load the manifest assetBundle.
            if (SimulateAssetBundleInEditor)
                return;
#endif
            
#if ENABLE_ASSET_INFO
            if (AssetBundleManager.isPrintAssetInfo)
                Diagnostic.Log("AssetService Unload ab " + assetBundleName);
#endif

            UnloadAssetBundleInternal(assetBundleName);
            UnloadDependencies(assetBundleName);
        }

        static public void ReloadAssetbundle(string assetBundleName, bool unloadAsset)
        {
            string error;
            LoadedAssetBundle bundle = GetLoadedAssetBundleInternal(assetBundleName, out error);
            if (bundle != null)
            {
                bundle.m_AssetBundle.Unload(unloadAsset);

                string filePath = GetAssetBundleUrl(assetBundleName, out bool external, false);
                if (!external)
                {
                    filePath = GetInPkgAssetBundlePath(assetBundleName, true);
                }
                else
                {
                    if (filePath.Contains(FILE_START_TAG))
                        filePath = filePath.Replace(FILE_START_TAG, "");
                }
                var ab = AssetBundle.LoadFromFile(filePath);
                bundle.m_AssetBundle = ab;
            }
        }

        static public void UnloadAssetBundleSelfOnly(string assetBundleName)
        {
#if UNITY_EDITOR
            // If we're in Editor simulation mode, we don't have to load the manifest assetBundle.
            if (SimulateAssetBundleInEditor)
                return;
#endif

            //  Debug.LogError(m_LoadedAssetBundles.Count + " assetbundle(s) in memory before unloading " + assetBundleName);

            UnloadAssetBundleInternal(assetBundleName);
        }

        static public void UnloadAssetbundleDependencies(string assetBundleName)
        {
#if UNITY_EDITOR
            // If we're in Editor simulation mode, we don't have to load the manifest assetBundle.
            if (SimulateAssetBundleInEditor)
                return;
#endif

            UnloadDependencies(assetBundleName);
        }

        static protected void UnloadDependencies(string assetBundleName)
        {
            string[] dependencies = AssetBundleDependenciesManager.Instance.GetAllDependencies(assetBundleName);

            // Loop dependencies.
            foreach (var dependency in dependencies)
            {
                UnloadAssetBundleInternal(dependency);
            }
        }

        static protected void UnloadAssetBundleInternal(string assetBundleName)
        {
            string error;
            LoadedAssetBundle bundle = GetLoadedAssetBundleInternal(assetBundleName, out error);
            if (bundle == null)
                return;

            //  Diagnostic.Warn("  Unload AssetBundle  {0} ,refcount:{1}", assetBundleName, bundle.m_ReferencedCount);
            if (--bundle.m_ReferencedCount == 0)
            {
                if (Unload_Ab && bundle.m_isUnloadable)
                {
                    bundle.m_AssetBundle.Unload(true);
                }
                else
                {
                    bundle.m_AssetBundle.Unload(false);
                }

                m_LoadedAssetBundles.Remove(assetBundleName);
                //   Diagnostic.Warn("AssetBundle  {0} has been unloaded successfully", assetBundleName);
            }
        }

        void Update()
        {
            // Collect all the finished WWWs.
            m_keysToRemove.Clear();
            m_keysToRetry.Clear();

            try
            {
                foreach (var keyValue in m_DownloadingWWWs)
                {
#if UNITY_IOS || NO_AB_CACHE
					AssetBundleCreateRequest download = keyValue.Value;
#else
					WWW download = keyValue.Value;

					// If downloading fails.
					if (download.error != null)
					{
						if(!m_DownloadingErrors.ContainsKey(keyValue.Key))
						{
							m_DownloadingErrors.Add(keyValue.Key, download.error);
						}
						m_keysToRemove.Add(keyValue.Key);
						continue;
					}
#endif

                    // If downloading succeeds.
                    if (download.isDone)
                    {
                        if (download.assetBundle != null  )
                        {
                            var loadedAssetBundle = new LoadedAssetBundle(download.assetBundle);
                            loadedAssetBundle.m_ReferencedCount = m_DownloadingWWWsReqCount[keyValue.Key];
                            m_LoadedAssetBundles.Add(keyValue.Key, loadedAssetBundle);
                            m_keysToRemove.Add(keyValue.Key);
                        }
                        else
                        {
#if UNITY_IOS
							Diagnostic.Warn (" load assetbundle {0} is null ... ;   progress :{1}", keyValue.Key, download.progress);
							
							bool external;
							string url = GetAssetBundleUrl(keyValue.Key, out external);
							string	filePath = url.Replace("file://","");
							AssetBundle ab = AssetBundle.LoadFromFile (filePath);

							if( ab != null )
							{
								var loadedAssetBundle = new LoadedAssetBundle (ab);
								loadedAssetBundle.m_ReferencedCount = m_DownloadingWWWsReqCount [keyValue.Key];
								m_LoadedAssetBundles.Add (keyValue.Key, loadedAssetBundle);
							}
							else
							{
								if(!m_DownloadingErrors.ContainsKey(keyValue.Key))
								{
									m_DownloadingErrors.Add(keyValue.Key, " assetbundle null... ");
								}
							}

							m_keysToRemove.Add(keyValue.Key);
#elif NO_AB_CACHE
                            Diagnostic.Warn(" load assetbundle {0} is null ... ;   progress :{1}", keyValue.Key,
                                download.progress);

                            bool external;
                            string url = GetAssetBundleUrl(keyValue.Key, out external);
                            string filePath /* = url*/;
                            if (!external)
                            {
                                filePath = GetInPkgAssetBundlePath(keyValue.Key);
                            }
                            else
                            {
                                filePath = url.Replace("file://", "");
                            }

                            AssetBundle ab = AssetBundle.LoadFromFile(filePath);

#if !JK_RELEASE
                            Diagnostic.Log($"[AssetbundleManager.Update] StartLoad {filePath} {Time.frameCount}");
#endif

                            if (ab != null)
                            {
                                var loadedAssetBundle = new LoadedAssetBundle(ab);
                                loadedAssetBundle.m_ReferencedCount = m_DownloadingWWWsReqCount[keyValue.Key];
                                m_LoadedAssetBundles.Add(keyValue.Key, loadedAssetBundle);
                            }
                            else
                            {
                                if (!m_DownloadingErrors.ContainsKey(keyValue.Key))
                                {
                                    Diagnostic.Log("AssetBundle.LoadFromFile   Error  FilePath：" + filePath +
                                                   "    FileName：" + keyValue.Key);

                                    m_DownloadingErrors.Add(keyValue.Key, " assetbundle null... ");
                                }
                            }

                            m_keysToRemove.Add(keyValue.Key);
#else
							Diagnostic.Warn (" load assetbundle {0} is null ... ; err:{1}; progress :{2}", keyValue.Key,download.error,download.progress);
							m_keysToRetry.Add (keyValue.Key);
#endif
                        }

                    }
                }

            }
            catch (Exception e)
            {
                Diagnostic.Error($"Crash !! {e}");
            }
            
            for (int i = 0; i < m_keysToRetry.Count; i++)
            {

                string key = m_keysToRetry[i];
#if UNITY_IOS || NO_AB_CACHE
				continue;
#else
				WWW download = m_DownloadingWWWs[key];
				string url = download.url;
				download.Dispose ();
#endif

#if UNITY_IOS || NO_AB_CACHE
#else
				var manifest = AssetBundleDependenciesManager.Instance.AssetBundleManifestObject;
				string assetBundleName = key;
				//Hash128 bundleHash = manifest.GetAssetBundleHash(GetAssetBundleNameWithExtend(assetBundleName));
				uint bundleCrc = TKAssetDatabase.Instance.GetAssetBundleCRC(assetBundleName, false);
				download = new WWW(url);
#endif
                
#if UNITY_IOS || NO_AB_CACHE
#else
				m_DownloadingWWWs [key] = download;
#endif
            }

            // Remove the finished WWWs.
            for (int i = 0; i < m_keysToRemove.Count; i++)
            {
                string key = m_keysToRemove[i];
#if UNITY_IOS || NO_AB_CACHE
                m_DownloadingWWWs.Remove(key);
                m_DownloadingWWWsReqCount.Remove(key);
#else
                WWW download = m_DownloadingWWWs[key];
				m_DownloadingWWWs.Remove(key);
				m_DownloadingWWWsReqCount.Remove(key);
				download.Dispose();
#endif
            }

            // 用于等待AB加载完毕。加载完毕就会移出队列中。
            // 模拟类不需要等待，因为没有AB可以加载
            if (m_InProgressOperations.Count > 0)
            {
                LinkedListNode<AssetBundleLoadOperation> curNode = m_InProgressOperations.First;
                while (curNode != null)
                {
                    AssetBundleLoadOperation curReq = curNode.Value;
                    LinkedListNode<AssetBundleLoadOperation> next = curNode.Next;
                    if (!curReq.Update())
                    {
                        m_InProgressOperations.Remove(curNode);
                    }
                    curNode = next;
                }
            }
        }

        // Load asset from the given assetBundle.
        static public AssetBundleLoadAssetOperation LoadAssetAsync(string assetBundleName, string assetName, System.Type type, bool isFirstLoad = false)
        {
            AssetBundleLoadAssetOperation operation = null;
#if UNITY_EDITOR
            if (SimulateAssetBundleInEditor)
            {
#if ENABLE_ASSET_BUNDLE_EXTEND
                if (assetBundleName.Contains(".unity3d") == false)
                {
                    assetBundleName += ".unity3d";
                }
#endif
                string[] assetPaths = null;
                Diagnostic.Log($"AssetBundle Load {assetBundleName}");

                if (!string.IsNullOrEmpty(assetName))
                {
                    assetPaths = AssetDatabase.GetAssetPathsFromAssetBundleAndAssetName(assetBundleName, assetName);
                    Diagnostic.Log($"AssetBundle Load  {assetName},count = {assetPaths.Length}");
                }
                else
                {
                    assetPaths = AssetDatabase.GetAssetPathsFromAssetBundle(assetBundleName);
                    Diagnostic.Log($"AssetBundle Load  {assetName},count = {assetPaths.Length}");
                }

                if (assetPaths.Length == 0)
                {
#if OPEN_DEBUG
                    Debug.LogError("There is no asset with name \"" + assetName + "\" in " + assetBundleName);
#endif
                    return null;
                }

                // @TODO: Now we only get the main object from the first asset. Should consider type also.
                Object target = AssetDatabase.LoadAssetAtPath(assetPaths[0], type);
                operation = new AssetBundleLoadAssetOperationSimulation(target);
            }
            else
#endif
            {
                LoadAssetBundle(assetBundleName);
                operation = new AssetBundleLoadAssetOperationFull(assetBundleName, assetName, type);
                
                if(!isFirstLoad)
                    m_InProgressOperations.AddLast(operation);
                else
                    m_InProgressOperations.AddFirst(operation);
            }

            return operation;
        }

        // Load level from the given assetBundle.
        static public AssetBundleLoadOperation LoadLevelAsync(string assetBundleName, string levelName, bool isAdditive)
        {
            AssetBundleLoadOperation operation = null;
#if UNITY_EDITOR
            if (SimulateAssetBundleInEditor)
            {
#if ENABLE_ASSET_BUNDLE_EXTEND
                if (assetBundleName.Contains(".unity3d") == false)
                {
                    assetBundleName += ".unity3d";
                }
#endif
                string[] levelPaths = AssetDatabase.GetAssetPathsFromAssetBundleAndAssetName(assetBundleName, levelName);
                if (levelPaths.Length == 0)
                {
                    ///@TODO: The error needs to differentiate that an asset bundle name doesn't exist
                    //        from that there right scene does not exist in the asset bundle...

#if OPEN_DEBUG
                    Debug.LogError("There is no scene with name \"" + levelName + "\" in " + assetBundleName);
#endif
                    return null;
                }

                if (isAdditive)
                    EditorApplication.LoadLevelAdditiveInPlayMode(levelPaths[0]);
                else
                    EditorApplication.LoadLevelInPlayMode(levelPaths[0]);

                operation = new AssetBundleLoadLevelSimulationOperation();
            }
            else
#endif
            {
                LoadAssetBundle(assetBundleName);
                operation = new AssetBundleLoadLevelOperation(assetBundleName, levelName, isAdditive);

                m_InProgressOperations.AddLast(operation);
            }

            return operation;
        }

        static public IEnumerator LoadLevelSync(string assetBundleName, string levelName, bool isAdditive)
        {
            //<TKLua> assetBundle小写规则和Lua Stage机制冲突，所以在这里统一转成小写
            assetBundleName = assetBundleName.ToLower();
            levelName = levelName.ToLower();
            //</TKLua>

#if UNITY_EDITOR
            if (SimulateAssetBundleInEditor)
            {
#if ENABLE_ASSET_BUNDLE_EXTEND
                if (assetBundleName.Contains(".unity3d") == false)
                {
                    assetBundleName += ".unity3d";
                }
#endif
                string[] levelPaths = AssetDatabase.GetAssetPathsFromAssetBundleAndAssetName(assetBundleName, levelName);
                if (levelPaths.Length == 0)
                {
                    ///@TODO: The error needs to differentiate that an asset bundle name doesn't exist
                    //        from that there right scene does not exist in the asset bundle...

#if OPEN_DEBUG
                    Debug.LogError("There is no scene with name \"" + levelName + "\" in " + assetBundleName);
#endif
                    yield break;
                }

                if (isAdditive)
                    EditorApplication.LoadLevelAdditiveInPlayMode(levelPaths[0]);
                else
                    EditorApplication.LoadLevelInPlayMode(levelPaths[0]);

                yield break;
            }
            else
#endif
            {
                LoadAssetBundle(assetBundleName);

                string error = string.Empty;
                LoadedAssetBundle loadedBundle = GetLoadedAssetBundle(assetBundleName, out error);

                while (loadedBundle == null && string.IsNullOrEmpty(error))
                {
                    yield return null;
                    loadedBundle = GetLoadedAssetBundle(assetBundleName, out error);
                }
                if (loadedBundle != null)
                {
                    if (isAdditive)
                    {
                        Application.LoadLevelAdditive(levelName);
                    }
                    else
                    {
                        Application.LoadLevel(levelName);
                    }
                }
                yield return null;
            }

        }

        // Load asset from the given assetBundle.
        static public AssetBundleLoadAssetOperation LoadAllAssetAsync(string assetBundleName, string assetName, System.Type type)
        {
            AssetBundleLoadAssetOperation operation = null;
#if UNITY_EDITOR
            if (SimulateAssetBundleInEditor)
            {
#if ENABLE_ASSET_BUNDLE_EXTEND
                if (assetBundleName.Contains(".unity3d") == false)
                {
                    assetBundleName += ".unity3d";
                }
#endif
                string[] assetPaths = AssetDatabase.GetAssetPathsFromAssetBundle(assetBundleName);
                if (assetPaths.Length == 0)
                {
#if OPEN_DEBUG
                    Debug.LogError("There is no asset with name \"" + assetName + "\" in " + assetBundleName);
#endif
                    return null;
                }
                List<Object> assetLst = new List<Object>();
                for (int i = 0; i < assetPaths.Length; i++)
                {
                    Object target = AssetDatabase.LoadAssetAtPath(assetPaths[i], type);
                    assetLst.Add(target);
                }

                // @TODO: Now we only get the main object from the first asset. Should consider type also.

                operation = new AssetBundleLoadAllAssetSimulation(assetLst.ToArray());
            }
            else
#endif
            {
                LoadAssetBundle(assetBundleName);
                operation = new AssetBundleLoadAllAssetOperation(assetBundleName, assetName, type);

                m_InProgressOperations.AddLast(operation);
            }

            return operation;
        }

        static public UnityEngine.Object LoadAssetSync(string assetBundleName, string assetName, System.Type type)
        {
#if UNITY_EDITOR
            if (SimulateAssetBundleInEditor)
            {
#if ENABLE_ASSET_BUNDLE_EXTEND
                if (assetBundleName.EndsWith(".unity3d") == false)
                {
                    assetBundleName += ".unity3d";
                }
#endif
                string[] assetPaths = null;
                if (string.IsNullOrEmpty(assetName))
                {
                    assetPaths = AssetDatabase.GetAssetPathsFromAssetBundle(assetBundleName);
                }
                else
                {
                    assetPaths = AssetDatabase.GetAssetPathsFromAssetBundleAndAssetName(assetBundleName, assetName);
                }

                if (assetPaths.Length == 0)
                {
                    Debug.LogError("There is no asset with name \"" + assetName + "\" in " + assetBundleName);
                    return null;
                }
                List<Object> assetLst = new List<Object>();
                for (int i = 0; i < assetPaths.Length; i++)
                {
                    Object target = AssetDatabase.LoadAssetAtPath(assetPaths[i], type);
                    if (target == null)
                    {
                        continue;
                    }

                    if (assetPaths[i].Contains(assetName + ".shader") && type == typeof(Shader))
                    {
                        return target;
                    }

                    //<TKLua> type类型可以是资源类型的父类
                    /*old
                    if (target.name == assetName && type==target.GetType())
                    {
                        return target;
                    }
                    */
                    if (type.IsAssignableFrom(target.GetType()))
                    {
                        return target;
                    }
                    //</TKLua>
                }

                return null;
            }
#endif

            string error = string.Empty;
            LoadedAssetBundle loadedAssetBundle = AssetBundleManager.GetLoadedAssetBundle(assetBundleName, out error);
            if (loadedAssetBundle == null)
            {
                //Debug.LogErrorFormat("asset bundle path {0}  not existed in  AssetBundleManager", assetBundleName, assetName);
                Diagnostic.Warn("asset bundle path {0}  not existed in  AssetBundleManager", assetBundleName, assetName);

                return null;
            }
            return loadedAssetBundle.m_AssetBundle.LoadAsset(assetName, type);
        }

        //处理etc和astc分包问题
        public static string GetAssetBundleRealName(string assetBundleName)
        {
            AssetBundleMapData assetBundleMapData = null;
            if (m_AssetBundleMap != null && m_AssetBundleMap.TryGetValue(assetBundleName, out assetBundleMapData))
            {
                // if(assetBundleMapData.EtcVariant && !TexturePackerUtil.SupportAstc())

//调试代码=======
                if (TexturePackerUtil.Test_UseHD)
                {
                    Diagnostic.Log($"开始加载HD资源：{assetBundleMapData.HDVariantBundleName} ");
                    return assetBundleMapData.HDVariantBundleName.ToLower();
                }

                if (TexturePackerUtil.Test_UseETC && assetBundleMapData.EtcVariant)
                {
                    Diagnostic.Log($"开始加载ETC2资源：{assetBundleMapData.EtcVariantBundleName} ");
                    return assetBundleMapData.EtcVariantBundleName.ToLower();
                }
//调试代码=======

                if (TexturePackerUtil.SupportAstc() && assetBundleMapData.HDVariant && TKFrameworkDelegateInterface.NotchSizeImp_IsPad())
                {
                    Diagnostic.Log($"开始加载HD资源：{assetBundleMapData.HDVariantBundleName} ");
                    return assetBundleMapData.HDVariantBundleName.ToLower();
                }

                if (!TexturePackerUtil.SupportAstc() && assetBundleMapData.EtcVariant)
                {
                    Diagnostic.Log($"开始加载ETC2资源：{assetBundleMapData.EtcVariantBundleName} ");
                    return assetBundleMapData.EtcVariantBundleName.ToLower();
                }
            }
            return assetBundleName;
        }

        public static void UnloadAllAssetBundle()
        {
            foreach (string assetBundleName in m_LoadedAssetBundles.Keys)
            {
                LoadedAssetBundle bundle = null;
                m_LoadedAssetBundles.TryGetValue(assetBundleName, out bundle);
                if (bundle != null)
                {
                    bundle.m_AssetBundle.Unload(false);
                }
            }
            m_LoadedAssetBundles.Clear();
        }

        public static void UnloadAssetBundleMananger()
        {
            m_DownloadingErrors.Clear();
        }

        public static void print()
        {
            Diagnostic.Log("------------m_LoadedAssetBundles-------------");
            foreach (string key in m_LoadedAssetBundles.Keys)
            {
                Diagnostic.Log(key);
            }
            Diagnostic.Log("---------------------------------------------");
        }

    } // End of AssetBundleManager.
}
