using System.Collections;
using System.Collections.Generic;
using TKFrame;
using UnityEngine;
using UnityEngine.UI;

public class ScreenBgAdapter : MonoBehaviour
{
    public void Awake()
    {
        float aspect = TKScreenUIScaler.getAspectRatio();

        //matchWidthOrHeight = 1.0
        if (aspect >= TKScreenUIScaler.getReferenceRatio())
        {           
            var tex = GetComponent<RawImage>();
            if (tex != null)
            {
                float w = tex.texture.height * aspect;
                if (w - tex.texture.width > 0)
                {
                    float h = w * tex.texture.height / tex.texture.width;
                    var trans = GetComponent<RectTransform>();
                    trans.sizeDelta = new Vector2(w, h);
                }
            }
        }
    }
}
