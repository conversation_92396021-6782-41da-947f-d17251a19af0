using System.Collections.Generic;
using System.IO;
using TKFrame;

namespace TKFrame
{
    public abstract class MsgReceiveQueue
    {
        private Queue<int> m_dataLens = new Queue<int>();
        private MsgByteQueue m_datas = new MsgByteQueue(1024 * 512);
        private Queue<DecoderData> m_packages = new Queue<DecoderData>();

        public int Count
        {
            get
            {
                lock (this)
                {
                    return m_packages.Count;
                }
            }
        }
        
        public void Clear()
        {
            lock (this)
            {
                m_datas.Clear();
                m_dataLens.Clear();
                m_packages.Clear();
            }
        }
        
        public void Enqueue(DecoderData decoderData)
        {
            MemoryStream stream = GetMemoryStream(decoderData);
            stream.Position = 0;
            int len = (int)stream.Length;

            lock (this)
            {
                m_datas.Enqueue(stream, len);
                m_dataLens.Enqueue(len);
                m_packages.Enqueue(decoderData);
            }
        }

        protected abstract MemoryStream GetMemoryStream(DecoderData decoderData);

        public void CopyTo(MsgReceiveList otherReceiveList)
        {
            lock (this)
            {
                otherReceiveList.CopyFrom(m_dataLens, m_packages, m_datas);
                
                m_datas.Clear();
                m_dataLens.Clear();
                m_packages.Clear();
            }
        }
    }
}