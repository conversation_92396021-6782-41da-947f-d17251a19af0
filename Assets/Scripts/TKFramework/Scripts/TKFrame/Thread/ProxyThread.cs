using System;
using System.Threading;
using System.Collections.Generic;
using System.Linq;
using System.Security;
using System.Text;
using TKFrame;
using UnityEngine;

namespace TKFrame
{

    /// <summary>
    /// 代理线程，便于继承，只需要重写run函数即可完成
    /// 线程任务类型：自身线程任务：run、代理任务：runAction、任务队列：taskList
    /// 不同任务无本质区别，只有执行顺序区别
    /// </summary>
    public class ProxyThread
    {

        //线程ID
        public int ThreadID { get; private set; }

        //标识ID
        public int TagID { get; set; }

        //标识名称
        public String TagName { get; set; }

        //是否是活动线程
        private volatile bool _isActive;
        public bool isActive
        {
            get
            {
                return _isActive;
            }
            set
            {
                _isActive = value;
            }
        }

        private int m_affinity = 0;

        //系统线程
        protected System.Threading.Thread thread { get; set; }

        //线程优先级（-1表示未设置）
        private int threadPriority = -1;
        
        //任务代理
        public Action runAction;

        //线程任务队列
        private TaskList taskList = new TaskList();

        //信号同步事件
        private AutoResetEvent signalEvent = new AutoResetEvent(false);

        //设置线程优先级
        public virtual void setPriority(System.Threading.ThreadPriority priority)
        {
            threadPriority = (int)priority;
            if (thread != null) thread.Priority = (System.Threading.ThreadPriority)threadPriority;
        }

        public void SetAffinity(bool isBigCore)
        {
            if (isBigCore)
                m_affinity = 1;
            else
                m_affinity = 2;
            if (thread != null)
            {
#if ENABLE_MANAGED_BIND_CORE
                ThreadHelperUtils.SetManagedThreadAffinity(thread, m_affinity == 1);
#endif
            }
        }

        //启动线程
        public virtual void start()
        {
            //防止多次启动
            if (thread != null) return;
            isActive = true;
            thread = new System.Threading.Thread(ThreadProc);
            
            if (!string.IsNullOrEmpty(TagName))
                thread.Name = TagName;
            
            thread.IsBackground = true;
            if (threadPriority >= 0)
            {
                thread.Priority = (System.Threading.ThreadPriority)threadPriority;
            }
            
            if (m_affinity != 0)
            {
#if ENABLE_MANAGED_BIND_CORE
                ThreadHelperUtils.SetManagedThreadAffinity(thread, m_affinity == 1);
#endif
            }
            
            ThreadID = thread.ManagedThreadId;
            thread.Start(this);

        }

        //线程执行回调，要求是static函数,注意：自身线程任务：run、代理任务：runAction、任务队列：taskList
        protected static void ThreadProc(object obj)
        {
            ProxyThread thread = (ProxyThread)obj;
            
            //执行自身线程任务
            thread.run();

            //执行代理任务
            if (thread.runAction !=null)
            {
                thread.runAction();
            }
            
            //执行挂接的子任务
            while (thread.taskList.execute());

            //执行完毕，释放线程
            thread.stop();
        }

        //中断线程运行
        public virtual void abort()
        {
            if (!isActive) return;
            
            try
            {
                isActive = false;
                thread.Abort();
                thread = null;
            }
            catch(SecurityException se)
            {
                Diagnostic.Log("ProxyThread.abort:"+se.ToString());
            }
            catch(ThreadStateException te)
            {
                Diagnostic.Log("ProxyThread.abort:" + te.ToString());
            }
            catch(Exception e)
            {
                Diagnostic.Log("ProxyThread.abort:" + e.ToString());
            }
        }

		//停止线程运行
		public virtual void stop()
		{
			if (!isActive) return;

			isActive = false;
			thread = null;
		}

        //信号-阻塞等待函数
        public virtual void wait()
        {
            signalEvent.WaitOne();
        }

        //信号-阻塞等待函数
        public virtual void wait(int millisecondsTimeout)
        {
            signalEvent.WaitOne(millisecondsTimeout);
        }

        //信号-通知函数
        public virtual void notify()
        {
            signalEvent.Set();
        }

        //获取任务列表
        public TaskList getTaskList()
        {
            return taskList;
        }


        //子线程执行体函数
        public virtual void run()
        {

        }


    }
}
