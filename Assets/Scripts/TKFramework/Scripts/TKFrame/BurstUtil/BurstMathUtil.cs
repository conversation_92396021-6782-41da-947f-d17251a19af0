using AOT;
using Unity.Burst;
using Unity.Mathematics;
using UnityEngine;

namespace TKFrame
{
    [BurstCompile(CompileSynchronously = true)]
    public static class BurstMathUtil
    {
        #region Normalize
        
        public static BurstDelegate.TwoFloat3_Func Normalize;
        
        [BurstCompile(CompileSynchronously = true)]
        [MonoPInvokeCallback(typeof(BurstDelegate.TwoFloat3_Func))]
        public static void Normalize_Func(ref float3 x, ref float3 y)
        {
            float xDot = math.dot(x, x);
            if (xDot > 0.000000001f)
            {
                y = math.rsqrt(xDot) * x;
            }
        }
        
        #endregion
        
        #region MultiplyPoint3x4
        
        public static BurstDelegate.OneFloat4x4_TwoFloat3_Func MultiplyPoint3x4;
        
        [BurstCompile(CompileSynchronously = true)]
        [MonoPInvokeCallback(typeof(BurstDelegate.OneFloat4x4_TwoFloat3_Func))]
        public static void MultiplyPoint3x4_Func(ref float4x4 matrix, ref float3 x, ref float3 y)
        {
            y = math.mul(matrix, new float4(x, 1f)).xyz;
        }
        
        #endregion
        
        #region Lerp

        public static BurstDelegate.ThreeFloat3_OneFloat_Func Lerp;
        
        [BurstCompile(CompileSynchronously = true)]
        [MonoPInvokeCallback(typeof(BurstDelegate.ThreeFloat3_OneFloat_Func))]
        public static void Lerp_Func(ref float3 x, ref float3 y, ref float3 result, float s)
        {
            result = math.lerp(x, y, s);
        }
        
        #endregion
        
        #region Distance
        
        public static BurstDelegate.TwoFloat3_RetFloat_Func Distance;
        
        [BurstCompile(CompileSynchronously = true)]
        [MonoPInvokeCallback(typeof(BurstDelegate.TwoFloat3_RetFloat_Func))]
        public static float Distance_Func(ref float3 x, ref float3 y)
        {
            return math.distance(x, y);
        }
        
        #endregion
        
        #region Length
        
        public static BurstDelegate.OneFloat3_RetFloat_Func Length;
        
        [BurstCompile(CompileSynchronously = true)]
        [MonoPInvokeCallback(typeof(BurstDelegate.OneFloat3_RetFloat_Func))]
        public static float Length_Func(ref float3 x)
        {
            return math.length(x);
        }
        
        #endregion

        #region Inverse

        public static BurstDelegate.OneFloat4x4_OneMatreix4x4 Inverse;

        [BurstCompile(CompileSynchronously = true)]
        [MonoPInvokeCallback(typeof(BurstDelegate.OneFloat4x4_OneMatreix4x4))]
        public static void Inverse_Func(ref float4x4 x, ref Matrix4x4 y)
        {
            y = math.inverse(x);
        }
        
        #endregion

        public static void Init()
        {
            Normalize = BurstUtil.BurstCompile<BurstDelegate.TwoFloat3_Func>(Normalize_Func);
            MultiplyPoint3x4 = BurstUtil.BurstCompile<BurstDelegate.OneFloat4x4_TwoFloat3_Func>(MultiplyPoint3x4_Func);
            Lerp = BurstUtil.BurstCompile<BurstDelegate.ThreeFloat3_OneFloat_Func>(Lerp_Func);
            Distance = BurstUtil.BurstCompile<BurstDelegate.TwoFloat3_RetFloat_Func>(Distance_Func);
            Length = BurstUtil.BurstCompile<BurstDelegate.OneFloat3_RetFloat_Func>(Length_Func);
            Inverse = BurstUtil.BurstCompile<BurstDelegate.OneFloat4x4_OneMatreix4x4>(Inverse_Func);
        }
    }
}