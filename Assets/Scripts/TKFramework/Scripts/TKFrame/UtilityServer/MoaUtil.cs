using UnityEngine;

#if UNITY_ANDROID || UNITY_IPHONE
using System.Text;
using System.Security.Cryptography;
using System;
using System.IO;
#endif

#if UNITY_EDITOR_WIN || UNITY_STANDALONE_WIN
using System.Threading;
#endif

public class MoaUtil
{
    public static string GetName()
    {
#if UNITY_ANDROID || UNITY_IPHONE
        string txtPath = Path.Combine(Application.persistentDataPath, "LoginInfo.txt");
        if (File.Exists(txtPath))
        {
            string encryptBase64Content = System.IO.File.ReadAllText(txtPath);
            if (!string.IsNullOrEmpty(encryptBase64Content))
            {
                return AesDecrypt(encryptBase64Content, SECRET_KEY, IV);
                //Debug.LogWarningFormat("AesDecrypt result:{0}", AesDecrypt(encryptBase64Content, SECRET_KEY, IV));
            }
        }
        else
        {
            //Debug.LogErrorFormat("Can not find file:{0}", txtPath);
        }
#endif
        return "";
    }

#if UNITY_ANDROID || UNITY_IPHONE

    private static readonly String SECRET_KEY = "4ny2dqtz0b56da4y";

    private static readonly String IV = "0000000000000000";

    public static string AesDecrypt(string data, string key, string iv)
    {

        try
        {
            byte[] keyArray = UTF8Encoding.UTF8.GetBytes(key);
            byte[] ivArray = UTF8Encoding.UTF8.GetBytes(iv);
            byte[] toEncryptArray = Convert.FromBase64String(data);
            byte[] resultArray = null;

            using (RijndaelManaged rDel = new RijndaelManaged())
            {
                rDel.Key = keyArray;
                rDel.IV = ivArray;
                rDel.Mode = CipherMode.CBC;
                rDel.Padding = PaddingMode.PKCS7;

                ICryptoTransform cTransform = rDel.CreateDecryptor();
                resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);
            }

            return UTF8Encoding.UTF8.GetString(resultArray);
        }
        catch (Exception e)
        {
            return e.ToString();
        }
    }
#endif
}
