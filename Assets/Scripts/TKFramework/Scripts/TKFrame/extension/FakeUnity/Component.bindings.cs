#if ACGGAME_CLIENT

using System;
using System.Collections.Generic;

namespace UnityEngine4Server
{
 
    public partial class Component : UnityEngine4Server.Object
    {
        public  Transform transform
        {        
            get { return gameObject.transform; }
        }

        public  GameObject gameObject
        {         
            get;
            set;
        }
      

        public Component GetComponent(Type type)
        {
            return gameObject.GetComponent(type);
        }
     
        public T GetComponent<T>()
        {
            
            return gameObject.GetComponent<T>();
        }


        public override void CloneFrom<Component>(Component original)
        {          

        }

        //��ʱ����Ҫ
        /*
        public string tag
        {
            get { return gameObject.tag; }
            set { gameObject.tag = value; }
        }
       

        public bool CompareTag(string tag)
        {
            return gameObject.CompareTag(tag);
        }
        */
    }
}
#endif
