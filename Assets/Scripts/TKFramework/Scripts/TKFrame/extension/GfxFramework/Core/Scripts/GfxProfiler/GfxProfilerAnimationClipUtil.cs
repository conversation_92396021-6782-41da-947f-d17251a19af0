#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;

namespace GfxFramework
{
    public static class GfxProfilerAnimationClipUtil
    {
        /// <summary>
        /// 纹理工具类型
        /// </summary>
        public static Type animationClipStatsType
        {
            get
            {
                if (s_AnimationClipStatsType == null)
                {
                    Assembly asm = Assembly.GetAssembly(typeof(Editor));
                    s_AnimationClipStatsType = asm.GetType("UnityEditor.AnimationClipStats");
                }

                return s_AnimationClipStatsType;
            }
        }
        private static Type s_AnimationClipStatsType;

        private static MethodInfo s_GetAnimationClipStats = typeof(AnimationUtility).GetMethod("GetAnimationClipStats", BindingFlags.Static | BindingFlags.NonPublic | BindingFlags.Public);

        private static FieldInfo size = animationClipStatsType.GetField("size", BindingFlags.Public | BindingFlags.Instance);
        private static FieldInfo totalCurves = animationClipStatsType.GetField("totalCurves", BindingFlags.Public | BindingFlags.Instance);
        //private static FieldInfo constantCurves = animationClipStatsType.GetField("constantCurves", BindingFlags.Public | BindingFlags.Instance);
        //private static FieldInfo positionCurves = animationClipStatsType.GetField("positionCurves", BindingFlags.Public | BindingFlags.Instance);
        //private static FieldInfo quaternionCurves = animationClipStatsType.GetField("quaternionCurves", BindingFlags.Public | BindingFlags.Instance);
        //private static FieldInfo scaleCurves = animationClipStatsType.GetField("scaleCurves", BindingFlags.Public | BindingFlags.Instance);
        private static FieldInfo streamCurves = animationClipStatsType.GetField("streamCurves", BindingFlags.Public | BindingFlags.Instance);

        public static object GetStat(AnimationClip animationClip)
        {
            if (s_GetAnimationClipStats != null)
                return s_GetAnimationClipStats.Invoke(null, new object[] { animationClip });
            return null;
        }

        public static int GetSize(object stat)
        {
            return (int)size.GetValue(stat);
        }

        public static int GetTotalCurves(object stat)
        {
            return (int)totalCurves.GetValue(stat);
        }

        public static int GetStreamCurves(object stat)
        {
            return (int)streamCurves.GetValue(stat);
        }
    }
}
#endif