using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace GfxFramework
{
    #if ENABLE_PARTICLE_SYSTEM_POOL
    
    public static class ParticleSystemShell
    {
        public static void Init()
        {
            PsShell<ParticleSystem>.m_GetData = GetData;
            PSList<ParticleSystem>.m_GetData = GetData;
        }

        private static ParticleSystem GetData(ParticleSystemCompressData compressData)
        {
            if(compressData != null)
                return compressData.ParticleSystem;
            return null;
        }
    }
    
    public static class ParticleSystemRendererShell
    {
        public static void Init()
        {
            PSList<ParticleSystemRenderer>.m_GetData = GetData;
            PsShell<ParticleSystemRenderer>.m_GetData = GetData;
        }

        private static ParticleSystemRenderer GetData(ParticleSystemCompressData compressData)
        {
            if(compressData != null)
                return compressData.ParticleSystemRenderer;
            return null;
        }
    }
    
    public static class BaseRendererShell
    {
        public static void Init()
        {
                PSList<Renderer>.m_GetData = GetData;
                PsShell<Renderer>.m_GetData = GetData;
        }
    
        private static ParticleSystemRenderer GetData(ParticleSystemCompressData compressData)
        {
            if(compressData != null)
                return compressData.ParticleSystemRenderer;
            return null;
        }
    }    
    
    #endif
    
    public static class ParticleSystemShellInit
    {
        public static void Init()
        {
#if ENABLE_PARTICLE_SYSTEM_POOL
            ParticleSystemRendererShell.Init();
            ParticleSystemShell.Init();
            BaseRendererShell.Init();
#endif
        }
    }
}
