using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace GfxFramework
{
    [Serializable]
    public class GfxLodSystem
    {
        public GfxLod m_lod = GfxLod.Low;
        public string[] m_tags = null;
        
        #if UNITY_EDITOR

        private static List<string> m_tmpTags = new List<string>();

        public void ClearTags()
        {
            m_tags = null;
        }

        public void AddTag(string tag)
        {
            BeginChange();
            m_tmpTags.Add(tag);
            EndChange();
        }

        public void AddTagsRange(string[] otherTags)
        {
            BeginChange();
            m_tmpTags.AddRange(otherTags);
            EndChange();
        }

        public void RemoveTagAt(int index)
        {
            BeginChange();
            m_tmpTags.RemoveAt(index);
            EndChange();
        }

        public void RemoveTag(string tag)
        {
            BeginChange();
            m_tmpTags.Remove(tag);
            EndChange();
        }

        private void BeginChange()
        {
            m_tmpTags.Clear();
            if(m_tags != null)
                m_tmpTags.AddRange(m_tags);
        }

        private void EndChange()
        {
            if (m_tmpTags.Count == 0)
                m_tags = null;
            else
                m_tags = m_tmpTags.ToArray();
        }
        
        #endif

        #region Runtime
        private GfxLodBase m_lodCtr = null;
        public GfxLodBase lodCtr
        {
            get
            {
                return m_lodCtr;
            }
        }

        // 当前这个节点的标签是否在激活状态
        private bool m_tagActive = true;

        // 当前这个节点的被外界控制为显示状态还是隐藏状态的
        private bool m_outsideActive = true;

        // 当前这个节点是否已经被相机cull掉了
        private bool m_cull = false;
        #endregion

        public bool TagActive
        {
            get { return m_tagActive; }
        }

        public void Awake(Transform transform)
        {
            if (m_lod == GfxLod.None)
            {
                return;
            }

            if (transform != null)
            {
                m_lodCtr = GfxLodBase.Create(transform);
            }
        }

        public void Deactive()
        {
            if (m_lod == GfxLod.None)
            {
                return;
            }

            // 优化 节约性能
            bool canOptimize = true;

            // fx maker里面不能这么做 特效想看效果
#if UNITY_EDITOR 
            if (SceneManager.GetActiveScene().name == "sceneFXMaker")
            {
                canOptimize = false;
            }
#endif

            if (canOptimize && Application.isPlaying && m_lodCtr != null && m_lodCtr.NodeType != GfxNodeType.Transform)
            {
                SetActive(false);
            }
        }

        public bool UpdateTag(List<string> activeTags)
        {
            bool newActive;

            if (m_tags == null || m_tags.Length == 0)
            {
                newActive = true;
            }
            else
            {
                newActive = false;
                for (int i = 0; i < m_tags.Length; ++i)
                {
                    if (activeTags.Contains(m_tags[i]))
                    {
                        newActive = true;
                        break;
                    }
                }
            }

            if (newActive != m_tagActive)
            {
                m_tagActive = newActive;
                return true;
            }

            return false;
        }

        public void ExceLod(GfxLod lod)
        {
            if (m_lod == GfxLod.None)
            {
                return;
            }

            SetActive((int)lod >= (int)m_lod && m_tagActive && m_outsideActive);
        }

        /// <summary>
        /// 外部想要去控制显示隐藏用这个接口
        /// </summary>
        /// <param name="active"></param>
        /// <returns></returns>
        public bool SetActiveByOutside(bool active)
        {
            if (m_outsideActive != active)
            {
                m_outsideActive = active;
                return true;
            }
            return false;
        }

        public void SetActive(bool active)
        {
            if (m_lod == GfxLod.None)
            {
                return;
            }

            if (m_lodCtr != null)
            {
                if (m_cull)
                    active = false;

                //bool oldActive = m_lodCtr.IsActive();

                //if (oldActive != active)
                {
                    m_lodCtr.SetActive(active);
                }
            }
        }

        public bool IsAwaked()
        {
            return m_lodCtr != null && m_lodCtr.IsReady();
        }

        public bool IsActive()
        {
            if (m_lod == GfxLod.None)
            {
                return true;
            }

            if (m_lodCtr != null)
            {
                return m_lodCtr.IsActive();
            }

            return false;
        }

        public Renderer GetRenderer()
        {
            if (m_lodCtr != null && (m_lodCtr.NodeType == GfxNodeType.Renderer || m_lodCtr.NodeType == GfxNodeType.ParticleSystem))
            {
                return (m_lodCtr as GfxLodRenderer).GetRenderer();
            }
            else
            {
                return null;
            }
        }

        public int GetParticleCount()
        {
            if (m_lodCtr != null)
            {
                if (m_lodCtr.NodeType == GfxNodeType.ParticleSystem)
                    return (m_lodCtr as GfxLodParticleSystemRenderer).GetParticleCount();
            }

            return 0;
        }

        public bool SetCull(bool cull)
        {
            if (m_cull != cull)
            {
                m_cull = cull;

                return true;
            }

            return false;
        }
    }
}
