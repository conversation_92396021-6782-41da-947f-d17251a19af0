using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GfxFramework
{
    public class GfxSceneAdapter : MonoBehaviour
    {
        protected List<Renderer> m_renderers = new List<Renderer>();

        protected List<Camera> m_cameras = new List<Camera>();

        protected List<Light> m_lights = new List<Light>();

        protected CameraClearFlags[] m_cameraClearFlags;
        protected Color[] m_cameraBGColors;
        //protected float[] m_cameraFar;

        protected Color[] m_lightColors;
        protected MaterialPropertyBlock m_propertyBlock;
        protected int m_darkId;

        protected List<Renderer> m_runtimeRenderers = new List<Renderer>();

        protected bool m_playing = false;

        public void Awake()
        {
            m_propertyBlock = new MaterialPropertyBlock();
            m_darkId = Shader.PropertyToID("_dark");

            RefershComponents();
        }

        public void RefershComponents()
        {
            TKFrame.TKDictionary<int, int> dict = new TKFrame.TKDictionary<int, int>();
            for (int i = 0; i < m_cameras.Count; ++i)
            {
                var c = m_cameras[i];
                if (c == null) continue;
                if (!dict.ContainsKey(c.GetInstanceID()))
                {
                    dict.Add(c.GetInstanceID(), i);
                }
            }
            for (int i = 0; i < m_lights.Count; ++i)
            {
                var l = m_lights[i];
                if (l == null) continue;
                if (!dict.ContainsKey(l.GetInstanceID()))
                {
                    dict.Add(l.GetInstanceID(), i);
                }
            }

            gameObject.GetComponentsInChildren<Renderer>(true, m_renderers);
            gameObject.GetComponentsInChildren<Camera>(m_cameras);
            gameObject.GetComponentsInChildren<Light>(m_lights);

            var oldCamBGColors = m_cameraBGColors;
            var oldCamClearFlags = m_cameraClearFlags;
            var oldLightColors = m_lightColors;

            m_cameraBGColors = new Color[m_cameras.Count];
            m_cameraClearFlags = new CameraClearFlags[m_cameras.Count];
            for (int i = 0; i < m_cameras.Count; ++i)
            {
                var c = m_cameras[i];
                m_cameraBGColors[i] = c.backgroundColor;
                m_cameraClearFlags[i] = c.clearFlags;

                if (dict.TryGetValue(c.GetInstanceID(), out int index))
                {
                    if (oldCamBGColors != null && oldCamBGColors.Length > index && index >= 0)
                    {
                        m_cameraBGColors[i] = oldCamBGColors[index];
                    }
                    if (oldCamClearFlags != null && oldCamClearFlags.Length > index && index >= 0)
                    {
                        m_cameraClearFlags[i] = oldCamClearFlags[index];
                    }
                }
            }

            m_lightColors = new Color[m_lights.Count];
            for (int i = 0; i < m_lights.Count; ++i)
            {
                var l = m_lights[i];
                m_lightColors[i] = l.color;

                if (dict.TryGetValue(l.GetInstanceID(), out int index))
                {
                    if (oldLightColors != null && oldLightColors.Length > index && index >= 0)
                    {
                        m_lightColors[i] = oldLightColors[index];
                    }
                }
            }
        }

        public bool StartDark()
        {
            if (m_playing)
                return false;

            m_playing = true;

            for (int i = 0; i < m_cameras.Count; ++i)
            {
                if (m_cameras[i] != null)
                    m_cameras[i].clearFlags = CameraClearFlags.SolidColor;
                //m_cameras[i].farClipPlane = 40f;
            }

            if (m_runtimeRenderers != null)
            {
                m_runtimeRenderers.Clear();
                for (int i = 0; i < m_renderers.Count; ++i)
                {
                    if (m_renderers[i] != null && m_renderers[i].enabled)
                        m_runtimeRenderers.Add(m_renderers[i]);
                }
            }

            return true;
        }

        public void ResetDark()
        {
            if (!m_playing)
                return;

            m_playing = false;

            Renderer r;
            for (int i = 0; i < m_runtimeRenderers.Count; ++i)
            {
                r = m_runtimeRenderers[i];
                if (r != null)
                {
                    r.GetPropertyBlock(m_propertyBlock);
                    m_propertyBlock.SetFloat(m_darkId, 0);
                    r.SetPropertyBlock(m_propertyBlock);
                    if (!r.enabled)
                    {
                        r.enabled = true;
                    }
                }
            }
            m_runtimeRenderers.Clear();

            for (int i = 0; i < m_cameras.Count; ++i)
            {
                if (m_cameras[i] == null)
                    continue;

                m_cameras[i].clearFlags = m_cameraClearFlags[i];
                m_cameras[i].backgroundColor = m_cameraBGColors[i];
                //m_cameras[i].farClipPlane = m_cameraFar[i];
            }

            for (int i = 0; i < m_lights.Count; ++i)
            {
                if (m_lights[i] == null)
                    continue;

                m_lights[i].color = m_lightColors[i];
            }
        }

        public void UpdateDark(float dark)
        {
            if (!m_playing)
                return;

            bool hide = dark >= 1;
            Renderer r;

            for (int i = 0; i < m_runtimeRenderers.Count; ++i)
            {
                r = m_runtimeRenderers[i];
                if (r != null)
                {
                    if (hide)
                    {
                        if (r.enabled)
                        {
                            r.enabled = false;
                        }
                    }
                    else
                    {
                        if (!r.enabled)
                        {
                            r.enabled = true;
                        }

                        r.GetPropertyBlock(m_propertyBlock);
                        m_propertyBlock.SetFloat(m_darkId, dark);
                        r.SetPropertyBlock(m_propertyBlock);
                    }
                }
            }

            Color color;

            for (int i = 0; i < m_cameras.Count; ++i)
            {
                if (m_cameras[i] == null)
                    continue;
                color = m_cameraBGColors[i];
                color *= (1 - dark);
                m_cameras[i].backgroundColor = color;
            }

            for (int i = 0; i < m_lights.Count; ++i)
            {
                if (m_lights[i] == null)
                    continue;
                color = m_lightColors[i];
                color *= (1 - dark);
                m_lights[i].color = color;
            }
        }
    }
}
