using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GfxFramework
{
    [Serializable]
    public class GfxRandomModuleBase
    {
        public bool m_enable = false;
        public bool m_useCustomTimeControl = false;
        public GfxRandom m_delay = new GfxRandom() { randomType = GfxRandom.RandomType.Constant };
        public GfxRandom m_interval = new GfxRandom() { randomType = GfxRandom.RandomType.Constant };

        protected bool m_firstRandom = true;
        protected float m_lastRandomTime = float.MinValue;
        protected float m_runtimeDelay = 0;
        protected float m_runtimeInterval = 0;
        public virtual void Active(Transform transform)
        {
            m_firstRandom = true;
            m_lastRandomTime = float.MinValue;

            TimeRandom();
        }

        public virtual void Deactive()
        {
            m_firstRandom = true;
            m_lastRandomTime = float.MinValue;
            m_runtimeDelay = 0;
            m_runtimeInterval = 0;
        }

        public virtual void Tick(float time)
        {
            if (!m_enable)
            {
                return;
            }

            if (m_useCustomTimeControl)
            {
                if (m_firstRandom)
                {
                    if (m_runtimeDelay > 0)
                    {
                        if (time > m_runtimeDelay)
                        {
                            Random();
                            m_lastRandomTime = time;
                            m_firstRandom = false;
                        }
                    }
                    else
                    {
                        Random();
                        m_lastRandomTime = time;
                        m_firstRandom = false;
                    }
                }
                else if (m_runtimeInterval > 0)
                {
                    if (time - m_lastRandomTime > m_runtimeInterval)
                    {
                        Random();
                        m_lastRandomTime = time;
                    }
                }
            }
        }

        public virtual void Random()
        {
            TimeRandom();
        }

        private void TimeRandom()
        {
            if (!m_useCustomTimeControl)
            {
                m_runtimeDelay = 0;
                m_runtimeInterval = 0;
                return;
            }

            m_runtimeDelay = m_delay.Random();
            m_runtimeInterval = m_interval.Random();

            //Debug.Log(GetType().Name +  " 随机延迟时间: " + m_runtimeDelay + " 下一次随机间隔时间: " + m_runtimeInterval);
        }
    }
}
