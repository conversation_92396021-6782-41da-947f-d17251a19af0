using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using UnityEngine.Playables;

namespace GfxFramework
{
    [CustomEditor(typeof(GfxRoot))]
    public class GfxRootInspector : Editor
    {
        private int selectLodId = 0;
        private string[] lodNameList;
        private GfxLod[] lodValueList;

        protected double m_lastTime;
        protected double m_startTime;

        private GfxTimeDrawer timeDrawer = new GfxTimeDrawer();
        protected GfxRootTreeView treeView = new GfxRootTreeView();
        private GfxRootLodView lodView = new GfxRootLodView();

        public List<string> m_activeTags = new List<string>();

        protected static GfxConfigScriptObject ms_gfxConfig = null;

        private bool needCombine;

        #region 性能测试相关
        private GfxRootProfiler m_profiler = new GfxRootProfiler();
        #endregion

        private GfxPreviewView m_preview = new GfxPreviewView();

        protected virtual void OnEnable()
        {
            GfxRoot gfxRoot = (GfxRoot)target;

            if (!Application.isPlaying && !EditorUtility.IsPersistent(target))
            {
                GfxUtil.RefershGfxRoot(gfxRoot);
            }

            //Debug.Log(PrefabUtility.GetPrefabAssetType(target));

            ms_gfxConfig = GfxConfigScriptObject.Cfg;
            if (!Application.isPlaying)
            {
                gfxRoot.UpdateTag(m_activeTags);
            }

            InitLod();

            RefershTree();

            EditorApplication.hierarchyWindowItemOnGUI -= OnHierarchyItemOnGUI;
            EditorApplication.hierarchyWindowItemOnGUI += OnHierarchyItemOnGUI;
            SceneView.onSceneGUIDelegate -= OnSceneViewGUI;
            SceneView.onSceneGUIDelegate += OnSceneViewGUI;
            EditorApplication.update -= EditorUpdate;
            EditorApplication.update += EditorUpdate;
            PrefabUtility.prefabInstanceUpdated -= OnPrefabInstanceUpdated;
            PrefabUtility.prefabInstanceUpdated += OnPrefabInstanceUpdated;

            if (Application.isPlaying)
            {
                ChangePlayMode(GfxTimeDrawer.PlayMode.Play);
            }

            if (m_preview == null)
                m_preview = new GfxPreviewView();
            m_preview.Initialize();

            if (m_profiler != null && EditorPrefs.HasKey("m_profiler.m_enable"))
                m_profiler.m_enable = EditorPrefs.GetBool("m_profiler.m_enable");

            if (m_profiler.m_enable)
                m_profiler.Initialize(gfxRoot);
            //#if PARTICLESYSTEM_DATA
            //            if (!EditorUtility.IsPersistent(target) && !Application.isPlaying)
            //            {
            //                gfxRoot.UnpackParticleData();
            //                Debug.Log("特效: " + gfxRoot.name + " 解压成功");
            //            }
            //#endif

            if (!Application.isPlaying)
            {
                needCombine = GfxTextureCombineTool.AnalyseGameObject(gfxRoot.gameObject).Count > 0;
            }
        }

        protected virtual void OnDisable()
        {
            EditorApplication.hierarchyWindowItemOnGUI -= OnHierarchyItemOnGUI;
            SceneView.onSceneGUIDelegate -= OnSceneViewGUI;
            EditorApplication.update -= EditorUpdate;
            PrefabUtility.prefabInstanceUpdated -= OnPrefabInstanceUpdated;
            if (timeDrawer == null)
                timeDrawer = new GfxTimeDrawer();
            if (timeDrawer.Mode != GfxTimeDrawer.PlayMode.Stop)
                OnPlayModeChanged(GfxTimeDrawer.PlayMode.Stop);

            if (m_profiler != null)
            {
                m_profiler.Uninitialize();
            }
        }

        private void OnPrefabInstanceUpdated(GameObject instance)
        {
            GfxRoot gfxRoot = (GfxRoot)target;
            if (gfxRoot != null && instance == gfxRoot.gameObject)
            {
                bool dirty = false;
                if (GfxUtil.AutoCleanUnuseResources(instance))
                {
                    dirty = true;
                }
//#if PARTICLESYSTEM_DATA
//                if (gfxRoot.ConvertEffectToPsData())
//                {
//                    dirty = true;
//                    Debug.Log("特效: " + instance.name + " 压缩成功");
//                }
//#endif

                if (timeDrawer.Mode != GfxTimeDrawer.PlayMode.Stop)
                {
                    Debug.Log(PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(instance) + " need resave");

                    timeDrawer.Mode = GfxTimeDrawer.PlayMode.Stop;

                    OnPlayModeChanged(timeDrawer.Mode);

                    dirty = true;
                }

                if (dirty)
                {
                    PrefabUtility.ApplyPrefabInstance(instance, InteractionMode.UserAction);
                }
            }
        }

        private void InitLod()
        {
            List<string> l = new List<string>();
            List<GfxLod> vl = new List<GfxLod>();

            for (int i = (int)GfxLod.Ultra; i > (int)GfxLod.None; --i)
            {
                GfxLod lod = (GfxLod)i;
                l.Add(lod.ToString());
                vl.Add(lod);
            }

            // l.Add(GfxLod.SeaLow.ToString());
            // vl.Add(GfxLod.SeaLow);

            lodNameList = l.ToArray();
            lodValueList = vl.ToArray();

            selectLodId = vl.IndexOf(GfxManager.Instance.Lod);
        }

        protected void RefershTree()
        {
            GfxRoot gfxRoot = (GfxRoot)target;
            treeView.ClearAllItem();
            treeView.tag += 1;
            FetchTransform(gfxRoot.transform, treeView, gfxRoot, 1);
        }

        private void FetchTransform(Transform t, GfxRootTreeViewItem item, GfxRoot gfxRoot, int depth)
        {
            for (int i = 0; i < t.childCount; ++i)
            {
                var child = t.GetChild(i);
                var gfxNode = GfxUtil.GetGfxNode(gfxRoot, child);
                if (gfxNode != null)
                {
                    var newItem = new GfxRootTreeViewItem(child.name, depth < 3, gfxNode, child);
                    newItem.OnClickMenu = OnClickItemMenu;
                    item.AddItem(newItem);
                    FetchTransform(child, newItem, gfxRoot, depth + 1);
                }
                else
                {
                    var newItem = new GfxRootTreeViewItem(child.name, depth < 3, null, child);
                    newItem.OnClickMenu = OnClickItemMenu;
                    item.AddItem(newItem);
                    FetchTransform(child, newItem, gfxRoot, depth + 1);
                }
            }
        }

        private GUIStyle redStyle;
        private GUIStyle greenStyle;

        private void DrawOptimizeTools(GfxRoot gfxRoot)
        {
            GfxEditorUtility.GroupHeader("优化工具");
            GfxEditorUtility.BeginGroup();

            GUILayout.BeginHorizontal();
            if (redStyle == null)
            {
                redStyle = new GUIStyle(EditorStyles.label);
                redStyle.normal.textColor = Color.red;
            }

#if PARTICLESYSTEM_DATA
            if (!gfxRoot.NoNeedConfig() && !gfxRoot.UnpackParticleSystemData)
            {
                GUILayout.Label("请先解压特效", redStyle);
            }
            else
#endif
            if (needCombine)
            {
                GUILayout.Label("可合并贴图、材质", redStyle);
                if (GUILayout.Button("合并", GUILayout.Width(100)))
                {
                    GfxTextureCombineTool.ShowWindow(gfxRoot.gameObject);
                }
            }
            else
            {
                if (greenStyle == null)
                {
                    greenStyle = new GUIStyle(EditorStyles.label);
                    greenStyle.normal.textColor = Color.green;
                }
                if (!Application.isPlaying)
                    GUILayout.Label("无需合并贴图、材质", greenStyle);
                else
                    GUILayout.Label("游戏运行中，无法检测是否需要合并贴图、材质", greenStyle);
            }
            GUILayout.EndHorizontal();
#if PARTICLESYSTEM_DATA
            if (gfxRoot.NoNeedConfig() || gfxRoot.UnpackParticleSystemData)
#endif
            {
                // 这个按钮运行时要开起来 方便优化
                bool enable = GUI.enabled;
                if (Application.isPlaying)
                {
                    GUI.enabled = true;
                }
                if (GUILayout.Button("Drawcall优化"))
                {
                    GfxDrawcallTool.ShowWindow(gfxRoot.gameObject);
                }
                if (Application.isPlaying)
                {
                    GUI.enabled = enable;
                }
            }

            GfxEditorUtility.EndGroup();
        }

        private void DrawPreviewTools(GfxRoot gfxRoot)
        {
            GfxEditorUtility.GroupHeader("预览方式");
            GfxEditorUtility.BeginGroup();

            m_preview.Draw();

            GfxEditorUtility.EndGroup();
        }

        private void OnHierarchyItemOnGUI(int instanceID, Rect selectionRect)
        {
            if (instanceID == Selection.activeInstanceID)
                CheckKey();
        }

        private void OnSceneViewGUI(SceneView sceneView)
        {
            if (showBounds)
                ShowBounds();

            CheckKey();
        }

        public void ShowBounds()
        {
            GfxRoot gfxRoot = (GfxRoot)target;
            var pss = gfxRoot.particleSystems;
            for (int i = 0; i < pss.Count; ++i)
            {
                var ps = pss[i];
                if (ps != null)
                {
                    ParticleSystemRenderer particleSystemRenderer = ps.GetComponent<ParticleSystemRenderer>();

                    var oldCol = Handles.color;
                    Handles.color = Color.yellow;
                    var worldBounds = particleSystemRenderer.bounds;
                    Handles.DrawWireCube(worldBounds.center, worldBounds.size);
                    Handles.color = oldCol;
                }
            }
        }

        private Camera cam;
        private bool showBounds = false;
        private bool showSphereBounds = false;

        public void DrawCullTools(GfxRoot gfxRoot)
        {
            if (gfxRoot.HasCull())
            {
                GfxEditorUtility.GroupHeader("剔除预览");
                GfxEditorUtility.BeginGroup();

                if (cam == null)
                    cam = Camera.main;
                cam = EditorGUILayout.ObjectField("相机", cam, typeof(Camera), true) as Camera;
                showBounds = EditorGUILayout.Toggle("显示Bounds", showBounds);
                bool c = EditorGUILayout.Toggle("显示SphereBounds", showSphereBounds);
                if (showSphereBounds != c)
                {
                    showSphereBounds = c;

                    gfxRoot.SetCullBoundsVisible(showSphereBounds);
                }


                GfxEditorUtility.EndGroup();
            }
        }

        public override void OnInspectorGUI()
        {
            GfxRoot gfxRoot = (GfxRoot)target;

            base.OnInspectorGUI();

            CheckKey();

            if (Application.isPlaying)
                DrawCullTools(gfxRoot);

            if (Application.isPlaying)
            {
                GUI.enabled = false;
            }

            DrawPreviewTools(gfxRoot);

            DrawOptimizeTools(gfxRoot);

            EditorGUI.BeginChangeCheck();
            GfxEditorUtility.GroupHeader("时间控制");
            GfxEditorUtility.BeginGroup();
            timeDrawer.OnInspectorGUI(gfxRoot.m_timeSystem, this, OnPlayModeChanged);
            gfxRoot.m_playFinishedHide = EditorGUILayout.Toggle("播放完成自动隐藏特效:", gfxRoot.m_playFinishedHide);
            if (Application.isPlaying)
            {
                Time.timeScale = EditorGUILayout.Slider("时间缩放(只影响编辑器)", Time.timeScale, 0, 10);
            }

            if (timeDrawer.Mode == GfxTimeDrawer.PlayMode.Stop)
            {
                if (GUILayout.Button("根据子节点自动设置时间"))
                {
                    AutoSetTime(gfxRoot);
                }
            }

            GfxEditorUtility.EndGroup();

            if (ms_gfxConfig != null && ms_gfxConfig.TagGroupList.Count != 0)
            {
                GUILayout.Space(10);
                GfxEditorUtility.GroupHeader("标签系统");
                GfxEditorUtility.BeginGroup();
                for (int i = 0; i < ms_gfxConfig.TagGroupList.Count; ++i)
                {
                    var tagGroup = ms_gfxConfig.TagGroupList[i];

                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField(tagGroup.GroupName);
                    EditorGUI.BeginChangeCheck();
                    var activeIndex = FindActiveTagIndex(tagGroup.Tags);
                    int newActiveIndex = GUILayout.Toolbar(activeIndex, tagGroup.Tags);

                    if (EditorGUI.EndChangeCheck())
                    {
                        if (activeIndex != -1)
                        {
                            m_activeTags.Remove(tagGroup.Tags[activeIndex]);
                        }

                        if (newActiveIndex != -1)
                        {
                            m_activeTags.Add(tagGroup.Tags[newActiveIndex]);
                        }

                        gfxRoot.UpdateTag(m_activeTags);
                    }

                    EditorGUILayout.EndHorizontal();
                }

                GfxEditorUtility.EndGroup();
            }

            GUILayout.Space(10);
            GfxEditorUtility.GroupHeader("节点-Lod");
            GfxEditorUtility.BeginGroup();
            if (Application.isPlaying)
            {
                selectLodId = Array.IndexOf(lodValueList, gfxRoot.Lod);
            }

            var newSelectId = GUILayout.Toolbar(selectLodId, lodNameList);

            if (newSelectId != selectLodId)
            {
                selectLodId = newSelectId;

                var newLod = lodValueList[selectLodId];

                GfxManager.Instance.Lod = newLod;
                gfxRoot.UpdateTag(m_activeTags, false);
                gfxRoot.ExceLod(newLod, true);

                m_profiler.Initialize(gfxRoot);

                Repaint();
            }

            lodView.OnInspectorGUI(gfxRoot);

            treeView.OnInspectorGUI(this);
            GfxEditorUtility.EndGroup();
            GUI.enabled = true;
            if (EditorGUI.EndChangeCheck())
            {
                EditorUtility.SetDirty(target);
            }
        }

        private int FindActiveTagIndex(string[] tags)
        {
            for (int i = 0; i < tags.Length; ++i)
            {
                if (m_activeTags.Contains(tags[i]))
                {
                    return i;
                }
            }

            return -1;
        }

        private void AutoSetTime(GfxRoot gfxRoot)
        {
            float time = 0f;
            var gfxAnimations = gfxRoot.GetComponentsInChildren<GfxAnimation>();

            foreach (var gfxAnimation in gfxAnimations)
            {
                time = Mathf.Max(time, gfxAnimation.m_timeSystem.m_startTime + gfxAnimation.m_timeSystem.m_endTime);
            }

            var particleSystems = gfxRoot.GetComponentsInChildren<ParticleSystem>();

            foreach (var ps in particleSystems)
            {
                var startDelay = ps.main.startDelay.constant;

                if (ps.main.startDelay.mode != ParticleSystemCurveMode.Constant)
                {
                    startDelay = ps.main.startDelay.constantMax;
                }

                var pst = startDelay + ps.main.duration;

                time = Mathf.Max(pst, time);
            }

            gfxRoot.m_timeSystem.m_endTime = time;

            EditorUtility.SetDirty(gfxRoot);
        }

        public void OnPlayModeChanged(GfxTimeDrawer.PlayMode mode)
        {
            GfxRoot gfxRoot = target as GfxRoot;

            if (gfxRoot == null)
            {
                return;
            }

            if (Application.isPlaying || EditorUtility.IsPersistent(target))
            {
                return;
            }

            // 按钮点击后会进入这个if
            if (mode == GfxTimeDrawer.PlayMode.Play)
            {
#if PARTICLESYSTEM_DATA
                if (gfxRoot.UnpackParticleData())
                    Debug.Log("[播放]解压成功！");
#endif
                //Debug.Log("play start: " + Time.realtimeSinceStartup);
                if (Application.isPlaying)
                {
                    GfxUtil.RefershGfxRoot(gfxRoot);
                }

                // 这个是预览方式 飞行道具的直线和绕着圆圈飞
                m_preview.Stop();
                m_preview.Start(gfxRoot);

                // 这里是性能分析
                m_profiler.Initialize(gfxRoot);
                
                // 如果是激活的  先关掉 避免重复进入
                if (gfxRoot.IsActive)
                {
                    gfxRoot.Deactive(Time.time);
                }

                // awake里面会做一些初始化的逻辑 会调用各个Node的Awake 各个Node的Awake会收集Node下面的GfxSystem，然后调用GfxSystem的Awake
                gfxRoot.Awake();
                // 激活
                gfxRoot.Active(Time.time);
                // Tag系统
                gfxRoot.UpdateTag(m_activeTags, false);
                // 刷新一下LOD
                gfxRoot.ExceLod(lodValueList[selectLodId], true);
                
                // 播粒子特效
                var psArr = gfxRoot.GetComponentsInChildren<ParticleSystem>();
                for (int i = 0; i < psArr.Length; ++i)
                {
                    var ps = psArr[i];

                    ps.Stop(true, ParticleSystemStopBehavior.StopEmittingAndClear);
                    ps.Play(true);
                }

                // 。。。
                var animtorArr = gfxRoot.GetComponentsInChildren<Animator>();
                for (int i = 0; i < animtorArr.Length; ++i)
                {
                    var animator = animtorArr[i];

                    if (animator != null)
                    {
                        animator.gameObject.SetActive(false);
                        animator.gameObject.SetActive(true);
                    }
                }

                //var renderers = gfxRoot.GetComponentsInChildren<Renderer>();
                //foreach (var renderer in renderers)
                //{
                //    if (renderer.gameObject.activeSelf && renderer.enabled)
                //    {
                //        renderer.enabled = false;
                //        renderer.enabled = true;
                //    }
                //}
                //Repaint();

                //m_profiler.InitializeDrawcall(gfxRoot);

                m_lastTime = m_startTime = Time.time;

                //Debug.Log("play start finished: " + Time.realtimeSinceStartup);
            }
            else if (mode == GfxTimeDrawer.PlayMode.NextFrame)
            {
                UpdateGfxFrame();
            }
            else
            {
                Debug.Log("[GfxRoot]play end: " + Time.realtimeSinceStartup);

                m_preview.Stop();

                if (mode == GfxTimeDrawer.PlayMode.Stop)
                {
                    gfxRoot.Deactive(Time.time);

                    var psArr = gfxRoot.GetComponentsInChildren<ParticleSystem>();
                    for (int i = 0; i < psArr.Length; ++i)
                    {
                        var ps = psArr[i];
                        ps.Stop(true, ParticleSystemStopBehavior.StopEmittingAndClear);
                    }
                }

                //Debug.Log("play end finished: " + Time.realtimeSinceStartup);
            }
        }

        private void UpdateGfxFrame()
        {

            GfxRoot gfxRoot = target as GfxRoot;

            if (gfxRoot == null)
            {
                return;
            }

            // 每帧更新各个GfxSystem
            double newTime = Time.time;
            float liveTime = (float)(newTime - m_startTime);

            // 更新其他东西
            float deltaTime = (float)(newTime - m_lastTime);

            //Debug.Log("play update: " + Time.realtimeSinceStartup + " liveTime: " + liveTime + " deltaTime: " + deltaTime + " m_lastTime: " + m_lastTime + " newTime: " + newTime);

            m_lastTime = newTime;

            gfxRoot.RunByLifeTime(liveTime);

  
            var psArr = gfxRoot.GetComponentsInChildren<ParticleSystem>();

            for (int i = 0; i < psArr.Length; ++i)
            {
                var ps = psArr[i];
                if (!ps.isPlaying)
                    ps.Play();
                ps.Simulate(deltaTime, false, false, false);
                //EditorUtility.SetDirty(ps);
            }

            var rendererArr = gfxRoot.GetComponentsInChildren<Renderer>();

            for (int i = 0; i < rendererArr.Length; ++i)
            {
                EditorUtility.SetDirty(rendererArr[i]);
            }

            var animArr = gfxRoot.GetComponentsInChildren<Animation>();

            for (int i = 0; i < animArr.Length; ++i)
            {
                var anim = animArr[i];

                if (anim != null && anim.clip != null)
                {
                    anim.clip.SampleAnimation(anim.gameObject, liveTime);
                    // AnimationMode.SampleAnimationClip(state.activeRootGameObject, state.activeAnimationClip, time.time);
                }
            }

            var animtorArr = gfxRoot.GetComponentsInChildren<Animator>();

            for (int i = 0; i < animtorArr.Length; ++i)
            {
                var animator = animtorArr[i];

                if (animator != null)
                {
                    animator.Update(deltaTime);
                }
            }

            var timelineArr = gfxRoot.GetComponentsInChildren<PlayableDirector>();
            for (int i = 0; i < timelineArr.Length; ++i)
            {
                var timeline = timelineArr[i];

                if (timeline != null)
                {
                    timeline.time += deltaTime;
                }
            }

            if (gfxRoot.m_timeSystem.Phase >= 1 && !gfxRoot.m_timeSystem.m_loop)
            {
                ChangePlayMode(GfxTimeDrawer.PlayMode.Stop);
                Repaint();
            }

            m_profiler.Update();
            m_preview.Update();
        }

        private void CheckKey()
        {
            Event e = Event.current;
            if (e != null && e.type == EventType.KeyDown && e.keyCode == KeyCode.D)
            {
                /* 有缘再做逐帧播放
                if (e.alt)
                {
                    if (timeDrawer.Mode != GfxTimeDrawer.PlayMode.Stop)
                    {
                        timeDrawer.Mode = GfxTimeDrawer.PlayMode.Stop;
                        OnPlayModeChanged(GfxTimeDrawer.PlayMode.Stop);
                    }


                }
                else */
                if (timeDrawer.Mode != GfxTimeDrawer.PlayMode.Play)
                {
                    timeDrawer.Mode = GfxTimeDrawer.PlayMode.Play;
                    OnPlayModeChanged(GfxTimeDrawer.PlayMode.Play);
                }
                else
                {
                    timeDrawer.Mode = GfxTimeDrawer.PlayMode.Stop;
                    OnPlayModeChanged(GfxTimeDrawer.PlayMode.Stop);

                    timeDrawer.Mode = GfxTimeDrawer.PlayMode.Play;
                    OnPlayModeChanged(GfxTimeDrawer.PlayMode.Play);
                }
            }
        }

        public void EditorUpdate()
        {
            if (Application.isPlaying || EditorUtility.IsPersistent(target))
            {
                return;
            }

            if (timeDrawer.Mode == GfxTimeDrawer.PlayMode.Play)
            {
                UpdateGfxFrame();
            }
        }

        public void ChangePlayMode(GfxTimeDrawer.PlayMode mode)
        {
            timeDrawer.Mode = mode;
            OnPlayModeChanged(mode);
        }

        public override void OnPreviewSettings()
        {
            bool profilerEnable = GUILayout.Toggle(m_profiler.m_enable, "性能分析", "preButton");
            if (profilerEnable != m_profiler.m_enable)
            {
                m_profiler.m_enable = profilerEnable;
                EditorPrefs.SetBool("m_profiler.m_enable", profilerEnable);
            }

            if (GUILayout.Button("全面分析", EditorStyles.toolbarButton))
            {
#if PARTICLESYSTEM_DATA
                (target as GfxRoot).UnpackParticleData();
#endif
                GfxProfilerWindow.Open((target as GfxRoot).transform);
            }
        }

        public override bool HasPreviewGUI()
        {
            return m_profiler != null;
        }

        public override void OnPreviewGUI(Rect r, GUIStyle background)
        {
            base.OnPreviewGUI(r, background);

            if (lodValueList != null && lodValueList.Length > selectLodId && selectLodId >= 0)
            {
                m_profiler.OnDrawPerview(r, background, selectLodId);
            }
            else
            {
                m_profiler.OnDrawPerview(r, background, 0);
            }
        }

        public virtual void OnClickItemMenu(Transform transform)
        {

        }
    }
}
