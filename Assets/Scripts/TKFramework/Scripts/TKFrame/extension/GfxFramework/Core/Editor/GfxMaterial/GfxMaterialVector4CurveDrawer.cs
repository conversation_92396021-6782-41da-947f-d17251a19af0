using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;

namespace GfxFramework
{
    public static class GfxMaterialVector4CurveDrawer
    {
        public static void OnInspectorGUI(GfxMaterialVector4Curve curve, Editor editor, List<GfxMaterialVector4Curve> ownerList)
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(curve.m_propertyName);
            if (GUILayout.Button("X", GUILayout.Width(20)))
            {
                ownerList.Remove(curve);
                EditorUtility.SetDirty(editor.target);
            }

            EditorGUILayout.EndHorizontal();

            GfxCurveDrawer.OnInspectorGUI("    x", curve.m_xCurve, editor);
            GfxCurveDrawer.OnInspectorGUI("    y", curve.m_yCurve, editor);
            GfxCurveDrawer.OnInspectorGUI("    z", curve.m_zCurve, editor);
            GfxCurveDrawer.OnInspectorGUI("    w", curve.m_wCurve, editor);
        }
    }
}
