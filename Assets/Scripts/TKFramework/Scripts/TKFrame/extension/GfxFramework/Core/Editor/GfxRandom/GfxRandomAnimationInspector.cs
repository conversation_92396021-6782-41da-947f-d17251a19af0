using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;

namespace GfxFramework
{
    [CustomEditor(typeof(GfxRandomAnimation))]
    public class GfxRandomAnimationInspector : Editor
    {
        protected GfxPositionRandomModuleDrawer m_positionModuleDrawer = new GfxPositionRandomModuleDrawer();
        protected GfxRotationRandomModuleDrawer m_ratationModuleDrawer = new GfxRotationRandomModuleDrawer();
        protected GfxActiveRandomModuleDrawer m_activeModuleDrawer = new GfxActiveRandomModuleDrawer();

        public override void OnInspectorGUI()
        {
            GfxRandomAnimation gfx = target as GfxRandomAnimation;

            if (gfx == null)
            {
                return;
            }

            EditorGUI.BeginChangeCheck();

            GfxRandomDrawer.OnInspectorGUI("延迟:(s)", gfx.m_delay, this);
            if (gfx.m_delay.minValue < 0)
            {
                gfx.m_delay.minValue = 0;
            }

            if (gfx.m_delay.maxValue < 0)
            {
                gfx.m_delay.maxValue = 0;
            }

            GfxRandomDrawer.OnInspectorGUI("重复:(s)", gfx.m_interval, this);
            if (gfx.m_interval.minValue < 0)
            {
                gfx.m_interval.minValue = 0;
            }

            if (gfx.m_interval.maxValue < 0)
            {
                gfx.m_interval.maxValue = 0;
            }

            m_positionModuleDrawer.OnInspectorGUI("位置", gfx.m_position, this, null);
            m_ratationModuleDrawer.OnInspectorGUI("旋转", gfx.m_rotation, this, null);
            m_activeModuleDrawer.OnInspectorGUI("显示控制", gfx.m_active, this, null);

            if (EditorGUI.EndChangeCheck())
            {
                EditorUtility.SetDirty(gfx);
            }
        }
    }
}
