using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;

namespace GfxFramework
{
    [CustomEditor(typeof(GfxGlobalShaderAnimation))]
    public class GfxGlobalShaderAnimationInspector : Editor
    {
        protected GfxSystemTimeControl m_timeControl = new GfxSystemTimeControl();

        private void OnEnable()
        {
            GfxGlobalShaderAnimation gfx = target as GfxGlobalShaderAnimation;
            if (gfx == null)
            {
                return;
            }

            if (Application.isPlaying)
            {
                var gfxRoot = gfx.GetComponentInParent<GfxRoot>();

                if (gfxRoot != null)
                {
                    GfxUtil.Reawake(gfxRoot, gfx.transform);
                }
            }

            if (m_timeControl != null)
            {
                m_timeControl.OnEnable(gfx.gameObject, gfx.m_timeSystem, this);
            }
        }

        public void OnDisable()
        {
            if (m_timeControl != null)
            {
                m_timeControl.OnDisable();
            }
        }

        public override void OnInspectorGUI()
        {
            GfxGlobalShaderAnimation gfx = target as GfxGlobalShaderAnimation;

            if (gfx == null)
            {
                return;
            }

            if (m_timeControl != null)
            {
                m_timeControl.OnInspectorGUI(gfx.m_timeSystem, this);
            }

            GfxEditorUtility.ContentsHeader("全局Shader参数");
            GfxEditorUtility.BeginContents();
            for (int i = 0; i < gfx.m_globalVars.Count; ++i)
            {
                DrawGlobalVar(gfx.m_globalVars[i], gfx.m_globalVars);
            }

            if (GUILayout.Button("添加"))
            {
                gfx.m_globalVars.Add(new GfxGlobalShaderVar());
                EditorUtility.SetDirty(target);
            }

            GfxEditorUtility.EndContents();

        }

        private void DrawGlobalVar(GfxGlobalShaderVar globalVar, List<GfxGlobalShaderVar> ownerList)
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.BeginHorizontal();
            globalVar.m_varName = EditorGUILayout.TextField("参数名：", globalVar.m_varName);
            if (GUILayout.Button("X", GUILayout.Width(20)))
            {
                ownerList.Remove(globalVar);
                EditorUtility.SetDirty(target);
            }

            EditorGUILayout.EndHorizontal();
            var newVarType = (GfxGlobalShaderVarType)EditorGUILayout.EnumPopup("参数类型：", globalVar.m_varType);

            if (globalVar.m_varType != newVarType)
            {
                globalVar.m_varType = newVarType;
                EditorUtility.SetDirty(target);
            }

            switch (globalVar.m_varType)
            {
                case GfxGlobalShaderVarType.Float:
                    {
                        GfxCurveDrawer.OnInspectorGUI("参数值：", globalVar.m_xCurve, this);
                    }
                    break;
                case GfxGlobalShaderVarType.Vector4:
                    {
                        GfxCurveDrawer.OnInspectorGUI("  x:", globalVar.m_xCurve, this);
                        GfxCurveDrawer.OnInspectorGUI("  y:", globalVar.m_yCurve, this);
                        GfxCurveDrawer.OnInspectorGUI("  z:", globalVar.m_zCurve, this);
                        GfxCurveDrawer.OnInspectorGUI("  w:", globalVar.m_wCurve, this);
                    }
                    break;
                case GfxGlobalShaderVarType.Color:
                    {
                        var newColorCurve = EditorGUILayout.GradientField(new GUIContent("颜色值:"), globalVar.m_colorCurve, true);

                        if (newColorCurve != globalVar.m_colorCurve)
                        {
                            globalVar.m_colorCurve = newColorCurve;
                            EditorUtility.SetDirty(target);
                        }
                    }
                    break;
                default:
                    break;
            }

            EditorGUILayout.EndVertical();
        }
    }
}
