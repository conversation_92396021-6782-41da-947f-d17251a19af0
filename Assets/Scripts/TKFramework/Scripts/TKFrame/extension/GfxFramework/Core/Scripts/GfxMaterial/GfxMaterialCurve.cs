using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GfxFramework
{
    [Serializable]
    public abstract class GfxMaterialCurve
    {
        public string m_propertyName;

        protected int m_propertyId;

        public virtual void Initilize(MaterialPropertyBlock propertyBlock)
        {
            m_propertyName = string.Intern(m_propertyName);
            m_propertyId = Shader.PropertyToID(m_propertyName);
        }

        public abstract void Evaluate(MaterialPropertyBlock propertyBlock, float phase, float totalTime);
    }
}
