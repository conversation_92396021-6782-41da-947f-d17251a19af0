using System;

namespace TKFrame
{

    /// <summary>
    /// 【引用计数接口】
    /// 当引用计数到零时，该对象就将释放占有的资源
    /// 以扩展方法注入接口的实现，用途：接口子类中无需重复实现接口，以此替代类似多继承类的功能
    /// </summary>
    public interface IRefCounter : IDisposable
    {

        /// <summary>
        /// 引用计数
        /// </summary>
        int RefCount { get; set; }

    }

    
    /// <summary>
    /// 【扩展方法】注入引用计数的实现函数
    /// </summary>
    public static class ExtendRefCounter
    {
        //public static TKFrame.TKDictionary<string, int> assetDic = new TKFrame.TKDictionary<string, int>();
        /// <summary>
        /// 增加引用计数
        /// </summary>
        public static void Retain(this IRefCounter t)
        {
            //LoadedAsset loadAsset = t as LoadedAsset;
            //if (loadAsset != null)
            //{
            //    string key = loadAsset.BundlePath + "|" + loadAsset.AssetName;
            //    if (assetDic.ContainsKey(key))
            //        assetDic[key]++;
            //    else
            //        assetDic.Add(key, 1);
            //}
            t.RefCount++;
        }

        /// <summary>
        /// 释放引用计数，如果引用计数为0，将触发Dispose释放占有的资源
        /// </summary>
        public static void Release(this IRefCounter t)
        {
            //LoadedAsset loadAsset = t as LoadedAsset;
            //if (loadAsset != null)
            //{
            //    string key = loadAsset.BundlePath + "|" + loadAsset.AssetName;
            //    if (assetDic.ContainsKey(key))
            //    {
            //        assetDic[key]--;
            //        if (assetDic[key] == 0)
            //            assetDic.Remove(key);
            //    }
            //    else
            //        Diagnostic.Error("Asset Release Error:" + key);
            //}
            t.RefCount--;
            if (t.RefCount==0)
            {
                t.Dispose();
            }
        }
    }


}
