using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace TKFrame
{
    public class EventManager : ServiceBehaviour, IEventService
    {
        private EventHandler<EventRaiseEventArgs> _eventRaise; 
        public event EventHandler<EventRaiseEventArgs> EventRaise
        {
            add
            {
                if ( this._eventRaise == null )
                {
                    this._eventRaise = value;
                }
                else
                {
                    this._eventRaise += value;
                }
                
            }
            remove
            {
                this._eventRaise -=  value;
            }
        }

        TKDictionary<string, List<EventHandler>> _dicEventHandlers = new TKDictionary<string, List<EventHandler>>();
        Utility.ObjectPool<EventRaiseEventArgs> _eventArgsPool = new Utility.ObjectPool<EventRaiseEventArgs>(null, null);

        public void Notify(Event notification)
        {
            var args = _eventArgsPool.Get();

            args.RaisedEvent = notification;
            _eventRaise?.Invoke(this, args);

            List<EventHandler> listHandlers;
            if (_dicEventHandlers.TryGetValue(notification.EventName, out listHandlers))
            {
                foreach (var item in listHandlers)
                    item?.Invoke(this, args);

                listHandlers.Clear();
            }

            _eventArgsPool.Release(args);
        }

        public void EventRaiseOnce(EventHandler handler, string eventName)
        {
            List<EventHandler> listHandlers;
            if (_dicEventHandlers.TryGetValue(eventName, out listHandlers))
            {
                if (listHandlers.Contains(handler))
                {
                    Diagnostic.Warn("#EventRaiseOnce# handler has allready add, eventName:" + eventName);
                    return;
                }
            }
            else
            {
                listHandlers = new List<EventHandler>();
                _dicEventHandlers.Add(eventName, listHandlers);
            }
            listHandlers.Add(handler);
        }


        protected override IEnumerator BindService( )
        {
            Services.AddService<IEventService>(this);
            yield break;
        }
    }
}
