//#define OPEN_DEBUG

using System;
using UnityEngine;
using System.Collections.Generic;
using UnityEngine.EventSystems;

namespace TKFrame
{
    /// <summary>
    /// 结束拖拽事件
    /// </summary>
    public class EndDragListener : PointerEventListener, IEndDragHandler
    {

        /// <summary>
        /// 结束拖拽事件
        /// </summary>
        /// <param name="eventData"></param>
        public void OnEndDrag(PointerEventData eventData)
        {
            NotifyEventCBFunc(eventData);
        }
    }
}




