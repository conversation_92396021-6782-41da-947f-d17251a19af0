local T_Enum = TKNS.System.Enum
local F_ToObject = T_Enum.ToObject

local CSLType = class("CSLType")

local middleTypeDict = {}
function CSLType:initialize(aType)
    self._aType = aType
    self.FullName = aType.name
end
function CSLType:CreateInstance(...)
    return self._aType(...)
end
function CSLType:get_FullName()
    return self.FullName
end
function CSLType:IsAssignableFrom(aType)
  --native类型不可能是csl 类型的子类
  if type(aType) == 'userdata' or rawget(aType,'_aType') == nil then
      return false
    end
   return self._aType:isSubclassOf(aType._aType)
end
function CSLType.GetTypeFromHandle(aType)
	--兼容纯middleclass的情况
	if typeof ~= nil then
		local bindType = typeof(aType)
        
		if bindType~=nil then
			return bindType
		end
	end
	
    local middleType = middleTypeDict[aType]
    if middleType == nil then
        middleType = CSLType(aType)
        middleTypeDict[aType] = middleType
    end
    return middleType
end

function CSLType:ConvertIntToEnum(aType,value)
    local type =self.GetTypeFromHandle(aType)
    return F_ToObject(type,value)
end

return CSLType