using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System;
using System.IO;

public class MediaScanner : EditorWindow
{
    public static string mediaInfoPath;
    public const int WaitForExitTimeout = 60000; // 60s

    /// <summary>
    /// <br>The detail of video format and its standard</br>
    /// <br>Should hint a warning if not suitable.</br>
    /// </summary>
    public struct VideoProfileDetails : IEquatable<VideoProfileDetails>
    {
        // Main@L4 or Main@L4.1
        public string formatProfile;
        // 24.000FPS
        public string frameRate;
        // constant
        public string frameRateMode;
        // YUV
        public string colorSpace;
        // 4:2:0
        public string chromaSubsampling;
        // 8 bits
        public string bitDepth;
        // Progressive
        public string scanType;
        // BT.709
        public string matrixCoefficients;
        // AAC LC
        public string audioFormat;
        // 2 channels
        public string audioChannels;
        // 48.0kHz
        public string audioSampleRate;
        // constant
        public string audioBitRateMode;

        public override bool Equals(object obj)
        {
            return obj is VideoProfileDetails details && Equals(details);
        }

        public bool Equals(VideoProfileDetails other)
        {
            return formatProfile == other.formatProfile &&
                   frameRate == other.frameRate &&
                   frameRateMode == other.frameRateMode &&
                   colorSpace == other.colorSpace &&
                   chromaSubsampling == other.chromaSubsampling &&
                   bitDepth == other.bitDepth &&
                   scanType == other.scanType &&
                   matrixCoefficients == other.matrixCoefficients &&
                   audioFormat == other.audioFormat &&
                   audioChannels == other.audioChannels &&
                   audioSampleRate == other.audioSampleRate &&
                   audioBitRateMode == other.audioBitRateMode;
        }

        // auto generated by visual studio
        public override int GetHashCode()
        {
            int hashCode = -1793420322;
            hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(formatProfile);
            hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(frameRate);
            hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(frameRateMode);
            hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(colorSpace);
            hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(chromaSubsampling);
            hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(bitDepth);
            hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(scanType);
            hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(matrixCoefficients);
            hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(audioFormat);
            hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(audioChannels);
            hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(audioSampleRate);
            hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(audioBitRateMode);
            return hashCode;
        }

        public static bool operator ==(VideoProfileDetails left, VideoProfileDetails right)
        {
            return left.Equals(right);
        }

        public static bool operator !=(VideoProfileDetails left, VideoProfileDetails right)
        {
            return !(left == right);
        }
    }

    static readonly VideoProfileDetails standardDetail = new VideoProfileDetails()
    {
        formatProfile = "High@L4",
        frameRate = "24.000 FPS",
        frameRateMode = "Constant",
        colorSpace = "YUV",
        chromaSubsampling = "4:2:0",
        bitDepth = "8 bits",
        scanType = "Progressive",
        matrixCoefficients = "BT.709",
        audioFormat = "AAC LC",
        audioChannels = "2 channels",
        audioSampleRate = "48.0 kHz",
        audioBitRateMode = "Constant"
    };

    /// <summary>
    /// <br>errorMsg可以用于放给机器人。其中filePath可以为绝对路径，也可以是从Assets起始的相对路径，例如Assets\StreamingAssets\Movie\cg.mp4</br>
    /// <br>details用于返回该视频技术信息，errorMsg可以放给Debug.LogError也可以放给企业微信机器人。</br>
    /// </summary>
    public static bool ScanResult(string filePath, out VideoProfileDetails details, out string errorMsg)
    {
#if UNITY_EDITOR_WIN
        errorMsg = string.Empty;
        details = new VideoProfileDetails();
        mediaInfoPath = Application.dataPath + "/../MediaTools/MediaInfo.exe";
        string fileFullPath;
        if (Path.IsPathRooted(filePath) == false)
            fileFullPath = Application.dataPath + "/../" + filePath;
        else
            fileFullPath = filePath;
        if (!File.Exists(fileFullPath))
        {
            errorMsg = filePath + " 文件不存在。\n";
            return false;
        }
        fileFullPath = fileFullPath.Replace('\\', '/');
        var args = string.Format(" -f \"{0}\"", fileFullPath);
        Debug.Log("MediaScanner.cs: Execute command " + mediaInfoPath + args);
        var process = new System.Diagnostics.Process();
        process.StartInfo.FileName = mediaInfoPath;
        process.StartInfo.Arguments = args;
        process.StartInfo.UseShellExecute = false;
        process.StartInfo.RedirectStandardOutput = true;
        process.StartInfo.CreateNoWindow = true;
        process.Start();
        var output = process.StandardOutput.ReadToEnd();
        process.WaitForExit(WaitForExitTimeout);

        details.formatProfile = ExtractProfile(output, "Format profile");
        details.frameRate = ExtractProfile(output, "Frame rate", order: 1);
        details.frameRateMode = ExtractProfile(output, "Frame rate mode", order: 1);
        details.colorSpace = ExtractProfile(output, "Color space");
        details.chromaSubsampling = ExtractProfile(output, "Chroma subsampling");
        details.bitDepth = ExtractProfile(output, "Bit depth", order: 1);
        details.scanType = ExtractProfile(output, "Scan type");
        details.matrixCoefficients = ExtractProfile(output, "Matrix coefficients");
        details.audioFormat = ExtractProfile(output, "Format", 2, 1);
        details.audioChannels = ExtractProfile(output, "Channel(s)", 2, 1);
        details.audioSampleRate = ExtractProfile(output, "Sampling rate", 2, 1);
        details.audioBitRateMode = ExtractProfile(output, "Bit rate mode", 2, 1);

        // Wanna to output what went wrong if not match
        if (details != standardDetail)
        {
            errorMsg += filePath + " 视频编码格式有问题。\n";
            if (details.formatProfile != standardDetail.formatProfile)
                errorMsg += "Format profile: 结果为 " + details.formatProfile + " 应该为 " + standardDetail.formatProfile + '\n';
            if (details.frameRate != standardDetail.frameRate)
                errorMsg += "Frame rate: 结果为 " + details.frameRate + " 应该为 " + standardDetail.frameRate + '\n';
            if (details.frameRateMode != standardDetail.frameRateMode)
                errorMsg += "Frame rate mode: 结果为 " + details.frameRateMode + " 应该为 " + standardDetail.frameRateMode + '\n';
            if (details.colorSpace != standardDetail.colorSpace)
                errorMsg += "Color space: 结果为 " + details.colorSpace + " 应该为 " + standardDetail.colorSpace + '\n';
            if (details.chromaSubsampling != standardDetail.chromaSubsampling)
                errorMsg += "Chroma subsampling: 结果为 " + details.chromaSubsampling + " 应该为 " + standardDetail.chromaSubsampling + '\n';
            if (details.bitDepth != standardDetail.bitDepth)
                errorMsg += "Bit depth: 结果为 " + details.bitDepth + " 应该为 " + standardDetail.bitDepth + '\n';
            if (details.scanType != standardDetail.scanType)
                errorMsg += "Scan type: 结果为 " + details.scanType + " 应该为 " + standardDetail.scanType + '\n';
            if (details.matrixCoefficients != standardDetail.matrixCoefficients)
                errorMsg += "Matrix coefficients: 结果为 " + details.matrixCoefficients + " 应该为 " + standardDetail.matrixCoefficients + '\n';
            if (details.audioFormat != standardDetail.audioFormat)
                errorMsg += "Audio format: 结果为 " + details.audioFormat + " 应该为 " + standardDetail.audioFormat + '\n';
            if (details.audioChannels != standardDetail.audioChannels)
                errorMsg += "Audio channels: 结果为 " + details.audioChannels + " 应该为 " + standardDetail.audioChannels + '\n';
            if (details.audioSampleRate != standardDetail.audioSampleRate)
                errorMsg += "Audio sample rate: 结果为 " + details.audioSampleRate + " 应该为 " + standardDetail.audioSampleRate + '\n';
            if (details.audioBitRateMode != standardDetail.audioBitRateMode)
                errorMsg += "Audio bit rate mode: 结果为 " + details.audioBitRateMode + " 应该为 " + standardDetail.audioBitRateMode + '\n';
            return false;
        }
        else
            return true;
#else
        throw new NotImplementedException("Only available in Windows Editor");
#endif
    }

    public static float ExtractVideoDurationInSeconds(string filePath)
    {
#if UNITY_EDITOR_WIN
        mediaInfoPath = Application.dataPath + "/../MediaTools/MediaInfo.exe";
        string fileFullPath;
        if (Path.IsPathRooted(filePath) == false)
            fileFullPath = Application.dataPath + "/../" + filePath;
        else
            fileFullPath = filePath;
        if (!File.Exists(fileFullPath))
        {
            Debug.LogError(filePath + " 文件不存在。\n");
            return 0;
        }
        fileFullPath = fileFullPath.Replace('\\', '/');
        var args = string.Format(" -f \"{0}\"", fileFullPath);
        Debug.Log("MediaScanner.cs: Execute command " + mediaInfoPath + args);
        var process = new System.Diagnostics.Process();
        process.StartInfo.FileName = mediaInfoPath;
        process.StartInfo.Arguments = args;
        process.StartInfo.UseShellExecute = false;
        process.StartInfo.RedirectStandardOutput = true;
        process.StartInfo.CreateNoWindow = true;
        process.Start();
        var output = process.StandardOutput.ReadToEnd();
        process.WaitForExit(WaitForExitTimeout);

        var duration = ExtractProfile(output, "Duration", 0, 0);
        var durationInSeconds = 0f;
        // Now assume we get "40500", convert to seconds. This is time in milliseconds.
        durationInSeconds = float.Parse(duration) / 1000f;
        return durationInSeconds;
#else
        throw new NotImplementedException("Only available in Windows Editor");
#endif
    }

    /// <summary>
    /// <br><paramref name="section"/>: 0: general; 1: video; 2: audio. Because some of the params share the same key name between general, video and audio.</br>
    /// <br><paramref name="order"/>: some values might has the same key from mediainfo, so if want to extract the right key need to use the order to specify. If 0, return the first value maches, if 1, return second. etc.</br>
    /// </summary>
    private static string ExtractProfile(string output, string key, int section = 1, int order = 0)
    {
        var lines = output.Split(new string[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries);
        int i;
        // skip to the video or audio section
        for (i = 0; i < lines.Length; i++)
        {
            if (section == 0)
                break;
            if (lines[i].Trim() == "Video" && section == 1)
                break;
            else if (lines[i].Trim() == "Audio" && section == 2)
                break;
        }
        for (; i < lines.Length; i++)
        {
            if (lines[i].Trim() == "Video" && section == 0)
                break;
            if (lines[i].Trim() == "Audio" && section == 1)
                break;
            if (lines[i].Contains(":") && lines[i].Split(':')[0].Trim() == key)
                return lines[i + order].Substring(lines[i + order].IndexOf(':') + 1).Trim();
        }

        return string.Empty;
    }

    // Generate a window, contains text box which enter the path of a video, and a button to scan the video
    [MenuItem("Custom Editor/MediaScanner")]
    public static void ShowWindow()
    {
        GetWindow(typeof(MediaScanner));
    }

    string videoPath = string.Empty;
    string result = string.Empty;
    bool isSuitable = false;
    Vector2 scrollPos = Vector2.zero;

    void OnGUI()
    {
#if UNITY_EDITOR_WIN
        GUILayout.Label("Media Scanner", EditorStyles.boldLabel);
        GUILayout.Label("Please enter the path of the video you want to scan:");
        videoPath = EditorGUILayout.TextField("Video Path", videoPath);
        if (GUILayout.Button("Scan"))
        {
            if (string.IsNullOrEmpty(videoPath))
            {
                result = "Please enter the path of the video you want to scan.";
                isSuitable = false;
            }
            else
            {
                isSuitable = ScanResult(videoPath, out var _, out var errorMsg);
                result = isSuitable ? "The video is suitable for the game." : "The video is not suitable for the game.";
                result += "\n" + "Details: \n\n" + errorMsg;
            }
        }
        scrollPos = EditorGUILayout.BeginScrollView(scrollPos);
        EditorGUILayout.TextArea(result);
        EditorGUILayout.EndScrollView();
#else
        GUILayout.Label("Media Scanner", EditorStyles.boldLabel);
        GUILayout.Label("Only available in Windows Editor");
#endif
    }
}

/*
A sample output of MediaInfo:

General
Count                                    : 334
Count of stream of this kind             : 1
Kind of stream                           : General
Kind of stream                           : General
Stream identifier                        : 0
Count of video streams                   : 1
Count of audio streams                   : 1
Video_Format_List                        : AVC
Video_Format_WithHint_List               : AVC
Codecs Video                             : AVC
Video_Language_List                      : English
Audio_Format_List                        : AAC LC
Audio_Format_WithHint_List               : AAC LC
Audio codecs                             : AAC LC
Audio_Language_List                      : English
Complete name                            : D:\StoneStudioWork\akali\akali_final_1213.mp4
Folder name                              : D:\StoneStudioWork\akali
File name extension                      : akali_final_1213.mp4
File name                                : akali_final_1213
File extension                           : mp4
Format                                   : MPEG-4
Format                                   : MPEG-4
Format/Extensions usually used           : braw mov mp4 m4v m4a m4b m4p m4r 3ga 3gpa 3gpp 3gp 3gpp2 3g2 k3g jpm jpx mqv ismv isma ismt f4a f4b f4v
Commercial name                          : MPEG-4
Format profile                           : Base Media / Version 2
Internet media type                      : video/mp4
Codec ID                                 : mp42
Codec ID                                 : mp42 (mp42/mp41)
Codec ID/Url                             : http://www.apple.com/quicktime/download/standalone.html
CodecID_Compatible                       : mp42/mp41
File size                                : 152313782
File size                                : 145 MiB
File size                                : 145 MiB
File size                                : 145 MiB
File size                                : 145 MiB
File size                                : 145.3 MiB
Duration                                 : 40500
Duration                                 : 40 s 500 ms
Duration                                 : 40 s 500 ms
Duration                                 : 40 s 500 ms
Duration                                 : 00:00:40.500
Duration                                 : 00:00:40:12
Duration                                 : 00:00:40.500 (00:00:40:12)
Overall bit rate mode                    : VBR
Overall bit rate mode                    : Variable
Overall bit rate                         : 30086673
Overall bit rate                         : 30.1 Mb/s
Frame rate                               : 24.000
Frame rate                               : 24.000 FPS
Frame count                              : 972
Stream size                              : 30338
Stream size                              : 29.6 KiB (0%)
Stream size                              : 30 KiB
Stream size                              : 30 KiB
Stream size                              : 29.6 KiB
Stream size                              : 29.63 KiB
Stream size                              : 29.6 KiB (0%)
Proportion of this stream                : 0.00020
HeaderSize                               : 27947
DataSize                                 : 152285835
FooterSize                               : 0
IsStreamable                             : Yes
Encoded date                             : UTC 2023-12-13 07:08:09
Tagged date                              : UTC 2023-12-13 07:08:09
File creation date                       : UTC 2023-12-26 12:52:38.886
File creation date (local)               : 2023-12-26 20:52:38.886
File last modification date              : UTC 2023-12-26 12:47:02.365
File last modification date (local)      : 2023-12-26 20:47:02.365
TIM                                      : 00:00:00:00
TSC                                      : 24
TSZ                                      : 1

Video
Count                                    : 382
Count of stream of this kind             : 1
Kind of stream                           : Video
Kind of stream                           : Video
Stream identifier                        : 0
StreamOrder                              : 0
ID                                       : 1
ID                                       : 1
Format                                   : AVC
Format                                   : AVC
Format/Info                              : Advanced Video Codec
Format/Url                               : http://developers.videolan.org/x264.html
Commercial name                          : AVC
Format profile                           : Main@L4.1
Format settings                          : CABAC / 3 Ref Frames
Format settings, CABAC                   : Yes
Format settings, CABAC                   : Yes
Format settings, Reference frames        : 3
Format settings, Reference frames        : 3 frames
Format settings, GOP                     : M=1, N=30
Internet media type                      : video/H264
Codec ID                                 : avc1
Codec ID/Info                            : Advanced Video Coding
Duration                                 : 40500
Duration                                 : 40 s 500 ms
Duration                                 : 40 s 500 ms
Duration                                 : 40 s 500 ms
Duration                                 : 00:00:40.500
Duration                                 : 00:00:40:12
Duration                                 : 00:00:40.500 (00:00:40:12)
Bit rate                                 : 29635475
Bit rate                                 : 29.6 Mb/s
Width                                    : 1920
Width                                    : 1 920 pixels
Height                                   : 1080
Height                                   : 1 080 pixels
Stored_Height                            : 1088
Sampled_Width                            : 1920
Sampled_Height                           : 1080
Pixel aspect ratio                       : 1.000
Display aspect ratio                     : 1.778
Display aspect ratio                     : 16:9
Rotation                                 : 0.000
Frame rate mode                          : CFR
Frame rate mode                          : Constant
Frame rate                               : 24.000
Frame rate                               : 24.000 FPS
FrameRate_Num                            : 24
FrameRate_Den                            : 1
Frame count                              : 972
Color space                              : YUV
Chroma subsampling                       : 4:2:0
Chroma subsampling                       : 4:2:0
Bit depth                                : 8
Bit depth                                : 8 bits
Scan type                                : Progressive
Scan type                                : Progressive
Bits/(Pixel*Frame)                       : 0.595
Stream size                              : 150029591
Stream size                              : 143 MiB (99%)
Stream size                              : 143 MiB
Stream size                              : 143 MiB
Stream size                              : 143 MiB
Stream size                              : 143.1 MiB
Stream size                              : 143 MiB (99%)
Proportion of this stream                : 0.98500
Language                                 : en
Language                                 : English
Language                                 : English
Language                                 : en
Language                                 : eng
Language                                 : en
Encoded date                             : UTC 2023-12-13 07:08:09
Tagged date                              : UTC 2023-12-13 07:08:09
Codec configuration box                  : avcC

Audio
Count                                    : 285
Count of stream of this kind             : 1
Kind of stream                           : Audio
Kind of stream                           : Audio
Stream identifier                        : 0
StreamOrder                              : 1
ID                                       : 2
ID                                       : 2
Format                                   : AAC
Format                                   : AAC LC
Format/Info                              : Advanced Audio Codec Low Complexity
Commercial name                          : AAC
Format_AdditionalFeatures                : LC
Codec ID                                 : mp4a-40-2
Duration                                 : 40500
Duration                                 : 40 s 500 ms
Duration                                 : 40 s 500 ms
Duration                                 : 40 s 500 ms
Duration                                 : 00:00:40.500
Duration                                 : 00:00:40.500
Source duration                          : 40533
Source duration                          : 40 s 533 ms
Source duration                          : 40 s 533 ms
Source duration                          : 40 s 533 ms
Source duration                          : 00:00:40.533
Source duration                          : 00:00:40.533
Bit rate mode                            : VBR
Bit rate mode                            : Variable
Bit rate                                 : 445308
Bit rate                                 : 445 kb/s
Maximum bit rate                         : 497998
Maximum bit rate                         : 498 kb/s
Channel(s)                               : 2
Channel(s)                               : 2 channels
Channel positions                        : Front: L R
Channel positions                        : 2/0/0
Channel layout                           : L R
Samples per frame                        : 1024
Sampling rate                            : 48000
Sampling rate                            : 48.0 kHz
Samples count                            : 1944000
Frame rate                               : 46.875
Frame rate                               : 46.875 FPS (1024 SPF)
Frame count                              : 1898
Source frame count                       : 1900
Compression mode                         : Lossy
Compression mode                         : Lossy
Stream size                              : 2253853
Stream size                              : 2.15 MiB (1%)
Stream size                              : 2 MiB
Stream size                              : 2.1 MiB
Stream size                              : 2.15 MiB
Stream size                              : 2.149 MiB
Stream size                              : 2.15 MiB (1%)
Proportion of this stream                : 0.01480
Source stream size                       : 2256228
Source stream size                       : 2.15 MiB (1%)
Source stream size                       : 2 MiB
Source stream size                       : 2.2 MiB
Source stream size                       : 2.15 MiB
Source stream size                       : 2.152 MiB
Source stream size                       : 2.15 MiB (1%)
Source_StreamSize_Proportion             : 0.01481
Language                                 : en
Language                                 : English
Language                                 : English
Language                                 : en
Language                                 : eng
Language                                 : en
Encoded date                             : UTC 2023-12-13 07:08:09
Tagged date                              : UTC 2023-12-13 07:08:09
 */