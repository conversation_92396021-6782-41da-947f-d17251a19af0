using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using UnityEngine;
using System;

public class RipGrepHelper
{
    const int WaitForExitTimeout = 60000; // 60s
    // provided by visual studio code.
    private const string flags = " --hidden --no-ignore-parent --follow --crlf --no-config --no-ignore-global";
    private const string ignoreFileListPatterns = " -g !**/.git -g !**/.svn -g !**.hg -g !**/CVS -g !**/.DS_Store -g !**/Thumbs.db -g !**/node_modules -g !**/bower_components -g !**/*.code-search";

    public enum SearchCaseType
    {
        IgnoreCase = 0,
        CaseSensitive = 1,
        SmartCase = 2,
    };

    // match path like "Art_tft_raw/test, bla bla"
    private static readonly Regex relativePathMatch = new Regex(@"^(.*/).*$");

    static readonly string rgExePath =
#if UNITY_EDITOR_WIN
        Application.dataPath + "/../RipGrepHelper/rg.exe";
#else
        // You should use brew to install ripgrep on macOS before use.
        "rg";
#endif

    /// <summary>
    /// <br>如果查到了匹配，那么返回true，<paramref name="resultPath"/>包括这些文件的相对路径，例如"Assets/Editor/ABTool.cs"，否则返回false，输出的<paramref name="resultPath"/>为null。</br>
    /// <br><paramref name="SearchFileListPatterns"/>传入的文件是FileList通配符。比如说"*.prefab", "*/cfg/*.asset", "*/cfg/**/*.asset", "Editor/ABTool.cs"等。<paramref name="SkipSearcFileListPatterns"/>为null意味着不想跳过目录。</br>
    /// <br><see cref="ignoreFileListPatterns"/>规定了部分目录和文件默认跳过。这个规则参考VS Code。</br><br></br>
    /// <br>如果不设置<paramref name="workingDirectory"/>，那么起始路径就是<see cref="Application.dataPath"/>(Assets)，若传入相对路径，不能包括起始路径这一级别。例如"Editor/ABTool.cs"是允许的，"Assets/Editor/ABTool.cs"不允许。</br>
    /// <br>但是注意你传入的SearchFileListPatterns对应的文件必须包括在<paramref name="workingDirectory"/>目录下。如果不是，那么请使用<see cref="RipGrepSearchPatternInFile"/>。</br><br></br>
    /// <br>如果<paramref name="ignoreFileNameLikeMyself"/>为true，将跳过文件名和传入的Pattern相同的文件搜索。比如说，<paramref name="searchPattern"/>为"MT_Hero",自动跳过文件名为MT_Hero.*的文件。</br>
    /// <br>如果传入的<paramref name="searchPattern"/>均为小写，<see cref="SearchCaseType.SmartCase"/>视为<see cref="SearchCaseType.IgnoreCase"/>，否则，视为<see cref="SearchCaseType.CaseSensitive"/></br>
    /// </summary>
    public static bool RipGrepSearchPatternInWorkingDirectory(string searchPattern, List<string> SearchFileListPatterns, out List<string> resultPath, List<string> SkipSearcFileListPatterns = null, SearchCaseType caseStatus = SearchCaseType.IgnoreCase, bool ignoreFileNameLikeMyself = true, string workingDirectory = "")
    {
        string caseSwitch;

        switch (caseStatus)
        {
            case SearchCaseType.SmartCase:
                caseSwitch = " -S ";
                break;
            case SearchCaseType.CaseSensitive:
                caseSwitch = " -s ";
                break;
            case SearchCaseType.IgnoreCase:
            default:
                caseSwitch = " -i ";
                break;
        }

        var rgArgs = caseSwitch + ignoreFileListPatterns + flags;
        // search file whose name or location match patterns.
        foreach (var searchFilePattern in SearchFileListPatterns)
        {
            // -g means --glob, which you let rg.exe find files with matched patterns.
            // for example, -g "*.prefab"
            rgArgs += " -g \"" + searchFilePattern + "\"";
        }
        if (ignoreFileNameLikeMyself)
            // -g !"****" means do not search file with this pattern.
            rgArgs += " -g !\""
                + searchPattern // Do not search myself
                + ".*\""; // include any extension match this file name.

        if (SkipSearcFileListPatterns != null
            && SkipSearcFileListPatterns.Count > 0)
        {
            foreach (var skipFilePattern in SkipSearcFileListPatterns)
            {
                // -g !"****" means do not search file with this pattern.
                rgArgs += " -g !\"" + skipFilePattern + "\"";
            }
        }

        // Pattern, you can use ? or * to match any character or any string.
        rgArgs += " \"" + searchPattern + "\""
            // Tell rg.exe do not wait for stdin.
            + " ./";
        Debug.Log("RipGrepHelper.cs: Execute command " + rgExePath + ' ' + rgArgs);
        var rgProcess = new System.Diagnostics.Process();
        if (string.IsNullOrWhiteSpace(workingDirectory) || !Directory.Exists(workingDirectory))
            rgProcess.StartInfo.WorkingDirectory = Application.dataPath;
        else
            rgProcess.StartInfo.WorkingDirectory = workingDirectory;
        rgProcess.StartInfo.FileName = rgExePath;
        rgProcess.StartInfo.Arguments = rgArgs;
        rgProcess.StartInfo.RedirectStandardOutput = true;
        rgProcess.StartInfo.RedirectStandardError = true;
        rgProcess.StartInfo.UseShellExecute = false;
        rgProcess.StartInfo.CreateNoWindow = true;

        var output = new List<string>();
        var error = new List<string>();
        // using handle instead of using stdout and stderr
        // because C# stdout and stderr might hang process when one of their buffer overflow.
        using (var outputWaitHandle = new AutoResetEvent(false))
        {
            using (var errorWaitHandle = new AutoResetEvent(false))
            {
                rgProcess.OutputDataReceived += (sender, e) =>
                {
                    if (e.Data == null)
                        outputWaitHandle.Set();
                    else
                        output.Add(e.Data);
                };
                rgProcess.ErrorDataReceived += (sender, e) =>
                {
                    if (e.Data == null)
                        errorWaitHandle.Set();
                    else
                        error.Add(e.Data);
                };

                rgProcess.Start();
                rgProcess.PriorityClass = System.Diagnostics.ProcessPriorityClass.BelowNormal;
                rgProcess.BeginOutputReadLine();
                rgProcess.BeginErrorReadLine();

                if (rgProcess.WaitForExit(WaitForExitTimeout)
                  && outputWaitHandle.WaitOne(WaitForExitTimeout)
                  && errorWaitHandle.WaitOne(WaitForExitTimeout))
                {
                    switch (rgProcess.ExitCode)
                    {
                        case 0:
                            resultPath = new List<string>();
                            // Read filepath from rg.exe output
                            foreach (var line in output)
                            {
                                //var line = rgProcess.StandardOutput.ReadLine();
                                // line should looks like "Asset\\dira\\dirb\\filec.ext:  blabla pattern_matched blabla".
                                if (!line.Contains(':'))
                                    continue;
                                // extract path, replace '\' to '/' for Windows platform.
                                var path = line.Split(':')[0].Replace('\\', '/');
                                // Test if it is a valid path.
                                if (relativePathMatch.IsMatch(path))
                                    resultPath.Add(path);
                            }
                            // remove duplicates.
                            resultPath = resultPath.Distinct().ToList();
                            foreach (var path in resultPath)
                                Debug.Log("RipGrepHelper.cs: Found asset name " + searchPattern + " in " + path);

                            return true;
                        case 2:
                            var errorMsg = "RipGrepHelper.cs: Ripgrep error, Detailed below:\n";
                            foreach (var line in error)
                                errorMsg += line + '\n';
                            Debug.LogError(errorMsg);
                            break;
                        case 1:
                        default:
                            Debug.Log("RipGrepHelper.cs: Keyword " + searchPattern + " is not found.");
                            break;
                    }
                    resultPath = null;
                    return false;
                }
                else
                {
                    Debug.LogError("RipGrepHelper.cs: Ripgrep timeout.");
                    resultPath = null;
                    return false;
                }
            }
        }
    }

    /// <summary>
    /// <br>如果查到了匹配，那么返回true，<paramref name="resultPathWithLineDetails"/>的key是文件的相对路径，例如"Assets/Editor/ABTool.cs", Value是匹配行的内容，例如"blabla <paramref name="searchPattern"/> blabla."，没有也拿不到行数，否则返回false，输出的<paramref name="resultPathWithLineDetails"/>为null。</br>
    /// <br>余下用法请参考<see cref="RipGrepSearchPatternInWorkingDirectory"/></br>
    /// </summary>
    public static bool RipGrepSearchPatternInWorkingDirectoryDetail(string searchPattern, List<string> SearchFileListPatterns, out Dictionary<string, List<string>> resultPathWithLineDetails, List<string> SkipSearcFileListPatterns = null, SearchCaseType caseStatus = SearchCaseType.IgnoreCase, bool ignoreFileNameLikeMyself = true, string workingDirectory = "")
    {
        string caseSwitch;

        switch (caseStatus)
        {
            case SearchCaseType.SmartCase:
                caseSwitch = " -S ";
                break;
            case SearchCaseType.CaseSensitive:
                caseSwitch = " -s ";
                break;
            case SearchCaseType.IgnoreCase:
            default:
                caseSwitch = " -i ";
                break;
        }

        var rgArgs = caseSwitch + ignoreFileListPatterns + flags;
        // search file whose name or location match patterns.
        foreach (var searchFilePattern in SearchFileListPatterns)
        {
            // -g means --glob, which you let rg.exe find files with matched patterns.
            // for example, -g "*.prefab"
            rgArgs += " -g \"" + searchFilePattern + "\"";
        }
        if (ignoreFileNameLikeMyself)
            // -g !"****" means do not search file with this pattern.
            rgArgs += " -g !\""
                + searchPattern // Do not search myself
                + ".*\""; // include any extension match this file name.

        if (SkipSearcFileListPatterns != null
            && SkipSearcFileListPatterns.Count > 0)
        {
            foreach (var skipFilePattern in SkipSearcFileListPatterns)
            {
                // -g !"****" means do not search file with this pattern.
                rgArgs += " -g !\"" + skipFilePattern + "\"";
            }
        }

        // Pattern, you can use ? or * to match any character or any string.
        rgArgs += " \"" + searchPattern + "\""
            // Tell rg.exe do not wait for stdin.
            + " ./";
        Debug.Log("RipGrepHelper.cs: Execute command " + rgExePath + ' ' + rgArgs);
        var rgProcess = new System.Diagnostics.Process();
        if (string.IsNullOrWhiteSpace(workingDirectory) || !Directory.Exists(workingDirectory))
            rgProcess.StartInfo.WorkingDirectory = Application.dataPath;
        else
            rgProcess.StartInfo.WorkingDirectory = workingDirectory;
        rgProcess.StartInfo.FileName = rgExePath;
        rgProcess.StartInfo.Arguments = rgArgs;
        rgProcess.StartInfo.RedirectStandardOutput = true;
        rgProcess.StartInfo.RedirectStandardError = true;
        rgProcess.StartInfo.UseShellExecute = false;
        rgProcess.StartInfo.CreateNoWindow = true;

        var output = new List<string>();
        var error = new List<string>();
        // using handle instead of using stdout and stderr
        // because C# stdout and stderr might hang process when one of their buffer overflow.
        using (var outputWaitHandle = new AutoResetEvent(false))
        {
            using (var errorWaitHandle = new AutoResetEvent(false))
            {
                rgProcess.OutputDataReceived += (sender, e) =>
                {
                    if (e.Data == null)
                        outputWaitHandle.Set();
                    else
                        output.Add(e.Data);
                };
                rgProcess.ErrorDataReceived += (sender, e) =>
                {
                    if (e.Data == null)
                        errorWaitHandle.Set();
                    else
                        error.Add(e.Data);
                };

                rgProcess.Start();
                rgProcess.PriorityClass = System.Diagnostics.ProcessPriorityClass.BelowNormal;
                rgProcess.BeginOutputReadLine();
                rgProcess.BeginErrorReadLine();

                if (rgProcess.WaitForExit(WaitForExitTimeout)
                  && outputWaitHandle.WaitOne(WaitForExitTimeout)
                  && errorWaitHandle.WaitOne(WaitForExitTimeout))
                {
                    switch (rgProcess.ExitCode)
                    {
                        case 0:
                            resultPathWithLineDetails = new Dictionary<string, List<string>>();
                            // Read filepath from rg.exe output
                            foreach (var line in output)
                            {
                                //var line = rgProcess.StandardOutput.ReadLine();
                                // line should looks like "Asset\\dira\\dirb\\filec.ext:  blabla pattern_matched blabla".
                                if (!line.Contains(':'))
                                    continue;
                                // extract path, replace '\' to '/' for Windows platform.
                                var lineSplitResult = line.Split(':');
                                var path = lineSplitResult[0].Replace('\\', '/');
                                // sould looks like "  blabla pattern_matched blabla".
                                var lineDetails = lineSplitResult[1].Trim();
                                // Test if it is a valid path.
                                if (relativePathMatch.IsMatch(path))
                                {
                                    if (resultPathWithLineDetails.ContainsKey(path))
                                        resultPathWithLineDetails[path].Add(lineDetails);
                                    else
                                    {
                                        resultPathWithLineDetails.Add(path, new List<string> { lineDetails });
                                        // Output log the first time we found this asset.
                                        Debug.Log("RipGrepHelper.cs: Found asset name " + searchPattern + " in " + path);
                                    }
                                }
                            }

                            return true;
                        case 2:
                            var errorMsg = "RipGrepHelper.cs: Ripgrep error, Detailed below:\n";
                            foreach (var line in error)
                                errorMsg += line + '\n';
                            Debug.LogError(errorMsg);
                            break;
                        case 1:
                        default:
                            Debug.Log("RipGrepHelper.cs: Keyword " + searchPattern + " is not found.");
                            break;
                    }
                    resultPathWithLineDetails = null;
                    return false;
                }
                else
                {
                    Debug.LogError("RipGrepHelper.cs: Ripgrep timeout.");
                    resultPathWithLineDetails = null;
                    return false;
                }
            }
        }
    }

    /// <summary>
    /// <br>如果查找到了匹配，返回true，否则返回false</br><br></br>
    /// <br><paramref name="searchFilePath"/>是你传入的绝对或者自<paramref name="workingDirectory"/>起的相对路径，可搜索的路径不局限于<paramref name="workingDirectory"/>目录。此路径必须是精确路径，不支持通配符。</br>
    /// <br>如果不设置<paramref name="workingDirectory"/>，那么起始路径就是工程根目录</br>
    /// <br>例如："/usr/bin/bash"，"C:/Windows/Win.ini"，"../ProjectSettings/ProjectSettings.asset"，"Editor/ABTool.cs"等都是可以的。</br><br></br>
    /// <br>如果传入的<paramref name="searchPattern"/>均为小写，<see cref="SearchCaseType.SmartCase"/>视为<see cref="SearchCaseType.IgnoreCase"/>，否则，视为<see cref="SearchCaseType.CaseSensitive"/></br>
    /// </summary>
    public static bool RipGrepSearchPatternInFile(string searchPattern, string searchFilePath, SearchCaseType caseStatus = SearchCaseType.IgnoreCase, string workingDirectory = "")
    {
        string caseSwitch;

        switch (caseStatus)
        {
            case SearchCaseType.SmartCase:
                caseSwitch = " -S ";
                break;
            case SearchCaseType.CaseSensitive:
                caseSwitch = " -s ";
                break;
            case SearchCaseType.IgnoreCase:
            default:
                caseSwitch = " -i ";
                break;
        }

        var rgArgs = caseSwitch + "--engine auto --regexp " + "\""
            // Pattern, you can use ? or * to match any character or any string.
            + searchPattern + "\" \""
            // search in this file.
            + searchFilePath + "\"";

        Debug.Log("RipGrepHelper.cs: Execute command " + rgExePath + ' ' + rgArgs);
        var rgProcess = new System.Diagnostics.Process();
        if (string.IsNullOrWhiteSpace(workingDirectory) || !Directory.Exists(workingDirectory))
            // 7 means "/Assets"'s length.
            rgProcess.StartInfo.WorkingDirectory = Application.dataPath.Substring(0, Application.dataPath.Length - 7);
        else
            rgProcess.StartInfo.WorkingDirectory = workingDirectory;
        rgProcess.StartInfo.FileName = rgExePath;
        rgProcess.StartInfo.Arguments = rgArgs;
        rgProcess.StartInfo.RedirectStandardError = true;
        rgProcess.StartInfo.UseShellExecute = false;
        rgProcess.StartInfo.CreateNoWindow = true;

        var error = new List<string>();
        using (var errorWaitHandle = new AutoResetEvent(false))
        {
            rgProcess.ErrorDataReceived += (sender, e) =>
            {
                if (e.Data == null)
                    errorWaitHandle.Set();
                else
                    error.Add(e.Data);
            };

            rgProcess.Start();
            rgProcess.PriorityClass = System.Diagnostics.ProcessPriorityClass.BelowNormal;
            rgProcess.BeginErrorReadLine();

            if (rgProcess.WaitForExit(WaitForExitTimeout)
              && errorWaitHandle.WaitOne(WaitForExitTimeout))
            {
                switch (rgProcess.ExitCode)
                {
                    case 0:
                        Debug.Log("RipGrepHelper.cs: Found keyword " + searchPattern + " in " + searchFilePath);
                        return true;
                    case 2:
                        var errorMsg = "RipGrepHelper.cs: Ripgrep error, Detailed below:\n";
                        foreach (var line in error)
                            errorMsg += line + '\n';
                        Debug.LogError(errorMsg);
                        return false;
                    case 1:
                    default:
                        Debug.Log("RipGrepHelper.cs: Keyword " + searchPattern + " is not found in " + searchFilePath);
                        return false;
                }
            }
            else
            {
                Debug.LogError("RipGrepHelper.cs: Ripgrep timeout.");
                return false;
            }
        }
    }

    /// <summary>
    /// <br>如果查找到了匹配，返回true，否则返回false</br>
    /// <br>isRegExp：是否开启正则匹配</br>
    /// <br>resultLine包括匹配行的内容，如果找不到，则返回null。余下用法请参考<see cref="RipGrepSearchPatternInFile"/></br>
    /// </summary>
    public static bool RipGrepSearchPatternInFileWithDetail(string searchPattern, string searchFilePath, out List<string> resultLine, SearchCaseType caseStatus = SearchCaseType.IgnoreCase, string workingDirectory = "")
    {
        string caseSwitch;
        resultLine = null;

        switch (caseStatus)
        {
            case SearchCaseType.SmartCase:
                caseSwitch = " -S ";
                break;
            case SearchCaseType.CaseSensitive:
                caseSwitch = " -s ";
                break;
            case SearchCaseType.IgnoreCase:
            default:
                caseSwitch = " -i ";
                break;
        }

        var rgArgs = caseSwitch + "--engine auto --regexp " + "\""
            // Pattern, you can use ? or * to match any character or any string.
            + searchPattern + "\" \""
            // search in this file.
            + searchFilePath + "\"";

        Debug.Log("RipGrepHelper.cs: Execute command " + rgExePath + ' ' + rgArgs);
        var rgProcess = new System.Diagnostics.Process();
        if (string.IsNullOrWhiteSpace(workingDirectory) || !Directory.Exists(workingDirectory))
            // 7 means "/Assets"'s length.
            rgProcess.StartInfo.WorkingDirectory = Application.dataPath.Substring(0, Application.dataPath.Length - 7);
        else
            rgProcess.StartInfo.WorkingDirectory = workingDirectory;
        rgProcess.StartInfo.FileName = rgExePath;
        rgProcess.StartInfo.Arguments = rgArgs;
        rgProcess.StartInfo.RedirectStandardOutput = true;
        rgProcess.StartInfo.RedirectStandardError = true;
        rgProcess.StartInfo.UseShellExecute = false;
        rgProcess.StartInfo.CreateNoWindow = true;

        var output = new List<string>();
        var error = new List<string>();
        using (var outputWaitHandle = new AutoResetEvent(false))
        {
            using (var errorWaitHandle = new AutoResetEvent(false))
            {
                rgProcess.OutputDataReceived += (sender, e) =>
                {
                    if (e.Data == null)
                        outputWaitHandle.Set();
                    else
                        output.Add(e.Data);
                };

                rgProcess.ErrorDataReceived += (sender, e) =>
                {
                    if (e.Data == null)
                        errorWaitHandle.Set();
                    else
                        error.Add(e.Data);
                };

                rgProcess.Start();
                rgProcess.PriorityClass = System.Diagnostics.ProcessPriorityClass.BelowNormal;
                rgProcess.BeginOutputReadLine();
                rgProcess.BeginErrorReadLine();
                if (rgProcess.WaitForExit(WaitForExitTimeout)
                    && outputWaitHandle.WaitOne(WaitForExitTimeout)
                    && errorWaitHandle.WaitOne(WaitForExitTimeout))
                {
                    switch (rgProcess.ExitCode)
                    {
                        case 0:
                            Debug.Log("RipGrepHelper.cs: Found keyword " + searchPattern + " in " + searchFilePath);
                            resultLine = new List<string>();
                            foreach (var line in output)
                            {
                                // line should looks like "blabla pattern_matched blabla".
                                // Trim white spaces
                                var lineDetails = line.Trim();
                                resultLine.Add(lineDetails);
                            }
                            return true;
                        case 2:
                            var errorMsg = "RipGrepHelper.cs: Ripgrep error, Detailed below:\n";
                            foreach (var line in error)
                                errorMsg += line + '\n';
                            Debug.LogError(errorMsg);
                            return false;
                        case 1:
                        default:
                            Debug.Log("RipGrepHelper.cs: Keyword " + searchPattern + " is not found in " + searchFilePath);
                            return false;
                    }
                }
                else
                {
                    Debug.LogError("RipGrepHelper.cs: Ripgrep timeout.");
                    return false;
                }
            }
        }
    }
}