using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using TKFrame;
using UnityEditor;
using UnityEngine;


/// <summary>
/// 打包之前对资源进行处理
/// </summary>
public class PreBuilder
{
    private static PreBuilder preBuilder;
    public static PreBuilder Instance
    {
        get
        {
            if(preBuilder == null)
            {
                preBuilder = new PreBuilder();
            }
            return preBuilder;
        }
    }

    private static AssetIEnumerator AssetIEnumeratorInstance = new AssetIEnumerator();

    public void ProcessPreBuilder(bool isBasePack = false)
    {
        Diagnostic.Log("************** PreBuilder ProcessPreBuilder **************");


        PreBuilderAsset(isBasePack);
    }
    public void RegisterPrefabPrebuildHander<T>(Func<T,bool> action) where T : UnityEngine.Component
    {
        AssetIEnumeratorInstance.RegisterPrefabPrebuildHander<T>(action);

    }
    public void RegisterAssetPrebuildHander<T>(Action<T> action) where T : UnityEngine.Object
    {
        AssetIEnumeratorInstance.RegisterAssetPrebuildHander<T>(action);
    }  
 

    //对资源进行的预处理
    private void PreBuilderAsset(bool isBasePack = false)
    {
        AssetDatabase.StartAssetEditing();
        try
        {
            AssetIEnumeratorInstance.ProcessAllAsset(isBasePack);

            AssetDatabase.SaveAssets();
        }
        finally
        {
            AssetDatabase.StopAssetEditing();
        }
    }

    //预处理之后，要对本地资源进行还原
    public void ProcessAfterBuidler()
    {
        foreach(var item in AssetIEnumeratorInstance.assetBuffer)
        {
           
            File.Copy(item.Key,item.Value,true);
        }

        DirectoryInfo info = new DirectoryInfo("Assets/Art/AssetTemporaryPath");

        FileInfo[] fileInfo = info.GetFiles();
        foreach(var item in fileInfo)
        {
            File.Delete(item.FullName);
        }

        if(Directory.Exists("Assets/Art/AssetTemporaryPath"))
        {
            Directory.Delete("Assets/Art/AssetTemporaryPath");
        }

        AssetDatabase.Refresh();
    }
}




public class AssetIEnumerator
{
    //处理prefab上面的组件
    public List<KeyValuePair<Type, Func<UnityEngine.Component,bool>>> prefabObjectHandles = new List<KeyValuePair<Type, Func<UnityEngine.Component, bool>>>();

    //处理非prefab资源，例如Texture，shader等
    public List<KeyValuePair<Type, Action<UnityEngine.Object>>> assetHandles = new List<KeyValuePair<Type, Action<UnityEngine.Object>>>();

    //
    public List<KeyValuePair<string, string>> assetBuffer = new List<KeyValuePair<string, string>>();

    public void RegisterPrefabPrebuildHander<T>(Func<T,bool> action) where T : UnityEngine.Component
    {
        prefabObjectHandles.Add(new KeyValuePair<Type, Func<UnityEngine.Component, bool>>(typeof(T), (x) =>
        {
            return action((T)x);
        }));

    }
    public void RegisterAssetPrebuildHander<T>(Action<T> action) where T : UnityEngine.Object
    {
        assetHandles.Add(new KeyValuePair<Type, Action<UnityEngine.Object>>(typeof(T), (x) =>
        {
            action((T)x);
        }));
    }
    //处理所有的asset
    public void ProcessAllAsset(bool isBasePack = false)
    {
        List<string> bundlePaths = new List<string>();
#if TKF_EDITOR
        foreach (var bundleName in BuildScript.GetCurrentAssetBundleNames())
        {
            string[] bundlePath = AssetDatabase.GetAssetPathsFromAssetBundle(bundleName);
            bundlePaths.AddRange(bundlePath);
        }
#endif

        //**************resource下面的**************

        //**************resource下面的**************


        //收集依赖
        List<string> bundleDependencePaths = new List<string>();
        for (int i = 0; i < bundlePaths.Count; i++)
        {
            string[] dependencies = AssetDatabase.GetDependencies(bundlePaths[i], true);
            foreach (var item in dependencies)
            {
                if (!bundlePaths.Contains(item))
                {
                    bundleDependencePaths.Add(item);
                }
            }
        }
        bundlePaths.AddRange(bundleDependencePaths);


        //遍历所有资源
        for (int i = 0; i < bundlePaths.Count; i++)
        {
            var assetPath = bundlePaths[i];

            var assetObject = AssetDatabase.LoadMainAssetAtPath(assetPath);
            foreach (var kvp in assetHandles)
            {
                if (kvp.Key.IsInstanceOfType(assetObject))
                {
                    kvp.Value(assetObject);
                }
            }
            if (assetObject is GameObject)
            {
                bool isNeedInstantiate = false;
                var prefabGo = (GameObject)assetObject;
                foreach (var kvp in prefabObjectHandles)
                {
                    var children = prefabGo.GetComponentsInChildren(kvp.Key, true);
                    if (children.Length > 0)
                    {
                        isNeedInstantiate = true;
                    }
                }

                if (isNeedInstantiate)
                {

                    //GameObject go = PrefabUtility.InstantiatePrefab(prefabGo) as GameObject;

                    //if (isBasePack)
                    //{
                    //    if (!Directory.Exists("Assets/Art/AssetTemporaryPath"))
                    //    {
                    //        Directory.CreateDirectory("Assets/Art/AssetTemporaryPath");
                    //    }

                    //    string desPath = "Assets/Art/AssetTemporaryPath/" + go.name + ".prefab";
                    //    File.Copy(assetPath, desPath, true);
                    //    assetBuffer.Add(new KeyValuePair<string, string>(desPath, assetPath));
                    //}
                    bool modifyed = false;
                    foreach (var kvp in prefabObjectHandles)
                    {
                        var components = prefabGo.GetComponentsInChildren(kvp.Key, true);
                        foreach (var comp in components)
                        {
                            if (kvp.Value(comp))
                            {
                                modifyed = true;
                            }
                        }
                    }
                    if (modifyed)
                    {
                        //PrefabUtility.ReplacePrefab(go, prefabGo, ReplacePrefabOptions.Default);
                        EditorUtility.SetDirty(prefabGo);
                        AssetDatabase.SaveAssets();
                    }
                }
            }       
        }
    }

}

