using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Networking;

namespace TKFrame.COSResource
{
    /// <summary>
    /// 下载到本地 保存成文件
    /// </summary>
    public class COSDownloadManager
    {
        private string m_serverPathRoot = string.Empty;
        private string m_clientPathRoot = string.Empty;

        private COSVersionRequest m_versionRequest = null;

        // 优先下载队列 （这个队列会尽可能的使用下载资源）
        private LinkedList<COSDownloadRequest> m_priorityRequestList = new LinkedList<COSDownloadRequest>();
        // 正常请求队列 （这个队列的下载尽可能不影响到玩家体验）
        private LinkedList<COSDownloadRequest> m_requestList = new LinkedList<COSDownloadRequest>();

        //是否开启多通道加载，在速度优先时开启
        private COSDownloadRequest[] m_channelConcurrency = new COSDownloadRequest[20];

        private Queue<COSDownloadRequest> m_requestFinishList = new Queue<COSDownloadRequest>();

        private int m_channelCount = 16;

        /// <summary>
        /// 优先队列独占的通道数量
        /// </summary>
        private const int PRIORTY_REQUEST_CHANNEL_COUNT = 4;

        #region LinkListNode对象池
        private static LinkedListNode<COSDownloadRequest> GetLinkedNodeFormPool(COSDownloadRequest request)
        {
            return COSLinkedNodePool.Get(request);
        }

        private static void RecycleLinkedNodeToPool(LinkedListNode<COSDownloadRequest> node)
        {
            COSLinkedNodePool.Free(node);
        }
        #endregion

        public void Display(ICOSConsole sb)
        {
            sb.AppendLine(string.Format(" DownloadManager(channel: {0})", m_channelCount));
            sb.AppendLine(string.Format("    PriorityRequestList({0})", m_priorityRequestList.Count));
            foreach (var item in m_priorityRequestList)
            {
                item.Display("      ", sb);
            }
            sb.AppendLine(string.Format("    RequestList({0})", m_requestFinishList.Count));
            foreach (var item in m_priorityRequestList)
            {
                item.Display("      ", sb);
            }
            sb.AppendLine("    Channel");
            for (int i = 0; i < m_channelConcurrency.Length; ++i)
            {
                var channel = m_channelConcurrency[i];
                if (channel != null)
                {
                    channel.Display(string.Format("      [{0}]", i), sb);
                }
            }
            sb.AppendLine("    FinishList");
            foreach (var item in m_requestFinishList)
            {
                item.Display("      ", sb);
            }
        }

        public void Init()
        {
            COSUpdater.Instance.Regist(Update);
        }

        public void Bind(COSResourceManagerBase resManager)
        {
            m_serverPathRoot = resManager.GetServerPathRoot();
            m_clientPathRoot = resManager.GetClientPathRoot();
        }

        public void UnInit()
        {
            if (COSUpdater.HasInstance())
                COSUpdater.Instance.UnRegist(Update);
        }

        public void SetChannelCount(int channelCount)
        {
            m_channelCount = channelCount;

            // 至少要给独占通道留出位置
            if (m_channelCount + PRIORTY_REQUEST_CHANNEL_COUNT >= m_channelConcurrency.Length)
            {
                m_channelCount = m_channelConcurrency.Length - PRIORTY_REQUEST_CHANNEL_COUNT;
            }

            if (m_channelCount <= 0)
                m_channelCount = 1;
        }

        public bool DownloadVersion(string relativePath, Action<string, byte[]> onLoadFinished, int maxTime, bool showFileNotFindWarning)
        {
            if (m_versionRequest == null)
            {
                m_versionRequest = new COSVersionRequest();
                m_versionRequest.pathRoot = m_serverPathRoot;
                m_versionRequest.relativePath = relativePath;
                m_versionRequest.onLoadFinished = onLoadFinished;
                m_versionRequest.maxTime = maxTime;
                m_versionRequest.showFileNotFindWarning = showFileNotFindWarning;
                m_versionRequest.RunTick();
                return true;
            }
            return false;
        }

        public bool IsBusy()
        {
            if (m_versionRequest != null)
                return true;
            if (m_requestList.First != null)
                return true;
            if (m_priorityRequestList.First != null)
                return true;
            for (int i = 0; i < m_channelConcurrency.Length; ++i)
            {
                if (m_channelConcurrency[i] != null)
                    return true;
            }
            return false;
        }

        // 将这个资源拉到优先队列的最开头进行下载
        public void TryInsertToPriorityRequestList(string relativePath)
        {
            // 待加载
            var cur = m_requestList.First;
            while (cur != null)
            {
                var request = cur.Value;
                if (request.RelativePath == relativePath)
                {
                    // 拉到最前面等待下载
                    m_requestList.Remove(cur);
                    m_priorityRequestList.AddFirst(cur);
                    return;
                }

                cur = cur.Next;
            }
            // 待加载的优先队列
            cur = m_priorityRequestList.First;
            while (cur != null)
            {
                var request = cur.Value;
                if (request.RelativePath == relativePath)
                {
                    // 拉到最前面等待下载
                    m_priorityRequestList.Remove(cur);
                    m_priorityRequestList.AddFirst(cur);
                    return;
                }

                cur = cur.Next;
            }
        }

        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="relativePath">路径</param>
        /// <param name="fileInfo">文件的校验信息</param>
        /// <param name="onProgress">下载进度回调</param>
        /// <param name="onLoadFinished">下载完成回调</param>
        /// <param name="blockSize">下载每帧缓存大小（可以控制下载速度）</param>
        /// <param name="maxTime">下载超时时间</param>
        /// <param name="insertToPriorityRequestList">是否优先下载</param>
        /// <returns></returns>
        public COSDownloadRequest DownloadFile(string relativePath,
            COSFileInfo fileInfo, Action<string, int, int> onProgress,
            Action<COSDownloadRequest> onLoadFinished,
            int blockSize, int maxTime, bool insertToPriorityRequestList)
        {
            // 正在下载
            for (int i = 0; i < m_channelConcurrency.Length; ++i)
            {
                var channel = m_channelConcurrency[i];
                if (channel != null)
                {
                    if (channel.RelativePath == relativePath)
                    {
                        channel.AddProgress(onProgress);
                        channel.AddFinishedCallback(onLoadFinished);
                        return channel;
                    }
                }
            }

            // 待加载
            var cur = m_requestList.First;
            while (cur != null)
            {
                var request = cur.Value;
                if (request.RelativePath == relativePath)
                {
                    request.AddProgress(onProgress);
                    request.AddFinishedCallback(onLoadFinished);

                    // 拉到优先队列中进行加载
                    if (insertToPriorityRequestList)
                    {
                        m_requestList.Remove(cur);
                        m_priorityRequestList.AddFirst(cur);
                    }

                    return request;
                }

                cur = cur.Next;
            }

            // 待加载的优先队列
            cur = m_priorityRequestList.First;
            while (cur != null)
            {
                var request = cur.Value;
                if (request.RelativePath == relativePath)
                {
                    request.AddProgress(onProgress);
                    request.AddFinishedCallback(onLoadFinished);

                    // 拉到最前面等待下载
                    if (insertToPriorityRequestList)
                    {
                        m_priorityRequestList.Remove(cur);
                        m_priorityRequestList.AddFirst(cur);
                    }

                    return request;
                }

                cur = cur.Next;
            }

            // 加载完成 等待回调
            foreach (var request in m_requestFinishList)
            {
                if (!request.IsCancel && request.RelativePath == relativePath)
                {
                    request.IsCancel = false;
                    request.AddProgress(onProgress);
                    request.AddFinishedCallback(onLoadFinished);
                    return request;
                }
            }

            var r = COSPool.Get<COSDownloadRequest>();
            r.Init(relativePath, fileInfo, maxTime, false);
            r.SetServerPathRoot(m_serverPathRoot);
            r.SetClientPathRoot(m_clientPathRoot);
            r.SetCheckCrc(true);
            r.SetBlockSize(blockSize);
            r.AddProgress(onProgress);
            r.AddFinishedCallback(onLoadFinished);
            var linkedNode = GetLinkedNodeFormPool(r);

            if (insertToPriorityRequestList)
                m_priorityRequestList.AddFirst(linkedNode);
            else
                m_requestList.AddLast(linkedNode);
            //m_requestList.Enqueue(r);
            return r;
        }

        private COSDownloadRequest GetNewRequest(LinkedList<COSDownloadRequest> requestList)
        {
            while (requestList.First != null)
            {
                var firstNode = requestList.First;
                var request = firstNode.Value;
                RecycleLinkedNodeToPool(firstNode);
                requestList.RemoveFirst();
                if (request.IsCancel)
                {
                    COSPool.Free(request);
                }
                else
                {
                    return request;
                }
            }

            return null;
        }

        private COSDownloadRequest GetNewRequest(int channelIndex)
        {
            if (m_channelCount > channelIndex)
            {
                var newRequest = GetNewRequest(m_priorityRequestList);
                if (newRequest == null)
                    newRequest = GetNewRequest(m_requestList);
                return newRequest;
            }
            else if (m_channelCount + PRIORTY_REQUEST_CHANNEL_COUNT > channelIndex)
            {
                // 这里是优先队列独占的
                return GetNewRequest(m_priorityRequestList);
            }
            else
            {
                return null;
            }
        }

        public void Update()
        {
            if (m_versionRequest != null)
            {
                m_versionRequest.RunTick();

                if (m_versionRequest.IsError || m_versionRequest.IsSuccess)
                {
                    var r = m_versionRequest;
                    m_versionRequest = null;
                    r.OnFinished();
                }
            }

            for (int i = 0; i < m_channelConcurrency.Length; ++i)
            {
                var channel = m_channelConcurrency[i];
                if (channel == null)
                {
                    channel = GetNewRequest(i);
                    if (channel != null)
                    {
                        m_channelConcurrency[i] = channel;
                        channel.RunTick();
                    }
                }
                else
                {
                    channel.RunTick();

                    if (channel.IsSuccess || channel.IsError)
                    {
                        m_requestFinishList.Enqueue(channel);
                        m_channelConcurrency[i] = null;
                    }
                }
            }

            ExceFinishedCallback();
        }

        private void ExceFinishedCallback()
        {
            float beginLoadSuccessfulTime = Time.realtimeSinceStartup;
            while (m_requestFinishList.Count > 0)
            {
                var request = m_requestFinishList.Dequeue();
                if (request != null)
                {
                    if (!request.IsCancel)
                        request.ExceFinishedCallback();
                    COSPool.Free(request);
                }

                if (Time.realtimeSinceStartup - beginLoadSuccessfulTime > 0.016f) // 超过16ms就放在下一帧去处理
                    break;
            }
        }

        private bool RemoveFromList(LinkedList<COSDownloadRequest> requestList, string relativePath)
        {
            var cur = requestList.First;
            while (cur != null)
            {
                if (cur.Value.RelativePath == relativePath)
                {
                    COSPool.Free(cur.Value);
                    RecycleLinkedNodeToPool(cur);
                    requestList.Remove(cur);
                    return true;
                }

                cur = cur.Next;
            }
            return false;
        }

        public bool Cancel(string relativePath)
        {
            if (RemoveFromList(m_priorityRequestList, relativePath))
                return true;

            if (RemoveFromList(m_requestList, relativePath))
                return true;

            for (int i = 0; i < m_channelConcurrency.Length; ++i)
            {
                var channel = m_channelConcurrency[i];
                if (channel != null && !channel.IsCancel && channel.RelativePath == relativePath)
                {
                    // 部分android直接停会导致卡死...（Java层的ReadableByteChannel导致的） 垃圾unity
                    if (Application.platform == RuntimePlatform.Android || Application.isEditor)
                    {

                    }
                    else
                    {
                        channel.Cancel();
                        m_channelConcurrency[i] = null;
                        COSPool.Free(channel);
                    }
                    return true;
                }
            }

            foreach (var request in m_requestFinishList)
            {
                if (!request.IsCancel && request.RelativePath == relativePath)
                {
                    request.IsCancel = true;
                    return true;
                }
            }

            return false;
        }

        public void ClearAll()
        {
            var curNode = m_priorityRequestList.First;
            while (curNode != null)
            {
                COSPool.Free(curNode.Value);
                RecycleLinkedNodeToPool(curNode);
                curNode = curNode.Next;
            }
            m_priorityRequestList.Clear();

            curNode = m_requestList.First;
            while (curNode != null)
            {
                COSPool.Free(curNode.Value);
                RecycleLinkedNodeToPool(curNode);
                curNode = curNode.Next;
            }
            m_requestList.Clear();

            foreach (var r in m_requestFinishList)
            {
                COSPool.Free(r);
            }
            m_requestFinishList.Clear();

            for (int i = 0; i < m_channelConcurrency.Length; ++i)
            {
                var channel = m_channelConcurrency[i];
                if (channel != null)
                {
                    channel.Cancel();
                    COSPool.Free(channel);
                    m_channelConcurrency[i] = null;
                }
            }
        }

        /// <summary>
        /// 获取服务器URL
        /// </summary>
        /// <param name="relativePath"></param>
        /// <param name="md5"></param>
        /// <returns></returns>
        public static string GetUrl(string relativePath, string md5)
        {
            if (string.IsNullOrEmpty(md5))
                return $"{COSGlobalDefine.CDNURL}{relativePath}";
            else
                return $"{COSGlobalDefine.CDNURL}{relativePath}.{md5}";
        }
    }
}
