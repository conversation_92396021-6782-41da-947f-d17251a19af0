using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace TKFrame.COSResource
{
    public class COSTask : ICOSPoolItem
    {
        private static long ms_taskId = 0;

        public static void ResetTaskId()
        {
            ms_taskId = 0;
        }

        private long m_taskId;
        private float m_delayTime;
        private Action m_action;
        private float m_startTime;
        private bool m_cancel = false;

        public long GetTaskId()
        {
            return m_taskId;
        }

        public void Set(float delayTime, Action action)
        {
            m_taskId = ms_taskId++;
            m_startTime = Time.time;
            m_delayTime = delayTime;
            m_action = action;
        }

        public void Cancel()
        {
            m_cancel = true;
        }

        public bool TryExce()
        {
            if (m_cancel)
            {
                return true;
            }

            if (m_startTime + m_delayTime <= Time.time)
            {
                COSFuncUtil.ExceFunc("COSTask.TryExce", m_action);
                //m_action?.Invoke();
                return true;
            }
            return false;
        }

        public void Reset()
        {
            m_taskId = 0;
            m_startTime = 0;
            m_delayTime = 0;
            m_action = null;
            m_cancel = false;
        }
    }

    public class COSTaskManager
    {
        private bool m_taskIdDirty = false;
        public List<COSTask> m_taskList = new List<COSTask>();

        public List<Action> m_taskInMainThreadList = new List<Action>();

        public void Init()
        {
            COSUpdater.Instance.UnRegist(Tick);
            COSUpdater.Instance.Regist(Tick);
        }

        public void Destory()
        {
            if (COSUpdater.HasInstance())
                COSUpdater.Instance.UnRegist(Tick);
        }

        public long AddTask(float delayTime, Action action)
        {
            m_taskIdDirty = true;
            COSTask task = COSPool.Get<COSTask>();
            task.Set(delayTime, action);
            m_taskList.Add(task);
            return task.GetTaskId();
        }

        public void AddTaskToMainThread(Action action)
        {
            lock (m_taskInMainThreadList)
            {
                m_taskInMainThreadList.Add(action);
            }
        }

        public void RemoveTask(long taskId)
        {
            m_taskIdDirty = true;
            for (int i = m_taskList.Count - 1; i >= 0; --i)
            {
                var task = m_taskList[i];
                if (task.GetTaskId() == taskId)
                {
                    task.Cancel();
                    break;
                }
            }
        }

        private List<Action> m_tmpTaskList = new List<Action>();
        private void Tick()
        {
            if (m_taskList.Count > 0)
            {
                for (int i = m_taskList.Count - 1; i >= 0; --i)
                {
                    var task = m_taskList[i];
                    if (task.TryExce())
                    {
                        COSPool.Free(task);
                        m_taskList.RemoveAt(i);
                    }
                }

            }
            else if (m_taskIdDirty)
            {
                m_taskIdDirty = false;
                COSTask.ResetTaskId();
            }

            if (m_taskInMainThreadList.Count > 0)
            {
                lock (m_taskInMainThreadList)
                {
                    m_tmpTaskList.Clear();
                    m_tmpTaskList.AddRange(m_taskInMainThreadList);
                    m_taskInMainThreadList.Clear();
                }

                for (int i = 0; i < m_tmpTaskList.Count; ++i)
                {
                    COSFuncUtil.ExceFunc("COSTask.ExceTaskInMainThread", m_tmpTaskList[i]);
                }
                m_tmpTaskList.Clear();
            }
        }
    }
}
