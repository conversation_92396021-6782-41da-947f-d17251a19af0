using System;
using UnityEngine;

namespace TKFrame.COSResource
{
    public class COSBundle : IRefCounter
    {
        public string BundlePath;
        public TKDictionary<string, COSBundleAsset> m_assetDict = new TKDictionary<string, COSBundleAsset>();
        private bool m_unloadAB = false;

        private int m_refCount = 0;
        public int RefCount
        {
            get
            {
                return m_refCount;
            }
            set
            {
                //if (m_refCount > value)
                //    TKFrame.Diagnostic.Log("[COSBundle.Release] " + BundlePath + " " + m_refCount + " -> " + value);
                //else
                //    TKFrame.Diagnostic.Log("[COSBundle.Retain] " + BundlePath + " " + m_refCount + " -> " + value);
                m_refCount = value;
            }
        }

        public COSBundle(string bundlePath, bool unloadAB)
        {
            this.BundlePath = bundlePath;
            this.m_unloadAB = unloadAB;
        }

        public COSBundleAsset AddAsset(string assetName, UnityEngine.Object Object)
        {
            COSBundleAsset asset = null;
            if (!m_assetDict.TryGetValue(assetName, out asset))
            {
                asset = new COSBundleAsset(this, assetName, Object, OnAssetDispose);
                m_assetDict.Add(assetName, asset);
                return asset;
            }
            else
            {
                if (asset.Object == Object)
                {
                    return asset;
                }
                else if (asset.Object == null)
                {
                    asset.SetObject(Object);
                    return asset;
                }
                else
                {
                    Diagnostic.Error($"[COSBundle.AddAsset] bundlePath: {BundlePath} add asset: {assetName} faild! asset allready loaded!");
                    return asset;
                }
            }
        }

        public COSBundleAsset GetAsset(string assetName)
        {
            if (m_assetDict.TryGetValue(assetName, out COSBundleAsset asset))
            {
                return asset;
            }
            //Diagnostic.Error($"[COSBundle.GetAsset] find bundlePath: {BundlePath} assetName: {assetName} Faild! asset not load!");
            return null;
        }

        public bool HasRef()
        {
            if (RefCount > 0)
                return true;

            foreach (var item in m_assetDict)
            {
                if (item.Value.RefCount > 0)
                    return true;
            }
            return false;
        }

        public void UnloadAll()
        {
            foreach (var item in m_assetDict)
            {
                item.Value.Unload();
            }
            m_assetDict.Clear();

            if (m_unloadAB)
                COS.ABManager.UnloadAssetBundle(BundlePath);
        }

        private void OnAssetDispose(string assetName)
        {
            m_assetDict.Remove(assetName);
        }

        public void Dispose()
        {
#if UNITY_EDITOR
            COSLog.Log($"[COSBundle.Dispose]url: {BundlePath}");
#endif
        }
    }

    public class COSBundleAsset : IRefCounter
    {
        public COSBundle bundle { get; private set; }
        public string assetName { get; private set; }
        public UnityEngine.Object Object { get; private set; }

        private int m_refCount = 0;
        public int RefCount
        {
            get
            {
                return m_refCount;
            }
            set
            {
                //if (m_refCount > value)
                //    TKFrame.Diagnostic.Log("[COSBundleAsset.Release] " + assetName + " " + m_refCount + " -> " + value);
                //else
                //    TKFrame.Diagnostic.Log("[COSBundleAsset.Retain] " + assetName + " " + m_refCount + " -> " + value);
                m_refCount = value;
            }
        }

        public Action<string> onDispose { get; private set; }

        public COSBundleAsset(COSBundle bundle, string assetName, UnityEngine.Object Object, Action<string> onDispose)
        {
            this.bundle = bundle;
            this.assetName = assetName;
            this.Object = Object;
            this.onDispose = onDispose;
        }

        public void SetObject(UnityEngine.Object Object)
        {
            this.Object = Object;
        }

        public T Get<T>() where T : UnityEngine.Object
        {
            return Object as T;
        }

        public void Dispose()
        {
            Unload();
            onDispose?.Invoke(assetName);

#if UNITY_EDITOR
            COSLog.Log($"[COSBundleAsset.Dispose]url: {assetName}");
#endif
        }

        public void Unload()
        {
            if (Object != null)
            {
                var gameObject = Object as GameObject;
                if (gameObject != null)
                {
                    //GameObject.DestroyImmediate(gameObject, true);
                    return;
                }

                var component = Object as Component;
                if (component != null)
                {
                    //GameObject.Destroy(component);
                    return;
                }

                COSLog.Log($"[COSBundleAsset.Unload] bundle: {bundle.BundlePath} assetName: {assetName}");
                Resources.UnloadAsset(Object);
            }
        }
    }
}