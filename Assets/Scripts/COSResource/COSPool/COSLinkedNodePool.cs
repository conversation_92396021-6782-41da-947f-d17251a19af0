using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TKFrame.COSResource
{

    public interface ICOSLinkedNodePool
    {
        int GetUsingCount();
        int GetPoolCount();
        void Clear();
    }

    public static class COSLinkedNodePool
    {
        private static TKDictionary<string, ICOSLinkedNodePool> m_poolDict = new TKDictionary<string, ICOSLinkedNodePool>();

        public static void Clear()
        {
            foreach (var item in m_poolDict)
            {
                item.Value.Clear();
            }
        }

        public static LinkedListNode<T> Get<T>(T value) where T : class
        {
            var type = typeof(T).FullName;
            if (!m_poolDict.TryGetValue(type, out ICOSLinkedNodePool pool))
            {
                pool = new COSLinkedNodePoolItem<T>();
                m_poolDict.Add(type, pool);
            }
            var p = pool as COSLinkedNodePoolItem<T>;
            if (p != null)
            {
                return p.Get(value);
            }
            else
            {
                return new LinkedListNode<T>(value);
            }
        }

        public static void Free<T>(LinkedListNode<T> node) where T : class
        {
            var type = typeof(T).FullName;
            if (!m_poolDict.TryGetValue(type, out ICOSLinkedNodePool pool))
            {
                pool = new COSLinkedNodePoolItem<T>();
                m_poolDict.Add(type, pool);
            }
            var p = pool as COSLinkedNodePoolItem<T>;
            if (p != null)
            {
                p.Free(node);
            }
        }

        public static void Display(ICOSConsole sb)
        {
            sb.AppendLine("COSLinkedNodePool: ");
            foreach (var item in m_poolDict)
            {
                sb.AppendLine(string.Format("    {0} {1} {2}", item.Key, item.Value.GetUsingCount(), item.Value.GetPoolCount()));
            }
        }
    }

    public class COSLinkedNodePoolItem<T> : ICOSLinkedNodePool where T : class
    {
        public TKStack<LinkedListNode<T>> m_pool = new TKStack<LinkedListNode<T>>();

        private int m_usingCount = 0;

        public void Clear()
        {
            m_pool.Clear();
        }

        public LinkedListNode<T> Get(T value)
        {
            ++m_usingCount;
            if (m_pool.Count != 0)
            {
                var p = m_pool.Pop();
                p.Value = value;
                return p;
            }
            else
            {
                var p = new LinkedListNode<T>(value);
                return p;
            }
        }

        public void Free(LinkedListNode<T> item)
        {
            --m_usingCount;
            if (!m_pool.Contains(item))
            {
                item.Value = null;
                m_pool.Push(item);
            }
            else
            {
#if UNITY_EDITOR && !JK_RELEASE
                COSLog.Error($"COSLinkedNodePool.Free to pool error! m_usingCount: " + m_usingCount + " item: " + item.ToString());
#endif
            }
        }

        public int GetUsingCount()
        {
            return m_usingCount;
        }

        public int GetPoolCount()
        {
            return m_pool.Count;
        }
    }
}
