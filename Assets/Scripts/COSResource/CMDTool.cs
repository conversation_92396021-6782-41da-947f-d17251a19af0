using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace TKFrame.COSResource
{
#if UNITY_EDITOR
    public static class CMDTool
    {
        private static string shellApp
        {
            get
            {
#if UNITY_EDITOR_WIN
                string app = "cmd.exe";
#elif UNITY_EDITOR_OSX
			    string app = "bash";
#endif
                return app;
            }
        }

        public static bool ProcessCMDSync(string cmd, out string output, out string error)
        {
            string workDir = Application.dataPath.Remove(Application.dataPath.LastIndexOf("/Assets", StringComparison.Ordinal));
            return ProcessCommandSync(cmd, workDir, null, out output, out error);
        }

        public static bool ProcessCommandSync(string cmd, string workDirectory, List<string> environmentVars, out string output, out string error)
        {
            System.Diagnostics.Process p = null;
            try
            {
                System.Diagnostics.ProcessStartInfo start = new System.Diagnostics.ProcessStartInfo(shellApp);

#if UNITY_EDITOR_OSX
					string splitChar = ":";
					start.Arguments = "-c";
#elif UNITY_EDITOR_WIN
                string splitChar = ";";
                start.Arguments = "/c";
#endif

                if (environmentVars != null)
                {
                    foreach (string var in environmentVars)
                    {
                        start.EnvironmentVariables["PATH"] += (splitChar + var);
                    }
                }

                start.Arguments += (" \"" + cmd + " \"");
                start.CreateNoWindow = true;
                start.ErrorDialog = true;
                start.UseShellExecute = false;
                start.WorkingDirectory = workDirectory;

                if (start.UseShellExecute)
                {
                    start.RedirectStandardOutput = false;
                    start.RedirectStandardError = false;
                    start.RedirectStandardInput = false;
                }
                else
                {
                    start.RedirectStandardOutput = true;
                    start.RedirectStandardError = true;
                    start.RedirectStandardInput = true;
                    start.StandardOutputEncoding = Encoding.UTF8;
                    start.StandardErrorEncoding = Encoding.UTF8;
                }

                p = System.Diagnostics.Process.Start(start);
                p.ErrorDataReceived += delegate (object sender, System.Diagnostics.DataReceivedEventArgs e)
                {
                    UnityEngine.Debug.LogError(e.Data);
                };
                p.OutputDataReceived += delegate (object sender, System.Diagnostics.DataReceivedEventArgs e)
                {
                    UnityEngine.Debug.LogError(e.Data);
                };
                p.Exited += delegate (object sender, System.EventArgs e)
                {
                    UnityEngine.Debug.LogError(e.ToString());
                };

                output = p.StandardOutput.ReadToEnd();
                error = p.StandardError.ReadToEnd();
                return string.IsNullOrEmpty(error);
            }
            catch (Exception e)
            {
                UnityEngine.Debug.LogError(e);
                if (p != null)
                {
                    p.Close();
                }
            }

            output = string.Empty;
            error = string.Empty;
            return false;
        }
    }
#endif
}
