using System;
using System.Collections.Generic;
#if UNITY_EDITOR
using System.IO;
using UnityEditor;
#endif
using UnityEngine;
using ZGameChess;
using Object = UnityEngine.Object;

namespace TKFrame.Asset
{
    public enum LodType
    {
        [EnumName("None")]    
        none = 0,
        [EnumName("海外极低配(1G)")]
        sealow = 1,
        [EnumName("低配(2G)(棋子[low])")]
        low = 2,
        [EnumName("中配(棋子[high])")]
        middle = 3,
        [EnumName("高配(棋子[pad])")]
        high = 4,
    }
    
    public enum RecoverType
    {
        [EnumName("覆盖模式", "直接用配置中的资源赋值回去")]
        overlay = 0,
        [EnumName("保留资源模式", "如果原有节点已存在资源，则使用原有节点的资源，否则使用配置中的资源赋值回去")] //注意: Recover_Animation_State_Arr暂时不支持保留资源的还原模式，可以直接去lod_cfg里去修改先.
        reserve_res = 1,
    }
    
    /// <summary>
    /// 资源组 可以加载不同级别的lod
    /// </summary>
    public class TKAssetGroup : MonoBehaviour
    {
        [SerializeField]
        private List<TKAssetPath> m_lods = new List<TKAssetPath>();

        //当前应用的lodType;
        public LodType AppliedLodType { get; private set; } = LodType.none;
        //因为棋子部分要使用prefab作为传入，并且会并行预加载，所以这里做event;
        private event Action _OnLodChangedCompleteCallbackEvent;

        public void RecoverLod(IReleaseList releaseList, Action OnLodChangedCompleteCallback = null, LodType lodType = LodType.none, RecoverType recoverType = RecoverType.overlay)
        {
            if (lodType == LodType.none)
            {
                lodType = GetLodTypeByPerformaceOrCfg();
            }
            // if (this.AppliedLodType != LodType.none)
            // {
            //     this.Release();
            // }
            this.AppliedLodType = lodType;
            this._OnLodChangedCompleteCallbackEvent += OnLodChangedCompleteCallback;
            bool isCanRecoverLodAssets = true;
            do
            {
                bool hasOprValid = CheckRecoverByType(lodType, releaseList, recoverType);
                if(hasOprValid)break;
                //向下兼容的
                hasOprValid = RecoverLodBackward(lodType, releaseList, recoverType);
                if(hasOprValid)break;
                //向上兼容的;
                hasOprValid = RecoverLodUpward(lodType, releaseList, recoverType);
                if(hasOprValid)break;
                isCanRecoverLodAssets = false;
            } while (false);

            if (!isCanRecoverLodAssets)
            {
                //给个保底返回;
                for (int i = 0; i < this.m_lods.Count; i++)
                {
                    TKAssetPath tkAssetPath = this.m_lods[i];
                    if (tkAssetPath.lodType == lodType)
                    {
                        tkAssetPath.RecoverLodAssets(this.transform, releaseList, OnLoaded, recoverType); 
                        break;
                    }
                }
            }
        }
        
        private bool RecoverLodBackward(LodType fromLodTypeExclude, IReleaseList releaseList, RecoverType recoverType = RecoverType.overlay)
        {
            for (int i = (int)fromLodTypeExclude - 1; i > (int)LodType.none; i--)
            {
                var checkLodType = (LodType)i;
                if (CheckRecoverByType(checkLodType, releaseList, recoverType))
                {
                    return true;
                }
            }
            return false;
        }

        public bool CheckRecoverByType(LodType checkLodType, IReleaseList releaseList, RecoverType recoverType = RecoverType.overlay)
        {
            for (int k = 0; k < this.m_lods.Count; k++)
            {
                TKAssetPath tkAssetPath = this.m_lods[k];
                if (tkAssetPath.lodType == checkLodType)
                {
                    bool hasOprValid = SetLodGameobjectActive(this.transform, checkLodType, true);
                    if (hasOprValid)
                    {
                        tkAssetPath.RecoverLodAssets(this.transform, releaseList, OnLoaded, recoverType);
                        return true;
                    }
                    break;
                }
            }
            return false;
        }

        /// <summary>
        /// 向上寻找显示;
        /// </summary>
        /// <param name="fromLodTypeExclude">起始LodType（不包括这个）</param>
        /// <param name="releaseList"></param>
        /// <returns></returns>
        private bool RecoverLodUpward(LodType fromLodTypeExclude, IReleaseList releaseList, RecoverType recoverType = RecoverType.overlay)
        {
            for (int i = (int)fromLodTypeExclude + 1; i <= (int)LodType.high; i++)
            {
                var checkLodType = (LodType)i;
                if (CheckRecoverByType(checkLodType, releaseList, recoverType))
                {
                    return true;
                }
            }
            return false;
        }

        private void OnLoaded(TKAssetMap t, string assetBundleName)
        {
            this._OnLodChangedCompleteCallbackEvent?.Invoke();
            this._OnLodChangedCompleteCallbackEvent = null;
        }

        public void Release()
        {
            this._OnLodChangedCompleteCallbackEvent = null;
            for (int i = 0; i < this.m_lods.Count; i++)
            {
                TKAssetPath tkAssetPath = this.m_lods[i];
                if (tkAssetPath.lodType == this.AppliedLodType)
                {
                    tkAssetPath.Release();
                }
            }
            this.AppliedLodType = LodType.none;
        }

        public static string GetLodNameByLodType(LodType lodType)
        {
            switch (lodType)
            {
                case LodType.sealow:
                {
                    return "lod_l";
                }
                case LodType.low:
                {
                    return "lod_l";
                }
                case LodType.middle:
                {
                    return "lod_h";
                }
                case LodType.high:
                {
                    return "lod_s";
                }
            }
            return "";
        }

        public static string[] allLodNameList = { "lod_l", "lod_h", "lod_s" };
        
        public static void FindTransform(Transform root, string startName, ref List<Transform> list)
        {
            if (root.name.Contains(startName))
            {
                list.Add(root);
                return;
            }
            foreach (Transform child in root)
            {
                FindTransform(child, startName, ref list);
            }
        }

        public static bool SetLodGameobjectActive(Transform root, LodType lodType, bool value)
        {
            string lodName = GetLodNameByLodType(lodType);
            //收集名为lod的gameobject;
            List<Transform> lodNodeTransList = new List<Transform>();
            FindTransform(root, lodName, ref lodNodeTransList);
            //显/隐部分lod gameobject;
            for (int i = 0; i < lodNodeTransList.Count; i++)
            {
                lodNodeTransList[i].gameObject.SetActive(value);
            }
            return lodNodeTransList.Count > 0;
        }
        
        /**
         * 通过机器性能或者自定义配置去返回LodType;
         */
        public static LodType GetLodTypeByPerformaceOrCfg()
        {
            switch (ModelPathUtil.GlobalModelLod)
            {
                case ModelPathUtil.ModelLod.None:  // 正常对局进入的话 走这里
                    {
                        if (HardwareCheck.GetMemoryGB() <= 2.1f)
                        {   // 小于等于2G的机器 就不要用高模了 老老实实低模
                            if (HardwareCheck.GetMemoryGB() <= 1.1f)
                            {
                                return LodType.sealow;
                            }
                            return LodType.low;
                        }
                        else if (NotchSizeImp.IsPad)
                        {
                            return LodType.high;
                        }
                        else
                        {
                            var dp = GameLOD.Instance.DevicePower;
                            if (dp == EDevicePower.EDP_Low || dp == EDevicePower.EDP_Middle)
                            {
                                return LodType.low;
                            }
                            else
                            {
                                return LodType.middle;
                            }
                        }
                    }
                case ModelPathUtil.ModelLod.Low:
                    return LodType.low;
                case ModelPathUtil.ModelLod.High:
                    return LodType.middle;
                case ModelPathUtil.ModelLod.Pad:
                    return LodType.high;
                default:
                    break;
            }
            return LodType.middle; 
        }
        
        /// <summary>
        /// </summary>
        /// <param name="normaLAbName"></param>
        /// <param name="lodType"></param>
        /// <returns></returns>
        public static string ConvertNormalToTKAssetGroupAbName(string normalAbName, LodType lodType = LodType.none)
        {
            if (lodType == LodType.none)
            {
                lodType = GetLodTypeByPerformaceOrCfg();
            }

            string finalAbName = normalAbName;
#if  ENABLE_ASSET_BUNDLE_EXTEND
            bool hasUnity3dExtension = false;
            if (normalAbName.Contains(".unity3d"))
            {
                finalAbName = normalAbName.Replace(".unity3d", "") ;
                hasUnity3dExtension = true;
            }
#endif
            string lodTypeStr = Enum.GetName(typeof(LodType), lodType);
            finalAbName = string.Format("{0}_{1}_res", finalAbName, lodTypeStr);
#if  ENABLE_ASSET_BUNDLE_EXTEND
            if (hasUnity3dExtension)
            {
                finalAbName += ".unity3d";
            }
#endif
            return finalAbName;
        }
        
#if UNITY_EDITOR
        public List<TKAssetPath> Lods { get { return m_lods; } }

        public void ResetData()
        {
            m_lods.Clear();   
        }

        public void RecoverAllLodShows()
        {
            foreach (var lodItem in this.Lods)
            {
                lodItem.CheckSetDetailConfig();
            }
            if (this.Lods.Count > 0 && this.Lods[0].DetailConfig)
            {
                for (int i = (int)LodType.sealow; i <= (int)LodType.high; i++)
                {
                    string lodTypeStr = Enum.GetName(typeof(LodType), (LodType)i);
                    this.TestLoad((LodType)i);
                }
            }
        }
        
        private void TestLoad(LodType lodType)
        {
            TKAssetGroup newTkAssetGroup = this.gameObject.GetComponent<TKAssetGroup>();
            IReleaseList newIReleaseList = this.gameObject.GetComponent<IReleaseList>();
            newTkAssetGroup.RecoverLod(newIReleaseList, () =>
            {
                Debug.Log("加载完毕: "+newTkAssetGroup.gameObject.name+" , "+lodType.ToString());
            }, lodType);
        }

        public TKAssetPath CheckAddTkAssetPath(LodType lodType, string abName, string assetName)
        {
            for (int i = 0; i < this.m_lods.Count; i++)
            {
                TKAssetPath tkAssetPath = this.m_lods[i];
                if (tkAssetPath.lodType == lodType)
                {
                    return tkAssetPath;
                }
            }
            TKAssetPath newTkAssetPath = new TKAssetPath(lodType, abName, assetName);
            this.m_lods.Add(newTkAssetPath);
            return newTkAssetPath;
        }

        public TKAssetPath GetTkAssetPath(LodType lodType)
        {
            for (int i = 0; i < this.m_lods.Count; i++)
            {
                TKAssetPath tkAssetPath = this.m_lods[i];
                if (tkAssetPath.lodType == lodType)
                {
                    return tkAssetPath;
                }
            }
            return null;
        }

        public string GetAbName(LodType lodType)
        {
            string assetPath = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(this.gameObject);
            var assetImporter = AssetImporter.GetAtPath(assetPath);
            if (assetImporter != null)
            {
                // string lodTypeStr = Enum.GetName(typeof(LodType), lodType);
                // return string.Format("{0}_{1}_res", assetImporter.assetBundleName, lodTypeStr);
                return ConvertNormalToTKAssetGroupAbName(assetImporter.assetBundleName, lodType);
            }
            return "None";
        }

        public string GetAssetName(LodType lodType)
        {
            // string lodTypeStr = Enum.GetName(typeof(LodType), lodType);
            // return string.Format("{0}_{1}", this.gameObject.name, lodTypeStr);
            return this.gameObject.name;
        }

        public bool CheckTransformLodHasAssets()
        {
            for (int i = (int)LodType.sealow; i <= (int)LodType.high; i++)
            {
                LodType curLodType = (LodType)i;
                TKAssetMap newAssetMap = new TKAssetMap();
                newAssetMap.GenLod(this.transform, curLodType, null);
                if (!newAssetMap.IsDataEmpty())
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 里面会自动判断是否要执行AssetDataBase.Refresh;
        /// </summary>
        public void CollectLods()
        {
            EditorUtility.SetDirty(this.gameObject);
            //生成对应的lod配置文件;
            this.ResetData();
            bool isNeedAssetRefreshInvoke = false;
            for (int i = (int)LodType.sealow; i <= (int)LodType.high; i++)
            {
                LodType curLodType = (LodType)i;
                string newAbName = this.GetAbName(curLodType);
                string newAssetName = this.GetAssetName(curLodType);
                TKAssetPath curTkAssetPath = this.CheckAddTkAssetPath(curLodType, newAbName, newAssetName);
                string curAssetMapPath = curTkAssetPath.GetLodCfgPathToSaveDiskByTkAssetPath(this.transform);
                TKAssetMap curAssetMap = AssetDatabase.LoadAssetAtPath<TKAssetMap>(curAssetMapPath);
                bool isNewCreateAssetMap = false;
                if (curAssetMap == null)
                {
                    curAssetMap = ScriptableObject.CreateInstance<TKAssetMap>();
                    isNewCreateAssetMap = true;
                }
                else
                {
                    EditorUtility.SetDirty(curAssetMap);
                }

                curAssetMap.GenLod(this.transform, curLodType, curTkAssetPath);

                #region 保存相关操作
                
                string dirName = Path.GetDirectoryName(curAssetMapPath);
                if (!Directory.Exists(dirName))
                {
                    isNeedAssetRefreshInvoke = true;
                    Directory.CreateDirectory(dirName);
                }

                if (isNewCreateAssetMap)
                {
                    isNeedAssetRefreshInvoke = true;
                    Debug.Log("准备创建TKAssetMap资源: "+curAssetMapPath);
                    AssetDatabase.CreateAsset(curAssetMap, curAssetMapPath);
                    var assetImporter = AssetImporter.GetAtPath(curAssetMapPath);
                    string abName = this.GetAbName(curTkAssetPath.lodType);
                    assetImporter.SetAssetBundleNameAndVariant(abName, "unity3d");
                }
                #endregion
            }
            #region 收尾
            //清空资源引用;
            TKAssetMap.ClearLodRefRes(this.transform);
            for (int i = (int)LodType.sealow; i <= (int)LodType.high; i++)
            {
                LodType curLodType = (LodType)i;
                TKAssetGroup.SetLodGameobjectActive(this.transform, curLodType, false);
            }
            AssetDatabase.SaveAssets();
            if (isNeedAssetRefreshInvoke)
            {
                AssetDatabase.Refresh();
            }

            #endregion
        }

        /// <summary>
        /// 在预制体实例创建后的处理;
        /// </summary>
        /// <param name="go"></param>
        public static void OnAfterPrefabInstanceCreated(GameObject go)
        {
            TKAssetGroup tkAssetGroup = go.GetOrAddComponent<TKAssetGroup>();
            tkAssetGroup.CollectLods();
        }
        
        /// <summary>
        /// 获取配置的路径;
        /// </summary>
        /// <returns></returns>
        public string[] GetTKAssetMapFilePaths()
        {
            List<string> filePathList = new List<string>();
            for (int i = 0; i < this.m_lods.Count; i++)
            {
                var lodData = this.m_lods[i];
                string savePath = lodData.GetLodCfgPathToSaveDiskByTkAssetPath(this.transform);
                filePathList.Add(savePath);
            }
            return filePathList.ToArray();
        }

        public T GetAsset<T>(TKAssetLinkType linkType, LodType fromLodTypeIndex) where T : UnityEngine.Object
        {
            LodType lodType = fromLodTypeIndex;
            for (int i = (int)lodType; i > (int)LodType.none; i--)
            {
                TKAssetPath tkAssetPath = this.GetTkAssetPath((LodType)i);
                if (tkAssetPath != null)
                {
                    tkAssetPath.CheckSetDetailConfig();
                    if (tkAssetPath.DetailConfig)
                    {
                        for (int k = 0; k < tkAssetPath.DetailConfig.assets.Count; k++)
                        {
                            var tkAssetLink = tkAssetPath.DetailConfig.assets[k];
                            if (tkAssetLink.linkType == linkType)
                            {
                                return (T)tkAssetLink.asset;
                            }
                        }
                    }
                }
            }
            return default(T);
        }
        
        public List<T> GetAssets<T>(TKAssetLinkType linkType, LodType fromLodTypeIndex) where T : UnityEngine.Object
        {
            List<T> rtnList = new List<T>();
            LodType lodType = fromLodTypeIndex;
            for (int i = (int)lodType; i > (int)LodType.none; i--)
            {
                TKAssetPath tkAssetPath = this.GetTkAssetPath((LodType)i);
                if (tkAssetPath != null)
                {
                    tkAssetPath.CheckSetDetailConfig();
                    if (tkAssetPath.DetailConfig)
                    {
                        for (int k = 0; k < tkAssetPath.DetailConfig.assets.Count; k++)
                        {
                            var tkAssetLink = tkAssetPath.DetailConfig.assets[k];
                            if (tkAssetLink.linkType == linkType)
                            {
                                rtnList.Add((T)tkAssetLink.asset);
                            }
                        }
                    }
                }
            }
            return rtnList;
        }
        
        public List<TKAssetLink> GetAssetLinks(TKAssetLinkType linkType, LodType fromLodTypeIndex)
        {
            List<TKAssetLink> rtnList = new List<TKAssetLink>();
            LodType lodType = fromLodTypeIndex;
            for (int i = (int)lodType; i > (int)LodType.none; i--)
            {
                TKAssetPath tkAssetPath = this.GetTkAssetPath((LodType)i);
                if (tkAssetPath != null)
                {
                    tkAssetPath.CheckSetDetailConfig();
                    if (tkAssetPath.DetailConfig)
                    {
                        for (int k = 0; k < tkAssetPath.DetailConfig.assets.Count; k++)
                        {
                            var tkAssetLink = tkAssetPath.DetailConfig.assets[k];
                            if (tkAssetLink.linkType == linkType)
                            {
                                rtnList.Add(tkAssetLink);
                            }
                        }
                    }
                }
            }
            return rtnList;
        }
#endif
    }
}