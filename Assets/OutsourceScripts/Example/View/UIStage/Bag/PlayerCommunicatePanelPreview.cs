using System;
using System.Collections.Generic;
using TKFrame;
using UnityEngine;
using UnityEngine.UI;
using ZGame;
using TKPlugins;
using GfxFramework;

namespace Outsource.Stage
{

    //PreviewPlayerExpressionPanel
    public class PlayerCommunicatePanelPreview : TKUIBehaviour
    {
        [UIObject("Toggle_Expression")] public Toggle Toggle_Expression { get; set; }
        [UIObject("Toggle_Action")] public Toggle Toggle_Action { get; set; }
        [UIObject("Toggle_MagicExpression")] public Toggle Toggle_MagicExpression { get; set; }

        [UIObject("Content")] public CircleLayoutGroup Content { get; set; }
        [UIObject("IngameEmoteItem")] public GameObject IngameEmoteItem { get; set; }
        [UIObject("Btn_Close")] public Button Btn_Close { get; set; }

        [UIObject("Ani")] public GameObject Ani { get; set; }
        [UIObject("Effect_UI_JuNei_BPJinBian")] public GfxRoot_Unity Effect_UI_JuNei_BPJinBian { get; set; }
        [UIObject("Effect_UI_JuNei_ExpressionPanel")] public GfxRoot_Unity Effect_UI_JuNei_ExpressionPanel { get; set; }


        protected int m_tinyId;
        protected List<int> m_expressionList;
        protected List<int> m_actionList;
        protected List<int> m_magicExpressionList;

        private TKGraphicRaycaster m_tkRay;

        public Action onClosed;

        protected override void Awake()
        {
            base.Awake();

            //InitItems();

            if (Toggle_Expression != null) Toggle_Expression.onValueChanged.AddListener(OnToggleChanged);
            if (Toggle_Action != null) Toggle_Action.onValueChanged.AddListener(OnToggleChanged);
            if (Toggle_MagicExpression != null) Toggle_MagicExpression.onValueChanged.AddListener(OnToggleChanged);

            m_tkRay = GetComponent<TKGraphicRaycaster>();

            if (IngameEmoteItem != null)
            {
                IngameEmoteItem.SetActive(false);
            }

            if (Btn_Close != null)
                Btn_Close.onClick.AddListener(OnClickCloseBtn);

            if (Effect_UI_JuNei_BPJinBian != null)
            {
                Effect_UI_JuNei_BPJinBian.OnGfxPlayEnd = OnGfxEnd;
                GameUtil.SetLayerRecursive(Effect_UI_JuNei_BPJinBian.gameObject, GameObjectLayer.UI);
            }
            if (Effect_UI_JuNei_ExpressionPanel != null)
            {
                Effect_UI_JuNei_ExpressionPanel.OnGfxPlayEnd = OnGfxEnd;
                GameUtil.SetLayerRecursive(Effect_UI_JuNei_ExpressionPanel.gameObject, GameObjectLayer.UI);
            }

            if (Ani != null)
                Ani.SetActive(false);
        }

        private void OnGfxEnd(GfxRoot root)
        {
            if (root != null && root.gameObject != null)
                root.gameObject.SetActive(false);
        }

        private void OnClickCloseBtn()
        {
            SetScaleToZero(true);
        }

        public void SetScaleToZero(bool isZero)
        {
            GameUtil.SetScaleToZero(gameObject, isZero);

            if (m_tkRay != null)
            {
                m_tkRay.DisableRaycaster = isZero;
            }

            // 显示的时候播一下特效..
            if (!isZero)
            {
                if (Effect_UI_JuNei_BPJinBian != null)
                    Effect_UI_JuNei_BPJinBian.gameObject.SetActive(true);
                if (Effect_UI_JuNei_ExpressionPanel != null)
                    Effect_UI_JuNei_ExpressionPanel.gameObject.SetActive(true);
                if (Ani != null)
                    Ani.SetActive(true);
            }
            else
            {
                if (Ani != null)
                    Ani.SetActive(false);
            }

            if (isZero && onClosed != null)
                onClosed();
        }

        private void OnToggleChanged(bool isOn)
        {
          
        }
    }
}