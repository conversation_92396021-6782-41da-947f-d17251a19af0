using System;
using System.Collections;
using System.Collections.Generic;
using TKFrame;
using UnityEngine;
using UnityEngine.UI;

namespace Outsource.Stage
{
    public class PreviewActionController : TKUIBehaviour
    {
        [UIObject("ToggleAction1")]
        public Toggle ToggleAction1 { get; set; }

        [UIObject("ToggleAction2")]
        public Toggle ToggleAction2 { get; set; }

        [UIObject("ToggleAction3")]
        public Toggle ToggleAction3 { get; set; }

        [UIObject("ToggleAction4")]
        public Toggle ToggleAction4 { get; set; }

        [UIObject("ToggleAction5")]
        public Toggle ToggleAction5 { get; set; }

        protected Action<int> m_onClickCallback;

        protected Toggle selectedToggle;

        private List<Toggle> toggleList = null;

        protected List<int> actionList = null;

        protected float actionSelectDisplayTimer = 0;


        protected override void Start()
        {
            base.Start();

            Init();

            if (actionList != null)
            {
                for (int i = 0, imx = toggleList.Count; i < imx; i++)
                {
                    if (actionList.Count > i)
                    {
                        int index = actionList[i];
                        if (null != toggleList[i])
                        {
                            toggleList[i].onValueChanged.AddListener((isOn) =>
                            {
                                if (isOn)
                                {
                                    selectedToggle = toggleList[index];
                                    OnToggleClicked(index);
                                    actionSelectDisplayTimer = 0.6f;
                                }
                            });
                            toggleList[i].gameObject.SetActive2(true);
                        }
                    }
                    else
                    {
                        toggleList[i].onValueChanged.RemoveAllListeners();
                        toggleList[i].gameObject.SetActive2(false);
                    }

                }

                actionList = null;
            }
        }

        private void OnSelectTimer()
        {
            if (actionSelectDisplayTimer > 0)
            {
                actionSelectDisplayTimer -= Time.deltaTime;
                if (actionSelectDisplayTimer <= 0)
                {
                    if (selectedToggle != null)
                    {
                        selectedToggle.isOn = false;
                    }
                }
            }
        }

        private void LateUpdate()
        {
            OnSelectTimer();
        }


        void Init()
        {


            toggleList = new List<Toggle>();
            if (null != ToggleAction1)
            {
                toggleList.Add(ToggleAction1);
            }
            if (null != ToggleAction2)
            {
                toggleList.Add(ToggleAction2);
            }
            if (null != ToggleAction3)
            {
                toggleList.Add(ToggleAction3);
            }
            if (null != ToggleAction4)
            {
                toggleList.Add(ToggleAction4);
            }

            if (null != ToggleAction5)
            {
                toggleList.Add(ToggleAction5);
            }
        }



        public void SetActionList(List<int> actionList)
        {
            this.actionList = actionList;

            if (null == toggleList || toggleList.Count < 1)
            {
                return;
            }
            int tempRecordIdx = -1;


            for (int i = 0, imx = toggleList.Count; i < imx; i++)
            {
                if (actionList.Count > i)
                {
                    int index = actionList[i];
                    if (null != toggleList[i])
                    {
                        toggleList[i].onValueChanged.AddListener((isOn) =>
                        {
                            if (isOn)
                            {
                                selectedToggle = toggleList[index];
                                OnToggleClicked(index);
                            }
                        });
                        toggleList[i].gameObject.SetActive2(true);
                    }
                }
                else
                {
                    if (tempRecordIdx == -1)
                        tempRecordIdx = -1;

                    toggleList[i].onValueChanged.RemoveAllListeners();
                    toggleList[i].gameObject.SetActive2(false);
                }

                this.actionList = null;

                if (tempRecordIdx != -1 && tempRecordIdx < toggleList.Count)
                {
                    toggleList[tempRecordIdx].transform.GetChild(0).Find("Line")?.SetActive(false);
                }
            }
        }

        public void SetClickHandler(Action<int> handler)
        {
            m_onClickCallback = handler;
        }

        void OnToggleClicked(int index)
        {
            if (null != m_onClickCallback)
            {
                m_onClickCallback(index);
            }
        }


    }



}
