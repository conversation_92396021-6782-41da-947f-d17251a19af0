using System.Collections;
using System.Collections.Generic;
using TKFrame;
using UnityEngine;
using ZGameChess;

namespace Outsource.Stage
{
    public class MallStage : BaseStage
    {
        /// <summary>
        /// �����Ԥ����һЩ��Դ������һЩ3D��Դ����UI����ҪԤ���ص���Դ��
        /// </summary>
        /// <returns></returns>
        public override IEnumerator buildWorld()
        {
            yield return base.buildWorld();

            gameObject.AddComponent<MapPreviewManager>();
            //yield return LoginBgScene.Global.CheckBgSceneObj();
        }

        /// <summary>
        /// ����ͨ�����ڼ���UIPanel
        /// </summary>
        /// <returns></returns>
        public override IEnumerator buildUI()
        {
            var panel = PanelMgr.ShowScreenPanel<MallPanel>(new PanelDescriptor("os/ui/panel/mallpanel", "MallPanel")) as MallPanel;
            if (panel != null)
            {
                while (!panel.Loaded)
                {
                    yield return null;
                }
            }
        }

        public override void Leave()
        {
            base.Leave();

            TinyPreviewSceneManager.Instance.Hide();
        }
    }
}