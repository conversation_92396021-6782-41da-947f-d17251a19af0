using System.IO;

namespace UniqueInfo.ChessAttackConfig_Gen
{
    public class ReadUniqueInfo_Normal
    {
        // Read Method
        // string Class Func

        // string Ref Func
        public static void System_String_RefRead(ref string data, FastBinaryReader br)
        {
            int index = 0;
            TransferBaseField_Read.Read(ref index, br);
            if(index < 0)
            {
                data = null;
                return;
            }
        
            data = ReadUniqueInfo_NormalPool.instance.Get_System_String(index, br);
        }

        // ChessAttackItemConfig.TriggerCondition
        public static void ChessAttackItemConfig_TriggerCondition_Read(ref ChessAttackItemConfig.TriggerCondition data, FastBinaryReader br)
        {     
            int tmp = 0;
            TransferBaseField_Read.Read(ref tmp, br);
            data = (ChessAttackItemConfig.TriggerCondition)tmp;
        }

        // ChessAttackItemConfig.HideParam.Phase
        public static void ChessAttackItemConfig_HideParam_Phase_Read(ref ChessAttackItemConfig.HideParam.Phase data, FastBinaryReader br)
        {     
            int tmp = 0;
            TransferBaseField_Read.Read(ref tmp, br);
            data = (ChessAttackItemConfig.HideParam.Phase)tmp;
        }

        // ChessAttackItemConfig.HideParam Class Func
        public static void ChessAttackItemConfig_HideParam_Read(ChessAttackItemConfig.HideParam data, FastBinaryReader br)
        {     
            // Field
            TransferBaseField_Read.Read(ref data.m_enable, br);
            ReadUniqueInfo_Normal.ChessAttackItemConfig_HideParam_Phase_Read(ref data.m_hideStage, br);
            TransferBaseField_Read.Read(ref data.m_hideByTime, br);
            TransferBaseField_Read.Read(ref data.m_hideTime, br);
            ReadUniqueInfo_Normal.ChessAttackItemConfig_HideParam_Phase_Read(ref data.m_showStage, br);
        }

        // ChessAttackItemConfig.HideParam Ref Func
        public static void ChessAttackItemConfig_HideParam_RefRead(ref ChessAttackItemConfig.HideParam data, FastBinaryReader br)
        {
            int index = 0;
            TransferBaseField_Read.Read(ref index, br);
            if(index < 0)
            {
                data = null;
                return;
            }
        
            data = ReadUniqueInfo_NormalPool.instance.Get_ChessAttackItemConfig_HideParam(index, br);
        }

        // CharacterHangPoint.SupportHangPointType
        public static void CharacterHangPoint_SupportHangPointType_Read(ref CharacterHangPoint.SupportHangPointType data, FastBinaryReader br)
        {     
            int tmp = 0;
            TransferBaseField_Read.Read(ref tmp, br);
            data = (CharacterHangPoint.SupportHangPointType)tmp;
        }

        // UnityEngine.GameObject Ref Func
        public static void UnityEngine_GameObject_RefRead(ref UnityEngine.GameObject data, FastBinaryReader br)
        {
            int index = 0;
            TransferBaseField_Read.Read(ref index, br);
            if(index < 0)
            {
                data = null;
                return;
            }
        
            data = (UnityEngine.GameObject)ReadUniqueInfo_NormalPool.instance.Get_UnityEngine_Object_Data(index, br);
        }

        // ChessAttackItemConfig.EffectSpawnPos
        public static void ChessAttackItemConfig_EffectSpawnPos_Read(ref ChessAttackItemConfig.EffectSpawnPos data, FastBinaryReader br)
        {     
            int tmp = 0;
            TransferBaseField_Read.Read(ref tmp, br);
            data = (ChessAttackItemConfig.EffectSpawnPos)tmp;
        }

        // ChessAttackItemConfig.HitEffectDir
        public static void ChessAttackItemConfig_HitEffectDir_Read(ref ChessAttackItemConfig.HitEffectDir data, FastBinaryReader br)
        {     
            int tmp = 0;
            TransferBaseField_Read.Read(ref tmp, br);
            data = (ChessAttackItemConfig.HitEffectDir)tmp;
        }

        // ClassAttackCastConfig Class Func
        public static void ClassAttackCastConfig_Read(ClassAttackCastConfig data, FastBinaryReader br)
        {     
            // Field
            ReadUniqueInfo_Normal.CharacterHangPoint_SupportHangPointType_Read(ref data.m_spwanLoc, br);
            TransferBaseField_Read.Read(ref data.m_syncLocPos, br);
            TransferBaseField_Read.Read(ref data.m_syncLocRot, br);
            TransferBaseField_Read.Read(ref data.m_scale, br);
            ReadUniqueInfo_Normal.UnityEngine_GameObject_RefRead(ref data.m_castEffect, br);
            ReadUniqueInfo_Normal.UnityEngine_GameObject_RefRead(ref data.m_mirrorCastEffect, br);
            TransferBaseField_Read.Read(ref data.m_castTime, br);
            ReadUniqueInfo_Normal.ChessAttackItemConfig_EffectSpawnPos_Read(ref data.m_spawnPos, br);
            TransferBaseField_Read.Read(ref data.m_trajectoryPercent, br);
            ReadUniqueInfo_Normal.ChessAttackItemConfig_HitEffectDir_Read(ref data.m_spawnDir, br);
        }

        // ClassAttackCastConfig Ref Func
        public static void ClassAttackCastConfig_RefRead(ref ClassAttackCastConfig data, FastBinaryReader br)
        {
            int index = 0;
            TransferBaseField_Read.Read(ref index, br);
            if(index < 0)
            {
                data = null;
                return;
            }
        
            data = ReadUniqueInfo_NormalPool.instance.Get_ClassAttackCastConfig(index, br);
        }

        // ChessAttackItemConfig.AttackType
        public static void ChessAttackItemConfig_AttackType_Read(ref ChessAttackItemConfig.AttackType data, FastBinaryReader br)
        {     
            int tmp = 0;
            TransferBaseField_Read.Read(ref tmp, br);
            data = (ChessAttackItemConfig.AttackType)tmp;
        }

        // ChessAttackItemConfig.BulletFlyMode
        public static void ChessAttackItemConfig_BulletFlyMode_Read(ref ChessAttackItemConfig.BulletFlyMode data, FastBinaryReader br)
        {     
            int tmp = 0;
            TransferBaseField_Read.Read(ref tmp, br);
            data = (ChessAttackItemConfig.BulletFlyMode)tmp;
        }

        // UnityEngine.AnimationCurve Class Func

        // UnityEngine.AnimationCurve Ref Func
        public static void UnityEngine_AnimationCurve_RefRead(ref UnityEngine.AnimationCurve data, FastBinaryReader br)
        {
            int index = 0;
            TransferBaseField_Read.Read(ref index, br);
            if(index < 0)
            {
                data = null;
                return;
            }
        
            data = ReadUniqueInfo_NormalPool.instance.Get_UnityEngine_AnimationCurve(index, br);
        }

        // GfxFramework.GfxParabolaCurveParam Class Func
        public static void GfxFramework_GfxParabolaCurveParam_Read(GfxFramework.GfxParabolaCurveParam data, FastBinaryReader br)
        {     
            // Field
            TransferBaseField_Read.Read(ref data.m_standardDist, br);
            TransferBaseField_Read.Read(ref data.m_minHeight, br);
            TransferBaseField_Read.Read(ref data.m_maxHeight, br);
            TransferStructField_Read.Read(ref data.m_dir, br);
            ReadUniqueInfo_Normal.UnityEngine_AnimationCurve_RefRead(ref data.m_curve, br);
        }

        // GfxFramework.GfxParabolaCurveParam Ref Func
        public static void GfxFramework_GfxParabolaCurveParam_RefRead(ref GfxFramework.GfxParabolaCurveParam data, FastBinaryReader br)
        {
            int index = 0;
            TransferBaseField_Read.Read(ref index, br);
            if(index < 0)
            {
                data = null;
                return;
            }
        
            data = ReadUniqueInfo_NormalPool.instance.Get_GfxFramework_GfxParabolaCurveParam(index, br);
        }

        // ChessAttackItemConfig.HitBulletAction
        public static void ChessAttackItemConfig_HitBulletAction_Read(ref ChessAttackItemConfig.HitBulletAction data, FastBinaryReader br)
        {     
            int tmp = 0;
            TransferBaseField_Read.Read(ref tmp, br);
            data = (ChessAttackItemConfig.HitBulletAction)tmp;
        }

        // ChessAttackItemBulletTimeEvent.EventType
        public static void ChessAttackItemBulletTimeEvent_EventType_Read(ref ChessAttackItemBulletTimeEvent.EventType data, FastBinaryReader br)
        {     
            int tmp = 0;
            TransferBaseField_Read.Read(ref tmp, br);
            data = (ChessAttackItemBulletTimeEvent.EventType)tmp;
        }

        // System.Collections.Generic.List<UnityEngine.GameObject> Class Func
        public static void System_Collections_Generic_List_1_Begin_UnityEngine_GameObject_End_Read(System.Collections.Generic.ICollection<UnityEngine.GameObject> dataCollection, FastBinaryReader br)
        {
            int len = 0;
            TransferBaseField_Read.Read(ref len, br);
            for (int i = 0; i < len; i++)
            {
                UnityEngine.GameObject data = default(UnityEngine.GameObject);
                ReadUniqueInfo_Normal.UnityEngine_GameObject_RefRead(ref data, br);
                dataCollection.Add(data);
            }
        }

        // System.Collections.Generic.List<UnityEngine.GameObject> Ref Func
        public static void System_Collections_Generic_List_1_Begin_UnityEngine_GameObject_End_RefRead(ref System.Collections.Generic.List<UnityEngine.GameObject> data, FastBinaryReader br)
        {
            int index = 0;
            TransferBaseField_Read.Read(ref index, br);
            if(index < 0)
            {
                data = null;
                return;
            }
        
            data = ReadUniqueInfo_NormalPool.instance.Get_System_Collections_Generic_List_1_Begin_UnityEngine_GameObject_End(index, br);
        }

        // ChessAttackItemBulletTimeEvent Class Func
        public static void ChessAttackItemBulletTimeEvent_Read(ChessAttackItemBulletTimeEvent data, FastBinaryReader br)
        {     
            // Field
            TransferBaseField_Read.Read(ref data.m_timePercent, br);
            ReadUniqueInfo_Normal.ChessAttackItemBulletTimeEvent_EventType_Read(ref data.m_eventType, br);
            ReadUniqueInfo_Normal.System_String_RefRead(ref data.m_param, br);
            ReadUniqueInfo_Normal.System_Collections_Generic_List_1_Begin_UnityEngine_GameObject_End_RefRead(ref data.m_gameObjectList, br);
        }

        // ChessAttackItemBulletTimeEvent Ref Func
        public static void ChessAttackItemBulletTimeEvent_RefRead(ref ChessAttackItemBulletTimeEvent data, FastBinaryReader br)
        {
            int index = 0;
            TransferBaseField_Read.Read(ref index, br);
            if(index < 0)
            {
                data = null;
                return;
            }
        
            data = ReadUniqueInfo_NormalPool.instance.Get_ChessAttackItemBulletTimeEvent(index, br);
        }

        // System.Collections.Generic.List<ChessAttackItemBulletTimeEvent> Class Func
        public static void System_Collections_Generic_List_1_Begin_ChessAttackItemBulletTimeEvent_End_Read(System.Collections.Generic.ICollection<ChessAttackItemBulletTimeEvent> dataCollection, FastBinaryReader br)
        {
            int len = 0;
            TransferBaseField_Read.Read(ref len, br);
            for (int i = 0; i < len; i++)
            {
                ChessAttackItemBulletTimeEvent data = default(ChessAttackItemBulletTimeEvent);
                ReadUniqueInfo_Normal.ChessAttackItemBulletTimeEvent_RefRead(ref data, br);
                dataCollection.Add(data);
            }
        }

        // System.Collections.Generic.List<ChessAttackItemBulletTimeEvent> Ref Func
        public static void System_Collections_Generic_List_1_Begin_ChessAttackItemBulletTimeEvent_End_RefRead(ref System.Collections.Generic.List<ChessAttackItemBulletTimeEvent> data, FastBinaryReader br)
        {
            int index = 0;
            TransferBaseField_Read.Read(ref index, br);
            if(index < 0)
            {
                data = null;
                return;
            }
        
            data = ReadUniqueInfo_NormalPool.instance.Get_System_Collections_Generic_List_1_Begin_ChessAttackItemBulletTimeEvent_End(index, br);
        }

        // ChessAttackMovieConfig.PlayCompletedAction
        public static void ChessAttackMovieConfig_PlayCompletedAction_Read(ref ChessAttackMovieConfig.PlayCompletedAction data, FastBinaryReader br)
        {     
            int tmp = 0;
            TransferBaseField_Read.Read(ref tmp, br);
            data = (ChessAttackMovieConfig.PlayCompletedAction)tmp;
        }

        // ChessAttackMovieConfig Class Func
        public static void ChessAttackMovieConfig_Read(ChessAttackMovieConfig data, FastBinaryReader br)
        {     
            // Field
            TransferBaseField_Read.Read(ref data.uiSortingLayer, br);
            ReadUniqueInfo_Normal.UnityEngine_GameObject_RefRead(ref data.startUIEffect, br);
            TransferBaseField_Read.Read(ref data.startUIEffectTime, br);
            ReadUniqueInfo_Normal.System_String_RefRead(ref data.moviePath, br);
            TransferBaseField_Read.Read(ref data.movieTime, br);
            TransferBaseField_Read.Read(ref data.useCosMovie, br);
            ReadUniqueInfo_Normal.ChessAttackMovieConfig_PlayCompletedAction_Read(ref data.playCompleteAction, br);
            TransferBaseField_Read.Read(ref data.blendInStartTime, br);
            TransferBaseField_Read.Read(ref data.blendInTime, br);
            TransferBaseField_Read.Read(ref data.blendOutStartTime, br);
            TransferBaseField_Read.Read(ref data.blendOutTime, br);
            ReadUniqueInfo_Normal.UnityEngine_GameObject_RefRead(ref data.endUIEffect, br);
            TransferBaseField_Read.Read(ref data.endUIEffectTime, br);
        }

        // ChessAttackMovieConfig Ref Func
        public static void ChessAttackMovieConfig_RefRead(ref ChessAttackMovieConfig data, FastBinaryReader br)
        {
            int index = 0;
            TransferBaseField_Read.Read(ref index, br);
            if(index < 0)
            {
                data = null;
                return;
            }
        
            data = ReadUniqueInfo_NormalPool.instance.Get_ChessAttackMovieConfig(index, br);
        }

        // ChessAttackItemConfig Class Func
        public static void ChessAttackItemConfig_Read(ChessAttackItemConfig data, FastBinaryReader br)
        {     
            // Field
            ReadUniqueInfo_Normal.System_String_RefRead(ref data.m_desc, br);
            TransferBaseField_Read.Read(ref data.m_enable, br);
            ReadUniqueInfo_Normal.ChessAttackItemConfig_TriggerCondition_Read(ref data.m_triggerCondition, br);
            TransferBaseField_Read.Read(ref data.m_lockAttackMoveTime, br);
            ReadUniqueInfo_Normal.ChessAttackItemConfig_HideParam_RefRead(ref data.m_hideOwnerLettleLengend, br);
            ReadUniqueInfo_Normal.ChessAttackItemConfig_HideParam_RefRead(ref data.m_hideEnemyLettleLengend, br);
            ReadUniqueInfo_Normal.ChessAttackItemConfig_HideParam_RefRead(ref data.m_hideHero, br);
            ReadUniqueInfo_Normal.ChessAttackItemConfig_HideParam_RefRead(ref data.m_hideScene, br);
            ReadUniqueInfo_Normal.ChessAttackItemConfig_HideParam_RefRead(ref data.m_hideUI, br);
            TransferBaseField_Read.Read(ref data.m_hasCast, br);
            ReadUniqueInfo_Normal.ClassAttackCastConfig_RefRead(ref data.m_cast, br);
            ReadUniqueInfo_Normal.ChessAttackItemConfig_AttackType_Read(ref data.m_attackType, br);
            ReadUniqueInfo_Normal.ChessAttackItemConfig_BulletFlyMode_Read(ref data.m_bulletFlyMode, br);
            TransferBaseField_Read.Read(ref data.m_useStaticSpawnPosition, br);
            TransferStructField_Read.Read(ref data.m_spawnPosition, br);
            TransferStructField_Read.Read(ref data.m_spawnRotation, br);
            ReadUniqueInfo_Normal.CharacterHangPoint_SupportHangPointType_Read(ref data.m_spwanLoc, br);
            TransferBaseField_Read.Read(ref data.m_spawnLookAtTarget, br);
            TransferBaseField_Read.Read(ref data.m_bulletLookAtTargetInFly, br);
            TransferBaseField_Read.Read(ref data.m_bulletFalldownInFly, br);
            TransferBaseField_Read.Read(ref data.m_bulletDelaySendTime, br);
            TransferBaseField_Read.Read(ref data.m_hasParabolaCurve, br);
            ReadUniqueInfo_Normal.GfxFramework_GfxParabolaCurveParam_RefRead(ref data.m_parabolaCurve, br);
            TransferBaseField_Read.Read(ref data.m_speed, br);
            TransferBaseField_Read.Read(ref data.m_acceleratedVelocity, br);
            ReadUniqueInfo_Normal.ChessAttackItemConfig_HitBulletAction_Read(ref data.m_hitBulletAction, br);
            TransferBaseField_Read.Read(ref data.m_hitActionDelay, br);
            ReadUniqueInfo_Normal.UnityEngine_GameObject_RefRead(ref data.m_spawnEffect, br);
            ReadUniqueInfo_Normal.UnityEngine_GameObject_RefRead(ref data.m_mirrorSpawnEffect, br);
            ReadUniqueInfo_Normal.ChessAttackItemConfig_EffectSpawnPos_Read(ref data.m_spawnPos, br);
            TransferBaseField_Read.Read(ref data.m_trajectoryPercent, br);
            ReadUniqueInfo_Normal.CharacterHangPoint_SupportHangPointType_Read(ref data.m_spwanEffectLoc, br);
            TransferBaseField_Read.Read(ref data.m_spawnScaleWithLoc, br);
            TransferBaseField_Read.Read(ref data.m_spawnUpdateRotation, br);
            ReadUniqueInfo_Normal.ChessAttackItemConfig_HitEffectDir_Read(ref data.m_spawnDir, br);
            ReadUniqueInfo_Normal.UnityEngine_GameObject_RefRead(ref data.m_bullet, br);
            ReadUniqueInfo_Normal.UnityEngine_GameObject_RefRead(ref data.m_mirrorBullet, br);
            TransferBaseField_Read.Read(ref data.m_bulletScaleWithLoc, br);
            TransferBaseField_Read.Read(ref data.m_bulletSpawnOffset, br);
            TransferBaseField_Read.Read(ref data.m_bulletSpawnOffsetVertical, br);
            TransferBaseField_Read.Read(ref data.m_bulletSpawnOffsetHorizontal, br);
            TransferBaseField_Read.Read(ref data.m_bulletTargetOffset, br);
            TransferBaseField_Read.Read(ref data.m_bulletTargetOffsetVertical, br);
            TransferBaseField_Read.Read(ref data.m_bulletTargetOffsetHorizontal, br);
            TransferBaseField_Read.Read(ref data.m_hitDelay, br);
            ReadUniqueInfo_Normal.System_Collections_Generic_List_1_Begin_ChessAttackItemBulletTimeEvent_End_RefRead(ref data.m_timeEvents, br);
            ReadUniqueInfo_Normal.ChessAttackMovieConfig_RefRead(ref data.m_movieConfig, br);
            ReadUniqueInfo_Normal.System_String_RefRead(ref data.m_runAction, br);
            TransferBaseField_Read.Read(ref data.m_runSpeed, br);
            ReadUniqueInfo_Normal.System_String_RefRead(ref data.m_hitAction, br);
            ReadUniqueInfo_Normal.UnityEngine_GameObject_RefRead(ref data.m_runFinishedEffect, br);
            ReadUniqueInfo_Normal.CharacterHangPoint_SupportHangPointType_Read(ref data.m_runFinishedEffectSpwanLoc, br);
            ReadUniqueInfo_Normal.ChessAttackItemConfig_EffectSpawnPos_Read(ref data.m_runFinishedEffectSpawnPos, br);
            ReadUniqueInfo_Normal.ChessAttackItemConfig_HitEffectDir_Read(ref data.m_runFinishedEffectHitDir, br);
            ReadUniqueInfo_Normal.System_String_RefRead(ref data.attackStartSoundBnk, br);
            ReadUniqueInfo_Normal.System_String_RefRead(ref data.attackStartSoundName, br);
            TransferBaseField_Read.Read(ref data.attackStartSoundTime, br);
            TransferBaseField_Read.Read(ref data.autoStopAttackStartSound, br);
            ReadUniqueInfo_Normal.ChessAttackItemConfig_EffectSpawnPos_Read(ref data.attackStartSoundBindTarget, br);
            ReadUniqueInfo_Normal.CharacterHangPoint_SupportHangPointType_Read(ref data.m_targetLoc, br);
            TransferBaseField_Read.Read(ref data.m_expandHitEffect, br);
            ReadUniqueInfo_Normal.UnityEngine_GameObject_RefRead(ref data.m_hitEffect, br);
            TransferBaseField_Read.Read(ref data.m_hitScaleWithLoc, br);
            ReadUniqueInfo_Normal.ChessAttackItemConfig_HitEffectDir_Read(ref data.m_hitEffectDir, br);
            ReadUniqueInfo_Normal.CharacterHangPoint_SupportHangPointType_Read(ref data.m_hitLoc, br);
            TransferStructField_Read.Read(ref data.m_hitOffset, br);
            TransferBaseField_Read.Read(ref data.m_hideAttackerWhenHit, br);
            TransferBaseField_Read.Read(ref data.m_hitHurtDelay, br);
            ReadUniqueInfo_Normal.System_String_RefRead(ref data.m_hitShakeCamera, br);
            TransferBaseField_Read.Read(ref data.m_hitVibration, br);
            TransferBaseField_Read.Read(ref data.m_lockDefenderMoveTime, br);
            ReadUniqueInfo_Normal.System_String_RefRead(ref data.m_hitAttackerAction, br);
        }

        // ChessAttackItemConfig Ref Func
        public static void ChessAttackItemConfig_RefRead(ref ChessAttackItemConfig data, FastBinaryReader br)
        {
            int index = 0;
            TransferBaseField_Read.Read(ref index, br);
            if(index < 0)
            {
                data = null;
                return;
            }
        
            data = ReadUniqueInfo_NormalPool.instance.Get_ChessAttackItemConfig(index, br);
        }

        // System.Collections.Generic.List<ChessAttackItemConfig> Class Func
        public static void System_Collections_Generic_List_1_Begin_ChessAttackItemConfig_End_Read(System.Collections.Generic.ICollection<ChessAttackItemConfig> dataCollection, FastBinaryReader br)
        {
            int len = 0;
            TransferBaseField_Read.Read(ref len, br);
            for (int i = 0; i < len; i++)
            {
                ChessAttackItemConfig data = default(ChessAttackItemConfig);
                ReadUniqueInfo_Normal.ChessAttackItemConfig_RefRead(ref data, br);
                dataCollection.Add(data);
            }
        }

        // System.Collections.Generic.List<ChessAttackItemConfig> Ref Func
        public static void System_Collections_Generic_List_1_Begin_ChessAttackItemConfig_End_RefRead(ref System.Collections.Generic.List<ChessAttackItemConfig> data, FastBinaryReader br)
        {
            int index = 0;
            TransferBaseField_Read.Read(ref index, br);
            if(index < 0)
            {
                data = null;
                return;
            }
        
            data = ReadUniqueInfo_NormalPool.instance.Get_System_Collections_Generic_List_1_Begin_ChessAttackItemConfig_End(index, br);
        }

        // Cinemachine.CinemachineBlendDefinition.Style
        public static void Cinemachine_CinemachineBlendDefinition_Style_Read(ref Cinemachine.CinemachineBlendDefinition.Style data, FastBinaryReader br)
        {     
            int tmp = 0;
            TransferBaseField_Read.Read(ref tmp, br);
            data = (Cinemachine.CinemachineBlendDefinition.Style)tmp;
        }

        // ChessAttackTimelineConfig Class Func
        public static void ChessAttackTimelineConfig_Read(ChessAttackTimelineConfig data, FastBinaryReader br)
        {     
            // Field
            TransferBaseField_Read.Read(ref data.m_attackDelay, br);
            ReadUniqueInfo_Normal.UnityEngine_GameObject_RefRead(ref data.m_attackTimelineGo, br);
            TransferBaseField_Read.Read(ref data.m_attackResetAttackerScale, br);
            ReadUniqueInfo_Normal.UnityEngine_GameObject_RefRead(ref data.m_hitTimelineGo, br);
            ReadUniqueInfo_Normal.Cinemachine_CinemachineBlendDefinition_Style_Read(ref data.m_cameraBlendStype, br);
            TransferBaseField_Read.Read(ref data.m_cameraBlendTime, br);
        }

        // ChessAttackTimelineConfig Ref Func
        public static void ChessAttackTimelineConfig_RefRead(ref ChessAttackTimelineConfig data, FastBinaryReader br)
        {
            int index = 0;
            TransferBaseField_Read.Read(ref index, br);
            if(index < 0)
            {
                data = null;
                return;
            }
        
            data = ReadUniqueInfo_NormalPool.instance.Get_ChessAttackTimelineConfig(index, br);
        }

        // ChessAttackConfig Class Func
        public static void ChessAttackConfig_Read(ChessAttackConfig data, FastBinaryReader br)
        {     
            // Field
            ReadUniqueInfo_Normal.System_String_RefRead(ref data.m_attackAction, br);
            TransferBaseField_Read.Read(ref data.m_mainAttackItemIndex, br);
            TransferBaseField_Read.Read(ref data.m_defenderPosEnable, br);
            TransferStructField_Read.Read(ref data.m_defenderPos, br);
            ReadUniqueInfo_Normal.System_Collections_Generic_List_1_Begin_ChessAttackItemConfig_End_RefRead(ref data.m_effects, br);
            ReadUniqueInfo_Normal.ChessAttackTimelineConfig_RefRead(ref data.m_timelineConfig, br);
        }

        // ChessAttackConfig Ref Func
        public static void ChessAttackConfig_RefRead(ref ChessAttackConfig data, FastBinaryReader br)
        {
            int index = 0;
            TransferBaseField_Read.Read(ref index, br);
            if(index < 0)
            {
                data = null;
                return;
            }
        
            data = ReadUniqueInfo_NormalPool.instance.Get_ChessAttackConfig(index, br);
        }

    }
}
