using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GfxFramework;
using UnityEngine;


public enum ChainNode_Type_Enum
{
    //顺序
    [EnumName("顺序")]
    Sequence,

    [EnumName("父子")]
    FatherAndSons,
}

public enum ChainNode_Child_Type_Enum
{
    [EnumName("父")]
    Parent,

    [EnumName("子")]
    Child,
}

public enum ChainNode_LookAtType
{
    None,
    ChildLookAtParent,
}

//[ExecuteInEditMode]
public class ChainNode : MonoBehaviour
{
    private static Vector3 vec3Zero = Vector3.zero;
    //支持的判断类型;
    public enum JudgeType
    {
        CasterID,
        KeyName,
    }
    public List<ChainNode> parentNodeList = new List<ChainNode>();

    public ChainNode_Type_Enum type;

    public ChainNode_Child_Type_Enum childType;

    public string KeyName;

    public ulong CasterID = 0;

    public int childIndex;

    [Header("是否可以多连,如电刀连双手")]
    public bool isCanMultiContact = false;

    [Header("判断的类型")]
    public JudgeType judgeType = JudgeType.CasterID;

    [HideInInspector]
    public ChainNode_LookAtType lookAtType = ChainNode_LookAtType.None;
    [HideInInspector]
    public Vector3 lookOffsetEuler = Vector3.zero;

    //public ChainNodeLineRender lineRender;
    public List<ChainNodeLineRender> lineRenders;
    //是否有重新设置了开始时间;
    [HideInInspector] public bool HasResetStartTime = false;
    [HideInInspector] public GfxRoot gfxRoot;
    [HideInInspector] public bool isRendering = false; //表明这个chainNode是否在渲染中，为了用起来方便，不要再次做逻辑;
    private Vector3 initLocalEuler;


    [Header("是否忽视 hidebody，详细请询问程序")]
    public bool IsIngnoreHideBody = false;
    
    // public static int Node_Index = 0;
    // public int self_Index = -1;
    // public void Awake()
    // {
    //     if (self_Index == -1)
    //     {
    //         self_Index = ++Node_Index;
    //     }
    // }

    private void Start()
    {
        this.gfxRoot = this.gameObject.GetComponent<GfxRoot>();
        this.initLocalEuler = this.transform.localEulerAngles;
    }

    private List<ChainNode> childNodeForParentList = new List<ChainNode>(4);
    public void ChainNodeSetInfo(ulong casterObjectId)
    {
        //Debug.LogError("set self " + self_Index + " curentid " + CasterID  +" casetid " + casterObjectId);
        CasterID = casterObjectId;
        this.HasResetStartTime = false;
        CheckAddNode(this);
    }

    private static void CheckAddNode(ChainNode node)
    {
        ChainNodeFinder.AddNode(node);
        for (int i = 0; i < node.lineRenders.Count; i++)
        {
            if (node.lineRenders[i] != null)
            {
                node.lineRenders[i].selfChainNode = node;
            }
        }
        //if (this.name.Contains("camil")) { 
        //    Diagnostic.Log(string.Format("camille log pzf1 : keyname={0}, childType={1}, juudgeType={2}, casterId={3}, name={4}", this.KeyName, this.childType, this.judgeType, this.CasterID, this.name ));
        //}
        //Find parent node and connect 
        if (node.childType == ChainNode_Child_Type_Enum.Child)
        {
            CheckBindChildToParent(node);
        }
        else
        {
            node.childNodeForParentList.Clear();
            List<ChainNode> childNodeList = ChainNodeFinder.FindChildNode(node);
            if (childNodeList != null && childNodeList.Count > 0)
            {
                node.childNodeForParentList.AddRange(childNodeList);
                for (int i = 0, len = node.childNodeForParentList.Count; i < len; i++)
                {
                    CheckBindChildToParent(node.childNodeForParentList[i]);
                }
            }
        }
    }

    private static void CheckBindChildToParent(ChainNode childNode)
    {
        List<ChainNode> tmpList = ChainNodeFinder.FindParentNode(childNode);
        List<ChainNode> parentNodeList = childNode.parentNodeList;
        List<ChainNodeLineRender> lineRenders = childNode.lineRenders;
        if (tmpList != null && parentNodeList != null)
        {
            parentNodeList.Clear();
            for (int i = 0, len = tmpList.Count; i < len; i++)
            {
                parentNodeList.Add(tmpList[i]);
            }
            if (parentNodeList.Count > 0)
            {
                if (childNode.childType == ChainNode_Child_Type_Enum.Child && lineRenders != null)
                {
                    for (int i = 0; i < lineRenders.Count; i++)
                    {
                        if (lineRenders[i] != null)
                        {
                            //设置开始渲染的时间;
                            lineRenders[i].startupTimestampS = Time.realtimeSinceStartup;
                            if (!lineRenders[i].Render(parentNodeList))
                            {
                                lineRenders[i].DiableRender();
                            }
                        }
                    }
                }
            }
        }
    }

    private void OnEnable()
    {
        CheckAddNode(this);
    }

    private void OnDisable()
    {
        ChainNodeFinder.RemoveNode(this);
        this.parentNodeList.Clear();
        childNodeForParentList.Clear();
        //Debug.LogError("OnDisable self " + self_Index + " curentid " + CasterID  +" casetid " + 0);
        CasterID = 0;
        this.transform.localEulerAngles = this.initLocalEuler;
    }

    public void LateUpdate()
    {
        this.isRendering = true;
        if (childType == ChainNode_Child_Type_Enum.Child)
        {
            if (lineRenders != null)
            {
                switch (type)
                {
                    case ChainNode_Type_Enum.FatherAndSons:
                    {
                        this.isRendering = false;
                    }
                        break;
                    case ChainNode_Type_Enum.Sequence:
                    {
                        if (this.childIndex != 0)
                        {
                            this.isRendering = false;
                        }
                    }
                        break;
                }

                bool isValid = false;

                if (parentNodeList != null && parentNodeList.Count > 0)
                {
                    for (int i = parentNodeList.Count - 1; i >= 0; i--)
                    {
                        if (parentNodeList[i] == null)
                        {
                            parentNodeList.RemoveAt(i);
                            continue;
                        }

                        if (this.judgeType == JudgeType.CasterID && parentNodeList[i].judgeType == JudgeType.CasterID && 
                           ( this.KeyName != parentNodeList[i].KeyName || this.CasterID != parentNodeList[i].CasterID)
                           )
                        {
                            parentNodeList.RemoveAt(i);
                            continue;
                        }
                        
                    }
                }
                
                for (int i = 0; i < lineRenders.Count; i++)
                {
                    if (lineRenders[i] != null)
                    {
                        if (!lineRenders[i].Render(parentNodeList))
                        {
                            lineRenders[i].DiableRender();
                        }
                        else
                        {
                            isValid = true;
                        }
                    }
                }

                if (lineRenders.Count == 0)
                {
                    isValid = true;
                }
                
                if (parentNodeList.Count > 0)
                {
                    ChainNode parentChainNode = parentNodeList[0];
                    if (parentChainNode != null &&
                        parentChainNode.transform != null &&
                        isValid && 
                        type == ChainNode_Type_Enum.FatherAndSons && 
                        parentChainNode.lookAtType == ChainNode_LookAtType.ChildLookAtParent)
                    {
                        this.transform.LookAt(parentChainNode.transform);
                        if (!parentChainNode.lookOffsetEuler.Equals(vec3Zero))
                        {
                            if (this != null && this.transform != null)
                            {
                                Vector3 preLocalEuler = this.transform.localEulerAngles;
                                preLocalEuler += parentChainNode.lookOffsetEuler;
                                this.transform.localEulerAngles = preLocalEuler;
                            }
                        }
                    }
                }
            }
        }
    }
}

public class ChainNodeFinder
{
    public static List<ChainNode> AllNodes;
    public static Dictionary<string, List<ChainNode>> AllSeqNodes;
    private static List<ChainNode> cacheNodeList = new List<ChainNode>(2);

    private static void Create()
    {
        if (AllNodes == null) AllNodes = new List<ChainNode>();
        if (AllSeqNodes == null) AllSeqNodes = new Dictionary<string, List<ChainNode>>();
    }

    public static void Release()
    {
        if (AllNodes != null)
        {
            AllNodes.Clear();
            AllSeqNodes.Clear();
            cacheNodeList.Clear();
        }
    }

    public static ChainNode FindEffectChainAndSetInfo(GameObject effect, ulong casterObjectId)
    {
        if (effect == null)
            return null;
       
        ChainNode node = FindEffectChain(effect);
        
        ChainSetInfo(node,casterObjectId);

        return node;
    }
    public static ChainNode FindEffectChain(GameObject effect)
    {
        if (effect == null)
            return null;

        ChainNode node = effect.GetComponent<ChainNode>();

        return node;
    }
    public static void ChainSetInfo(ChainNode node,ulong casterObjectId)
    {
        if (node != null)
        {
            node.ChainNodeSetInfo(casterObjectId);
        }
    }

    public static void AddNode(ChainNode node)
    {
        Create();
        if (node != null)
        {
            if (node.lineRenders != null)
            {
                node.lineRenders.RemoveAll(delegate(ChainNodeLineRender cnlr)
                {
                    return cnlr == null;
                });
            }
            if (node.type == ChainNode_Type_Enum.FatherAndSons)
            {
                if (!AllNodes.Contains(node))
                {
                    AllNodes.Add(node);
                    if (node.childType == ChainNode_Child_Type_Enum.Child)
                    {
                        for (int i = 0; i < node.lineRenders.Count; i++)
                        {
                            if (node.lineRenders[i] != null)
                            {
                                node.lineRenders[i].DiableRender();
                            }
                        }   
                    }
                }
            }
            else
            {
                string KeyName = node.KeyName + node.CasterID;
                if (!AllSeqNodes.ContainsKey(KeyName))
                {
                    AllSeqNodes.Add(KeyName, new List<ChainNode>());
                }
                if (!AllSeqNodes[KeyName].Contains(node))
                {
                    AllSeqNodes[KeyName].Add(node);
                    node.childIndex = AllSeqNodes[KeyName].Count - 1;
                }
            }
        }
    }
    public static void RemoveNode(ChainNode node)
    {
        Create();
        if (node != null)
        {
            if (node.type == ChainNode_Type_Enum.FatherAndSons)
            {
                if (AllNodes.Contains(node))
                    AllNodes.Remove(node);
            }
            else
            {
                string KeyName = node.KeyName + node.CasterID;
                if (!AllSeqNodes.ContainsKey(KeyName))
                {
                    AllSeqNodes.Add(KeyName, new List<ChainNode>());
                }
                if (AllSeqNodes[KeyName].Contains(node))
                    AllSeqNodes[KeyName].Remove(node);
            }
        }
    }
    
    public static List<ChainNode> FindChildNode(ChainNode parentNode)
    {
        Create();
        if (parentNode == null)
            return null;
        bool isValid = false;
        cacheNodeList.Clear();
        if (parentNode.type == ChainNode_Type_Enum.FatherAndSons)
        {
            for (int i = 0; i < AllNodes.Count; i++)
            {
                if (AllNodes[i] != null &&
                    AllNodes[i].KeyName == parentNode.KeyName &&
                    AllNodes[i].childType == ChainNode_Child_Type_Enum.Child &&
                    AllNodes[i].judgeType == parentNode.judgeType)
                {
                    isValid = false;
                    switch (parentNode.judgeType)
                    {
                        case ChainNode.JudgeType.CasterID:
                            isValid = AllNodes[i].CasterID == parentNode.CasterID;
                            break;
                        case ChainNode.JudgeType.KeyName:
                            isValid = AllNodes[i].KeyName == parentNode.KeyName;
                            break;
                    }
                    if (isValid)
                    {
                        cacheNodeList.Add(AllNodes[i]);
                    }
                }
            }
        }
        return cacheNodeList;
    }

    public static List<ChainNode> FindParentNode(ChainNode node)
    {
        Create();
        if (node == null)
            return null;
        bool isValid = false;
        cacheNodeList.Clear();
        if (node.type == ChainNode_Type_Enum.FatherAndSons)
        {
            for (int i = 0; i < AllNodes.Count; i++)
            {
                if (AllNodes[i] != null &&
                    AllNodes[i].KeyName == node.KeyName &&
                    AllNodes[i].childType == ChainNode_Child_Type_Enum.Parent &&
                    AllNodes[i].judgeType == node.judgeType)
                {
                    isValid = false;
                    switch (node.judgeType)
                    {
                        case ChainNode.JudgeType.CasterID:
                            isValid = AllNodes[i].CasterID == node.CasterID && node.CasterID > 0;
                            break;
                        case ChainNode.JudgeType.KeyName:
                            isValid = AllNodes[i].KeyName == node.KeyName;
                            break;
                    }
                    if (isValid)
                    {
                        cacheNodeList.Add(AllNodes[i]);
                        return cacheNodeList;
                    }
                }
            }
        }
        else
        {
            string KeyName = node.KeyName + node.CasterID;
            if (AllSeqNodes.ContainsKey(KeyName))
            {
                List<ChainNode> tmpSeqNodes = AllSeqNodes[KeyName];
                ChainNode tmpNode;
                for (int i = 0; i < tmpSeqNodes.Count; i++)
                {
                    tmpNode = tmpSeqNodes[i];
                    if (tmpNode != null
                        && tmpNode.childIndex == node.childIndex - 1)
                    {
                        cacheNodeList.Add(tmpNode);
                        //如果是可以多连的，如电刀开始连双，那么继续往回找，看下还有没有父级;
                        if (tmpNode.isCanMultiContact)
                        {
                            for (int k = i - 1; k >= 0; k--)
                            {
                                tmpNode = tmpSeqNodes[k];
                                if (tmpNode.isCanMultiContact)
                                {
                                    cacheNodeList.Add(tmpNode);
                                }
                                else
                                {
                                    break;
                                }
                            }
                        }
                        break;
                    }
                }
                return cacheNodeList;
            }

        }

        return null;

    }

}

