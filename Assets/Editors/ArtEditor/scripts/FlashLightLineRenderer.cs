using Lucifer.ActCore;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class FlashLightLineRenderer : ChainNodeLineRender
{
    public const string RendererGoName = "LineRender";
    [Serializable]
    public class ColorGradual {
        public Color start;
        public Color end;

        public Gradient gradient;
    }

    public enum FadeState {
        NO, //无 
        FADEING, //缓动中
        COMPLETE, //完成
    }

    [Serializable]
    public class FlashLight
    {
        public LineRenderer lineRenderer;
        public FixedDataList_Struct<Vector3> positionList = new FixedDataList_Struct<Vector3>(10);
        public Transform targetTrans { get; private set; }
        public Vector2[] materialOffsetArr;

        public Material lineRendererMaterial;
        [HideInInspector]
        public FadeState fadeInState = FadeState.NO;

        public FlashLightLineRenderer flashLightLineRenderer;
        public int offsetIndex;
        private int colorIndex;
        private Vector3 startTransPos;
        private Vector3 endTransPos;
        private float calcTime = 0.0f;
        private float calcSwitchTime = 0.0f;
        private Color tmpColor1;
        private Color tmpColor2;
        private float lerpTime = 0.0f;
        private Vector2 finalFadeInAlhpa = Vector2.zero;
        public float remainNoRenderTime;

        public void Clone(FlashLight rawFlashLight) {
            //复制LineRenderer;
            Transform rawLineRendererTrans = rawFlashLight.lineRenderer.transform;
            LineRenderer newLineRenderer = GameObject.Instantiate<GameObject>(rawLineRendererTrans.gameObject).GetComponent<LineRenderer>();
            newLineRenderer.transform.SetParent(rawLineRendererTrans.parent);
            newLineRenderer.transform.localPosition = rawLineRendererTrans.localPosition;
            newLineRenderer.transform.localEulerAngles = rawLineRendererTrans.localEulerAngles;
            newLineRenderer.transform.localScale = rawLineRendererTrans.localScale;
            this.Init(newLineRenderer);
            //复制属性;
            this.materialOffsetArr = new Vector2[rawFlashLight.materialOffsetArr.Length];
            for (int i = 0, len = this.materialOffsetArr.Length; i < len; i++) {
                this.materialOffsetArr[i] = this.materialOffsetArr[i].Clone();
            }
        }

        public void SetFlashLightRenderer(FlashLightLineRenderer flashLightLineRenderer) {
            this.flashLightLineRenderer = flashLightLineRenderer;
        }

        public void Init(LineRenderer renderer) {
            this.lineRenderer = renderer;
            if (Application.isEditor)
            {
                this.lineRendererMaterial = this.lineRenderer.sharedMaterial;
            }
            else {
                this.lineRendererMaterial = this.lineRenderer.material;
            }
        }

        public void SetTarget(Transform target) {
            this.targetTrans = target;
        }

        public void Update() {
            this.lineRenderer.positionCount = positionList.Count;
            for (int i = 0, len = positionList.Count; i < len; i++)
            {
                var pos = positionList[i];
                this.lineRenderer.SetPosition(i, pos.data);
            }
        }

        public void Diable() {
            if (this.lineRenderer != null && this.lineRenderer.positionCount != 0) {
                this.lineRenderer.positionCount = 0;
                positionList.Clear();
            }
        }

        public void Render() {
            if (this.calcTime > this.flashLightLineRenderer.intervalTime)
            {
                this.CalcFlashLightPosition();
                this.calcTime = 0;
                this.flashLightLineRenderer.intervalTime = UnityEngine.Random.Range(this.flashLightLineRenderer.randomInterval.x, this.flashLightLineRenderer.randomInterval.y);

                if (!this.flashLightLineRenderer.isUseRandomPositionAndColor && offsetIndex >= this.materialOffsetArr.Length)
                {
                    offsetIndex = 0;
                }
                if (this.lineRendererMaterial != null) {
                    if (this.flashLightLineRenderer.isNeedCustomTiling)
                    {
                        this.lineRendererMaterial.mainTextureScale = this.flashLightLineRenderer.customTiling;   
                    }
                    else
                    {
                        this.lineRendererMaterial.mainTextureScale = this.flashLightLineRenderer.textureScale;
                    }
                    this.lineRendererMaterial.SetTextureOffset("_MainTex", materialOffsetArr[offsetIndex]);
                }
                if (!this.flashLightLineRenderer.isUseRandomPositionAndColor)
                {
                    offsetIndex++;
                }
                else
                {
                    offsetIndex = UnityEngine.Random.Range(0, this.materialOffsetArr.Length);
                }
                if (offsetIndex >= this.materialOffsetArr.Length)
                {
                    offsetIndex = 0;
                }

                if (this.flashLightLineRenderer.ColorGradualList.Count > 0)
                {
                    if (!this.flashLightLineRenderer.isUseRandomPositionAndColor && colorIndex >= this.flashLightLineRenderer.ColorGradualList.Count)
                    {
                        colorIndex = 0;
                    }
                    if(this.flashLightLineRenderer.isNeedColorFadeIn){
                        this.lineRenderer.startColor = this.flashLightLineRenderer.ColorGradualList[colorIndex].start;
                        this.lineRenderer.endColor = this.flashLightLineRenderer.ColorGradualList[colorIndex].end;
                    }else{
                        this.lineRenderer.colorGradient = this.flashLightLineRenderer.ColorGradualList[colorIndex].gradient;
                    }
                    if (!this.flashLightLineRenderer.isUseRandomPositionAndColor)
                    {
                        colorIndex++;
                    }
                    else
                    {
                        colorIndex = UnityEngine.Random.Range(0, this.flashLightLineRenderer.ColorGradualList.Count);
                    }
                    if (colorIndex >= this.flashLightLineRenderer.ColorGradualList.Count)
                    {
                        colorIndex = 0;
                    }
                }
            }
            if (this.calcSwitchTime > this.flashLightLineRenderer.segmentIntervalTime)
            {
                this.flashLightLineRenderer.runCount++;
                this.remainNoRenderTime = UnityEngine.Random.Range(this.flashLightLineRenderer.segmentNoRenderRangeTimeSeconds.x, this.flashLightLineRenderer.segmentNoRenderRangeTimeSeconds.y);
                this.calcSwitchTime = 0;
            }
            this.calcSwitchTime += Time.deltaTime;
            this.calcTime += Time.deltaTime;
            if(this.flashLightLineRenderer.isNeedColorFadeIn){
                this.CheckFadeIn();
            }
        }


        private void CheckFadeIn()
        {
            if (this.fadeInState == FadeState.NO)
            {
                this.fadeInState = FadeState.FADEING;
                tmpColor1 = this.lineRenderer.startColor;
                tmpColor2 = this.lineRenderer.endColor;
                finalFadeInAlhpa.x = tmpColor1.a;
                finalFadeInAlhpa.y = tmpColor2.a;
                lerpTime = 0.0f;
            }
            if (this.fadeInState == FadeState.FADEING)
            {
                lerpTime += Time.deltaTime;
                Vector2 calcVec2 = Vector2.Lerp(Vector2.zero, finalFadeInAlhpa, lerpTime / this.flashLightLineRenderer.fadeInTime);
                tmpColor1.a = calcVec2.x;
                tmpColor2.a = calcVec2.y;
                this.lineRenderer.startColor = tmpColor1;
                this.lineRenderer.endColor = tmpColor2;
                if (calcVec2.Equals(finalFadeInAlhpa))
                {
                    this.fadeInState = FadeState.COMPLETE;
                }
            }
        }


        /// <summary>
        /// 计算闪电的折线点;
        /// </summary>
        private void CalcFlashLightPosition()
        {
            positionList.Clear();
            startTransPos = this.lineRenderer.transform.position;
            if (targetTrans != null)
            {
                endTransPos = this.targetTrans.position;
                // if (targetTrans.name.Contains("hand")) {
                //     int a = targetTrans.GetInstanceID();
                //     int b = this.lineRenderer.GetInstanceID();
                // }
            }
            else
            {
                endTransPos = this.lineRenderer.transform.position + this.lineRenderer.transform.position.normalized * 20;
            }
            //开始点;
            var tmpRefPos = positionList.GetUnused();
            tmpRefPos.data.Set(startTransPos.x, startTransPos.y, startTransPos.z);
            this.RecursiveCalcFlashLightPosition(startTransPos.x, startTransPos.y, startTransPos.z, endTransPos.x, endTransPos.y, endTransPos.z, this.flashLightLineRenderer.segmentNum);
            //结束点;
            tmpRefPos = positionList.GetUnused();
            tmpRefPos.data.Set(endTransPos.x, endTransPos.y, endTransPos.z);

        }

        private void RecursiveCalcFlashLightPosition(float startX, float startY, float startZ, float endX, float endY, float endZ, float displace)
        {
            if (displace < this.flashLightLineRenderer.minSegment)
            {
                var tmpRefPos = positionList.GetUnused();
                tmpRefPos.data.Set(startX, startY, startZ);
            }
            else
            {
                float centerX = (startX + endX) / 2;
                float centerY = (startY + endY) / 2;
                float centerZ = (startZ + endZ) / 2;
                //添加一个偏移;
                centerX += (UnityEngine.Random.Range(0.0f, 1.0f) - this.flashLightLineRenderer.offsetXYZRandomRate.x) * displace;
                centerY += (UnityEngine.Random.Range(0.0f, 1.0f) - this.flashLightLineRenderer.offsetXYZRandomRate.y) * displace;
                centerZ += (UnityEngine.Random.Range(0.0f, 1.0f) - this.flashLightLineRenderer.offsetXYZRandomRate.z) * displace;
                RecursiveCalcFlashLightPosition(startX, startY, startZ, centerX, centerY, centerZ, displace / 2);
                RecursiveCalcFlashLightPosition(centerX, centerY, centerZ, endX, endY, endZ, displace / 2);
            }
        }

        public bool IsValid {
            get {
                return this.lineRenderer != null;
            }
        }
    }
    [Header("目标")]
    public Transform targetTrans;
    [Range(0.0f, 100.0f)]
    [Header("偏折幅度")]
    public float segmentNum = 10;
    [Range(0.0f, 100.0f)]
    [Header("最小偏折幅度")]
    public float minSegment = 2;
    [Header("XYZ偏折随机率")]
    public Vector3 offsetXYZRandomRate = new Vector3(0.5f, 0.5f, 0.5f);
    [Header("偏折间隔")]
    public Vector2 randomInterval = new Vector2(0.05f, 0.15f);
    [HideInInspector]
    public Vector2Int tileSize = Vector2Int.one;
    [Header("持续时间")]
    [HideInInspector]
    public float duration = -1;

    private Vector2 textureScale = Vector2.zero;
    [HideInInspector]
    public bool isNeedCustomTiling = false;
    public Vector2 customTiling = new Vector2(0.5f, 0.5f);

    [Header("是否随机位置和颜色")]
    public bool isUseRandomPositionAndColor = true;
    [HideInInspector]
    public bool isNeedColorFadeIn = true;
    [HideInInspector]
    public List<ColorGradual> ColorGradualList;
    public bool isCanRender = false;
    [HideInInspector]
    public float fadeInTime;

    [Header("是否循环")]
    public bool isLoop = true;
    [Header("一个循环的时间(秒),要在运行前设置")]
    public float onceLoopTimeSeconds = 1.0f;
    [Header("每个片段间的留白最小最大时间(秒)")]
    public Vector2 segmentNoRenderRangeTimeSeconds = Vector2.zero;

    //运行的次数;
    private int runCount = 0;
    //一轮有多少个;
    private int roundCount = 0;

    //[HideInInspector]
    public FlashLight mainFlashLight;
    //其余的闪电链;
    [HideInInspector]
    public List<FlashLight> otherFlashLightList = new List<FlashLight>();
    //有效的otherLight数量;
    private int validOtherFlashLightCount = 0;
    private float intervalTime;
    private bool isPreviewInEditor = false;
    private float segmentIntervalTime;

    public float randomTime {
        get {
            return UnityEngine.Random.Range(randomInterval.x, randomInterval.y);
        }
    }

    private void Awake()
    {
        if (mainFlashLight == null) {
            mainFlashLight = new FlashLight();
        }
        mainFlashLight.SetFlashLightRenderer(this);
        LineRenderer lineRender = mainFlashLight.lineRenderer;
        if (lineRender == null) {
            GameObject subGo = this.transform.Find(RendererGoName).gameObject;
            if (subGo != null)
            {
                lineRender = subGo.GetComponent<LineRenderer>();
            }
        }
        if(lineRender != null){
            this.Init(lineRender);
        }
        if (this.mainFlashLight.IsValid)
        {
            this.ChangeTileSize();
        }
        //初始化主要的FlashLight，当有多目标时，都基于这个进行复制;
#if ART_EDITOR
        this.isCanRender = true;
#endif
    }


    public void Init(LineRenderer lineRender)
    {
        mainFlashLight.Init(lineRender);
        if (this.mainFlashLight.IsValid)
        {
            this.ChangeTileSize();
        }
    }

    public void ChangeTileSize() {
        //通过纹理格子行列值计算uv数组;
        this.textureScale.x = 1.0f / this.tileSize.x;
        this.textureScale.y = 1.0f / this.tileSize.y;
        this.mainFlashLight.materialOffsetArr = new Vector2[this.tileSize.x * this.tileSize.y];
        Vector2 tmpVec2;
        int tmpIndex = 0;
        //从左上到右下的顺序添加元素;
        for (int row = this.tileSize.y; row > 0; row--)
        {
            tmpVec2 = new Vector2(0.0f, this.textureScale.y * (row - 1));
            for (int col = 0; col < this.tileSize.x; col++)
            {
                tmpVec2.x = col * this.textureScale.x;
                this.mainFlashLight.materialOffsetArr[tmpIndex] = tmpVec2;
                tmpIndex++;
            }
        }
        this.roundCount = this.tileSize.x * this.tileSize.y;
        this.segmentIntervalTime = this.onceLoopTimeSeconds / this.roundCount;
        this.runCount = 0;
        this.mainFlashLight.offsetIndex = 0;
    }

    void Update()
    {
        if (!this.isCanRender) {
            return;
        }
        this.mainFlashLight.Update();
        for (int i = 0; i < this.validOtherFlashLightCount; i++) {
            this.otherFlashLightList[i].Update();
        }
    }

    public void SetIsPreviewInEdtitor(bool value) {
        this.isPreviewInEditor = value;
    }

#if ART_EDITOR
    private void LateUpdate()
    {
        if (this.isPreviewInEditor) {
            this.mainFlashLight.SetTarget(this.targetTrans);
            this.TryRender();
        }
    }
#endif

    private void OnDisable()
    {
        this.mainFlashLight.Diable();
        for (int i = 0, len = otherFlashLightList.Count; i < len; i++)
        {
            this.otherFlashLightList[i].Diable();
        }
    }


    public void PreviewFadeInEditor() {
        this.mainFlashLight.fadeInState = FadeState.NO;
        for (int i = 0; i < this.validOtherFlashLightCount; i++)
        {
            this.otherFlashLightList[i].fadeInState = FadeState.NO;
        }
        this.isCanRender = true;
        this.runCount = 0;
    }

    private void TryRender() {
        if (isCanRender)
        {
            this.selfChainNode.isRendering = true;
            this.ResetStartTime();                
        }
        if (!this.isLoop) {
            if (this.isCanRender) {
                if (this.runCount >= this.roundCount) {
                    this.DiableRender();
                    return;
                }
            }
        }
        if (this.isCanRender) {
            this.RequestRender();
        }
    }

    public override void DiableRender()
    {
        this.mainFlashLight.Diable();
        for (int i = 0, len = this.otherFlashLightList.Count; i < len; i++)
        {
            this.otherFlashLightList[i].Diable();
        }
        this.isCanRender = false;
    }


    private void RequestRender() {
        if(this.mainFlashLight.remainNoRenderTime <= 0.0f){
            this.mainFlashLight.Render();
        }else{
            this.mainFlashLight.Diable();
            this.mainFlashLight.remainNoRenderTime -= Time.deltaTime;
        }
        for (int i = 0; i < this.validOtherFlashLightCount; i++)
        {
            if(this.otherFlashLightList[i].remainNoRenderTime <= 0.0f){
                this.otherFlashLightList[i].Render();
            }else{
                this.otherFlashLightList[i].Diable();
                this.otherFlashLightList[i].remainNoRenderTime -= Time.deltaTime;
            }
        }
    }

    public override bool Render(ChainNode parentNode)
    {
        this.CheckCanRender(parentNode);
        if (!this.isCanRender) {
            this.mainFlashLight.Diable();
            return false;
        }
        this.mainFlashLight.SetTarget(parentNode.transform);
        this.TryRender();
        return true;
    }

     
    public override bool Render(List<ChainNode> parentNodeList)
    {
        if (parentNodeList == null || parentNodeList.Count < 1 || parentNodeList[0] == null) return false;
        this.CheckCanRender(parentNodeList[0]);
        if (!this.isCanRender) {
            this.mainFlashLight.Diable();
            for (int i = 0, len = otherFlashLightList.Count; i < len; i++)
            {
                this.otherFlashLightList[i].Diable();
            }
            return false;
        }
        this.mainFlashLight.SetTarget(parentNodeList[0].transform);
        //多余的要隐藏掉;
        this.validOtherFlashLightCount = 0;
        if (parentNodeList.Count > 1)
        {
            this.validOtherFlashLightCount = parentNodeList.Count - 1; //1为去除mainFlashLight;
            //确保OtherFlashLight有数据;
            for (int i = 1, k = 0, len = parentNodeList.Count; i < len; i++, k++) {
                FlashLight newFlashLight = null;
                if (this.otherFlashLightList.Count > k)
                {
                    newFlashLight = this.otherFlashLightList[k];
                }
                else {
                    newFlashLight = new FlashLight();
                    newFlashLight.SetFlashLightRenderer(this);
                    newFlashLight.Clone(this.mainFlashLight);
                    this.otherFlashLightList.Add(newFlashLight);
                }
                newFlashLight.SetTarget(parentNodeList[i].transform);
            }
        }
        //检测是否有多余的;
        for (int i = this.validOtherFlashLightCount, len = this.otherFlashLightList.Count; i < len; i++)
        {
            this.otherFlashLightList[i].Diable();
        }
        this.TryRender();
        return this.isCanRender;
    }

    private void CheckCanRender(ChainNode parentNode) {
        if (!this.mainFlashLight.IsValid){
            this.isCanRender = false;
            return;
        }
        if (parentNode == null){
            this.isCanRender = false;
            return;
        }
        if (!parentNode.gameObject.activeInHierarchy){
            this.isCanRender = false;
            return;
        }
        if (this.duration >= 0)
        {
            if ((Time.realtimeSinceStartup - this.startupTimestampS) > this.duration)
            {
                this.isCanRender = false;
                return;
            }
        }

        if (parentNode.type == ChainNode_Type_Enum.Sequence)
        {
            if (parentNode.childType == ChainNode_Child_Type_Enum.Child)
            {
                if (!parentNode.isRendering)
                {
                    this.isCanRender = false;
                    return;
                }
            }
        }

        if (!this.isCanRender)
        {
            //记录下开始运行的时间;
            this.isCanRender = true;   
        }
    }

}