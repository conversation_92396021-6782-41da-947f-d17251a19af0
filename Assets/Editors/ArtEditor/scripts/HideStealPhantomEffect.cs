using System.Collections;
using System.Collections.Generic;
using UnityEngine;



public class HideStealPhantomEffect : MonoBehaviour
{

    public MeshRenderer[] meshRenderList;

    public float fadeTime = 1f;

    public float duration = 1f;

    float startTime = 0;

    float pass = 0;

    public float from;

    public float to;


    void Awake()
    {
        initRenderInfo();
    }

    void initRenderInfo()
    {
        if (meshRenderList == null || meshRenderList.Length == 0)
        {
            meshRenderList = GetComponentsInChildren<MeshRenderer>();

        }

    }

    public void StartTween(float from, float to, float duration, float fadeTime)
    {
        startTime = Time.realtimeSinceStartup;
        pass = 0;
        enabled = true;
        this.from = from;
        this.to = to;
        this.duration = duration;
        this.fadeTime = fadeTime;
        initRenderInfo();

    }


    // Update is called once per frame
    void Update()
    {
        if (startTime != 0)
        {

#if UNITY_EDITOR
            pass += Lucifer.ActCore.ActionCoreConfig.frameLength;
#else
            pass = Time.realtimeSinceStartup - startTime;
#endif
            float toValue = tweenEffect();


            for (int i = 0; i < meshRenderList.Length; i++)
            {
                if (meshRenderList[i] != null)
                {
                    for (int j = 0; j < meshRenderList[i].materials.Length; j++)
                    {
                        if (meshRenderList[i].materials[j] != null)
                        {
                            meshRenderList[i].materials[j].SetFloat("_Alpha", toValue);
                        }
                    }
                }
            }



        }
    }

    float tweenEffect()
    {
        float toValue = 0;
        if (pass >= fadeTime + duration)
        {
            startTime = 0;
            pass = 0;
            toValue = to;
            enabled = false;

        }
        else if (pass >= duration)
        {
            if (fadeTime == 0f || pass >= duration + fadeTime)
            {
                toValue = to;
            }
            else
            {
                toValue = from + (to - from) * ((pass - duration) / fadeTime);
            }
        }
        else
        {
            toValue = from;
        }
        return toValue;
    }



    /// <summary>
    /// 隐藏
    /// </summary>
    /// <param name="role"></param>
    /// <param name="toValue"></param>
    /// <param name="duration"></param>
    public static void Hide(GameObject role, float toValue, float duration, float fadeTime)
    {
        HideStealPhantomEffect stealEffectScript = role.GetComponent<HideStealPhantomEffect>();
        stealEffectScript.StartTween(1, toValue, duration, fadeTime);
    }

}
