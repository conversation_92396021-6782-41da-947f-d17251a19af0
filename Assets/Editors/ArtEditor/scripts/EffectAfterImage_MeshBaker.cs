using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// This Class Bakes Skinned Mesh Renderer for shadow. 
/// </summary>
public class EffectAfterImage_MeshBaker : MonoBehaviour 
{
	#region Public Variables

	/// <summary>
	/// These are the meshes we wanna bake :)
	/// </summary>
	public SkinnedMeshRenderer[] meshes;
    public MeshRenderer[] meshes_1;
    /// <summary>
    /// Of course we need a shader to make a material. 
    /// </summary>
    public Shader shader;

	/// <summary>
	/// How often do we bake the mesh. 
	/// In seconds. 
	/// </summary>
	public float timeInterval;

	/// <summary>
	/// The duration of the shadow.
	/// </summary>
	public float timeDestroy;

    public float timeDuration;

    public int maxCount;

	public bool open = false;
    public float fadeTime = 0.5f;
    /// <summary>
    /// Well just in case u have a material ... 
    /// </summary>
    public Material material;

	#endregion

	#region Private Variables

	private bool isBaking;

	private Transform m_transform;

    private float passTime;

   

    private int bakeCount;

	#endregion

	#region MonoBehaviours

	private void Start()
	{
		meshes = GetComponentsInChildren<SkinnedMeshRenderer> ();
        meshes_1 = GetComponentsInChildren<MeshRenderer>();
        isBaking = false;

		m_transform = transform;

        enabled = false;

        passTime = 0;
        bakeCount = 0;

        
	}

    [ContextMenu("start show effect")]
    public void StartEffect()
    {
        enabled = true;
        open = true;
        passTime = 0;
        bakeCount = 0;
        StartCoroutine(Bake());
    }

    public void CloseEffect()
    {
        enabled = false;
        StopAllCoroutines();
    }

	private void Update()
	{
		if (!isBaking || !open)
			return;

        GameObject go = new GameObject("残影root");
        
		
        for (int i = 0, imax = meshes.Length; i < imax; i++)
		{
			Mesh snapshot = new Mesh();
			meshes[i].BakeMesh(snapshot);
			PutMeshIntoHierarchy(snapshot, go,m_transform.position,
               // Quaternion.Euler(new Vector3(0f, -35f, 0)) *
            Quaternion.Euler(new Vector3(-90f, m_transform.rotation.eulerAngles.y, 0)));
		}

        for (int i = 0, imax = meshes_1.Length; i < imax; i++)
        {
            //Mesh snapshot = new Mesh();
            //meshes_1[i].BakeMesh(snapshot);
            PutMeshIntoHierarchy(meshes_1[i].GetComponent<MeshFilter>().mesh, 
                go, meshes_1[i].transform.position,
                meshes_1[i].transform.rotation);
        }
        HideAfterImageEffect.Hide(go, 0f, timeDestroy, fadeTime);
        bakeCount++;
		isBaking = false;
	}

	#endregion

	#region Private Methods

	private IEnumerator Bake()
	{
        
		while (bakeCount < maxCount && passTime < timeDuration) 
		{
			yield return TKFrame.CoroutineWait.GetWaitForSeconds(timeInterval);

            passTime += timeInterval;

            isBaking = true;
		}

        enabled = false;
        StopAllCoroutines();

    }

	private void PutMeshIntoHierarchy(Mesh mesh,GameObject root,Vector3 pos,Quaternion rotate)
	{
		GameObject temp = new GameObject("残影");
		temp.transform.parent = root.transform;
		temp.transform.position = pos;
		temp.transform.rotation = rotate;
	
		temp.AddComponent<MeshFilter>().mesh = mesh;
		temp.AddComponent<MeshRenderer>().material = material;	
        
	}

	#endregion
}


