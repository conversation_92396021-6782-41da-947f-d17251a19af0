//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using NUnit.Framework;
using static Unity.Mathematics.math;
using Burst.Compiler.IL.Tests;

namespace Unity.Mathematics.Tests
{
    [TestFixture]
    public class TestDouble4x3
    {
        [TestCompiler]
        public static void double4x3_zero()
        {
            TestUtils.AreEqual(0.0, double4x3.zero.c0.x);
            TestUtils.AreEqual(0.0, double4x3.zero.c0.y);
            TestUtils.AreEqual(0.0, double4x3.zero.c0.z);
            TestUtils.AreEqual(0.0, double4x3.zero.c0.w);
            TestUtils.AreEqual(0.0, double4x3.zero.c1.x);
            TestUtils.AreEqual(0.0, double4x3.zero.c1.y);
            TestUtils.AreEqual(0.0, double4x3.zero.c1.z);
            TestUtils.AreEqual(0.0, double4x3.zero.c1.w);
            TestUtils.AreEqual(0.0, double4x3.zero.c2.x);
            TestUtils.AreEqual(0.0, double4x3.zero.c2.y);
            TestUtils.AreEqual(0.0, double4x3.zero.c2.z);
            TestUtils.AreEqual(0.0, double4x3.zero.c2.w);
        }

        [TestCompiler]
        public static void double4x3_operator_equal_wide_wide()
        {
            double4x3 a0 = double4x3(492.15758275061728, -495.20632027797694, 227.45765195947968, -147.37405950733182, -222.68201909897942, 64.093720704360749, -23.890404473939157, -16.8197190839889, 163.23210890741655, -165.27101071424363, 470.87767980568003, -423.94255967808078);
            double4x3 b0 = double4x3(192.56880888369346, -235.61102472786376, -254.04311740307281, -412.62472052715009, 471.90480945627428, -6.4727852374654162, -339.10237447316865, 488.1875700839737, -379.5965842584132, -308.41700258311675, -82.333374300195544, -102.92108087563935);
            bool4x3 r0 = bool4x3(false, false, false, false, false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r0, a0 == b0);

            double4x3 a1 = double4x3(109.63436918595539, 462.69031283943468, -335.38147727371262, 357.23446934168896, 1.5455777652308598, -347.38824741327585, -114.47217302884542, 435.84865804940864, 194.23808607563285, 138.76554710174241, -467.34914205379278, 370.43337767684523);
            double4x3 b1 = double4x3(226.51573835430463, -356.90132896830391, -362.91277544708589, -427.89843746083716, 466.65013978753711, -102.79904680270658, -43.355954428834821, 85.045664111639212, -91.127054972167628, 422.19208856215334, -477.43130873024057, 1.8770024785198984);
            bool4x3 r1 = bool4x3(false, false, false, false, false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r1, a1 == b1);

            double4x3 a2 = double4x3(476.70826147343416, 320.55264702465047, -498.59197377534207, 92.4169581366782, 104.51136856177425, 166.75460608618084, -204.73343024250744, 434.75675674656259, -397.32965988541469, 503.98163699730378, -503.7141270598928, 90.659743112819115);
            double4x3 b2 = double4x3(312.5800799394865, 254.59934365684137, 352.72583763335172, 62.490957050812881, 119.71476059766246, -511.05808639482507, -302.47273053902791, -371.76924365189359, -20.007841834802093, 21.459455738523729, -426.02067228128232, -305.41193666374863);
            bool4x3 r2 = bool4x3(false, false, false, false, false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r2, a2 == b2);

            double4x3 a3 = double4x3(-303.4452423078219, 9.3449113412503948, 290.9010785980621, -147.57193882184657, 368.08236067745941, -321.60959044173808, -171.4654224717363, -441.30646368549503, -137.76681834914109, 304.68958463551928, 301.88943948498434, -222.22090564585335);
            double4x3 b3 = double4x3(261.68332517411716, 50.0047347778476, -334.13464824023407, 75.065677916196023, -51.186689639085273, -135.96155721319911, -409.36487431515235, 160.81974013187914, 102.12079553591127, 277.81306637349212, 434.90674444423371, -15.289183385339186);
            bool4x3 r3 = bool4x3(false, false, false, false, false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r3, a3 == b3);
        }

        [TestCompiler]
        public static void double4x3_operator_equal_wide_scalar()
        {
            double4x3 a0 = double4x3(-303.2300766926399, 451.52631327674089, -253.65587413201848, -105.20363502632995, -500.6910920090466, -426.19248338518315, 159.87609656149334, -59.558379439431405, -57.477391031327386, -182.04973968400139, 406.51375861024189, 370.88599866017978);
            double b0 = (123.5445759871717);
            bool4x3 r0 = bool4x3(false, false, false, false, false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r0, a0 == b0);

            double4x3 a1 = double4x3(-172.03530629539642, -11.338988547836891, 363.93823044557973, -27.150561106927, -325.97606507221985, -290.35904254129116, 180.19686635779067, -374.12832015293105, -439.35894295170851, -126.54608899287234, -197.2617896521752, -227.15933357326281);
            double b1 = (455.40001198993991);
            bool4x3 r1 = bool4x3(false, false, false, false, false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r1, a1 == b1);

            double4x3 a2 = double4x3(-479.8991937487848, -495.23734902555, -224.51705013239621, -422.83322616239695, -450.19627043707123, -20.106708774392814, 297.37999906082632, 185.9665759475746, -102.97598962810633, -220.59704910060253, -228.686854707397, -333.00125972041917);
            double b2 = (-439.77767750237962);
            bool4x3 r2 = bool4x3(false, false, false, false, false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r2, a2 == b2);

            double4x3 a3 = double4x3(434.2130317325765, -239.86977707588568, 380.93927281952426, 90.349506658664723, -361.32792751925433, -453.59993836544453, 157.73248799039629, -491.04621457077855, 296.61425055964582, 482.26513432071783, -305.87698259292029, -290.10212601819171);
            double b3 = (406.24874062382094);
            bool4x3 r3 = bool4x3(false, false, false, false, false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r3, a3 == b3);
        }

        [TestCompiler]
        public static void double4x3_operator_equal_scalar_wide()
        {
            double a0 = (-253.39728534100453);
            double4x3 b0 = double4x3(19.952187785856495, -185.79199346610903, 407.8136052600172, -87.2766969610363, -206.27469382354741, 160.503138855334, -274.77081478516141, -2.6315281403397535, 448.35453602688131, -410.03524251004461, 247.32901465489022, 355.53915350303942);
            bool4x3 r0 = bool4x3(false, false, false, false, false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r0, a0 == b0);

            double a1 = (-298.06671180299793);
            double4x3 b1 = double4x3(414.10151429385951, -481.30262707234482, 196.55074438664633, 34.60100008668428, 113.76156645350227, -386.45337861890596, -124.49174672201821, 243.8866447153905, -492.6181826501238, 145.424413033493, 421.55070968230757, -95.409988209330493);
            bool4x3 r1 = bool4x3(false, false, false, false, false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r1, a1 == b1);

            double a2 = (336.80928746648567);
            double4x3 b2 = double4x3(209.58380589707929, 487.441424358376, 161.80653365040507, 149.84247095409899, 225.723996505944, -71.21880176999548, 85.780251781353854, 192.547256797807, -49.887493395194156, -229.80195652218629, -103.40733413743197, 19.215747126944279);
            bool4x3 r2 = bool4x3(false, false, false, false, false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r2, a2 == b2);

            double a3 = (492.88110827509365);
            double4x3 b3 = double4x3(140.40315849166507, -267.53641546309757, 125.9727018466092, 478.00049398746364, 116.14462071105118, -368.95778220191494, -225.02866350162247, 2.7237255585955609, -452.2632198055569, 87.456553261474028, 401.30651802630462, -18.645524272064449);
            bool4x3 r3 = bool4x3(false, false, false, false, false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r3, a3 == b3);
        }

        [TestCompiler]
        public static void double4x3_operator_not_equal_wide_wide()
        {
            double4x3 a0 = double4x3(430.8425316432689, 104.69001798736394, 225.80243478799355, -310.57017841496048, -418.61945815506363, 304.12820281839379, -509.32682561749908, -160.53807719076895, -203.30197606016975, -505.76325368590807, 162.17220623892365, 1.1561973100324394);
            double4x3 b0 = double4x3(210.02470622305975, -55.203330304102678, -269.92533672504373, -234.54673372700194, 25.917412054686565, -63.726991444699024, -484.55371092471933, -425.333599050219, -53.274394775402925, 328.1944192984115, 15.963139303011417, 461.71412417931208);
            bool4x3 r0 = bool4x3(true, true, true, true, true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r0, a0 != b0);

            double4x3 a1 = double4x3(65.662074358045174, 102.78780250567377, 172.93008120960098, 26.621009123800832, 235.12595259171258, 128.54198533321824, -354.99697630246959, 334.35948220564023, -495.83200692377613, 468.30740163675853, 458.37094733601941, 299.93733300824522);
            double4x3 b1 = double4x3(-113.36304455313973, -240.07297264787974, 495.11916970420589, 203.5583661550462, 340.49345103860526, -241.90719448863865, 459.56982896270688, 213.0737384357833, -384.7828506831, -255.07233846144396, 477.66343115161328, -248.03662621604121);
            bool4x3 r1 = bool4x3(true, true, true, true, true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r1, a1 != b1);

            double4x3 a2 = double4x3(43.12718560319729, -354.71349994964595, -145.28719551176169, 390.80186218340032, -303.13149108697263, 391.13459533785215, 139.2868607692825, 104.52318506339714, 511.29640293088573, 213.1470559635884, -101.09569625793756, 441.6633772522506);
            double4x3 b2 = double4x3(-407.92344565313471, -199.78886971240343, 151.84326488889906, -97.120607659742518, 154.97589380805186, -172.83452065886672, 441.5027942329192, -401.73862785926957, -411.43016333665241, -337.8202766561044, -430.63088270213029, -150.87180502287663);
            bool4x3 r2 = bool4x3(true, true, true, true, true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r2, a2 != b2);

            double4x3 a3 = double4x3(124.36612301895684, 312.02642622218764, 59.65573766625289, -508.65682315670739, 98.699622438615052, 228.79984174892297, 337.83266965385189, -163.1544383331921, 461.69158885520494, -450.77570340166596, -443.56476637514527, -438.2131223334992);
            double4x3 b3 = double4x3(-206.83699212169137, 34.955056922023687, -255.77146422852366, 99.99864320643178, -161.17557127828502, 68.853526862735634, -285.59012116379574, -428.71731229718648, -286.33740700703925, 2.0271298894784877, -4.8059971354929871, -425.33480115669539);
            bool4x3 r3 = bool4x3(true, true, true, true, true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r3, a3 != b3);
        }

        [TestCompiler]
        public static void double4x3_operator_not_equal_wide_scalar()
        {
            double4x3 a0 = double4x3(-16.914588697680529, 168.83411486858233, -462.71352145760949, 130.30776959765137, 214.50161443208424, -440.26328178879959, -197.12796053529155, -169.09985860115842, -386.61117595555783, -281.02101362916687, -270.26885593601912, -403.96372313236992);
            double b0 = (-145.37277109239847);
            bool4x3 r0 = bool4x3(true, true, true, true, true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r0, a0 != b0);

            double4x3 a1 = double4x3(-269.80570877241234, -71.750904831919286, -432.75573917513515, -457.36312100727258, -13.519590622521719, 273.87305773136814, 185.042454567292, -482.53069351731364, 116.39514427836764, 511.73495578753523, 230.50753628020527, 100.27476768394683);
            double b1 = (299.65422763473089);
            bool4x3 r1 = bool4x3(true, true, true, true, true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r1, a1 != b1);

            double4x3 a2 = double4x3(129.68240863163135, -220.63900409482375, 140.33521921016984, 369.2123617461009, 453.81121489676241, -333.66624871532724, -373.93775218256644, 150.20429451307484, -442.16476627912596, 372.32001488856974, -95.837970539852051, 495.56669663617697);
            double b2 = (321.17879048044733);
            bool4x3 r2 = bool4x3(true, true, true, true, true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r2, a2 != b2);

            double4x3 a3 = double4x3(-5.385580780629823, -459.61274812166243, 243.3090676010163, 314.10215702378287, 96.745011136282756, -168.16192944727931, -71.905446324453408, 216.60847983910162, -377.37381356646017, 142.35499841643264, -432.27255722148, 94.290808959999481);
            double b3 = (-210.50298581388915);
            bool4x3 r3 = bool4x3(true, true, true, true, true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r3, a3 != b3);
        }

        [TestCompiler]
        public static void double4x3_operator_not_equal_scalar_wide()
        {
            double a0 = (275.79582823244664);
            double4x3 b0 = double4x3(-57.196896341255353, -382.4325279586169, 97.820359990848374, -161.46364529499022, -458.39563367254829, -499.61786364932448, 327.92217818271467, 367.57121699283425, 59.7863667289663, -209.58068118318016, -62.580453186566217, -479.97497604786184);
            bool4x3 r0 = bool4x3(true, true, true, true, true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r0, a0 != b0);

            double a1 = (-49.494519495169868);
            double4x3 b1 = double4x3(-114.68521338081229, 109.93924599044919, -176.28482755286842, -347.48529903380449, 85.540928165214609, -356.65954868712441, -104.24357490625397, -133.54918605347592, 243.53971135036079, 13.141311890045813, -379.98594754747393, -41.281226892620907);
            bool4x3 r1 = bool4x3(true, true, true, true, true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r1, a1 != b1);

            double a2 = (87.911684792447659);
            double4x3 b2 = double4x3(-339.07727996403224, -371.82034533648766, 333.14425936953364, 294.81196011920088, -187.14565977228136, 220.19225774528093, -228.18207250730234, -499.72373914146971, 97.4059055305114, 501.60439395420462, 459.67539880223353, 158.09812290877949);
            bool4x3 r2 = bool4x3(true, true, true, true, true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r2, a2 != b2);

            double a3 = (358.48858921531985);
            double4x3 b3 = double4x3(243.51259171381253, 336.70294991913386, 89.953149122164177, -65.578377515812576, -159.26015503670095, 410.58855528877518, 123.96303206494224, -239.6251271886868, -299.42983808155628, -491.29190443981992, 207.71164641515895, 271.56546724567443);
            bool4x3 r3 = bool4x3(true, true, true, true, true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r3, a3 != b3);
        }

        [TestCompiler]
        public static void double4x3_operator_less_wide_wide()
        {
            double4x3 a0 = double4x3(196.84256825076534, 336.40979997087732, 251.96372115424072, 257.65591466503963, 430.04588647840819, -62.419644146421774, 8.8392293494376872, -333.81671563434259, 164.67880662003472, -350.94487516532877, 3.84143662631584, 125.40972024081725);
            double4x3 b0 = double4x3(-465.34502313348696, -256.15239751346053, -314.814018634527, 364.56673662949663, 100.21050290959442, 182.56098636545289, 3.116978885194726, -259.43047893207074, -437.33490749696966, -456.0437321402336, -394.2559718537405, 401.91369099259077);
            bool4x3 r0 = bool4x3(false, false, false, true, false, true, false, true, false, false, false, true);
            TestUtils.AreEqual(r0, a0 < b0);

            double4x3 a1 = double4x3(-111.12994127680076, 70.005523475820951, 448.19828173527412, -419.98711200244122, -258.30166757213965, -34.832201735504043, -69.859397682295821, 67.767227442826766, -139.77729207825723, 385.43464130229995, 133.707390609061, 506.18837117878184);
            double4x3 b1 = double4x3(313.43916454605721, 121.28668194696616, -28.012290729215522, -282.96589697663012, 330.06440631023816, 124.09937077579059, -183.69031700104955, 373.0607623406969, 109.75094013556418, -203.57134232463841, 45.6486556742567, -360.95226280808089);
            bool4x3 r1 = bool4x3(true, true, false, true, true, true, false, true, true, false, false, false);
            TestUtils.AreEqual(r1, a1 < b1);

            double4x3 a2 = double4x3(34.442885653322037, 412.11373896715872, -84.809773246203463, 444.78534504621541, -78.754743374304269, 366.97754376334024, 127.18045788965208, 428.36845489422251, 8.1976149120356467, -71.137346062407516, -474.05081937930117, 322.42891875022508);
            double4x3 b2 = double4x3(211.91309867236441, -313.28636207863985, -259.66108691862837, 79.0985401045059, 446.49610897828643, 450.52455660818362, -375.63076728192658, -53.941822792376286, -291.4537471697916, 190.77482303919965, 54.083913589866825, -163.63087637891567);
            bool4x3 r2 = bool4x3(true, false, false, false, true, true, false, false, false, true, true, false);
            TestUtils.AreEqual(r2, a2 < b2);

            double4x3 a3 = double4x3(6.8978650602036851, 195.73355993802363, -267.69061315604051, -243.79369961647024, 319.25079336727538, -425.15620370635588, 71.873970303625811, 313.84387626957334, 397.27906126402274, -309.14588584990514, -38.667860764389786, -266.11969554895518);
            double4x3 b3 = double4x3(-212.00563750602566, 406.09049649075166, -183.01893743454428, 355.22140304894253, -81.042213716098217, -275.71481693709029, 405.29925007619863, -510.64058065351128, 398.06925815999011, -4.35550666058225, 129.24267083464315, -276.1465247963306);
            bool4x3 r3 = bool4x3(false, true, true, true, false, true, true, false, true, true, true, false);
            TestUtils.AreEqual(r3, a3 < b3);
        }

        [TestCompiler]
        public static void double4x3_operator_less_wide_scalar()
        {
            double4x3 a0 = double4x3(-132.05731708000292, -192.46500477216438, -66.834607870706634, -379.01750081545561, -360.28242199508588, 20.927834282129879, -158.24074537970159, 437.34587522845061, -20.452607402788772, 225.29148517609178, 307.48418607725023, 274.01523292903562);
            double b0 = (-156.01021845452965);
            bool4x3 r0 = bool4x3(false, true, false, true, true, false, true, false, false, false, false, false);
            TestUtils.AreEqual(r0, a0 < b0);

            double4x3 a1 = double4x3(373.54965584983563, 105.0301654827922, -58.010895994496934, 109.67008810381878, -108.853174498702, -44.971252223929014, 140.42607147080173, -500.08827638071415, 172.10334857371788, -197.50074610370245, -7.27149987559369, -432.99049898283113);
            double b1 = (398.52368301829495);
            bool4x3 r1 = bool4x3(true, true, true, true, true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r1, a1 < b1);

            double4x3 a2 = double4x3(62.158315449095426, -377.85232299279994, -500.25573586870718, 149.1149638393498, 119.88061695912882, 202.63918909925928, 274.95066393304182, 66.4120323967245, 274.99944580486022, -149.63581402117194, 223.75870834279749, 73.266824041151835);
            double b2 = (-72.254720959931035);
            bool4x3 r2 = bool4x3(false, true, true, false, false, false, false, false, false, true, false, false);
            TestUtils.AreEqual(r2, a2 < b2);

            double4x3 a3 = double4x3(213.09497390179661, 418.3772096197946, 421.10357947885223, -187.16683658732421, 389.10944313048822, 401.33542818638284, -106.28507929029178, 380.60798162063952, 385.65284484701829, 120.65986376659009, -13.830871826890359, -500.12711238308208);
            double b3 = (322.85949459805124);
            bool4x3 r3 = bool4x3(true, false, false, true, false, false, true, false, false, true, true, true);
            TestUtils.AreEqual(r3, a3 < b3);
        }

        [TestCompiler]
        public static void double4x3_operator_less_scalar_wide()
        {
            double a0 = (-423.117411095238);
            double4x3 b0 = double4x3(385.09483617595151, -123.93348532725753, 86.376572887588509, 133.44217378154497, 161.45794947513286, 229.75426660746064, 222.57159934871436, 315.53116360098647, -447.20351883731945, 271.83385790131695, -393.60531324595462, 317.48689737798964);
            bool4x3 r0 = bool4x3(true, true, true, true, true, true, true, true, false, true, true, true);
            TestUtils.AreEqual(r0, a0 < b0);

            double a1 = (-164.6051085761772);
            double4x3 b1 = double4x3(-282.87605370342544, 296.97953071118309, -254.40115582868509, 365.61562054493265, -441.98425671178114, -131.42866021554391, 442.62897631275882, -29.792842163607872, -138.37379533535511, 9.2169721169476588, -226.7305482489665, 171.02944310523083);
            bool4x3 r1 = bool4x3(false, true, false, true, false, true, true, true, true, true, false, true);
            TestUtils.AreEqual(r1, a1 < b1);

            double a2 = (376.62522595777421);
            double4x3 b2 = double4x3(-462.58872697436658, -142.36729795409707, -456.25377414014832, 66.6102416825529, 169.37875779409831, 327.44439450253003, 64.0879266560487, -153.50390369887646, 199.38014921889646, -244.96905314408662, 472.74382112582396, -363.78010075342843);
            bool4x3 r2 = bool4x3(false, false, false, false, false, false, false, false, false, false, true, false);
            TestUtils.AreEqual(r2, a2 < b2);

            double a3 = (-179.48750575794259);
            double4x3 b3 = double4x3(-83.4251511485433, 178.88648828253451, 62.155780582761281, 409.74679560668153, -117.16365366669544, 316.60167684992848, 285.51627339307049, 18.674469718092382, 282.52931298060776, 132.92379075518056, -318.21533957040651, 314.83989181874313);
            bool4x3 r3 = bool4x3(true, true, true, true, true, true, true, true, true, true, false, true);
            TestUtils.AreEqual(r3, a3 < b3);
        }

        [TestCompiler]
        public static void double4x3_operator_greater_wide_wide()
        {
            double4x3 a0 = double4x3(483.50140141113729, 310.81563415695712, 106.9661896726891, 295.73526038589671, 116.95757179938141, -478.29977653841479, -14.897393471979228, -33.817441717636484, -24.740548383789417, 319.78262701620474, -120.15856581561201, -289.00857962714906);
            double4x3 b0 = double4x3(-471.39802454011425, -371.98528617060992, 36.900723236101044, -316.76360407320954, 19.683055648432628, 207.3091381561519, 362.79748861994483, 324.95341816775192, 340.94807140014507, 25.986035120666997, -114.2111352021858, 240.80346428640348);
            bool4x3 r0 = bool4x3(true, true, true, true, true, false, false, false, false, true, false, false);
            TestUtils.AreEqual(r0, a0 > b0);

            double4x3 a1 = double4x3(455.85146662958505, 144.70691139283917, 63.931990891663304, -285.68304099034663, -502.0907201720824, -337.19446412529538, 474.31734274063137, -507.14510679018923, -133.56559735795742, -443.10913654934109, -464.34137056038776, -68.361549647693323);
            double4x3 b1 = double4x3(273.42244757033063, 325.51576224226312, 27.341068995809678, 64.479532510265472, 200.94836983501375, 100.12266998184964, -79.00710896356361, -315.137945560337, -122.98542815213347, -163.77920229908972, -492.56600617457462, -90.797273439726439);
            bool4x3 r1 = bool4x3(true, false, true, false, false, false, true, false, false, false, true, true);
            TestUtils.AreEqual(r1, a1 > b1);

            double4x3 a2 = double4x3(-185.99299987870876, -157.80389340119615, -74.124229227250567, -94.471165939453613, 329.61055508703487, -315.83675280019486, 404.193811843262, 131.30440503512716, -206.6339033612208, 197.39985832823436, 187.99195274524016, 362.63607542712055);
            double4x3 b2 = double4x3(-284.9012335673446, -23.653687249707843, 174.93002112905026, 85.7125366133231, -441.98783012944637, 345.54374210235835, 482.21949814363359, -422.38349719642827, -30.779309048680261, 296.15423669300708, 378.05988830051376, -457.73343942022575);
            bool4x3 r2 = bool4x3(true, false, false, false, true, false, false, true, false, false, false, true);
            TestUtils.AreEqual(r2, a2 > b2);

            double4x3 a3 = double4x3(336.09317819033436, -352.44836752137559, -183.10199865284471, 193.14483484679124, -170.216002781976, -0.49123787902817639, -326.85503760299412, -373.39623826248396, -216.58046422553269, 282.51211489481489, -275.17035616336875, -207.331757403599);
            double4x3 b3 = double4x3(122.92057257654176, -509.17313766347854, 386.7706226719406, 436.41747280415962, -276.49581516743444, -163.16677554099203, 249.97064625936127, -165.02074130113272, 89.092999261381578, 404.30517287007774, -340.68884889254758, -103.78509550159106);
            bool4x3 r3 = bool4x3(true, true, false, false, true, true, false, false, false, false, true, false);
            TestUtils.AreEqual(r3, a3 > b3);
        }

        [TestCompiler]
        public static void double4x3_operator_greater_wide_scalar()
        {
            double4x3 a0 = double4x3(64.317918092160426, -397.70346445483318, 431.87690826499693, 85.702980796668157, 246.26305233978803, 197.49155602114809, 286.1994608781298, 280.81334818564972, -405.78459210218148, 171.56538661362856, -241.80727326209063, 333.57817498481745);
            double b0 = (305.85991992888034);
            bool4x3 r0 = bool4x3(false, false, true, false, false, false, false, false, false, false, false, true);
            TestUtils.AreEqual(r0, a0 > b0);

            double4x3 a1 = double4x3(370.27919524269146, -356.5923551789449, -353.03129522550444, 396.64532608382649, 467.22205541432936, -240.0134228393498, 502.91505193287276, 315.46759024051369, -259.28970134411458, 281.23064554912537, 428.79219909608, 245.15306460352292);
            double b1 = (-413.70138116073861);
            bool4x3 r1 = bool4x3(true, true, true, true, true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r1, a1 > b1);

            double4x3 a2 = double4x3(-279.17542494422543, -124.77154856769909, -425.65293451103054, 99.132852838902181, 355.0605339273161, -456.43941256796916, 154.48902208846482, 405.52974409867534, -157.73379643155903, 186.08628639303436, 249.99909531790718, -110.0969179189284);
            double b2 = (-453.86309668694764);
            bool4x3 r2 = bool4x3(true, true, true, true, true, false, true, true, true, true, true, true);
            TestUtils.AreEqual(r2, a2 > b2);

            double4x3 a3 = double4x3(-435.3045134187231, -254.34657037181154, -428.98794980951953, 255.37367761105941, -309.11230459302305, 185.50160678918553, -201.33417687254689, 23.321151029002408, -143.97610027341921, -111.77951412637697, -356.65661852278589, -318.31356945555359);
            double b3 = (72.752033029101767);
            bool4x3 r3 = bool4x3(false, false, false, true, false, true, false, false, false, false, false, false);
            TestUtils.AreEqual(r3, a3 > b3);
        }

        [TestCompiler]
        public static void double4x3_operator_greater_scalar_wide()
        {
            double a0 = (-282.67049635698572);
            double4x3 b0 = double4x3(358.09997360692353, -72.5964134077525, -232.16380106292843, -60.706723956720282, 75.156642710397364, 150.88350040786133, 339.53917924479538, -498.19602965665797, 459.74610326241054, -227.96872316485678, 335.86213485145106, 76.178844248959308);
            bool4x3 r0 = bool4x3(false, false, false, false, false, false, false, true, false, false, false, false);
            TestUtils.AreEqual(r0, a0 > b0);

            double a1 = (296.85993899817572);
            double4x3 b1 = double4x3(177.49000390688423, -281.20120657663847, 244.72285162877427, 137.32857257562159, -385.33824724021287, 443.16345879210326, -353.56254141105455, 26.040673983302327, -331.7939499969566, -43.691963454565041, 20.949428806523542, -211.17984423934473);
            bool4x3 r1 = bool4x3(true, true, true, true, true, false, true, true, true, true, true, true);
            TestUtils.AreEqual(r1, a1 > b1);

            double a2 = (227.42171894173214);
            double4x3 b2 = double4x3(-84.7797711290325, -375.13548701588786, -205.17813096064054, -197.04714617368165, -219.63402305340117, -210.01563344244641, -266.773715858708, 144.77848703450456, -471.71120069535039, -155.91317494023275, 99.724721716588647, -230.94484316135981);
            bool4x3 r2 = bool4x3(true, true, true, true, true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r2, a2 > b2);

            double a3 = (-338.86889640375455);
            double4x3 b3 = double4x3(334.06826630889827, -158.66085703608621, -315.01822414762262, -177.19281991626735, 171.959285100903, 198.38915047347041, 303.67832603290594, 400.69957346501735, 351.87867252523017, -31.76966072608235, 386.07330077983124, -360.34885733218346);
            bool4x3 r3 = bool4x3(false, false, false, false, false, false, false, false, false, false, false, true);
            TestUtils.AreEqual(r3, a3 > b3);
        }

        [TestCompiler]
        public static void double4x3_operator_less_equal_wide_wide()
        {
            double4x3 a0 = double4x3(-438.52313753521219, 210.48942837980087, 4.8773329280677444, -137.29793817237857, 156.09410174009111, -363.92412035722475, -97.948485181642923, 437.29539009430232, 458.53029153241323, -294.06474675520542, 23.622613679441884, -34.284056441059363);
            double4x3 b0 = double4x3(-474.8141498392514, 304.3710555063426, 234.8241737982371, -390.48543209139513, -297.17535295019638, -326.29239121372461, 107.2538764976216, -413.13107342884462, 67.094432623635271, 470.07522724106684, -84.499104777583455, 392.78422683886447);
            bool4x3 r0 = bool4x3(false, true, true, false, false, true, true, false, false, true, false, true);
            TestUtils.AreEqual(r0, a0 <= b0);

            double4x3 a1 = double4x3(149.736484835733, -418.8866781754823, -197.50252899783783, -88.2055118494693, -376.71814292330208, 341.62712899857536, -83.309179106405566, -107.49073295830317, 319.46688833807912, 205.35738501574724, 345.56372968552807, 395.32190746596177);
            double4x3 b1 = double4x3(-263.53175485484849, 369.30090039284005, -333.32529298091555, 238.41347443238533, 486.24259279959028, 279.65021408705513, 236.05201803709008, 132.75898248178839, 66.294708998079727, 183.00210699020056, 200.13055071613314, 339.043800750302);
            bool4x3 r1 = bool4x3(false, true, false, true, true, false, true, true, false, false, false, false);
            TestUtils.AreEqual(r1, a1 <= b1);

            double4x3 a2 = double4x3(-222.87415490992095, 439.02200790821666, -368.0755667016262, -200.03860173003682, 71.46990660180802, -357.36542932939039, 141.7108519737194, 319.0170969064427, 303.03015889927292, -461.57424829042247, 277.62674749904625, 182.178105677561);
            double4x3 b2 = double4x3(438.53791710293751, 145.40187866306019, 178.16310199450845, 157.97596724237133, 329.7052015409364, -243.59091221708383, 5.4011614347813293, -22.580605278993289, -90.33759478961008, -72.19107798123315, -354.35482399275281, -289.52172650467685);
            bool4x3 r2 = bool4x3(true, false, true, true, true, true, false, false, false, true, false, false);
            TestUtils.AreEqual(r2, a2 <= b2);

            double4x3 a3 = double4x3(-337.41483441806156, -361.39166109701227, 222.14351020666936, -464.7795028466636, -146.8536623208102, 80.175055302761052, -260.34730088913221, 94.489041134011472, 174.2811945296271, -303.81969251475283, 81.417447366480474, 503.048130508069);
            double4x3 b3 = double4x3(85.176270763006187, 469.32790468136216, 294.71383656874013, 461.60593411959985, -245.93047892578431, -124.04044610077534, 278.39260948747051, -42.881244917810534, -328.34883824379597, 98.985619352658091, -375.8998207412194, -197.93427309670221);
            bool4x3 r3 = bool4x3(true, true, true, true, false, false, true, false, false, true, false, false);
            TestUtils.AreEqual(r3, a3 <= b3);
        }

        [TestCompiler]
        public static void double4x3_operator_less_equal_wide_scalar()
        {
            double4x3 a0 = double4x3(193.4958237118534, 168.91555197952107, -313.9930695565385, 81.826965131716292, 18.503590830836288, -0.35819602029312136, 241.36115776810846, -463.81641242644582, -1.3577692515020203, -268.89945591096739, 398.9919504593089, -471.253072242836);
            double b0 = (443.85054299042122);
            bool4x3 r0 = bool4x3(true, true, true, true, true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r0, a0 <= b0);

            double4x3 a1 = double4x3(-264.93778264938749, 11.246050124636895, 424.7040156911612, 426.48223157715154, 56.319978501796754, -196.28791126808522, 31.901173844887467, -152.2575724833913, -437.92645975478297, -37.104814785115821, -47.144214413661587, 333.6230348710078);
            double b1 = (82.258299150624453);
            bool4x3 r1 = bool4x3(true, true, false, false, true, true, true, true, true, true, true, false);
            TestUtils.AreEqual(r1, a1 <= b1);

            double4x3 a2 = double4x3(-274.80387438219225, -260.46056926458169, 192.30916008367626, 145.30606777281787, -466.13296363602063, -494.26732968458316, -111.57013922164691, -139.54120332540072, -146.58935148389514, 33.984021917909445, -445.70445248377717, -451.04219624541804);
            double b2 = (358.67627804292192);
            bool4x3 r2 = bool4x3(true, true, true, true, true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r2, a2 <= b2);

            double4x3 a3 = double4x3(-122.03926115950537, -202.46552119146361, -76.564869677910963, 218.03280482908372, -103.5359361653849, -283.35842312656268, -374.76167910860931, -213.49586568283655, 477.49183891596829, -383.37006849021191, 23.964948426915726, -5.9607760933976692);
            double b3 = (83.327714720427821);
            bool4x3 r3 = bool4x3(true, true, true, false, true, true, true, true, false, true, true, true);
            TestUtils.AreEqual(r3, a3 <= b3);
        }

        [TestCompiler]
        public static void double4x3_operator_less_equal_scalar_wide()
        {
            double a0 = (393.60626644343427);
            double4x3 b0 = double4x3(-75.688363825757222, -44.2638714519627, 125.86491566797019, 191.96488174794467, 13.543054825413492, -197.0519259893577, -423.945100743298, -330.04861680141119, 420.16553779140372, 105.54730777887039, 174.82126363311954, 296.71757831085358);
            bool4x3 r0 = bool4x3(false, false, false, false, false, false, false, false, true, false, false, false);
            TestUtils.AreEqual(r0, a0 <= b0);

            double a1 = (-469.70041845259277);
            double4x3 b1 = double4x3(123.26718979853536, 112.9969695140594, 495.14339493920249, -488.65789364681478, 388.53941148730894, -493.24077080806751, 16.451064832718657, -387.6516336815672, -229.1773127192526, -373.01533930982248, -391.142134610164, 90.994149488859875);
            bool4x3 r1 = bool4x3(true, true, true, false, true, false, true, true, true, true, true, true);
            TestUtils.AreEqual(r1, a1 <= b1);

            double a2 = (-178.39613517485378);
            double4x3 b2 = double4x3(-69.621067317957568, 471.7908458522478, -67.4667532758167, 45.305359623071467, -154.69219000390365, 385.73888248286153, -431.652945004242, -331.67304841227508, -349.89271013340573, -114.83913021666888, -245.21782671903156, -486.69548224224496);
            bool4x3 r2 = bool4x3(true, true, true, true, true, true, false, false, false, true, false, false);
            TestUtils.AreEqual(r2, a2 <= b2);

            double a3 = (391.95091957224111);
            double4x3 b3 = double4x3(-125.77055150166643, -229.81227527829458, 289.44901265271426, -200.49423680104979, 281.59270991086623, 99.901066588191838, -146.02742845659492, 124.14839774190841, 94.357016368935319, 93.920113845691162, -484.92414711645068, -270.79689396116737);
            bool4x3 r3 = bool4x3(false, false, false, false, false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r3, a3 <= b3);
        }

        [TestCompiler]
        public static void double4x3_operator_greater_equal_wide_wide()
        {
            double4x3 a0 = double4x3(-507.92858409692, 504.49748181947393, -385.43449205226938, -262.32340944107784, -37.550928848586466, -111.59527759980193, -463.70202157632542, 387.44885772627265, 456.96878573716094, -211.01015506079892, 182.41135391146474, -53.596053863687473);
            double4x3 b0 = double4x3(-81.346509732933043, 297.66615047010885, 171.06540616371922, -431.03805538222105, -6.859075311040101, 319.72570362674333, 254.079170106947, 396.5724000393285, 178.83927615864172, -447.06336304501787, 288.49268569075161, 474.88929460704765);
            bool4x3 r0 = bool4x3(false, true, false, true, false, false, false, false, true, true, false, false);
            TestUtils.AreEqual(r0, a0 >= b0);

            double4x3 a1 = double4x3(-309.57021608463032, -136.02249127999994, 280.73629082401112, -96.9958942388165, -174.05950673579213, 88.9019382413951, 43.816040774721728, -446.07842585354967, 16.645595796706857, 409.83252043734888, -191.32987245886113, 222.99782548798146);
            double4x3 b1 = double4x3(-321.75022831640683, -395.97722048125104, -158.69246037243516, 391.48869318118727, -368.10924141859135, 89.1238043723273, -510.27932214656812, -486.92979525352354, -81.215552606254619, 274.21882046117389, -212.88155494112596, 288.99530591117);
            bool4x3 r1 = bool4x3(true, true, true, false, true, false, true, true, true, true, true, false);
            TestUtils.AreEqual(r1, a1 >= b1);

            double4x3 a2 = double4x3(404.28838915577546, 230.60328136691976, -441.78928228923553, -86.293056289801882, 484.24954413075443, 95.2363665547391, -204.91210255628084, -199.77434620623211, -421.86318107222354, -18.214789637464492, -346.8227681344481, -159.24364073539323);
            double4x3 b2 = double4x3(307.73173131967508, 307.24516620638087, -199.39178213821339, -284.42126978767163, -482.39181278757371, 448.3157362641374, -378.3461889598268, -390.8584684761513, 8.9160292190108521, 416.40721984226593, -213.67494664605471, 455.24810788372906);
            bool4x3 r2 = bool4x3(true, false, false, true, true, false, true, true, false, false, false, false);
            TestUtils.AreEqual(r2, a2 >= b2);

            double4x3 a3 = double4x3(112.9177020121914, 48.29104115827522, 390.66016525340274, 154.21916706590878, -32.748053804388292, -288.2656096370265, 122.70425826064513, 321.2779754704228, 230.18381487121053, 116.87426024157287, -93.515688701307283, 229.98230730275736);
            double4x3 b3 = double4x3(-236.08035980727539, -248.37309348228064, 184.18512567513858, 415.31133885649558, 86.982202808830039, 485.00455950433604, 107.75893955480262, -486.66772459757874, -138.67679197093321, 14.207853562295327, -382.39416211768713, -117.00821524346628);
            bool4x3 r3 = bool4x3(true, true, true, false, false, false, true, true, true, true, true, true);
            TestUtils.AreEqual(r3, a3 >= b3);
        }

        [TestCompiler]
        public static void double4x3_operator_greater_equal_wide_scalar()
        {
            double4x3 a0 = double4x3(465.15218732559686, -424.8860745024337, -209.22109685150025, 58.779852656079356, -302.26910533675414, 140.12558252183976, 16.353385694489475, -344.55997316192838, 393.27804846003562, -315.70155086913218, 441.0115565923096, -509.78156757251435);
            double b0 = (-5.5998842742293391);
            bool4x3 r0 = bool4x3(true, false, false, true, false, true, true, false, true, false, true, false);
            TestUtils.AreEqual(r0, a0 >= b0);

            double4x3 a1 = double4x3(-36.994287269652943, -164.97393830352183, -466.12009046325466, -123.8137477020797, 215.65121779947128, 104.99569730879534, 314.34603014325069, 190.51609882643265, -83.111429014760745, -23.836435567511444, 143.04935962662535, -264.91997945724052);
            double b1 = (494.82028865014217);
            bool4x3 r1 = bool4x3(false, false, false, false, false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r1, a1 >= b1);

            double4x3 a2 = double4x3(-169.70222457205051, 359.09582035573931, -260.42331016269668, 354.19514219565087, -111.84533768140028, 33.309096113456917, 355.63126938214123, -435.36056753404466, -38.39930893778768, -93.29572896533449, -338.84962169213668, 436.89581676800537);
            double b2 = (329.70751610850334);
            bool4x3 r2 = bool4x3(false, true, false, true, false, false, true, false, false, false, false, true);
            TestUtils.AreEqual(r2, a2 >= b2);

            double4x3 a3 = double4x3(511.08413982348713, -453.79924771459741, 170.91899998994495, -182.82575591971437, -207.51692710049309, -319.500592142111, -240.5086177515372, 436.34132286363342, -66.956061632817637, 303.32088174639307, 180.19605907248149, 337.9651765012951);
            double b3 = (-277.67452419813469);
            bool4x3 r3 = bool4x3(true, false, true, true, true, false, true, true, true, true, true, true);
            TestUtils.AreEqual(r3, a3 >= b3);
        }

        [TestCompiler]
        public static void double4x3_operator_greater_equal_scalar_wide()
        {
            double a0 = (374.82703393270594);
            double4x3 b0 = double4x3(-1.609757185731894, 338.61524049314448, -116.18140392945213, -332.15732375353451, -355.9793509710484, -468.90144107719021, 38.579884785497484, -332.34754697063357, 2.8901150240051265, 467.77776477661814, 121.40638762405445, -305.02337303060267);
            bool4x3 r0 = bool4x3(true, true, true, true, true, true, true, true, true, false, true, true);
            TestUtils.AreEqual(r0, a0 >= b0);

            double a1 = (-58.428812292604164);
            double4x3 b1 = double4x3(-226.51955209789776, -47.020994446715804, 305.3026770582901, -427.40123315686418, 92.263649745035764, -497.17853736187266, -408.62564225151465, -455.23049113491106, 396.42608637196292, -469.29488561548987, -485.7540130493017, -182.34619268325446);
            bool4x3 r1 = bool4x3(true, false, false, true, false, true, true, true, false, true, true, true);
            TestUtils.AreEqual(r1, a1 >= b1);

            double a2 = (-291.54536284671417);
            double4x3 b2 = double4x3(278.740809331993, -75.87113932327884, 28.907059921374071, 287.72014988945807, 420.50978990109161, 473.62684152723614, 181.514540518408, -369.20287220981106, 243.74977385427326, -244.12415825767636, -242.9933451353541, -322.11536780098237);
            bool4x3 r2 = bool4x3(false, false, false, false, false, false, false, true, false, false, false, true);
            TestUtils.AreEqual(r2, a2 >= b2);

            double a3 = (192.974957794405);
            double4x3 b3 = double4x3(-54.725568558427312, -166.00083907228003, 244.29344117096321, 438.24940105818655, -162.69282610839832, 37.185346382290732, -506.66736459483735, -205.1630781652234, 368.3899807261256, -35.459948317827639, -20.916435966694905, 9.041354422011068);
            bool4x3 r3 = bool4x3(true, true, false, false, true, true, true, true, false, true, true, true);
            TestUtils.AreEqual(r3, a3 >= b3);
        }

        [TestCompiler]
        public static void double4x3_operator_add_wide_wide()
        {
            double4x3 a0 = double4x3(506.12905263627374, -501.77980803967444, 420.08479638587903, -186.03206476291274, -9.3123953385801883, 328.51179686585056, 424.34407659263536, 87.791079800478656, 462.41368148402012, -46.178705952213477, 401.17006296718966, -454.12414643453627);
            double4x3 b0 = double4x3(-28.757987751047096, -337.135153689019, -340.676816860529, 152.31202633320913, 423.66745420157326, 90.374096674087468, 376.18866246574964, 1.7671887882831925, -120.18586045139745, -279.62936628965167, -344.66710273580026, 242.8391956029642);
            double4x3 r0 = double4x3(477.37106488522664, -838.91496172869347, 79.407979525350015, -33.720038429703607, 414.35505886299308, 418.885893539938, 800.532739058385, 89.558268588761848, 342.22782103262267, -325.80807224186515, 56.502960231389409, -211.28495083157208);
            TestUtils.AreEqual(r0, a0 + b0);

            double4x3 a1 = double4x3(69.195687564646732, -177.95734485329939, 299.60415544156183, 340.7048587001417, 219.91602740991675, -321.90838232725321, 286.35534037573041, -333.41949311523672, -118.93216973120911, 68.607509406566351, 23.190902005504313, -205.57787547147734);
            double4x3 b1 = double4x3(418.5930504363929, -23.312797318823982, -95.099945827899489, 147.92812568877275, 331.03287926830023, -82.502564230236487, 279.44956291813844, 342.6227215931857, -300.35853185335105, -209.69408736456842, 446.55942150883345, -351.98918955027557);
            double4x3 r1 = double4x3(487.78873800103963, -201.27014217212337, 204.50420961366234, 488.63298438891445, 550.948906678217, -404.41094655748969, 565.80490329386885, 9.2032284779489828, -419.29070158456017, -141.08657795800207, 469.75032351433777, -557.56706502175291);
            TestUtils.AreEqual(r1, a1 + b1);

            double4x3 a2 = double4x3(11.521422629953122, -340.7950796283759, -68.931167873056211, 304.8532370556394, -86.633841316510825, 105.66915874633435, 349.28052799277032, 364.7078708916473, -429.03740449856843, 382.45806926417072, 186.09704479300274, 227.41184841255279);
            double4x3 b2 = double4x3(-263.12385642860261, -252.4585670216282, 289.82535542632706, 338.79615537207394, -232.61900364263869, -510.50825405051387, 349.2807325559113, -426.2124495106807, -331.41632882292208, -418.68880267566482, -341.7063559692848, -329.03588143411832);
            double4x3 r2 = double4x3(-251.60243379864949, -593.25364665000416, 220.89418755327085, 643.64939242771334, -319.25284495914951, -404.83909530417952, 698.56126054868162, -61.504578619033396, -760.45373332149052, -36.2307334114941, -155.60931117628206, -101.62403302156554);
            TestUtils.AreEqual(r2, a2 + b2);

            double4x3 a3 = double4x3(-298.76636733616067, 351.30280344155744, 98.725387857633336, -292.35170640254006, 112.17092590787024, 477.1657800512229, -266.30486619952364, -295.14070643817104, -485.82035778733916, -507.86872291372566, -338.21959582819585, 505.34219360041538);
            double4x3 b3 = double4x3(123.19857245460082, 189.52859482054066, 267.56994093003209, 134.63626605581317, -337.96815530302382, 50.728011870164437, 81.16342572176984, 442.09067198358969, -148.70453769932715, 6.9743440183691519, -334.91123906472291, 43.787097712879586);
            double4x3 r3 = double4x3(-175.56779488155985, 540.83139826209811, 366.29532878766543, -157.71544034672689, -225.79722939515358, 527.89379192138733, -185.1414404777538, 146.94996554541865, -634.52489548666631, -500.89437889535651, -673.13083489291876, 549.129291313295);
            TestUtils.AreEqual(r3, a3 + b3);
        }

        [TestCompiler]
        public static void double4x3_operator_add_wide_scalar()
        {
            double4x3 a0 = double4x3(-194.51420387742769, 338.54838696985894, 246.97140252169754, 100.51093797595752, -45.724677822424439, -478.11131094308166, 30.916145577522116, 60.37435224483454, -242.1187475855084, 82.50134495762245, 6.7993848355483806, -484.69981287638649);
            double b0 = (124.121678171736);
            double4x3 r0 = double4x3(-70.3925257056917, 462.67006514159493, 371.09308069343354, 224.63261614769351, 78.397000349311554, -353.98963277134567, 155.03782374925811, 184.49603041657053, -117.99706941377241, 206.62302312935844, 130.92106300728437, -360.57813470465049);
            TestUtils.AreEqual(r0, a0 + b0);

            double4x3 a1 = double4x3(-188.26501068298938, -267.78430688929944, 189.25996669999324, 198.53359684652355, 187.53610023648298, -424.92567582844089, 302.10236730338181, 300.39907970111778, 124.02158909850823, -200.16134295247559, 31.37822701007974, 362.52213518811493);
            double b1 = (-213.52673087526426);
            double4x3 r1 = double4x3(-401.79174155825365, -481.31103776456371, -24.266764175271021, -14.993134028740712, -25.990630638781283, -638.45240670370515, 88.575636428117548, 86.872348825853521, -89.505141776756034, -413.68807382773986, -182.14850386518452, 148.99540431285067);
            TestUtils.AreEqual(r1, a1 + b1);

            double4x3 a2 = double4x3(-423.98885961248953, 374.21141474983256, -465.69948957194549, -311.04303779781003, 84.918990413154916, -432.44245716204978, 235.75065886031405, -472.63775394514096, -257.57773721291579, 186.120703068618, -170.29822667422621, -115.27248840931452);
            double b2 = (432.41331907380993);
            double4x3 r2 = double4x3(8.4244594613203958, 806.62473382364249, -33.286170498135562, 121.3702812759999, 517.33230948696485, -0.029138088239847093, 668.163977934124, -40.224434871331027, 174.83558186089414, 618.534022142428, 262.11509239958372, 317.14083066449541);
            TestUtils.AreEqual(r2, a2 + b2);

            double4x3 a3 = double4x3(-101.16882686557659, 246.5492557243208, -397.53459066782824, -199.04838213652761, 20.585038433123827, 207.3238519203494, 197.93518671669779, -201.54056439247938, -106.63866453368155, -179.38222631224534, 203.81710610343941, -364.82094853223344);
            double b3 = (257.77516973101308);
            double4x3 r3 = double4x3(156.60634286543649, 504.32442545533388, -139.75942093681516, 58.726787594485472, 278.36020816413691, 465.09902165136248, 455.71035644771086, 56.2346053385337, 151.13650519733153, 78.392943418767743, 461.59227583445249, -107.04577880122037);
            TestUtils.AreEqual(r3, a3 + b3);
        }

        [TestCompiler]
        public static void double4x3_operator_add_scalar_wide()
        {
            double a0 = (-340.35468284243473);
            double4x3 b0 = double4x3(511.36225652665007, -146.21663791789518, -106.21042661844308, -363.45024960276214, 199.08958325120136, -27.108407271610758, 419.84900041103788, 284.95503748811552, -164.92418129971446, -249.19032561461921, 150.92817718858282, 298.17509784278229);
            double4x3 r0 = double4x3(171.00757368421534, -486.57132076032991, -446.56510946087781, -703.80493244519687, -141.26509959123337, -367.46309011404549, 79.494317568603151, -55.399645354319205, -505.27886414214919, -589.54500845705388, -189.4265056538519, -42.179584999652434);
            TestUtils.AreEqual(r0, a0 + b0);

            double a1 = (-457.15341803857751);
            double4x3 b1 = double4x3(424.71807094324288, -301.85750283946163, 230.28885208363124, -423.58759351428023, -67.060037882560891, 68.7241366229598, -164.02241833695325, 318.93515339444161, 7.8045504129512437, 187.69836029210046, -3.6569664495331153, -446.0830535581722);
            double4x3 r1 = double4x3(-32.435347095334635, -759.01092087803909, -226.86456595494627, -880.7410115528578, -524.21345592113835, -388.42928141561771, -621.17583637553071, -138.2182646441359, -449.34886762562627, -269.45505774647705, -460.81038448811063, -903.23647159674965);
            TestUtils.AreEqual(r1, a1 + b1);

            double a2 = (-209.28724227160552);
            double4x3 b2 = double4x3(-38.212905186327589, -346.25717870623674, 465.60741708502519, -192.18595108398512, 278.69379843338106, 381.97845548297209, 481.24367283342576, -97.228162095522578, -455.51374289743313, 501.83498858932171, 358.70657818331688, 430.69978519468555);
            double4x3 r2 = double4x3(-247.50014745793311, -555.54442097784226, 256.32017481341967, -401.47319335559064, 69.406556161775541, 172.69121321136657, 271.95643056182024, -306.5154043671281, -664.80098516903865, 292.54774631771619, 149.41933591171136, 221.41254292308003);
            TestUtils.AreEqual(r2, a2 + b2);

            double a3 = (256.987155795557);
            double4x3 b3 = double4x3(207.65164837970008, -376.96518605619912, -428.08534093763927, -373.493553097124, -468.89328573126966, -467.65843818507085, 297.48495139623287, -506.89978646994558, -233.35846315760097, 434.55879493921941, -387.3151673690021, 171.59027751329836);
            double4x3 r3 = double4x3(464.63880417525706, -119.97803026064213, -171.09818514208229, -116.506397301567, -211.90612993571267, -210.67128238951386, 554.47210719178986, -249.91263067438859, 23.628692637956021, 691.5459507347764, -130.32801157344511, 428.57743330885535);
            TestUtils.AreEqual(r3, a3 + b3);
        }

        [TestCompiler]
        public static void double4x3_operator_sub_wide_wide()
        {
            double4x3 a0 = double4x3(160.4922617229131, 11.223957305412682, 359.20010607279846, -498.22830485656311, -355.25362913462038, -94.534852787170053, -410.46404786150163, -401.38464398001537, 317.70681944382693, 447.0604133303558, -489.07414482956477, -230.00838218909149);
            double4x3 b0 = double4x3(115.46876078260539, -130.98230630298252, 241.54083716196044, 9.9870860623135513, 419.89512582304656, 59.124466208333388, -402.38163847587145, -75.370143687059226, 320.97960796997859, -73.908757482612884, -31.444742455819949, -389.25194734579509);
            double4x3 r0 = double4x3(45.023500940307713, 142.2062636083952, 117.65926891083802, -508.21539091887666, -775.14875495766694, -153.65931899550344, -8.0824093856301715, -326.01450029295614, -3.2727885261516576, 520.96917081296874, -457.62940237374482, 159.2435651567036);
            TestUtils.AreEqual(r0, a0 - b0);

            double4x3 a1 = double4x3(24.875419389864192, 366.61447136784648, -107.3741567634857, -219.0081404275299, 473.90756891384137, 259.63620793988753, -360.119631219711, 7.8096120393879573, 437.42847439154446, -59.1991718091067, 418.74433322378638, 183.14215072576985);
            double4x3 b1 = double4x3(-375.02884000122026, 259.18275821357167, 276.648654351313, -453.06919905779381, -272.57653225240136, -191.14805301984217, 87.136884968325944, 430.02477594373033, 343.65711538105143, 121.02942067060133, -354.1881703595576, 249.05200373802893);
            double4x3 r1 = double4x3(399.90425939108445, 107.4317131542748, -384.02281111479869, 234.06105863026391, 746.48410116624268, 450.78426095972969, -447.25651618803693, -422.21516390434238, 93.771359010493029, -180.22859247970803, 772.932503583344, -65.909853012259077);
            TestUtils.AreEqual(r1, a1 - b1);

            double4x3 a2 = double4x3(271.23036516421962, 496.20853709439211, 165.35493691514944, -227.40367113212295, -166.52285702830312, 356.14227430715334, 386.92636579411396, -394.63875717420075, 126.90326625057651, 97.2168972944919, -150.01784641575898, -227.25051246734824);
            double4x3 b2 = double4x3(-2.2254426489702723, 22.447240601502017, 478.1129555544411, -320.0629958212669, -111.52409534879217, 222.22894607401872, -245.41106307013473, -119.90228348593337, -153.46565372937624, 374.11248439089979, 301.7634090398268, -281.43006552449896);
            double4x3 r2 = double4x3(273.45580781318989, 473.7612964928901, -312.75801863929166, 92.659324689143943, -54.998761679510949, 133.91332823313462, 632.33742886424875, -274.73647368826738, 280.36891997995275, -276.89558709640789, -451.78125545558578, 54.179553057150713);
            TestUtils.AreEqual(r2, a2 - b2);

            double4x3 a3 = double4x3(-198.83000406940931, 0.66276812584271738, -484.2455706467133, -295.99628056958147, -46.170990726990169, 499.95239304935205, 292.44011725692087, -106.42413597294325, 466.82713887972159, 487.37480400846096, 242.9946106611726, -468.90158985038363);
            double4x3 b3 = double4x3(-494.96436261337453, -320.73126021061614, 160.96219714030724, -132.93641025057826, -394.43753237018245, 406.85128588548457, 270.54461897096814, 507.79461335940039, 67.699203761154422, 263.40446412908125, 297.58066596536923, 170.83953746167924);
            double4x3 r3 = double4x3(296.13435854396522, 321.39402833645886, -645.20776778702054, -163.05987031900321, 348.26654164319228, 93.101107163867482, 21.895498285952726, -614.21874933234358, 399.12793511856717, 223.97033987937971, -54.586055304196634, -639.74112731206287);
            TestUtils.AreEqual(r3, a3 - b3);
        }

        [TestCompiler]
        public static void double4x3_operator_sub_wide_scalar()
        {
            double4x3 a0 = double4x3(207.38960108877609, 248.45773684627272, -384.82393211164697, -205.34476122881506, -374.81156152058929, 191.64204820973896, 18.856238135535364, -44.96160151667965, 480.85798738936796, 16.338193185784917, -366.86545269883493, -35.523088233323335);
            double b0 = (-36.112476604111691);
            double4x3 r0 = double4x3(243.50207769288778, 284.57021345038441, -348.71145550753528, -169.23228462470337, -338.6990849164776, 227.75452481385065, 54.968714739647055, -8.8491249125679587, 516.9704639934796, 52.450669789896608, -330.75297609472324, 0.589388370788356);
            TestUtils.AreEqual(r0, a0 - b0);

            double4x3 a1 = double4x3(349.39776460705218, 490.2222661870635, 195.02405104181923, -384.84940952102158, 189.05188545447402, 55.602777745389744, -54.931482579061537, 53.088051582261983, 316.80250730961677, -273.80670917863335, 256.88723695319482, 297.17363156805447);
            double b1 = (439.07729336203886);
            double4x3 r1 = double4x3(-89.679528754986677, 51.144972825024638, -244.05324232021962, -823.92670288306044, -250.02540790756484, -383.47451561664911, -494.0087759411004, -385.98924177977688, -122.27478605242209, -712.88400254067221, -182.19005640884404, -141.90366179398438);
            TestUtils.AreEqual(r1, a1 - b1);

            double4x3 a2 = double4x3(101.82901363346218, -19.732211837420323, 336.58969966349639, -51.876563334780087, 317.34576311583896, -467.05592773251976, -50.167055391784345, 477.804535373023, -60.821922092149919, 0.41113877315592617, 46.660927078994405, -19.241408595462076);
            double b2 = (136.60794765157993);
            double4x3 r2 = double4x3(-34.778934018117752, -156.34015948900026, 199.98175201191646, -188.48451098636002, 180.73781546425903, -603.6638753840997, -186.77500304336428, 341.19658772144305, -197.42986974372985, -136.196808878424, -89.947020572585529, -155.849356247042);
            TestUtils.AreEqual(r2, a2 - b2);

            double4x3 a3 = double4x3(396.80972809195976, -334.27423373529416, -198.07713684722648, -239.20061432532992, -339.68122665010446, -14.514425522887336, 219.99709211103482, -180.26066621591366, -438.89060398512083, 186.35550102328671, -365.06679241967703, -478.80124615076988);
            double b3 = (69.590537342052244);
            double4x3 r3 = double4x3(327.21919074990751, -403.8647710773464, -267.66767418927873, -308.79115166738217, -409.27176399215671, -84.10496286493958, 150.40655476898257, -249.8512035579659, -508.48114132717308, 116.76496368123446, -434.65732976172927, -548.39178349282213);
            TestUtils.AreEqual(r3, a3 - b3);
        }

        [TestCompiler]
        public static void double4x3_operator_sub_scalar_wide()
        {
            double a0 = (-86.008225719448262);
            double4x3 b0 = double4x3(466.42511413359318, 298.48694219183506, -300.95010652251085, 315.38003006362362, -381.09218543632522, -125.00837546447684, 58.466194418476107, 214.74609361158036, -257.54942739082009, 480.22459505508868, -443.35507723472784, 260.79503858312728);
            double4x3 r0 = double4x3(-552.43333985304139, -384.49516791128332, 214.94188080306259, -401.38825578307188, 295.08395971687696, 39.00014974502858, -144.47442013792437, -300.75431933102863, 171.54120167137182, -566.232820774537, 357.34685151527958, -346.80326430257554);
            TestUtils.AreEqual(r0, a0 - b0);

            double a1 = (29.681931747906788);
            double4x3 b1 = double4x3(139.85773164586055, -247.78996216868512, -248.4662297929014, 91.445112509394562, 86.384162704639266, 373.81828206303453, 260.41195428576873, 114.35393171867076, -464.40545318294573, -109.74146156652898, -311.67535057276268, 107.86401586787031);
            double4x3 r1 = double4x3(-110.17579989795377, 277.47189391659191, 278.14816154080819, -61.763180761487774, -56.702230956732478, -344.13635031512774, -230.73002253786194, -84.671999970763977, 494.08738493085252, 139.42339331443577, 341.35728232066947, -78.18208411996352);
            TestUtils.AreEqual(r1, a1 - b1);

            double a2 = (-258.7951592219971);
            double4x3 b2 = double4x3(14.097560173877355, -461.97019527012958, 30.310863747406188, 63.701105862716759, -462.67674634544028, 39.759483117498235, 47.998150132595583, -177.61928113625351, 202.47706017386031, -289.30880250097664, -459.92539832551284, 248.38668715599306);
            double4x3 r2 = double4x3(-272.89271939587445, 203.17503604813248, -289.10602296940328, -322.49626508471385, 203.88158712344318, -298.55464233949533, -306.79330935459268, -81.175878085743591, -461.27221939585741, 30.513643278979544, 201.13023910351575, -507.18184637799015);
            TestUtils.AreEqual(r2, a2 - b2);

            double a3 = (85.3297222057962);
            double4x3 b3 = double4x3(-73.374776159122, -510.65201044019869, 426.96324535224733, 239.5901807470201, 477.85233257610923, 256.01360785961788, 338.620331683485, -483.83120440125055, 330.39224139339865, -263.41821706640451, 123.92803603221103, -269.11598194256237);
            double4x3 r3 = double4x3(158.70449836491821, 595.981732645995, -341.63352314645113, -154.2604585412239, -392.52261037031303, -170.68388565382168, -253.2906094776888, 569.1609266070468, -245.06251918760245, 348.74793927220071, -38.598313826414824, 354.44570414835857);
            TestUtils.AreEqual(r3, a3 - b3);
        }

        [TestCompiler]
        public static void double4x3_operator_mul_wide_wide()
        {
            double4x3 a0 = double4x3(-482.71381710596097, -407.29348559272171, 137.70058995937029, 208.54113278563182, 194.296573967811, -484.24241684574747, 183.98730739578014, -241.33547770294149, 45.868758938214114, 363.32610266438041, -328.11893692990714, -471.02307413100408);
            double4x3 b0 = double4x3(-236.36788355389979, 260.72759139757954, -416.38629718142852, -364.49561541364324, -253.14750897751537, -369.20287220981106, 193.54791531038836, 169.08491976982214, 201.96966442930034, 249.45608317547294, -308.19319810913555, -385.57964843585137);
            double4x3 r0 = double4x3(114098.04331156026, -106192.64949051509, -57336.638772880389, -76012.328533757158, -49185.69370281692, 178783.69114527057, 35610.359790024842, -40806.189885013562, 9264.0978505395742, 90633.9064860661, 101124.02453259782, 181616.91132860651);
            TestUtils.AreEqual(r0, a0 * b0);

            double4x3 a1 = double4x3(-262.68257415605831, -379.26274674910246, -374.09058182970182, 481.44738720424812, 104.62807397946165, 412.93539948618752, 477.87724731763694, 20.377821216535722, 291.99596299417124, -138.48832399141429, -393.46498483860165, 9.36312318284206);
            double4x3 b1 = double4x3(-183.27959522198864, 22.275629292370581, -265.52144229855458, -95.677454277722859, 133.25437146669924, 148.31146080247663, 249.284127113076, 500.00547503866505, -19.331578978957396, -36.691062705913112, 30.5238278054278, -401.36701054189678);
            double4x3 r1 = double4x3(48144.355863192381, -8448.3163509892329, 99329.070837727879, -46063.660376363579, 13942.148235904471, 61243.052314850727, 119127.21246477668, 10189.022177626932, -5644.7430201585421, 5081.2837796057929, -12010.057444678736, -3758.048761232847);
            TestUtils.AreEqual(r1, a1 * b1);

            double4x3 a2 = double4x3(-131.94228917543882, 364.44964258952518, 390.61597866128011, 418.79794974755396, -277.34480942289565, 11.410165553637853, 474.87644956767394, -502.40503358394142, -222.59489618176354, 38.169053810727291, 292.61251582420084, 203.20767245218519);
            double4x3 b2 = double4x3(3.4372422711165882, 257.24176681099539, -290.97193516929258, 337.47938100317469, 490.28616284312966, -191.01981481864107, -325.73449650673871, -52.181983733634468, 123.43503743197539, -461.2670640709191, 122.35306149458188, 308.58463182513822);
            double4x3 r2 = double4x3(-453.51761370170692, 93751.669973365249, -113658.28721911977, 141335.67284620318, -135978.32239641057, -2179.56771110594, -154683.64120283397, 26216.491290173308, -27476.00934236266, -17606.127389639103, 35802.037142722758, 62706.764787700849);
            TestUtils.AreEqual(r2, a2 * b2);

            double4x3 a3 = double4x3(-330.40815678723538, 469.4601201813017, 342.29512588227874, -504.11466359724972, 319.35728159516918, -357.7820815321906, -117.9710848880797, 25.706567060997031, 226.45642171914528, -86.343729774627718, -274.12603844056184, -486.87097452900883);
            double4x3 b3 = double4x3(375.32062762571525, 203.21264204905026, 77.667988574909032, 218.793598038514, -489.89573620720569, 134.47217589918159, -287.79437960674727, -116.39999085124583, -436.54398151698706, 499.59108447450728, -300.60236396482321, 105.73045950091);
            double4x3 r3 = double4x3(-124008.99677804091, 95400.23135870698, 26585.373926271874, -110297.06107241736, -156451.77058019731, -48111.735001372064, 33951.415186899816, -2992.2441707169919, -98858.187977365582, -43136.557595680068, 82402.935179544889, -51477.091854607956);
            TestUtils.AreEqual(r3, a3 * b3);
        }

        [TestCompiler]
        public static void double4x3_operator_mul_wide_scalar()
        {
            double4x3 a0 = double4x3(-96.318821236639678, -277.14229239017811, -239.93690191951436, 509.53140544776409, 255.85810172551226, 215.73149667295229, -455.50827500573746, -389.24327367788334, -338.29248658674419, 53.796284939067618, 243.75734459783757, 135.35469991311186);
            double b0 = (-301.20720424373042);
            double4x3 r0 = double4x3(29011.922860739887, 83477.255068544036, 72270.723422079071, -153474.5301092997, -77066.303503849529, -64979.880980175592, 137202.37402436248, 117242.87823519246, 101896.13410145289, -16203.828585195659, -73421.468280190238, -40769.81074207752);
            TestUtils.AreEqual(r0, a0 * b0);

            double4x3 a1 = double4x3(-207.35010275959507, -31.425238862366086, 42.676120539510634, 260.38388049806645, 176.86755927692525, 25.672123205695357, -290.50059689697838, 207.09101805793637, -156.52330858843555, -208.4020064847553, 370.94506400215676, -341.59844247512444);
            double b1 = (-383.93960946795517);
            double4x3 r1 = double4x3(79609.9174766593, 12065.393936254042, -16385.053053547093, -99971.685390178332, -67906.461636333086, -9856.5449578079042, 111534.68572283375, -79510.444597485344, 60095.497972076177, 80013.784982095211, -142420.50300705369, 131153.17259876104);
            TestUtils.AreEqual(r1, a1 * b1);

            double4x3 a2 = double4x3(10.270311121954705, -61.006107120311867, 186.27978214355176, -487.65221785365242, -129.37681800191143, -317.71628990663044, -207.62735686433842, 388.87138933170183, -233.33533274072005, 128.4155209662465, 510.38953399583215, 267.57635486665015);
            double b2 = (-176.88876565587185);
            double4x3 r2 = double4x3(-1816.7026572643401, 10791.294985981862, -32950.800730017589, 86260.198885480888, 22885.305640842493, 56200.442350346995, 36726.946872124034, -68786.980057768713, 41274.398992408111, -22715.262994775076, -90282.1746721984, -47331.251131059282);
            TestUtils.AreEqual(r2, a2 * b2);

            double4x3 a3 = double4x3(-309.20967569444781, -189.56950983291932, 233.20923887622041, -331.08696261564592, -98.644771860281367, -214.18099389513071, -87.880760949049488, -493.16573475914345, -407.30606551063528, -411.37138362013332, 477.93567512833317, 364.7485498696326);
            double b3 = (-36.482969062627717);
            double4x3 r3 = double4x3(11280.88703222569, 6916.0585624518963, -8508.1654470401063, 12079.03541414599, 3598.8541599686141, 7813.95857407891, 3206.1510829043546, 17992.150243965898, 14859.734587045124, 15008.049461863682, -17436.512449633072, -13307.11006053213);
            TestUtils.AreEqual(r3, a3 * b3);
        }

        [TestCompiler]
        public static void double4x3_operator_mul_scalar_wide()
        {
            double a0 = (37.432166355397612);
            double4x3 b0 = double4x3(96.747546479454058, 492.18539427788244, -274.05458534604617, -452.87096926796761, 420.85330434369541, 102.18292694081686, -114.94887762654054, -351.12003843445336, -464.66496799172131, 444.08484646495663, 447.10525605040846, 130.82935124767448);
            double4x3 r0 = double4x3(3621.4702542954869, 18423.565556306661, -10258.456829132712, -16951.941459168724, 15753.450899411988, 3824.9283199300971, -4302.785509682908, -13143.183689392061, -17393.41638139162, 16623.057848787463, 16736.118322851533, 4897.2260400716978);
            TestUtils.AreEqual(r0, a0 * b0);

            double a1 = (-321.41334191030512);
            double4x3 b1 = double4x3(445.30131861441828, 478.24357317306271, 358.57170622356784, -144.89011222910608, -438.89383741789209, -3.536441089369589, -471.80755470311624, -42.560401697904069, 119.91104155402218, 271.9000023677479, 239.6840079946835, 487.44143389511919);
            double4x3 r1 = double4x3(-143125.78497292573, -153713.86510067963, -115249.73041179709, 46569.615181316156, 141066.33502832282, 1136.6593490031996, 151645.24289565769, 13679.48094276837, -38541.008597823733, -87392.288426437735, -77037.638012027513, -156670.18025378135);
            TestUtils.AreEqual(r1, a1 * b1);

            double a2 = (-79.188288010278825);
            double4x3 b2 = double4x3(-112.92564468873928, 161.3700478828373, 459.75914332818195, -337.19599811043406, -276.83451689259823, 469.72386405883537, -274.56515110403541, 506.78586625810055, 65.882571966332648, 495.8556585236712, -347.27959148365983, -343.60605232026711);
            double4x3 r2 = double4x3(8942.3884753582988, -12778.617827978605, -36407.539457231134, 26701.973814282486, 21922.051454877466, -37196.628632392116, 21742.344263212082, -40131.505136785112, -5217.1280837278719, -39265.960698698946, 27500.476310500027, 27209.575033212248);
            TestUtils.AreEqual(r2, a2 * b2);

            double a3 = (-183.70378860444936);
            double4x3 b3 = double4x3(460.26475808595524, 437.513251746778, -324.55724755141756, -112.28778343661122, 273.13543070160574, -283.09366072485864, 1.8802692898923397, -310.81670322586626, 326.01218357962193, 243.64321982285162, 78.179342067884022, -308.66400184699523);
            double4x3 r3 = double4x3(-84552.379821500348, -80372.841910535339, 59622.395994227547, 20627.691231301418, -50176.013421993011, 52005.378005059138, -345.41259214982045, 57098.205944136411, -59889.673254785805, -44758.18254924452, -14361.841328473502, 56702.746545103779);
            TestUtils.AreEqual(r3, a3 * b3);
        }

        [TestCompiler]
        public static void double4x3_operator_div_wide_wide()
        {
            double4x3 a0 = double4x3(-353.13144390337703, -102.79985456485292, 51.319128298814917, -191.87167868012176, 8.0418245829836223, -128.73764210973758, -136.05959779399427, -370.4710053738537, -237.69456326109105, -432.54687496300176, 200.26549181727012, 361.44157068871039);
            double4x3 b0 = double4x3(-178.73954805114283, -302.09628381491467, -199.40583739029518, 278.85077561012042, 502.33758782890516, -361.48483078623417, 353.121059820578, -38.894930142394685, -75.764737402910725, -195.21784719974636, -405.03399224068687, -394.2300085473014);
            double4x3 r0 = double4x3(1.97567604793504, 0.34028837848212429, -0.25736021056579439, -0.68808013268139567, 0.016008805189634039, 0.35613566917796119, -0.3853058151307277, 9.5249176182488586, 3.1372716570909582, 2.2157137842034547, -0.49444119667433889, -0.9168291678773689);
            TestUtils.AreEqual(r0, a0 / b0);

            double4x3 a1 = double4x3(-416.22613234828509, -450.01919362042992, -273.49744594911925, -286.90817011841955, -314.25606241554772, 177.76210340194507, 97.626988217992221, -68.107280047660367, -386.45074027890837, 263.69934690357161, -297.0270885420158, -501.77703046322659);
            double4x3 b1 = double4x3(-375.82771342612227, -121.24548655433836, 447.623344391409, 338.28628007429018, -405.54420752336466, -431.16893526127978, 296.20513095343722, 437.939790691221, 39.21061684527001, 331.2897075765253, -310.61955156485533, 207.26946959610541);
            double4x3 r1 = double4x3(1.1074918572499153, 3.7116366671409717, -0.61099906735420106, -0.84812239519560884, 0.77489964493560781, -0.41227947763496636, 0.32959249525403717, -0.15551745124635385, -9.855767936625206, 0.79597808465769837, 0.95624080018671487, -2.420892143165184);
            TestUtils.AreEqual(r1, a1 / b1);

            double4x3 a2 = double4x3(-263.40686071263946, -451.08085248017721, -416.34552903489464, -315.27873411554788, -28.181118739853218, -397.87015146662952, -261.38664376986526, 40.348221559239619, 277.24575794732471, 464.77123162931355, -336.64104358136706, 375.47808163961304);
            double4x3 b2 = double4x3(-223.2929938879297, -480.091406807346, 448.67593666942605, -460.0974516626901, -220.56984601755153, -84.853158275062754, 441.3738078742166, 72.418480191574645, 44.9760778159723, -242.51539027062961, -451.30207011257392, -21.899694214528267);
            double4x3 r2 = double4x3(1.1796467776541293, 0.93957285234474042, -0.92794263076704353, 0.68524338262731188, 0.12776505605218016, 4.6889256635195675, -0.59221149761645042, 0.55715366371267527, 6.1642938070706279, -1.9164607702243661, 0.74593285933165443, -17.145357280400777);
            TestUtils.AreEqual(r2, a2 / b2);

            double4x3 a3 = double4x3(504.34254264474964, -320.76710692083793, -156.73333914425848, 414.79707999471441, -386.05068296289568, -369.8386258416989, 386.70419687158619, 242.63180910918481, 421.73452659218322, 109.01218347857343, 182.07528242006674, 187.32643446108625);
            double4x3 b3 = double4x3(-358.4866656542228, -350.94512502799978, -481.84813688781492, 406.39341921657012, -145.28866321653533, 461.7955479388105, -318.81676331107354, -250.93199908497371, 125.85955506463517, -193.80316576445625, -495.25412177259761, -315.82454815312497);
            double4x3 r3 = double4x3(-1.4068655572567703, 0.914009296739044, 0.32527538688138485, 1.0206786340053058, 2.6571287422992764, -0.80087092110880154, -1.2129355836106837, -0.96692255269931449, 3.3508343993080341, -0.56248917838145251, -0.36764011527736257, -0.59313449684811248);
            TestUtils.AreEqual(r3, a3 / b3);
        }

        [TestCompiler]
        public static void double4x3_operator_div_wide_scalar()
        {
            double4x3 a0 = double4x3(171.34242184988341, 0.10338377957384637, 57.888263967767443, -256.13074529177078, 95.6696842162263, -290.38690461329509, -127.44869118903239, -79.7448890580539, 146.46688110496234, -499.84355687529012, 58.686315802245531, -453.20579859856787);
            double b0 = (171.79682191265601);
            double4x3 r0 = double4x3(0.99735501473360411, 0.00060177934855167557, 0.33695771157628673, -1.4908933846400916, 0.55687691513214455, -1.6902926455818372, -0.74185709473618289, -0.46418139852783397, 0.85255873463962106, -2.909504095072363, 0.34160303519515922, -2.6380336583233435);
            TestUtils.AreEqual(r0, a0 / b0);

            double4x3 a1 = double4x3(-205.03382143985192, 464.47907159499778, -293.46349753693841, -158.50557930697948, -289.5822156824089, 494.12860535743118, 203.58342680874443, 180.97040160976837, 259.11918723728468, 460.84470603468117, 490.95625924084163, -280.47805536933151);
            double b1 = (481.73814247629514);
            double4x3 r1 = double4x3(-0.42561259605874163, 0.96417333534650185, -0.60917637957509008, -0.32902850185830795, -0.60111955053809829, 1.0257203276814348, 0.42260184290630909, 0.37566135137134876, 0.53788389249255919, 0.95662905923493058, 1.0191351191690206, -0.582220984054907);
            TestUtils.AreEqual(r1, a1 / b1);

            double4x3 a2 = double4x3(-320.24387112271222, 264.80085885934568, 226.85298524929817, -192.23568949114332, 460.97652957447644, -437.89221760159927, -413.23271794488312, 249.47184693509337, 313.03501739773662, 216.78560195527302, 383.73890298592812, 82.023314752626789);
            double b2 = (192.41448912043802);
            double4x3 r2 = double4x3(-1.6643438474233712, 1.3762002023329898, 1.1789807840682105, -0.99907075797611689, 2.3957474911670364, -2.2757756944567169, -2.14761746807034, 1.2965335826604067, 1.6268786141245246, 1.126659447249736, 1.9943347548309338, 0.4262845024175177);
            TestUtils.AreEqual(r2, a2 / b2);

            double4x3 a3 = double4x3(189.57466062790468, -391.92216343056509, 121.28058701440716, 417.90175147443165, -133.26287013537382, -428.74240299162568, -188.53187641339929, 356.25952570338711, 181.96896823773579, -140.8904808223669, 474.08261678837357, -451.35772511519383);
            double b3 = (314.50384273869167);
            double4x3 r3 = double4x3(0.60277374984385956, -1.2461601741260666, 0.38562513563681389, 1.3287651681307089, -0.42372413950470061, -1.3632342271501279, -0.59945810127999832, 1.1327668450760029, 0.57859060370504389, -0.44797697730970815, 1.5073984872810262, -1.4351421629217054);
            TestUtils.AreEqual(r3, a3 / b3);
        }

        [TestCompiler]
        public static void double4x3_operator_div_scalar_wide()
        {
            double a0 = (-264.44250095283729);
            double4x3 b0 = double4x3(105.58908157497137, -142.34910137129441, -288.94890679463231, 39.644133824689334, -363.99138396046658, -149.71822006521666, -395.72912306139671, 258.71868693955184, -9.6662514254759344, 117.72553282497711, -331.38655797177296, -509.98602676297821);
            double4x3 r0 = double4x3(-2.5044492954044237, 1.85770404172122, 0.915187753732487, -6.670406827961755, 0.72650758398599513, 1.7662679988958405, 0.66824119212426392, -1.0221236976771717, 27.357295947825712, -2.2462629355518375, 0.79798801306648692, 0.51852891466718543);
            TestUtils.AreEqual(r0, a0 / b0);

            double a1 = (427.8964666928614);
            double4x3 b1 = double4x3(467.61712882836218, -407.12461943511136, 252.69070994699871, 444.59937664708093, -88.313306134340053, 199.95503411067421, -218.34692607556792, -13.417186028052697, -296.13107575854804, 0.561349630617201, -289.29929865957206, 196.21833929615946);
            double4x3 r1 = double4x3(0.915057298617305, -1.0510208576591884, 1.6933604990172044, 0.96243154886949345, -4.8452094641543111, 2.1399634602648847, -1.9597091398702369, -31.891669817964367, -1.4449563106364052, 762.26373610042538, -1.4790788248552968, 2.1807159729704049);
            TestUtils.AreEqual(r1, a1 / b1);

            double a2 = (334.73346845001606);
            double4x3 b2 = double4x3(-282.39273203648293, -479.50358436978587, -473.43943927876626, 105.0507777226394, -287.63127841038227, 77.299297130340392, -210.89436421678141, -184.0682357214709, -315.14843645465953, 87.86691264429453, 101.5905373569534, 345.93639890567226);
            double4x3 r2 = double4x3(-1.1853473212149495, -0.698083349866838, -0.70702489205366215, 3.1863968616567222, -1.1637589287922645, 4.3303559136585132, -1.5872091684059353, -1.818529238018717, -1.0621454201571907, 3.8095508124325947, 3.2949276296657475, 0.96761563544311813);
            TestUtils.AreEqual(r2, a2 / b2);

            double a3 = (-146.31811744827689);
            double4x3 b3 = double4x3(479.99991177022457, -172.67688401633728, -178.0136545533378, 361.76045315422141, 349.37693111476347, -398.68612951724145, -243.7800091448147, 296.62295045360133, 477.81065224009126, 486.60035942802222, 256.91724622292315, -89.8642156542578);
            double4x3 r3 = double4x3(-0.3048294673819007, 0.84735208352748281, 0.82194884328064821, -0.40446133946515234, -0.41879730576777624, 0.36700077232546224, 0.60020556222622135, -0.4932798262053712, -0.30622615205898479, -0.30069463495725229, -0.56951457949739548, 1.6282133703944957);
            TestUtils.AreEqual(r3, a3 / b3);
        }

        [TestCompiler]
        public static void double4x3_operator_mod_wide_wide()
        {
            double4x3 a0 = double4x3(-388.81249422059045, 181.68118842955732, -167.07872470052854, 432.82015319951813, -258.43895995730486, -170.11079629236406, 283.318293464984, 122.71651297561664, 335.27101413126616, -503.60851668920765, 191.02251848532933, 289.74269379756538);
            double4x3 b0 = double4x3(436.94417187056695, 58.940049437312382, -201.11623368091705, 279.2893537391393, -397.07975954426445, 377.89994758083481, 174.69386657266591, -228.17652736798698, -317.06019106370405, -417.48011107811709, -249.9759434433542, -397.57157177364991);
            double4x3 r0 = double4x3(-388.81249422059045, 4.8610401176201776, -167.07872470052854, 153.53079946037883, -258.43895995730486, -170.11079629236406, 108.62442689231807, 122.71651297561664, 18.210823067562103, -86.128405611090557, 191.02251848532933, 289.74269379756538);
            TestUtils.AreEqual(r0, a0 % b0);

            double4x3 a1 = double4x3(-124.03371745163281, 259.27395761165485, -274.35845030208975, -140.03080398404541, 324.5775689205982, -200.51308903494527, 211.42317328761476, -51.272212767634642, -230.63392483006879, 99.989400671790122, 399.18986649028489, 24.903281461868119);
            double4x3 b1 = double4x3(-358.74544947163452, -198.1592100589346, 208.73709378425826, -12.119406944196385, 25.27141596063575, -194.12068495253135, -493.8717965995296, -312.3016990685378, -216.98060546488529, 413.57096047586344, -436.39440151508637, 3.4912750737235);
            double4x3 r1 = double4x3(-124.03371745163281, 61.114747552720246, -65.621356517831487, -6.7173275978851734, 21.3205773929692, -6.3924040824139183, 211.42317328761476, -51.272212767634642, -13.653319365183506, 99.989400671790122, 399.18986649028489, 0.46435594580361794);
            TestUtils.AreEqual(r1, a1 % b1);

            double4x3 a2 = double4x3(50.92402961241271, -364.86367886367429, -252.62662398658068, -281.28977955565313, -364.79852192699843, -329.02623311105475, 51.6098087074281, 41.647804041229051, 254.95104443978096, -458.67762133976333, -136.79304439238882, 72.400299344398263);
            double4x3 b2 = double4x3(-308.23343076754054, -441.37506195594324, 84.6008532441225, 373.16344922276369, 67.252760203207231, -320.33327522889397, 118.97936325845274, 44.823946258436877, 354.00861065183233, -253.95312249565177, -195.16280207185207, 317.14281073079576);
            double4x3 r2 = double4x3(50.92402961241271, -364.86367886367429, -83.424917498335674, -281.28977955565313, -28.534720910962278, -8.6929578821607834, 51.6098087074281, 41.647804041229051, 254.95104443978096, -204.72449884411157, -136.79304439238882, 72.400299344398263);
            TestUtils.AreEqual(r2, a2 % b2);

            double4x3 a3 = double4x3(246.21202170393053, 325.1538137519517, 162.03465588485574, -284.76143826393479, 128.35126906649737, 262.91676032865269, 61.600772647932558, -271.4927829576157, -205.43880448371118, -341.32216302553292, 347.1544365115252, 148.0884922240341);
            double4x3 b3 = double4x3(320.6931823793301, -103.99687604978533, 388.17173332170194, -199.63931593654644, -256.21731746206865, -478.12501953454921, -210.65574202810217, -272.02328432352431, -61.676538257709012, -367.82958691559247, -242.93893753874067, 162.38671191147841);
            double4x3 r3 = double4x3(246.21202170393053, 13.163185602595718, 162.03465588485574, -85.122122327388354, 128.35126906649737, 262.91676032865269, 61.600772647932558, -271.4927829576157, -20.409189710584144, -341.32216302553292, 104.21549897278453, 148.0884922240341);
            TestUtils.AreEqual(r3, a3 % b3);
        }

        [TestCompiler]
        public static void double4x3_operator_mod_wide_scalar()
        {
            double4x3 a0 = double4x3(-244.49962889612635, -211.81931958525411, -145.92677576184587, -304.91822090042672, 155.47946436492703, -133.90778428591221, 281.30965412841624, -226.53575311719243, 335.16613046041039, 101.70649032560482, 319.47152033423606, -285.40231646476423);
            double b0 = (39.634963769295723);
            double4x3 r0 = double4x3(-6.6898462803520147, -13.644500738775491, -27.021884453958705, -27.473474515356656, 36.574573057039856, -15.002892978025045, 3.86490774334618, -28.360934270713813, 18.0864203060446, 22.436562787013372, 2.3918101798702764, -7.9575700796941646);
            TestUtils.AreEqual(r0, a0 % b0);

            double4x3 a1 = double4x3(-355.84685985923136, -330.87193957477433, -284.34358109363518, -102.68343811048356, -172.14173921017988, 206.41684517935698, -416.71365447375626, -339.256669917729, 435.29751440291182, 132.55290490600885, 226.94410215455298, -306.11827268550093);
            double b1 = (259.37800061860025);
            double4x3 r1 = double4x3(-96.4688592406311, -71.493938956174077, -24.965580475034926, -102.68343811048356, -172.14173921017988, 206.41684517935698, -157.335653855156, -79.878669299128774, 175.91951378431156, 132.55290490600885, 226.94410215455298, -46.740272066900673);
            TestUtils.AreEqual(r1, a1 % b1);

            double4x3 a2 = double4x3(115.43844633709568, -218.3474491659307, -140.04050237501065, -462.32346961569203, -211.60869822819188, 351.33104555277669, 321.04701176334504, 346.08518497370426, -94.407745643708722, 465.40920446133669, -367.19701617173712, -467.51058957889239);
            double b2 = (281.88292015804109);
            double4x3 r2 = double4x3(115.43844633709568, -218.3474491659307, -140.04050237501065, -180.44054945765095, -211.60869822819188, 69.4481253947356, 39.164091605303952, 64.20226481566317, -94.407745643708722, 183.5262843032956, -85.31409601369603, -185.6276694208513);
            TestUtils.AreEqual(r2, a2 % b2);

            double4x3 a3 = double4x3(415.21510215067076, -3.729830982037754, 128.24987822782714, 134.94156104649494, 247.61696230974837, -285.28786553316183, 433.76666017704019, -141.83102209019989, -229.7818902608854, 471.21804283150379, 377.68146651689028, 433.40759559786306);
            double b3 = (506.18618011203887);
            double4x3 r3 = double4x3(415.21510215067076, -3.729830982037754, 128.24987822782714, 134.94156104649494, 247.61696230974837, -285.28786553316183, 433.76666017704019, -141.83102209019989, -229.7818902608854, 471.21804283150379, 377.68146651689028, 433.40759559786306);
            TestUtils.AreEqual(r3, a3 % b3);
        }

        [TestCompiler]
        public static void double4x3_operator_mod_scalar_wide()
        {
            double a0 = (-66.945025236785909);
            double4x3 b0 = double4x3(-249.77609479137516, -396.07375664081133, 386.49204582091977, 168.93948109864232, -199.4182442163202, 261.7517141130528, 16.127438791155555, 257.66814744550186, -75.788451945310669, 170.95630439136005, -242.85828005655588, 425.94531913564788);
            double4x3 r0 = double4x3(-66.945025236785909, -66.945025236785909, -66.945025236785909, -66.945025236785909, -66.945025236785909, -66.945025236785909, -2.4352700721636893, -66.945025236785909, -66.945025236785909, -66.945025236785909, -66.945025236785909, -66.945025236785909);
            TestUtils.AreEqual(r0, a0 % b0);

            double a1 = (303.27240409668184);
            double4x3 b1 = double4x3(3.033060790520608, -505.74352788633831, 461.95706126743789, 205.97275672013529, 270.04063642678807, -47.480711720642034, -150.254496405951, 149.49949009227544, -220.29804263836616, 31.118842377848409, 400.63568348467152, 6.2314283876826266);
            double4x3 r1 = double4x3(2.999385835141652, 303.27240409668184, 303.27240409668184, 97.299647376546545, 33.23176766989377, 18.388133772829633, 2.7634112847798633, 4.2734239121309656, 82.974361458315684, 23.202822696046155, 303.27240409668184, 4.1638414879157608);
            TestUtils.AreEqual(r1, a1 % b1);

            double a2 = (-39.050740021770252);
            double4x3 b2 = double4x3(-71.941097054603063, -495.30713843521994, -86.71961859926563, -436.97006365143233, -472.2947320753218, -130.00875359867177, -251.51684605866524, 281.97637022751212, 388.86081928241106, 50.615297579493017, 293.870868581287, 123.74424820940203);
            double4x3 r2 = double4x3(-39.050740021770252, -39.050740021770252, -39.050740021770252, -39.050740021770252, -39.050740021770252, -39.050740021770252, -39.050740021770252, -39.050740021770252, -39.050740021770252, -39.050740021770252, -39.050740021770252, -39.050740021770252);
            TestUtils.AreEqual(r2, a2 % b2);

            double a3 = (422.90433211946129);
            double4x3 b3 = double4x3(-53.8761976016109, -178.85765966161046, -362.27595799149753, 361.08526747351755, 465.27609822958527, -269.88963306596952, -159.40897734435691, -29.095214618879936, 484.49945067078784, -354.95061008769585, -328.69059411095952, -171.73922236810404);
            double4x3 r3 = double4x3(45.770948908185005, 65.189012796240377, 60.628374127963752, 61.81906464594374, 422.90433211946129, 153.01469905349177, 104.08637743074746, 15.571327455142182, 422.90433211946129, 67.953722031765437, 94.213738008501764, 79.425887383253212);
            TestUtils.AreEqual(r3, a3 % b3);
        }

        [TestCompiler]
        public static void double4x3_operator_plus()
        {
            double4x3 a0 = double4x3(-418.82956357432045, -405.79894823851015, -34.041791216489742, 236.99924456188421, -459.83910129025537, 210.8614223985287, 293.74197902052754, -373.015422279488, -386.059833944803, 4.9544198536101476, -418.64524932328857, 504.47483062393724);
            double4x3 r0 = double4x3(-418.82956357432045, -405.79894823851015, -34.041791216489742, 236.99924456188421, -459.83910129025537, 210.8614223985287, 293.74197902052754, -373.015422279488, -386.059833944803, 4.9544198536101476, -418.64524932328857, 504.47483062393724);
            TestUtils.AreEqual(r0, +a0);

            double4x3 a1 = double4x3(-170.74650843941907, -478.74939916969714, 116.40075665172219, 421.40964742256779, -258.5960806620289, 447.86609122150867, 124.16434031546316, 222.17254386757156, -65.949277193261878, 239.04183947250328, 498.4495329793773, -139.382530515726);
            double4x3 r1 = double4x3(-170.74650843941907, -478.74939916969714, 116.40075665172219, 421.40964742256779, -258.5960806620289, 447.86609122150867, 124.16434031546316, 222.17254386757156, -65.949277193261878, 239.04183947250328, 498.4495329793773, -139.382530515726);
            TestUtils.AreEqual(r1, +a1);

            double4x3 a2 = double4x3(279.07295549990283, 37.999210613779383, 136.81214934997831, -236.03003965878395, -440.3083276414817, 342.2791270419392, 102.4722116470673, -161.454825714908, -355.27087919566355, 141.31435949230308, 239.32088600812517, -494.60408543730347);
            double4x3 r2 = double4x3(279.07295549990283, 37.999210613779383, 136.81214934997831, -236.03003965878395, -440.3083276414817, 342.2791270419392, 102.4722116470673, -161.454825714908, -355.27087919566355, 141.31435949230308, 239.32088600812517, -494.60408543730347);
            TestUtils.AreEqual(r2, +a2);

            double4x3 a3 = double4x3(361.59198134094106, 141.71249515456725, 25.25630880578251, -268.22689569565784, 106.77467613423926, 176.74438079481217, 104.11991005023935, 144.61861736356218, 289.45191372998613, -393.01668781461973, -198.95573506083139, -419.00921388110578);
            double4x3 r3 = double4x3(361.59198134094106, 141.71249515456725, 25.25630880578251, -268.22689569565784, 106.77467613423926, 176.74438079481217, 104.11991005023935, 144.61861736356218, 289.45191372998613, -393.01668781461973, -198.95573506083139, -419.00921388110578);
            TestUtils.AreEqual(r3, +a3);
        }

        [TestCompiler]
        public static void double4x3_operator_neg()
        {
            double4x3 a0 = double4x3(148.46174890755753, -467.12267873581624, 132.04719954917539, 183.52262290917463, 473.7010145009034, -407.99109024926605, -54.958759571872065, -382.98981803608581, -299.09338893512887, -383.01406377508027, 407.70980305583669, 168.73550351370852);
            double4x3 r0 = double4x3(-148.46174890755753, 467.12267873581624, -132.04719954917539, -183.52262290917463, -473.7010145009034, 407.99109024926605, 54.958759571872065, 382.98981803608581, 299.09338893512887, 383.01406377508027, -407.70980305583669, -168.73550351370852);
            TestUtils.AreEqual(r0, -a0);

            double4x3 a1 = double4x3(466.44152829909763, -280.55831564616335, -78.85761622286293, 318.69633522569029, -39.91539694737429, 140.34000284054321, 132.19563180403577, -505.89525127126615, 410.38058466947666, -237.05693375182193, -137.617827241131, -245.34998547534923);
            double4x3 r1 = double4x3(-466.44152829909763, 280.55831564616335, 78.85761622286293, -318.69633522569029, 39.91539694737429, -140.34000284054321, -132.19563180403577, 505.89525127126615, -410.38058466947666, 237.05693375182193, 137.617827241131, 245.34998547534923);
            TestUtils.AreEqual(r1, -a1);

            double4x3 a2 = double4x3(422.52133222227974, 60.222219256787639, -466.56631515294606, 426.89450116962871, 146.64955885086658, -391.37208408460583, 423.23773809114368, 254.29757296959758, -114.84889536483627, 108.05966263080927, -507.97628688624889, -306.24571456864743);
            double4x3 r2 = double4x3(-422.52133222227974, -60.222219256787639, 466.56631515294606, -426.89450116962871, -146.64955885086658, 391.37208408460583, -423.23773809114368, -254.29757296959758, 114.84889536483627, -108.05966263080927, 507.97628688624889, 306.24571456864743);
            TestUtils.AreEqual(r2, -a2);

            double4x3 a3 = double4x3(219.66627298093692, -98.760666177962264, 492.11106156376707, 84.0458290968304, 300.97664298721429, -483.86463307024195, -389.157431545275, -324.68608418325243, 378.8543824529529, 190.2192524365239, -69.102404865018286, 507.49539184360549);
            double4x3 r3 = double4x3(-219.66627298093692, 98.760666177962264, -492.11106156376707, -84.0458290968304, -300.97664298721429, 483.86463307024195, 389.157431545275, 324.68608418325243, -378.8543824529529, -190.2192524365239, 69.102404865018286, -507.49539184360549);
            TestUtils.AreEqual(r3, -a3);
        }

        [TestCompiler]
        public static void double4x3_operator_prefix_inc()
        {
            double4x3 a0 = double4x3(-139.84208137348389, -56.743654039103376, -381.955324589254, 509.79634380237962, -222.89634452708827, 210.31986556310198, -392.73151058365193, -300.19410218866267, 362.21273939787068, 401.614830919362, 130.90919429199266, -450.23016402229212);
            double4x3 r0 = double4x3(-138.84208137348389, -55.743654039103376, -380.955324589254, 510.79634380237962, -221.89634452708827, 211.31986556310198, -391.73151058365193, -299.19410218866267, 363.21273939787068, 402.614830919362, 131.90919429199266, -449.23016402229212);
            TestUtils.AreEqual(r0, ++a0);

            double4x3 a1 = double4x3(243.54693114177644, -41.497298975241051, 299.18547000511808, 154.35656530892311, -281.23327435237974, 200.70599922943211, 92.957765384091886, 448.60215565590283, -295.58701171334229, 18.499063262016989, -215.71113381893895, 471.94723651928234);
            double4x3 r1 = double4x3(244.54693114177644, -40.497298975241051, 300.18547000511808, 155.35656530892311, -280.23327435237974, 201.70599922943211, 93.957765384091886, 449.60215565590283, -294.58701171334229, 19.499063262016989, -214.71113381893895, 472.94723651928234);
            TestUtils.AreEqual(r1, ++a1);

            double4x3 a2 = double4x3(257.07660090973445, 4.8254301570474354, 243.00478588929627, -472.61902330472088, -125.7202084649914, -477.45955227197129, 9.8914859340952717, -76.922842299995409, -29.767583622488928, -387.17744344620849, 461.70929906410595, 13.699699169816313);
            double4x3 r2 = double4x3(258.07660090973445, 5.8254301570474354, 244.00478588929627, -471.61902330472088, -124.7202084649914, -476.45955227197129, 10.891485934095272, -75.922842299995409, -28.767583622488928, -386.17744344620849, 462.70929906410595, 14.699699169816313);
            TestUtils.AreEqual(r2, ++a2);

            double4x3 a3 = double4x3(-46.303758404359087, -222.22908626414329, 340.81780807153223, 399.74125046270956, -311.37233772472121, 300.17795457512977, -272.77828777617697, 351.01916782512296, 436.57524010007046, -137.06332475369021, 312.57995453131377, -315.99901380948677);
            double4x3 r3 = double4x3(-45.303758404359087, -221.22908626414329, 341.81780807153223, 400.74125046270956, -310.37233772472121, 301.17795457512977, -271.77828777617697, 352.01916782512296, 437.57524010007046, -136.06332475369021, 313.57995453131377, -314.99901380948677);
            TestUtils.AreEqual(r3, ++a3);
        }

        [TestCompiler]
        public static void double4x3_operator_postfix_inc()
        {
            double4x3 a0 = double4x3(-396.6697396695007, 511.20749378167443, 249.11127030528678, -128.81731301584153, -259.49027669592306, 278.00817764830219, -81.393423356764686, 66.719732554033271, 167.85212691493894, 147.94395048354932, -326.10758486674524, 41.033564825092185);
            double4x3 r0 = double4x3(-396.6697396695007, 511.20749378167443, 249.11127030528678, -128.81731301584153, -259.49027669592306, 278.00817764830219, -81.393423356764686, 66.719732554033271, 167.85212691493894, 147.94395048354932, -326.10758486674524, 41.033564825092185);
            TestUtils.AreEqual(r0, a0++);

            double4x3 a1 = double4x3(128.5304239394751, -60.132380275117384, -446.22976490772783, -296.93783797739906, 267.29380071689081, 446.22930714405572, 49.200223230384381, -326.64314738225335, -510.86424064583343, 471.64748762159024, -171.01308186865089, 310.72735967800361);
            double4x3 r1 = double4x3(128.5304239394751, -60.132380275117384, -446.22976490772783, -296.93783797739906, 267.29380071689081, 446.22930714405572, 49.200223230384381, -326.64314738225335, -510.86424064583343, 471.64748762159024, -171.01308186865089, 310.72735967800361);
            TestUtils.AreEqual(r1, a1++);

            double4x3 a2 = double4x3(-298.91717185588425, 184.60345109952777, 290.69102896875279, 117.1923401901463, 164.44293578175962, 412.36778874526158, -229.38657079887884, 239.59693848322934, 36.624316947825378, -80.708194531830145, -391.03352016538076, -478.22714136458336);
            double4x3 r2 = double4x3(-298.91717185588425, 184.60345109952777, 290.69102896875279, 117.1923401901463, 164.44293578175962, 412.36778874526158, -229.38657079887884, 239.59693848322934, 36.624316947825378, -80.708194531830145, -391.03352016538076, -478.22714136458336);
            TestUtils.AreEqual(r2, a2++);

            double4x3 a3 = double4x3(166.86049159190645, -389.39665216458809, -52.132133269744031, 35.755328910311391, 356.05211298356392, 6.5294592410929226, -285.34983052189921, 418.0164985219094, 47.142905018824536, 31.451607480389839, 148.9468749263076, -219.80038200123255);
            double4x3 r3 = double4x3(166.86049159190645, -389.39665216458809, -52.132133269744031, 35.755328910311391, 356.05211298356392, 6.5294592410929226, -285.34983052189921, 418.0164985219094, 47.142905018824536, 31.451607480389839, 148.9468749263076, -219.80038200123255);
            TestUtils.AreEqual(r3, a3++);
        }

        [TestCompiler]
        public static void double4x3_operator_prefix_dec()
        {
            double4x3 a0 = double4x3(123.12869626056806, 256.8437465433235, 156.33078844674435, 461.73742530389563, 325.86799755965728, 392.01561731473339, 187.87412580655609, -236.2252043393558, 125.10963517292851, 469.8447313112415, 45.536655685648611, 376.04684680329956);
            double4x3 r0 = double4x3(122.12869626056806, 255.8437465433235, 155.33078844674435, 460.73742530389563, 324.86799755965728, 391.01561731473339, 186.87412580655609, -237.2252043393558, 124.10963517292851, 468.8447313112415, 44.536655685648611, 375.04684680329956);
            TestUtils.AreEqual(r0, --a0);

            double4x3 a1 = double4x3(-363.07547991493504, 248.79012667797042, 168.0950144120003, 168.26565011230559, -190.284744112885, 166.9455474200405, 183.95795854551625, 485.69469259944492, -460.73930261132273, 89.569894117102876, -267.42982090051743, 201.75623450137505);
            double4x3 r1 = double4x3(-364.07547991493504, 247.79012667797042, 167.0950144120003, 167.26565011230559, -191.284744112885, 165.9455474200405, 182.95795854551625, 484.69469259944492, -461.73930261132273, 88.569894117102876, -268.42982090051743, 200.75623450137505);
            TestUtils.AreEqual(r1, --a1);

            double4x3 a2 = double4x3(-141.21688682456357, 197.36173281323249, -213.54412732531506, 180.74062570405226, -128.31251412644633, 478.04553888647149, -454.56614062495817, -386.89835256473083, 387.85698408068015, -315.11044969927076, -108.28654556548526, -286.31702937107394);
            double4x3 r2 = double4x3(-142.21688682456357, 196.36173281323249, -214.54412732531506, 179.74062570405226, -129.31251412644633, 477.04553888647149, -455.56614062495817, -387.89835256473083, 386.85698408068015, -316.11044969927076, -109.28654556548526, -287.31702937107394);
            TestUtils.AreEqual(r2, --a2);

            double4x3 a3 = double4x3(-375.60158007945938, 78.275426662655263, 161.5319641388636, -346.8479546731561, -57.540783670517044, 455.37286231265068, 444.79814478605897, 129.82014638270255, 134.71065455987616, 61.323015956824179, -274.54334486394345, -43.3955581390278);
            double4x3 r3 = double4x3(-376.60158007945938, 77.275426662655263, 160.5319641388636, -347.8479546731561, -58.540783670517044, 454.37286231265068, 443.79814478605897, 128.82014638270255, 133.71065455987616, 60.323015956824179, -275.54334486394345, -44.3955581390278);
            TestUtils.AreEqual(r3, --a3);
        }

        [TestCompiler]
        public static void double4x3_operator_postfix_dec()
        {
            double4x3 a0 = double4x3(379.68831723727669, 302.69287814884115, -176.07134040448409, -291.25267066212962, 470.56758401848731, -402.92594666170231, -63.655158787805192, 355.26110069605568, -27.889220489137415, -100.76183824462902, 156.14034969924967, 479.94519613680677);
            double4x3 r0 = double4x3(379.68831723727669, 302.69287814884115, -176.07134040448409, -291.25267066212962, 470.56758401848731, -402.92594666170231, -63.655158787805192, 355.26110069605568, -27.889220489137415, -100.76183824462902, 156.14034969924967, 479.94519613680677);
            TestUtils.AreEqual(r0, a0--);

            double4x3 a1 = double4x3(-200.30429491787419, 407.42034907239508, 327.67032519340069, 48.0602071509046, -209.66798100698179, -38.435048836485976, 283.941595924991, -94.802087112703418, 152.51066334196867, -287.262531175866, -215.94803939384781, -407.04635567546188);
            double4x3 r1 = double4x3(-200.30429491787419, 407.42034907239508, 327.67032519340069, 48.0602071509046, -209.66798100698179, -38.435048836485976, 283.941595924991, -94.802087112703418, 152.51066334196867, -287.262531175866, -215.94803939384781, -407.04635567546188);
            TestUtils.AreEqual(r1, a1--);

            double4x3 a2 = double4x3(159.23357136511879, 168.4139531442961, -278.93379868144814, 289.91284073978329, 402.03954691534841, 470.71654937729079, -208.56061873611094, 145.89674789546837, -296.79095258228062, -274.57083309561517, -250.04125630578085, -70.856303486440481);
            double4x3 r2 = double4x3(159.23357136511879, 168.4139531442961, -278.93379868144814, 289.91284073978329, 402.03954691534841, 470.71654937729079, -208.56061873611094, 145.89674789546837, -296.79095258228062, -274.57083309561517, -250.04125630578085, -70.856303486440481);
            TestUtils.AreEqual(r2, a2--);

            double4x3 a3 = double4x3(-485.627825724719, -503.19208335466317, 397.64861387649955, 446.6215557747621, -292.8101204805123, 126.6225212209963, -250.44240700939781, 470.81648204793055, 26.943619502216393, -186.92351945998308, 45.746085426651916, -206.45597586708885);
            double4x3 r3 = double4x3(-485.627825724719, -503.19208335466317, 397.64861387649955, 446.6215557747621, -292.8101204805123, 126.6225212209963, -250.44240700939781, 470.81648204793055, 26.943619502216393, -186.92351945998308, 45.746085426651916, -206.45597586708885);
            TestUtils.AreEqual(r3, a3--);
        }

        [TestCase]
        public static void double4x3_EqualsObjectOverride()
        {
            TestUtils.IsFalse(new double4x3().Equals((object)new int()));
            TestUtils.IsTrue(new double4x3().Equals((object)new double4x3()));
        }


    }
}
